// eslint配置，用于校验代码
module.exports = {
  // env指定环境 支持的环境: browser node commonjs es6 es2016~es2022...
  // 环境很多，详情查看文档https://zh-hans.eslint.org/docs/latest/use/configure/language-options
  env: {
    browser: true,
    es2021: true,
    node: true,
    "vue/setup-compiler-macros": true
  },
  parser: "vue-eslint-parser",
  // 配置支持的js语言选项
  parserOptions: {
    // 支持的js版本
    ecmaVersion: "latest",
    parser: "@typescript-eslint/parser", // 自定义的解析器  eslint默认使用Espree为解析器
    sourceType: "module",
    // 允许使用保留字作为标识符
    allowReserved: false,
    // 要使用哪些附加语言功能
    ecmaFeatures: {
      impliedStrict: true // 启用全局严格模式
    }
  },
  // eslint第三方插件配置
  plugins: ["@typescript-eslint", "vue"],
  // 使用插件配置
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:vue/vue3-essential",
    "@vue/prettier" /*使用prettier*/
  ],
  // eslint规则配置
  rules: {
    "no-var": "error", // 要求使用 let 或 const 而不是 var
    "no-multiple-empty-lines": ["warn", { max: 1 }], // 不允许多个空行
    "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-unexpected-multiline": "error", // 禁止空余的多行
    "no-useless-escape": "off", // 禁止不必要的转义字符
    // typeScript (https://typescript-eslint.io/rules)
    "@typescript-eslint/no-unused-vars": "error", // 禁止定义未使用的变量
    "@typescript-eslint/prefer-ts-expect-error": "error", // 禁止使用 @ts-ignore
    "@typescript-eslint/no-explicit-any": "off", // 禁止使用 any 类型
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-namespace": "off", // 禁止使用自定义 TypeScript 模块和命名空间。
    "@typescript-eslint/semi": "off",
    // eslint-plugin-vue (https://eslint.vuejs.org/rules/)
    "vue/multi-word-component-names": "off", // 要求组件名称始终为 “-” 链接的单词
    "vue/script-setup-uses-vars": "error", // 防止<script setup>使用的变量<template>被标记为未使用
    "vue/no-mutating-props": "off", // 不允许组件 prop的改变
    "vue/attribute-hyphenation": "off", // 对模板中的自定义组件强制执行属性命名样式
    "prettier/prettier": "error"
  }
};
