<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新氧画廊扩展</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .file-check {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .file-check.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .file-check.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .file-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>新氧画廊 VSCode 扩展测试</h1>
        
        <h2>文件检查结果</h2>
        <div id="file-checks"></div>
        
        <h2>找到的文件列表</h2>
        <div id="file-list" class="file-list"></div>
        
        <h2>模拟扩展加载</h2>
        <button onclick="simulateExtensionLoad()">模拟加载扩展</button>
        <div id="simulation-result"></div>
    </div>

    <script>
        // 模拟VSCode环境
        window.__VSCODE_ENV__ = true;
        window.acquireVsCodeApi = () => ({
            postMessage: (msg) => console.log('VSCode message:', msg),
            setState: (state) => console.log('VSCode setState:', state),
            getState: () => console.log('VSCode getState called')
        });

        function checkFile(url, name) {
            return fetch(url)
                .then(response => {
                    if (response.ok) {
                        return { name, status: 'success', size: response.headers.get('content-length') };
                    } else {
                        return { name, status: 'error', error: `HTTP ${response.status}` };
                    }
                })
                .catch(error => {
                    return { name, status: 'error', error: error.message };
                });
        }

        function displayFileCheck(result) {
            const container = document.getElementById('file-checks');
            const div = document.createElement('div');
            div.className = `file-check ${result.status}`;
            
            if (result.status === 'success') {
                div.innerHTML = `✅ ${result.name} - 加载成功 (${result.size || 'unknown'} bytes)`;
            } else {
                div.innerHTML = `❌ ${result.name} - 加载失败: ${result.error}`;
            }
            
            container.appendChild(div);
        }

        function simulateExtensionLoad() {
            const resultDiv = document.getElementById('simulation-result');
            resultDiv.innerHTML = '<p>正在模拟扩展加载...</p>';
            
            // 模拟检查文件
            const filesToCheck = [
                { url: './dist-vscode/index.7c7944d0.js', name: 'Main JS File' },
                { url: './dist-vscode/index.167d386e.css', name: 'Main CSS File' },
                { url: './public/common.css', name: 'Common CSS' },
                { url: './public/favicon.ico', name: 'Favicon' }
            ];

            Promise.all(filesToCheck.map(file => checkFile(file.url, file.name)))
                .then(results => {
                    results.forEach(displayFileCheck);
                    
                    const successCount = results.filter(r => r.status === 'success').length;
                    const totalCount = results.length;
                    
                    resultDiv.innerHTML = `
                        <p><strong>检查完成:</strong> ${successCount}/${totalCount} 文件可访问</p>
                        ${successCount === totalCount ? 
                            '<p style="color: green;">✅ 所有文件都可以正常访问，扩展应该能正常工作</p>' : 
                            '<p style="color: red;">❌ 部分文件无法访问，可能影响扩展功能</p>'
                        }
                    `;
                });
        }

        // 页面加载时显示文件列表
        window.addEventListener('load', () => {
            fetch('./dist-vscode/')
                .then(response => response.text())
                .then(html => {
                    // 简单解析目录列表（这在实际环境中可能不工作）
                    document.getElementById('file-list').innerHTML = 
                        '注意：此测试页面需要在本地服务器环境中运行才能正确检查文件访问性。';
                })
                .catch(() => {
                    document.getElementById('file-list').innerHTML = 
                        '无法获取文件列表。请确保在正确的目录中运行此测试。';
                });
        });
    </script>
</body>
</html>
