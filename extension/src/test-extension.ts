import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('🔥 TEST: Extension is activating...');
    
    // 立即显示通知
    vscode.window.showInformationMessage('🎉 测试扩展已激活！这证明扩展系统工作正常。');
    
    // 注册一个简单的命令
    const disposable = vscode.commands.registerCommand('test.hello', () => {
        vscode.window.showInformationMessage('Hello from Test Extension!');
    });
    
    context.subscriptions.push(disposable);
    
    console.log('✅ TEST: Extension activated successfully');
}

export function deactivate() {
    console.log('🔥 TEST: Extension is deactivating...');
}
