import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 SY Gallery extension is being activated');
    console.log('Extension context:', context);
    console.log('Extension URI:', context.extensionUri.toString());

    // 立即显示一个信息消息来确认扩展已激活
    vscode.window.showInformationMessage('🎉 新氧画廊扩展已激活！如果你看到这个消息，说明扩展正常工作！');
    console.log('✨ Activation message shown');

    // 注册打开画廊的命令
    const disposable = vscode.commands.registerCommand('sy-gallery.open', () => {
        console.log('🎯 SY Gallery command executed');
        vscode.window.showInformationMessage('正在打开新氧画廊...');
        SYGalleryPanel.createOrShow(context.extensionUri);
    });

    context.subscriptions.push(disposable);

    // 注册webview面板序列化器，用于恢复webview状态
    if (vscode.window.registerWebviewPanelSerializer) {
        vscode.window.registerWebviewPanelSerializer(SYGalleryPanel.viewType, {
            async deserializeWebviewPanel(webviewPanel: vscode.WebviewPanel, state: any) {
                SYGalleryPanel.revive(webviewPanel, context.extensionUri);
            }
        });
    }

    console.log('✅ SY Gallery extension activated successfully');
}

export function deactivate() {}

/**
 * 管理SY Gallery webview面板
 */
class SYGalleryPanel {
    /**
     * Track the currently opened panel. 只允许存在一个面板
     */
    public static currentPanel: SYGalleryPanel | undefined;

    public static readonly viewType = 'syGallery';

    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // 如果已经有一个面板存在，显示它
        if (SYGalleryPanel.currentPanel) {
            SYGalleryPanel.currentPanel._panel.reveal(column);
            return;
        }

        // 否则，创建新面板
        const panel = vscode.window.createWebviewPanel(
            SYGalleryPanel.viewType,
            '新氧画廊',
            column || vscode.ViewColumn.One,
            {
                // 启用JavaScript
                enableScripts: true,
                // 保持webview内容状态
                retainContextWhenHidden: true,
                // 限制webview只能访问特定目录
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'dist'),
                    vscode.Uri.joinPath(extensionUri, '..', 'dist'),
                    vscode.Uri.joinPath(extensionUri, '..', 'public')
                ]
            }
        );

        SYGalleryPanel.currentPanel = new SYGalleryPanel(panel, extensionUri);
    }

    public static revive(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        SYGalleryPanel.currentPanel = new SYGalleryPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        // 设置webview的HTML内容
        this._update();

        // 监听面板关闭事件
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // 监听webview消息
        this._panel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'alert':
                        vscode.window.showInformationMessage(message.text);
                        break;
                    case 'error':
                        vscode.window.showErrorMessage(message.text);
                        break;
                }
            },
            null,
            this._disposables
        );
    }

    private _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        // 构建资源文件的URI
        const galleryDistPath = vscode.Uri.joinPath(this._extensionUri, '..', 'dist');
        const publicPath = vscode.Uri.joinPath(this._extensionUri, '..', 'public');
        
        // 获取构建后的CSS和JS文件
        let stylesUri = '';
        let scriptUri = '';
        
        try {
            const distPath = path.join(this._extensionUri.fsPath, '..', 'dist');
            if (fs.existsSync(distPath)) {
                const files = fs.readdirSync(distPath);
                const cssFile = files.find(file => file.startsWith('index') && file.endsWith('.css'));
                const jsFile = files.find(file => file.startsWith('index') && file.endsWith('.js'));
                
                if (cssFile) {
                    stylesUri = webview.asWebviewUri(vscode.Uri.joinPath(galleryDistPath, cssFile)).toString();
                }
                if (jsFile) {
                    scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(galleryDistPath, jsFile)).toString();
                }
            }
        } catch (error) {
            console.error('Error reading dist files:', error);
        }

        // 获取公共资源URI
        const commonCssUri = webview.asWebviewUri(vscode.Uri.joinPath(publicPath, 'common.css'));
        const faviconUri = webview.asWebviewUri(vscode.Uri.joinPath(publicPath, 'favicon.ico'));

        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新氧画廊</title>
    <link rel="icon" href="${faviconUri}" />
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_2598030_cltf7hf8v8e.css" />
    <link rel="stylesheet" href="${commonCssUri}" />
    ${stylesUri ? `<link rel="stylesheet" href="${stylesUri}" />` : ''}
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
        }
        #app {
            width: 100%;
            height: 100vh;
        }
        /* VSCode主题适配 */
        .vscode-dark {
            --primary-color: #007acc;
        }
        .vscode-light {
            --primary-color: #0066cc;
        }
    </style>
</head>
<body class="vscode-body">
    <div id="app">
        ${!scriptUri ? '<div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-size: 18px;">正在加载新氧画廊...</div>' : ''}
    </div>
    
    <script>
        // VSCode API
        const vscode = acquireVsCodeApi();
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            vscode.postMessage({
                command: 'error',
                text: '发生错误: ' + event.error.message
            });
        });
        
        // 为Vue应用提供VSCode环境检测
        window.__VSCODE_ENV__ = true;
        window.__VSCODE_API__ = vscode;
    </script>
    
    ${scriptUri ? `<script type="module" src="${scriptUri}"></script>` : ''}
</body>
</html>`;
    }

    public dispose() {
        SYGalleryPanel.currentPanel = undefined;

        // 清理资源
        this._panel.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
