{"name": "sy-gallery-vscode", "displayName": "新氧画廊", "description": "新氧画廊-素材内容整合创意平台的VSCode扩展", "version": "1.0.0", "publisher": "local-dev", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["gallery", "design", "materials", "creative"], "activationEvents": ["*"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "sy-gallery.open", "title": "打开新氧画廊", "category": "SY Gallery"}], "menus": {"commandPalette": [{"command": "sy-gallery.open", "title": "打开新氧画廊"}], "explorer/context": [{"command": "sy-gallery.open", "title": "在新氧画廊中打开", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "18.x", "typescript": "^4.9.4"}}