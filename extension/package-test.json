{"name": "test-extension", "displayName": "测试扩展", "description": "用于测试VSCode扩展基础功能", "version": "1.0.0", "publisher": "local-test", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/test-extension.js", "contributes": {"commands": [{"command": "test.hello", "title": "Hello Test", "category": "Test"}]}, "scripts": {"compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "18.x", "typescript": "^4.9.4"}}