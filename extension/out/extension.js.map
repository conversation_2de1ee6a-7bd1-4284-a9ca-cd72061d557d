{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAEzB,SAAgB,QAAQ,CAAC,OAAgC;IACrD,YAAY;IACZ,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACvE,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvC,gCAAgC;IAChC,IAAI,MAAM,CAAC,MAAM,CAAC,8BAA8B,EAAE;QAC9C,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,cAAc,CAAC,QAAQ,EAAE;YAClE,KAAK,CAAC,uBAAuB,CAAC,YAAiC,EAAE,KAAU;gBACvE,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9D,CAAC;SACJ,CAAC,CAAC;KACN;AACL,CAAC;AAhBD,4BAgBC;AAED,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B;AAE/B;;GAEG;AACH,MAAM,cAAc;IAYT,MAAM,CAAC,YAAY,CAAC,YAAwB;QAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YACzC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEhB,kBAAkB;QAClB,IAAI,cAAc,CAAC,YAAY,EAAE;YAC7B,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClD,OAAO;SACV;QAED,WAAW;QACX,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,cAAc,CAAC,QAAQ,EACvB,MAAM,EACN,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACI,eAAe;YACf,aAAa,EAAE,IAAI;YACnB,gBAAgB;YAChB,uBAAuB,EAAE,IAAI;YAC7B,oBAAoB;YACpB,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,aAAa,CAAC;gBACtD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,QAAQ,CAAC;aACpD;SACJ,CACJ,CAAC;QAEF,cAAc,CAAC,YAAY,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IAC1E,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAA0B,EAAE,YAAwB;QACrE,cAAc,CAAC,YAAY,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED,YAAoB,KAA0B,EAAE,YAAwB;QAvChE,iBAAY,GAAwB,EAAE,CAAC;QAwC3C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,mBAAmB;QACnB,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,WAAW;QACX,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,cAAc;QACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,OAAO;oBACR,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnD,MAAM;gBACV,KAAK,OAAO;oBACR,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC7C,MAAM;aACb;QACL,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CACpB,CAAC;IACN,CAAC;IAEO,OAAO;QACX,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,iCAAiC;QACjC,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACrF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAE3E,iBAAiB;QACjB,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI;YACA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;YAEpD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACzB,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAErF,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtF,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEpF,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;gBAEtC,IAAI,OAAO,EAAE;oBACT,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;iBAC9F;gBACD,IAAI,MAAM,EAAE;oBACR,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;iBAC7F;aACJ;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,QAAQ,CAAC,CAAC;aACpE;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;SACrD;QAED,YAAY;QACZ,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;QACzF,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;QAExF,OAAO;;;;;;6BAMc,UAAU;;mCAEJ,YAAY;MACzC,SAAS,CAAC,CAAC,CAAC,gCAAgC,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;UAuB5D,CAAC,SAAS,CAAC,CAAC,CAAC,6HAA6H,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;MAoBnJ,SAAS,CAAC,CAAC,CAAC,8BAA8B,SAAS,aAAa,CAAC,CAAC,CAAC,EAAE;;QAEnE,CAAC;IACL,CAAC;IAEM,OAAO;QACV,cAAc,CAAC,YAAY,GAAG,SAAS,CAAC;QAExC,OAAO;QACP,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAClC,IAAI,CAAC,EAAE;gBACH,CAAC,CAAC,OAAO,EAAE,CAAC;aACf;SACJ;IACL,CAAC;;AAxLsB,uBAAQ,GAAG,WAAW,CAAC"}