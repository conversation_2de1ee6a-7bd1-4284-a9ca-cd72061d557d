"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
function activate(context) {
    console.log('🔥 TEST: Extension is activating...');
    // 立即显示通知
    vscode.window.showInformationMessage('🎉 测试扩展已激活！这证明扩展系统工作正常。');
    // 注册一个简单的命令
    const disposable = vscode.commands.registerCommand('test.hello', () => {
        vscode.window.showInformationMessage('Hello from Test Extension!');
    });
    context.subscriptions.push(disposable);
    console.log('✅ TEST: Extension activated successfully');
}
exports.activate = activate;
function deactivate() {
    console.log('🔥 TEST: Extension is deactivating...');
}
exports.deactivate = deactivate;
//# sourceMappingURL=test-extension.js.map