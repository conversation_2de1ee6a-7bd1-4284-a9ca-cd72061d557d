import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
// 用于自动导入elementplus用到的组件
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import viteStyleLint from "vite-plugin-stylelint";
import { fileURLToPath, URL } from "node:url";
import vue from "@vitejs/plugin-vue";
import { defineConfig, loadEnv, UserConfig, UserConfigExport } from "vite";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import Inspect from "vite-plugin-inspect";
const CWD = process.cwd();
export default (config: UserConfig): UserConfigExport => {
  const mode = config.mode as string;
  console.log(loadEnv(mode, CWD));
  const {
    VITE_STATIC_BASE_URL,
    VITE_SERVER_PORT,
    VITE_SERVER_HOST,
    VITE_SERVER_PROXY_TARGET
  }: any = loadEnv(mode, CWD);
  return defineConfig({
    plugins: [
      AutoImport({
        imports: ["vue", "vue-router", "pinia"],
        // 导入存储的文件地址
        dts: "src/auto-import.d.ts",
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
          }),
          // 自动导入图标组件
          IconsResolver({
            prefix: "Icon"
          })
        ]
      }),
      // 自动导入组件
      Components({
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        resolvers: [
          // 自动注册图标组件
          IconsResolver({
            enabledCollections: ["ep"]
          }),
          ElementPlusResolver({
            importStyle: "sass"
          })
        ],
        // 导入存储的文件地址
        dts: "src/components.d.ts"
      }),

      Icons({
        autoInstall: true
      }),

      viteStyleLint(),
      vue(),
      Inspect()
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url))
      },
      extensions: [".mjs", ".js", ".mts", ".ts", ".jsx", ".tsx", ".json"]
    },
    base: VITE_STATIC_BASE_URL,
    server: {
      host: VITE_SERVER_HOST,
      port: VITE_SERVER_PORT,
      strictPort: true,
      open: false, // 设置服务启动时是否自动打开浏览器
      cors: true, // 允许跨域
      headers: {
        "Access-Control-Allow-Origin": "*"
      },
      proxy: {
        "/api/": {
          target: VITE_SERVER_PROXY_TARGET,
          changeOrigin: true,
          secure: false
        }
      }
    },
    // css 处理, 用于后续的主题色
    css: {
      preprocessorOptions: {
        scss: {
          /* .scss全局预定义变量，引入多个文件 以;(分号分割)*/
          additionalData: `@use '@/assets/scss/element-variables.scss' as *;@use '@/assets/scss/global.scss' as *;@use '@/assets/iconfont/iconfont.scss' as *;`
        }
      },
      // 可以查看 CSS 的源码
      devSourcemap: true
    },
    //  生产环境
    build: {
      //指定输出路径
      assetsDir: "./static",
      // 指定输出文件路径
      outDir: "dist",
      minify: "terser",
      sourcemap: true, // 构建后是否生成 source map 文件。如果为 true，将会创建一个独立的 source map 文件。
      // 代码压缩配置
      terserOptions: {
        // 生产环境移除console
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      },
      rollupOptions: {}
    }
  });
};
