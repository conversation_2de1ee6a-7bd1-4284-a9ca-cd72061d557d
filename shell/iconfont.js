import { get } from "http";
import { resolve, join, dirname } from "path";
import { writeFileSync } from "fs";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ICONFONT_DIR = resolve(__dirname, "../src/assets/iconfont");
const ICONFONT_CSS_FILE = join(ICONFONT_DIR, "iconfont.scss");

/**
 * iconfont 点击生成代码，生成的 css 文件
 */
const CSS_URL = "//at.alicdn.com/t/c/font_4426361_e4ctr55mye.css";

function download() {
  get("http:" + CSS_URL, (res) => {
    const { statusCode } = res;

    if (statusCode != 200 && statusCode != 201) {
      console.error("Failed to download:", CSS_URL, ", status code: ", statusCode);
      res.resume();
      return;
    }

    res.setEncoding("utf8");

    let rawData = "";
    res.on("data", (chunk) => {
      rawData += chunk;
    });

    res.on("end", () => {
      rawData = rawData.replace(".iconfont {", '[class^="sy-gicon-"],[class*="sy-gicon-"] {');
      rawData = rawData.replace("font-size: 16px;", "");
      rawData = rawData.replace(/url\(.*format\('woff'\),/, "");
      rawData = rawData.replace(/url\(.*format\('truetype'\),/, "");
      rawData = rawData.replace(/url\(.*format\('svg'\);/, "");
      rawData = rawData.replace(/src:\s*url\(.*\/\* IE9 \*\//, "");
      rawData = rawData.replace(/url\(.*\/\* IE6-IE8 \*\//, "");
      rawData = rawData.replace(/format\('woff2'\),/, "format('woff2');");

      writeFileSync(ICONFONT_CSS_FILE, rawData);
    });

    res.on("error", (err) => {
      console.error("Failed to read response: ", err);
    });
  }).on("error", (err) => {
    console.error("Failed to download:", CSS_URL, ", error: ", err);
  });
}

download();
