module.exports = {
  singleQuote: false,
  semi: true, // 结尾加分号
  trailingComma: "none", // 将多行JSX元素放在最后一行的末尾
  printWidth: 100,
  arrowParens: "always",
  tabWidth: 2, // 指定每个缩进级别的空格数字
  endOfLine: "auto",
  overrides: [
    {
      files: ".prettierrc",
      options: { parser: "json" }
    },
    {
      files: "*.vue",
      options: {
        parser: "vue",
        printWidth: 300
      }
    }
  ]
};
