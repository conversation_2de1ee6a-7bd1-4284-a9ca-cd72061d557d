# VSCode扩展调试检查清单

## 🔍 问题排查步骤

### 1. 扩展是否正确激活？
- [ ] 在Extension Host输出中看到 "SY Gallery extension is being activated"
- [ ] 在Extension Host输出中看到 "SY Gallery extension activated successfully"
- [ ] 命令面板中能找到 "打开新氧画廊" 命令

### 2. 文件路径是否正确？
- [ ] dist-vscode目录存在
- [ ] index.*.js 文件存在
- [ ] index.*.css 文件存在
- [ ] 在日志中看到正确的文件路径

### 3. Webview是否创建成功？
- [ ] 扩展面板打开
- [ ] 面板标题显示 "新氧画廊"
- [ ] 面板不是完全空白

### 4. 资源加载是否成功？
- [ ] 在webview的Network标签中看到CSS/JS请求
- [ ] 资源请求返回200状态码
- [ ] 没有CORS错误

### 5. Vue应用是否启动？
- [ ] 在webview console中看到 "Initializing in VSCode environment"
- [ ] 在webview console中看到 "新氧画廊已成功加载"
- [ ] 没有JavaScript错误

## 🚨 常见错误及解决方案

### 错误1: "找不到命令"
**症状**: 命令面板中没有 "打开新氧画廊" 命令
**解决**: 
1. 检查extension/package.json中的commands配置
2. 重新编译扩展: `npm run extension:compile`
3. 重启调试会话

### 错误2: "资源加载失败"
**症状**: 网络请求失败，404错误
**解决**:
1. 确认dist-vscode目录存在
2. 检查localResourceRoots配置
3. 重新构建: `npm run vscode:build`

### 错误3: "Vue应用不启动"
**症状**: 页面显示加载中，但Vue应用没有初始化
**解决**:
1. 检查main-vscode.ts中的VSCode环境检测
2. 查看webview console中的错误信息
3. 确认API调用被正确跳过

### 错误4: "样式丢失"
**症状**: 页面显示但样式不正确
**解决**:
1. 检查CSS文件是否正确加载
2. 确认所有CSS文件都被包含
3. 检查VSCode主题适配

## 🔧 调试命令

```bash
# 重新构建所有内容
npm run vscode:build

# 只重新编译扩展
npm run extension:compile

# 检查文件是否存在
ls -la dist-vscode/index.*

# 查看扩展日志
# 在VSCode中: View > Output > Extension Host
```

## 📝 调试技巧

1. **使用console.log**: 在extension.ts中添加更多日志
2. **检查webview HTML**: 在_getHtmlForWebview方法中打印生成的HTML
3. **验证文件权限**: 确保VSCode能访问dist-vscode目录
4. **测试简化版本**: 先用简单的HTML测试webview是否工作

## 🎯 成功标志

当一切正常工作时，你应该看到：
- ✅ 扩展成功激活
- ✅ 命令可以执行
- ✅ Webview面板打开
- ✅ 资源正确加载
- ✅ Vue应用启动
- ✅ 界面正常显示
