import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('🔥 MINIMAL TEST: Extension is activating...');
    
    // 立即显示通知 - 这是最重要的测试
    vscode.window.showInformationMessage('🎉 最小测试扩展已激活！如果你看到这个消息，说明VSCode扩展开发环境工作正常！');
    
    // 注册一个简单的命令
    const disposable = vscode.commands.registerCommand('test.hello', () => {
        vscode.window.showInformationMessage('Hello World from Test Extension!');
        console.log('🎯 Test command executed successfully');
    });
    
    context.subscriptions.push(disposable);
    
    console.log('✅ MINIMAL TEST: Extension activated successfully');
}

export function deactivate() {
    console.log('🔥 MINIMAL TEST: Extension is deactivating...');
}
