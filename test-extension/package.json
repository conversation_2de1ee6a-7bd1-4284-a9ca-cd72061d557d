{"name": "minimal-test-extension", "displayName": "最小测试扩展", "description": "用于验证VSCode扩展开发环境", "version": "1.0.0", "publisher": "test", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "test.hello", "title": "Hello World", "category": "Test"}]}, "scripts": {"compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "18.x", "typescript": "^4.9.4"}}