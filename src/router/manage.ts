import { RouteRecordRaw } from "vue-router";

const ManageRouterList: RouteRecordRaw[] = [
  {
    path: "/manage/index", // 画廊后台管理
    name: "manage",
    redirect: "/manage/category-list",
    component: () => import("@/views/admin/index.vue"),
    children: [
      {
        path: "/manage/category-list",
        name: "category",
        component: () => import("@/views/admin/categoryManage/index.vue"),
        meta: {
          name: "分类管理",
          isNav: true // 是否显示导航栏
        }
      },
      {
        path: "/manage/material-list",
        name: "material",
        component: () => import("@/views/admin/material/index.vue"),
        meta: {
          name: "素材管理",
          isNav: true
        }
      },
      {
        path: "/manage/app-list",
        name: "app",
        component: () => import("@/views/admin/appManage/index.vue"),
        meta: {
          name: "应用管理",
          isNav: true // 是否显示导航栏
        }
      },
      {
        path: "/manage/module-list",
        name: "module",
        component: () => import("@/views/admin/moduleManage/index.vue"),
        meta: {
          name: "模块管理",
          isNav: true // 是否显示导航栏
        }
      }
    ]
  }
];

export default ManageRouterList;
