import { RouteRecordRaw } from "vue-router";
// import manageRouterList from "./manage";

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "home",
    redirect: "/item/project",
    children: [
      {
        path: "/home/<USER>", // 首页
        name: "home",
        component: () => import("@/views/layouts/home/<USER>/index.vue")
      },
      {
        path: "/home/<USER>", // 工作台
        name: "workbench",
        component: () => import("@/views/layouts/home/<USER>/index.vue")
      }
    ]
  },
  {
    path: "/item/project",
    name: "item",
    redirect: "/item/project/index",
    component: () => import("@/views/designCooperate/home.vue"),
    children: [
      {
        path: "/item/project/index",
        name: "index",
        component: () => import("@/views/designCooperate/index.vue"),
        meta: {
          name: "设计协作工作台"
        }
      },
      {
        path: "/item/project/stage",
        name: "stage",
        component: () => import("@/views/designCooperate/list.vue"),
        meta: {
          name: "设计协作工作台"
        }
      },
      {
        path: "/item/project/detail",
        name: "detail",
        component: () => import("@/views/designCooperate/detail.vue"),
        meta: {
          name: "设计协作工作台"
        }
      },
      {
        path: "/item/project/check",
        name: "authCheck",
        component: () => import("@/views/designCooperate/authCheck.vue")
      }
    ]
  },
  {
    path: "/viewImage", // 查看素材详情页
    name: "viewImage",
    component: () => import("@/views/layouts/viewImage/index.vue")
  },
  {
    path: "/screenCode",
    name: "screenCode",
    component: () => import("@/views/layouts/screenToCode/index.vue")
  },
  {
    path: "/sketch",
    name: "Sketch",
    children: [
      {
        path: "/sketch/folderChoose",
        name: "folderChoose",
        component: () => import("@/views/layouts/sketch/folderChoose.vue")
      }
    ]
  },

  {
    path: "/:w+",
    name: "*",
    redirect: "/home/<USER>"
  }
  // ...manageRouterList
];
export default routes;
