import { createRouter, createWebHashHistory } from "vue-router";
/**
 * createWebHistory: 路由history模式，核心就是使用historyAPI
 * createWebHashHistory：路由hash模式，核心是根据url的hash值变化来实现的
 */
import routes from "./router";

const router = createRouter({
  history: createWebHashHistory(),
  routes
});
// router.beforeEach((to, from, next) => {
//   if (to.path == "/home/<USER>" || to.path == "/") {
//     next();
//   } else {
//     const userInfo = userInfoStore();
//     if (userInfo.syUid) {
//       next();
//     } else {
//       window.location.href =
//         "/api/user/login?return_url=" + `${encodeURIComponent(window.location.origin)}/#${to.path}`;
//     }
//   }
// });
export default router;
