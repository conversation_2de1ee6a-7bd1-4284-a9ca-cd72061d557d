
html, body, #app {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: Avenir, Helvetica, Arial, sans-serif;
    a {
      text-decoration: none;
      // color: #333;
     }
    ul, li {
      list-style: none;
     }
   }
   /*设置滚动条样式*/
   ::-webkit-scrollbar {
    width: 5px;
   }

   /*定义滚动条轨道 内阴影+圆角*/
   ::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.1);
   }
   /*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(26, 25, 25, 0.3);
    background-color: rgba(0, 0, 0, 0.1);
   }
