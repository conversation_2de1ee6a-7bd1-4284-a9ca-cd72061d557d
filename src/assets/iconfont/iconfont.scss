@font-face {
  font-family: "iconfont"; /* Project id 4426361 */
  src: url('//at.alicdn.com/t/c/font_4426361_e4ctr55mye.woff2?t=1716950566013') format('woff2');
}

[class^="sy-gicon-"],[class*="sy-gicon-"] {
  font-family: "iconfont" !important;
  
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.sy-gicon-tuandui:before {
  content: "\e71c";
}

.sy-gicon-zhaopian:before {
  content: "\e6ba";
}

.sy-gicon-file-:before {
  content: "\e83e";
}

.sy-gicon-mouseM:before {
  content: "\e6bd";
}

.sy-gicon-kongge:before {
  content: "\e851";
}

.sy-gicon-kuaijiejian:before {
  content: "\e720";
}

.sy-gicon-option:before {
  content: "\e60c";
}

.sy-gicon-plus_app:before {
  content: "\e87a";
}

.sy-gicon-jian1:before {
  content: "\e673";
}

.sy-gicon-vuesax-linear-command-square:before {
  content: "\e623";
}

.sy-gicon-plus-border:before {
  content: "\e627";
}

.sy-gicon-jian:before {
  content: "\e655";
}

