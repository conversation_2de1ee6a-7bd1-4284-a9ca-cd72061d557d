import { createApp } from "vue";
import App from "@/views/app.vue";
import router from "@/router/index";
import Vue<PERSON>on<PERSON> from "vue-konva";
import { createPinia } from "pinia";
import "element-plus/dist/index.css";
import VueLazyload from 'vue-lazyload'
const clickOutside = {
  beforeMount: (el, binding) => {
    el.clickOutsideEvent = (event) => {
      if (!(el == event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    document.body.addEventListener("click", el.clickOutsideEvent);
  },
  unmounted: (el) => {
    document.body.removeEventListener("click", el.clickOutsideEvent);
  }
};




const app = createApp(App);
app.use(VueKonva);
app.use(createPinia());
app.use(router);
app.directive("click-outside", clickOutside);

app.use(VueLazyload, {
  preLoad: 1.3,
  attempt: 1,
  filter: {
    webp(listener, options) {
      const isCDN = /img2.soyoung.com|static.soyoung.com/;
      const imageView2 = "?imageView2/0";
      if (isCDN.test(listener.src) && !listener.src.includes("?")) {
        if (!options.supportWebp) return;
        listener.src = `${listener.src}${imageView2}/format/webp`;
      }
    }
  }
});

app.mount("#app");
