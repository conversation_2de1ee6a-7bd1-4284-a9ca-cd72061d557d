export enum ErrorCode {
  OK = 0, // 正常处理完成

  ILLEGAL = 100000, // 全局错误10
  SERVER_REPAIRING = 100001, // 服务器正在维护中
  NOT_ALLOWED = 100002, // 无权限进行此操作
  MOCK_LOGIN_NOT_PERMIT = 100003, // 免登陆不允许进行此操作

  PARAMS = 100100, // 参数错误
  NOT_FOUND = 100101, // 数据未找到
  // 验证相关
  AUTH = 100200, // 用户验证失败
  LOGIN_VALIDATION = 100201, // 用户登录失败

  // body相关
  PAYLOAD_TOOLLARGE = 100400, // 负荷太大 （ body 超出 了最大限制 ）
  UPLOAD_ERROR = 100401, // 上传失败

  // 数据库相关
  DB_DATA_NOT_FOUND = 100501 // 未找到对应数据
}

export enum Permission {
  OWNER = "owner", // 所有者， 这个数据是创建者，不会存在数据库中
  EDIT = "edit", // 编辑模式
  MANAGE = "manage", // 管理模式
  PREVIEW = "preview" // 预览模式
}

export const permissionName = {
  [Permission.EDIT]: "编辑",
  [Permission.MANAGE]: "管理",
  [Permission.PREVIEW]: "预览"
};
// 邀请分类
export enum InviteCategory {
  SMB = "smb"
}

export enum NotifyCategory {
  PROJECT = "project", // 项目
  TEAM = "team" //  团队
}

export enum NotifyAction {
  ADD = "add", // 新增
  UPDATE = "update", // 更新
  DELETE = "delete", // 删除
  INVITE = "invite" // 邀请
}
