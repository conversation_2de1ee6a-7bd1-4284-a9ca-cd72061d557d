<template>
  <div
    :class="{
      home: true,
      'home-theme': store.themeShow
    }"
  >
    <sp-header :headBgColor="store.themeShow ? '#26282B' : '#FFFFFF'">
      <sp-login></sp-login>
    </sp-header>

    <div class="home__line" :style="{ background: store.themeShow ? '#000000' : '#F5F5F5' }"></div>

    <div class="home-body">
      <!--      &lt;!&ndash; 左侧导航部分内容 start &ndash;&gt;-->
      <!--      <div class="home-body__left">-->
      <!--        &lt;!&ndash; 灵感集&设计协作tabs start &ndash;&gt;-->
      <!--        <sp-tabs></sp-tabs>-->
      <!--        &lt;!&ndash; 灵感集&设计协作tabs end &ndash;&gt;-->

      <!--        &lt;!&ndash; 灵感集导航 start &ndash;&gt;-->
      <!--        <sp-nav @tabs-change="tabsChange" :data="routerListData"></sp-nav>-->
      <!--        &lt;!&ndash; 灵感集导航 end &ndash;&gt;-->
      <!--      </div>-->
      <!-- 左侧导航部分内容 end -->

      <!-- 右侧内容 start -->
      <div class="home-body__right">
        <!-- 图片放大 start -->
        <view-larger-image></view-larger-image>
        <!-- 图片放大 end -->
      </div>
      <!-- 右侧内容 start -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import SpHeader from "@/views/layouts/components/spHeader.vue";
import SpLogin from "@/views/layouts/components/spLogin.vue";
import SpNav from "@/views/layouts/components/spNav.vue";
import SpTabs from "@/views/layouts/components/tabs.vue";
import { themeStore, materialStore } from "@/store";
import HomeBodyHeader from "@/views/layouts/home/<USER>/homeBodyHeader.vue";
import HomeBodyContainer from "@/views/layouts/home/<USER>/homeBodyContainer.vue";
import ViewLargerImage from "@/views/layouts/viewImage/components/viewLargerImage.vue";
import { appsList } from "@/api/common";
import { onMounted } from "vue";

const store = themeStore(); // 黑白主题切换Store
const material = materialStore(); // 素材Store

const appOrDesign = ref<number>(1); // 1: 'app' or 'it'

const routerListData = ref<any>([{ _id: 1, name: "全部应用" }]);
const tabsChange = (item: any) => {
  console.log(item, "Item");
  appOrDesign.value = +item._id;
  material.updateMaterialInfo(item);
};

const initData = async () => {
  const data = await appsList({});
  console.log(data, "应用列表");
  if (data.data.code === 0) {
    routerListData.value = [...routerListData.value, ...data.data.data];
  }
};

onMounted(() => {
  initData();
});
</script>
<style lang="less">
.home {
  width: 100%;
  display: flex;
  flex-direction: column;
  //height: calc(100vh - 1px);
  &::-webkit-scrollbar {
    display: none;
  }

  .home__line {
    width: 100%;
    height: 1px;
    background: #f5f5f5;
  }

  .home-body {
    width: 100%;
    display: flex;
    flex-direction: row;

    .home-body__left {
      width: 250px;
      display: flex;
      flex-direction: column;
    }

    .home-body__right {
      width: 100%;
      position: relative;
      height: calc(100vh - 64px);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }

  &.home-theme {
    .home-body {
      .home-body__right {
        .home-body-right__header {
          background: #26282b;

          .swiper-list-tool__bg {
            background: #000000;
            opacity: 0.4;
          }

          .previous-icon {
            color: #5c54f0;
            z-index: 1;
          }

          .home-body-right-header__more {
            .more-list__item {
              label {
                color: #ffffff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
