<template>
  <div class="view-larger-image">
    <div class="view-larger-image__left">
      <div class="view-larger-image-left__header">
        <!--        <div class="view-larger-image-header__left">-->
        <!--          <img src="https://static.soyoung.com/sy-pre/3re6h6fgwsmoq-1697807400719.png" alt="" @click="handleClickClose">-->
        <!--        </div>-->
        <div class="view-larger-image-header__right">
          <img loading="lazy" src="https://static.soyoung.com/sy-pre/1rvzbhc4prwgs-1697808769379.png" alt="" @click="handleClickZoom(1)" />
          <img loading="lazy" src="https://static.soyoung.com/sy-pre/3g0qyyheb6e5d-1697808769379.png" alt="" @click="handleClickZoom(0.5)" />
          <!--          <img src="https://static.soyoung.com/sy-pre/1nubvuocuun6k-1697808769379.png" alt="">-->
          <img loading="lazy" src="https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697808769379.png" alt="" @click="handleClickDownload" />
        </div>
      </div>
      <div class="view-large-image-left__body">
        <div class="view-large-image-left-body__cover">
          <img loading="lazy" id="imgTooles" :data-original="`http://*************:7777${info?.url}`" :src="`http://*************:7777${info?.url}`" object-fit="contain" alt="" />
        </div>
      </div>
    </div>
    <div class="view-larger-image__right">
      <div class="view-larger-image-right__content">
        <div class="view-larger-image-right-content__title">
          <span>基础信息</span>
        </div>
        <div class="view-larger-image-right-content__item">
          <label>文件大小</label>
          <span>{{ (info?.size / 1000).toFixed(2) }}mb</span>
        </div>
        <div class="view-larger-image-right-content__item">
          <label>尺寸</label>
          <span>{{ info?.width }} * {{ info?.height }}</span>
        </div>
        <div class="view-larger-image-right-content__item">
          <label>文件格式</label>
          <span>{{ info?.url?.split(".")[1] }}</span>
        </div>
        <div class="view-larger-image-right-content__item">
          <label>添加日期</label>
          <span>{{ new Date(parseInt(info?.createTime)).toLocaleString().replace(/:\d{1,2}$/, " ") }}</span>
        </div>
        <div class="view-larger-image-right-content__item origin__path">
          <label>来源路径</label>
          <div class="origin-path__content">
            <div class="origin-path__item">
              <img loading="lazy" src="https://static.soyoung.com/sy-pre/pic_1684138594288-1697631000719.png" alt="" />
              <span>{{ info?.appName }} V{{ info.appVersion }}</span>
            </div>
            <div class="origin-path__item">
              <img loading="lazy" src="https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png" alt="" />
              <span>{{ info?.appTags }}</span>
            </div>
          </div>
          <div class="view-larger-image-right__content">
            <div class="view-larger-image-right-content__title">
              <!--              <span style="display:inline-block; margin-bottom: 10px;">灵感升级</span>-->
              <div>
                <el-button type="primary" @click="handleClickAI">AI灵感升级</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { aiGenerated, moduleList } from "@/api/upload";
import { appsList } from "@/api/common";
import { ElMessageBox, ElLoading } from "element-plus";
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.css";
import { versionList } from "@/api/common";

type InfoTypeData = {
  url: string;
  size: number;
  width: number;
  height: number;
  createTime: string;
  appName: string;
  appVersion: string;
  appTags: string[];
};

const route = useRoute();

let info = ref<InfoTypeData>({
  url: "",
  size: 0,
  width: 0,
  height: 0,
  createTime: "",
  appName: "",
  appTags: [],
  appVersion: ""
});
let viewer = ref<any>({});
const router = useRouter();

const handleClickAI = async () => {
  ElMessageBox.confirm("确认以此图喂给AI生成新图?时间有点长耐心等待啊", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      const res = await aiGenerated({
        name: info.value.appTags || "首页"
      });

      if (res?.data?.data) {
        loading.close();
        window.open(res?.data?.data);
      }
    })
    .catch(() => {
      // window.open(res?.data?.data);
    });
};

const appData = ref<any>([]);
const getAppData = async () => {
  const data = await appsList({});
  console.log(data, "数据");
  if (data.data.code === 0) {
    appData.value = data.data.data;
  }
};

const tagsData = ref<any>([]); // TAGS Data
const getModuleList = async () => {
  const data = await moduleList({});
  if (data.data.code === 0) {
    tagsData.value = data.data.data;
  }
};
const versionData = ref<any>([]); // TAGS Data
const getVersionData = async () => {
  const data = await versionList({});
  if (data.data.code === 0) {
    versionData.value = data.data.data;
  }
};

const filterName = (type: string, id: string) => {
  console.log(type, id, "数据");
  if (type === "app") {
    return appData.value.filter((e: any) => e._id === id)[0].name;
  } else if (type === "version") {
    return versionData.value.filter((e: any) => e._id === id)[0].name;
  } else {
    const data = tagsData.value.filter((e: any) => e._id === id);
    console.log(data, "标签");
    return data.map((e: any) => e.name).join(",");
  }
};

onMounted(async () => {
  await getAppData();
  await getModuleList();
  await getVersionData();
  const { query } = route;
  info.value = {
    ...query,
    appName: filterName("app", <string>query.appId),
    appVersion: filterName("version", <string>query.version),
    appTags: filterName("tags", <string>query.tags)
  } as any;

  const ViewerDom = document.getElementById("imgTooles");
  //@ts-ignore
  viewer.value = new Viewer(ViewerDom, {
    url: "data-original",
    show: function () {
      //@ts-ignore
      viewer.update();
    }
  });
});

const handleClickClose = () => {
  router.go(-1);
};

const handleClickDownload = () => {
  let url = "http://*************:7777";

  window.open((url += info.value.url));
};

const handleClickZoom = (zoom) => {
  viewer.value.zoomTo(parseInt(zoom));
  viewer.value.show();
};
</script>
<style lang="less">
.view-larger-image {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: row;
  background: #1b1a24;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;

  .view-larger-image__left {
    flex: 1;

    .view-larger-image-left__header {
      width: 100%;
      height: 64px;
      background: #26252e;
      display: flex;
      flex-direction: row;
      align-items: center;

      .view-larger-image-header__left {
        width: 28px;
        height: 28px;
        margin-left: 22px;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .view-larger-image-header__right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        img {
          width: 22px;
          height: 22px;
          margin-right: 20px;
          cursor: pointer;
        }
      }
    }

    .view-large-image-left__body {
      width: 100%;
      height: calc(100% - 64px);
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .view-large-image-left-body__cover {
        width: 100%;
        height: 100%;
        max-width: 300px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        overflow-x: hidden;
        overflow-y: auto;

        img {
          width: 100%;
        }
      }
    }
  }

  .view-larger-image__right {
    width: 300px;
    border-left: 1px solid #ffffff;
    padding: 27px 30px;
    box-sizing: border-box;

    .view-larger-image-right__content {
      width: 100%;
      display: flex;
      flex-direction: column;

      .view-larger-image-right-content__title {
        width: 100%;
        height: 37px;

        span {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 500;
        }
      }

      .view-larger-image-right-content__item {
        width: 100%;
        margin-bottom: 15px;

        label,
        span {
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
        }

        label {
          width: 101px;
          text-align: left;
          display: inline-block;
        }

        &.origin__path {
          display: flex;
          flex-direction: column;

          .origin-path__content {
            width: 100%;
            display: flex;
            flex-direction: column;
            margin-top: 16px;

            .origin-path__item {
              padding: 5px 10px;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              margin-bottom: 15px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 14px;
              font-size: 14px;

              img {
                width: 18px;
                height: 18px;
                border-radius: 4.5px;
                margin-right: 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
