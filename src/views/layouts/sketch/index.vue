<template>
  <div
    :class="{
      home: true,
      'home-theme': store.themeShow
    }"
  >
    <SpHeader> </SpHeader>
    <RouterView></RouterView>
  </div>
</template>
<script lang="ts" setup>
import { themeStore } from "@/store";
import { RouterView } from "vue-router";
import SpHeader from "@/views/layouts/components/spHeader.vue";
const store = themeStore(); // 黑白主题切换Store
</script>
<style lang="less" scoped></style>
