<template>
  <div class="sketch-progress" v-if="progressInfo !== null">
    <div class="sketch-progress-content" v-if="!progressInfo.done">
      <el-progress :color="customColors" :stroke-width="10" indeterminate :width="200" type="dashboard" :percentage="+progressInfo.process">
        <template #default="{ percentage }">
          <span class="percentage-value">{{ percentage }}%</span>
          <span class="percentage-label">{{ progressInfo.name || "" }}</span>
        </template>
      </el-progress>
    </div>
    <template v-else>
      <div class="sketch-progress-done">
        <el-icon class="sketch-progress-done-icon"><CircleCheckFilled /></el-icon>
        <div class="sketch-progress-done-text">上传成功</div>
        <div class="sketch-progress-done-desc">
          新增画板： {{ progressInfo.add.length }} <br />
          更新画板： {{ progressInfo.update.length }}
        </div>
      </div>
      <div class="sketch-progress-btn">
        <el-button type="primary" @click="openExternal" round>查看设计</el-button>
        <el-button @click="progressInfo = null" round>继续上传</el-button>
        <el-button @click="close" round>关 闭</el-button>
      </div>
    </template>
  </div>
  <template v-else>
    <div class="sketch" v-if="userInfo.ssoId">
      <div class="sketch-logo">
        <img loading="lazy" src="https://static.soyoung.com/sy-design/2gv1oqwpo580u1716895637825.png" alt="新氧画廊LOGO" />
        <!-- <el-divider direction="vertical" /> -->
        <div class="sketch-bar">
          <el-dropdown @command="handleCommand" trigger="click">
            <span class="sketch-dropdown-link">
              <div>{{ team.current?.name || "-" }}</div>
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="(item, i) in team.list" :disabled="item.permission !== Permission.OWNER && item.permission !== Permission.MANAGE" :command="i" :key="item._id">
                  <div>
                    {{ item.name }}
                    <div v-if="item.permission !== Permission.OWNER && item.permission !== Permission.MANAGE">(无上传权限)</div>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="create">
                  <el-icon><Plus /></el-icon>
                  新增团队
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <div class="sketch__userInfo" v-if="userInfo.ssoId">
            <img loading="lazy" class="sketch__avatar" :src="userInfo.url || 'https://static.soyoung.com/sy-pre/<EMAIL>'" alt="" />
          </div>
        </div>
      </div>
      <div class="sketch-selected">
        <!-- {{ artBoardList.length }} -->
        <div v-if="!artBoardList.length" class="sketch-selected-placeholder">
          <el-empty description="未选中画板" />
        </div>
        <ul v-else class="sketch-selected-list">
          <li v-for="(name, index) in artBoardList" class="sketch-selected-li" :key="index">
            <el-icon><Monitor /></el-icon> {{ name }}
          </li>
        </ul>
      </div>
      <div class="sketch-radio">
        <el-radio-group v-model="sketchSelectType">
          <el-radio label="1"
            >该页面全部画板<span v-if="sketchSelectType == '1'">({{ artBoardList.length }})</span></el-radio
          >
          <el-radio label="2"
            >选中画板 <span v-if="sketchSelectType == '2'">({{ artBoardList.length }})</span></el-radio
          >
        </el-radio-group>
      </div>
      <div class="sketch-info" @click="projectClick">
        <div class="sketch-info-label">项目</div>
        <div class="sketch-info-content">
          <div class="sketch-info-content__text">
            {{ project.current?.name || "未选择" }}
          </div>
          <el-icon><ArrowRightBold /></el-icon>
        </div>
      </div>
      <div class="sketch-info" @click="groupClick">
        <div class="sketch-info-label">分组</div>
        <div class="sketch-info-content">
          <div class="sketch-info-content__text">
            {{ getGroupName() }}
          </div>
          <el-icon><ArrowRightBold /></el-icon>
        </div>
      </div>
      <!-- <div class="sketch-info" @click="versionClick">
      <div class="sketch-info-label">版本</div>
      <div class="sketch-info-content">
        <div class="sketch-info-content__text">
          {{ version?.name || "未选择" }}
        </div>
        <el-icon><ArrowRightBold /></el-icon>
      </div>
    </div> -->
      <div class="sketch-submit">
        <el-button class="submit-button" @click="submit" type="primary" round>上 传</el-button>
        <el-dropdown @command="settingCommand" trigger="click">
          <span class="sketch-setting">
            <el-icon><Setting /></el-icon>
            设置
          </span>
          <template #dropdown>
            <el-dropdown-menu class="setting-menu">
              <el-dropdown-item disabled>
                <div class="setting-menu-item">
                  <div>版本更新</div>
                  <span>v1.0</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item command="jumpTo"> 前往画廊 </el-dropdown-item>
              <el-dropdown-item divided command="logout"> 退出登录 </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div v-else class="sketch-login">
      <img src="https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png" alt="" />
      <el-button color="#626aef" size="large" :disabled="!!uuid" @click="ssoLogin" dark>登 录</el-button>
    </div>
  </template>

  <el-dialog class="sketch-add" v-model="addTeamInfo.visible" title="新建团队" width="70%">
    <div class="sketch-add-item">
      <el-input placeholder="请输入团队名称" v-model="addTeamInfo.name"></el-input>
    </div>
    <template #footer>
      <span class="sketch-add-footer">
        <el-button @click="handleAddClose">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
  <ProjectDrawer :visible="project.visible" :id="project.current?._id" :teamId="team.current?._id" @close="handleCloseProject" @change="handleProjectChange" />
  <GroupDrawer :visible="group.visible" :id="group.current?._id" :projectId="project.current?._id" @close="handleCloseGroup" @change="handleGroupChange" />
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from "vue";
import { ElMessage } from "element-plus";
import { userInfoStore } from "@/store";
import { Monitor, ArrowRightBold, ArrowDown, CircleCheckFilled, Plus, Setting } from "@element-plus/icons-vue";
import { upload } from "@/api/common";
import { getTeamList, addSketch, sendStatus, addTeam } from "@/api/design";
import { ObjectAny } from "@/types/index";
import ProjectDrawer from "./components/project.vue";
import GroupDrawer from "./components/group.vue";
import { FormItem } from ".";
import { Permission } from "@/model";
import { setUserLogout, userCheckPoling, getUserInfo } from "@/api/login";
import { createPoller } from "@/utils/date";
import { de } from "element-plus/es/locale";
const sketchSelectType = ref("2");
const userInfo = userInfoStore();
const customColors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 }
];

const team = reactive<FormItem>({
  list: [],
  current: null
});
const addTeamInfo = reactive<ObjectAny>({
  visible: false,
  name: ""
});
const project = ref<ObjectAny>({});
const group = ref<ObjectAny>({});
const uuid = ref<string>("");
// const version = ref<ObjectAny>({});
const artBoardList = ref([]);
const timer = ref<any>(null);
const poller = ref<ObjectAny | null>(null);

const progressInfo = ref<null | ObjectAny>(null);
watch(
  () => sketchSelectType.value,
  () => {
    window.postMessage("getSelectedBoard", sketchSelectType.value);
  }
);

const submit = () => {
  if (!group.value?.current) {
    ElMessage.error("请选择分组");
    return;
  }
  progressInfo.value = {
    process: 0
  };
  window.postMessage("submit", sketchSelectType.value);
};
window.sketchError = (e) => {
  console.log("sketchError", e);
};
window.sketchResult = (jsonString) => {
  try {
    uploadJson(jsonString);
  } catch (error) {
    console.log(error);
  }
};
// 上传sketch 资源 到cdn
window.uploadInfo = (info) => {
  // setTimeout(() => {
  //   window.postMessage("uploadResult", {
  //     exportable: [],
  //     index: info.index,
  //     type: info.type
  //   });
  // }, 1000);
  if (info.type === "slice") {
    uploadSlice(info.export, info.index);
  } else {
    uploadArtboard(info.export, info.index);
  }
};
// 上传进度
window.sketchProcess = (process) => {
  progressInfo.value = process;
};
// 获取当前画板的信息
window.sketchArtBoard = (list) => {
  artBoardList.value = list;
};
// const getFullPath = () => {
//   return addSmbInfo.parent?.fullPath.map(({ name }) => name).join(" / ");
// };

const uploadSlice = async (exportInfo, index) => {
  const info: ObjectAny = {
    exportable: [],
    index,
    type: "slice"
  };
  for (let i = 0; i < exportInfo.length; i++) {
    const item = exportInfo[i];
    const path = await uploadBuffer(item);
    info.exportable.push({
      path,
      format: item.format,
      name: item.path
    });
  }
  window.postMessage("uploadResult", info);
};

const uploadArtboard = async (exportInfo, index) => {
  const info: ObjectAny = {
    exportable: {
      format: exportInfo.format,
      name: exportInfo.name
    },
    index,
    type: "artboard"
  };
  info.exportable.path = await uploadBuffer(exportInfo);
  window.postMessage("uploadResult", info);
};
const uploadBuffer = async (options) => {
  const formData = new FormData();
  const arraybuffer = new Int8Array(options.buffer.data);
  const blob = new Blob([arraybuffer], { type: options.format });
  const file = new File([blob], options.name + "." + options.format, {
    type: options.format
  });
  formData.append("file", file);
  const { data } = await upload(formData);
  return data.data.url;
};

const handleCommand = async (command) => {
  if (command === "create") {
    addTeamInfo.visible = true;
    return;
  }
  if (command === undefined) {
    return;
  }
  team.current = team.list[command];
  window.localStorage.setItem("teamId", team.current!._id);
  // team.def = await getTeamDefFolder();
  project.value = {};
  group.value = {};
};

// const getTeamDefFolder = async () => {
//   const res = await getDefFolder({
//     teamId: team.current!._id,
//     type: "default"
//   });
//   return res.data;
// };

const getGroupName = () => {
  if (!group.value?.current) {
    return "未选择";
  }
  return group.value.current.fullPath.map(({ name }) => name).join("/");
};
const handleProjectChange = (info) => {
  project.value = {
    current: info,
    visible: false
  };
  group.value = {};
};
const handleCloseProject = () => {
  project.value.visible = false;
};
const handleGroupChange = (info) => {
  group.value = {
    current: info,
    visible: false
  };
};
const handleCloseGroup = () => {
  group.value.visible = false;
};
const getTeam = async () => {
  const res = await getTeamList({});
  team.list = res.data;
  if (res.data?.length) {
    const teamId = window.localStorage.getItem("teamId");
    let index;
    if (teamId) {
      index = res.data.findIndex((item) => item._id === teamId);
    }
    handleCommand(index || res.data.findIndex((item) => item.permission === Permission.OWNER || item.permission === Permission.MANAGE));
  }
};

function categorizeSlices(slices, artboards) {
  // 创建返回对象
  let categorizedSlices = {};

  // 遍历每个 slice
  slices.forEach((slice) => {
    // 查找对应的 artboard
    artboards.forEach((artboard) => {
      artboard.layers.forEach((layer) => {
        // 检查 slice 是否属于这个 artboard
        if (layer.objectID === slice.objectID) {
          // 如果这个 artboard 还没有在返回对象中，初始化它
          if (!categorizedSlices[artboard.objectID]) {
            categorizedSlices[artboard.objectID] = [];
          }
          // 添加 slice 到对应的 artboard
          categorizedSlices[artboard.objectID].push(slice);
        }
      });
    });
  });

  return categorizedSlices;
}
const uploadJson = async (json) => {
  // progressInfo.value = null;
  //   console.log(JSON.stringify(jsonString));
  const categorizedSlices = categorizeSlices(json.slices, json.artboards);
  progressInfo.value = {
    process: 0,
    name: "上传json"
  };
  const resultList = await Promise.all(
    json.artboards.map(async (artboard, index) => {
      artboard.layers.forEach((layer) => {
        if (layer.exportable) {
          layer.exportable.forEach((exportable) => {
            delete exportable.buffer;
          });
        }
      })
      const res = await addSketch({
        artboard: JSON.stringify(artboard),
        name: artboard.name,
        slices: JSON.stringify(categorizedSlices[artboard.objectID] || []),
        unit: json.unit,
        artId: artboard.objectID,
        pageId: artboard.pageObjectID,
        thumb: artboard.imagePath,
        resolution: json.resolution,
        colorFormat: json.colorFormat,
        colors: JSON.stringify(json.colors),
        groupId: group.value.current?._id,
        projectId: project.value.current?._id,
        index
      });
      return res.data;
    })
  );
  const { add, update } = resultList.reduce(
    (prev, next) => {
      prev[next.updated ? "update" : "add"].push(next.name);
      return prev;
    },
    {
      add: [],
      update: []
    }
  );
  progressInfo.value = {
    process: 100,
    done: true,
    add,
    update,
    name: "上传成功"
  };
  ElMessage.success("上传成功");
  sendStatus({
    projectId: project.value.current?._id,
    groupId: group.value.current?._id,
    sketchs: resultList
  });
  setTimeout(() => {
    // window.postMessage("closed");
  }, 2000);
};
// 点击项目
const projectClick = () => {
  project.value.visible = true;
};
// 点击分组
const groupClick = () => {
  if (!project.value.current) {
    return;
  }
  group.value.visible = true;
};
// 点击版本
// const versionClick = () => {
//   if (!group.value.current) {
//     return;
//   }
//   version.value.visible = true;
// };
onMounted(() => {
  if (!userInfo.ssoId) {
    // ssoLogin();
    return;
  }
  init();
});
function getUUid() {
  let s: any[] = [];
  let hexDigits = "0123456789abcdef";
  for (let i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23];
  let uuid = s.join("");
  return uuid;
}

const checkStatus = async () => {
  const res = await userCheckPoling({
    token: uuid.value
  });
  if (res.code !== 0) {
    ElMessage.error(res.msg);
    uuid.value = "";
    poller.value?.stop();
    return;
  }
  if (res?.data === 1) {
    uuid.value = "";
    poller.value?.stop();
    ElMessage.success("登录成功");
    refreshUserInfo();
  }
};
// 登陆
const ssoLogin = async () => {
  uuid.value = getUUid();
  window.postMessage("openExternalUrl", `${window.location.origin}/#/item/project/check?from=gallery-sketch&token=${uuid.value}`);
  // window.location.href = "/api/user/login?mode=mobile&return_url=" + encodeURIComponent(window.location.href);
  poller.value = createPoller(checkStatus, 2000, 30);
  poller.value.start();
};

const refreshUserInfo = async () => {
  const res = await getUserInfo({});
  if (res.status == 200 && res.data.code == 0) {
    const payload = res.data.data;
    userInfo.updateInfo({
      syUserName: payload.syUserName,
      syUid: payload.syUid,
      ssoId: payload.ssoId,
      type: payload.type,
      syData: payload.syData,
      url: payload.url,
      name: payload.name
    });
    init();
  }
};
const openHeartBeat = () => {
  timer.value = setInterval(() => {
    window.postMessage("getSelectedBoard", sketchSelectType.value);
  }, 1000);
};

const handleAddClose = () => {
  addTeamInfo.visible = false;
  addTeamInfo.name = "";
};

const handleAddSubmit = async () => {
  if (!addTeamInfo.name) {
    return;
  }
  const res = await addTeam({
    name: addTeamInfo.name
  });
  if (res.code === 0) {
    ElMessage.success("添加成功");
    getTeam();
    handleAddClose();
  }
};
const close = () => {
  window.postMessage("closed");
};
const openExternal = () => {
  window.postMessage("openExternalUrl", `${window.location.origin}/#/item/project/stage?teamId=${team.current!._id}&projectId=${project.value.current?._id}`);
};
const settingCommand = (command) => {
  switch (command) {
    case "logout":
      setUserLogout();
      break;
    case "jumpTo":
      window.postMessage("openExternalUrl", `${window.location.origin}/#/item/project/index?teamId=${team.current!._id}`);
      break;
    default:
      break;
  }
};
const init = () => {
  getTeam();
  openHeartBeat();
};
</script>
<style lang="less" scoped>
.sketch {
  background: #fff;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  user-select: none;
  &-logo {
    height: 60px;
    display: flex;
    padding: 0 15px;
    align-items: center;
    justify-content: space-between;
    background: #5c54f0;
    img {
      height: 18px;
    }
    .sketch-dropdown-link {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #ffffff;
      font-weight: 500;
      white-space: nowrap;
      display: flex;
      max-width: calc(100vw - 165px);
      justify-content: space-between;
      margin-left: 10px;
      border-left: 1px solid #fff;
      padding-left: 10px;
      div {
        width: calc(100% - 20px);
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .sketch-bar {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      //
      .el-dropdown {
        cursor: pointer;
      }
      .sketch__userInfo {
        display: flex;
        align-items: center;
        max-width: 100px;
      }
      .sketch__avatar {
        border-radius: 50%;
        height: 25px;
        border: 1px solid #fff;
      }
      .sketch__username {
        color: #fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 5px;
        font-size: 12px;
      }
    }
  }
  &-selected {
    margin: 20px;
    height: 200px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
    box-sizing: border-box;
    font-size: 13px;
    user-select: none;
    &-placeholder {
      width: 100%;
      height: 100%;
      position: relative;
      .el-empty {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        ::v-deep {
          .el-empty__image {
            height: 60px;
          }
        }
      }
    }
    &-list {
      height: 100%;
      width: 100%;
      overflow: overlay;
    }
    &-li {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .el-icon {
        margin-right: 5px;
      }
    }
  }
  &-radio {
    text-align: center;
  }
  &-info {
    margin: 10px;
    font-size: 13px;
    height: 44px;
    display: flex;
    align-items: center;
    border: 1px solid #c7c7c7;
    border-radius: 5px;
    transition: border 0.3s;
    cursor: pointer;
    &:hover {
      border: 1px solid #5c54f0;
    }
    &-label {
      width: 80px;
      text-align: center;
      height: 26px;
      line-height: 26px;
      border-right: 1px solid #c7c7c7;
    }
    &-content {
      flex: 1;
      display: flex;
      padding: 0 10px;
      &__text {
        flex: 1;
      }
      .el-icon {
        font-size: 15px;
      }
    }
  }
  &-submit {
    position: absolute;
    bottom: 25px;
    left: 20px;
    right: 20px;
    .submit-button {
      height: 45px;
      width: 100%;
      margin-bottom: 16px;
    }
    .sketch-setting {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #262626;
      letter-spacing: 0;
      font-weight: 500;
      .el-icon {
        margin-right: 5px;
      }
    }
  }

  &-login {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    box-sizing: border-box;
    padding-top: 30vh;
    .el-button {
      margin-top: 50px;
      width: 130px;
    }
  }
}
.sketch-progress {
  background: rgba(255, 255, 255, 0.8);
  height: 100vh;
  box-sizing: border-box;
  padding-top: 100px;
  padding-bottom: 40px;
  display: flex;
  flex-direction: column;
  &-content {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-done {
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: center;
    &-icon {
      font-size: 70px;
      color: #5c54f0;
    }
    &-text {
      margin-top: 30px;
      font-family: PingFangSC-Medium;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
      text-align: center;
      line-height: 32px;
      font-weight: 500;
    }
    &-desc {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      line-height: 22px;
      font-weight: 400;
      margin-top: 8px;
    }
  }
  &-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    .el-button {
      width: 80%;
      margin-bottom: 16px;
      height: 40px;
    }
  }
  .percentage-value {
    display: block;
    margin-top: 10px;
    font-weight: 500;
    font-size: 28px;
  }
  .percentage-label {
    display: block;
    margin-top: 10px;
    font-size: 15px;
    font-weight: 500;
  }
}
.setting-menu {
  width: 160px;
  &-item {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
}
</style>
