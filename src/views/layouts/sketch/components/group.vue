<template>
  <el-drawer class="sketch-drawer" :model-value="visible" size="100%" :with-header="false">
    <div class="sketch-drawer-header">
      <div class="sketch-drawer-header-back" @click="emit('close')">
        <el-icon><Back /></el-icon>
      </div>
      <el-input class="sketch-drawer-header-filter" v-model="filterText" placeholder="搜索分组">
        <template #prepend>
          <el-button type="text" :icon="Search" />
        </template>
        <template #append>
          <el-button @click="openAddLog()" type="text" :icon="Plus"></el-button>
        </template>
      </el-input>
    </div>
    <div v-loading="loading" class="sketch-drawer-body">
      <el-tree :auto-expand-parent="false" node-key="_id" highlight-current :expand-on-click-node="false" ref="treeRef" :current-node-key="id" class="filter-tree" :data="list" :props="defaultProps" @node-click="nodeClick" default-expand-all :filter-node-method="filterNode">
        <template #default="{ node, data }">
          <div class="sketch-tree-node">
            <div>
              <el-icon><FolderOpened /></el-icon><span>{{ node.label }}</span>
            </div>
            <el-button @click.stop="openAddLog(data)" type="text" :icon="FolderAdd"></el-button>
          </div>
        </template>
        <template #empty>
          <el-empty description="暂无项目" />
        </template>
      </el-tree>
    </div>
  </el-drawer>
  <el-dialog class="sketch-add" v-model="addSmbInfo.visible" title="新建分组" width="70%">
    <div v-if="addSmbInfo.parent" class="sketch-add-item">{{ getFullPath() }} /</div>
    <div class="sketch-add-item">
      <el-input placeholder="请输入分组名称" v-model="addSmbInfo.name"></el-input>
    </div>
    <template #footer>
      <span class="sketch-add-footer">
        <el-button @click="handleAddClose">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, defineProps, defineEmits } from "vue";
import { Search, Back, Plus, FolderOpened, FolderAdd } from "@element-plus/icons-vue";
import { addGroup, getGroupList } from "@/api/design";
import { ElTree } from "element-plus";
import { ObjectAny } from "@/types";
const treeRef = ref<InstanceType<typeof ElTree>>();
const props = defineProps<{
  visible: boolean;
  id: string;
  projectId: string;
}>();

const defaultProps = {
  label: "name",
  children: "children"
};
const emit = defineEmits(["change", "close"]);
const loading = ref(false);
const filterText = ref("");
const list = ref<ObjectAny[]>([]);
watch(filterText, (val) => {
  treeRef.value!.filter(val);
});
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      getGroups();
    } else {
      filterText.value = "";
    }
  }
);

watch(
  () => props.projectId,
  (projectId) => {
    if (projectId) {
      projectChage();
    }
  }
);

const projectChage = async () => {
  await getGroups();
  if (list.value.length) {
    nodeClick(list.value.find((item) => item.name === "未分组") || list.value[0]);
  }
  console.log(list.value);
};
const addSmbInfo = reactive<{ visible: boolean; name: string; parent: any }>({
  visible: false,
  name: "",
  parent: null
});
const getFullPath = () => {
  return addSmbInfo.parent?.fullPath.map(({ name }) => name).join(" / ");
};
const handleAddClose = () => {
  addSmbInfo.visible = false;
  addSmbInfo.parent = null;
  addSmbInfo.name = "";
};
const handleAddSubmit = async () => {
  if (!addSmbInfo.name) {
    return;
  }
  const res = await addGroup({
    projectId: props.projectId,
    parentId: addSmbInfo.parent?._id,
    name: addSmbInfo.name
  });
  if (res.code === 0) {
    getGroups();
    handleAddClose();
  }
};
const openAddLog = (parent?: any) => {
  addSmbInfo.visible = true;
  addSmbInfo.parent = parent;
};

const getGroups = async () => {
  loading.value = true;
  const res = await getGroupList({
    projectId: props.projectId
  });
  list.value = res.data;
  loading.value = false;
};
const nodeClick = (node) => {
  emit("change", node);
};
const filterNode = (value: string, data) => {
  if (!value) return true;
  return data.label.includes(value);
};
</script>
<style lang="less" scoped>
.sketch-drawer {
  &-header {
    height: 44px;
    display: flex;
    align-items: center;
    &-back {
      font-weight: 600;
      font-size: 24px;
      margin-right: 10px;
      display: inline-flex;

      align-items: center;
      cursor: pointer;
      transition: color 0.3s;
      &:hover {
        color: #5c54f0;
      }
    }
    &-filter {
      height: 38px;
      border-radius: 5px;
      flex: 1;
      ::v-deep {
        .el-input-group__prepend {
          background: transparent;
        }
      }
    }
  }
  &-body {
    height: calc(100% - 44px);
  }
  .el-tree {
    height: 100%;
    padding: 10px 0;
    ::v-deep {
      .el-tree-node__content {
        height: 40px;
      }
    }
  }
  .sketch-tree-node {
    display: flex;
    width: 100%;
    align-items: center;
    font-size: 15px;
    justify-content: space-between;
    height: 50px;
    box-sizing: border-box;
    padding: 5px;
    &:hover {
      .el-button {
        display: block;
      }
    }
    .el-button {
      font-size: 16px;
      display: none;
    }
    .el-icon {
      margin-right: 10px;
    }
  }
}
</style>
