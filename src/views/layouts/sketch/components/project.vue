<template>
  <el-drawer class="sketch-drawer" :model-value="visible" size="100%" :with-header="false">
    <div class="sketch-drawer-header">
      <div class="sketch-drawer-header-back" @click="emit('close')">
        <el-icon><Back /></el-icon>
      </div>
      <el-input class="sketch-drawer-header-filter" v-model="filterText" placeholder="搜索文件夹 / 项目">
        <template #prepend>
          <el-button type="text" :icon="Search" />
        </template>
        <template #append>
          <el-button @click="openAddLog" type="text" :icon="Plus"></el-button>
        </template>
      </el-input>
    </div>
    <el-tree v-loading="loading" class="group-tree" @node-click="handleClick" :expand-on-click-node="false" :data="list" node-key="_id" default-expand-all>
      <template #default="{ data }">
        <div v-if="data.type !== 'sketch'" class="group-tree-node">
          <div class="node-content" v-if="data.type == 'project'">
            <img class="node-icon" src="https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png" alt="" /><span class="node-name">{{ data.name }}</span>
          </div>
          <div class="node-content" v-else>
            <img class="node-icon" src="https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png" alt="" /><span class="node-name">{{ data.name }}</span>
          </div>
          <div v-if="data.type !== 'project'" class="node-right-bar">
            <div class="node-right-bar-handle">
              <el-button @click.stop type="text" @click="openAddLog(data)"> 新建项目 </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-tree>
    <!-- <ul v-loading="loading" class="sketch-drawer-body">
      <li
        v-for="item in activeList"
        :class="{
          active: id == item._id
        }"
        @click="handleClick(item)"
        :key="item._id"
      >
        <el-icon><Files /></el-icon> {{ item.name }}
      </li>
      <el-empty v-if="!activeList.length" description="暂无项目" />
    </ul> -->
  </el-drawer>
  <el-dialog class="sketch-add" v-model="addSmbInfo.visible" title="新建项目" width="70%">
    <div class="sketch-add-item">
      <el-input placeholder="请输入项目名称" v-model="addSmbInfo.name"></el-input>
    </div>
    <div class="sketch-add-item-btn"><el-checkbox v-model="autoCheck" style="margin-right: 10px" /> 创建后自动选择</div>
    <template #footer>
      <span class="sketch-add-footer">
        <el-button @click="handleAddClose">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, defineProps, defineEmits } from "vue";
import { Search, Back, Plus } from "@element-plus/icons-vue";
import { addProject, getTeamTreeList } from "@/api/design";
import { ObjectAny } from "@/types";

const props = defineProps<{
  visible: boolean;
  id: string;
  teamId: string;
}>();

const emit = defineEmits(["change", "close"]);
const loading = ref(false);
const filterText = ref("");
const list = ref<any[]>([]);
const autoCheck = ref(false);

watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      getProjects();
    } else {
      filterText.value = "";
    }
  }
);

const addSmbInfo = reactive<ObjectAny>({
  visible: false,
  name: ""
});
const handleAddClose = () => {
  addSmbInfo.visible = false;
  addSmbInfo.folderId = null;
  addSmbInfo.name = "";
};
const handleAddSubmit = async () => {
  if (!addSmbInfo.name) {
    return;
  }
  const res = await addProject({
    teamId: props.teamId,
    name: addSmbInfo.name,
    folderId: addSmbInfo.folderId || null
  });
  if (res.code === 0) {
    handleAddClose();
    if (autoCheck.value) {
      emit("change", res.data);
      return;
    }
    getProjects();
  }
};
const openAddLog = (data: ObjectAny) => {
  addSmbInfo.visible = true;
  addSmbInfo.folderId = data._id;
};

const getProjects = async () => {
  loading.value = true;
  const res = await getTeamTreeList({
    teamId: props.teamId
  });
  list.value = res.data;
  loading.value = false;
};

const handleClick = (node) => {
  if (node.type !== "project") {
    return;
  }
  emit("change", node);
};
</script>
<style lang="less" scoped>
.sketch-drawer {
  &-header {
    height: 44px;
    display: flex;
    align-items: center;
    &-back {
      font-weight: 600;
      font-size: 24px;
      margin-right: 10px;
      display: inline-flex;

      align-items: center;
      cursor: pointer;
      transition: color 0.3s;
      &:hover {
        color: #5c54f0;
      }
    }
    &-filter {
      height: 38px;
      border-radius: 5px;
      flex: 1;
      ::v-deep {
        .el-input-group__prepend {
          background: transparent;
        }
      }
    }
  }
  &-body {
    height: calc(100% - 44px);
    padding: 15px 5px;
    box-sizing: border-box;
  }
  .el-tree {
    height: 100%;
    padding: 10px 0;
    ::v-deep {
      .el-tree-node__content {
        height: 40px;
      }
    }
  }
  .sketch-tree-node {
    display: flex;
    width: 100%;
    align-items: center;
    font-size: 15px;
    justify-content: space-between;
    height: 50px;
    box-sizing: border-box;
    padding: 5px;
    &:hover {
      .el-button {
        display: block;
      }
    }
    .el-button {
      font-size: 16px;
      display: none;
    }
    .el-icon {
      margin-right: 10px;
    }
  }
}
.sketch-add {
  .sketch-add-item-btn {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }
}
.group-tree {
  overflow: overlay;
  // padding: 0 12px;
  ::v-deep {
    .el-tree-node__content {
      height: 35px;
      box-sizing: border-box;
    }
  }
  .group-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 30px);
    height: 100%;
    .iconfont {
      font-size: 12px;
      margin-right: 5px;
    }
    &:hover {
      .node-right-bar-handle {
        // display: block;
        visibility: visible;
        position: relative;
      }
    }
    .node-root {
      font-size: 16px;
      color: #303233;
      font-family: PingFangSC-Medium;
      font-weight: 500;
    }
    .node-content {
      display: flex;
      // width: calc(100% - 60px);
      align-items: center;
      .node-icon {
        display: block;
        width: 15px;
        height: 15px;
        align-self: center;
      }
      .node-name {
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #303233;
        font-weight: 400;
        display: block;
        // width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .node-label {
      flex: 0 0 160px;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .node-right-bar {
      display: flex;
      align-items: center;
      .node-count {
        font-size: 12px;
        color: #8e8e8e;
      }
      &-handle {
        // display: none;
        visibility: hidden;
        margin-left: 10px;
      }
    }
  }
}
</style>
