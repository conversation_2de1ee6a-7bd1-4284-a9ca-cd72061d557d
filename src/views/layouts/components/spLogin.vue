<template>
  <div
    :class="{
      'home__user--login': true,
      'home-user__active': store.themeShow
    }"
  >
    <div class="home-user__body">
      <div class="home-user-body__item" @click="themeChange">
        <img loading="lazy" :src="store.themeShow ? 'https://static.soyoung.com/sy-pre/q66gwg2hjhf4-1697533800712.png' : 'https://static.soyoung.com/sy-pre/2zls11axnhtgk-1697530200725.png'" alt="" />
        <span>{{ store.themeShow ? "浅色模式" : "深色模式" }}</span>
      </div>
      <div class="home-user-body__item" @click="handleUpload">
        <i class="iconfont icon-wodegongzuotai" />
        <span>工作台</span>
      </div>
    </div>
    <div class="home__userInfo" v-if="userInfo.ssoId">
      <el-dropdown :popper-class="'home-user__dropdown'" :hide-on-click="false">
        <div>
          <img loading="lazy" class="home__avatar" src="https://static.soyoung.com/sy-pre/<EMAIL>" alt="" />
          <span class="home__username">{{ userInfo.name }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleCollection">
              <i class="iconfont icon-shoucang1"></i>
              <span>我的收藏</span>
            </el-dropdown-item>
            <el-dropdown-item @click="handleLogout">
              <i class="iconfont icon-tuichudenglu"></i>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="home__user--unlogin" v-else>
      <!-- <el-button @click="ssoLogin" type="primary" size="large">新氧登录</el-button> -->
    </div>
  </div>
</template>

<script lang="ts">
import { setUserLogout } from "@/api/login";
import { themeStore, userInfoStore } from "@/store";
import { ElButton, ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage } from "element-plus";
import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";
export default {
  setup() {
    const router = useRouter();
    const userInfo = userInfoStore();
    // 深浅模式切换store
    const store = themeStore();

    console.log("store", store.themeShow);
    // 退出登录
    const handleLogout = async () => {
      await setUserLogout();
      userInfo.clearInfo();
      ElMessage({
        type: "success",
        message: "退出成功"
      });
    };
    // 切换主题
    const themeChange = () => {
      // 使用本地存储来持久化主题设置
      localStorage.setItem("theme", store.themeShow ? "light" : "dark");
      store.updateThemeValue(!store.themeShow);
    };

    const handleUpload = () => {
      // if (!userInfo.syUid) {
      //   ssoLogin();
      //   return;
      // }
      router.push({
        path: "/home/<USER>"
      });
    };

    const handleCollection = () => {
      router.push({
        path: "/home/<USER>"
      });
    };
    // 登陆
    // const ssoLogin = async () => {
    //   window.location.href = "/api/user/login?return_url=" + encodeURIComponent(window.location.href);
    // };

    onMounted(() => {
      const element = document.getElementsByTagName("html")[0];
      if (localStorage.getItem("theme") && localStorage.getItem("theme") == "light") {
        store.updateThemeValue(false);
      } else {
        store.updateThemeValue(true);
      }
      element.setAttribute("class", store.themeShow ? "dark" : "light");
    });

    return {
      handleLogout,
      handleUpload,
      store,
      // ssoLogin,
      handleCollection,
      userInfo: computed(() => userInfo),
      updateLoginStatus: (payload) => userInfo.updateInfo(payload),
      themeChange
    };
  },
  watch: {
    // 监听路由是否变化
    $route(to, from) {
      if (to.query.id != from.query.id) {
        this.$router.go(0);
      }
    }
  },
  components: {
    ElButton,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem
  }
};
</script>

<style lang="less">
.home__user--login {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  .home-user__body {
    display: flex;
    flex-direction: row;
    .home-user-body__item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 24px;
      cursor: pointer;
      img {
        width: 18px;
        height: 18px;
      }
      span {
        font-size: 14px;
        color: #303233;
        margin-left: 5px;
      }
    }
  }
  .home__userInfo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 50px;
    padding: 22px 0;
  }
  .home__avatar {
    width: 20px;
    height: 20px;
    box-sizing: border-box;
    border: 1px solid #005dfc;
    border-radius: 10px 10px 1px 10px;
    margin-right: 10px;
    cursor: pointer;
    vertical-align: middle;
  }
  .home__username {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #303233;
    letter-spacing: 0;
    font-weight: 400;
    line-height: 20px;
    vertical-align: middle;
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &.home-user__active {
    .home__username {
      color: #ffffff;
    }
  }
}
.home__user--unlogin {
  margin-right: 50px;
  padding: 11px 0;
}
.home-user__dropdown {
  .el-dropdown-menu {
    width: 144px;
    display: flex;
    flex-direction: column;
    .el-dropdown-menu__item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 16px;
      i {
        margin-right: 10px;
      }
    }
  }
}

/*主题激活样式*/
.home-user__active {
  .home-user__body {
    .home-user-body__item {
      span {
        color: #ffffff;
      }
      i {
        color: #ffffff;
      }
    }
  }
}
</style>
