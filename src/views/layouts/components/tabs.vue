<template>
  <div
    :class="{
      'home-body__content': true,
      'home-body-theme__active': store.themeShow
    }"
  >
    <div
      :class="{
        'home-body-tabs__item': true,
        'home-body-tabs-item__span': item.id === currentId
      }"
      v-for="item in tabsList"
      :key="item.id"
      @click="tabsChange(item)"
    >
      <span class="active-name">{{ item.name }}</span>
      <img v-if="item.iconImg" src="https://static.soyoung.com/sy-pre/<EMAIL>" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { themeStore, userInfoStore } from "@/store";
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
const store = themeStore();
const route = useRoute();
const router = useRouter();
const userInfo = userInfoStore();
// tabs 数据集
const tabsList = ref<any[]>([
  { id: 1, name: "灵感集", hasIcon: false, iconImg: "" },
  { id: 2, name: "设计协作", hasIcon: true, iconImg: "https://static.soyoung.com/sy-pre/<EMAIL>" }
]);
const barLeft = ref<number>(0); // 初始偏移量
const currentId = ref<number>(route.fullPath.indexOf("/item/project/index") > -1 ? 2 : 1); // 默认渲染样式
// 登陆
// const ssoLogin = async () => {
//   window.location.href = "/api/user/login?return_url=" + encodeURIComponent(window.location.href);
// };
const tabsChange = (item: any) => {
  if (item.id === 2) {
    // if (!userInfo.syUid) {
    //   ssoLogin();
    //   return;
    // }
    const route = router.resolve({ path: "/item/project/index" });
    window.open(route.href, "_blank");
  } else {
    barLeft.value = item.left;
    currentId.value = item.id;
  }
};

watch(
  () => route.fullPath,
  (newVal, oldVal) => {
    console.log("Route changed from", oldVal, "to", newVal);
  }
);
</script>
<style lang="less" scoped>
.home-body__content {
  box-sizing: border-box;
  position: relative;
  padding-left: 10px;
  .home-body-tabs__item {
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 16px;
    color: #595959;
    font-weight: 500;
    cursor: pointer;
    z-index: 1;
    display: inline-block;
    position: relative;
    padding: 0 20px;
    &.home-body-tabs-item__span {
      color: #262626;
      &::after {
        content: "";
        position: absolute;
        bottom: -6px;
        left: 50%;
        width: 24px;
        height: 3px;
        transform: translateX(-50%);
        background: #5c54f0;
        border-radius: 2px;
      }
    }
    img {
      width: 24px;
      height: 12px;
      position: absolute;
    }
  }
  &.home-body-theme__active {
    .home-body-tabs__item {
      color: #ffffff;
      .active-name {
        opacity: 0.5;
      }
      &.home-body-tabs-item__span {
        color: #ffffff;
        .active-name {
          opacity: 1;
        }
      }
    }
  }
}
</style>
