<template>
  <div
    :class="{
      sp__nav: true,
      'sp-nav-theme__active': store.themeShow
    }"
  >
    <div class="sp-nav__content">
      <div
        v-for="item in data"
        :key="item._id"
        :class="{
          'sp-nav-content__item': true,
          'sp-nav-content-item__active': item._id === currentId
        }"
        @click="navChange(item)"
      >
        <img loading="lazy" v-if="item.icon === '' || item.icon === undefined" :src="store.themeShow || item._id === currentId ? 'https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png' : 'https://static.soyoung.com/sy-pre/ty5osyh2u1oh-1697609400738.png'" alt="" />
        <img loading="lazy" v-else :src="item.icon" alt="" />
        <span class="sp-nav-content-item__span">{{ item.name }}</span>

        <el-popover v-if="item.name !== '收藏夹' && identify === 1 && item._id === currentId" placement="bottom" :width="103" :popper-class="'popover-operation__style'" trigger="hover" :effect="store.themeShow ? 'light' : 'dark'">
          <template #reference>
            <!-- 操作按钮 start -->
            <div class="operation-span__container">
              <img loading="lazy" class="operation__btn" src="https://static.soyoung.com/sy-pre/1spmepi29mrwu-1698304200710.png" alt="" />
            </div>
            <!-- 操作按钮 end-->
          </template>
          <div class="operation-body">
            <div class="operation-body__item" @click="handleRename(item)">
              <span>重命名</span>
            </div>
            <div class="operation-body__item" @click="handleDelete(item)">
              <span>删除</span>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { themeStore } from "@/store";
import { PropType, ref, watch } from "vue";
let emits = defineEmits(["tabsChange", "handleRename", "handleDelete"]);
const store = themeStore();

type RouterListData = {
  _id: string;
  name: string;
  icon?: string;
};
let props = defineProps({
  data: {
    type: Array as PropType<RouterListData[]>,
    default: () => []
  },
  defaultId: {
    type: String,
    default: ""
  },
  identify: {
    // 用来确定是否有右侧操作按钮 0: 没有  1: 有
    type: Number,
    default: 0
  }
});

const currentId = ref<string>("");
const navChange = (item: any) => {
  currentId.value = item._id;
  emits("tabsChange", item);
};

// 重命名
const handleRename = (item: any) => {
  emits("handleRename", item);
};

// 删除
const handleDelete = (item: any) => {
  emits("handleDelete", item);
};

watch(
  () => props.defaultId,
  (n: string) => {
    currentId.value = n;
  },
  { immediate: true }
);

watch(
  () => props.data,
  (n: { _id: string; name: string }[]) => {
    if (n.length === 0 || n.length === 1) {
      emits("tabsChange", { _id: "", name: "" });
    }
  },
  { immediate: true, deep: true }
);
</script>
<style lang="less">
.sp__nav {
  width: 250px;
  height: calc(100vh - 160px);
  background: #ffffff;
  padding: 0 20px;
  box-sizing: border-box;
  position: relative;
  &::before {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background-image: linear-gradient(to bottom, #ededed 50%, #ededed 50%);
  }
  .sp-nav__content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    scroll-snap-type: x proximity;
    &::-webkit-scrollbar {
      display: none;
    }
    .sp-nav-content__item {
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: left;
      font-size: 14px;
      color: #303233;
      padding: 0 11px;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      .sp-nav-content-item__span {
        flex: 1;
      }
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        border-radius: 50%;
        &.operation__btn {
          width: 3px;
        }
      }
      &.sp-nav-content-item__active {
        border-radius: 6px;
        background-color: #5c54f0;
        color: #ffffff;
      }
    }
  }
  &.sp-nav-theme__active {
    background-color: #26282b;
    &::before {
      background-image: linear-gradient(to bottom, #000000 50%, #000000 50%);
    }
    .sp-nav__content {
      .sp-nav-content__item {
        color: #ffffff;
      }
    }
  }
  .operation-span__container {
    width: 20px;
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: flex-end;
  }
}
.popover-operation__style {
  min-width: 103px !important;
}
.operation-body {
  width: 100%;
  display: flex;
  flex-direction: column;
  .operation-body__item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 15px;
    cursor: pointer;
    &:hover {
      color: #5c54f0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
