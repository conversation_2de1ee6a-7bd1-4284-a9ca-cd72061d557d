<template>
  <div
    :class="{
      'sp-screen-tabs': true,
      'sp-screen-tabs-theme__active': store.themeShow
    }"
    id="tabsContainer"
    :style="{ justifyContent: !isToggle ? 'center' : 'flex-start' }"
    @scroll="scroll"
  >
    <div
      v-for="item in data"
      :key="item._id"
      :class="{
        'sp-screen-tabs__item': true,
        'sp-screen-tabs-item__active': selectedId.includes(item._id)
      }"
      @click="screenChange(item)"
    >
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { materialStore, themeStore } from "@/store";
import { defineEmits, ref, watch } from "vue";
const store = themeStore();
const material = materialStore();

let emits = defineEmits(["updateScrollWidth"]);

let props = defineProps({
  isToggle: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array as any,
    default: () => []
  },
  materialType: {
    type: String,
    default: "version"
  }
});

const selectedId = ref<string[]>([]); // 已选ID集合

const screenChange = (item: any) => {
  if (selectedId.value.includes(item._id)) {
    selectedId.value = selectedId.value.filter((e: string) => e !== item._id);
  } else {
    selectedId.value.push(item._id);
  }
  console.log(selectedId.value, "valueIDS");
  if (props.materialType === "version") {
    material.updateVersionInfo(selectedId.value);
  } else {
    material.updateTagsInfo(selectedId.value);
  }
};

const scroll = (event: any) => {
  emits("updateScrollWidth", event.target.scrollLeft);
};

watch(
  () => material.ids,
  (n: string[]) => {
    selectedId.value = n;
  }
);
</script>
<style lang="less" scoped>
.sp-screen-tabs {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  overflow-y: hidden;
  overflow-x: auto;
  scroll-behavior: smooth;
  scroll-snap-type: x proximity;
  scrollbar-width: none;
  &.sp-screen-tabs-theme__active {
    .sp-screen-tabs__item {
      background: #262626;
      color: #ffffff;
    }
  }
  &::-webkit-scrollbar {
    display: none;
  }
  .sp-screen-tabs__item {
    width: 85px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    color: #303233;
    background: #f0f0f0;
    border-radius: 20px;
    margin-right: 10px;
    cursor: pointer;
    flex-shrink: 0;
    &.sp-screen-tabs-item__active {
      background: #5c54f0;
      color: #ffffff;
    }
  }
}
</style>
