<template>
  <teleport to="body">
    <transition name="viewer-fade" appear>
      <div ref="wrapper" v-if="showViewer" class="image-view-wrapper">
        <div class="image-view-mask" />
        <div class="image-view-content">
          <!-- BAR -->
          <div class="image-view-bar">
            <div class="image-view-title"></div>
            <div class="image-view-btn">
              <el-icon @click="handleActions('zoomOut')">
                <ZoomOut />
              </el-icon>
              <el-icon @click="handleActions('zoomIn')">
                <ZoomIn />
              </el-icon>
              <el-icon @click="handleActions('anticlockwise')">
                <RefreshLeft />
              </el-icon>
              <el-icon @click="handleActions('clockwise')">
                <RefreshRight />
              </el-icon>
            </div>
          </div>
          <!-- CANVAS -->
          <div class="image-view-canvas" @click="handleClose">
            <img loading="lazy" :key="material.url" @mousedown="handleMouseDown" :src="material.url && material.url.indexOf('?') < 0 ? `${material.url}?imageView2/0/format/webp` : material.url" :style="imgStyle" class="image-view-img" />
          </div>
        </div>
        <div class="image-view-actions">
          <div class="image-view-actions-content">
            <div class="image-view-actions-content__title">
              <span>基础信息</span>
            </div>
            <div class="image-view-actions-content__item">
              <label>文件大小</label>
              <span>{{ (material?.size / 1000).toFixed(2) }}mb</span>
            </div>
            <div class="image-view-actions-content__item">
              <label>尺寸</label>
              <span>{{ material.width }} * {{ material.height }}</span>
            </div>
            <div class="image-view-actions-content__item">
              <label>文件格式</label>
              <span>{{ material?.format }}</span>
            </div>
            <div class="image-view-actions-content__item">
              <label>颜色类型</label>
              <span>{{ material?.colorModel }}</span>
            </div>
            <div class="image-view-actions-content__item">
              <label>添加日期</label>
              <span>{{ new Date(parseInt(material?.createTime)).toLocaleString().replace(/:\d{1,2}$/, " ") }}</span>
            </div>
            <div class="image-view-actions-content__item origin__path">
              <label>来源路径</label>
              <div class="origin-path__content">
                <div class="origin-path__item">
                  <div class="tips">
                    <img loading="lazy" :src="material?.app.icon" alt="" />
                    <span>{{ material?.app?.name }} V{{ material.lver?.name }}</span>
                  </div>
                </div>
                <div class="origin-path__item">
                  <div class="tips">
                    <img loading="lazy" src="https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png" alt="" />
                    <span>{{ material?.module.name }}</span>
                  </div>
                </div>
              </div>
              <div class="image-view-actions-content__btns">
                <el-button class="el-button-code" type="primary" @click="handleClickAICode">AI原型Code</el-button>
                <el-button type="primary" @click="handleClickAI">AI灵感升级</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script lang="ts" setup>
import { aiGenerated } from "@/api/upload";
import { RefreshLeft, RefreshRight, ZoomIn, ZoomOut } from "@element-plus/icons-vue";
import { useEventListener } from "@vueuse/core";
import { ElLoading, ElMessageBox } from "element-plus";
import { throttle } from "lodash-unified";
import type { CSSProperties } from "vue";
import { defineEmits, defineProps, effectScope, onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
type ImageViewerAction = "zoomIn" | "zoomOut" | "clockwise" | "anticlockwise";
const EVENT_CODE = {
  esc: "Escape",
  space: "Space",
  left: "ArrowLeft",
  up: "ArrowUp",
  right: "ArrowRight",
  down: "ArrowDown"
};
const scopeEventListener = effectScope();
const props = withDefaults(
  defineProps<{
    material: Record<string, any>;
    showViewer: boolean;
    minScale?: number;
    maxScale?: number;
    zoomRate?: number;
    closeOnPressEscape: boolean;
  }>(),
  {
    minScale: 0.2,
    maxScale: 6,
    zoomRate: 1.2,
    closeOnPressEscape: false
  }
);
const emits = defineEmits({
  close: () => true
});
const router = useRouter();
onMounted(() => {
  registerEventListener();
});
function registerEventListener() {
  const keydownHandler = throttle((e: KeyboardEvent) => {
    switch (e.code) {
      // ESC
      case EVENT_CODE.esc:
        props.closeOnPressEscape && handleClose();
        break;
      // SPACE
      case EVENT_CODE.space:
        break;
      // UP_ARROW
      case EVENT_CODE.up:
        handleActions("zoomIn");
        break;
      // DOWN_ARROW
      case EVENT_CODE.down:
        handleActions("zoomOut");
        break;
    }
  });
  const mousewheelHandler = throttle((e: WheelEvent) => {
    const delta = e.deltaY || e.deltaX;
    handleActions(delta < 0 ? "zoomIn" : "zoomOut", {
      zoomRate: props.zoomRate,
      enableTransition: false
    });
  });

  scopeEventListener.run(() => {
    useEventListener(document, "keydown", keydownHandler);
    useEventListener(document, "wheel", mousewheelHandler);
  });
}

const transform = ref({
  scale: 1,
  deg: 0,
  offsetX: 0,
  offsetY: 0,
  enableTransition: false
});

const imgStyle = computed(() => {
  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value;
  let translateX = offsetX / scale;
  let translateY = offsetY / scale;

  switch (deg % 360) {
    case 90:
    case -270:
      [translateX, translateY] = [translateY, -translateX];
      break;
    case 180:
    case -180:
      [translateX, translateY] = [-translateX, -translateY];
      break;
    case 270:
    case -90:
      [translateX, translateY] = [-translateY, translateX];
      break;
  }

  const style: CSSProperties = {
    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,
    transition: enableTransition ? "transform .3s" : ""
  };
  style.maxWidth = style.maxHeight = "95%";
  return style;
});

function handleActions(action: ImageViewerAction, options = {}) {
  const { minScale, maxScale, showViewer } = props;
  const { zoomRate, rotateDeg, enableTransition } = {
    zoomRate: props.zoomRate,
    rotateDeg: 90,
    enableTransition: true,
    ...options
  };
  if (!showViewer) {
    return;
  }
  switch (action) {
    case "zoomOut":
      if (transform.value.scale > minScale) {
        transform.value.scale = Number.parseFloat((transform.value.scale / zoomRate).toFixed(3));
      }
      break;
    case "zoomIn":
      if (transform.value.scale < maxScale) {
        transform.value.scale = Number.parseFloat((transform.value.scale * zoomRate).toFixed(3));
      }
      break;
    case "clockwise":
      transform.value.deg += rotateDeg;
      break;
    case "anticlockwise":
      transform.value.deg -= rotateDeg;
      break;
  }
  transform.value.enableTransition = enableTransition;
}
function handleMouseDown(e: MouseEvent) {
  transform.value.enableTransition = false;

  const { offsetX, offsetY } = transform.value;
  const startX = e.pageX;
  const startY = e.pageY;

  const dragHandler = throttle((ev: MouseEvent) => {
    transform.value = {
      ...transform.value,
      offsetX: offsetX + ev.pageX - startX,
      offsetY: offsetY + ev.pageY - startY
    };
  });
  const removeMousemove = useEventListener(document, "mousemove", dragHandler);
  useEventListener(document, "mouseup", () => {
    removeMousemove();
  });

  e.preventDefault();
}
const handleClickAI = async () => {
  ElMessageBox.confirm("确认以此图喂给AI生成新图?时间有点长耐心等待啊", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      const res = await aiGenerated({
        name: props.material?.module.name || "首页"
      });

      if (res?.data?.data) {
        loading.close();
        window.open(res?.data?.data);
      }
    })
    .catch(() => {
      // window.open(res?.data?.data);
    });
};
const handleClickAICode = () => {
  const { href } = router.resolve({
    path: "/screenCode",
    query: {
      id: props.material._id
    }
  });

  window.open(href, "_blank");
};

const handleClose = () => {
  transform.value = {
    scale: 1,
    deg: 0,
    offsetX: 0,
    offsetY: 0,
    enableTransition: false
  };
  emits("close");
};
</script>
<style lang="less" scoped>
.image-view-wrapper {
  position: fixed;
  top: 64px;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  z-index: 999;
  .image-view-mask {
    width: calc(100% - 300px);
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.75);
  }
  .image-view-content {
    flex: 0 0 calc(100% - 300px);
    height: 100%;
    position: relative;
  }
  .image-view-bar {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .image-view-btn {
    display: inline-flex;
    .el-icon {
      font-size: 18px;
      color: #fff;
      margin: 0 10px;
      cursor: pointer;
    }
  }
  .image-view-canvas {
    height: calc(100% - 124px);
    overflow: overlay;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .image-view-actions {
    background: #000;
    height: 100%;
    width: 300px;
    &-content {
      width: 100%;
      padding: 30px;
      display: flex;
      flex-direction: column;
      &__title {
        width: 100%;
        height: 37px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
        span {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 500;
        }
      }
      &__item {
        width: 100%;
        margin-bottom: 15px;

        label,
        span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 400;
        }

        label {
          width: 101px;
          text-align: left;
          display: inline-block;
        }

        &.origin__path {
          display: flex;
          flex-direction: column;
          margin-top: 25px;
          .origin-path__content {
            width: 100%;
            flex-direction: column;
            margin-top: 16px;

            .origin-path__item {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              .tips {
                padding: 5px 10px;
                margin-bottom: 15px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 14px;
                font-size: 14px;
                display: inline-block;
              }
              img {
                width: 18px;
                height: 18px;
                border-radius: 4.5px;
                margin-right: 10px;
                vertical-align: text-top;
              }
            }
          }
        }
      }
      &__btns {
        display: flex;
        flex-direction: column;
        .el-button {
          width: max-content;
          margin: 5px 0;
        }
        .el-button-code {
          background-color: transparent;
          border: none;
          opacity: 0.8;
          background-image: linear-gradient(90deg, #978de5 4%, #8664d2 51%, #713eff 96%);
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
