<template>
  <header
    :class="{
      home__header: true,
      'home-header__active': store.themeShow
    }"
    :style="{ background: store.themeShow ? '#26282B' : '#FFFFFF' }"
  >
    <div class="header__left">
      <slot name="left-slot">
        <div class="home__logo" @click="handleClickLogo">
          <img loading="lazy" class="home__logo-img" :src="store.themeShow ? 'https://static.soyoung.com/sy-pre/pft7c5v6tl9w-1709797007858.png' : 'https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png'" alt="新氧画廊LOGO" />
        </div>
      </slot>
    </div>
    <div class="header__center">
      <slot name="center-slot">
        <SpTabs></SpTabs>
      </slot>
    </div>
    <div class="header__right">
      <slot></slot>
    </div>
  </header>
  <div class="home__line" :style="{ background: store.themeShow ? '#000000' : '#F5F5F5' }"></div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";
import { themeStore } from "@/store";
import SpTabs from "@/views/layouts/components/tabs.vue";
const store = themeStore();

const router = useRouter();
const handleClickLogo = () => {
  router.push({
    path: "/"
  });
};
</script>

<style lang="less" scoped>
.home__header {
  height: 64px;
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  .header__left {
    display: flex;
    justify-content: center;
    align-items: center;
    .home__logo {
      display: inline-block;
      padding-left: 25px;
      margin-top: 4px;
      cursor: pointer;
      width: 250px;
      box-sizing: border-box;
      .home__logo-img {
        width: 84px;
        display: inline-block;
      }
    }
    .header__title {
      display: inline-block;
      .header__title__title {
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #606266;
        position: relative;
        line-height: 22px;
        margin-left: 30px;
        &:before {
          content: "";
          width: 1px;
          height: 16px;
          position: absolute;
          left: -15px;
          top: 4px;
          background: #d8d8d8;
        }
      }
    }
  }
  .header__center {
    flex: 1;
  }

  &.home-header__active {
    .header__left {
      .home__logo {
        color: #ffffff;
      }
    }
    .header__right {
      .home__user--login .home__username {
        color: #ffffff;
      }
    }
  }
}
.home__line {
  width: 100%;
  height: 1px;
  background: #f5f5f5;
}
</style>
