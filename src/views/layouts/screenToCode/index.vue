<template>
  <div
    :class="{
      home: true,
      'home-theme': store.themeShow
    }"
  >
    <SpHeader>
      <SpLogin></SpLogin>
    </SpHeader>
    <div class="screen-body">
      <div class="screen-body__left">
        <CodePreview v-if="appState === 'CODING'" :code="generatedCode" />
        <div
          :class="{
            scan: appState === 'CODING',
            'screen-body__left-image': true
          }"
        >
          <img loading="lazy" style="width: 100%" :src="material.url" alt="Reference" />
        </div>
        <el-button :disabled="loading" @click="doCreate">开始生成</el-button>
      </div>
      <div class="screen-body__content">
        <div
          :class="{
            'preview-desktop': deviceType === 'desktop',
            'preview-mobile': deviceType === 'mobile'
          }"
        >
          <Preview v-if="appState === 'CODING' || appState === 'CODE_READY'" :appState="appState" :code="generatedCode" :device="deviceType" />
        </div>
      </div>
      <div v-if="appState === 'CODE_READY'" class="screen-body__right">
        <CodeMirror :code="generatedCode" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { themeStore } from "@/store";
import { useRoute } from "vue-router";
import { ErrorCode } from "@/model";
import SpHeader from "@/views/layouts/components/spHeader.vue";
import SpLogin from "@/views/layouts/components/spLogin.vue";
import Preview from "./components/preview.vue";
import CodePreview from "./components/codePreview.vue";
import CodeMirror from "./components/codeMirror.vue";
import { materialDetail } from "@/api/common";
import { generateCode } from ".";
interface Settings {
  isImageGenerationEnabled: boolean;
  editorTheme: string;
}
const store = themeStore(); // 黑白主题切换Store
const appState = ref<"INITIAL" | "CODING" | "CODE_READY">("INITIAL");
const generatedCode = ref<string>("");
const referenceImage = ref<string>("");
const executionConsole = ref<string[]>([]);
const history = ref<string[]>([]);
const updateInstruction = ref<string>("");
const loading = ref<boolean>(false);
const deviceType = ref<"desktop" | "mobile">("desktop");
const material = ref<Record<string, any>>({});
const route = useRoute();
const settings = ref<Settings>({
  isImageGenerationEnabled: true,
  editorTheme: "cobalt"
});
function detectDeviceType(width: number, height: number) {
  const aspectRatio = width / height;

  // 定义移动端和 PC 端的宽高比范围
  const mobileAspectRatioThreshold = 1.5; // 举例：移动端宽高比的阈值
  const pcAspectRatioThreshold = 1.7; // 举例：PC 端宽高比的阈值
  // 判断宽高比来确定设备类型
  if (aspectRatio <= mobileAspectRatioThreshold) {
    deviceType.value = "mobile";
  } else {
    deviceType.value = "desktop";
  }
}

const findMaterial = async (id) => {
  try {
    loading.value = true;
    const { code, data, message } = await materialDetail({ id });
    if (code !== ErrorCode.OK) {
      throw new Error(message);
    }
    material.value = data;
    detectDeviceType(data.width, data.height);
    referenceImage.value = await loadImage();
  } catch (error) {
    console.log(error);
  }
  loading.value = false;
};
const doCreate = () => {
  doGenerateCode({
    generationType: "create",
    image: referenceImage.value
  });
};
const doGenerateCode = (params) => {
  executionConsole.value = [];
  appState.value = "CODING";
  const updatedParams = { ...params, ...settings.value };
  generateCode(
    updatedParams,
    (token) => {
      generatedCode.value += token;
    },
    (code) => {
      generatedCode.value = code;
    },
    (line) => {
      executionConsole.value = [...executionConsole.value, line];
    },
    () => {
      appState.value = "CODE_READY";
    }
  );
};

const reset = () => {
  appState.value = "INITIAL";
  generatedCode.value = "";
  referenceImage.value = "";
  executionConsole.value = [];
  history.value = [];
};

const loadImage = (): Promise<string> => {
  return new Promise((next, error) => {
    const image = new Image();
    image.onload = function () {
      next(getBase64Image(image));
    };
    image.crossOrigin = "*";
    image.onerror = error;
    image.src = material.value.url;
  });
};

function getBase64Image(img) {
  let canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  let ctx = canvas.getContext("2d") as any;
  ctx.drawImage(img, 0, 0);
  let dataURL = canvas.toDataURL("image/png");
  return dataURL.replace(/^data:image\/?[A-z]*;base64,/, "");
}
watch(
  () => route.query,
  (query) => {
    console.log(query);
    if (query.id) {
      findMaterial(query.id);
    }
  },
  {
    immediate: true
  }
);
</script>
<style lang="less" scoped>
.screen-body {
  height: calc(100vh - 64px);
  display: flex;
  justify-content: space-between;
  &__left {
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    border-right: 1px solid #ccc;
    background: #fff;
    max-width: 300px;
    overflow: auto;
    &-image {
      border: 1px solid transparent;
      padding: 5px;
    }
  }
  &__content {
    height: 100%;
    display: flex;
    padding: 20px;
    flex: 1;
    min-width: 400px;
    justify-content: center;
  }
  &__right {
    width: 400px;
    padding: 5px;
    box-sizing: border-box;
    overflow: hidden;
  }
}
.preview {
  &-desktop {
    width: 100%;
    height: 100%;
    border: 1px solid #f0f0f0;
    border-radius: 5px;
  }
  &-mobile {
    width: 420px;
    height: 820px;
    box-sizing: border-box;
    padding: 105px 20px;
    z-index: 0;
    background: url(https://static.soyoung.com/sy-pre/780-1701159000690.jpeg) 0 0 no-repeat;
    background-size: 100% 100%;
  }
}

@media screen and (max-width: 768px) {
  .screen-body__left {
    display: none;
  }
}
.scan {
  background: url(https://static.soyoung.com/sy-pre/2-1701310200687.png) center center no-repeat;
  border: 1px solid #b0f9e4;
  background-size: 100% 100%;
  position: relative;
  &::after {
    content: "";
    background: url(https://static.soyoung.com/sy-pre/4-1701310200687.png) center center no-repeat;
    position: absolute;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    overflow: hidden;
    animation: move 1.5s ease-in-out infinite;
    top: 0;
    left: 0;
  }
}
@keyframes move {
  from {
    top: -100%;
  }
  to {
    top: 0;
  }
}
</style>
