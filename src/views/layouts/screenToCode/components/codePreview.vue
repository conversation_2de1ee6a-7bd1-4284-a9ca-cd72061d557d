<template>
  <div ref="scrollRef" class="code-review">{{ code }}</div>
</template>
<script lang="ts" setup>
import { defineProps, watch } from "vue";
import { throttle } from "lodash";
interface Props {
  code: string;
}
const props = defineProps<Props>();
const scrollRef = ref();

watch(
  () => props.code,
  (code) => {
    if (scrollRef.value) {
      scrollRef.value.scrollTop = scrollRef.value.scrollHeight;
    }
  }
);
</script>
<style lang="less" scoped>
.code-review {
  width: 100%;
  overflow-x: hidden;
  background: #000;
  color: #5fff5f;
  font-size: 12px;
  padding: 5px;
  max-height: 200px;
  overflow-y: overlay;
  position: relative;
  z-index: 2;
}
</style>
