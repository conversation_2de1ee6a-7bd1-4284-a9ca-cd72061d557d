<template>
  <Codemirror v-model:value="generateCode" :options="cmOptions" border ref="cmRef" @change="onChange" @ready="onReady"> </Codemirror>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, defineProps } from "vue";
import Codemirror from "codemirror-editor-vue3";
import "codemirror/theme/monokai.css";
// language
import "codemirror/mode/htmlmixed/htmlmixed.js";
interface Props {
  code: string;
}
const props = defineProps<Props>();
const generateCode = ref<string>("");
const cmRef = ref();
const cmOptions = reactive({
  mode: "text/html",
  theme: "monokai" // Theme
});
watch(
  () => props.code,
  (code) => {
    generateCode.value = code;
  },
  {
    immediate: true
  }
);
onMounted(() => {
  setTimeout(() => {
    cmRef.value?.refresh();
  }, 1000);
});

onUnmounted(() => {
  cmRef.value?.destroy();
});
const onChange = (val, cm) => {
  console.log(val);
  console.log(cm.getValue());
};
const onReady = (cm) => {
  console.log(cm.focus());
};
</script>
<style lang="less" scoped></style>
