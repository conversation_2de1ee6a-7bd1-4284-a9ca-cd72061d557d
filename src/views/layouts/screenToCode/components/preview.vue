<template>
  <iframe title="预览" :srcDoc="throttledCode"></iframe>
</template>
<script lang="ts" setup>
import { defineProps, watch } from "vue";
import { throttle } from "lodash";
interface Props {
  code: string;
  appState: "INITIAL" | "CODING" | "CODE_READY";
}
const props = defineProps<Props>();
const throttledCode = ref("");

const throttled = throttle(
  () => {
    console.log(props.code);
    throttledCode.value = props.code;
  },
  200,
  { trailing: false }
);
watch(
  () => props.code,
  (code) => {
    throttled();
  }
);
</script>
<style lang="less" scoped>
iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
