import { ElMessage } from "element-plus";

const WS_BACKEND_URL =
  import.meta.env.VITE_WS_BACKEND_URL || "wss://design-ws.sy.soyoung.com/generate-code";
const ERROR_MESSAGE = "生成代码错误。";

export interface CodeGenerationParams {
  generationType: "create" | "update";
  image: string;
  history?: string[];
  // isImageGenerationEnabled: boolean; // TODO: Merge with Settings type in types.ts
}

export function generateCode(
  params: CodeGenerationParams,
  onChange: (chunk: string) => void,
  onSetCode: (code: string) => void,
  onStatusUpdate: (status: string) => void,
  onComplete: () => void
) {
  const wsUrl = `${WS_BACKEND_URL}`;

  const ws = new WebSocket(wsUrl);

  ws.addEventListener("open", () => {
    ws.send(
      JSON.stringify({
        event: "message",
        data: params
      })
    );
  });

  ws.addEventListener("message", async (event: MessageEvent) => {
    const response = JSON.parse(event.data);
    if (response.type === "chunk") {
      onChange(response.value);
    } else if (response.type === "status") {
      onStatusUpdate(response.value);
    } else if (response.type === "setCode") {
      onSetCode(response.value);
    } else if (response.type === "error") {
      console.error("Error generating code", response.value);
      ElMessage({
        message: response.value,
        type: "error"
      });
    }
  });

  ws.addEventListener("close", (event) => {
    console.log("Connection closed", event.code, event.reason);
    if (event.code != 1000) {
      console.error("WebSocket error code", event);
      ElMessage({
        message: ERROR_MESSAGE,
        type: "error"
      });
    } else {
      onComplete();
    }
  });

  ws.addEventListener("error", (error) => {
    console.error("WebSocket error", error);
    ElMessage({
      message: ERROR_MESSAGE,
      type: "error"
    });
  });
}
