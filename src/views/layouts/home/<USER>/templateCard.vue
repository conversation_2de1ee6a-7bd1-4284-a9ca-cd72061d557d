<template>
  <div class="ai-home-template-card" ref="templateCard" @click="handleClick">
    <div :class="`template-card-img-wrap ${needCenter ? 'need-center' : ''}`" :style="`${templateInfo.type === 'imgWaterfall' ? 'height:' + ((templateInfo.size.h * width) / templateInfo.size.w ? (templateInfo.size.h * width) / templateInfo.size.w : 200) + 'px;background:' + background + ';' : ''}`">
      <!-- 收藏&下载按钮 start -->
      <div class="group__btn_start">
        <div class="group-btn-start__item" @click.stop="collectionChange(templateInfo)" v-if="userInfo.syUid !== templateInfo.userId">
          <img loading="lazy" :src="isCollection ? 'https://static.soyoung.com/sy-pre/23ust432yoq4s-1697804341094.png' : 'https://static.soyoung.com/sy-pre/1nubvuocuun6k-1697804341094.png'" alt="" />
        </div>
        <div class="group-btn-start__item" @click.stop="download(templateInfo)">
          <img loading="lazy" src="https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697804341094.png" alt="" />
        </div>
      </div>
      <!-- 收藏&下载按钮 end -->
      <img loading="lazy" class="template-card-cover-img" :src="templateInfo.src && templateInfo.src.indexOf('?') < 0 ? `${templateInfo.src}?imageView2/0/format/webp` : templateInfo.src" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, defineEmits } from "vue";
import { collect } from "@/api/common";
import { downlondImage } from "@/utils/downloadImage";
import { userInfoStore } from "@/store";
import { ElNotification } from "element-plus";
const userInfo = userInfoStore();
defineProps({
  templateInfo: {
    type: Object as any,
    default: () => {
      return {
        key: "1111111",
        src: "https://static.soyoung.com/sy-pre/8d15af2b60d957d86961c0fbaa5b0cb7-1637511000713.jpeg",
        name: "name",
        collection: false,
        designCategory: { _id: "", name: "" },
        size: { w: "", h: "" },
        type: "",
        styleType: "girl"
      };
    }
  },
  params: {
    type: Object,
    default: () => {
      return {};
    }
  },
  noBorder: {
    type: Boolean,
    default: false
  },
  needCenter: {
    type: Boolean,
    default: false
  },
  designType: {
    type: String,
    default: ""
  },
  maxHeight: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(["preview"]);
const templateCard = ref<any>(null);

// 收藏
const isCollection = ref<boolean>(false);
const collectionChange = async (item: any) => {
  const data = await collect({
    userId: item.userId,
    id: item.key,
    isCollect: 1
  });
  if (data.data.code === 0) {
    isCollection.value = true;
    ElNotification({
      type: "success",
      message: "收藏成功",
      duration: 3000
    });
  } else {
    ElNotification({
      type: "error",
      message: data.data.msg,
      duration: 3000
    });
  }
};

// 下载
const download = (item: any) => {
  console.log(item, "下载");
  downlondImage(item);
};
let width = ref(0);
const bgArr = ref(["#F0F7FE", "#FEF0F0", "#FEFBF0", "#EDF6E7", "#F0EAF7"]);
let background = ref("");

onMounted(() => {
  width.value = templateCard.value.offsetWidth;
  background.value = bgArr.value[Math.floor(Math.random() * 5)];
  window.addEventListener("resize", () => {
    templateCard.value && (width.value = templateCard.value.offsetWidth);
  });
});

const handleClick = () => {
  emit("preview");
};
</script>
<style lang="less" scoped>
.ai-home-template-card {
  cursor: pointer;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  display: flex;
  border-radius: 8px;
  transition: all 0.5s ease;
  z-index: 10;
  &:hover .group__btn_start {
    display: flex;
    transition: all 0.5s ease;
  }
  .group__btn_start {
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: none;
    transition: all 0.5s ease;
    .group-btn-start__item {
      margin-right: 10px;
      &:last-child {
        margin-right: 0;
      }
    }
    img {
      width: 22px;
      height: 22px;
    }
  }
  &:hover {
    transform: scale(0.99);
    transition:
      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.2s;
  }
  &:hover {
    border: 1px solid #675efc;
    box-shadow: 0 2px 4px 0 rgba(34, 31, 84, 0.2);
    box-sizing: border-box;
  }
  &:hover .ai-home-template-card__btn {
    z-index: 2;
    opacity: 1;
    transition:
      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.3s;
  }
  &:hover .ai-home-template-card__danger {
    z-index: 2;
    opacity: 1;
    transition:
      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.3s;
  }
  &:hover &__avatar {
    z-index: 2;
    opacity: 1;
    transition:
      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.3s;
  }
  &__avatar {
    width: 80%;
    position: absolute;
    top: 10px;
    left: 10px;
    box-sizing: border-box;
    transition:
      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.3s;
    z-index: 10;
    opacity: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 1px solid #ffffff;
    }
    &-text {
      color: #ffffff;
      font-size: 12px;
      width: 60%;
      margin-left: 10px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .template-card-img-wrap {
    position: relative;
    box-sizing: border-box;
    transition:
      all 30000ms cubic-bezier(0, 0, 1, 1) 0ms,
      0.3s;
    border-radius: 5px;
    overflow: hidden;
    width: 100%;
    background: transparent;
    &.need-center {
      display: flex;
      align-items: center;
    }
  }
  .template-title {
    position: absolute;
    left: 0;
    bottom: 8px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #303233;
    letter-spacing: 0;
    font-weight: 400;
    line-height: 20px;
    padding: 8px 0 0;
    text-align: left;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    &.hover {
      display: none;
    }
  }
  .temp__sec__imgUp {
    position: absolute;
    top: 0;
    bottom: 50%;
    left: 0;
    right: 0;
    // background: rgba(0, 0, 0, 0.3);
    z-index: 2;
  }
  .temp__sec__imgDown {
    position: absolute;
    top: 50%;
    bottom: 0;
    left: 0;
    right: 0;
    // background: rgba(0, 0, 0, 0.6);
    z-index: 2;
  }
  &:hover .template-card-cover-img {
    //transform: scale(0.8);
    //transition: all 300ms cubic-bezier(0,0,1,1) 0ms, .2s;
  }
  .transparent-cover {
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 1;
  }
  &.no-border {
    border: none;
    border-radius: 0;
    .template-card-cover-img {
      // width: 100%;
      // height: 100% !important;
      // object-fit: cover;
    }
    .el-image__inner {
      width: 100%;
      height: 100% !important;
      object-fit: cover;
    }
  }
  &:hover {
    .template-card-preview-tools,
    .tools-use-btn {
      display: block;
    }
  }
  .template-card-preview-tools {
    position: absolute;
    top: 10px;
    right: 10px;
    line-height: 30px;
    z-index: 2;
    display: none;
  }
  .tools-use-btn {
    position: absolute;
    left: 5px;
    top: 5px;
    z-index: 2;
    font-size: 12px;
    height: 30px;
    padding: 7px 15px;
    display: none;
  }
  .tools-icon-btn {
    width: 30px;
    height: 30px;
    vertical-align: middle;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    display: inline-block;
    line-height: 30px;
    border-radius: 4px;
    margin-right: 5px;
    text-align: center;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
  .icon-shoucang {
    color: #c0c2cc;
    &:hover {
      color: #f4f5fa;
    }
    &.active {
      color: #f33155;
      &:hover {
        color: #fd7890;
      }
    }
  }

  .template-card-cover {
    width: 100%;
    vertical-align: middle;
    display: flex;
    align-items: center;
  }
  .el-image__inner {
    height: auto !important;
  }

  .template-card-cover-img {
    width: 100%;
    height: 100%;
    //object-fit: cover;
    display: inline-block;
    vertical-align: middle;
    transition: transform 0.2s ease;
  }
}
</style>
