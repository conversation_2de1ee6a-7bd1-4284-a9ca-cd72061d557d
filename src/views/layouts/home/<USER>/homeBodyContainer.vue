<template>
  <div class="home-body-right__container">
    <lazy-image ref="lazyWaterfall"></lazy-image>
  </div>
</template>
<script lang="ts" setup>
import LazyImage from "@/views/layouts/home/<USER>/lazyImage.vue";
</script>
<style lang="less" scoped>
.home-body-right__container {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 0 55px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  .home-body-right-container__item {
    width: 200px;
    height: 100%;
    margin-right: 24px;
    img {
      width: 100%;
    }
  }
}
</style>
