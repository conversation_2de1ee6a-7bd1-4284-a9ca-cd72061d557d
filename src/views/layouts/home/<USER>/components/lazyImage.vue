<template>
  <div class="ai-home-lazy-img__waterfall" ref="wrappRef">
    <div class="img__waterfall__container" ref="imgWaterfallRef" style="overflow: hidden">
      <div class="img__ls__box" v-for="(v, index) in imgWaterfallList" :key="index">
        <div class="img__box" v-for="child in v.children" :key="child._id" @click="onClickImage(child)">
          <TemplateCard
            :templateInfo="{
              key: child._id,
              src: child.url,
              name: child.name,
              isShared: child.isShared,
              size: { w: child.width, h: child.height },
              type: 'imgWaterfall',
              isShowShareBtn: showUploadBtnStatus
            }"
            :params="child"
            @update-material="handleUpdateMaterial"
          />
        </div>
      </div>
      <el-empty v-if="imgWaterfallList.length === 0 && isReady" description="没有数据"></el-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref, onMounted, watch } from "vue";
import TemplateCard from "./templateCard.vue";

const props = defineProps<{
  list: any;
  showUploadBtnStatus: boolean;
}>();

const emits = defineEmits(["change", "updateMaterial"]);
let imgWaterfallList = ref<any>([]);
let imgWaterfallRef = ref<any>(null);
let wrappRef = ref<any>(null);
let pageCount = ref<number>(1);
let isReady = ref<boolean>(false);
let width = ref(0);

const getImgWaterfall = () => {
  // 数据分成5组-加载就可以，然后懒加载，懒加载判断从上次加到的组开始加数组
  isReady.value = true;
  pageCount.value === 1 && (imgWaterfallList.value = []);
  props.list.forEach((e: any, index: number) => {
    if (index < 5 && pageCount.value === 1) {
      let _h = e.height * (width.value / e.width);
      imgWaterfallList.value[index] = {
        height: (_h > 400 ? 400 : _h) + 36,
        children: []
      };
      imgWaterfallList.value[index].children.push(e);
    } else {
      // 计算高度比较小的添加
      let heigthArr: Array<number> = [];
      for (let v in imgWaterfallList.value) {
        heigthArr.push(imgWaterfallList.value[v].height);
      }
      for (let v in imgWaterfallList.value) {
        let _h = e.height * (width.value / e.width);
        if (imgWaterfallList.value[v].height === heigthArr.sort((a: number, b: number) => a - b)[0]) {
          imgWaterfallList.value[v].height += (_h > 400 ? 400 : _h) + 36;
          imgWaterfallList.value[v].children.push(e);
          return;
        }
      }
    }
  });
};

const handleUpdateMaterial = () => {
  emits("updateMaterial");
};

watch(
  () => props.list,
  () => {
    getImgWaterfall();
  },
  {
    immediate: true
  }
);

// 查看大图
const onClickImage = (item: any) => {
  if (item._id == 1) {
    emits("change");
  } else {
    // viewLargerImage();
    console.log(item, "查看大图");
  }
};
</script>

<style lang="less" scoped>
.ai-home-lazy-img__waterfall {
  width: 100%;
  overflow-y: auto;
  min-height: calc(100vh - 134px);
  position: relative;
  padding-top: 20px;
  box-sizing: border-box;
  &::-webkit-scrollbar {
    width: 0 !important;
  }
  .design-list-no-more {
    clear: both;
    text-align: center;
    color: #aaa;
    padding: 20px;
    font-size: 14px;
  }
  .img__ls__box {
    width: 20%;
    float: left;
    padding-right: 20px;
    box-sizing: border-box;
    &:nth-child(5n) {
      padding-right: 0;
    }
  }
  .img__box {
    margin-bottom: 16px;
    width: 100%;
    border-radius: 4px;
    vertical-align: middle;
  }
  .item {
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    font-size: 1.2em;
    color: rgb(0, 158, 107);
  }
  .item:after {
    content: attr(index);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
  }
  .wf-transition {
    transition: opacity 0.3s ease;
    -webkit-transition: opacity 0.3s ease;
  }
  .wf-enter {
    opacity: 0;
  }
  .item-move {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    -webkit-transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }
}
</style>
