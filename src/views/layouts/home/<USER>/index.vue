<template>
  <div class="home-body">
    <!-- 左侧导航部分内容 start -->
    <div class="home-body__left">
      <!-- 分类导航list start -->
      <div
        :class="{
          'category-nav-selector': true,
          'category-nav-selector__active': store.themeShow
        }"
      >
        <div class="category-nav-selector__btn" v-click-outside="handleOutsideClick" @click="handleToggle">
          <span>全部{{ currentDropdownMenuName }}</span>
          <i class="iconfont icon-jiantoushouqi" :style="{ transform: isReverse ? 'rotate(180deg)' : 'rotate(0deg)', color: isReverse ? '#5C54F0' : '' }"></i>
        </div>
        <!-- 分类loop list start -->
        <div
          ref="dropdownRef"
          :class="{
            'category-nav-selector__dropdown': true,
            'category-nav-selector-dropdown-reverse__false': !isReverse,
            'category-nav-selector-dropdown-reverse__true': isReverse
          }"
        >
          <div class="category-nav-selector-dropdown__item" v-for="item in categoryList" :key="item._id" @click="handleSelectedMenuItem(item._id)">
            <span>全部{{ item.name }}</span>
            <i v-if="item._id === material.categoryId" class="iconfont icon-duigou icon-duigou__active"></i>
            <div v-if="item._id === material.categoryId" class="category-nav-selector-dropdown-item__bg"></div>
          </div>
        </div>
        <!-- <div v-if="routerListData.length === 0" class="category-nav-selector-no__data">暂无数据</div> -->
        <!-- 分类loop list end -->
      </div>
      <!-- 分类导航list end -->

      <!-- 灵感集导航 start -->
      <sp-nav @tabs-change="tabsChange" :data="routerListData" :default-id="material.appId"></sp-nav>
      <!-- 灵感集导航 end -->
    </div>
    <!-- 左侧导航部分内容 end -->

    <!-- 右侧内容 start -->
    <div class="home-body__right">
      <home-body-header :app-or-design="appOrDesign"></home-body-header>
      <home-body-container></home-body-container>
    </div>
    <!-- 右侧内容 start -->
  </div>
</template>
<script lang="ts" setup>
import SpNav from "@/views/layouts/components/spNav.vue";
import { materialStore, themeStore } from "@/store";
import HomeBodyHeader from "@/views/layouts/home/<USER>/homeBodyHeader.vue";
import HomeBodyContainer from "@/views/layouts/home/<USER>/homeBodyContainer.vue";
import { appsList } from "@/api/common";
import { onMounted, ref, computed } from "vue";
import { GetCategoryList } from "@/api/admin";
import { ElMessage } from "element-plus";

const material = materialStore(); // 素材Store
const store = themeStore(); // 主题store

const appOrDesign = ref<number>(1); // 1: 'app' or 'it'

const routerListData = ref<any>([]);

const tabsChange = (item: any) => {
  appOrDesign.value = +item._id;
  material.updateMaterialInfo(item);
};

const initData = async () => {
  await getCategoryList();
};

// 分类下拉
const dropdownRef = ref<any>(null);
const categoryList = ref<any>([]);
const currentDropdownMenuName = computed(() => {
  return categoryList.value.find((item: any) => item._id === material.categoryId)?.name;
});
const isReverse = ref<boolean>(false);

const handleOutsideClick = () => {
  isReverse.value = false;
};

const handleToggle = () => {
  isReverse.value = true;
};

// 选中某一个分类
const handleSelectedMenuItem = (_id: string) => {
  material.updateCategoryId(_id);
  getRouterListData();
  isReverse.value = false;
};

const getCategoryList = async () => {
  try {
    const res = await GetCategoryList({});
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    if (res.data.length === 0) {
      ElMessage.error("暂无数据");
      return;
    }
    categoryList.value = [...categoryList.value, ...res.data];

    handleSelectedMenuItem(res.data[0]._id);
    await getRouterListData();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const getRouterListData = async () => {
  const data = await appsList({
    categoryId: material.categoryId
  });
  routerListData.value = data.data.data.list;
};

onMounted(() => {
  initData();
});
</script>
<style lang="less" scoped>
.home-body {
  width: 100%;
  display: flex;
  flex-direction: row;
  .home-body__left {
    width: 250px;
    display: flex;
    flex-direction: column;
    .category-nav-selector {
      position: relative;
      width: 100%;
      padding: 10px 20px 0;
      box-sizing: border-box;
      background: #ffffff;
      .category-nav-selector__btn {
        position: relative;
        width: 100%;
        height: 44px;
        background: #f4f4f4;
        color: #171717;
        border-radius: 6px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;
        i {
          position: absolute;
          right: 20px;
          font-size: 10px;
          color: #c0c4cc;
          transition: transform 0.3s;
          transform: rotate(0);
        }
      }
      .category-nav-selector__dropdown {
        width: 210px;
        position: absolute;
        top: 50px;
        background: hsla(0, 0%, 100%, 0.98);
        box-shadow: 0 2px 25px 0 rgba(0, 0, 0, 0.21);
        z-index: 10;
        border: none;
        border-radius: 1px;
        box-sizing: border-box;
        overflow-x: hidden;
        overflow-y: auto;
        margin-top: 5px;
        &::-webkit-scrollbar {
          display: none;
        }
        .category-nav-selector-dropdown__item {
          position: relative;
          height: 40px;
          font-size: 14px;
          color: #171717;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
          box-sizing: border-box;
          cursor: pointer;
          span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          &:first-child {
            margin-top: 10px;
          }
          i {
            font-size: 12px;
            &.icon-duigou__active {
              color: var(--el-color-primary);
            }
          }
          .category-nav-selector-dropdown-item__bg {
            position: absolute;
            top: 0;
            left: 0;
            background: var(--el-color-primary);
            transition: all 0.2s;
            width: 100%;
            height: 40px;
            opacity: 0.05;
          }
        }
        &.category-nav-selector-dropdown-reverse__false {
          max-height: 0;
          transition: max-height 0.3s ease-in;
        }
        &.category-nav-selector-dropdown-reverse__true {
          transition: max-height 0.3s ease-out;
          transform-origin: 50% 0;
          animation: slide-down 0.3s ease-out;
          -webkit-animation: slide-down 0.3s ease-out;
          max-height: 316px;
          padding: 0 0 10px;
        }
      }
      .category-nav-selector-no__data {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      &.category-nav-selector__active {
        background: #26282b;
        .category-nav-selector__btn {
          background: rgba(255, 255, 255, 0.05);
          span {
            color: #ffffff;
          }
        }
        .category-nav-selector__dropdown {
          background: rgba(51, 51, 51);
          .category-nav-selector-dropdown__item {
            span {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .home-body__right {
    width: 100%;
    position: relative;
    height: calc(100vh - 64px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}
@-webkit-keyframes slide-down {
  0% {
    height: 0;
  }

  100% {
    height: 316px;
  }
}

@-webkit-keyframes slide-up {
  0% {
    height: 316px;
  }

  100% {
    height: 0;
  }
}
</style>
