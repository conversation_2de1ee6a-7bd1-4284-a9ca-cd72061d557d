<template>
  <div class="upload-home-body">
    <!-- 左侧导航部分内容 start -->
    <div class="home-body__left">
      <div :class="['upload-box', { 'upload-theme': store.themeShow }]">
        <div :class="['upload-folder']" @click="onClickUpload">
          <div class="upload-title">我的上传</div>
          <div class="upload-btn">
            <i class="iconfont icon-baoliu"></i>
            文件夹
          </div>
        </div>
      </div>
      <!-- 灵感集导航 start -->
      <SpNav :identify="1" :data="folderList" :defaultId="folderList && folderList.length > 0 ? folderList[0]._id : ''" @tabs-change="tabsChange" @handleRename="handleRename" @handleDelete="handleDelete"></SpNav>
      <!-- 灵感集导航 end -->
    </div>
    <!-- 左侧导航部分内容 end -->

    <!-- 右侧内容 start -->
    <div class="home-body__right">
      <div class="home-body-right__container">
        <lazy-image :list="materialListArr" :showUploadBtnStatus="folderType === 'collect'" ref="lazyWaterfall" @change="hanlerUploadImage" @update-material="handleUpdateMaterial"></lazy-image>
      </div>
    </div>
    <!-- 右侧内容 start -->
  </div>
  <!-- 新建文件夹 -->
  <el-dialog v-model="dialogVisible" :title="folderReNameStatus ? '文件夹重命名' : '创建文件夹'" center width="480px">
    <el-input v-model="folderName" placeholder="请输入文件夹名称"></el-input>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="
            dialogVisible = false;
            folderReNameStatus = false;
          "
          >取消</el-button
        >
        <el-button type="primary" @click="handleCreateFolder">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogUploadStatus" :show-close="false" center width="480px" :modal-class="'upload-dialog__style'">
    <div class="upload-dialog-style__header">
      <span @click="close">
        <i class="iconfont icon-zuojiantou"></i>
      </span>
      <span class="upload-dialog-style-header__span">上传</span>
      <span @click="close">
        <img loading="lazy" src="https://static.soyoung.com/sy-pre/1t4pyhik1dytx-1698235800755.png" alt="" />
      </span>
    </div>
    <el-form label-width="100px">
      <el-form-item label="上传图片：">
        <div class="upload-image" @click="handleUpload">
          <i class="iconfont icon-tianjia"></i>
          <input class="input-image" type="file" multiple accept="image/*" ref="inputImage" @change="changeImgCheck($event)" />
        </div>
      </el-form-item>
      <el-form-item v-if="material.url.length > 0">
        <div class="upload-image-list">
          <div class="upload-image-list__item" v-for="(item, index) in imageData" :key="index" v-loading="item.loading">
            <img loading="lazy" :src="item.url" alt="" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="选择应用：">
        <el-select v-model="material.appId" placeholder="请选择应用" @change="onChangeApp">
          <el-option v-for="item in appsListArr" :key="item._id" :label="item.name" :value="item._id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择版本：">
        <el-select v-model="material.lverId" placeholder="请选择版本">
          <el-option v-for="item in appsVersionList" :key="item._id" :label="item.name" :value="item._id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择功能：">
        <el-select v-model="material.moduleId" placeholder="请选择功能">
          <el-option v-for="item in tagsListArr" :key="item._id" :label="item.name" :value="item._id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="共享图片：">
        <el-switch v-model="material.isShared"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeUpload">取消</el-button>
        <el-button type="primary" @click="handleCreateImage">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { appsList, appsModuleList, getfolderList, materialList, upload, versionList, folderDelete } from "@/api/common";
import { folderAdd, materialAddAll, folderNename } from "@/api/upload";
import { userInfoStore } from "@/store";
import SpNav from "@/views/layouts/components/spNav.vue";
import LazyImage from "@/views/layouts/home/<USER>/components/lazyImage.vue";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { themeStore } from "@/store";
const store = themeStore();
const userInfo = userInfoStore();
const folderReNameStatus = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const dialogUploadStatus = ref<boolean>(false);
const folderName = ref<string>(""); // 文件夹名称
const folderId = ref<string>(""); // 1: 'app' or 'it'
const folderType = ref<string>("collect"); // 1: 'app' or 'it'
const folderList = ref<any>([]);
const appsListArr = ref<any>([]);
const tagsListArr = ref<any>([]);
const materialListArr = ref<any>([]);
const inputImage = ref();
const appsVersionList = ref<any>([]);
const material = reactive<any>({
  url: [],
  appId: "",
  lverId: "",
  moduleId: "",
  isShared: false,
  fileIds: [] // 已经存储的图片ID
});

// 工作台文件夹选中
const tabsChange = async (item: any) => {
  folderId.value = item._id;
  folderType.value = item.type;
  if (folderId.value) {
    getMaterialList();
  }
};

// 工作台文件夹重命名
const handleRename = async (item: any) => {
  folderReNameStatus.value = true;
  dialogVisible.value = true;
  folderName.value = "";
  folderId.value = item._id;
};
// 工作台文件夹删除
const handleDelete = async (item: any) => {
  const res = await folderDelete({ id: item._id });
  if (res.status == 200 && res.data.code == 0) {
    ElMessage({
      type: "success",
      message: "操作成功"
    });
    try {
      const res = await getfolderList({});
      if (res.status == 200 && res.data.code == 0) {
        folderList.value = res.data.data;
      }
    } catch (error) {
      console.log(error);
    }
  } else {
    ElMessage({
      type: "error",
      message: res.data.message || "操作失败"
    });
  }
};

const onChangeApp = async () => {
  try {
    const res3 = await versionList({
      appId: material.appId,
      isDeleted: 0
    });
    if (res3.status == 200 && res3.data.code == 0) {
      appsVersionList.value = res3.data.data;
    }
  } catch (error) {
    console.log(error);
  }
  try {
    const res = await appsModuleList({
      appId: material.appId
    });
    if (res.status == 200 && res.data.code == 0) {
      tagsListArr.value = res.data.data;
    }
  } catch (error) {
    console.log(error);
  }
};

const handleCreateFolder = async () => {
  if (!folderName.value) {
    ElMessage({
      type: "error",
      message: "请输入文件夹名称"
    });
    return;
  }

  const res = folderId.value
    ? await folderNename({
        name: folderName.value,
        id: folderId.value
      })
    : await folderAdd({ name: folderName.value });
  if (res.status == 200 && res.data.code == 0) {
    dialogVisible.value = false;
    ElMessage({
      type: "success",
      message: "操作成功"
    });
    try {
      const res = await getfolderList({});
      if (res.status == 200 && res.data.code == 0) {
        folderList.value = res.data.data;
      }
    } catch (error) {
      console.log(error);
    }
  } else {
    ElMessage({
      type: "error",
      message: res.data.message || "操作失败"
    });
  }
};
const closeUpload = () => {
  dialogUploadStatus.value = false;
};
const getAppsList = async () => {
  try {
    const res1 = await appsList({});
    if (res1.status == 200 && res1.data.code == 0) {
      appsListArr.value = res1.data.data.list;
    }
  } catch (error) {
    console.log(error);
  }
};

const hanlerUploadImage = () => {
  dialogUploadStatus.value = true;
  material.appId = "";
  material.lverId = "";
  material.moduleId = "";
  material.isShared = false;
  material.fileIds = [];
  material.url = [];
  // inputImage.value = "";
  getAppsList();
};

const handleUpdateMaterial = () => {
  getMaterialList();
};

const handleCreateImage = async () => {
  const args = {
    appId: material.appId,
    lverId: material.lverId,
    moduleId: material.moduleId,
    fileIds: material.fileIds,
    folderId: folderId.value,
    isShared: material.isShared ? 1 : 0
  };
  const res = await materialAddAll(args);
  if (res.data.code == 0) {
    dialogUploadStatus.value = false;
    ElMessage({
      type: "success",
      message: "操作成功"
    });
    getMaterialList();
  } else {
    ElMessage({
      type: "error",
      message: res.data.message || "操作失败"
    });
  }
};

const imageData = ref<any>([]); // 用来展示多选图片信息&加载状态
const startIndex = ref<number>(0);

// 选择文件进行初始化loading
const initImageData = (data: any) => {
  // 保存新上传的文件的起始索引
  startIndex.value = imageData.value.length;

  if (material.url.length === 0) {
    imageData.value = [];
  } else {
    imageData.value = material.url.map((item: any) => {
      return {
        url: item,
        loading: false
      };
    });
  }

  for (let i = 0; i < data.length; i++) {
    imageData.value.push({
      url: "",
      loading: true
    });
  }
};

const changeImgCheck = async (e) => {
  if (e.target.files.length > 0) {
    let files = e.target.files;
    initImageData(files); // 初始化loading
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
      formData.delete("file");
      formData.append("file", files[i]);
      const res = await upload(formData);
      material.fileIds.push(res.data.data._id);
      material.url.push(res.data.data.url);
      if (material.url.length === 1) {
        imageData.value[i].url = res.data.data.url;
        imageData.value[i].loading = false;
      } else {
        imageData.value[startIndex.value + i].url = res.data.data.url;
        imageData.value[startIndex.value + i].loading = false;
      }
    }
  }
};

const onClickUpload = () => {
  dialogVisible.value = true;
  folderName.value = "";
};

const handleUpload = async () => {
  console.log(inputImage.value);
  if (inputImage.value) {
    inputImage.value.click();
  }
};

// 获取素材
const getMaterialList = async () => {
  materialListArr.value = [];
  try {
    const res = await materialList({
      page: 1,
      pageSize: 500,
      folderId: folderId.value
    });
    if (res.status == 200 && res.data.code == 0) {
      if (folderType.value != "collect") {
        const uploadImage = {
          url: "https://static.soyoung.com/sy-pre/20231102-170244-1698912600671.png",
          width: 400,
          height: 400,
          _id: "1",
          name: ""
        };
        materialListArr.value.push(uploadImage);
      }
      materialListArr.value = materialListArr.value.concat(res.data.data.list);
    }
  } catch (error) {
    console.log(error);
  }
};

// 获取文件夹
const getFolderList = async () => {
  try {
    const res = await getfolderList({});
    if (res.status == 200 && res.data.code == 0) {
      folderList.value = res.data.data;
      folderType.value = res.data.data[0].type;
      folderId.value = res.data.data[0]._id;
      getMaterialList();
    }
  } catch (error) {
    console.log(error);
  }
};

// 关闭&取消
const close = () => {
  dialogUploadStatus.value = false;
};

onMounted(() => {
  getFolderList();
  getAppsList();
});
</script>
<style lang="less">
.upload-home-body {
  width: 100%;
  display: flex;
  flex-direction: row;
  .home-body__left {
    width: 250px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .upload-box {
      padding: 20px;
      background: #ffffff;
      box-sizing: border-box;
      &.upload-theme {
        background-color: #26282b;
        .upload-folder {
          background: #000;
          border: none;
          .upload-title {
            color: #ffffff;
          }
        }
      }
      .upload-folder {
        padding: 0 10px;
        background: #fefefe;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        cursor: pointer;
        height: 50px;
        line-height: 50px;
        display: flex;
        justify-content: space-between;
      }

      .upload-title {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #262626;
        font-weight: 500;
        width: 126px;
      }
      .upload-btn {
        flex: 1;
        color: #675efc;
        font-size: 14px;
        .iconfont {
          font-size: 12px;
          margin-right: 4px;
        }
      }
    }
  }
  .home-body__right {
    width: 100%;
    height: calc(100vh - 64px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .home-body-right__container {
      width: 100%;
      height: 100%;
      position: relative;
      padding: 0 55px;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      overflow-y: auto;
      overflow-x: hidden;
      scroll-behavior: smooth;
      scroll-snap-type: x proximity;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      .home-body-right-container__item {
        width: 200px;
        height: 100%;
        margin-right: 24px;
        img {
          width: 100%;
        }
      }
    }
  }
}
.upload-image {
  border: 1px dashed #bfbfbf;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
  img {
    width: 100%;
    height: 100px;
    display: inline-block;
  }
  .iconfont {
    display: inline-block;
    font-size: 20px;
    color: #d9d9d9;
  }
  .input-image {
    visibility: hidden;
  }
}
.upload-dialog__style {
  width: 100%;
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog--center {
    border-radius: 18px !important;
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
  .el-dialog__header {
    padding: 0;
  }
  .upload-dialog-style__header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 20px;
    .upload-dialog-style-header__span {
      flex: 1;
      font-size: 16px;
      color: #303233;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
    i {
      font-size: 16px;
      color: #000000;
      cursor: pointer;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .el-button {
    width: 130px;
    height: 44px;
  }
}
.upload-image-list {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  &__item {
    width: 102px;
    height: 102px;
    border: 1px solid #999999;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 10px;
    img {
      width: 100%;
      object-fit: contain;
      height: 100%;
    }
  }
}
</style>
