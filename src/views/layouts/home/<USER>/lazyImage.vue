<template>
  <div class="ai-home-lazy-img__waterfall" ref="wrappRef" @scroll="handleScrollChange">
    <div class="img__waterfall__container" ref="imgWaterfallRef" style="overflow: hidden">
      <div class="img__ls__box" v-for="(v, index) in imgWaterfallList" :key="index">
        <div class="img__box" v-for="child in v.children" :key="child._id">
          <template-card
            :templateInfo="{
              key: child._id,
              src: child.url,
              name: child.name,
              size: { w: child.width, h: child.height },
              type: 'imgWaterfall',
              userId: child.userId
            }"
            :params="child"
            @preview="handlePreview(child)"
          />
        </div>
      </div>
      <el-empty v-if="imgWaterfallList.length === 0 && isReady" description="没有数据"></el-empty>
      <div v-else>
        <div v-if="imgWaterfallLoading" class="design-list-no-more">加载中...</div>
        <div v-if="listNoMore" class="design-list-no-more">没有更多了</div>
      </div>
    </div>
  </div>
  <ImageViewer :material="material" :showViewer="showViewer" @close="showViewer = false" />
</template>

<script lang="ts">
import { materialList } from "@/api/common";
import { materialStore } from "@/store";
import { ElNotification } from "element-plus";
import { defineComponent, onMounted, ref, watch } from "vue";
import TemplateCard from "./templateCard.vue";
import ImageViewer from "../../components/image-view.vue";
import { debounce } from "lodash";

export default defineComponent({
  components: {
    TemplateCard,
    ImageViewer
  },
  setup() {
    let imgWaterfallList = ref<any>([]);
    let imgWaterfallRef = ref<any>(null);
    let wrappRef = ref<any>(null);
    let pageCount = ref<number>(1);
    let listNoMore = ref<boolean>(false);
    let imgWaterfallLoading = ref<boolean>(false);
    let showViewer = ref<boolean>(false);
    let material = ref<Record<string, any>>({});
    let isReady = ref<boolean>(false);
    let width = ref(0);
    let store = materialStore(); // 素材Store
    const total = ref<number>(0);

    const setMenuId = () => {
      pageCount.value = 1;
      listNoMore.value = false;
      getImgWaterfall();
    };
    const handlePreview = (info) => {
      material.value = info;
      showViewer.value = true;
    };
    const getImgWaterfall = async () => {
      if (listNoMore.value) {
        return false;
      }
      imgWaterfallLoading.value = true;
      const data = await materialList({
        appId: +store.appId !== 1 ? store.appId : "",
        lverId: store.versionId.join(",") || "",
        moduleId: store.tagsId.join(",") || "",
        categoryId: store.categoryId,
        page: pageCount.value,
        pageSize: 20
      });
      if (data.data.code === 0) {
        // 数据分成5组-加载就可以，然后懒加载，懒加载判断从上次加到的组开始加数组
        isReady.value = true;
        total.value = +data.data.data.total;
        data.data.data.list.length < 10 && (listNoMore.value = true);
        pageCount.value === 1 && (imgWaterfallList.value = []);
        data.data.data.list.forEach((e: any, index: number) => {
          if (index < 5 && pageCount.value === 1) {
            let _h = e.height * (width.value / e.width);
            imgWaterfallList.value[index] = {
              height: (_h > 400 ? 400 : _h) + 36,
              children: []
            };
            imgWaterfallList.value[index].children.push(e);
          } else {
            // 计算高度比较小的添加
            let heigthArr: Array<number> = [];
            for (let v in imgWaterfallList.value) {
              heigthArr.push(imgWaterfallList.value[v].height);
            }
            for (let v in imgWaterfallList.value) {
              let _h = e.height * (width.value / e.width);
              if (imgWaterfallList.value[v].height === heigthArr.sort((a: number, b: number) => a - b)[0]) {
                imgWaterfallList.value[v].height += (_h > 400 ? 400 : _h) + 36;
                imgWaterfallList.value[v].children.push(e);
                return;
              }
            }
          }
        });
      } else {
        ElNotification({
          message: data.data.msg,
          type: "error",
          duration: 1500
        });
      }
      imgWaterfallLoading.value = false;
    };

    watch(
      () => store.getMaterialData(),
      () => {
        pageCount.value = 1;
        imgWaterfallList.value = [];
        listNoMore.value = false;
        getImgWaterfall();
      },
      { immediate: true, deep: true }
    );

    onMounted(() => {
      width.value = imgWaterfallRef.value.offsetWidth / 5 - 20;
    });

    // scroll
    const handleScrollChange = debounce((event: any) => {
      if (listNoMore.value) {
        return false;
      }
      const { scrollHeight, scrollTop, clientHeight } = event.target;
      const sh = parseInt(scrollHeight);
      const st = parseInt(scrollTop);
      const ch = parseInt(clientHeight);
      if (sh - (st + ch) === 0 && pageCount.value !== total.value) {
        pageCount.value++;
        getImgWaterfall();
      }
    }, 300);

    return {
      setMenuId,
      getImgWaterfall,
      handleScrollChange,
      imgWaterfallList,
      imgWaterfallRef,
      wrappRef,
      isReady,
      imgWaterfallLoading,
      listNoMore,
      showViewer,
      material,
      handlePreview
    };
  }
});
</script>

<style lang="less" scoped>
.ai-home-lazy-img__waterfall {
  width: 100%;
  overflow-y: auto;
  min-height: calc(100vh - 134px);
  position: relative;
  padding-top: 20px;
  padding-bottom: 102px;
  box-sizing: border-box;
  &::-webkit-scrollbar {
    width: 0 !important;
  }
  .design-list-no-more {
    clear: both;
    text-align: center;
    color: #aaa;
    padding: 20px;
    font-size: 14px;
  }
  .img__ls__box {
    width: 20%;
    float: left;
    padding-right: 20px;
    box-sizing: border-box;
    &:nth-child(5n) {
      padding-right: 0;
    }
  }
  .img__box {
    margin-bottom: 16px;
    width: 100%;
    border-radius: 4px;
    vertical-align: middle;
  }
  .item {
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    font-size: 1.2em;
    color: rgb(0, 158, 107);
  }
  .item:after {
    content: attr(index);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
  }
  .wf-transition {
    transition: opacity 0.3s ease;
    -webkit-transition: opacity 0.3s ease;
  }
  .wf-enter {
    opacity: 0;
  }
  .item-move {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    -webkit-transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }
}
</style>
