<template>
  <div :class="['home', { 'home-theme': store.themeShow }]">
    <SpHeader>
      <SpLogin></SpLogin>
    </SpHeader>
    <RouterView></RouterView>
  </div>
</template>
<script lang="ts" setup>
import SpHeader from "@/views/layouts/components/spHeader.vue";
import SpLogin from "@/views/layouts/components/spLogin.vue";
import { themeStore } from "@/store";
import { RouterView } from "vue-router";
const store = themeStore(); // 黑白主题切换Store
</script>
<style lang="less" scoped>
.home {
  width: 100%;
  display: flex;
  flex-direction: column;
  &::-webkit-scrollbar {
    display: none;
  }
  &.home-theme {
    background: #26282b;
    .home-body {
      .home-body__right {
        .home-body-right__header {
          background: #26282b;
          .swiper-list-tool__bg {
            background: #000000;
            opacity: 0.4;
          }
          .previous-icon {
            color: #5c54f0;
            z-index: 1;
          }
          .home-body-right-header__more {
            .more-list__item {
              label {
                color: #ffffff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
