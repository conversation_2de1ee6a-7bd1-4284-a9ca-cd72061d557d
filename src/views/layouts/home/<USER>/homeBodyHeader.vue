<template>
  <div
    :class="{
      'home-body-right__header': true,
      'home-body-right-header__active': store.themeShow
    }"
    id="container"
  >
    <div class="home-body-right-header__one" v-if="appOrDesign === 1 || appOrDesign === 0">
      <div class="swiper-list__tool swiper-list__tool--previous" v-if="isToggle" @click="preChange">
        <div class="swiper-list-tool__bg"></div>
        <span class="previous-icon">
          <i class="iconfont icon-zuojiantou"></i>
        </span>
      </div>
      <div class="home-body-right-scroll__tabs">
        <sp-screen-tabs :is-toggle="isToggle" :data="appTabsData" @update-scroll-width="updateScrollWidth" :material-type="'module'"></sp-screen-tabs>
      </div>
      <div class="swiper-list__tool swiper-list__tool--next" v-if="isToggle" @click="nextChange">
        <div class="swiper-list-tool__bg"></div>
        <span class="previous-icon">
          <i class="iconfont icon-zuojiantou"></i>
        </span>
      </div>
    </div>
    <div class="home-body-right-header__more" v-else>
      <div class="more-list__item">
        <label>版本筛选：</label>
        <sp-screen-tabs :is-toggle="true" :data="versionTabsData" :material-type="'version'"></sp-screen-tabs>
      </div>
      <div class="more-list__item">
        <label>功能筛选：</label>
        <sp-screen-tabs :is-toggle="true" :data="appTabsData" :material-type="'module'"></sp-screen-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { versionList } from "@/api/common";
import { moduleList } from "@/api/upload";
import { themeStore } from "@/store";
import SpScreenTabs from "@/views/layouts/components/spScreenTabs.vue";
import { defineProps, onMounted, ref, watch, nextTick } from "vue";
import { materialStore } from "@/store";

defineProps({
  appOrDesign: {
    type: Number,
    default: 1
  }
});

const store = themeStore();
const materialStoreData = materialStore();

const isToggle = ref<boolean>(false); // 是否展示左右切换导航

const container = ref<any>(0); // 总宽度
const clientWidth = ref<any>(0); // 容器宽度
const scrollWidth = ref<any>(0); // 容器滚动宽度
const listItemW = ref<any>(0); // 内容宽度
const scrollListItemW = ref<any>(0); // 内容滚动宽度
const underWayScrollWidth = ref<number>(0); // 正在滚动的距离默认值为零
const appTabsData = ref<any[]>([]);
const versionTabsData = ref<any[]>([]);

/*
 * @description 点击左侧按钮实现Tab平缓向左滑动
 * */
const preChange = () => {
  const header: any = document.getElementById("tabsContainer");
  if (+header.scrollLeft > 0) {
    underWayScrollWidth.value -= 400;
    header.scrollLeft = underWayScrollWidth.value;
  }
};

/*
 * @description 点击右侧按钮实现Tab平缓向右滑动
 * */
const nextChange = () => {
  const design: any = document.getElementById("tabsContainer");
  if (+design.scrollLeft < +scrollWidth.value) {
    // 每次滚动增加固定距离
    underWayScrollWidth.value += 400;
    // 给当前的Element增加滚动距离
    design.scrollLeft = underWayScrollWidth.value;
  }
};

/*
 * @description 获取自滚动距离，用来实现主动触发的时候
 * 计算需要平滑滚动的距离
 */
const updateScrollWidth = (distance: number) => {
  underWayScrollWidth.value = distance;
};

/*
 * @description 获取标签,用来实现header标签筛选
 * */
const getModuleList = async () => {
  const data: any = await moduleList({});
  if (data.data.code === 0 && data.data.data.length > 0) {
    appTabsData.value = data.data.data;
    container.value = document.getElementById("container")?.clientWidth;
    clientWidth.value = document.getElementById("tabsContainer")?.clientWidth;
    await nextTick();
    let cur: any = document.querySelectorAll(".sp-screen-tabs__item");
    scrollWidth.value = cur[0].offsetWidth * data.data.data.length + data.data.data.length * 10;
    listItemW.value = document.getElementById("tabsContainer")?.clientWidth;
    scrollListItemW.value = document.getElementById("tabsContainer")?.scrollWidth;
    if (container.value - 20 < +scrollWidth.value && +clientWidth.value <= +scrollWidth.value) {
      isToggle.value = true;
    } else {
      isToggle.value = false;
    }
  } else {
    appTabsData.value = [];
  }
};

/*
 * @description 获取版本号,用来实现header版本号的筛选
 * */
const getVersionList = async (_id: string) => {
  const data: any = await versionList({ appId: _id });
  if (data.data.code === 0) {
    versionTabsData.value = data.data.data;
  } else {
    versionTabsData.value = [];
  }
};

/*
 * 数据初始化
 * */
const initData = () => {
  getModuleList();
  getVersionList("");
};

watch(
  () => materialStoreData.appId,
  (newId: string) => {
    getVersionList(newId);
  }
);

onMounted(() => {
  initData();
});
</script>
<style lang="less" scoped>
.home-body-right__header {
  width: 100%;
  padding: 15px 20px;
  box-sizing: border-box;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  .home-body-right-header__one {
    display: flex;
    align-items: center;
  }
  .home-body-right-header__more {
    display: flex;
    flex-direction: column;
    .more-list__item {
      width: 100%;
      height: 30px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      label {
        font-size: 14px;
        color: #303233;
      }
    }
  }

  .swiper-list__tool {
    position: relative;
    width: 40px;
    height: 40px;
    margin-right: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: #5c54f0;
    cursor: pointer;

    .swiper-list-tool__bg {
      opacity: 0.1;
      background: #5c54f0;
      position: absolute;
      top: 0;
      left: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      z-index: 0;
    }

    &.swiper-list__tool--next {
      transform: rotateY(180deg);
      margin-left: 10px;
      margin-right: 0;
    }
  }
  .home-body-right-scroll__tabs {
    width: 100%;
    overflow: hidden;
  }
  &.home-body-right-header__active {
    background-color: #303233;
  }
}
</style>
