<template>
  <div class="material">
    <div class="material-title">素材管理</div>
    <el-form :inline="true">
      <el-form-item label="应用ID:">
        <el-input v-model="form.appId" />
      </el-form-item>
      <el-form-item label="模块ID:">
        <el-input v-model="form.moduleId" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getMaterialList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData">
      <el-table-column label="缩略图">
        <template #default="{ row }">
          <img loading="lazy" width="40" height="40" :src="row.url" />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="format" label="格式"></el-table-column>
      <el-table-column prop="" label="基础分类"></el-table-column>
      <el-table-column #default="{ row }" label="创建时间">
        {{ formatDate(row.createTime, "yyyy-MM-dd HH:mm:SS") }}
      </el-table-column>
      <el-table-column label="操作" center>
        <template #default="{ row }"> <el-link type="primary"></el-link>&nbsp; <el-link type="primary" @click="editModule(row)">编辑</el-link>&nbsp; <el-link type="primary" @click="deleteMaterial(row)">删除</el-link>&nbsp;&nbsp; </template>
      </el-table-column>
    </el-table>
    <center>
      <el-pagination class="pagination" @size-change="handleCurrentChange" @current-change="handleCurrentChange" v-model:current-page="form.page" layout="prev, pager, next" :page-size="10" :total="total"> </el-pagination>
    </center>
    <el-dialog v-model="dialogUploadStatus" :show-close="false" center width="480px" :modal-class="'upload-dialog__style'">
      <div class="upload-dialog-style__header">
        <span @click="close">
          <i class="iconfont icon-zuojiantou"></i>
        </span>
        <span class="upload-dialog-style-header__span">上传</span>
        <span @click="close">
          <img src="https://static.soyoung.com/sy-pre/1t4pyhik1dytx-1698235800755.png" alt="" />
        </span>
      </div>
      <el-form label-width="100px">
        <el-form-item label="选择应用：">
          <el-select v-model="material.appId" placeholder="请选择应用" @change="onChangeApp">
            <el-option v-for="item in appsListArr" :key="item._id" :label="item.name" :value="item._id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择版本：">
          <el-select v-model="material.lverId" placeholder="请选择版本">
            <el-option v-for="item in appsVersionList" :key="item._id" :label="item.name" :value="item._id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择功能：">
          <el-select v-model="material.moduleId" placeholder="请选择功能">
            <el-option v-for="item in tagsListArr" :key="item._id" :label="item.name" :value="item._id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogUploadStatus = false">取消</el-button>
          <el-button type="primary" @click="handleEdit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { formatDate } from "@/utils/date";
import { appsList, appsModuleList, materialList, versionList } from "@/api/common";
import { UpdateMaterial, delMaterial } from "@/api/admin";
import { ElMessage, ElMessageBox } from "element-plus";
interface AppListType {
  _id: string;
  name: string;
}
const tableData = ref([]);
const form = ref({
  appId: "",
  moduleId: "",
  page: 1,
  pageSize: 10
});
const material = ref({
  appId: "",
  lverId: "",
  moduleId: ""
});
const total = ref(0);
const currentPage = ref(1);
const dialogUploadStatus = ref(false);
const appsListArr = ref<AppListType[]>([]);
const appsVersionList = ref<AppListType[]>([]);
const tagsListArr = ref<AppListType[]>([]);
const folderList = ref([]);
const folderType = ref(null);
const folderId = ref("");

const getAppsList = async () => {
  try {
    const res1 = await appsList({});
    if (res1.status == 200 && res1.data.code == 0) {
      appsListArr.value = res1.data.data.list;
    }
  } catch (error) {
    console.log(error);
  }
};
const onChangeApp = async (val: any) => {
  if (val) {
    material.value.lverId = "";
    material.value.moduleId = "";
  }
  try {
    const res3 = await versionList({
      appId: material.value.appId,
      isDeleted: 0
    });
    if (res3.status == 200 && res3.data.code == 0) {
      appsVersionList.value = res3.data.data;
    }
  } catch (error) {
    console.log(error);
  }
  try {
    const res = await appsModuleList({
      appId: material.value.appId
    });
    if (res.status == 200 && res.data.code == 0) {
      tagsListArr.value = res.data.data;
    }
  } catch (error) {
    console.log(error);
  }
};
const getMaterialList = async () => {
  let res = await materialList(form.value);
  total.value = res.data.data.total;
  tableData.value = res.data.data.list;
};
const handleCurrentChange = async (val) => {
  form.value.page = val;
  getMaterialList();
};
const close = () => {
  dialogUploadStatus.value = false;
};
const editModule = async (row) => {
  dialogUploadStatus.value = true;
  material.value = {
    id: row._id,
    appId: row.appId,
    lverId: row.lverId,
    moduleId: row.moduleId
  } as { id: string; appId: string; lverId: string; moduleId: string };
  onChangeApp(null);
};
const deleteMaterial = (row) => {
  ElMessageBox.confirm("确定删除当前素材？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    center: true
  }).then(async () => {
    let res = await delMaterial({ id: row._id });
    if (+res.code === 0) {
      ElMessage.success("删除成功～");
      tableData.value.splice(row.$index, 1);
    }
    console.log(res);
  });
};
const handleEdit = async () => {
  let res = await UpdateMaterial(material.value);
  if (+res.code === 0) {
    getMaterialList();
    dialogUploadStatus.value = false;
    ElMessage.success("更新成功～");
  } else {
    ElMessage(res.msg);
  }
};
onMounted(() => {
  getMaterialList();
  getAppsList();
});
</script>

<style lang="less" scoped>
/deep/.pagination {
  display: flex;
  justify-content: center;
}
/deep/.upload-dialog__style {
  width: 100%;
  .el-dialog--center {
    border-radius: 18px !important;
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
  .el-dialog__header {
    display: none;
  }
  .upload-dialog-style__header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 20px;
    .upload-dialog-style-header__span {
      flex: 1;
      font-size: 16px;
      color: #303233;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
    i {
      font-size: 16px;
      color: #000000;
      cursor: pointer;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .el-button {
    width: 130px;
    height: 44px;
  }
}
</style>
