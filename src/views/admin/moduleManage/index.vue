<template>
  <div class="module">
    <div class="module-title">模块管理</div>
    <el-button type="primary" size="small" @click="dialogVisible = true">新增模块</el-button>
    <el-table :data="tableData" class="module-table" row-key="_id" default-expand-all>
      <el-table-column prop="name" label="模块名称" />
      <el-table-column label="应用数">
        <template #default="{ row }">
          {{ row.appIds.length }}
        </template>
      </el-table-column>
      <el-table-column label="创建人">
        <template #default="{ row }">
          {{ row.userId ? row.userId.name : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间">
        <template #default="{ row }">
          {{ formatDate(row.createTime, "yyyy-MM-dd HH:mm:SS") }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑模块' : '添加模块'" width="60%" :before-close="handleClose">
      <el-form :model="form" :rules="rules" ref="ruleFormRef" label-width="120px" status-icon>
        <el-form-item label="模块名称" prop="name">
          <el-input style="width: 200px" v-model="form.name" placeholder="请输入模块名称" />
        </el-form-item>
        <el-form-item label="所属应用" prop="appIds">
          <el-transfer :titles="['应用集合', '已选择']" v-model="form.appIds" :data="appList" :props="transferProps" />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input style="width: 200px" v-model="form.describe" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleRule"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { GetModuleList, GetAppList, AddModule, UpdateModule } from "@/api/admin";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from "@/utils/date";
const tableData = ref([]);
const appList = ref([]);
const dialogVisible = ref(false);
const form = ref<any>({});
const transferProps = {
  key: "_id",
  label: "name"
};
const ruleFormRef = ref();
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
  appIds: [
    {
      type: "array",
      required: true,
      message: "请选择所属应用",
      trigger: "change"
    }
  ]
});
const handleClose = () => {
  form.value = {};
  ruleFormRef.value.resetFields();
  dialogVisible.value = false;
};
const handleRule = () => {
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      handleSave();
    } else {
      console.log("error submit!", fields);
    }
  });
};
const handleSave = async () => {
  try {
    const res = await (form.value.id ? UpdateModule(form.value) : AddModule(form.value));
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    handleClose();
    getModuleList();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const handleEdit = (item) => {
  form.value = {
    id: item._id,
    name: item.name,
    appIds: item.appIds
  };
  dialogVisible.value = true;
};

const getModuleList = async () => {
  try {
    const res = await GetModuleList({});
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    tableData.value = res.data;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
const getAppList = async () => {
  try {
    const res = await GetAppList({
      page: 1,
      pageSize: 1000
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    appList.value = res.data.list.map(({ _id, name }) => ({ _id, name }));
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
getModuleList();
getAppList();
</script>
<style lang="less">
.module {
  width: 100%;
  height: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 10px;
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 22px;
    color: #131336;
    letter-spacing: 0.74px;
    font-weight: 500;
    margin-bottom: 15px;
  }
  &-table {
    margin-top: 20px;
    height: calc(100% - 130px);
  }
}
</style>
