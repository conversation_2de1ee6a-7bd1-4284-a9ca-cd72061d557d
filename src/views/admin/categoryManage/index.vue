<template>
  <div class="category">
    <div class="category-title">分类管理</div>
    <el-button type="primary" size="small" @click="dialogVisible = true">新增分类</el-button>
    <el-table :data="tableData" class="category-table" row-key="_id" default-expand-all>
      <el-table-column prop="name" label="分类名称" />
      <el-table-column prop="describe" label="分类描述" />
      <el-table-column prop="address" label="创建时间">
        <template #default="{ row }">
          {{ formatDate(row.createTime, "yyyy-MM-dd HH:mm:SS") }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑分类' : '添加分类'" width="400px" :before-close="handleClose">
      <el-form :model="form" label-width="120px" status-icon>
        <el-form-item label="分类名称">
          <el-input style="width: 200px" v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类层级">
          <el-cascader style="width: 200px" v-model="form.parentId" :props="levelProps" :options="tableData" clearable />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input style="width: 200px" v-model="form.describe" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { GetCategoryList, AddCategory, EditCategory } from "@/api/admin";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/date";
const tableData = ref([]);
const dialogVisible = ref(false);
const form = ref<any>({});
const levelProps = {
  checkStrictly: true,
  emitPath: false,
  value: "_id",
  label: "name"
};
const getCategoryList = async () => {
  try {
    const res = await GetCategoryList({});
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    tableData.value = res.data;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
const handleClose = () => {
  form.value = {};
  dialogVisible.value = false;
};
const handleSave = async () => {
  if (!form.value.name) {
    ElMessage.error("请输入分类名称");
    return;
  }
  try {
    const res = await (form.value.id ? EditCategory(form.value) : AddCategory(form.value));
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    handleClose();
    getCategoryList();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const handleEdit = (item) => {
  form.value = {
    name: item.name,
    parentId: item.parentId,
    describe: item.describe,
    id: item._id
  };
  dialogVisible.value = true;
};
getCategoryList();
</script>
<style lang="less">
.category {
  width: 100%;
  height: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 10px;
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 22px;
    color: #131336;
    letter-spacing: 0.74px;
    font-weight: 500;
    margin-bottom: 15px;
  }
  &-table {
    margin-top: 20px;
    height: calc(100% - 130px);
  }
}
</style>
