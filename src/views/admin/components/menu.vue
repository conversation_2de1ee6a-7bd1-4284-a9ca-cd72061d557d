<template>
  <div
    :class="{
      menu: true,
      'menu-nav-theme__active': store.themeShow
    }"
  >
    <div class="menu-nav__content">
      <div
        v-for="item in menuData"
        :key="item.path"
        :class="{
          'menu-nav-content__item': true,
          'menu-nav-content-item__active': item.path === currentPath
        }"
        @click="menuChange(item)"
      >
        <!-- 导航图标是否存在 <img :src="item.icon" alt="" /> -->
        <span class="sp-nav-content-item__span">{{ item.meta?.name }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { themeStore } from "@/store";
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { MENU_DATA } from "@/views/admin/config";
import { computed } from "vue";

const menuData = computed(() => MENU_DATA[0].children); // router
const store = themeStore();
const router = useRouter();
const route = useRoute();

const currentPath = ref<string>("/manage/dashboard");
const menuChange = (item: any) => {
  currentPath.value = item.path;
  router.push({
    path: item.path
  });
};

onMounted(() => {
  currentPath.value = route.path; // 用来防止用户刷新丢失选中状态
});
</script>
<style lang="less" scoped>
.menu {
  width: 250px;
  height: calc(100vh - 160px);
  background: #ffffff;
  padding: 0 20px;
  box-sizing: border-box;
  position: relative;
  &::before {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background-image: linear-gradient(to bottom, #ededed 50%, #ededed 50%);
  }
  .menu-nav__content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    scroll-snap-type: x proximity;
    &::-webkit-scrollbar {
      display: none;
    }
    .menu-nav-content__item {
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: left;
      font-size: 14px;
      color: #303233;
      padding: 0 11px;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      .menu-nav-content-item__span {
        flex: 1;
      }
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        border-radius: 50%;
        &.operation__btn {
          width: 3px;
        }
      }
      &.menu-nav-content-item__active {
        border-radius: 6px;
        background-color: #5c54f0;
        color: #ffffff;
      }
    }
  }
  &.menu-nav-theme__active {
    background-color: #26282b;
    &::before {
      background-image: linear-gradient(to bottom, #000000 50%, #000000 50%);
    }
    .menu-nav__content {
      .menu-nav-content__item {
        color: #ffffff;
      }
    }
  }
  .operation-span__container {
    width: 20px;
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: flex-end;
  }
}
</style>
