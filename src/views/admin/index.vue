<template>
  <div
    :class="{
      manage: true,
      'manage-theme__active': store.themeShow
    }"
  >
    <SpHeader>
      <SpLogin></SpLogin>
    </SpHeader>
    <div class="manage-body">
      <!-- 左侧导航部分内容 start -->
      <div class="manage-body__left">
        <!-- 菜单栏 start -->
        <Menu></Menu>
        <!-- 菜单栏 end -->
      </div>
      <!-- 左侧导航部分内容 end -->

      <!-- 右侧内容 start -->
      <div class="manage-body__right">
        <RouterView></RouterView>
      </div>
      <!-- 右侧内容 start -->
    </div>
  </div>
</template>
<script setup lang="ts">
import SpHeader from "@/views/layouts/components/spHeader.vue";
import SpLogin from "@/views/layouts/components/spLogin.vue";
import Menu from "@/views/admin/components/menu.vue";
import { themeStore } from "@/store";

const store = themeStore();
</script>
<style lang="less" scoped>
.manage {
  .manage-body {
    width: 100%;
    display: flex;
    flex-direction: row;
    .manage-body__left {
      width: 250px;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      background: #ffffff;
      .sp__nav {
        height: calc(100vh - 64px);
      }
    }
    .manage-body__right {
      width: 100%;
      position: relative;
      height: calc(100vh - 64px);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 20px;
      box-sizing: border-box;
    }
  }
  &.manage-theme__active {
    background: #26282b;
    .manage-body__left {
      background: #26282b;
    }
  }
}
</style>
