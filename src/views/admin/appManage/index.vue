<template>
  <div class="appManage">
    <div class="appManage-title">应用管理</div>
    <el-button type="primary" size="small" @click="dialogVisible = true">新增应用</el-button>
    <el-table :data="tableData" class="appManage-table">
      <el-table-column prop="name" label="应用名称" />
      <el-table-column label="所属分类">
        <template #default="{ row }">
          {{ row.categoryId ? row.categoryId.name : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="应用图标">
        <template #default="{ row }">
          <el-image class="appManage-icon" :src="row.icon" lazy />
        </template>
      </el-table-column>
      <el-table-column prop="lverCount" label="版本数" />
      <el-table-column label="创建人">
        <template #default="{ row }">
          {{ row.userId ? row.userId.name : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间">
        <template #default="{ row }">
          {{ formatDate(row.createTime, "yyyy-MM-dd HH:mm:SS") }}
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleVersion(row)">版本管理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination small background layout="prev, pager, next" @current-change="handleCurrentChange" hide-on-single-page :total="total" class="mt-4" />
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑应用' : '添加应用'" width="400px" :before-close="handleClose">
      <el-form :rules="rules" :model="form" ref="ruleFormRef" label-width="120px" status-icon>
        <el-form-item label="应用名称" prop="name">
          <el-input style="width: 200px" v-model="form.name" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="应用图标" prop="icon">
          <el-upload class="appManage-uploader" accept="image/*" action="/api/file/upload" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
            <img loading="lazy" v-if="form.icon" :src="form.icon" class="appManage-uploader-img" />
            <el-icon v-else class="appManage-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="应用类别" prop="categoryId">
          <el-cascader style="width: 200px" v-model="form.categoryId" :props="levelProps" :options="categoryList" clearable />
        </el-form-item>
        <el-form-item label="应用描述">
          <el-input style="width: 200px" v-model="form.describe" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleRule"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="versionVisible" title="版本管理" width="50%" :before-close="handleVersionClose">
      <el-form :model="form" status-icon>
        <el-form-item label="应用名称:">
          {{ form.name }}
        </el-form-item>

        <el-form-item label="版本管理:">
          <div class="appManage-version">
            <el-tag v-for="lver in versionList" :key="lver._id" class="mx-1">
              {{ lver.name }}
            </el-tag>
            <el-input v-if="inputVisible" ref="InputRef" v-model="form.lver_name" class="ml-1 w-20" size="small" @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
            <el-button v-else class="ml-1" size="small" @click="inputVisible = true"> + 新增版本 </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { GetAppList, GetCategoryList, AddApp, EditApp, GetLverList, AddLver } from "@/api/admin";
import { ElMessage } from "element-plus";
import type { FormRules, UploadProps } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { formatDate } from "@/utils/date";
const tableData = ref([]);
const categoryList = ref([]);
const versionList = ref<any>([]);
const dialogVisible = ref(false);
const versionVisible = ref(false);
const inputVisible = ref(false);
const total = ref(0);
const form = ref<any>({});
const ruleFormRef = ref();
const searchForm = ref({
  page: 1,
  pageSize: 10
});
const levelProps = {
  checkStrictly: true,
  emitPath: false,
  value: "_id",
  label: "name"
};
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
  icon: [
    {
      required: true,
      message: "请选择应用图标",
      trigger: "change"
    }
  ],
  categoryId: [
    {
      required: true,
      message: "请选择应用类别",
      trigger: "change"
    }
  ]
});
const getAppList = async () => {
  try {
    const res = await GetAppList(searchForm.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    tableData.value = res.data.list;
    total.value = res.data.total;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
const getVersionList = async () => {
  try {
    const res = await GetLverList({
      appId: form.value.id
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    versionList.value = res.data;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
const handleClose = () => {
  form.value = {};
  ruleFormRef.value.resetFields();
  dialogVisible.value = false;
};
const handleVersion = (item) => {
  form.value = {
    name: item.name,
    icon: item.icon,
    id: item._id
  };
  getVersionList();
  versionVisible.value = true;
};

const handleVersionClose = () => {
  form.value = {};
  inputVisible.value = false;
  versionList.value = [];
  versionVisible.value = false;
  getAppList();
};

const handleRule = () => {
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      handleSave();
    } else {
      console.log("error submit!", fields);
    }
  });
};
const handleSave = async () => {
  try {
    const res = await (form.value.id ? EditApp(form.value) : AddApp(form.value));
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    handleClose();
    getAppList();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
const handleEdit = (item) => {
  form.value = {
    name: item.name,
    categoryId: item.categoryId._id,
    describe: item.describe,
    icon: item.icon,
    id: item._id
  };
  dialogVisible.value = true;
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error("应用图标不能超过2MB");
    return false;
  }
  return true;
};
const handleAvatarSuccess: UploadProps["onSuccess"] = (response) => {
  form.value.icon = response.data.url;
};
const getCategoryList = async () => {
  try {
    const res = await GetCategoryList({});
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    categoryList.value = res.data;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const handleInputConfirm = async () => {
  if (!form.value.lver_name) {
    inputVisible.value = false;
    return;
  }
  try {
    const res = await AddLver({
      name: form.value.lver_name,
      appId: form.value.id
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    getVersionList();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
  inputVisible.value = false;
  form.value.lver_name = "";
};
const handleCurrentChange = (page = 1) => {
  searchForm.value.page = page;
  getAppList();
};
const init = () => {
  handleCurrentChange();
  getCategoryList();
};
init();
</script>
<style lang="less">
.appManage {
  width: 100%;
  min-height: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 10px;
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 22px;
    color: #131336;
    letter-spacing: 0.74px;
    font-weight: 500;
    margin-bottom: 15px;
  }
  &-table {
    margin-top: 20px;
    height: calc(100% - 130px);
  }
  &-icon {
    border-radius: 50%;
    width: 30px;
    height: 30px;
  }
  &-uploader {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    &-icon {
      font-size: 18px;
    }
    &-img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }
  &-version {
    display: flex;
    flex-wrap: wrap;
    .el-tag {
      margin: 5px;
    }
    .el-button {
      margin: 5px;
    }
  }
}
</style>
