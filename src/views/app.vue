<template>
  <div
    :class="{
      'sp-hl': store.themeShow
    }"
  >
    <RouterView v-if="isRouterAlive" />
  </div>
</template>

<script lang="ts" setup>
import { getUserInfo } from "@/api/login";
import { onMounted, ref, nextTick, provide, inject } from "vue";
import { themeStore, userInfoStore } from "@/store";
import { vscodeAdapter } from "@/utils/vscode-adapter";

const isRouterAlive = ref<boolean>(false);
const store = themeStore();
const userInfo = userInfoStore();

onMounted(async () => {
  try {
    // 在VSCode环境中，可能无法访问API，所以需要特殊处理
    if (vscodeAdapter.isVSCodeEnvironment()) {
      console.log('Running in VSCode environment, skipping user info API call');
      // 在VSCode环境中设置默认用户信息
      userInfo.updateInfo({
        syUserName: 'VSCode用户',
        syUid: 'vscode-user',
        ssoId: 'vscode-sso',
        type: 'vscode',
        syData: {},
        url: '',
        name: 'VSCode用户'
      });
      isRouterAlive.value = true;
      return;
    }

    // 非VSCode环境，正常调用API
    const res = await getUserInfo({});
    if (res.status == 200 && res.data.code == 0) {
      const payload = res.data.data;
      userInfo.updateInfo({
        syUserName: payload.syUserName,
        syUid: payload.syUid,
        ssoId: payload.ssoId,
        type: payload.type,
        syData: payload.syData,
        url: payload.url,
        name: payload.name
      });
    }
  } catch (error) {
    console.error('Failed to get user info:', error);
    // API调用失败时也要让应用继续运行
    if (vscodeAdapter.isVSCodeEnvironment()) {
      vscodeAdapter.showError('获取用户信息失败，但应用将继续运行');
    }
  } finally {
    isRouterAlive.value = true;
  }
});
// 页面刷新
let reload = () => {
  isRouterAlive.value = false;
  nextTick(() => {
    isRouterAlive.value = true;
  });
};

// provide
provide("reload", reload);
</script>
<style lang="less">
html {
  height: 100%;
}
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
#app {
  font-family: webfont-regular, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow: hidden;
  background: #f4f5fa;
}
</style>
