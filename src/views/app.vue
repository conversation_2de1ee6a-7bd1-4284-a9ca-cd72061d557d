<template>
  <div
    :class="{
      'sp-hl': store.themeShow
    }"
  >
    <RouterView v-if="isRouterAlive" />
  </div>
</template>

<script lang="ts" setup>
import { getUserInfo } from "@/api/login";
import { onMounted, ref, nextTick, provide } from "vue";
import { themeStore, userInfoStore } from "@/store";

const isRouterAlive = ref<boolean>(false);
const store = themeStore();
const userInfo = userInfoStore();

onMounted(async () => {
  const res = await getUserInfo({});
  if (res.status == 200 && res.data.code == 0) {
    const payload = res.data.data;
    userInfo.updateInfo({
      syUserName: payload.syUserName,
      syUid: payload.syUid,
      ssoId: payload.ssoId,
      type: payload.type,
      syData: payload.syData,
      url: payload.url,
      name: payload.name
    });
  }
  isRouterAlive.value = true;
});
// 页面刷新
let reload = () => {
  isRouterAlive.value = false;
  nextTick(() => {
    isRouterAlive.value = true;
  });
};

// provide
provide("reload", reload);
</script>
<style lang="less">
html {
  height: 100%;
}
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
#app {
  font-family: webfont-regular, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow: hidden;
  background: #f4f5fa;
}
</style>
