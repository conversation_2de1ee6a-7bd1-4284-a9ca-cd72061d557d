<template>
  <Header :team="team">
    <div class="team-select">
      <!-- <el-select v-model="teamId" @change="teamChange" style="width: 100%" placeholder="团队名称">
        <el-option :key="index" :label="item.name" :value="item._id" v-for="(item, index) in smbInfo.teamList"> </el-option>
      </el-select> -->

      <el-dropdown @command="handleCommand" trigger="click">
        <span class="team-dropdown-link">
          <div>{{ team?.name || "-" }}</div>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="team-dropdown-menu">
            <el-dropdown-item v-for="(item, i) in smbInfo.teamList" :command="i" :key="item._id">
              <div class="team-dropdown-item">
                <b> {{ item.name }}</b>
                <div v-if="item.user">团队负责人： {{ item.user.name }}</div>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <SearchSelect @teamChange="teamSelect" @folderChange="folderSelect" />
      <!-- <el-input class="team-select-search" v-model="search" placeholder="搜索" @change="handleSearch" /> -->
    </div>
  </Header>
  <div class="design-contaienr">
    <div class="design-left">
      <div class="left-1">
        <span><img loading="lazy" src="https://static.soyoung.com/sy-pre/2fd5qelwhffsm-1709277000663.png" alt="" />主页</span>
        <!--  只有owner 和 manage 才有邀请成员  -->
        <span v-if="curPermission == Permission.OWNER || curPermission == Permission.MANAGE" @click="inviteVisible = true"><img loading="lazy" src="https://static.soyoung.com/sy-pre/16f37iplysf6y-1709277000663.png" alt="" />邀请成员</span>
        <span v-if="curPermission == Permission.OWNER" @click="manageVisible = true"><img loading="lazy" src="https://static.soyoung.com/sy-pre/tuandui-1716949830949.png" alt="" />团队管理</span>
        <!-- <span v-if="curPermission == Permission.OWNER" @click="deleteTeam"><img src="https://static.soyoung.com/sy-pre/jiesan-1712913000722.png" alt="" />解散团队</span> -->
        <div class="left-module-1"></div>
      </div>
      <div class="line"></div>
      <TeamTree v-if="treeData.length" @openMove="projectMove" :permission="curPermission" :teamId="teamId" :treeList="treeData" @addFolder="addFolder" @refresh="refreshTeamTree" @nodeClick="nodeClick" @rename="reName" @share="projectShare" @del="delFile" @move="updateTree" />
    </div>
    <div class="design-right">
      <div class="rigth-top">
        <div v-if="curPermission !== Permission.PREVIEW" class="top-flag" @click="addFolder()">
          <div>
            <img src="https://static.soyoung.com/sy-pre/2tnf53vlkh34v-1713427800701.png" alt="" />
            <text>增加文件夹</text>
          </div>
          <img src="https://static.soyoung.com/sy-pre/2ajrwmwzd1l16-1709277000663.png" alt="" />
        </div>
        <div class="top-flag" @click="sketchDownLoad">
          <div>
            <img loading="lazy" src="https://static.soyoung.com/sy-pre/3bwjh10ho2qu6-1713427800701.png" alt="" />
            <text>sketch插件</text>
          </div>
          <img loading="lazy" src="https://static.soyoung.com/sy-pre/1tl6bvpqr8o1l-1709536200691.png" alt="" />
        </div>
      </div>

      <div class="right-content">
        <el-tabs v-model="activeName" @tab-remove="handleTabsRemove">
          <el-tab-pane v-for="(item, i) in fileList" :label="item.name" :name="item.id" :key="i" :closable="i !== 0">
            <div class="file-container" v-if="item.children && item.children.length">
              <div class="file-content" v-for="info in item.children" @click="nodeClick(info)" :key="`filelist-item-${info._id}`">
                <div class="file-top">
                  <img v-lazy="isImage(info)" loading="lazy" />
                </div>
                <div class="file-bottom">
                  <div class="file-bottom-left">
                    <img loading="lazy" v-if="info.thumb" src="https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png" alt="" />
                    <img v-else loading="lazy" src="https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png" />
                    <text>{{ info.name }}</text>
                  </div>
                  <div class="file-bottom-right" v-if="team!.permission !== Permission.PREVIEW">
                    <el-dropdown popper-class="tree-popper" trigger="click" :effect="'dark'" placement="bottom">
                      <el-button @click.stop type="text" style="margin-right: 10px; margin-top: 2px">
                        <span style="transform: rotate(90deg); user-select: none"><i class="iconfont icon-gengduo svg-icon"></i></span>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu class="header-new-drop">
                          <el-dropdown-item @click.stop="reName(info)">重命名</el-dropdown-item>
                          <el-dropdown-item @click.stop="delFile(info)">删除</el-dropdown-item>
                          <el-dropdown-item v-if="info.type === 'project'" @click.stop="projectShare(info)">复制链接</el-dropdown-item>
                          <el-dropdown-item v-if="info.type === 'project'" @click.stop="projectMove(info)">移动文件夹</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
            <el-empty description="暂无项目" v-else />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
  <el-dialog class="folder-dialog" v-model="addFolderInfo.visible" :beforeClose="folderClose" title="新建文件夹" align-center width="400px">
    <div v-if="addFolderInfo.parentId" class="folder-add-name">{{ addFolderInfo.getFullPath }} /</div>
    <div class="folder-add-item">
      <el-input placeholder="请输入文件夹名称" v-model="addFolderInfo.name"></el-input>
    </div>
    <template #footer>
      <center class="folder-add-footer">
        <el-button @click="folderClose">取消</el-button>
        <el-button type="primary" @click="folderSubmit"> 确定 </el-button>
      </center>
    </template>
  </el-dialog>
  <el-dialog class="move-dialog" v-model="moveInfo.visible" :beforeClose="closeMoveInfo" title="移动文件夹" align-center width="400px">
    <div v-if="moveInfo.data">
      <div class="folder-header">
        将<b style="color: #09c">{{ moveInfo.data.name }}</b> 移动到
      </div>
      <el-tree class="folder-tree" highlight-current :current-node-key="moveInfo.target" @node-click="handleMoveNodeClick" :data="folderList" node-key="_id" default-expand-all>
        <template #default="{ data }">
          <div class="folder-content">
            <img loading="lazy" class="node-icon" src="https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png" alt="" /><span class="node-name" style="max-width: 170px">{{ data.name }}</span>
          </div>
        </template>
      </el-tree>
    </div>
    <template #footer>
      <center class="folder-footer">
        <el-button @click="closeMoveInfo">取消</el-button>
        <el-button type="primary" @click="submitMoveInfo"> 确定 </el-button>
      </center>
    </template>
  </el-dialog>
  <RenameLog :visible="renameVisible" :item="renameInfo" @refresh="submitRename" @close="closeRename" />
  <InviteLogVue v-if="team" v-model:visible="inviteVisible" :team="team" @share="share" />
  <ManageLog v-if="team" :visible="manageVisible" @close="manageVisible = false" :team="team" @refresh="refresh" />
</template>
<script lang="ts" setup>
import { getTeamTreeList, addProjectFolder, updateProject, updateFolder } from "@/api/design";
import { ArrowDown } from "@element-plus/icons-vue";
import { ref, onMounted, computed, nextTick } from "vue";
import TeamTree from "./components/teamTree.vue";
import { useRouter, useRoute } from "vue-router";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import Header from "./components/header.vue";
import InviteLogVue from "./components/inviteLog.vue";
import SearchSelect from "./components/select.vue";
import ManageLog from "./components/manage.vue";
import RenameLog from "./components/rename.vue";
import { Permission } from "@/model";
import { smbStore } from "@/store";
import { ObjectAny } from "@/types";
import { invite, handleShare, findItem } from "./utils";

const route = useRoute();
const smbInfo = smbStore();
const router = useRouter();
const teamId = ref<string>();
const treeData = ref<any>([]);
const addFolderInfo = ref<any>({
  visible: false,
  name: "",
  parentId: undefined
});

const moveInfo = ref<ObjectAny>({});

const folderList = ref<ObjectAny[]>([]);

const filterFolder = (list: ObjectAny[], folderId: string = ""): ObjectAny[] => {
  return (list || []).reduce((acc: ObjectAny[], next) => {
    if (next.type === "project") {
      return acc;
    }
    if (folderId === next._id) {
      next.disabled = true;
    }
    acc.push(next);
    if (next.children?.length) {
      next.children = filterFolder(next.children);
    }
    return acc;
  }, [] as ObjectAny[]);
};

const team = computed(() => {
  return smbInfo.teamList.find((item) => item._id == teamId.value);
});

const curPermission = computed<Permission>(() => {
  return team.value?.permission;
});
const inviteVisible = ref<boolean>(false);
const manageVisible = ref<boolean>(false);
const fileList = ref<ObjectAny[]>([]);
const activeName = ref<number>(0);
const renameVisible = ref<boolean>(false);
const renameInfo = ref<ObjectAny>({});

onMounted(() => {
  initInvite();
  getTeam(route.query.teamId as string);
});

const initInvite = async () => {
  const { iv_id } = route.query;
  const teamId = await invite(iv_id as string);
  if (!teamId) {
    return;
  }
  getTeam(teamId);
};

const refresh = () => {
  getTeam(team.value!._id);
};

const getTeam = async (id?: string) => {
  await smbInfo.refreshTeamList();
  if (smbInfo.teamList) {
    if (id) {
      const team = smbInfo.teamList.find((item) => item._id == id);
      if (team) {
        teamId.value = team._id;
        teamChange();
        return;
      }
    }
    teamId.value = smbInfo.teamList[0]._id;
  }
  teamChange();
};

const nodeClick = async (clickNode) => {
  const { type, _id, name, children } = clickNode;
  if (type === "root") {
    await nextTick();
    activeName.value = 0;
    return;
  }
  if (type === "project") {
    router.push({
      path: "/item/project/stage",
      query: {
        projectId: _id,
        teamId: teamId.value
      }
    });
  } else {
    let index = fileList.value.findIndex((item) => item.id == _id);
    if (index === -1) {
      fileList.value.push({
        name,
        id: _id,
        children
      });
    }
    await nextTick();
    activeName.value = _id;
  }
};
const addFolder = ({ parentId, name = "", parentName = "" }: ObjectAny = {}) => {
  addFolderInfo.value = {
    parentId,
    name: name,
    getFullPath: parentName,
    visible: true
  };
};
const folderClose = () => {
  addFolderInfo.value = {};
};

const folderSubmit = async () => {
  const { name, parentId } = addFolderInfo.value;
  let res = await addProjectFolder({
    name,
    parentId,
    teamId: teamId.value
  });
  if (res.code == 0) {
    addFolderInfo.value = {};
    getTeamTree();
  }
};

const handleCommand = (command) => {
  const team = smbInfo.teamList[command];
  teamSelect(team._id);
};
const teamSelect = (id) => {
  teamId.value = id;
  teamChange();
};

const folderSelect = (folder) => {
  teamId.value = folder.teamId;
  router.replace({
    path: route.path,
    query: {
      teamId: teamId.value
    }
  });
  getTeamTree(folder.id);
};
const teamChange = () => {
  router.replace({
    path: route.path,
    query: {
      teamId: teamId.value
    }
  });
  getTeamTree();
};
const refreshTeamTree = () => {
  getTeamTree(null, true);
};
const getTeamTree = async (folderId?: string | null, force: boolean = false) => {
  const loading = ElLoading.service({
    fullscreen: true
  });
  const params: ObjectAny = {
    teamId: teamId.value
  };
  if (force) {
    params.force = 1;
  }
  try {
    const res = await getTeamTreeList(params);
    treeData.value = [
      {
        name: "团队文件",
        children: res.data,
        type: "root"
      }
    ];
    fileList.value = [
      {
        name: "全部项目",
        children: res.data,
        id: 0
      }
    ];
    if (folderId) {
      const folder = findItem(res.data, folderId, "_id");
      if (folder) {
        return nodeClick(folder);
      }
    }

    activeName.value = 0;
  } catch (e) {
  } finally {
    nextTick(() => {
      loading.close();
    });
  }
};
const handleTabsRemove = (id: any) => {
  const index = fileList.value.findIndex((item) => item.id === id);
  fileList.value.splice(index, 1);
  activeName.value = 0;
};
const isImage = (item) => {
  console.log(item);
  try {
    function findThumb(item: any, depth: number = 0) {
      if (depth > 3) return null;
      if (item?.thumb) return item;
      if (item?.children?.[0]) return findThumb(item.children[0], depth + 1);
      return null;
    }

    const itemThumb = findThumb(item);
    // const itemThumb = itemList.find((item) => item.thumb);

    if (itemThumb) {
      return itemThumb.thumb || "https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png";
    }

    if (item.type !== "project") {
      return "https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png";
    }

    return "https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png";
  } catch (e) {
    console.log("e", e);
    return "https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png";
  }
};
const sketchDownLoad = () => {
  window.open("https://static.soyoung.com/sy-pre/sy-gallery-sketch.sketchplugin-1742199000635.zip");
};
const share = (permission: Permission) => {
  handleShare(team.value!, permission, "/#/item/project/index?", [team.value!.name]);
};
const projectShare = (data: ObjectAny) => {
  handleShare(team.value!, Permission.PREVIEW, `/#/item/project/stage?projectId=${data._id}&`, [team.value!.name, data.name]);
};
const projectMove = (data: ObjectAny) => {
  folderList.value = filterFolder(JSON.parse(JSON.stringify(treeData.value)), data.folderId);
  moveInfo.value = {
    data,
    target: data.folderId,
    visible: true
  };
};
const closeMoveInfo = () => {
  moveInfo.value = {};
};
const handleMoveNodeClick = (clickNode: any) => {
  moveInfo.value.target = clickNode._id;
};
const submitMoveInfo = async () => {
  try {
    const params = {
      type: "project",
      id: moveInfo.value.data._id,
      folderId: moveInfo.value.target || null
    };
    await updateTree(params);
    closeMoveInfo();
  } catch (error) {
    ElMessage.error((error as any).message);
  }
};

const updateTree = async ({ type, ...query }: ObjectAny) => {
  const awaitFn = type === "project" ? updateProject : updateFolder;
  const res = await awaitFn(query);
  if (res.code !== 0) {
    throw new Error(res.msg);
  }
  refreshTeamTree();
};
const closeRename = () => {
  renameInfo.value = {};
  renameVisible.value = false;
};

const submitRename = () => {
  closeRename();
  getTeamTree();
};
const reName = (data) => {
  renameInfo.value = data;
  renameVisible.value = true;
};

/**
 * 删除文件
 */
const delFile = async (data: ObjectAny) => {
  await ElMessageBox.confirm(`确定要删除该${data.type === "project" ? "项目" : "文件夹"}吗`, "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  try {
    const awaitFn = data.type === "project" ? updateProject : updateFolder;
    const res = await awaitFn({
      id: data._id,
      isDeleted: 1
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    ElMessage.success("删除成功");
    getTeamTree();
  } catch (e) {
    ElMessage.error((e as any).message);
  }
};
</script>
<style lang="less">
.folder-dialog {
  .folder-add-name {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 700;
  }
  .el-dialog__footer {
    center {
      display: flex;
      justify-content: space-around;
      button {
        padding: 0 50px;
      }
    }
  }
}
.team-dropdown-menu {
  max-height: 80vh;
  overflow: overlay;
  .team-dropdown-item {
    width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    b {
      font-size: 15px;
    }
    div {
      font-size: 12px;
      color: #8e8e8e;
    }
  }
}
</style>
<style lang="less" scoped>
.design-contaienr {
  display: flex;
  height: calc(100vh - 48px);
}
.team-select {
  display: flex;
  align-items: center;
  .el-dropdown {
    cursor: pointer;
  }
  .team-dropdown-link {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    white-space: nowrap;
    display: flex;
    justify-content: space-between;
    max-width: 600px;
    div {
      width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.design-left {
  width: 300px;
  height: 100%;
  background: #fff;
  padding: 16px 10px 20px;
  box-shadow: 0 2px 1px 0 #dedede;
  border-top: 1px solid #f1f1f5;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  .left-1 {
    border-bottom: 1px solid #f1f1f5;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
    span {
      font-size: 14px;
      color: #262626;
      padding: 10px 5px;
      align-items: center;
      border-radius: 6px;
      display: inline-flex;
      &:hover {
        background: rgb(238, 238, 252);
        cursor: pointer;
        // cursor: not-allowed;
      }
      img {
        width: 15px;
        margin-right: 5px;
      }
    }
  }
  // .left-2 {
  //   margin-top: 20px;
  //   .left-module-2 {
  //     display: flex;
  //     justify-content: space-between;
  //     margin-bottom: 15px;
  //     line-height: 16px;
  //     span {
  //       img {
  //         width: 14px;
  //         margin-right: 2px;
  //         vertical-align: sub;
  //       }
  //     }
  //     span:first-child {
  //       font-family: PingFangSC-Medium;
  //       font-size: 16px;
  //       color: #303233;
  //       letter-spacing: 0;
  //       text-align: center;
  //       font-weight: 500;
  //     }
  //     span:last-child {
  //       font-size: 12px;
  //       cursor: pointer;
  //       color: #303233;
  //     }
  //   }
  // }
}
.design-contaienr {
  display: flex;
}
.design-right {
  margin-left: 30px;
  width: 100%;
  height: 100%;
  width: calc(100% - 330px);
  overflow: overlay;
  .rigth-top {
    display: flex;
    margin: 30px 0;
    .top-flag {
      margin-right: 20px;
      display: flex;
      padding: 18px 15px;
      background: #fefefe;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      align-items: center;
      cursor: pointer;
      width: 210px;
      height: 90px;
      box-sizing: border-box;
      justify-content: space-between;
      & > div {
        flex: 1;
        display: flex;
        align-items: center;
      }
      img:first-child {
        width: 45px;
        height: 45px;
      }
      text {
        margin-left: 10px;
        display: block;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #303233;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
      img:last-child {
        width: 20px;
        height: 20px;
      }
    }
  }
  .right-content {
    margin-top: 30px;
    ::v-deep .el-tabs__item {
      &.is-active {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #303233;
        letter-spacing: 0;
        font-weight: 500;
      }
    }
  }
  .file-container {
    display: flex;
    flex-wrap: wrap;
  }
  .file-content {
    background: #f7f7f7;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 23px;
    cursor: pointer;
    margin-bottom: 23px;
    &:hover {
      .file-bottom-right {
        opacity: 1;
      }
    }
    .file-top {
      width: 300px;
      height: 200px;
      display: flex;
      justify-content: center;
      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        align-self: center;
      }
    }
    .file-bottom {
      padding: 15px;
      display: flex;
      align-items: center;
      background: #fff;
      &-left {
        height: 100%;
        flex: 1;
        display: flex;
        align-items: center;
      }
      &-right {
        opacity: 0;
        transition: all 0.3s;
      }
      img {
        width: 20px;
        height: 20px;
      }
      text {
        margin-left: 5px;
        font-size: 14px;
      }
    }
  }
}
.invite-content {
  // width: 400px;
  font-size: 14px;
  color: #303233;
  b {
    color: #5c54f0;
  }
}
.move-dialog {
  ::v-deep {
    .el-dialog__body {
      padding: 10px !important;
    }
  }
  .folder-header {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  .folder-tree {
    .folder-content {
      display: flex;
      // width: calc(100% - 60px);
      align-items: center;
      .node-icon {
        display: block;
        width: 20px;
        height: 20px;
        align-self: center;
      }
      .node-name {
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #303233;
        font-weight: 500;
        display: block;
        // width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
