<template>
  <div class="web-list" @contextmenu="handleContextMenu">
    <div class="web-header-bar">
      <div class="web-header-logo">
        <img src="https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png" alt="logo" @click="goBack" />
      </div>
      <div class="bar-project-name-box">
        <template v-if="team">
          <div class="breadcrumb-button" @click="goBack">
            {{ team.name }}
            <!-- <i class="iconfont icon-zuojiantou" @click="goBack"></i> -->
          </div>
          <div class="breadcrumb-divider">
            <el-icon class="el-icon--right">
              <ArrowRight />
            </el-icon>
          </div>
        </template>
        <div class="project-menu-wrapper-box">
          <el-dropdown trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              {{ project?.name || "" }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu class="project-dropdown-menu">
                <el-dropdown-item v-for="info in projectList" :command="info._id" :key="info._id" :disabled="info._id === projectId">{{ info.name }} </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="bar-project-menu">
        <!--        <Notify />-->
        <!-- <Share v-if="team" :team="team" :userInfo="userInfo" :url="`/#/item/project/stage?projectId=${projectId}&`" :project="project" /> -->
        <el-button v-if="project" style="margin-right: 25px; width: 60px" type="primary" @click="share">分享</el-button>
        <Shortcut type="list" />
        <!-- <div class="bar-project-menu-item">
          <el-icon>
            <BellFilled />
          </el-icon>
        </div>
        <div class="bar-project-menu-item">
          <el-icon>
            <MoreFilled />
          </el-icon>
        </div>
        <div class="bar-project-menu-item">
          <el-button disabled size="small" type="primary">分享</el-button>
        </div> -->
      </div>
    </div>

    <div v-if="permission" class="web-list-section-wrap">
      <!-- 折叠的全部样式 start -->
      <div v-if="toggleShow" class="section-total-wrap" style="width: 142px; padding: 0 !important">
        <div class="hide-section" style="background: white" @click="handleToggleChange">
          <span class="total-name">全部</span>
          <div class="total-name__icon">
            <div class="show-exban-button">
              <i class="iconfont icon-jiantouzhankai"></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 折叠的全部样式 end -->

      <!-- 导航栏 start -->
      <div class="nav-tree-content nav-tree-content-show" v-else>
        <div class="section-total-wrap">
          <div class="section-total" @click="handleToggleChange">
            <span class="total-name">全部</span>
            <!-- <div class="moreSetButton" style="float: right">
              <el-dropdown trigger="click" placement="bottom-start">
                <div class="show-exban-button item-more" @click.stop="handleMoreChange">
                  <span style="transform: rotate(90deg)"><i class="iconfont icon-gengduo svg-icon"></i></span>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>下载图片</el-dropdown-item>
                    <el-dropdown-item>全选设计图</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div> -->
            <div class="total-num">{{ tree.total }}</div>
            <div class="total-name__icon">
              <div class="show-exban-button">
                <i class="iconfont icon-jiantoushouqi"></i>
              </div>
            </div>
          </div>
          <!-- 分割线 start -->
          <div class="section-line"></div>
          <!-- 分割线 end -->

          <!-- 新建分组 start -->
          <!-- <div class="section-total create-group-content create-group-disable" @click="openGroupDialog()">
            <i class="iconfont icon-jia"></i>
            <span class="create-group-content__sapn"> 新建分组 </span>
            <span class="key-icon"> <span class="key-icon-ctrl">Shift</span> + <span class="key-icon-code">N</span> </span>
          </div> -->
          <!-- 新建分组 end -->
        </div>
        <div class="section-group-list">
          <GroupTree :permission="permission" :tree-list="tree.list" @add="openGroupDialog" @nodeClick="nodeClick" @move="handleMove" @refresh="getTreeFn" />
        </div>
      </div>
      <!-- 导航栏 end -->
    </div>

    <!-- 设计图操作区 start -->
    <board v-if="permission" class="board" :team-id="teamId" :permission="permission" :renderData="boardList" :name="project?.name" v-model:loading="boardLoading" @handleBoardGroupClick="handleBoardGroupClick" />
    <div v-else class="board-placeholder">
      <el-empty description="暂无该项目权限，请联系项目管理员添加" />
    </div>
    <!-- <template v-if="permission">
      <board v-if="boardList.length" class="board" :team-id="teamId" :permission="permission" :renderData="boardList" :name="project?.name" v-model:loading="boardLoading" @handleBoardGroupClick="handleBoardGroupClick" />
      <div v-else class="board-placeholder">
        <el-empty description="暂无数据，请添加sketch文件" />
      </div>
    </template>
    <div v-else class="board-placeholder">
      <el-empty description="暂无该项目权限，请联系项目管理员添加" />
    </div> -->
    <!-- 设计图操作区 end -->
  </div>
  <el-dialog class="sketch-add" v-model="addGroupInfo.visible" title="新建分组" width="400px">
    <div v-if="addGroupInfo.parent" class="sketch-add-item">{{ getFullPath() }} /</div>
    <div class="sketch-add-item">
      <el-input placeholder="请输入分组名称" v-model="addGroupInfo.name"></el-input>
    </div>
    <template #footer>
      <span class="sketch-add-footer">
        <el-button @click="groupClose">取消</el-button>
        <el-button type="primary" @click="groupSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed, onUnmounted } from "vue";
import Shortcut from "./components/shortcut.vue";
import hotkeys from "hotkeys-js";
import board from "./components/board.vue";
import { ArrowDown, ArrowRight } from "@element-plus/icons-vue";
import { getProjectList, getTree, addGroup, sketchMove, groupMove } from "@/api/design";
import GroupTree from "./components/groupTree.vue";
import { ErrorCode, Permission } from "@/model";
import { ElMessage, ElMessageBox } from "element-plus";

import { Group, Project } from "./model/index";
import { useRouter, useRoute } from "vue-router";
import { ObjectAny } from "@/types";
import { useBoardStore } from "@/store/modules/board";
import Notify from "./components/notify.vue";
import { smbStore, userInfoStore } from "@/store";
import { invite, handleShare } from "./utils";

const router = useRouter();
const route = useRoute();
const toggleShow = ref<boolean>(false); // 是否展开
const projectId = ref<string>("");
const teamId = ref<string>("");
const smbInfo = smbStore();
const userInfo = userInfoStore();

const boardStore = useBoardStore();

const project = computed(() => {
  if (projectId.value) {
    return projectList.value.find((item) => {
      return item._id === projectId.value;
    });
  }
  return null;
});
const projectList = ref<Project[]>([]);
const tree = ref<{
  total?: number;
  list?: Group[];
}>({});
const boardLoading = ref<boolean>(true);
const boardList = ref<ObjectAny[]>([]);
const addGroupInfo = reactive<{ visible: boolean; name: string; parent: any }>({
  visible: false,
  name: "",
  parent: null
});
const share = () => {
  const breadcrumb = [team.value!.name];
  if (project.value) {
    breadcrumb.push(project.value.name);
  }
  handleShare(team.value!, Permission.PREVIEW, `/#/item/project/stage?projectId=${projectId.value}&`, breadcrumb);
};
const goBack = () => {
  router.replace({
    path: "/item/project/index",
    query: {
      teamId: teamId.value
    }
  });
};
const handleContextMenu = () => {
  // e.stopPropagation();
  // e.preventDefault();
};
const team = computed(() => {
  return smbInfo.teamList.find((item) => item._id == teamId.value);
});

const permission = computed<Permission | null>(() => {
  if (!team.value) {
    return null;
  }
  return team.value!.permission;
});
const handleToggleChange = () => {
  toggleShow.value = !toggleShow.value;
};

// 点击Icon收起&展开dom树
// const expandChange = () => {};

const getProjectListFn = async () => {
  try {
    const res = await getProjectList({
      teamId: route.query.teamId
    });
    if (res.code !== ErrorCode.OK) {
      throw res.msg;
    }
    projectList.value = res.data.reverse();
    if (res.data.length) {
      let info;
      if (route.query.projectId) {
        info = res.data.find((item) => {
          if (item._id === route.query.projectId) {
            return true;
          }
        });
      }
      handleCommand(info ? info._id : res.data[0]._id);
    }
  } catch (error: any) {
    ElMessage.error(error);
  }
};

const getTreeFn = async () => {
  try {
    boardLoading.value = true;
    const res = await getTree({
      projectId: projectId.value
    });
    if (res.code !== ErrorCode.OK) {
      throw res.msg;
    }

    tree.value = res.data;

    // 找到第一个type=sketch的分组

    const firstList = res.data.list.find((v) => {
      if (v.type === "sketch") {
        return true;
      }
      if (Array.isArray(v.subs) && v.subs.length > 0) {
        return v.subs.find((v1) => v1.type === "sketch");
      }
    });
    if (firstList) {
      boardList.value = getSketchList(firstList.subs);
    } else {
      boardList.value = [];
    }
  } catch (error: any) {
    ElMessage.error(error);
  }
};

const init = async () => {
  const query = route.query;
  if (query.iv_id) {
    delete query.teamId;
    const id = await invite(query.iv_id as string);
    if (!id) {
      return;
    }
    teamId.value = id;
    router.replace({
      path: route.path,
      query: {
        projectId: query.projectId,
        teamId: id
      }
    });
  } else {
    teamId.value = query.teamId as string;
  }
  await smbInfo.init();
  await getProjectListFn();
};

// 移动分组和 sketch
const handleMove = async (info: { parentId: string; id: string; type: "sketch" | "group" }) => {
  if (info.type === "sketch") {
    await moveSketch({
      id: info.id,
      groupId: info.parentId
    });
  } else {
    await moveGroup({
      id: info.id,
      parentId: info.parentId
    });
  }

  await getTreeFn();
};

const findNode = (list: any[], id: string) => {
  for (const item of list) {
    if (item._id === id) {
      return item;
    }
    if (Array.isArray(item.subs) && item.subs.length > 0) {
      const found = findNode(item.subs, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const nodeClick = async (node: ObjectAny) => {
  if (node.type === "sketch") {
    if (boardStore.hoverGroupInfo.groupId !== node.groupId) {
      if (tree.value.list) {
        let list = findNode(tree.value.list, node.groupId);
        if (list) {
          await handleGroupNodeClick(list);
        }
      }
    }

    boardStore.setHoverGroupInfo({
      id: node._id,
      groupId: node.groupId,
      source: "groupTree"
    });

    // handleSketchClick(node);
  } else {
    handleGroupNodeClick(node);
  }
};

const getSketchList = (arr: any[]) => {
  const sketchList: any[] = [];
  arr.forEach((v: any) => {
    if (v.type == "sketch") {
      sketchList.push(v);
    } else if (v.subs && v.subs.length) {
      sketchList.push(...getSketchList(v.subs));
    }
  });
  return sketchList;
};

const handleGroupNodeClick = async (node) => {
  boardLoading.value = true;
  boardList.value = getSketchList(node.subs);
  boardLoading.value = false;
};

// 移动sketch
const moveSketch = async (params: { id: string; groupId: string }) => {
  const res = await sketchMove(params);
  if (res.code != ErrorCode.OK) {
    ElMessage.error(res.msg);
  }
};
// 移动分组
const moveGroup = async (params: { id: string; parentId: string }) => {
  const res = await groupMove(params);
  if (res.code != ErrorCode.OK) {
    ElMessage.error(res.msg);
  }
};

const handleBoardGroupClick = (e) => {
  console.log("list-handleBoardGroupClick", e);
};

// 选择项目
const handleCommand = (command: string) => {
  if (projectId.value === command) {
    return;
  }
  router.replace({
    query: {
      ...route.query,
      projectId: command
    }
  });
  projectId.value = command;

  getTreeFn();
};
// 新建分组 关闭
const groupClose = () => {
  addGroupInfo.visible = false;
  addGroupInfo.parent = null;
  addGroupInfo.name = "";
};
// 分组的全路径
const getFullPath = () => {
  return addGroupInfo.parent?.fullPath.map(({ name }) => name).join(" / ");
};
// 新建分组
const groupSubmit = async () => {
  if (!addGroupInfo.name) {
    return;
  }
  const res = await addGroup({
    projectId: projectId.value,
    parentId: addGroupInfo.parent?._id,
    name: addGroupInfo.name
  });
  if (res.code === 0) {
    getTreeFn();
    groupClose();
  }
};
onMounted(() => {
  init();
  setHotKeys();
});

onUnmounted(() => {
  removeHotKeys();
});
const setHotKeys = () => {
  hotkeys("shift+n", () => openGroupDialog());
};
const removeHotKeys = () => {
  hotkeys.unbind("shift+n", () => openGroupDialog());
};

const openGroupDialog = (parent?: any) => {
  addGroupInfo.visible = true;
  addGroupInfo.parent = parent;
};
</script>
<style lang="less" scoped>
.web-list {
  width: 100%;
  position: relative;
  height: 100vh;

  .web-header-bar {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 406;
    height: 48px;
    background: white;
    width: 100%;
    line-height: 48x;
    font-size: 14px;
    transition: all 0.2s ease;
    transform: translateY(0);
    border-bottom: 1px solid #eeeff1;
    box-sizing: content-box;
    .web-header-logo {
      padding-left: 25px;
      cursor: pointer;
      padding-right: 10px;
      display: flex;
      align-items: center;
      img {
        width: 84px;
        height: 18px;
        display: inline-block;
      }
    }
    .bar-project-name-box {
      // width: 246px;
      padding-left: 10px;
      height: 48px;
      position: relative;
      display: flex;
      align-items: center;

      .breadcrumb-button {
        // width: 24px;
        // height: 24px;
        // display: flex;
        // align-items: center;
        // color: rgba(0, 0, 0, 0.87);
        // margin-left: 24px;
        // border-radius: 4px;
        cursor: pointer;
        line-height: 48px;
      }
      .breadcrumb-divider {
        padding: 0 10px;
        font-size: 14px;
      }
      .project-menu-wrapper-box {
        display: flex;
        align-items: center;
        // position: absolute;
        cursor: pointer;
        // margin-left: 71px;
        height: 100%;
        .el-dropdown {
          height: 100%;
        }
        .project-name {
          display: inline-block;
          width: auto;
          max-width: 160px;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-user-select: text;
          -moz-user-select: text;
          -ms-user-select: text;
          user-select: text;
          font-family: PingFang SC;
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 24px;
          color: #2f2e3f;

          .project-info-desc {
            max-width: 160px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
            pointer-events: none;
          }
        }

        .el-dropdown-link {
          display: flex;
          align-items: center;
        }

        .project-info-button {
          margin-left: 5px;
          display: flex;
        }
      }
    }

    .bar-project-menu {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      padding: 0 20px;
      box-sizing: border-box;

      .bar-project-menu-item {
        margin-right: 32px;
        display: flex;

        i {
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
  }

  .web-list-section-wrap {
    min-width: 264px;
    position: absolute;
    top: 64px;
    left: 24px;
    z-index: 405;
    pointer-events: auto;
    background: none;
    padding-top: 8px;
    box-sizing: border-box;
    border-radius: 4px;

    .hide-section {
      display: flex;
      align-items: center;
      width: 123px;
      height: 56px !important;
      border-radius: 10px !important;
      padding-left: 16px;
      cursor: pointer;
      box-shadow: 0 6px 18px 1px rgba(29, 41, 57, 0.14);

      .total-name {
        font-size: 14px;
        color: #303233;
        font-weight: 500;
      }

      .total-num {
        font-size: 13px;
        color: rgba(47, 46, 63, 0.3);
        letter-spacing: 0.6px;
        margin: 0 4px;
        width: 24px;
        height: 100%;
        justify-content: center;
        align-items: center;
        display: flex;
      }
    }

    .section-total-wrap {
      padding-top: 10px !important;
      padding: 0 8px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      // border-bottom: 1px solid #eeeff1;
    }

    .section-group-list {
      overflow: overlay;
      flex: 1;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .nav-tree-content {
      background: white;
      border-radius: 10px;
      -webkit-user-select: none;
      position: relative;
      max-height: calc(100vh - 90px);
      display: flex;
      flex-direction: column;

      .section-total {
        height: 36px;
        line-height: 36px;
        font-weight: 400;
        font-size: 14px;
        padding: 0 8px;
        border-radius: 4px;
        cursor: pointer;

        .total-name {
          float: left;
          font-style: normal;
          font-size: 14px;
          color: #303233;
          font-weight: 500;
        }

        .moreSetButton {
          float: right;
          display: flex !important;
          align-items: center;
          justify-content: center;
          height: 100%;

          .item-more {
            background: none;
            width: 24px;
            height: 24px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-size: 10px 10px !important;
          }
        }

        .total-num {
          float: right;
          margin: 0 4px;
          padding: 0 0;
          height: 24px;
          width: 24px;
          display: flex;
          justify-content: center;
          font-style: normal;
          font-weight: normal;
          font-size: 14px;
          color: #c0c2cc;
        }

        .total-name__icon {
          height: 100%;
          display: flex;
          align-items: center;
        }
      }

      .create-group-content {
        display: flex;
        align-items: center;
        font-size: 13px;
        font-weight: normal;
        color: #2f2e3f;

        .icon-jia {
          margin-right: 10px;
          margin-left: 6px;
          font-size: 12px;
        }

        &__span {
          font-size: 14px;
          color: #303233;
        }

        .key-icon {
          position: absolute;
          right: 16px;

          .key-icon-ctrl {
            margin-right: 3px;
          }

          .key-icon-code {
            margin-left: 3px;
          }

          span {
            font-size: 12px;
            color: rgba(31, 33, 38, 0.7);
            height: 18px;
            line-height: 18px;
            background: linear-gradient(360deg, #fafafa 0%, #ffffff 100%);
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #d1d5db;
            padding: 0 3px;
          }
        }
      }

      .create-group-disable {
        cursor: pointer;

        &:hover {
          background: #f5f7fa;
        }
      }

      .section-line {
        height: 1px;
        background: rgb(238, 239, 241);
        margin: 8px;
      }
    }

    .show-exban-button {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      position: relative;
      left: 4px;
      top: 0;

      .icon-gengduo {
        color: #575b66;
      }

      .icon-jiantouzhankai,
      .icon-jiantoushouqi {
        font-size: 6px;
        color: #909299;
      }
    }

    .nav-tree-content-show {
      padding-bottom: 8px;
      box-shadow: 0 6px 18px 1px rgba(29, 41, 57, 0.14);
    }

    .show-exban-button:hover {
      background: #e0e0e2;
    }

    .show-exban-button:active {
      background: #d5d5d9;
    }
  }

  .web-canvas-area::-webkit-scrollbar {
    /* WebKit */
    width: 0;
    height: 0;
  }

  .web-canvas-area {
    width: 100%;
    height: 100%;
    position: fixed;
    overflow: auto;
    box-sizing: border-box;
    top: 0;
    left: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;

    .canvas {
      box-sizing: border-box;
      // margin-left: -50%;
      // margin-top: -50%;
      border: 20px solid #e93030;
      position: relative;
    }

    .vue-draggable-resizable-class {
      box-sizing: border-box;

      &:hover {
        border: 1px solid #5c54f0;
        cursor: move;
      }

      img {
        width: 100%;
      }
    }

    .vue-draggable-resizable-active-class {
      border: 1px solid #5c54f0;
      cursor: move;
    }

    .ref-line {
      position: absolute;
      background-color: rgb(255 0 204);
      z-index: 9999;
    }

    .v-line {
      width: 1px;
    }

    .h-line {
      height: 1px;
    }

    .vdr {
      touch-action: none;
      position: absolute;
      box-sizing: border-box;
      border: 1px dashed #d6d6d6;
    }

    .handle {
      box-sizing: border-box;
      position: absolute;
      width: 8px;
      height: 8px;
      background: #fff;
      border: 1px solid #333;
      box-shadow: 0 0 2px #bbb;
    }

    .handle-tl {
      top: -5px;
      left: -5px;
      cursor: nw-resize;
    }

    .handle-tm {
      top: -5px;
      left: calc(50% - 4px);
      cursor: n-resize;
    }

    .handle-tr {
      top: -5px;
      right: -5px;
      cursor: ne-resize;
    }

    .handle-ml {
      top: calc(50% - 4px);
      left: -5px;
      cursor: w-resize;
    }

    .handle-mr {
      top: calc(50% - 4px);
      right: -5px;
      cursor: e-resize;
    }

    .handle-bl {
      bottom: -5px;
      left: -5px;
      cursor: sw-resize;
    }

    .handle-bm {
      bottom: -5px;
      left: calc(50% - 4px);
      cursor: s-resize;
    }

    .handle-br {
      bottom: -5px;
      right: -5px;
      cursor: se-resize;
    }
  }

  .pd_btm {
    padding-bottom: 8px;
  }
}

.el-popper.is-dark {
  background: rgba(97, 97, 97, 0.9) !important;
  color: #ffffff;
  border-radius: 4px;
  font-size: 12px;
  line-height: 22px;
  display: inline-block;
  padding: 5px 16px;
  position: absolute;
  text-transform: initial;
  width: auto;
  opacity: 1;
  pointer-events: none;
}

.el-popper__arrow {
  display: none;
}

.board {
  z-index: 300;
  &-placeholder {
    z-index: 300;
    width: 100vw;
    height: 100vh;
    padding: 50px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.project-dropdown-menu {
  max-height: 400px;
  overflow: overlay;
}
</style>
