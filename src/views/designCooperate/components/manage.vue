<template>
  <el-drawer :model-value="visible" title="团队管理" @close="handleClose" size="560px">
    <template v-if="permission == Permission.OWNER">
      <div class="team-manage-top">
        <div class="team-manage-name">
          <div v-if="!edit" class="team-manage-text">
            <div class="team-manage-text-label">{{ team.name }}</div>
            ({{ inviteeList.length }})
            <el-button type="primary" @click="edit = true" text>
              <el-icon><EditPen /></el-icon>
            </el-button>
          </div>
          <div v-else class="team-manage-input">
            <el-input style="width: 100%" v-model="name">
              <template #append>
                <el-button @click="teamRename" type="primary">确定</el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="team-manage-content">
        <el-table :data="inviteeList" empty-text="暂无成员">
          <el-table-column prop="invitee.name" label="用户">
            <template #default="{ row }">
              {{ row.invitee ? row.invitee.name : row.name }}
            </template>
          </el-table-column>
          <el-table-column label="权限" width="120">
            <template #default="{ row }">
              <div v-if="row.permission == Permission.OWNER">超级管理员</div>
              <el-dropdown v-else @command="(val) => handleCommand(val, row)">
                <span class="el-dropdown-link">
                  可{{ permissionName[row.permission] }}
                  <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="Permission.MANAGE">可管理</el-dropdown-item>
                    <el-dropdown-item :command="Permission.EDIT">可编辑</el-dropdown-item>
                    <el-dropdown-item :command="Permission.PREVIEW">可预览</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
          <el-table-column label="邀请时间" width="120">
            <template #default="{ row }">
              {{ row.createTime ? formatDate(row.createTime, "yyyy-MM-dd HH:mm:SS") : "-" }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90">
            <template #default="{ row }">
              <el-button v-if="row.permission !== Permission.OWNER" type="danger" @click="handleDelete(row._id)" text> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="team-manage-footer">
        <div class="team-manage-footer-l">
          <b>解散团队</b>
          <div>解散后,团队内所有内容都会被彻底删除不可恢复,请谨慎操作</div>
        </div>
        <el-button type="danger" @click="deleteTeam" plain>解散</el-button>
      </div>
    </template>
    <template v-else> </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { getMembers, updateTeam } from "@/api/design";
import { ErrorCode, permissionName, Permission } from "@/model";
import { ElMessage, ElMessageBox } from "element-plus";
import { ObjectAny } from "@/types";
import { EditPen, ArrowDown } from "@element-plus/icons-vue";
import { defineProps, defineEmits, watch, ref, computed } from "vue";
import { formatDate } from "@/utils/date";
import { deleteInvite, updateInvite } from "@/api/invite";
const props = defineProps<{
  visible: boolean;
  team: ObjectAny;
}>();

const inviteeList = ref<ObjectAny[]>([]);
const edit = ref<boolean>(false);
const emits = defineEmits(["close", "refresh"]);
const name = ref<string>("");
const permission = computed<Permission>(() => {
  return props.team.permission;
});
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      name.value = props.team.name || "";
      edit.value = false;
      init();
    }
  }
);
const init = async () => {
  const { code, msg, data } = await getMembers({
    teamId: props.team._id
  });
  if (code !== ErrorCode.OK) {
    throw msg;
  }
  const { list, owner } = data;
  inviteeList.value = [
    {
      name: owner.name,
      permission: Permission.OWNER
    },
    ...list
  ];
};
const handleClose = () => {
  emits("close");
};

const teamRename = async () => {
  const { code } = await updateTeam({
    name: name.value,
    id: props.team._id
  });
  if (code === ErrorCode.OK) {
    ElMessage.success("修改成功");
    emits("refresh");
    edit.value = false;
  }
};

const handleDelete = async (id) => {
  const { code } = await deleteInvite({
    id
  });
  if (code === ErrorCode.OK) {
    ElMessage.success("删除成功");
    init();
  }
};

const handleCommand = async (val, item: ObjectAny) => {
  if (item.permission === val) {
    return;
  }
  const { code } = await updateInvite({
    id: item._id,
    permission: val
  });
  if (code === ErrorCode.OK) {
    ElMessage.success("修改成功");
    init();
  }
};
const deleteTeam = async () => {
  await ElMessageBox.confirm(`确定要解散该团队吗？解散后团队下所有项目以及设计图均会被删除！`, "注意", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning"
  });
  const res = await updateTeam({ id: props.team._id, isDeleted: 1 });
  if (res.code == 0) {
    ElMessage.success("解散成功");
    emits("close");
    emits("refresh");
  }
};
</script>

<style lang="less" scoped>
.team-manage {
  &-top {
    display: flex;
    justify-content: space-between;
  }
  &-name {
    display: flex;
  }
  &-input {
    width: 360px;
  }
  &-text {
    font-size: 14px;
    display: flex;
    align-items: center;
    width: 360px;
    font-weight: 500;
    &-label {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  &-footer {
    display: flex;
    width: 500px;
    background: #f5f5f5;
    border-radius: 4px;
    align-items: center;
    margin: 40px auto 0;
    padding: 15px 20px;
    font-size: 14px;
    &-l {
      margin-right: 10px;
      div {
        margin-top: 10px;
      }
    }
  }
}
</style>
