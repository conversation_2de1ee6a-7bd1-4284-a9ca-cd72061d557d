<template>
  <el-button style="margin-right: 25px; width: 60px" type="primary" @click="share">分享</el-button>
</template>
<script lang="ts" setup>
import { Permission } from "@/model";
import { ObjectAny } from "@/types";
import { defineProps } from "vue";
import { handleShare } from "../utils";

const props = defineProps<{
  team: ObjectAny;
  url: string;
}>();
const share = () => {
  handleShare(props.team, Permission.PREVIEW, props.url, [props.team.name]);
};
</script>
