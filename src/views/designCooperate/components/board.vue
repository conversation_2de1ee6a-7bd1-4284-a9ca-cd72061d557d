<template>
  <div class="board">
    <div v-show="loading" class="board-loading" v-loading="loading"></div>
    <v-stage ref="stage" :config="configKonva" @dragend="onDragend($event)" @wheel="handleWheel($event)">
      <v-layer ref="boardLayer" :config="{}">
        <v-group v-for="[key, value] of imageDataMap" :key="`group-${key}`" :config="{
          x: value.position_x,
          y: value.position_y,
          draggable: groupCanDrag
        }" @dblclick="handleGroupBdClick(value._id)" @click="handleGroupClick(value._id, value.groupId)" @mouseenter="
          (e) =>
            handleGroupMouseEnter({
              id: value._id,
              e: e
            })
        " @mouseleave="
          (e) =>
            handleGroupMouseLeave({
              id: value._id,
              e: e
            })
        " @dragmove="
          (e) =>
            handleGroupDragmove({
              e,
              id: value._id
            })
        " @dragend="
          (e) =>
            handleGroupDragend({
              e,
              id: value._id
            })
        " @mousedown="(e) => handleGroupMouseDown({ e, id: value._id })">
          <v-image :config="{
            image: value.imageElement,
            width: value.width || 100,
            height: value.height || 100
          }" />
          <v-rect :config="{
            visible: value.borderVisible || false,
            width: value.width,
            height: value.height,
            stroke: hoverColor,
            strokeWidth: 4
          }" />
          <v-text :config="{
            x: 0,
            y: -40,
            text: value.name,
            fontSize: 24,
            fontFamily: 'Calibri',
            fill: value.borderVisible ? hoverColor : '#555',
            align: 'left'
          }"></v-text>
        </v-group>
        <v-line v-for="(line, index) in helperLines" :key="`helperLine-${index}`" :config="line" />
      </v-layer>
    </v-stage>

    <div class="board-toolbar">
      <div :class="{ 'bottom-toolbar__left': true, 'bottom-toolbar__left-disable': scaleNum < 10 }"
        @click="handleScaleChange(false)">
        <el-icon>
          <Minus />
        </el-icon>
      </div>
      <div class="bottom-toolbar__middle">{{ scaleNum }}%</div>
      <div :class="{ 'bottom-toolbar__right': true, 'bottom-toolbar__right-disable': scaleNum > 300 }"
        @click="handleScaleChange(true)">
        <el-icon>
          <Plus />
        </el-icon>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { defineProps, getCurrentInstance, watch, ref, defineEmits, onMounted, onUnmounted, computed, nextTick } from "vue";
import { Minus, Plus } from "@element-plus/icons-vue";
import { cloneDeep, debounce } from "lodash";
import { ObjectAny } from "@/types";
import { useRouter } from "vue-router";
import { useBoardStore } from "@/store/modules/board";
import { setSketchPosition } from "@/api/design";
import { ElMessage } from "element-plus";
import { Permission } from "@/model";
import hotkeys from "hotkeys-js";
const boardStore = useBoardStore();
const router = useRouter();

const props = defineProps<{
  renderData: any;
  loading: boolean;
  name?: string;
  teamId?: string;
  permission: Permission;
}>();

const emit = defineEmits(["update:loading"]);
const ins = getCurrentInstance();
const insProxy = ins!.proxy;
const stage = ref<any>(null);
const imageDataMap = ref(new Map());
const helperLines = ref<ObjectAny[]>([]);
const scaleNum = ref<any>(100);
const hoverColor = "#5c54f0";

const groupCanDrag = computed(() => {
  if (props.permission === Permission.PREVIEW) {
    return false;
  }
  return configKonva.value.draggable !== true;
});

const configKonva = ref<ObjectAny>({
  width: 0,
  height: 0,
  x: 0,
  y: 0,
  draggable: false
});


onMounted(() => {

  setHotKeys();
  window.addEventListener("keydown", spaceDown);
  window.addEventListener("keyup", spaceUp);
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("keydown", spaceDown);
  window.removeEventListener("keyup", spaceUp);
  window.removeEventListener("resize", handleResize);
});

const createPlaceholderImage = (sketch: any): Promise<HTMLImageElement> => {
  const canvas = document.createElement('canvas');
  canvas.width = sketch.width;
  canvas.height = sketch.height;
  const ctx = canvas.getContext('2d') as any;
  // 填充浅灰色背景
  ctx.fillStyle = '#F3F4F6';
  ctx.fillRect(0, 0, sketch.width, sketch.height);

  // 加载图片
  const img = new Image();
  img.crossOrigin = 'anonymous'; // 处理跨域问题
  img.src = 'https://static.soyoung.com/sy-pre/image-1741831800627.png';

  return new Promise((resolve) => {
    img.onload = () => {
      // 计算居中位置
      const x = (sketch.width - sketch.width / 3) / 2;
      const y = (sketch.height - sketch.width / 3) / 2;
      // 绘制30x30图片
      ctx.drawImage(img, x, y, sketch.width / 3, sketch.width / 3);
      const src = canvas.toDataURL('image/png');
      const image = new Image();
      image.src = src;
      // 转换为DataURL
      resolve(image);
    };
  });
}
const handleResize = () => {
  configKonva.value.width = window.innerWidth;
  configKonva.value.height = window.innerHeight;
};
const setHotKeys = () => {
  hotkeys("command-=", { splitKey: "-" }, (event) => {
    event.preventDefault();
    handleScaleChange(true);
  });
  hotkeys("command+-", (event) => {
    event.preventDefault();
    handleScaleChange(false);
  });
};
const spaceDown = (e) => {
  if (e.keyCode !== 32) {
    return;
  }
  configKonva.value.draggable = true;
};

const spaceUp = () => {
  configKonva.value.draggable = false;
};

const onDragend = (e) => {
  if (e.target.getClassName() !== "Stage") {
    return;
  }
  configKonva.value.x = e.target.x();
  configKonva.value.y = e.target.y();
};

// 增加鼠标滚轮滚动画布的拖拽效果
const handleWheel = (e) => {
  e.evt.preventDefault();
  let oldPos = {
    x: configKonva.value.x,
    y: configKonva.value.y
  };
  let newPos = {
    x: oldPos.x - e.evt.deltaX,
    y: oldPos.y - e.evt.deltaY
  };
  configKonva.value.x = newPos.x;
  configKonva.value.y = newPos.y;
};
const loadImage = (url: string, sketch: any) => {
  return new Promise<void>((resolve) => {
    const originalUrl = url;
    let modifiedUrl = url + '?imageView2/0/format/webp';

    const createImage = (src: string, isRetry = false) => {
      const img = new Image();
      img.src = src;

      img.onload = () => {
        sketch.imageElement = img;
        resolve();
      };
      img.onerror = () => {
        // 第一次重试时使用原始URL
        if (!isRetry && modifiedUrl !== originalUrl) {
          createImage(originalUrl, true);
        }
        // 加载默认图片
        else {
          console.log("load failed:", url);
          resolve();
        }
      };
    };
    createImage(modifiedUrl);
  });
};
const initKonva = async (sketchList: any[]) => {
  console.log("initKonva", sketchList);
  configKonva.value.width = document.body.clientWidth;
  configKonva.value.height = document.body.clientHeight;
  imageDataMap.value = new Map();

  if (!Array.isArray(sketchList) || sketchList.length == 0) {
    emit("update:loading", false);
    setTimeout(() => {
      ElMessage.error("没有发现可用数据");
    }, 200);
    return;
  }

  return eachSketchInfo(sketchList);
};

const eachSketchInfo = async (sketchList: any[]) => {
  let rect = {
    x1: 0,
    x2: 0,
    y1: 0,
    y2: 0
  };

  // 最左上角的那个图
  let rectLeftTopImage = sketchList[0];
  const tasks: any[] = [];
  await Promise.all(sketchList.map(async (sketch) => {
    if (!sketch.position_x) {
      sketch.position_x = sketch.x;
    }
    if (!sketch.position_y) {
      sketch.position_y = sketch.y;
    }
    let { x1, x2, y1, y2 } = rect;
    x1 = sketch.position_x > x1 ? x1 : sketch.position_x;
    x2 = sketch.position_x < x2 ? x2 : sketch.position_x;
    y1 = sketch.position_y > y1 ? y1 : sketch.position_y;
    y2 = sketch.position_y < y2 ? y2 : sketch.position_y;
    rect = {
      x1,
      x2,
      y1,
      y2
    };

    // 遍历所有的图，找到最左上角的图
    if (sketch.position_x < rectLeftTopImage.position_x && sketch.position_y < rectLeftTopImage.position_y) {
      rectLeftTopImage = sketch;
    }

    sketch.imageElement = await createPlaceholderImage(sketch);
    sketch.borderVisible = false;

    imageDataMap.value.set(sketch._id, sketch);
    tasks.push(loadImage(sketch.imagePath, sketch))
  }))

  nextTick().then(async () => {
    await Promise.all(tasks)
    imageDataMap.value = new Map(imageDataMap.value);
    // insProxy?.$forceUpdate();
  });

  // for (let [key, value] of imageDataMap.value) {
  //   // const image = await loadImage(value.imagePath, value);
  //   value.imageElement = (await loadImage(value.imagePath, value)) as any;
  // }

  const totalWidth = rect.x2 - rect.x1;
  const totalHeight = rect.y2 - rect.y1;
  const viewportWidth = document.body.clientWidth;
  const viewportHeight = document.body.clientHeight;

  const scaleX = viewportWidth / totalWidth;
  const scaleY = viewportHeight / totalHeight;

  let scale = Number(Math.min(scaleX, scaleY).toFixed(2));

  // todo
  // 初始化比例还需要优化，还要根据图大小来调整
  if (scale < 0.3) {
    scale = 0.3;
  }

  if (scale > 1) {
    scale = 0.8;
  }

  scaleNum.value = Number((scale * 100).toFixed(2));
  configKonva.value.scale = {
    x: scale,
    y: scale
  };
  console.log("左上角的图", rectLeftTopImage);

  const findItem = imageDataMap.value.get(boardStore.hoverGroupInfo.id);
  rectLeftTopImage = findItem || rectLeftTopImage;

  boardStore.setHoverGroupInfo({
    id: rectLeftTopImage._id,
    groupId: rectLeftTopImage.groupId,
    source: "konva"
  });

  handleMovePositionXYToScreenCenter(rectLeftTopImage);

  console.log("初始化konva", configKonva.value);
  emit("update:loading", false);
  // konvaRect.value = rect;
};

const handleMovePositionXYToScreenCenter = (sketch) => {
  const x = sketch.position_x || sketch.x;
  const y = sketch.position_y || sketch.y;

  console.log("handleMovePositionXYToScreenCenter", x, y);

  const viewportWidth = document.body.clientWidth;
  const viewportHeight = document.body.clientHeight;
  const scale = scaleNum.value / 100;
  let actualX = x * scale;
  let actualY = y * scale;

  // 计算将图像移动到屏幕中心所需的x和y坐标
  const centerX = viewportWidth / 2;
  const centerY = viewportHeight / 2;
  let _x = centerX - actualX;
  let _y = centerY - actualY;

  configKonva.value.x = _x;
  configKonva.value.y = _y;

  console.log("configKonva", configKonva.value);
};

watch(
  () => props.renderData,
  (newVal) => {
    if (Array.isArray(newVal)) {
      initKonva(cloneDeep(props.renderData));
    }
  },
  {
    deep: true
  }
);

watch(
  () => props.loading,
  (newVal) => {
    console.log("newVal-props.loading", newVal);
  },
  {
    immediate: true
  }
);

watch(
  () => boardStore.hoverGroupInfo,
  (newVal) => {
    if (newVal) {
      const image = imageDataMap.value.get(newVal.id);

      if (!image) {
        console.log("没有找到元素");
        return;
      }

      setImageBorderVisible(image._id, true);
      if (newVal.source === "groupTree") {
        handleMovePositionXYToScreenCenter(image);
      }
    }
  },
  { deep: true }
);

const handleScaleChange = (value: boolean) => {
  const step = 5;
  if (value) {
    if (scaleNum.value >= 300) {
      return;
    }
    scaleNum.value += step;
  } else {
    if (scaleNum.value <= 5) {
      return;
    }
    scaleNum.value -= step;
  }
  configKonva.value.scale = {
    x: scaleNum.value / 100,
    y: scaleNum.value / 100
  };
};

const setImageBorderVisible = (id: string, visible: boolean) => {
  const image = imageDataMap.value.get(id);
  if (image) {
    imageDataMap.value.forEach((_) => {
      _.borderVisible = false;
    });
    image.borderVisible = visible;
  }
};

const handleGroupClick = (id: string, groupId: string) => {
  const image = imageDataMap.value.get(id);
  if (!image) {
    return;
  }

  setImageBorderVisible(id, true);

  boardStore.setHoverGroupInfo({
    id: id,
    groupId: groupId,
    source: "konva"
  });
};

const handleGroupBdClick = (id: string) => {
  router.push({
    path: "/item/project/detail",
    query: {
      id: id,
      teamId: props.teamId
    }
  });
};

interface MouseEvent {
  id: string;
  e: any; // 这里你可以替换为具体的事件类型，例如MouseEvent
}

const handleChangeSketchPosition = debounce(({ id, x, y }) => {
  console.log("handleChangeSketchPosition", x, y);
  setSketchPosition({
    id,
    x,
    y
  });
}, 100);

const handleGroupMouseEnter = ({ id, e }: MouseEvent) => {
  // 检查id在不在imageDataMap的最后，如果不在就挪过去
  // 这样konvajs渲染才能在再高zIndex级
  if (imageDataMap.value.size > 0) {
    const lastId = Array.from(imageDataMap.value.keys()).pop();
    if (lastId !== id) {
      const lastItem = imageDataMap.value.get(lastId);
      const currentItem = imageDataMap.value.get(id);
      if (lastItem && currentItem) {
        imageDataMap.value.delete(id);
        imageDataMap.value.set(id, currentItem);
      }
    }
  }

  // const img = imageDataMap.value.get(id);
  // if (img) {
  //   setImageBorderVisible(id, true);
  // }
  e.evt.target.style.cursor = "pointer";
  helperLines.value = [];
};

const handleGroupMouseLeave = ({ id, e }: MouseEvent) => {
  // setImageBorderVisible(id, false);
  e.evt.target.style.cursor = "default";
  helperLines.value = [];
};

const drawHelperLine = (x1: number, y1: number, x2: number, y2: number) => {
  // console.log("drawHelperLine", x1, y1, x2, y2);
  helperLines.value.push({
    points: [x1, y1, x2, y2],
    stroke: "red",
    strokeWidth: 2
    // dash: [2, 2]
  });
};

const handleGroupDragmove = ({ id, e }: MouseEvent) => {
  const movePosition = e.target.getStage().getPointerPosition();

  if (!movePosition) {
    return;
  }

  helperLines.value = [];

  const movedX = e.target.attrs.x;
  const movedY = e.target.attrs.y;

  const currentGroup = imageDataMap.value.get(id);
  if (!currentGroup) return;

  currentGroup.position_x = movedX;
  currentGroup.position_y = movedY;
  // const item = { ...imageDataMap.value.get(id) };

  handleChangeSketchPosition({
    id,
    x: movedX,
    y: movedY
  });
  //
  //
  // currentGroupEl.zIndex(16)
  // currentGroupEl.moveToTop()
  // currentGroupEl.getLayer().draw()
  // console.log('currentGroupEl',currentGroupEl.zIndex(16))

  // todo 这里也涉及缩放，要不然画布缩放不一致的时候会有问题
  const minThreshold = 10;
  const collisionThreshold = 20;

  // todo
  // 遍历所有肯定不对。。。但是不知道咋空间换时间
  imageDataMap.value.forEach((group) => {
    // 碰撞检测
    // https://learnopengl-cn.github.io/06%20In%20Practice/2D-Game/05%20Collisions/02%20Collision%20detection/

    const isLeft = movedX < group.position_x;
    const isTop = movedY < group.position_y;

    const isCollisionX = isLeft ? movedX + currentGroup.width + collisionThreshold > group.position_x && movedX < group.position_x + group.width : movedX - collisionThreshold < group.position_x + group.width && movedX > group.position_x;
    const isCollisionY = isTop ? movedY + currentGroup.height + collisionThreshold > group.position_y && movedY < group.position_y + group.height : movedY - collisionThreshold < group.position_y + group.height && movedY > group.position_y;

    // 单独处理的定位坐标
    // let position_x = movedX;
    // let position_y = movedY;

    if (group._id !== id) {
      if (isCollisionX && isCollisionY) {
        // console.log("isCollisionX && isCollisionY");

        // todo 判断方向和距离，如果距离小于一定值，就吸附
        // 计算两个组的边界位置
        const currentGroupLeft = movedX;
        const currentGroupRight = movedX + currentGroup.width;
        const currentGroupTop = movedY;
        const currentGroupBottom = movedY + currentGroup.height;

        const groupLeft = group.position_x;
        const groupRight = group.position_x + group.width;
        const groupTop = group.position_y;
        const groupBottom = group.position_y + group.height;

        // 计算每个方向的25%区域
        const groupLeft25 = groupLeft + group.width * 0.75;
        const groupRight25 = groupRight - group.width * 0.75;
        const groupTop25 = groupTop + group.height * 0.75;
        const groupBottom25 = groupBottom - group.height * 0.75;

        const isLeft = movedX < group.position_x;
        console.log("isLeft", isLeft ? "左边" : "右边");

        console.log("movedX", movedX);
        console.log("currentGroupLeft - collisionThreshold < groupRight", currentGroupLeft - collisionThreshold < groupRight);

        // 判断碰撞方向
        const isCollisionLeft = currentGroupRight + collisionThreshold > groupLeft && currentGroupRight + collisionThreshold < groupLeft25;
        const isCollisionRight = currentGroupLeft - collisionThreshold < groupRight && currentGroupLeft - collisionThreshold > groupRight25;
        const isCollisionTop = currentGroupBottom + collisionThreshold > groupTop && currentGroupBottom + collisionThreshold < groupTop25;
        const isCollisionBottom = currentGroupTop - collisionThreshold < groupBottom && currentGroupTop - collisionThreshold > groupBottom25;

        console.log("isCollisionTop", isCollisionTop);
        console.log("isCollisionBottom", isCollisionBottom);
        console.log("isCollisionLeft", isCollisionLeft);
        console.log("isCollisionRight", isCollisionRight);

        let direction = "";
        if (isCollisionRight) direction = "right";
        else if (isCollisionLeft) direction = "left";
        else if (isCollisionBottom) direction = "bottom";
        else if (isCollisionTop) direction = "top";

        switch (direction) {
          case "right":
            currentGroup.position_x = group.position_x + group.width;
            e.target.attrs.x = currentGroup.position_x;
            // position_x = currentGroup.position_x;
            break;
          case "left":
            currentGroup.position_x = group.position_x - currentGroup.width;
            e.target.attrs.x = currentGroup.position_x;
            // position_x = currentGroup.position_x;
            break;
          case "bottom":
            currentGroup.position_y = group.position_y + group.height;
            e.target.attrs.y = currentGroup.position_y;
            // position_y = currentGroup.position_y;
            break;
          case "top":
            currentGroup.position_y = group.position_y - currentGroup.height;
            e.target.attrs.y = currentGroup.position_y;
            // position_y = currentGroup.position_y;
            break;
          default:
            break;
        }
      }

      // 画辅助线
      const isLeftAligned = Math.abs(movedX - group.position_x) < minThreshold;
      const isRightAligned = Math.abs(movedX + currentGroup.width - (group.position_x + group.width)) < minThreshold;
      const isTopAligned = Math.abs(movedY - group.position_y) <= minThreshold;
      const isBottomAligned = Math.abs(movedY + currentGroup.height - (group.position_y + group.height)) < minThreshold;

      const isCurrentLeftAndGroupRightAligned = Math.abs(movedX - (group.position_x + group.width)) < minThreshold;
      const isCurrentRightAndGroupLeftAligned = Math.abs(movedX + currentGroup.width - group.position_x) < minThreshold;
      const isCurrentTopAndGroupBottomAligned = Math.abs(movedY - (group.position_y + group.height)) < minThreshold;
      const isCurrentBottomAndGroupTopAligned = Math.abs(movedY + currentGroup.height - group.position_y) < minThreshold;

      // 如果currentGroup的左边和group的右边在相同Y的时候，画辅助线
      if (isCurrentLeftAndGroupRightAligned) {
        const y1 = isTop ? movedY : group.position_y;
        const y2 = isTop ? group.position_y + group.height : movedY;
        drawHelperLine(movedX, y1, movedX, y2);
      }

      if (isCurrentRightAndGroupLeftAligned) {
        const y1 = isTop ? movedY : group.position_y;
        const y2 = isTop ? group.position_y + group.height : movedY;
        drawHelperLine(movedX + currentGroup.width, y1, movedX + currentGroup.width, y2);
      }

      if (isCurrentTopAndGroupBottomAligned) {
        const x1 = isLeft ? movedX : group.position_x;
        const x2 = isLeft ? group.position_x + group.width : movedX;
        drawHelperLine(x1, movedY, x2, movedY);
      }

      if (isCurrentBottomAndGroupTopAligned) {
        const x1 = isLeft ? movedX : group.position_x;
        const x2 = isLeft ? group.position_x + group.width : movedX;
        drawHelperLine(x1, movedY + currentGroup.height, x2, movedY + currentGroup.height);
      }

      if (isTopAligned) {
        // console.log("isTopAligned");
        // console.log("movedX", movedX);
        // console.log("group.position_x", group.position_x);
        // console.log("isLeft", isLeft ? "左边" : "右边");
        const x1 = isLeft ? movedX : group.position_x;
        const x2 = isLeft ? group.position_x + group.width : movedX;

        drawHelperLine(x1, movedY, x2, movedY);
      }

      if (isBottomAligned) {
        // console.log("isBottomAligned");
        const x1 = isLeft ? movedX : group.position_x;
        const x2 = isLeft ? group.position_x + group.width : movedX;
        drawHelperLine(x1, movedY + currentGroup.height, x2, movedY + currentGroup.height);
      }

      if (isLeftAligned) {
        // console.log("isLeftAligned");
        const isTop = movedY < group.position_y;
        const y1 = isTop ? movedY : group.position_y;
        const y2 = isTop ? group.position_y + group.height : movedY;
        drawHelperLine(movedX, y1, movedX, y2);
      }

      if (isRightAligned) {
        // console.log("isRightAligned");
        const y1 = isTop ? movedY : group.position_y;
        const y2 = isTop ? group.position_y + group.height : movedY;
        drawHelperLine(movedX + currentGroup.width, y1, movedX + currentGroup.width, y2);
      }
    }
  });

  // 更新当前组的坐标
  // if (group._id == id && (group.position_x != position_x || group.position_y != position_y) && (!isCollisionX || !isCollisionY)) {
  //   group.position_x = position_x;
  //   group.position_y = position_y;
  //   return;
  // }
};
const handleGroupDragend = ({ id, e }: MouseEvent) => {
  console.log(id, e);
  helperLines.value = [];
};

const handleGroupMouseDown = ({ id, e }: MouseEvent) => {
  console.log(e, id);
};
</script>

<style scoped lang="less">
.board {
  width: 100vw;
  height: 100vh;
  position: relative;
  z-index: 300;

  &-loading {
    width: 100vw;
    height: 100vh;
  }

  &-toolbar {
    position: fixed;
    width: 170px;
    height: 40px;
    background: #ffffff;
    display: inline-flex;
    padding: 22px;
    box-shadow: 0 4px 8px 0 rgba(29, 41, 57, 0.1);
    border-radius: 6px;
    opacity: 1;
    border: 1px solid #e4e4e5;
    right: 24px;
    bottom: 24px;
    box-sizing: border-box;
    align-items: center;
    z-index: 12;

    .bottom-toolbar__left,
    .bottom-toolbar__right {
      cursor: pointer;

      &-disable {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }

    i {
      font-size: 12px;
      color: #909299;
    }

    .bottom-toolbar__middle {
      font-size: 14px;
      color: #303233;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}
</style>
