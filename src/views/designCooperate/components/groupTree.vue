<template>
  <el-tree draggable :allow-drop="allowDrop" class="group-tree" @node-drop="handleDrop" @node-click="handleNodeClick" :expand-on-click-node="false" :props="treeProps" :data="treeList" node-key="_id" default-expand-all>
    <template #default="{ data }">
      <div :id="`groupTree-${data._id}`" style="display: flex; flex: 1; height: 100%;">
        <div v-if="data.type !== 'sketch'" class="group-tree-node">
          <div>
            <i class="iconfont icon-a-sucaiku3x"></i> <span>{{ data.name }}</span>
          </div>
          <div v-if="permission !== Permission.PREVIEW" class="node-right-bar">
            <span class="node-count">{{ data.count || 0 }}</span>
            <div class="node-right-bar-handle" @click.stop>
              <el-dropdown trigger="click" placement="bottom-start">
                <el-button type="text" style="margin-right: 10px; margin-top: 2px">
                  <span style="transform: rotate(90deg)"><i class="iconfont icon-gengduo svg-icon"></i></span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.stop="renameGroup(data)">重命名</el-dropdown-item>

                    <el-dropdown-item @click.stop="deleteGroup(data)">删除分组</el-dropdown-item>
                    <!-- <el-dropdown-item>删除分组</el-dropdown-item> -->
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- <el-button @click="emit('add', data)" type="text">
                <el-icon><Plus /></el-icon>
              </el-button> -->
            </div>
          </div>
        </div>
        <div
          v-else
          class="sketch-tree-node"
          :class="{
            'sketch-tree-node--hover': data._id === boardStore?.hoverGroupInfo?.id
          }"
        >
          <div class="node-label">
            <el-icon class="iconfont"><Picture /></el-icon><span>{{ data.name }}</span>
          </div>
          <div v-if="permission !== Permission.PREVIEW" class="node-right-bar">
            <div class="node-right-bar-handle" @click.stop>
              <el-dropdown trigger="click" placement="bottom-start">
                <el-button type="text">
                  <span style="transform: rotate(90deg)"><i class="iconfont icon-gengduo svg-icon"></i></span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.stop="renameSketch(data)">重命名</el-dropdown-item>
                    <el-dropdown-item @click.stop="deleteSketch(data)">删除</el-dropdown-item>

                    <!-- <el-dropdown-item>移动分组</el-dropdown-item> -->
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-tree>
</template>
<script lang="ts" setup>
import type Node from "element-plus/es/components/tree/src/model/node";
import type { NodeDropType } from "element-plus/es/components/tree/src/tree.type";
import { deleteSketchById, deleteGroupById, updateGroup, updateSketch } from "@/api/design";

import { Permission } from "@/model";
import { Picture } from "@element-plus/icons-vue";
import { defineProps } from "vue";
import { ErrorCode } from "@/model";
import { useBoardStore } from "@/store/modules/board";
import { ElMessage, ElMessageBox } from "element-plus";
const boardStore = useBoardStore();
import { watch } from "vue";
import { ObjectAny } from "@/types";
defineProps<{
  treeList?: any[];
  permission: Permission;
}>();

const emit = defineEmits(["add", "move", "nodeClick", "refresh"]);
const treeProps = {
  children: "subs"
};

const handleDrop = (draggingNode: Node, dropNode: Node, dropType: NodeDropType) => {
  console.log("tree drop:", dropNode.label, dropType);
  const params = {
    id: draggingNode.data._id,
    parentId: dropNode.data._id,
    type: draggingNode.data.type === "sketch" ? "sketch" : "group"
  };
  emit("move", params);
};
const allowDrop = (draggingNode: Node, dropNode: Node) => {
  if (dropNode.data.type === "sketch") {
    return false;
  }
  if (draggingNode.data.groupId == dropNode.data._id) {
    return false;
  }
  return true;
};
const handleNodeClick = (clickNode: any) => {
  console.log("nodeClick:", clickNode);

  if ((!clickNode.type || clickNode.type !== "sketch") && (!Array.isArray(clickNode.subs) || clickNode.subs.length === 0)) {
    ElMessage.warning("该分组下没有素材");
    return;
  }
  emit("nodeClick", clickNode);
};

const deleteGroup = async (info: ObjectAny) => {
  if (info.subs.length) {
    return ElMessage.warning("该分组下有子项目，请先删除子项目");
  }
  await ElMessageBox.confirm(`确定删除“${info.name}”分组吗？`, "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  try {
    const res = await deleteGroupById({
      id: info._id
    });
    if (res.code !== ErrorCode.OK) {
      throw res.msg;
    }
    ElMessage.success("删除成功");
    emit("refresh");
  } catch (error: any) {
    ElMessage.error(error);
  }
  console.log(info.subs);
};
const renameGroup = async (data: ObjectAny) => {
  const input = await ElMessageBox.prompt("请输入分组名称", "重命名", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    inputValue: data.name
  });
  if (!input.value) {
    ElMessage({
      type: "error",
      message: "名字不能为空"
    });
    return;
  }
  const res = await updateGroup({
    name: input.value,
    id: data._id
  });
  if (res.code === 0) {
    ElMessage({
      type: "success",
      message: "重命名成功"
    });
    emit("refresh");
  } else {
    ElMessage({
      type: "error",
      message: "重命名失败"
    });
  }
};

const deleteSketch = async (info: ObjectAny) => {
  await ElMessageBox.confirm(`确定删除“${info.name}”素材吗？`, "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  try {
    const res = await deleteSketchById({
      id: info._id
    });
    if (res.code !== ErrorCode.OK) {
      throw res.msg;
    }
    ElMessage.success("删除成功");
    emit("refresh");
  } catch (error: any) {
    ElMessage.error(error);
  }
};
const renameSketch = async (data: ObjectAny) => {
  const input = await ElMessageBox.prompt("请输入设计图名称", "重命名", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    inputValue: data.name
  });
  if (!input.value) {
    ElMessage({
      type: "error",
      message: "名字不能为空"
    });
    return;
  }
  const res = await updateSketch({
    name: input.value,
    id: data._id
  });
  if (res.code === 0) {
    ElMessage({
      type: "success",
      message: "重命名成功"
    });
    emit("refresh");
  } else {
    ElMessage({
      type: "error",
      message: "重命名失败"
    });
  }
};

watch(
  () => boardStore.hoverGroupInfo,
  (newVal) => {
    if (newVal) {
      // console.log(newVal);
      const ref = document.getElementById(`groupTree-${newVal.id}`);
      if (ref) {
        ref.scrollIntoView({
          behavior: "smooth",
          block: "center"
        });
      }
    }
  },
  { deep: true }
);
</script>
<style lang="less" scoped>
.group-tree {
  padding: 0 12px;
  ::v-deep {
    .el-tree-node {
      display: flex;

      flex-direction: column;
    }
    .el-tree-node__content {
      display: flex;

      height: 40px;
      flex-direction: row;
      justify-content: flex-start;
    }
  }

  .group-tree-node,
  .sketch-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    .iconfont {
      font-size: 14px;
      margin-right: 5px;
    }

    &:hover {
      .node-right-bar-handle {
        opacity: 1;
      }
    }
    .node-label {
      flex: 0 0 160px;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #333333;
      line-height: 20px;
      height: 20px;
      .el-icon {
        margin-top: 3px;
      }
      span {
        vertical-align: text-bottom;
      }
    }

    .node-right-bar {
      display: flex;
      align-items: center;
      .node-count {
        font-size: 12px;
        color: #8e8e8e;
      }
      &-handle {
        opacity: 0;
        transition: all 0.3s;
        margin-left: 10px;
      }
    }
    &--hover {
      color: #5c54f0;
      background-color: #f5f5f5;
      ::v-deep {
        .node-label {
          color: #5c54f0;
        }
        .node-right-bar-handle {
          opacity: 1;
        }
      }
    }
  }
  .sketch-tree-node {
    width: 200px;
  }
}
</style>
