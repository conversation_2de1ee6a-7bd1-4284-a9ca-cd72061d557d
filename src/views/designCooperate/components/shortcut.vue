<template>
  <div class="shortcut-div">
    <el-dropdown trigger="click">
      <span class="shortcut-div-link"> <i class="sy-gicon-kuaijiejian" style="margin-right: 10px"></i>快捷键 </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item>
            <div class="shortcut-item">
              <span>移动</span> <span><i class="iconfont sy-gicon-kongge"></i> + <i class="iconfont sy-gicon-mouseM"></i> </span>
            </div>
          </el-dropdown-item>
          <el-dropdown-item>
            <div class="shortcut-item">
              <span>放大</span>
              <span><i class="iconfont sy-gicon-vuesax-linear-command-square"></i> + <i class="iconfont sy-gicon-plus_app"></i> </span>
            </div>
          </el-dropdown-item>
          <el-dropdown-item>
            <div class="shortcut-item">
              <span>缩小</span>
              <span><i class="iconfont sy-gicon-vuesax-linear-command-square"></i> + <i class="iconfont sy-gicon-jian1"></i> </span>
            </div>
          </el-dropdown-item>
          <template v-if="type === 'detail'">
            <el-dropdown-item>
              <div class="shortcut-item">
                <span>搜索</span>
                <span><i class="iconfont sy-gicon-vuesax-linear-command-square"></i> + <i class="iconfont">F</i></span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div class="shortcut-item">
                <span>切片</span>
                <span><i class="iconfont sy-gicon-option"></i> + <i class="iconfont">S</i></span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div class="shortcut-item">
                <span>颜色</span>
                <span><i class="iconfont sy-gicon-option"></i> + <i class="iconfont">C</i></span>
              </div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div class="shortcut-item">
                <span>历史</span>
                <span><i class="iconfont sy-gicon-option"></i> + <i class="iconfont">h</i></span>
              </div>
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script lang="ts" setup>
import { defineProps } from "vue";

defineProps<{
  type: "list" | "detail";
}>();
</script>
<style lang="less" scoped>
.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 120px;
  .iconfont {
    font-size: 12px;
  }
}
.shortcut-div {
  height: 100%;

  .el-dropdown {
    height: 100%;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
  }
}
</style>
