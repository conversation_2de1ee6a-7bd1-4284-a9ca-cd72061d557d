<template>
  <el-dialog class="folder-dialog" :model-value="visible" :beforeClose="close" title="重命名" align-center width="400px">
    <div class="folder-add-item">
      <el-input v-model="name"></el-input>
    </div>
    <template #footer>
      <center class="folder-add-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" v-loading="loading" @click="rename"> 确定 </el-button>
      </center>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { updateFolder, updateProject } from "@/api/design";
import { ObjectAny } from "@/types";
import { ElMessage } from "element-plus";
import { defineProps, watch, ref, defineEmits } from "vue";
const props = defineProps<{
  item: ObjectAny;
  visible: boolean;
}>();
const loading = ref<boolean>(false);
const name = ref<string>("");
watch(
  () => props.item,
  (item) => {
    name.value = item.name || "";
  }
);

const emit = defineEmits(["close", "refresh"]);
const close = () => {
  emit("close");
};
const rename = async () => {
  try {
    loading.value = true;
    const awaitFn = props.item.type === "project" ? updateProject : updateFolder;
    const res = await awaitFn({
      id: props.item._id,
      name: name.value
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    loading.value = false;
    ElMessage.success("修改成功");
    emit("refresh");
  } catch (e) {
    ElMessage.error((e as any).message);
  }
};
</script>
