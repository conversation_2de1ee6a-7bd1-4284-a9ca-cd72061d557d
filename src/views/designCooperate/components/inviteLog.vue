<template>
  <el-dialog width="550" class="invite-log" title="邀请成员" align-center :model-value="visible" :before-close="() => $emit('update:visible', false)">
    <div class="invite-header">邀请加入此团队，并为其分配权限</div>
    <div class="invite-item">
      <div class="invite-item-title">通过链接邀请</div>
      <el-select v-model="linkPermission" style="width: 400px">
        <el-option v-for="(label, key) in typeMap" :key="key" :value="key" :label="`通过此链接加入以上项目的人  ${label}`">
          <div class="invite-item-select">
            <span>通过此链接加入以上项目的人</span>
            <b>{{ label }}</b>
          </div>
        </el-option>
      </el-select>
      <el-button style="margin-left: 10px" @click="createInviteLink" type="primary">复制链接</el-button>
      <div class="invite-item-tips">链接有效期为 <b>7</b> 天</div>
    </div>
    <!-- <div class="invite-item">
      <div class="invite-item-title">通过uid邀请</div>
      <div class="invite-item-select">
        <el-select placeholder="请输入新氧uid" style="width: 400px" v-model="uid" remote @change="addInviteed" filterable :remote-method="searchUser" :loading="searchLoading">
          <el-option v-for="user in userOptions" :key="user._id" :value="user.syUid">
            <div class="invite-item-avatar">
              <el-avatar size="small" :src="user.syData.avatar" />
              <span class="invite-item-user">
                {{ user.name }}
              </span>
            </div>
          </el-option>
        </el-select>
        <el-select v-model="formSearch.permission" placeholder="Select" style="margin-left: 10px; width: 100px">
          <el-option v-for="(label, key) in typeMap" :key="key" :value="key" :label="label"> </el-option>
        </el-select>
      </div>

      <ul v-if="inviteSyUids.length" class="invite-item-users">
        <li v-for="(uid, i) in inviteSyUids" :key="uid">
          <div class="invite-item-avatar">
            <el-avatar v-if="userMap[uid].syData.avatar" loading="lazy" :src="userMap[uid].syData.avatar" />
            <span class="invite-item-user">
              {{ userMap[uid].name }}
            </span>
          </div>
          <div class="invite-item-type">
            <el-icon @click="handleRemoveUser(i)" class="invite-item-close"><CircleCloseFilled /></el-icon>
          </div>
        </li>
        <div class="invite-item-btn">
          <el-button type="primary" :loading="inviteLoading" @click="handleInviteUser">邀请</el-button>
        </div>
      </ul>
    </div> -->
  </el-dialog>
</template>
<script lang="ts" setup>
import { Permission } from "@/model";
import { ObjectAny } from "@/types";
// import { ElMessage } from "element-plus";
import { defineProps, reactive, ref } from "vue";
// import { CircleCloseFilled } from "@element-plus/icons-vue";
// import { getUserInfoByUid } from "@/api/login";
// import { addInvite } from "@/api/invite";
 defineProps<{
  visible: boolean;
  team: ObjectAny;
}>();
const emit = defineEmits(["update:visible", "share"]);

const linkPermission = ref<Permission>(Permission.PREVIEW);
const typeMap = {
  [Permission.PREVIEW]: "可查看",
  [Permission.EDIT]: "可编辑",
  [Permission.MANAGE]: "可管理"
};
// const searchLoading = ref(false);
// const inviteLoading = ref(false);
// const uid = ref<string>("");
// const userMap = ref<ObjectAny>({});
// const userOptions = ref<ObjectAny[]>([]);
// const inviteSyUids = ref<string[]>([]);
// const formSearch = reactive<InviteBase>({
//   permission: Permission.PREVIEW,
//   invites: [],
//   category: InviteCategory.SMB
// });

// const addInviteed = () => {
//   const _uid = uid.value.trim();
//   if (!_uid) {
//     return;
//   }
//   const item = inviteSyUids.value.includes(_uid);
//   if (item) {
//     return;
//   }
//   inviteSyUids.value.push(_uid);
//   uid.value = "";
// };
// const searchUser = async (query: string) => {
//   const _uid = query.trim();
//   if (!_uid) {
//     userOptions.value = [];
//     return;
//   }
//   searchLoading.value = true;
//   if (!userMap.value[_uid]) {
//     const res = await getUserInfoByUid({
//       uid: _uid
//     });
//     if (res.code !== ErrorCode.OK || !res.data) {
//       userOptions.value = [];
//       searchLoading.value = false;
//       return;
//     }
//     userMap.value[_uid] = res.data;
//   }
//   userOptions.value = Object.values(userMap.value).filter((item) => item.syUid == _uid);
//   searchLoading.value = false;
// };

// const handleRemoveUser = (index) => {
//   inviteSyUids.value.splice(index, 1);
// };

const createInviteLink = () => {
  emit("share", linkPermission.value);
};
// const handleInviteUser = async () => {
//   if (!inviteSyUids.value.length) {
//     ElMessage.error("请选择邀请用户");
//     return;
//   }
//   inviteLoading.value = true;
//   const params = {
//     ...formSearch,
//     resourceId: props.team?._id,
//     invites: inviteSyUids.value.map((uid) => userMap.value[uid]._id)
//   };
//   const res = await addInvite(params);
//   if (res.code == 0) {
//     ElMessage.success("邀请成功");
//   }
//   inviteLoading.value = false;
//   inviteSyUids.value = [];
//   emit("update:visible", false);
// };
</script>
<style lang="less">
.invite-log {
  .el-dialog__body {
    padding: 0 20px 20px;
  }
}
</style>
<style lang="less" scoped>
.invite-header {
  padding: 10px;
  background: #8580f2;
  color: #fff;
  border-radius: 5px;
  margin-bottom: 10px;
}
.invite-item {
  margin-bottom: 10px;
  &-tips {
    font-size: 12px;
    margin-top: 10px;
    b {
      color: #5c54f0;
    }
  }
  &-users {
    padding: 10px;
    background: #f3f3f3;
    margin-top: 10px;
    border-radius: 5px;
    li {
      margin: 10px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  &-avatar {
    display: flex;
    align-items: center;
    height: 30px;
    .invite-item-user {
      margin-left: 10px;
      font-weight: 500;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: 300px;
    }
  }
  &-title {
    padding: 8px 10px;
    font-size: 14px;
    font-weight: 700;
    font-family: PingFangSC-Medium;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 12px;
      top: 50%;
      margin-top: -6px;
      left: 0;
      border-radius: 3px;
      background: #5c54f0;
    }
  }
  &-select {
    display: flex;
    justify-content: space-between;
  }
  &-type {
    display: flex;
    align-items: center;
  }
  .invite-type-select {
    ::v-deep {
      .el-select__wrapper {
        box-shadow: none !important;
      }
    }
  }
  &-close {
    font-size: 22px;
    margin-left: 10px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
  }
  &-btn {
    display: flex;
    justify-content: end;
  }
}
</style>
