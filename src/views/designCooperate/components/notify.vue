<template>
  <el-popover @before-enter="notifyTab = NotifyCategory.TEAM" @after-leave="notifyTab = ''" width="350px" placement="bottom" trigger="click">
    <template #reference>
      <!-- <el-badge class="notify-badge" :value="stat.total.unread" :hidden="!stat.total.unread"> -->
      <div class="notify-bar">
        <div v-if="stat.total.unread" class="notify-dot">
          {{ stat.total.unread > 99 ? 99 : stat.total.unread }}
        </div>
        <img class="notify-icon" src="https://static.soyoung.com/sy-pre/3mu6fz0tbgoki-1717146600634.png" alt="" />
      </div>
      <!-- </el-badge> -->
    </template>
    <div class="notify-box" :loading="notifyLoading">
      <el-tabs v-model="notifyTab" @tab-click="handleNotifyClick">
        <el-tab-pane :name="NotifyCategory.TEAM">
          <template #label>
            <el-badge is-dot :hidden="!stat.team.unread" class="item"> 团队 </el-badge>
          </template>
          <MsgList v-if="notifyTab === NotifyCategory.TEAM" :categroy="NotifyCategory.TEAM" @refresh="getNotifyStat" />
        </el-tab-pane>
        <el-tab-pane label="项目" :name="NotifyCategory.PROJECT">
          <template #label>
            <el-badge is-dot :hidden="!stat.project.unread" class="item"> 项目 </el-badge>
          </template>
          <MsgList v-if="notifyTab === NotifyCategory.PROJECT" :categroy="NotifyCategory.PROJECT" @refresh="getNotifyStat" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { NotifyCategory } from "@/model";
import { ObjectAny } from "@/types";
import { ref, onMounted } from "vue";
import { getStat } from "@/api/notify";
import MsgList from "./msg.vue";

const notifyTab = ref<NotifyCategory | "">("");
const notifyLoading = ref<boolean>(false);
const stat = ref<ObjectAny>({
  total: {},
  team: {},
  project: {}
});

const getNotifyStat = async () => {
  notifyLoading.value = true;

  const res = await getStat({});
  if (res.code == 0) {
    stat.value = res.data;
  }
  notifyLoading.value = false;
};
const handleNotifyClick = () => {};
onMounted(() => {
  getNotifyStat();
});
</script>
<style lang="less" scoped>
.notify-icon {
  width: 18px;
  height: 18px;
  cursor: pointer;
}
.notify-bar {
  position: relative;
  width: 25px;
  margin-right: 25px;
  height: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  .notify-dot {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f00;
    text-align: center;
    color: #fff;
    transform: scale(0.7);
    line-height: 20px;
    padding: 2px;
  }
}
.notify-box {
  width: 100%;
}
</style>
