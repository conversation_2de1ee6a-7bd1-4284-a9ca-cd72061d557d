<template>
  <!-- <el-button v-click-outside="onClickOutside"> Click me </el-button>

  <el-popover ref="popoverRef" :virtual-ref="inputRef" trigger="click" title="With title" virtual-triggering>
    <span> Some content </span>
  </el-popover> -->

  <el-popover width="300px" placement="bottom-start" :trigger="manual" v-model:visible="popoverVisible">
    <!-- Popover 的内容 -->
    <div class="select-content" v-loading="loading">
      <el-tree default-expand-all style="max-width: 600px" highlight-current check-strictly :expand-on-click-node="false" :data="treeData" :props="props" :height="400" @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <img loading="lazy" class="select-icon" :src="imageMaps[data.type]" alt="" />
          <span>{{ node.label }}</span>
        </template>
      </el-tree>
    </div>
    <!-- reference 插槽：包裹 el-input -->
    <template #reference>
      <el-input class="select-search" ref="inputRef" placeholder="请输入关键词，按Enter键搜索" v-model="search" @change.stop @keydown.stop @keyup.stop="handleEnter"></el-input>
    </template>
  </el-popover>
</template>
<script lang="ts" setup>
import { searchInfo } from "@/api/design";
import { ref, defineEmits } from "vue";
import { useRouter } from "vue-router";
interface SearchResultNode {
  id: string;
  type: "SmbTeam" | "SmbFolder" | "SmbGroup" | "SmbProject" | "Sketch";
  name: string;
  projectId?: string;
  teamId?: string;
  children?: SearchResultNode[];
}

const emit = defineEmits(["teamChange", "folderChange"]);
// const props = defineProps<{}>();
const search = ref<string>("");
const manual = ref<any>("manual");

const router = useRouter();
const popoverVisible = ref<boolean>(false);
const loading = ref<boolean>(false);

const treeData = ref<any[]>([]);

const props = {
  value: "id",
  label: "name",
  children: "children"
};
const imageMaps = {
  SmbTeam: "https://static.soyoung.com/sy-pre/a-tuandui5-1735020600626.png",
  SmbFolder: "https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png",
  SmbGroup: "https://static.soyoung.com/sy-pre/fenzuguanli-1735020600626.png",
  SmbProject: "https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png",
  Sketch: "https://static.soyoung.com/sy-pre/sketch-1735020600626.png"
};
const handleSearch = async () => {
  loading.value = true;
  const res = await searchInfo({
    keywords: search.value
  });
  if (res.code === 0) {
    treeData.value = res.data;
  }
  loading.value = false;
};
const handleEnter = (e) => {
  if (e.code !== "Enter") {
    return;
  }
  if (!search.value.trim()) {
    popoverVisible.value = false;
    return;
  }
  popoverVisible.value = true;
  handleSearch();
};

const resetSearch = () => {
  search.value = "";
  popoverVisible.value = false;
  treeData.value = [] as any[];
};

const handleNodeClick = (data: any) => {
  switch (data.type) {
    case "SmbTeam":
      emit("teamChange", data.id);
      break;
    case "SmbFolder":
      emit("folderChange", data);
      break;
    case "SmbProject":
      router.push({
        path: "/item/project/stage",
        query: {
          projectId: data.id,
          teamId: data.teamId
        }
      });
      break;
    case "SmbGroup":
      router.push({
        path: "/item/project/stage",
        query: {
          projectId: data.projectId,
          teamId: data.teamId,
          groupId: data.id
        }
      });
      break;
    case "Sketch":
      router.push({
        path: "/item/project/detail",
        query: {
          id: data.id,
          teamId: data.teamId
        }
      });
      break;
  }

  resetSearch();
};
</script>
<style lang="less" scoped>
.select-search {
  margin-left: 20px;
  width: 300px;
}
.select-content {
  height: 400px;
  overflow: overlay;
  .select-icon {
    width: 15px;
    height: 15px;
    margin-right: 10px;
  }
}
</style>
