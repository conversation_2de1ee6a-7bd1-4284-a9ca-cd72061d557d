<template>
  <div class="design-header">
    <div class="header-logo">
      <img src="https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png" alt="logo" @click="goHome" />
      <!-- <span class="line"></span> -->
      <!-- <span class="name">设计协作</span> -->
    </div>
    <el-divider direction="vertical" />
    <div class="header-content">
      <slot />
    </div>
    <div class="header-user">
      <div class="share-btn">
        <!--        <Notify />-->
        <Share v-if="team" :team="team" url="/#/item/project/index?" />
        <!-- <el-button style="margin-left: 33px" type="primary" @click="$emit('share')">分享</el-button> -->
      </div>
      <div class="home__userInfo" v-if="userInfo.ssoId">
        <el-dropdown :popper-class="'home-user__dropdown'" :hide-on-click="false">
          <div>
            <!-- <img loading="lazy" class="home__avatar" src="https://static.soyoung.com/sy-pre/<EMAIL>" alt="" /> -->
            <span v-if="userInfo.name" class="home__avatar">{{ userInfo.name.slice(-2) }}</span>
            <span class="home__username">{{ userInfo.name }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <el-dropdown-item @click="handleRename">
                <el-icon><EditPen /></el-icon>
                <span>重命名</span>
              </el-dropdown-item> -->
              <el-dropdown-item @click="handleLogout">
                <i class="iconfont icon-tuichudenglu"></i>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <!-- <div class="home__user--unlogin" v-else>
        <el-button @click="ssoLogin" type="primary">新氧登录</el-button>
      </div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setUserLogout, rename, getUserInfo } from "@/api/login";
import { useRouter } from "vue-router";
import { themeStore, userInfoStore } from "@/store";
import { onMounted, defineProps } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ObjectAny } from "@/types";
import { EditPen } from "@element-plus/icons-vue";
import Share from "./share.vue";
import Notify from "./notify.vue";
defineProps<{
  team?: ObjectAny;
}>();
const store = themeStore();
const userInfo = userInfoStore();
const router = useRouter();
// 退出登录
const handleLogout = async () => {
  await setUserLogout();
  userInfo.clearInfo();
  ElMessage({
    type: "success",
    message: "退出成功"
  });
};

const handleRename = async () => {
  const input = await ElMessageBox.prompt("请输入您的名字", "重命名", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    inputValue: userInfo.name
  });
  if (!input.value) {
    ElMessage({
      type: "error",
      message: "名字不能为空"
    });
    return;
  }
  const res = await rename({
    name: input.value
  });
  if (res.code === 0) {
    ElMessage({
      type: "success",
      message: "重命名成功"
    });
    refreshUserInfo();
  } else {
    ElMessage({
      type: "error",
      message: "重命名失败"
    });
  }
};

const refreshUserInfo = async () => {
  const res = await getUserInfo({});
  if (res.status == 200 && res.data.code == 0) {
    const payload = res.data.data;
    userInfo.updateInfo({
      syUserName: payload.syUserName,
      syUid: payload.syUid,
      ssoId: payload.ssoId,
      type: payload.type,
      syData: payload.syData,
      url: payload.url,
      name: payload.name
    });
  }
};
// // 登陆
// const ssoLogin = async () => {
//   window.location.href = "/api/user/login?return_url=" + encodeURIComponent(window.location.href);
// };
// 首页
const goHome = () => {
  router.push({
    path: "/"
  });
};

onMounted(() => {
  const element = document.getElementsByTagName("html")[0];
  element.setAttribute("class", "light");
  store.updateThemeValue(false);
});
</script>
<style lang="less" scoped>
.design-header {
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  align-items: center;
  background: #ffffff;
  .header-content {
    flex: 1;
    padding-left: 10px;
  }
  .header-logo {
    padding-left: 25px;
    cursor: pointer;
    padding-right: 10px;
    img {
      width: 84px;
      height: 18px;
      display: inline-block;
    }
    .line {
      opacity: 0.2;
      background: #5c54f0;
      border-radius: 1px;
      display: inline-block;
      margin: 0 10px;
      height: 18px;
      width: 1px;
    }
    .name {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      color: #303233;
      letter-spacing: 0;
      text-align: justify;
      font-weight: 400;
      display: inline-block;
      line-height: 18px;
      vertical-align: top;
    }
  }

  .header-user {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    .share-btn {
      height: 100%;
      display: inline-flex;
      align-items: center;
    }
    .home__userInfo {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-right: 20px;
      // padding: 22px 0;
      cursor: pointer;
    }
    .home__user--unlogin {
      margin-right: 20px;
    }
    .home__avatar {
      width: 30px;
      height: 30px;
      background-color: #5c54f0;
      font-size: 12px;
      font-weight: 600;
      color: #fff;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      border-radius: 50%;
      margin-right: 10px;
      cursor: pointer;
      vertical-align: middle;
    }
    .home__username {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #303233;
      letter-spacing: 0;
      font-weight: 400;
      line-height: 20px;
      vertical-align: middle;
      display: inline-block;
      max-width: 180px;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &.home-user__active {
      .home__username {
        color: #ffffff;
      }
    }
  }
}
</style>
