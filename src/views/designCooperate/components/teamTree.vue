<template>
  <div class="team-tree">
    <el-tree draggable class="group-tree" :allow-drop="allowDrop" @node-drop="handleDrop" @node-click="handleNodeClick" :expand-on-click-node="false" :props="treeProps" :data="treeList" node-key="_id" default-expand-all>
      <template #default="{ data }">
        <div v-if="data.type !== 'sketch'" class="group-tree-node">
          <div class="node-root" v-if="data.type === 'root'">
            {{ data.name }}
          </div>
          <div class="node-content" v-else-if="data.type == 'project'">
            <img loading="lazy" class="node-icon" src="https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png" alt="" /><span class="node-name" style="max-width: 150px">{{ data.name }}</span>
          </div>
          <div class="node-content" v-else>
            <img loading="lazy" class="node-icon" src="https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png" alt="" /><span class="node-name" style="max-width: 170px">{{ data.name }}</span>
          </div>
          <div v-if="permission !== Permission.PREVIEW" class="node-right-bar">
            <div class="node-right-bar-handle">
              <el-dropdown v-if="data.type !== 'root'" popper-class="tree-popper" trigger="click" :effect="'dark'" placement="bottom">
                <el-button @click.stop type="text" style="margin-right: 10px; margin-top: 2px">
                  <span style="transform: rotate(90deg); user-select: none"><i class="iconfont icon-gengduo svg-icon"></i></span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="header-new-drop">
                    <el-dropdown-item @click.stop="$emit('rename', data)">重命名</el-dropdown-item>
                    <el-dropdown-item @click.stop="$emit('del', data)">删除</el-dropdown-item>
                    <el-dropdown-item v-if="data.type === 'project'" @click.stop="$emit('share', data)">复制链接</el-dropdown-item>
                    <el-dropdown-item v-if="data.type === 'project'" @click.stop="$emit('openMove', data)">移动文件夹</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-dropdown v-if="data.type !== 'project'" popper-class="tree-popper" trigger="click" :effect="'dark'" placement="bottom">
                <el-button @click.stop type="text">
                  <el-icon><Plus /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="header-new-drop">
                    <el-dropdown-item @click.stop="addFolder(data)">新增文件夹</el-dropdown-item>
                    <el-dropdown-item @click.stop="openProjectDialog(data)">新增项目</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
  <el-dialog class="folder-dialog" v-model="projectInfo.visible" :beforeClose="closeProject" title="新增项目" align-center width="400px">
    <div class="folder-add-item">
      <el-input v-model="projectInfo.name"></el-input>
    </div>
    <template #footer>
      <center class="folder-add-footer">
        <el-button @click="closeProject">取消</el-button>
        <el-button type="primary" v-loading="loading" @click="submitProject"> 确定 </el-button>
      </center>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import type Node from "element-plus/es/components/tree/src/model/node";
import { defineProps, defineEmits, ref } from "vue";
import { ElMessage } from "element-plus";
import { addProject } from "@/api/design";
import { Plus } from "@element-plus/icons-vue";

import { ObjectAny } from "@/types";
import { Permission } from "@/model";

const props = defineProps<{
  treeList?: any[];
  permission: Permission;
  teamId?: string;
}>();

const emit = defineEmits(["addFolder", "nodeClick", "refresh", "rename", "del", "share", "move", "openMove"]);
const treeProps = {
  children: "children",
  disabled: "disabled"
};
const loading = ref<boolean>(false);
const projectInfo = ref<ObjectAny>({});
const allowDrop = (draggingNode: Node, dropNode: Node, dropType: string) => {
  if (dropType !== "inner") {
    return false;
  }
  if (dropNode.data.type === "project") {
    return false;
  }
  return true;
};
const handleDrop = (draggingNode: Node, dropNode: Node) => {
  const targetNode = dropNode.data;
  const sourceNode = draggingNode.data;
  const params: ObjectAny = {
    type: sourceNode.type,
    id: sourceNode._id
  };
  if (sourceNode.type === "project") {
    params.folderId = targetNode._id || null;
  } else {
    params.parentId = targetNode._id || null;
  }
  emit("move", params);
};

const addFolder = (data: any) => {
  emit("addFolder", {
    parentId: data._id,
    parentName: data.name
  });
};
const openProjectDialog = (data: ObjectAny) => {
  projectInfo.value = {
    name: "",
    folderId: data._id,
    visible: true
  };
};
const submitProject = async () => {
  const { folderId, name } = projectInfo.value;
  if (!name.trim()) {
    return;
  }
  const res = await addProject({
    folderId,
    teamId: props.teamId,
    name
  });
  if (res.code === 0) {
    ElMessage.success("添加成功");
    closeProject();
    emit("refresh");
  }
};
const closeProject = () => {
  projectInfo.value = {};
};
const handleNodeClick = (clickNode: Node) => {
  emit("nodeClick", clickNode);
};
</script>

<style lang="less" scoped>
.header-new-drop {
  opacity: 0.8;
  background: #000000;
  /deep/li {
    color: #ffffff;
    &:focus {
      background: #000000;
      color: #ffffff;
    }
  }
}
.team-tree {
  flex: 1;
  position: relative;
}
.group-tree {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: overlay;
  // padding: 0 12px;
  ::v-deep {
    .el-tree-node__content {
      height: 35px;
      box-sizing: border-box;
    }
    > .el-tree-node {
      > .el-tree-node__content {
        > .el-icon {
          display: none !important;
        }
      }
    }
  }
  .group-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 30px);
    height: 100%;
    .iconfont {
      font-size: 12px;
      margin-right: 5px;
    }
    &:hover {
      .node-right-bar-handle {
        // display: block;
        visibility: visible;
        position: relative;
      }
    }
    .node-root {
      font-size: 16px;
      color: #303233;
      font-family: PingFangSC-Medium;
      font-weight: 500;
    }
    .node-content {
      display: flex;
      // width: calc(100% - 60px);
      align-items: center;
      .node-icon {
        display: block;
        width: 15px;
        height: 15px;
        align-self: center;
      }
      .node-name {
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #303233;
        font-weight: 400;
        display: block;
        // width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .node-label {
      flex: 0 0 160px;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .node-right-bar {
      display: flex;
      align-items: center;
      .node-count {
        font-size: 12px;
        color: #8e8e8e;
      }
      &-handle {
        // display: none;
        visibility: hidden;
        margin-left: 10px;
      }
    }
  }
}

.el-poppe.tree-popper .el-popper__arrow::before {
  opacity: 0.8;
  background: #000000 !important;
}
</style>
