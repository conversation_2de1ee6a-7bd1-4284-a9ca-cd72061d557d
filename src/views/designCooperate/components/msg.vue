<template>
  <div class="msg_list" v-infinite-scroll="loadMore" :infinite-scroll-distance="10">
    <ul v-if="list.length">
      <li v-for="item in list" :key="item._id" @click="changeStatus(item)" class="msg_item">
        <!-- <img :src="iconMaps[item.action]" alt="" /> -->
        <div class="msg_icon">
          <i :class="[categroy == NotifyCategory.TEAM ? 'sy-gicon-file-' : 'sy-gicon-zhaopian']"></i>
        </div>
        <div v-if="!item.readStatus" class="msg_dot"></div>
        <div
          class="msg_info"
          :class="{
            msg_info__read: item.readStatus
          }"
        >
          <div class="msg_content" v-html="item.content"></div>
          <div class="msg_date">{{ formatDate(item.createTime, "yyyy-MM-dd") }}</div>
        </div>
      </li>
    </ul>
    <div class="msg_placeholder" v-else>暂无通知</div>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, onMounted, reactive, ref, defineEmits } from "vue";
import { NotifyCategory } from "@/model";
import { ObjectAny } from "@/types";
import { getMsgList, updateMsg } from "@/api/notify";
import { formatDate } from "@/utils/date";

const props = defineProps<{
  categroy: NotifyCategory;
}>();

const emit = defineEmits(["refresh"]);
const loading = ref<boolean>(false);
const list = ref<ObjectAny[]>([]);
const form = reactive<ObjectAny>({});
const total = ref<number>(0);

// const iconMaps = {
//   [NotifyAction.INVITE]: "https://static.soyoung.com/sy-pre/yaoqinghan-1711451400690.png",
//   [NotifyAction.ADD]: "https://static.soyoung.com/sy-pre/xinzeng-1711451400690.png",
//   [NotifyAction.UPDATE]: "https://static.soyoung.com/sy-pre/gengxinku-1711451400690.png",
//   [NotifyAction.DELETE]: "https://static.soyoung.com/sy-pre/shanchu-1711451400690.png"
// };
onMounted(() => {
  form.page = 1;
  list.value = [];
  msgList();
});

const msgList = async () => {
  loading.value = true;
  const res = await getMsgList({
    ...form,
    category: props.categroy
  });
  if (res.code == 0) {
    total.value = res.data.total;
    list.value.push(...(res.data.list || []));
  }
  loading.value = false;
};

const loadMore = () => {
  if (list.value.length < total.value) {
    form.page++;
    msgList();
  }
};
const changeStatus = async (item: ObjectAny) => {
  const res = await updateMsg({
    id: item._id,
    readStatus: 1
  });
  if (res.code == 0) {
    item.readStatus = 1;
    emit("refresh");
  }
};
</script>
<style lang="less">
.msg_info__read {
  color: #ccc !important;
  font {
    color: #ccc !important;
  }
}
</style>
<style lang="less" scoped>
.msg_list {
  height: 400px;
  font-size: 12px;
  overflow: overlay;
  .msg_placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .msg_item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f7f7f7;
    cursor: pointer;
    position: relative;
    padding: 20px 0;
    .msg_icon {
      width: 50px;
      height: 50px;
      background: #5c54f0;
      border-radius: 50%;
      margin-right: 20px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      color: #fff;
    }
    .msg_dot {
      position: absolute;
      right: 5px;
      top: 5px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #f00;
    }
    &:hover {
      background: #f7f7f7;
    }

    .msg_info {
      width: calc(100% - 70px);
      color: #000;
    }
  }
}
</style>
