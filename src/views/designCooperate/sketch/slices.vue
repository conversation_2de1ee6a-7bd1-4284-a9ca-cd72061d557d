<template>
  <div @click.stop class="slices-div" :class="{ show: store.slicesVisible }">
    <ul class="slices-wrapper" v-if="slices && slices.length">
      <template v-for="(item, index) in slices" :key="index">
        <li @click="handleClick(item.objectID)" class="slices-item" v-for="(info, i) in item.exportable" :key="i">
          <!-- <div class="slices-layer" :id="`slice-${item.objectID}`" :data-objectId="item.objectID"></div> -->
          <picture class="slices-pic">
            <img :src="info.path" alt="" />
          </picture>
          <div class="slices-right">
            <div class="slices-title">{{ info.name }}</div>
            <div class="slices-info">
              <div class="slices-tips">{{ unitSize(item.rect.width) }}&nbsp;&nbsp;x&nbsp;&nbsp;{{ unitSize(item.rect.height) }}</div>
              <el-button type="primary" size="small" @click.stop="toCopy(info.path)" round>复制链接</el-button>
            </div>
          </div>
        </li>
      </template>
    </ul>
    <div class="slices-placeholder">该画板暂无切片，若有需要，请联系 UI</div>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, watch, defineEmits } from "vue";
import { unitSize } from "./utils/helper";
import { sketchStore } from "@/store";
import { hideDistance, mouseoutLayer, selectedLayer } from "./utils/document";
const store = sketchStore();
const props = defineProps<{
  slices: any[];
}>();
watch(
  () => props.slices,
  (slices) => {
    console.log(slices);
  }
);
const emit = defineEmits(["copy"]);
const toCopy = (text: string) => {
  emit("copy", text);
};
const handleClick = (id) => {
  const selected = document.querySelector(".layer-" + id) as HTMLElement;
  store.state.selectedIndex = parseInt(selected.dataset.index!);
  hideDistance();
  mouseoutLayer();
  selectedLayer();
};
</script>

<style lang="less" scoped>
.slices-div {
  width: 340px;
  height: 100%;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  transition: transform 0.3s ease-in-out;
  transform: translateX(100%);
  .slices-placeholder {
    height: 100%;
    width: 100%;
    color: #909299;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
  }
  &.show {
    transform: translateX(0);
  }
  .slices-item {
    display: flex;
    padding: 10px 20px;
    cursor: pointer;
    position: relative;
    &:hover {
      opacity: 0.9;
    }
  }

  .slices-pic {
    width: 70px;
    height: 70px;
    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%), linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%);
    background-position:
      0 0,
      5px 5px;
    background-size: 10px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    img {
      width: 30px;
      object-fit: contain;
    }
  }
  .slices-right {
    flex: 1;
    margin-left: 8px;
    .slices-title {
      background: #f5f5fb;
      border-radius: 4px;
      padding: 0 12px;
      display: inline-flex;
      align-items: center;
      font-size: 14px;
      color: #303233;
      width: 100%;
      box-sizing: border-box;
      height: 36px;
    }
    .slices-info {
      display: flex;
      width: 100%;
      margin-top: 7px;
      justify-content: space-between;
      align-items: center;

      .slices-tips {
        font-size: 12px;
        color: #909299;
      }
      .el-button {
        position: absolute;
        bottom: 10px;
        right: 20px;
      }
    }
  }
}
.slices-wrapper {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}
.slices-wrapper::-webkit-scrollbar {
  display: none;
}
</style>
