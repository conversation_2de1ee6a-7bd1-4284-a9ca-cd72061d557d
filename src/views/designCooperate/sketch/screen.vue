<template>
  <section class="screen-viewer">
    <div class="screen-viewer-inner" :style="screenInnerStyle">
      <div id="screen" :style="screenStyle" class="screen">
        <Rulers />
        <template v-if="funcShow">
          <!-- <div id="flows"></div> -->
          <Layers />
          <Notes />
        </template>
        <Distance />
      </div>
    </div>
    <div class="overlay"></div>
  </section>
</template>
<script lang="ts" setup>
import { computed, watch, nextTick, reactive, ref } from "vue";
import { alignElement, zoomSize } from "./utils/helper";
import { ArtboardData } from "./model/interfaces";
import { ObjectAny } from "@/types";
import Rulers from "./rulers.vue";
import Layers from "./layers.vue";
import Notes from "./notes.vue";
import Distance from "./distance.vue";
import { sketchStore } from "@/store";
import { Edge } from "./model";
import { isEmpty } from "lodash";

interface Point {
  x: number;
  y: number;
}
const funcShow = ref(true);
const store = sketchStore();
const current = computed<ArtboardData>(() => {
  return store.state.current;
});
const screenStyle = computed(() => {
  const imageData = current.value.imagePath;
  return {
    width: zoomSize(current.value.width) + "px",
    height: zoomSize(current.value.height) + "px",
    background: "#FFF url(" + imageData + ") no-repeat",
    backgroundSize: zoomSize(current.value.width) + "px " + zoomSize(current.value.height) + "px"
  };
});
const screenInnerStyle = reactive<ObjectAny>({});

const resetScroll = () => {
  const viewer: any = document.querySelector(".screen-viewer");
  const maxSize = Math.max(current.value.width, current.value.height, viewer!.clientWidth, viewer!.clientHeight) * 5;
  screenInnerStyle.width = maxSize + "px";
  screenInnerStyle.height = maxSize + "px";
  nextTick(() => {
    const screen: any = document.querySelector("#screen") as HTMLElement;
    screen.style.marginLeft = -zoomSize(current.value.width / 2) + "px";
    screen.style.marginTop = -zoomSize(current.value.height / 2) + "px";
    viewer.scrollLeft = (maxSize - viewer.clientWidth) / 2;
    let suitHight = screen.clientHeight > viewer.clientHeight ? screen.clientHeight : viewer.clientHeight;
    viewer.scrollTop = (maxSize - suitHight) / 2;
  });
};
watch(
  () => current.value,
  (val) => {
    if (!isEmpty(val)) {
      nextTick(() => {
        resetScroll();
      });
    }
  },
  {
    immediate: true
  }
);
function screenPointOnViewerCenter(viewer: HTMLDivElement, screen: HTMLDivElement): Point {
  let viewerRect = viewer.getBoundingClientRect();
  let screenRect = screen.getBoundingClientRect();
  let viewerCenter = <Point>{
    x: viewerRect.x + viewerRect.width / 2,
    y: viewerRect.y + viewerRect.height / 2
  };
  return {
    x: viewerCenter.x - screenRect.x,
    y: viewerCenter.y - screenRect.y
  };
}
watch(
  () => store.state.zoom,
  (val: number, oldVal: number) => {
    funcShow.value = false;
    let viewer = document.querySelector(".screen-viewer") as HTMLDivElement;
    let screen = document.querySelector("#screen") as HTMLDivElement;
    let currentRect = screen.getBoundingClientRect();
    let screenPoint = screenPointOnViewerCenter(viewer, screen);
    let screenPoint2 = <Point>{
      x: (screenPoint.x * val) / oldVal,
      y: (screenPoint.y * val) / oldVal
    };
    alignElement({
      scroller: viewer,
      target: screen,
      toRect: currentRect,
      fromEdge: Edge.hleft | Edge.vtop,
      toEdge: Edge.hleft | Edge.vtop
    });
    viewer.scrollLeft += screenPoint2.x - screenPoint.x;
    viewer.scrollTop += screenPoint2.y - screenPoint.y;
    funcShow.value = true;
  }
);
</script>
<style lang="less" scoped>
.screen-viewer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: calc(100vh - 48px);
  background: #ffffff;
  background-size: 16px 16px;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  .overlay {
    display: none;
    position: fixed;
    width: 100vw;
    height: calc(100vh);
    top: 60px;
    left: 0;
    background: transparent;
    overflow: hidden;
    z-index: 2;
    cursor: none;
  }
  &.moving-screen .overlay {
    display: block;
  }
  .screen-viewer-inner {
    position: relative;
    margin: 0 auto;
  }
  .screen {
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 50%;
    background: #fff;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);
  }
}
</style>
