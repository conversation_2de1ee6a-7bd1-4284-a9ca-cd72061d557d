<template>
  <div @click.stop class="inspector" id="inspector" :class="{ show: !!layerData }">
    <template v-if="layerData">
      <div class="inspector-title" @click.stop="toCopy(layerData.name)">{{ layerData.name }}</div>
      <Properties :layer-data="layerData" @copy="toCopy" />
      <el-divider />
      <template v-if="layerData.fills && layerData.fills.length">
        <Fills :layer-data="layerData" @copy="toCopy" />
        <el-divider />
      </template>
      <template v-if="layerData.type == 'text'">
        <Font :layer-data="layerData" @copy="toCopy" />
        <el-divider />
      </template>
      <template v-if="layerData.borders && layerData.borders.length">
        <Borders :layer-data="layerData" @copy="toCopy" />
        <el-divider />
      </template>
      <template v-if="layerData.shadows && layerData.shadows.length">
        <Shadows :layer-data="layerData" @copy="toCopy" />
        <el-divider />
      </template>
      <template v-if="layerData.css && layerData.css.length">
        <CodeTemplate :layer-data="layerData" @copy="toCopy" />
        <el-divider />
      </template>
      <template v-if="layerData.exportable && layerData.exportable.length">
        <Exportable :layer-data="layerData" @copy="toCopy" />
      </template>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { computed, defineEmits } from "vue";
import { sketchStore } from "@/store";
import Properties from "./components/properties.vue";
import Fills from "./components/fills.vue";
import Font from "./components/font.vue";
import Borders from "./components/borders.vue";
import Shadows from "./components/shadows.vue";
import CodeTemplate from "./components/codeTemplate.vue";
import Exportable from "./components/exportable.vue";
import { LayerData } from "./model/interfaces";
const store = sketchStore();
const layerData = computed<LayerData | null>(() => {
  const state = store.state;
  const current = state.current;
  if (state.selectedIndex === undefined || !current || !current.layers || !state.current.layers[state.selectedIndex]) {
    return null;
  }
  return state.current.layers[state.selectedIndex];
});
const emit = defineEmits(["copy"]);
const toCopy = (text: string) => {
  emit("copy", text);
};
</script>
<style lang="less" scoped>
.inspector {
  width: 340px;
  height: 100%;
  overflow: auto;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  transition: transform 0.3s ease-in-out;
  transform: translateX(100%);
  padding: 16px 20px;
  box-sizing: border-box;
  box-shadow: -2px 10px 10px 0 rgba(0, 0, 0, 0.2);
  &::-webkit-scrollbar {
    display: none;
  }

  &.show {
    transform: translateX(0);
  }
  &-title {
    height: 50px;
    width: 100%;
    background: #f5f5fb;
    border-radius: 4px;
    line-height: 50px;
    text-align: center;
    font-size: 14px;
    color: #303233;
  }
}
</style>
