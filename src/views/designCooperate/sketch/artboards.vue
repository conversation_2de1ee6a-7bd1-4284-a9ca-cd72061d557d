<template>
  <div class="sketch-artboards">
    <div class="search-box">
      <div class="search-name" v-if="!searchInputStatus">全部</div>
      <div class="search-container" v-else v-click-outside="handleOutsideClick">
        <el-input clearable style="width: 224px" @clear="handleSearch" ref="inputRef" v-model="searchInput" placeholder="搜索" @keyup.enter="handleSearch"></el-input>
      </div>
      <div class="search-icon" @click.stop="onClickBtn()">
        <i class="iconfont icon-sousuo"></i>
      </div>
    </div>
    <el-divider />

    <div class="list">
      <div class="tree-list-item">
        <div class="item-box">
          <div class="dnd-item">
            <div class="dnd-item-box" @click="handleClick">
              <i :class="{ 'iconfont icon-jiantouzhankai': true, 'active-item': dndTtemStatus }"></i>
              <i class="iconfont icon-a-sucaiku3x"></i>
              <div class="name">{{ group?.name || "未分组" }}</div>
            </div>
            <div v-show="dndTtemStatus" :id="item._id" :class="{ 'item-box-child': true, 'active-item': activeSketchId === item._id }" v-for="item in filterList" :key="item._id" @click="chooseSketchActive(item._id)">
              <el-popover :show-after="500" width="220px" placement="right" trigger="hover">
                <template #reference>
                  <div class="item">
                    <div class="item-img-box">
                      <img alt="" :src="item.imagePath" />
                    </div>
                    <div class="item-name">{{ item.name }}</div>
                  </div>
                </template>
                <img style="width: 200px" alt="" :src="item.imagePath" />
              </el-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, ref, defineProps, watch, onMounted } from "vue";
import hotkeys from "hotkeys-js";
import { GroupData } from "./model";
const filterList = ref<any[]>([]);
const props = defineProps<{
  activeSketchId: string;
  group: Partial<GroupData>;
  groupDataList: any[];
}>();
const emit = defineEmits(["change"]);
const dndTtemStatus = ref(true);
const searchInput = ref("");
const inputRef = ref<any>(null);
const searchInputStatus = ref(false);

onMounted(() => {
  hotkeys("command+f", (event) => {
    onClickBtn(true);
    event.preventDefault();
  });
});
watch(
  () => props.groupDataList,
  () => {
    handleSearch();
    if (props.activeSketchId) {
      nextTick(() => {
        domScrollToViewById(props.activeSketchId);
      });
    }
  }
);
const onClickBtn = (flag?: boolean) => {
  searchInputStatus.value = flag === undefined ? !searchInputStatus.value : flag;
  nextTick(() => {
    searchInputStatus.value ? inputRef.value?.focus() : domScrollToViewById(props.activeSketchId);
  });
};

const handleClick = () => {
  dndTtemStatus.value = !dndTtemStatus.value;
};
const chooseSketchActive = (id: string) => {
  emit("change", id);
};

const handleOutsideClick = () => {
  searchInputStatus.value = false;
  searchInput.value = "";
};

const handleSearch = () => {
  filterList.value = props.groupDataList.filter((item: any) => item.slug.includes(searchInput.value));
  dndTtemStatus.value = true;
  if (!searchInput.value) {
    nextTick(() => {
      domScrollToViewById(props.activeSketchId);
    });
  }
};

const domScrollToViewById = (id: string) => {
  // const element = document.getElementById(id);
  // if (element) {
  //   element.scrollIntoView({ behavior: "smooth", block: "start", inline: "nearest" });
  // }
};
</script>
<style lang="less" scoped>
.sketch-artboards {
  max-height: calc(100vh - 100px);
  position: absolute;
  top: 25px;
  left: 24px;
  z-index: 2022;
  transition: left ease 0.5s;
  padding: 24px 15px;
  box-sizing: border-box;
  background: #fff;
  width: 264px;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  .el-divider {
    margin: 10px 0;
  }
  .search-box {
    line-height: 32px;
    font-size: 14px;
    font-weight: 600;
    height: 32px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .search-name {
      color: #333;
    }
    .search-icon {
      cursor: pointer;
      padding: 0 6px;
      background-color: #f0f2f5;
      border-radius: 4px;
      .icon-sousuo {
        font-size: 16px;
      }
    }
  }
  .search-container {
    overflow: hidden;
    display: flex;
    align-items: center;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    box-sizing: border-box;
    margin-right: 10px;
  }
  .list::-webkit-scrollbar {
    /* WebKit */
    width: 0;
    height: 0;
  }
  .list {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    .tree-list-item {
      margin-bottom: 4px;
      margin-bottom: 4px;
      user-select: none;
      font-size: 14px;
      .item-box {
        transition: none !important;
      }
      .dnd-item {
        padding: 0;
        box-sizing: border-box;
        display: block;
        position: relative;
        .dnd-item-box {
          height: 40px;
          line-height: 32px;
          width: 100%;
          display: flex;
          padding: 4px;
          box-sizing: border-box;
          margin-bottom: 4px;
          cursor: pointer;
          &:hover {
            background-color: #f0f2f5;
          }
          .icon-jiantouzhankai {
            font-size: 8px;
            transform: rotate(90deg);
          }
          .active-item {
            transform: rotate(180deg);
          }
          .icon-a-sucaiku3x {
            padding-left: 10px;
            font-size: 13px;
          }
          .name {
            display: inline-block;
            margin-left: 10px;
            color: #333333;
          }
        }
        .item-box-child {
          width: 100%;
          cursor: pointer;
          margin-bottom: 4px;
          &:hover {
            background-color: #f0f2f5;
          }
          &.active-item {
            background-color: #f0f2f5;
            font-weight: 700;
          }
          .item {
            height: 48px;
            line-height: 40px;
            width: 100%;
            display: flex;
            padding: 4px 0;
            box-sizing: border-box;
            color: #333333;
            justify-content: space-between;
            flex-wrap: nowrap;
            align-items: center;
            .item-img-box {
              height: 40px;
              width: 40px;
              min-width: 40px;
              text-align: center;
              line-height: 40px;
              background: #e3e6ec;
              border-radius: 2px;
              display: flex;
              align-items: center;
              justify-content: space-around;
              margin-right: 8px;
              img {
                width: auto;
                height: auto;
                border-radius: 0;
                max-height: 40px;
                max-width: 40px;
              }
            }
            .item-name {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: left;
            }
          }
        }
      }
    }
  }
}
</style>
