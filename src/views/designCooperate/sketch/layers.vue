<template>
  <div id="layers">
    <template v-for="(layer, index) in current.layers" :key="layer.objectID">
      <div
        v-if="layer.type !== SMType.group && layer.type !== SMType.hotspot"
        :percentage-width="percentageSize(layer.rect.width, current.width)"
        :percentage-height="percentageSize(layer.rect.height, current.height)"
        :data-width="unitSize(layer.rect.width)"
        :data-height="unitSize(layer.rect.height)"
        :data-index="index"
        :id="`layer-${index}`"
        :style="computedStyle(layer)"
        :class="['layer', `layer-${layer.objectID}`, store.state.selectedIndex == index ? 'selected' : '']"
      >
        <i class="tl"></i><i class="tr"></i><i class="bl"></i><i class="br"></i> <b class="et h"></b><b class="er v"></b><b class="eb h"></b><b class="el v"></b>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { ArtboardData, SMType, LayerData } from "./model/interfaces";
import { sketchStore } from "@/store";
import { percentageSize, unitSize, zoomSize } from "./utils/helper";

const store = sketchStore();
const current = computed<ArtboardData>(() => {
  return store.state.current;
});
const computedStyle = (layer: LayerData) => {
  const x = zoomSize(layer.rect.x);
  const y = zoomSize(layer.rect.y);
  const width = zoomSize(layer.rect.width);
  const height = zoomSize(layer.rect.height);
  return {
    left: `${x}px`,
    top: `${y}px`,
    width: `${width}px`,
    height: `${height}px`
  };
};
</script>

<style lang="less">
.layer {
  position: absolute;
  cursor: pointer;
  z-index: 222;
  box-sizing: border-box;
  &.selected {
    border: 1px solid #f33155;
  }
  &.hover {
    border: 1px solid #5c54f0;
  }
  &.selected {
    i {
      display: block;
    }

    &:after,
    &:before {
      position: absolute;
      display: block;
      left: 50%;
      top: -23px;
      transform: translateX(-50%);
      content: attr(data-width);
      font-size: 12px;
      color: #fff;
      height: 12px;
      line-height: 12px;
      padding: 4px;
      background: #f33155;
      border-radius: 2px;
      z-index: 1;
      width: max-content;
    }
    &.hidden:after,
    &.hidden:before {
      display: none;
    }
    &:after {
      content: attr(data-height);
      left: auto;
      right: 0;
      top: 50%;
      transform: translateX(calc(100% + 3px)) translateY(-50%);
    }
  }
  b,
  i {
    position: absolute;
    width: 5px;
    height: 5px;
    background: #fff;
    border: 1px solid #f33155;
    border-radius: 50%;
    overflow: hidden;
    display: none;
  }
  b {
    width: 3px;
    height: 3px;
    background: #f33155;
  }
  .tl {
    top: -3px;
    left: -3px;
  }
  .tr {
    top: -3px;
    right: -3px;
  }
  .bl {
    bottom: -3px;
    left: -3px;
  }
  .br {
    bottom: -3px;
    right: -3px;
  }
}
</style>
