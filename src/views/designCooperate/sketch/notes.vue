<template>
  <div id="notes">
    <div
      class="note"
      v-for="(note, i) in current.notes"
      :key="i"
      :data-index="i + 1"
      :style="{
        left: `${zoomSize(note.rect.x)}px`,
        top: `${zoomSize(note.rect.y)}px`
      }"
    >
      <div style="white-space: nowrap; display: none">
        {{ note.note }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { sketchStore } from "@/store";
import { ArtboardData } from "./model/interfaces";
import { zoomSize } from "./utils/helper";

const store = sketchStore();
const current = computed<ArtboardData>(() => {
  return store.state.current;
});
</script>
<style lang="less" scoped>
.note {
  position: absolute;
  margin: -12px 0 0 -12px;
  width: 24px;
  height: 24px;
  background: #f33155;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.24);
  &:before {
    content: attr(data-index);
    font-size: 12px;
    display: block;
    color: #fff;
    text-align: center;
    width: 100%;
    height: 100%;
    line-height: 20px;
  }
  &:hover {
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.64);
  }
  div {
    position: absolute;
    top: 50%;
    left: 30px;
    border-radius: 4px;
    padding: 8px;
    background: #fff;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    -webkit-user-select: text;
    color: #222;
    transform: translateY(-50%);
    z-index: 2;
    &:before {
      content: "";
      position: absolute;
      left: -7px;
      top: 50%;
      width: 8px;
      height: 14px;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAcCAMAAABf788oAAAANlBMVEUAAAAAAAAAAAAAAAAAAAAAAADR0dEAAAAAAAAAAAAAAAAAAADNzc0AAAAAAADa2trk5OT////5auFFAAAAEXRSTlMAAgYKEhydNCkYDiOfLieWjz4MUj4AAABrSURBVBjTZZFbDoAgEANVFHmIOve/rERjWGj/psnCbjv1gg7nGTpcFmtUdA4Mu7QmMLzGGMHwlvMNhs9yAY3D7qkamcYHr/75ys1IMRePNbbwsRo6oo/qt7rY6Ohxer4GpBFqyFqDFtWqfAD4dQrlWDmShAAAAABJRU5ErkJggg==)
        no-repeat;
      background-size: 8px 14px;
      transform: translateY(-50%);
    }
  }
}
</style>
