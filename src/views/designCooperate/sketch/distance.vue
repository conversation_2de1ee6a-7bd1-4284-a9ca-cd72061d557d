<template>
  <div id="td" class="distance v" style="display: none"><div data-height="3"></div></div>
  <div id="rd" class="distance h" style="display: none"><div data-width=""></div></div>
  <div id="bd" class="distance v" style="display: none"><div data-height=""></div></div>
  <div id="ld" class="distance h" style="display: none"><div data-width=""></div></div>
</template>
<script lang="ts" setup></script>
<style lang="less" scoped>
.distance,
.distance div,
.distance div:before,
.distance:after,
.distance:before {
  position: absolute;
}

.distance.v,
.distance.v div {
  width: 1px;
}

.distance.h,
.distance.h div {
  height: 1px;
}

.distance.v div {
  top: 0;
  bottom: 0;
  background: #f33155;
}

.distance.h div {
  left: 0;
  right: 0;
  background: #f33155;
}

.distance.v:after,
.distance.v:before {
  content: "";
  top: 0;
  left: -2px;
  width: 5px;
  height: 1px;
  background: #f33155;
}

.distance.h:after,
.distance.h:before {
  content: "";
  top: -2px;
  left: 0;
  width: 1px;
  height: 5px;
  background: #f33155;
}

.distance.v:after {
  top: auto;
  bottom: 0;
}

.distance.h:after {
  left: auto;
  right: 0;
}

.distance.h div[data-width]:before,
.distance.v div[data-height]:before {
  position: absolute;
  display: block;
  left: 50%;
  top: -23px;
  transform: translateX(-50%);
  content: attr(data-width);
  font-size: 12px;
  color: #fff;
  height: 12px;
  line-height: 12px;
  padding: 4px;
  background: #f33155;
  border-radius: 2px;
  z-index: 1;
}

.distance.v div[data-height]:before {
  content: attr(data-height);
  left: auto;
  right: 0;
  top: 50%;
  transform: translateX(calc(100% + 3px)) translateY(-50%);
}
</style>
