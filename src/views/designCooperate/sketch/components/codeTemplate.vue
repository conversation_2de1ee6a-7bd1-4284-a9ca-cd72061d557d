<template>
  <el-tabs v-model="active" class="demo-tabs">
    <el-tab-pane label="css" name="css">
      <div class="css-panel code-item" @click="emit('copy', layerData.css.join('\r\n'))">
        <CodeMirror mode="css" :code="layerData.css.join('\r\n')" />
        <!-- <el-input type="textarea" :rows="layerData.css.length + 1" readonly :value="layerData.css.join('\r\n')" /> -->
      </div>
    </el-tab-pane>
    <el-tab-pane label="android" name="android">
      <CodeMirror mode="css" :code="codeObj.android" @click="emit('copy', codeObj.android)" />
    </el-tab-pane>
    <el-tab-pane label="ios" name="ios">
      <CodeMirror mode="clike" :code="codeObj.ios" @click="emit('copy', codeObj.ios)" />
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps, ref, computed, defineEmits } from "vue";
import { LayerData } from "../model/interfaces";
import { scaleSize, unitSize } from "../utils/helper";
import CodeMirror from "./codemirror.vue";
const props = defineProps<{
  layerData: LayerData;
}>();
const emit = defineEmits(["copy"]);
const active = ref<string>("css");
const codeObj = computed<ObjectAny>(() => {
  const obj: ObjectAny = {};
  const layerData = props.layerData;
  switch (layerData.type) {
    case "text":
      obj.android = `<TextView\r\n${getAndroidWithHeight(layerData)}android:text="${layerData.content}"\r\nandroid:textColor="${layerData.color["argb-hex"]}"\r\nandroid:textSize="${unitSize(layerData.fontSize, true)}"\r\n/>`;
      obj.ios = `UILabel *label = [[UILabel alloc] init];\r\nlabel.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\r\nlabel.text = @"${
        layerData.content
      }";\r\nlabel.font = [UIFont fontWithName:@"${layerData.fontFace}" size:${unitSize(layerData.fontSize)}];\r\nlabel.textColor = [UIColor colorWithRed:${layerData.color.rgb.r}/255.0 green:${layerData.color.rgb.g}/255.0 blue:${layerData.color.rgb.b}/255.0 alpha:${
        layerData.color.alpha
      }/255.0];\r\n`;
      break;
    case "shape":
      obj.android = `<View\r\n${getAndroidWithHeight(layerData)}${getAndroidShapeBackground(layerData)}/>`;
      obj.ios = `UIView *view = [[UIView alloc] init];\r\nview.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\r\n${getIOSShapeBackground(layerData)}`;
      break;
    case "slice":
      obj.android = `<ImageView\r\n${getAndroidWithHeight(layerData)}${getAndroidImageSrc(layerData)}/>`;
      obj.ios = `UIImageView *imageView = [[UIImageView alloc] init];\r\nimageView.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\r\n${getIOSImageSrc(layerData)}`;
      break;
  }
  return obj;
});
function getIOSImageSrc(layerData: LayerData) {
  if (layerData.type != "slice" || typeof layerData.exportable == "undefined") return "";
  return 'imageView.image = [UIImage imageNamed:@"' + layerData.exportable[0].name + "." + layerData.exportable[0].format + '"];\r\n';
}

function getAndroidImageSrc(layerData: LayerData) {
  if (layerData.type != "slice" || typeof layerData.exportable == "undefined") return "";
  return 'android:src="@mipmap/' + layerData.exportable[0].name + "." + layerData.exportable[0].format + '"\r\n';
}
function getIOSShapeBackground(layerData: LayerData) {
  let colorCode = "";
  if (layerData.type != "shape" || typeof layerData.fills == "undefined" || layerData.fills.length == 0) return colorCode;
  let f;
  for (f in layerData.fills) {
    if (layerData.fills[f].fillType.toLowerCase() == "color") {
      return "view.backgroundColor = [UIColor colorWithRed:" + layerData.fills[f].color.rgb.r + "/255.0 green:" + layerData.fills[f].color.rgb.g + "/255.0 blue:" + layerData.fills[f].color.rgb.b + "/255.0 alpha:" + layerData.fills[f].color.alpha + "/255.0];\r\n";
    }
  }
  return colorCode;
}
function getAndroidShapeBackground(layerData: LayerData) {
  let colorCode = "";
  if (layerData.type != "shape" || typeof layerData.fills == "undefined" || layerData.fills.length == 0) return colorCode;
  let f;
  for (f in layerData.fills) {
    if (layerData.fills[f].fillType.toLowerCase() == "color") {
      return 'android:background="' + layerData.fills[f].color["argb-hex"] + '"\r\n';
    }
  }
  return colorCode;
}
function getAndroidWithHeight(layerData: LayerData) {
  return 'android:layout_width="' + unitSize(layerData.rect.width, false) + '"\r\n' + 'android:layout_height="' + unitSize(layerData.rect.height, false) + '"\r\n';
}
</script>
<style lang="less"></style>
