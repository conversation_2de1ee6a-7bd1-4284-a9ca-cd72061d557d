<template>
  <section class="font-div">
    <div class="font-title">{{ localize("TYPEFACE") }}</div>
    <div class="item">
      <div class="item-label">
        {{ localize("Family") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input">
            {{ layerData.fontFace }}
          </div>
        </li>
      </ul>
    </div>
    <div class="item">
      <div class="item-label">
        {{ localize("Color") + ":" }}
      </div>
      <colorItem :color="layerData.color" />
    </div>
    <div v-if="layerData.fontSize" class="item">
      <div class="item-label">
        {{ localize("Size") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input">
            {{ unitSize(layerData.fontSize, true) }}
          </div>
        </li>
      </ul>
    </div>
    <div class="item">
      <div class="item-label">
        {{ localize("Spacing") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input">
            {{ unitSize(layerData.letterSpacing, true) }}
          </div>
          <div class="value-label">
            {{ localize("Character") }}
          </div>
        </li>
        <li>
          <div class="value-input">
            {{ layerData.lineHeight ? unitSize(layerData.lineHeight, true) : "Auto" }}
          </div>
          <div class="value-label">
            {{ localize("Line") }}
          </div>
        </li>
      </ul>
    </div>
    <div class="item">
      <div class="item-label">
        {{ localize("Content") + ":" }}
      </div>
      <div class="item-value">
        <el-input type="textarea" resize="none" :value="layerData.content" id="content" :rows="2" readonly :style="`font-family: ${layerData.fontFace},sans-serif`" />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps } from "vue";
import { localize } from "../model";
import colorItem from "./colorItem.vue";
import { unitSize } from "../utils/helper";
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less" scoped>
.font-div {
  .font-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .item {
    display: flex;
    width: 100%;
    margin-top: 10px;
    &-label {
      font-size: 14px;
      color: #595959;
      width: 70px;
      margin-top: 8px;
    }
    &-value {
      flex: 1;
      display: inline-flex;
      justify-content: space-between;
    }
    li {
      flex: 1;
    }
    .value-input {
      width: 107px;
      height: 32px;
      background: #f5f5fb;
      border-radius: 4px;
      font-size: 14px;
      color: #303233;
      box-sizing: border-box;
      padding-left: 12px;
      display: inline-flex;
      align-items: center;
    }
    .value-label {
      margin-top: 5px;
      font-size: 14px;
      color: #909299;
      padding-left: 12px;
      line-height: 20px;
    }
  }
}
</style>
