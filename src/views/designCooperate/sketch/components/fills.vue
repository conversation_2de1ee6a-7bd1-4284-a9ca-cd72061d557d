<template>
  <section class="fills">
    <div class="fills-title">{{ localize("FILLS") }}</div>
    <div v-for="(fill, i) in layerData.fills" :key="i" class="item items-group" :data-label="localize(fill.fillType) + ':'">
      <div class="item-label">
        {{ localize(fill.fillType) + ":" }}
      </div>
      <colorItem v-if="fill.fillType.toLowerCase() == 'color'" :color="fill.color" />
      <div v-else-if="fill.gradient && fill.gradient.colorStops" class="gradient">
        <colorItem v-for="(gradient, i) in fill.gradient.colorStops" :key="i" :color="gradient.color" />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps } from "vue";
import colorItem from "./colorItem.vue";
import { localize } from "../model";
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less">
.fills {
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .item {
    display: flex;
    width: 100%;
    align-items: center;
    margin-top: 10px;
    &-label {
      font-size: 14px;
      color: #595959;
      width: 70px;
    }
    .gradient {
      flex: 1;
      label {
        &:before {
          position: absolute;
          top: 12px;
          left: 11px;
          content: "";
          width: 2px;
          height: 32px;
          background: #fff;
          z-index: 2;
          box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.2);
        }
        &:after {
          position: absolute;
          top: 8px;
          left: 8px;
          content: "";
          width: 8px;
          height: 8px;
          background: #fff;
          z-index: 3;
          border-radius: 4px;
          box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}
</style>
