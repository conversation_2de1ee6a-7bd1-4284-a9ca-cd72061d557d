<template>
  <section class="properties">
    <div class="properties-title">{{ localize("PROPERTIES") }}</div>
    <div class="item">
      <div class="item-label">
        {{ localize("Position") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input" @click="emit('copy', unitSize(layerData.rect.x))">
            {{ unitSize(layerData.rect.x) }}
          </div>
          <div class="value-label">
            {{ localize("X") }}
          </div>
        </li>
        <li>
          <div class="value-input" @click="emit('copy', unitSize(layerData.rect.y))">
            {{ unitSize(layerData.rect.y) }}
          </div>
          <div class="value-label">
            {{ localize("Y") }}
          </div>
        </li>
      </ul>
    </div>
    <div class="item">
      <div class="item-label">
        {{ localize("Size") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input" @click="emit('copy', unitSize(layerData.rect.width))">
            {{ unitSize(layerData.rect.width) }}
          </div>
          <div class="value-label">
            {{ localize("Width") }}
          </div>
        </li>
        <li>
          <div class="value-input" @click="emit('copy', unitSize(layerData.rect.height))">
            {{ unitSize(layerData.rect.height) }}
          </div>
          <div class="value-label">
            {{ localize("Height") }}
          </div>
        </li>
      </ul>
    </div>
    <div v-if="typeof layerData.opacity == 'number'" class="item">
      <div class="item-label">
        {{ localize("Opacity") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input" @click="emit('copy', Math.round(layerData.opacity * 10000) / 100 + '%')">
            {{ Math.round(layerData.opacity * 10000) / 100 + "%" }}
          </div>
        </li>
      </ul>
    </div>
    <div v-if="layerData.radius" class="item">
      <div class="item-label">
        {{ localize("Radius") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input" @click="emit('copy', unitSize(layerData.radius[0]))">
            {{ unitSize(layerData.radius[0]) }}
          </div>
        </li>
      </ul>
    </div>
    <div v-if="layerData.styleName" class="item">
      <div class="item-label">
        {{ localize("Style") + ":" }}
      </div>
      <ul class="item-value">
        <li>
          <div class="value-input" @click="emit('copy', layerData.styleName)">
            {{ layerData.styleName }}
          </div>
        </li>
      </ul>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps, defineEmits } from "vue";
import { localize } from "../model";
import { unitSize } from "../utils/helper";
const emit = defineEmits(["copy"]);
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less" scoped>
.properties {
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .item {
    display: flex;
    width: 100%;
    margin-top: 10px;
    &-label {
      font-size: 14px;
      color: #595959;
      width: 70px;
      margin-top: 8px;
    }
    &-value {
      flex: 1;
      display: inline-flex;
      justify-content: space-between;
    }
    li {
      flex: 1;
    }
    .value-input {
      width: 107px;
      height: 32px;
      background: #f5f5fb;
      border-radius: 4px;
      font-size: 14px;
      color: #303233;
      box-sizing: border-box;
      padding-left: 12px;
      display: inline-flex;
      align-items: center;
    }
    .value-label {
      margin-top: 5px;
      font-size: 14px;
      color: #909299;
      padding-left: 12px;
      line-height: 20px;
    }
  }
}
</style>
