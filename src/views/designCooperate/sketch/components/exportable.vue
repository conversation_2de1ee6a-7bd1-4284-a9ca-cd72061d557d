<template>
  <section class="exportable">
    <div class="exportable-title">{{ localize("EXPORTABLE") }}</div>
    <div class="item-image">
      <img :src="layerData.exportable[0].path" alt="" />
    </div>
    <div v-for="(exp, i) in layerData.exportable" :key="i" class="item items-group">
      <div class="item">
        <div class="value-input">
          {{ exp.name }}
        </div>
        <el-button type="primary" size="small" @click.stop round @click="emit('copy', exp.path)">复制链接</el-button>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { localize } from "../model";
import { defineProps, defineEmits } from "vue";
const emit = defineEmits(["copy"]);
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less">
.exportable {
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .item-image {
    width: 100%;
    height: 80px;
    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%), linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%);
    background-position:
      0 0,
      5px 5px;
    background-size: 10px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    img {
      height: 50px;
      object-fit: contain;
    }
  }
  .item {
    display: flex;
    width: 100%;
    align-items: center;
    margin-top: 10px;
    .value-input {
      min-width: 107px;
      height: 32px;
      background: #f5f5fb;
      border-radius: 4px;
      font-size: 14px;
      color: #303233;
      box-sizing: border-box;
      padding: 0 12px;
      line-height: 32px;
      margin-right: 20px;
    }
  }
}
</style>
