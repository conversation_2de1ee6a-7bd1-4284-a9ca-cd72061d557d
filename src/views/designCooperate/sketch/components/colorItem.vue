<template>
  <div class="color" :data-name="store.project.colorNames ? store.project.colorNames[color['argb-hex']] : null">
    <label>
      <em>
        <i :style="`background-color: ${color['css-rgba']}`"></i>
      </em>
    </label>
    <span class="value-input">
      {{ color[store.state.colorFormat] }}
    </span>
  </div>
</template>

<script lang="ts" setup>
import { sketchStore } from "@/store";
import { defineProps } from "vue";
defineProps<{
  color: any;
}>();

const store = sketchStore();
</script>
<style lang="less">
.color {
  flex: 1;
  height: 32px;
  background: #f5f5fb;
  border-radius: 4px;
  font-size: 14px;
  color: #303233;
  box-sizing: border-box;
  padding-left: 12px;
  line-height: 32px;
  margin-bottom: 5px;
  position: relative;
  .value-input {
    margin-left: 50px;
  }
  // display: flex;
  // align-items: center;
  &:last-child label:before {
    display: none;
  }
  label {
    margin-right: 15px;
    position: absolute;
    display: block;
    width: 24px;
    height: 24px;
    padding: 0;
    top: 4px;
  }
  i {
    display: block;
    width: 24px;
    height: 24px;
    padding: 0;
    position: relative;
  }
}
</style>
