<template>
  <section class="shadows-div">
    <div class="shadows-title">{{ localize("SHADOWS") }}</div>
    <div v-for="(shadow, i) in layerData.shadows" :key="i" class="items-group">
      <div class="items-group-title">{{ localize(shadow.type + " Shadow") }}</div>
      <div class="item">
        <div class="item-label">
          {{ localize("Offset") + ":" }}
        </div>
        <ul class="item-value">
          <li>
            <div class="value-input">
              {{ unitSize(shadow.offsetX) }}
            </div>
            <div class="value-label">
              {{ localize("X") }}
            </div>
          </li>
          <li>
            <div class="value-input">
              {{ unitSize(shadow.offsetY) }}
            </div>
            <div class="value-label">
              {{ localize("Y") }}
            </div>
          </li>
        </ul>
      </div>
      <div class="item">
        <div class="item-label">
          {{ localize("Effect") + ":" }}
        </div>
        <ul class="item-value">
          <li>
            <div class="value-input">
              {{ unitSize(shadow.blurRadius) }}
            </div>
            <div class="value-label">
              {{ localize("Blur") }}
            </div>
          </li>
          <li>
            <div class="value-input">
              {{ unitSize(shadow.spread) }}
            </div>
            <div class="value-label">
              {{ localize("Spread") }}
            </div>
          </li>
        </ul>
      </div>
      <div class="item">
        <div class="item-label">
          {{ localize("Color") + ":" }}
        </div>
        <colorItem :color="shadow.color" />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps } from "vue";
import { localize } from "../model";
import colorItem from "./colorItem.vue";
import { unitSize } from "../utils/helper";
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less" scoped>
.shadows-div {
  .shadows-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .items-group {
    border-radius: 5px;
    padding: 5px;
    &-title {
      font-family: PingFangSC-Medium;
      font-size: 13px;
      color: #303233;
      font-weight: 500;
    }
  }
  .item {
    display: flex;
    width: 100%;
    margin-top: 10px;
    &-label {
      font-size: 14px;
      color: #595959;
      width: 70px;
      margin-top: 8px;
    }
    &-value {
      flex: 1;
      display: inline-flex;
      justify-content: space-between;
    }
    li {
      flex: 1;
    }
    .value-input {
      width: 107px;
      height: 32px;
      background: #f5f5fb;
      border-radius: 4px;
      font-size: 14px;
      color: #303233;
      box-sizing: border-box;
      padding-left: 12px;
      line-height: 32px;
    }
    .value-label {
      margin-top: 5px;
      font-size: 14px;
      color: #909299;
      padding-left: 12px;
      line-height: 20px;
    }
  }
}
</style>
