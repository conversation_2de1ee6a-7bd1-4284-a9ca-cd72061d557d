<template>
  <codemirror :value="code" :options="options" />
</template>
<script lang="ts" setup>
import { defineProps, reactive } from "vue";
import codemirror from "codemirror-editor-vue3";
import "codemirror/mode/css/css.js";
import "codemirror/mode/clike/clike.js";
// import "codemirror/mode/android/android.js";
import "codemirror/addon/display/autorefresh";
import { EditorConfiguration } from "codemirror";
import "codemirror/theme/dracula.css";
const props = defineProps<{
  code: string;
  mode: string;
}>();

const options = reactive<EditorConfiguration>({
  smartIndent: true,
  tabSize: 4,
  mode: props.mode,
  lineNumbers: false,
  autoRefresh: true,
  lineWrapping: true
});
</script>
<style lang="less" scoped></style>
