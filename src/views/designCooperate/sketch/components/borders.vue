<template>
  <section class="borders">
    <div class="borders-title">{{ localize("BORDERS") }}</div>
    <div v-for="(border, i) in layerData.borders" :key="i" class="items-group">
      <div class="items-group-title">{{ localize(border.position + " Border") }}</div>
      <div class="item">
        <div class="item-label">
          {{ localize("Weight") + ":" }}
        </div>
        <ul class="item-value">
          <li>
            <div class="value-input">
              {{ unitSize(border.thickness) }}
            </div>
          </li>
        </ul>
      </div>
      <div class="item">
        <div class="item-label">
          {{ localize(border.fillType) + ":" }}
        </div>
        <colorItem v-if="border.fillType.toLowerCase() == 'color'" :color="border.color" />
        <div v-else-if="border.gradient && border.gradient.colorStops" class="colors gradient">
          <colorItem v-for="(gradient, i) in border.gradient.colorStops" :key="i" :color="gradient.color" />
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ObjectAny } from "@/types";
import { defineProps } from "vue";
import { localize } from "../model";
import colorItem from "./colorItem.vue";
import { unitSize } from "../utils/helper";
defineProps<{
  layerData: ObjectAny;
}>();
</script>
<style lang="less" scoped>
.borders {
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #303233;
    font-weight: 500;
    margin-top: 16px;
  }
  .items-group {
    border-radius: 5px;
    padding: 5px;
    &-title {
      font-family: PingFangSC-Medium;
      font-size: 13px;
      color: #303233;
      font-weight: 500;
    }
  }
  .item {
    display: flex;
    width: 100%;
    margin-top: 10px;
    &-label {
      font-size: 14px;
      color: #595959;
      width: 70px;
      margin-top: 8px;
    }
    &-value {
      flex: 1;
      display: inline-flex;
      justify-content: space-between;
    }
    li {
      flex: 1;
    }
    .value-input {
      width: 107px;
      height: 32px;
      background: #f5f5fb;
      border-radius: 4px;
      font-size: 14px;
      color: #303233;
      box-sizing: border-box;
      padding-left: 12px;
      display: inline-flex;
      align-items: center;
    }
    .value-label {
      margin-top: 5px;
      font-size: 14px;
      color: #909299;
      padding-left: 12px;
      line-height: 20px;
    }
  }
}
</style>
