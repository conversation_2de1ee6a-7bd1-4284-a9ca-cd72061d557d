<template>
  <div class="scale-div">
    <el-button type="info" text :disabled="num <= 0.25" @click="handleScaleChange(-0.25)">
      <i class="iconfont sy-gicon-jian"></i>
    </el-button>
    <el-dropdown @command="handleCommand" class="scale-num" popper-class="scale-num-popper" trigger="click">
      <span class="el-dropdown-link"> {{ num * 100 }}% </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="1">
            放大
            <div class="dropdown-item-right"><i class="iconfont sy-gicon-vuesax-linear-command-square"></i>&nbsp;&nbsp;+&nbsp;&nbsp;<i class="iconfont sy-gicon-plus_app"></i></div>
          </el-dropdown-item>
          <el-dropdown-item command="2">
            缩小
            <div class="dropdown-item-right"><i class="iconfont sy-gicon-vuesax-linear-command-square"></i>&nbsp;&nbsp;+&nbsp;&nbsp;<i class="iconfont sy-gicon-jian1"></i></div>
          </el-dropdown-item>
          <el-dropdown-item command="3" disabled>全览</el-dropdown-item>
          <el-dropdown-item command="4" divided>200%</el-dropdown-item>
          <el-dropdown-item command="5">150%</el-dropdown-item>
          <el-dropdown-item command="6">100%</el-dropdown-item>
          <el-dropdown-item command="7">75%</el-dropdown-item>
          <el-dropdown-item command="8">50%</el-dropdown-item>
          <el-dropdown-item command="8">25%</el-dropdown-item>
          <el-dropdown-item disabled divided>适应屏幕</el-dropdown-item>
          <el-dropdown-item disabled>实际大小</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-button type="info" text :disabled="num >= 4" @click="handleScaleChange(0.25)">
      <i class="iconfont sy-gicon-plus-border"></i>
    </el-button>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, onMounted } from "vue";
import hotkeys from "hotkeys-js";
const props = defineProps<{
  num: number;
}>();
const emit = defineEmits(["change"]);
onMounted(() => {
  hotkeys("command-=", { splitKey: "-" }, (event) => {
    event.preventDefault();
    handleScaleChange(0.25);
  });
  hotkeys("command+-", (event) => {
    event.preventDefault();
    handleScaleChange(-0.25);
  });
});
const handleCommand = (command: string) => {
  switch (command) {
    case "1":
      handleScaleChange(0.25);
      break;
    case "2":
      handleScaleChange(-0.25);
      break;
    case "3":
      break;
    case "4":
      emit("change", 2);
      break;
    case "5":
      emit("change", 1.5);
      break;
    case "6":
      emit("change", 1);
      break;
    case "7":
      emit("change", 0.75);
      break;
    case "8":
      emit("change", 0.5);
      break;
    case "9":
      emit("change", 0.25);
      break;
    default:
      break;
  }
};
const handleScaleChange = (num: number) => {
  const sum = props.num + num;
  if (sum < 0.25 || sum > 4) {
    return;
  }
  emit("change", sum);
};
</script>
<style lang="less">
.scale-num-popper {
  width: 170px;
  .el-dropdown-menu__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }
}
</style>
<style lang="less" scoped>
.dropdown-item-right {
  display: flex;
}
.scale-div {
  width: 170px;
  display: flex;
  justify-content: center;
  height: 100%;
  align-items: center;
  .el-button {
    padding: 5px 10px;
    .iconfont {
      font-size: 24px;
    }
  }
  .scale-num {
    font-size: 14px;
    font-weight: 500;
    color: #303233;
    text-align: center;
    padding: 0 15px;
  }
}
</style>
