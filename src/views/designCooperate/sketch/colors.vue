<template>
  <div @click.stop class="colors-div" :class="{ show: store.colorsVisible }">
    <ul class="colors-wrapper">
      <template v-for="(item, index) in colors" :key="index">
        <li class="colors-item">
          <div class="colors-show" :style="{ background: item.color['css-rgba'] }"></div>
          <div class="colors-name">
            <b>{{ item.name }}</b>
            <div class="colors-tips">{{ item.color[colorFormat] }}</div>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, watch } from "vue";
import { sketchStore } from "@/store";
const store = sketchStore();
const props = defineProps<{
  colors: any[];
  colorFormat: string;
}>();
watch(
  () => props.colors,
  (slices) => {
    console.log(slices);
  }
);
</script>

<style lang="less" scoped>
.colors-div {
  width: 340px;
  height: 100%;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  transition: transform 0.3s ease-in-out;
  transform: translateX(100%);
  padding: 0 20px;
  &.show {
    transform: translateX(0);
  }
  .colors-item {
    display: flex;
    margin-top: 20px;
    align-items: center;
    .colors-show {
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }
    .colors-name {
      padding-left: 10px;
      .colors-tips {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
