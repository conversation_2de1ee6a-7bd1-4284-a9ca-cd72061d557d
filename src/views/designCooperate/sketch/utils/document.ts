import { distance, getEdgeRect, getEventTarget } from "./helper";
import { sketchStore } from "@/store";
import { panMode } from "./window";

export function hideDistance() {
  ["#td", "#rd", "#bd", "#ld"].forEach((s) => {
    const el = document.querySelector(s) as HTMLElement;
    if (el !== null) {
      el.style.display = "none";
    }
  });
  document.querySelector(".selected")?.classList.remove("hidden");
}
function mouseoverLayer() {
  const { state } = sketchStore();
  if (state.targetIndex && state.selectedIndex == state.targetIndex) return false;
  const target = document.querySelector("#layer-" + state.targetIndex) as HTMLElement;
  target.classList.add("hover");
  const rv = document.querySelector("#rv") as HTMLElement;
  rv.style.left = target.offsetLeft + "px";
  rv.style.width = target.offsetWidth + "px";
  const rh = document.querySelector("#rh") as HTMLElement;
  rh.style.top = target.offsetTop + "px";
  rh.style.height = target.offsetHeight + "px";
  const rulersHtml: any = document.querySelector("#rulers");
  if (rulersHtml !== null) {
    rulersHtml.style.display = "";
  }
}

export function mouseoutLayer() {
  document.querySelector(".hover")?.classList.remove("hover");
  const rulersHtml: any = document.querySelector("#rulers");
  if (rulersHtml !== null) {
    rulersHtml.style.display = "none";
  }
}
export function selectedLayer() {
  const { state } = sketchStore();
  if (state.selectedIndex == undefined) return false;
  document.querySelector(".selected")?.classList.remove("selected");
  document.querySelector("#layer-" + state.selectedIndex)?.classList.add("selected");
  const rulersHtml: any = document.querySelector("#rulers");
  if (rulersHtml !== null) {
    rulersHtml.style.display = "none";
  }
}

function removeSelected() {
  const { state } = sketchStore();
  if (state.selectedIndex === undefined) return false;
  document.querySelector("#layer-" + state.selectedIndex)?.classList.remove("selected");
  const rulersHtml: any = document.querySelector("#rulers");
  if (rulersHtml !== null) {
    rulersHtml.style.display = "none";
  }
  document.querySelector("#inspector")?.classList.remove("active");
  state.selectedIndex = undefined;
  state.tempTargetRect = undefined;
  hideDistance();
}
export const documentClickEvent = function (event) {
  const store = sketchStore();
  if (panMode) return;
  if (getEventTarget(document.body, event, ".sketch-nav,.sketch-artboards")) {
    event.stopPropagation();
    return;
  }
  store.slicesVisible = false;
  store.historyVisible = false;
  store.colorsVisible = false;
  const target = event.target as HTMLElement;
  if (target.classList.contains("layer") || target.classList.contains("slices-layer")) {
    const selected = !target.classList.contains("slices-layer")
      ? target
      : (document.querySelector(".layer-" + target.dataset.objectid) as HTMLElement);
    store.state.selectedIndex = parseInt(selected.dataset.index!);

    hideDistance();
    mouseoutLayer();
    selectedLayer();
    return;
  }
  removeSelected();
};

export const documentMouseMoveEvent = function (event) {
  if (panMode) return;
  const { state } = sketchStore();
  mouseoutLayer();
  hideDistance();
  const target = event.target as HTMLElement;
  if (
    target.classList.contains("screen-viewer") ||
    target.classList.contains("screen-viewer-inner")
  ) {
    state.tempTargetRect = getEdgeRect(event);
    state.targetIndex = 0;
    distance();
  } else if (target.classList.contains("layer")) {
    state.targetIndex = parseInt((event.target as HTMLElement).dataset.index!);
    state.tempTargetRect = undefined;
    mouseoverLayer();
    distance();
  } else {
    state.tempTargetRect = undefined;
  }
};
