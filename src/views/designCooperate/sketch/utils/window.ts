import { hideDistance, mouseoutLayer } from "./document";
export let panMode = false;
let moving = false;
let moveData: any;
export function windowKeyDownEvent(event) {
  if (event.which !== 32) return;
  const cursor: any = document.getElementById("cursor");
  const screenViewer: any = document.querySelector(".screen-viewer");
  cursor.style.display = "";
  screenViewer.classList.add("moving-screen");
  mouseoutLayer();
  hideDistance();
  panMode = true;
  event.preventDefault();
}

export function windowKeyUpEvent(event) {
  if (event.which !== 32) return;
  const cursor: any = document.getElementById("cursor");
  const screenViewer: any = document.querySelector(".screen-viewer");
  cursor.style.display = "none";
  cursor.classList.remove("moving");
  screenViewer.classList.remove("moving-screen");
  panMode = false;
  moving = false;
  event.preventDefault();
}

export function windowMouseMoveEvent(event) {
  const cursor: any = document.getElementById("cursor");
  if (cursor !== null) {
    cursor.style.left = event.clientX + "px";
    cursor.style.top = event.clientY - 48 + "px";
  }
  if (!moving) return;
  const viewer: any = document.querySelector(".screen-viewer");
  viewer.scrollLeft = moveData.x - event.clientX + moveData.scrollLeft;
  viewer.scrollTop = moveData.y - event.clientY + moveData.scrollTop;
  event.preventDefault();
}
export function windowMouseDownEvent(event) {
  if (!panMode) return;
  const cursor: any = document.getElementById("cursor");
  const viewer: any = document.querySelector(".screen-viewer");
  cursor.classList.add("moving");
  moveData = {
    x: event.clientX,
    y: event.clientY,
    scrollLeft: viewer.scrollLeft,
    scrollTop: viewer.scrollTop
  };
  moving = true;
  event.preventDefault();
}
export function windowMouseUpEvent(event) {
  if (!panMode || !moving) return;
  const cursor: any = document.getElementById("cursor");
  const viewer: any = document.querySelector(".screen-viewer");
  viewer.classList.remove("moving-screen");
  cursor.classList.remove("moving");
  moving = false;
  event.preventDefault();
}
