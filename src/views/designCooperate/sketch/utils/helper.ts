import { sketchStore } from "@/store";
import { SMRect } from "../model/interfaces";
import { Edge } from "../model";

export function getEventTarget(eventNode: Element, event: Event, selector: string): any {
  let current: any = event.target as HTMLElement;
  while (current && current !== eventNode) {
    if (current.matches(selector)) return current;
    current = current.parentElement;
  }
  return undefined;
}

export function unitSize(value: number, isText?: boolean) {
  const { state, project } = sketchStore();
  // logic point
  const pt = value / project.resolution;
  // convert to display value
  const sz = Math.round(pt * state.scale * 100) / 100;
  const units = state.unit.split("/");
  let unit = units[0];
  if (units.length > 1 && isText) {
    unit = units[1];
  }
  return sz + unit;
}

export function getEdgeRect(event: MouseEvent): SMRect {
  const { state } = sketchStore();
  const screen = document.querySelector("#screen") as HTMLElement;
  const rect = screen.getBoundingClientRect();
  let x = (event.pageX - rect.left) / state.zoom;
  let y = (event.pageY - rect.top) / state.zoom;
  let width = 10;
  let height = 10;
  const xScope = x >= 0 && x <= state.current.width;
  const yScope = y >= 0 && y <= state.current.height;
  // left and top
  if (x <= 0 && y <= 0) {
    x = -10;
    y = -10;
  }
  // right and top
  else if (x >= state.current.width && y <= 0) {
    x = state.current.width;
    y = -10;
  }
  // right and bottom
  else if (x >= state.current.width && y >= state.current.height) {
    x = state.current.width;
    y = state.current.height;
  }
  // left and bottom
  else if (x <= 0 && y >= state.current.height) {
    x = -10;
    y = state.current.height;
  }
  // top
  else if (y <= 0 && xScope) {
    x = 0;
    y = -10;
    width = state.current.width;
  }
  // right
  else if (x >= state.current.width && yScope) {
    x = state.current.width;
    y = 0;
    height = state.current.height;
  }
  // bottom
  else if (y >= state.current.height && xScope) {
    x = 0;
    y = state.current.height;
    width = state.current.width;
  }
  // left
  else if (x <= 0 && yScope) {
    x = -10;
    y = 0;
    height = state.current.height;
  }
  if (xScope && yScope) {
    x = 0;
    y = 0;
    width = state.current.width;
    height = state.current.height;
  }
  return {
    x: x,
    y: y,
    width: width,
    height: height
  };
}
export function scaleSize(length: number) {
  const { state } = sketchStore();
  return Math.round((length / state.scale) * 10) / 10;
}
function getRect(index: number): SMRect {
  const { state } = sketchStore();
  const layer = state.current.layers[index];
  return layer.rect;
}
function getIntersection(a: SMRect, b: SMRect): SMRect | undefined {
  const x1 = Math.max(a.x, b.x);
  const y1 = Math.max(a.y, b.y);
  const x2 = Math.min(a.x + a.width, b.x + b.width);
  const y2 = Math.min(a.y + a.height, b.y + b.height);
  const width = x2 - x1;
  const height = y2 - y1;
  if (width <= 0 || height <= 0) {
    // no intersection
    return undefined;
  }
  return {
    x: x1,
    y: y1,
    width: width,
    height: height
  };
}

export function zoomSize(size: number) {
  const { state, project } = sketchStore();
  return (size * state.zoom) / project.resolution;
}
export function percentageSize(size: number, size2: number) {
  return Math.round((size / size2) * 1000) / 10 + "%";
}
function getDistance(selected: SMRect, target: SMRect) {
  return {
    top: selected.y - target.y,
    right: target.x + target.width - selected.x - selected.width,
    bottom: target.y + target.height - selected.y - selected.height,
    left: selected.x - target.x
  };
}
export function distance() {
  const { state } = sketchStore();
  if (state.selectedIndex === undefined) return;
  if (state.selectedIndex == state.targetIndex && !state.tempTargetRect) return;

  const selectedRect: SMRect = getRect(state.selectedIndex);
  const targetRect: SMRect = state.tempTargetRect || getRect(state.targetIndex);
  let topData;
  let rightData;
  let bottomData;
  let leftData;
  const x = zoomSize(selectedRect.x + selectedRect.width / 2);
  const y = zoomSize(selectedRect.y + selectedRect.height / 2);

  const selectedX2 = selectedRect.x + selectedRect.width;
  const selectedY2 = selectedRect.y + selectedRect.height;
  const targetX2 = targetRect.x + targetRect.width;
  const targetY2 = targetRect.y + targetRect.height;
  if (!getIntersection(selectedRect, targetRect)) {
    if (selectedRect.y > targetY2) {
      //top
      topData = {
        x: x,
        y: zoomSize(targetY2),
        h: zoomSize(selectedRect.y - targetY2),
        distance: unitSize(selectedRect.y - targetY2),
        percentageDistance: percentageSize(selectedRect.y - targetY2, state.current.height)
      };
    }
    if (selectedX2 < targetRect.x) {
      //right
      rightData = {
        x: zoomSize(selectedX2),
        y: y,
        w: zoomSize(targetRect.x - selectedX2),
        distance: unitSize(targetRect.x - selectedX2),
        percentageDistance: percentageSize(targetRect.x - selectedX2, state.current.width)
      };
    }
    if (selectedY2 < targetRect.y) {
      //bottom
      bottomData = {
        x: x,
        y: zoomSize(selectedY2),
        h: zoomSize(targetRect.y - selectedY2),
        distance: unitSize(targetRect.y - selectedY2),
        percentageDistance: percentageSize(targetRect.y - selectedY2, state.current.height)
      };
    }
    if (selectedRect.x > targetX2) {
      //left
      leftData = {
        x: zoomSize(targetX2),
        y: y,
        w: zoomSize(selectedRect.x - targetX2),
        distance: unitSize(selectedRect.x - targetX2),
        percentageDistance: percentageSize(selectedRect.x - targetX2, state.current.width)
      };
    }
  } else {
    const distance = getDistance(selectedRect, targetRect);
    if (distance.top != 0) {
      //top
      topData = {
        x: x,
        y: distance.top > 0 ? zoomSize(targetRect.y) : zoomSize(selectedRect.y),
        h: zoomSize(Math.abs(distance.top)),
        distance: unitSize(Math.abs(distance.top)),
        percentageDistance: percentageSize(Math.abs(distance.top), state.current.height)
      };
    }
    if (distance.right != 0) {
      //right
      rightData = {
        x: distance.right > 0 ? zoomSize(selectedX2) : zoomSize(targetX2),
        y: y,
        w: zoomSize(Math.abs(distance.right)),
        distance: unitSize(Math.abs(distance.right)),
        percentageDistance: percentageSize(Math.abs(distance.right), state.current.width)
      };
    }
    if (distance.bottom != 0) {
      //bottom
      bottomData = {
        x: x,
        y: distance.bottom > 0 ? zoomSize(selectedY2) : zoomSize(targetY2),
        h: zoomSize(Math.abs(distance.bottom)),
        distance: unitSize(Math.abs(distance.bottom)),
        percentageDistance: percentageSize(Math.abs(distance.bottom), state.current.height)
      };
    }
    if (distance.left != 0) {
      //left
      leftData = {
        x: distance.left > 0 ? zoomSize(targetRect.x) : zoomSize(selectedRect.x),
        y: y,
        w: zoomSize(Math.abs(distance.left)),
        distance: unitSize(Math.abs(distance.left)),
        percentageDistance: percentageSize(Math.abs(distance.left), state.current.width)
      };
    }
  }
  if (topData) {
    const td = document.querySelector("#td") as HTMLElement;
    td.style.left = topData.x + "px";
    td.style.top = topData.y + "px";
    td.style.height = topData.h + "px";
    td.style.display = "";
    const tdDiv = document.querySelector("#td div") as HTMLElement;
    tdDiv.setAttribute("data-height", topData.distance);
    tdDiv.setAttribute("percentage-height", topData.percentageDistance);
  }
  if (rightData) {
    const rd = document.querySelector("#rd") as HTMLElement;
    rd.style.left = rightData.x + "px";
    rd.style.top = rightData.y + "px";
    rd.style.width = rightData.w + "px";
    rd.style.display = "";
    const rdDiv = document.querySelector("#rd div") as HTMLElement;
    rdDiv.setAttribute("data-width", rightData.distance);
    rdDiv.setAttribute("percentage-width", rightData.percentageDistance);
  }
  if (bottomData) {
    const bd = document.querySelector("#bd") as HTMLElement;
    bd.style.left = bottomData.x + "px";
    bd.style.top = bottomData.y + "px";
    bd.style.height = bottomData.h + "px";
    bd.style.display = "";
    const bdDiv = document.querySelector("#bd div") as HTMLElement;
    bdDiv.setAttribute("data-height", bottomData.distance);
    bdDiv.setAttribute("percentage-height", bottomData.percentageDistance);
  }
  if (leftData) {
    const ld = document.querySelector("#ld") as HTMLElement;
    ld.style.left = leftData.x + "px";
    ld.style.top = leftData.y + "px";
    ld.style.width = leftData.w + "px";
    ld.style.display = "";
    const ldDiv = document.querySelector("#ld div") as HTMLElement;
    ldDiv.setAttribute("data-width", leftData.distance);
    ldDiv.setAttribute("percentage-width", leftData.percentageDistance);
  }
  const selectedData: any = document.querySelector(".selected");
  if (selectedData !== null) {
    selectedData.classList.add("hidden");
  }
}

export function alignElement(options: {
  scroller: HTMLElement;
  target: HTMLElement;
  fromRect?: DOMRect;
  toRect: DOMRect;
  fromEdge: Edge;
  toEdge: Edge;
}): void {
  const fromRect = options.fromRect || options.target.getBoundingClientRect();
  const from = options.fromEdge;
  const to = options.toEdge;
  const fromHasV = !!(0b111000 & from);
  const toHasV = !!(0b111000 & to);
  const fromHasH = !!(0b000111 & from);
  const toHasH = !!(0b000111 & to);
  let offsetX = 0;
  let offsetY = 0;
  if (fromHasH && toHasH) {
    offsetX = options.toRect.x - fromRect.x; // left-to-left offset
    if (from & Edge.hcenter) offsetX -= fromRect.width / 2;
    if (from & Edge.hright) offsetX -= fromRect.width;
    if (to & Edge.hcenter) offsetX += options.toRect.width / 2;
    if (to & Edge.hright) offsetX += options.toRect.width;
  }
  if (fromHasV && toHasV) {
    offsetY = options.toRect.y - fromRect.y; // top-to-top offset
    if (from & Edge.vmiddle) offsetY -= fromRect.height / 2;
    if (from & Edge.vbottom) offsetY -= fromRect.height;
    if (to & Edge.vmiddle) offsetY += options.toRect.height / 2;
    if (to & Edge.vbottom) offsetY += options.toRect.height;
  }
  options.scroller.scrollTop -= offsetY;
  options.scroller.scrollLeft -= offsetX;
}
