<template>
  <div class="unit-div">
    <el-dropdown trigger="click" @command="handleUnitChange">
      <span class="unit-div-link">
        {{ curUnit?.name || "请选择" }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <template v-for="(item, index) in unitsData" :key="item.name">
            <el-dropdown-item disabled :divided="index != 0">{{ item.name }}</el-dropdown-item>
            <template v-for="(unit, i) in item.units" :key="unit.name">
              <el-dropdown-item :command="unit" :divided="i == 0">{{ unit.name }}</el-dropdown-item>
            </template>
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script lang="ts" setup>
import { ArrowDown } from "@element-plus/icons-vue";
import { localize } from "./model";
import { computed } from "vue";
import { sketchStore } from "@/store";

const store = sketchStore();

const unitsData = [
  {
    name: localize("Device switch"),
    units: [
      { name: localize("Web View") + " - px", unit: "px", scale: 1 },
      { name: localize("iOS Devices") + " - pt", unit: "pt", scale: 1 },
      { name: localize("Android Devices") + " - dp/sp", unit: "dp/sp", scale: 1 }
    ]
  },
  {
    name: localize("Convert to pixels"),
    units: [
      { name: "IOS " + localize("Points") + " @1x", unit: "px", scale: 1 },
      { name: "IOS " + localize("Retina") + " @2x", unit: "px", scale: 2 },
      { name: "IOS " + localize("Retina HD") + " @3x", unit: "px", scale: 3 },
      { name: "Android LDPI @0.75x", unit: "px", scale: 0.75 },
      { name: "Android MDPI @1x", unit: "px", scale: 1 },
      { name: "Android HDPI @1.5x", unit: "px", scale: 1.5 },
      { name: "Android XHDPI @2x", unit: "px", scale: 2 },
      { name: "Android XXHDPI @3x", unit: "px", scale: 3 },
      { name: "Android XXXHDPI @4x", unit: "px", scale: 4 }
    ]
  },
  {
    name: localize("Convert to others"),
    units: [
      { name: "CSS Rem 8px", unit: "rem", scale: 1 / 8 },
      { name: "CSS Rem 10px", unit: "rem", scale: 1 / 10 },
      { name: "CSS Rem 12px", unit: "rem", scale: 1 / 12 },
      { name: "CSS Rem 14px", unit: "rem", scale: 1 / 14 },
      { name: "CSS Rem 16px", unit: "rem", scale: 1 / 16 },
      { name: localize("Ubuntu Grid"), unit: "gu", scale: 1 / 27 }
    ]
  }
];
const curUnit = computed<any>(() => {
  let cur: any = null;
  if (!store.state.unitName) {
    return unitsData[0].units[0];
  }
  unitsData.find((item) => {
    const data = item.units.find((unit) => {
      return unit.unit == store.state.unit && unit.name == store.state.unitName;
    });
    if (data) {
      cur = data;
      return true;
    }
  });
  return cur;
});
const handleUnitChange = (unit: any) => {
  store.state.unit = unit.unit;
  store.state.scale = unit.scale;
  store.state.unitName = unit.name;
  //   emit("change", unit);
};
</script>
<style lang="less" scoped>
.unit-div {
  height: 100%;
  margin-left: 15px;
  .el-dropdown {
    height: 100%;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
  }
  .unit-div-link {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
</style>
