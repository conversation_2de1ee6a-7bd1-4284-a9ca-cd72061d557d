import { data } from "./i18n/zh-cn";
import { ArtboardData, ExportData } from "./interfaces";
export interface GroupData {
  _id: string;
  name: string;
}
export type ProjectData = ExportData & {
  colorNames: { [key: string]: string };
  group: Partial<GroupData>;
};
export interface State {
  zoom: number;
  unit: string;
  scale: number;
  artboardIndex?: number;
  colorFormat: string;
  current: ArtboardData;
  selectedIndex?: number;
  codeType: string;
  targetIndex: number;
  unitName: string;
  tempTargetRect?: any;
}

export function localize(str: string): string {
  return data[str] ? data[str] : str;
}

export enum Edge {
  vtop = 0b100000,
  vbottom = 0b010000,
  vmiddle = 0b001000,
  hleft = 0b000100,
  hright = 0b000010,
  hcenter = 0b000001
}
