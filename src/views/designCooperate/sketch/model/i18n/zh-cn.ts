import { ObjectAny } from "@/types";

export const data: ObjectAny = {
  Coordinate: "坐标",
  Overlay: "高亮区域",
  "Top Width": "宽度-顶部",
  "Middle Width": "宽度-居中",
  "Bottom Width": "宽度-底部",
  "Left Height": "高度-左侧",
  "Center Height": "高度-居中",
  "Right Height": "高度-右侧",
  "Vertical Distance": "垂直间距",
  "Top Spacing": "上边距",
  "Bottom Spacing": "下边距",
  "Horizontal Distance": "水平间距",
  "Left Spacing": "左边距",
  "Right Spacing": "右边距",
  "Label on top": "标记-顶部",
  "Label on right": "标记-右侧",
  "Make Note": "备注",
  "Label on bottom": "标记-底部",
  "Label on left": "标记-左侧",
  Influence: "影响范围",
  "Sizing by influence": "根据影响范围标记尺寸",
  Percentage: "百分比",
  "Sizing by percentage": "标记百分比尺寸",
  "Toggle Hidden": "切换可见",
  "Toggle Locked": "切换锁定",
  "Clean Marks": "清除选定区域或全部标注",
  Settings: "设置",
  "Design resolution": "设计分辨率",
  "Unit switch": "切换单位",
  "Device switch": "切换设备",
  "Convert to pixels": "转换为像素值",
  "Convert to rem": "转换为 rem 值",
  FLOW: "原型模式",
  NOTES: "备注",
  PROPERTIES: "属性",
  FILLS: "填充",
  TYPEFACE: "字体",
  BORDERS: "边框",
  SHADOWS: "阴影",
  "CSS STYLE": "CSS 样式",
  "CODE TEMPLATE": "代码模板",
  EXPORTABLE: "切图",
  Gradient: "渐变",
  Color: "颜色",
  "Layer Name": "图层名称",
  Weight: "粗细",
  "Style name": "样式名称",
  Custom: "自定义",
  Standard: "标准像素",
  "iOS Devices": "iOS 设备",
  Points: "标准点",
  Retina: "视网膜",
  "Retina HD": "高清视网膜",
  "Android Devices": "安卓设备",
  "Other Devices": "其他设备",
  "Ubuntu Grid": "Ubuntu 网格",
  "Web View": "网页",
  Scale: "倍率",
  Units: "单位",
  "Device Unit": "设备单位",
  "Design Resolution": "设计分辨率",
  "%@px on Sketch = 1%@ on device": "%@px Sketch = 1%@ 设备",
  "Color format": "颜色格式",
  "Color hex": "色值",
  "ARGB hex": "安卓色值",
  "Artboard order": "画板排序",
  "Order by artboard rows": "按画板行排序",
  "Order by artboard columns": "按画板列排序",
  "Order by alphabet": "按名称排序",
  "Order by layer order": "按图层顺序",
  Positive: "正序",
  Reverse: "逆序",
  Save: "保存",
  Width: "宽度",
  Height: "高度",
  Top: "上面",
  Right: "右侧",
  Bottom: "下面",
  Left: "左侧",
  "Fill / Color": "填充 / 颜色",
  Border: "边框",
  Opacity: "不透明度",
  Radius: "圆角",
  Shadow: "外(内)阴影",
  Style: "样式名称",
  "Font size": "字号",
  Line: "行高",
  Typeface: "字体",
  Character: "字间距",
  Paragraph: "段落间距",
  "Percentage of artboard": "基于画板百分比单位",
  Mark: "标注",
  All: "全选",
  None: "全不选",
  "Select filtered": "选中过滤的",
  "Selection of Sketch": "Sketch 的选中画板",
  "Current of Sketch": "Sketch 的当前画板",
  Filter: " 过滤",
  Export: "导出",
  Position: "位置",
  Size: "大小",
  Family: "字体",
  Spacing: "空间",
  Content: "内容",
  "All artboards": "全部画板",
  "Start points": "起点画板",
  "No slices added!": "未添加切图",
  "No color names added!": "未添加颜色名称",
  "Select 1 or 2 layers to mark!": "请选中 1 至 2 个图层!",
  "Select a text layer to mark!": "请选中 1 个文本图层!",
  "Select a layer to mark!": "请选中 1 个图层!",
  "Select any layer to mark!": "请选中任意个图层!",
  "Export spec": "导出规范",
  "Export to:": "导出到:",
  "Exporting...": "导出中...",
  "Please wait for former task to exit": "请等待先前的任务完成",
  "Cancelled by user": "用户取消了任务",
  "Export complete! Takes %@ seconds": "导出完成! 耗时 %@ 秒",
  "The slice not in current artboard.": "切图不在当前画板",
  "Inside Border": "内边框",
  "Outside Border": "外边框",
  "Center Border": "中心边框",
  "Inner Shadow": "内阴影",
  "Outer Shadow": "外阴影",
  Offset: "偏移",
  Effect: "效果",
  Blur: "模糊",
  Spread: "扩散",
  "No artboards!": "没有画板",
  "You need add some artboards.": "您需要添加一些画板",
  "No colors added!": "没有定义颜色",
  "Select a layer to add exportable!": "请选中 1 个图层!",
  "Import complete!": "导入完成!",
  "Processing layer %@ of %@": "图层处理中... %@ / %@",
  "Advanced mode": "高级模式",
  "Export layer influence rect": "导出图层的影响尺寸",
  "Keyboard shortcut": "快捷键"
};
