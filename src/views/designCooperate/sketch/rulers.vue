<template>
  <div id="rulers" style="display: none">
    <div id="rv" class="ruler v"></div>
    <div id="rh" class="ruler h"></div>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="less" scoped>
.ruler {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed #5c54f0;
  box-sizing: border-box;
}

.ruler.h {
  border-left: 0;
  border-right: 0;
}

.ruler.v {
  border-top: 0;
  border-bottom: 0;
}
</style>
