<template>
  <div @click.stop v-loading="loading" class="history-div" :class="{ show: store.historyVisible }">
    <el-timeline>
      <el-timeline-item v-for="(item, index) in history" :key="index">
        <div class="history-item" :class="{ active: id == item._id }" @click="emit('click', item._id)">
          <div class="history-item__user">
            <img :src="item.user.url" alt="" srcset="" />
            <div>{{ item.user.name }}</div>
          </div>
          <div class="history-item__content">
            <div class="slug">{{ item.slug }}</div>
            <div class="time">{{ getTime(item.createTime) }} <el-tag type="danger" v-if="item.isDeleted">已删除</el-tag></div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, defineEmits } from "vue";
import { sketchStore } from "@/store";
const store = sketchStore();
defineProps<{
  history: any[];
  id: string;
  loading: boolean;
}>();
const emit = defineEmits(["click"]);
const getTime = (time: number) => {
  return new Date(time).toLocaleString();
};
</script>

<style lang="less" scoped>
.history-div {
  height: 100%;
  background: #fff;
  position: absolute;
  width: 340px;
  box-sizing: border-box;
  top: 0;
  right: 0;
  z-index: 999;
  padding: 20px;
  transition: transform 0.3s ease-in-out;
  transform: translateX(100%);

  &.show {
    transform: translateX(0);
  }
  .history-item {
    background: #f5f5fb;
    border-radius: 4px;
    width: 272px;
    height: 80px;
    cursor: pointer;
    display: flex;
    padding: 10px;
    box-sizing: border-box;
    font-size: 12px;
    &.active,
    &:hover {
      background: #f0f0f0;
    }
    &__user {
      width: 80px;
      text-align: center;
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
      }
      div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    &__content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .slug {
        font-weight: 500;
        font-size: 14px;
      }
      .time {
        font-size: 12px;
        color: #333;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
