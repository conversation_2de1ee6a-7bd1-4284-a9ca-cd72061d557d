<template>
  <RouterView />
</template>

<script lang="ts" setup>
// import { onMounted } from "vue";
// import { userInfoStore } from "@/store";

// const userInfo = userInfoStore();

// onMounted(async () => {
//   if (!userInfo.syUid) {
//     ssoLogin();
//     return;
//   }
// });
// const ssoLogin = async () => {
//   window.location.href = "/api/user/login?return_url=" + encodeURIComponent(window.location.href);
// };
</script>
