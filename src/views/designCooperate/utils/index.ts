import { getLinkInfo, joinLink, linkInvite } from "@/api/invite";
import { ElMessage, ElMessageBox } from "element-plus";
import { ErrorCode, InviteCategory, Permission, permissionName } from "@/model";
import { ObjectAny } from "@/types";
import { userInfoStore } from "@/store";
import { formatDate } from "@/utils/date";
import useClipboard from "vue-clipboard3";

const { toClipboard } = useClipboard({
  appendToBody: false
});

export const invite = async (iv_id?: string) => {
  if (!iv_id) {
    return;
  }
  try {
    const res = await getLinkInfo({
      iv_id
    });
    if (res.code !== ErrorCode.OK) {
      throw res.msg;
    }
    const { resource, permission, isDeleted } = res.data;
    if (isDeleted) {
      ElMessage.error("用户已被管理员踢出团队，如有异议请寻找管理员");
      return;
    }
    if (permission) {
      await ElMessageBox.confirm(
        `
          <p>邀请您 加入 <b>${resource.name}</b></p>
          <div style="margin-top: 12px">
              您可在加入后，对其进行 <b>${permissionName[permission]}</b>
          </div>
        `,
        "注意",
        {
          confirmButtonText: "确 认",
          cancelButtonText: "取 消",
          dangerouslyUseHTMLString: true,
          type: "warning"
        }
      );
      await join(iv_id);
    }
    return resource._id;
  } catch (e) {
    ElMessage.error(e || "系统错误！");
  }
};

const join = async (iv_id: string) => {
  const res = await joinLink({
    iv_id
  });
  if (res.code !== ErrorCode.OK) {
    throw res.msg;
  }
  ElMessage.success("接受邀请成功");
};

export const handleShare = async (
  team: ObjectAny,
  permission = Permission.PREVIEW,
  url: string,
  breadcrumb: string[]
) => {
  if (!team) {
    return;
  }
  if (team.permission !== Permission.OWNER && team.permission !== Permission.MANAGE) {
    ElMessage.error("当前角色暂不支持分享");
    return;
  }
  const userInfo = userInfoStore();
  const res = await linkInvite({
    category: InviteCategory.SMB,
    permission,
    resourceId: team._id
  });
  if (res.code == 0) {
    const template = `${window.location.origin + url + "iv_id=" + res.data._id}
  分享人: ${userInfo.name}
  分享信息: ${breadcrumb.join(" >> ")}
  分享权限: ${permissionName[permission]}
  链接有效期至 ${formatDate(res.data.expiredTime, "yyyy-MM-dd HH:mm:SS")}`;
    await toClipboard(template);
    ElMessageBox.alert(
      `
      <p style="color: #f00;">请注意：不要把链接随意分享给陌生人</p>
      <p>分享人: ${userInfo.name}</p>
      <p>分享信息: ${breadcrumb.join(" >> ")}</p>
      <p>分享类型: ${permissionName[permission]}</p>
      <p>链接有效期至 ${formatDate(res.data.expiredTime, "yyyy-MM-dd HH:mm:SS")}</p>
      `,
      "复制成功",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定"
      }
    );
  }
};

export function findItem(list: ObjectAny[], target: any, key: string) {
  for (let i = 0; i < list.length; i++) {
    const element = list[i];
    if (element[key] === target) {
      return element;
    }
    if (element.children) {
      const res = findItem(element.children, target, key);
      if (res) {
        return res;
      }
    }
  }
}
