import { ObjectAny } from "@/types";

export interface Project {
  name: string;
  _id: string;
  describe: string;
  teamId: string;
}
export interface Group {
  name: string;
  _id: string;
  describe: string;
  projectId: string;
  subs: any[];
  count: number;
}
export interface Sketch {
  artboard: ObjectAny;
  name: string;
  resolution: number;
  unit: string;
  colorFormat: string;
  slices: any[];
  colors: any[];
  _id: string;
  groupId: string;
}

export interface LineType {
  display: boolean;
  position: string;
  origin: string;
  lineLength: string;
}
