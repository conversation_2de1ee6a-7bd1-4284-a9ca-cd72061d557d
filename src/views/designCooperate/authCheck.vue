<template>
  <div v-if="userInfo.ssoId && pageShow" :class="['home']">
    <!-- <Header /> -->
    <div class="home-header">
      <img src="https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png" alt="" />
    </div>
    <div class="authCheck">
      <div class="authCheck-content">
        <div class="authCheck-title">您正在登录 SoYoung 画廊 Sketch 插件</div>
        <div class="authCheck-card">
          <div class="authCheck-card-img">
            <img :src="userInfo.url" alt="" />
          </div>
          <div class="authCheck-card-name">
            {{ userInfo.name }}
          </div>
          <el-button @click="handleChecked" v-loading="loading" :disabled="countDown !== null" color="#626aef" size="large">登录</el-button>
          <el-button @click="handleLogout" size="large">切换账号</el-button>
          <div v-if="countDown !== null" class="authCheck-card-countdown">{{ countDown }}s后自动跳转</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setUserLogout, userChecked } from "@/api/login";
import { onMounted, ref } from "vue";
import { userInfoStore } from "@/store";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
const userInfo = userInfoStore();

const route = useRoute();
const router = useRouter();
const pageShow = ref<boolean>(false);
const loading = ref<boolean>(false);
const countDown = ref<number | null>(null);
onMounted(() => {
  const { token, from } = route.query;
  if (from === "gallery-sketch" && token) {
    pageShow.value = true;
  }
});
const handleLogout = async () => {
  await setUserLogout();
  userInfo.clearInfo();
  ElMessage({
    type: "success",
    message: "退出成功"
  });
};

const handleChecked = async () => {
  const { token } = route.query;
  if (!token) {
    ElMessage({
      type: "error",
      message: "授权失败"
    });
    return;
  }
  loading.value = true;
  const res = await userChecked({
    token
  });
  loading.value = false;
  if (res.code !== 0) {
    ElMessage({
      type: "error",
      message: res.msg || "授权失败"
    });
    return;
  }
  countDown.value = 5;
  getCountDown();
};
let timer;
const getCountDown = () => {
  timer && clearTimeout(timer);
  timer = setTimeout(() => {
    (countDown.value as number)--;
    if (countDown.value === 0) {
      clearTimeout(timer);
      countDown.value = null;
      // window.open("sketch://");
      router.push({
        path: "/"
      });
      return;
    }
    getCountDown();
  }, 1000);
};
</script>
<style lang="less" scoped>
.home {
  width: 100%;
  display: flex;
  flex-direction: column;
  &::-webkit-scrollbar {
    display: none;
  }
  &.home-theme {
    background: #26282b;
  }
  &-header {
    height: 48px;
    padding-left: 20px;
    img {
      height: 38px;
      margin-top: 5px;
    }
  }
  .authCheck {
    height: calc(100vh - 48px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: -30px;
    &-title {
      font-size: 18px;
      font-weight: 500;
      font-family: PingFangSC-Medium;
      color: #131336;
      letter-spacing: 0.74px;
    }
    &-card {
      margin-top: 20px;
      border-radius: 4px;
      background-color: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 30px 0 60px;
      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
      &-img {
        width: 80px;
        height: 80px;
        overflow: hidden;
        border-radius: 50%;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      &-name {
        margin-top: 20px;
        font-size: 14px;
        font-weight: 500;
        font-family: PingFangSC-Medium;
        color: #131336;
      }
      .el-button {
        width: 240px;
        margin: 10px 0;
      }
    }
  }
}
</style>
