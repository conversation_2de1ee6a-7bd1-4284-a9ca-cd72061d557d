<template>
  <div class="sketch-detail">
    <header class="sketch-nav">
      <div class="nav-left">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item v-if="team" replace :to="indexPath">{{ team?.name }}</el-breadcrumb-item>
          <el-breadcrumb-item v-if="project" replace :to="projectPath">{{ project?.name }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ activeSketch ? activeSketch.name : "" }}</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- <el-button class="nav-button" @click="router.back()" type="info" text> <i class="iconfont icon-zuojiantou"></i></el-button> -->
        <!-- {{ route.query.name }} -  -->
        <!-- {{ activeSketch ? activeSketch.name : "" }} -->
      </div>
      <ul class="sketch-nav-menu">
        <li>
          <el-button @click="historyHandler" class="nav-button" type="info" text> <img src="https://static.soyoung.com/sy-design/7x8p5l27n6np1706495684052.png" alt="" /></el-button>
        </li>
        <li>
          <el-button @click.stop="store.colorsVisible = !store.colorsVisible" class="nav-button" type="info" text><img src="https://static.soyoung.com/sy-pre/2own6t3xax3eb-1717146600634.png" alt="" /> </el-button>
        </li>
        <li>
          <el-button @click.stop="store.slicesVisible = !store.slicesVisible" class="nav-button" type="info" text> <img src="https://static.soyoung.com/sy-design/9j6t50seztbc1706495684053.png" alt="" /></el-button>
        </li>
        <li>
          <!-- 缩放组件 -->
          <Scale @change="handleScale" :num="store.state.zoom" />
        </li>
      </ul>
      <div class="nav-right">
        <!--        <Notify />-->
        <el-button v-if="activeSketch" style="margin-right: 25px; width: 60px" type="primary" @click="share">分享</el-button>
        <Shortcut type="detail" />
        <Unit />
      </div>
    </header>
    <main v-if="permission">
      <ArtBoards :active-sketch-id="activeSketchId" :group="store.group" :group-data-list="groupDataList" @change="handleArtBoardsChange" />
      <Slices @copy="toCopy" :slices="store.project.slices" @close="store.slicesVisible = false" />
      <Colors :color-format="store.state.colorFormat" :colors="store.project.colors" @close="store.colorsVisible = false" />
      <Screen />
      <History :id="activeSketchId" :loading="historyLoading" :history="historyList" @click="handleArtBoardsChange" />
      <Inspector @copy="toCopy" />
      <div id="cursor" class="cursor" style="display: none"></div>
    </main>
    <div v-else class="board-placeholder">
      <el-empty description="暂无该项目权限，请联系项目管理员添加" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import Scale from "./sketch/scale.vue";
import Unit from "./sketch/unit.vue";
import Shortcut from "./components/shortcut.vue";
import Slices from "./sketch/slices.vue";
import Colors from "./sketch/colors.vue";
import ArtBoards from "./sketch/artboards.vue";
import Screen from "./sketch/screen.vue";
import History from "./sketch/history.vue";
import Inspector from "./sketch/inspector.vue";
import Notify from "./components/notify.vue";
import { getSketchDetailById, getSketchGroupList, getSketchHistory, getProjectDetailById } from "@/api/design";
import { useRoute, useRouter } from "vue-router";
import { sketchStore, smbStore } from "@/store";
import hotkeys from "hotkeys-js";
import { documentClickEvent, documentMouseMoveEvent } from "./sketch/utils/document";
import { windowKeyDownEvent, windowKeyUpEvent, windowMouseMoveEvent, windowMouseDownEvent, windowMouseUpEvent } from "./sketch/utils/window";
import useClipboard from "vue-clipboard3";
import { ElNotification, ElMessage } from "element-plus";
import { ArrowRight } from "@element-plus/icons-vue";
import { invite } from "./utils";
import { ObjectAny } from "@/types";
import { Permission } from "@/model";
import { handleShare } from "./utils";
const { toClipboard } = useClipboard({
  appendToBody: false
});
const router = useRouter();
let notifytion: any = null;
const toCopy = async (text: string) => {
  if (notifytion) {
    notifytion.close();
  }
  await toClipboard(text);
  notifytion = ElNotification({
    title: "复制成功",
    type: "success"
  });
};
const store = sketchStore();
const smbInfo = smbStore();
// 切片弹窗
const route = useRoute();
const groupDataList = ref<any>([]);
const historyList = ref<any[]>([]);
const historyLoading = ref<boolean>(false);
const project = ref<ObjectAny | null>(null);
const activeSketchId = ref("");
const teamId = ref<string>("");
const team = computed(() => {
  return smbInfo.teamList.find((item) => item._id == teamId.value);
});

const indexPath = computed(() => {
  return {
    path: "/item/project/index",
    query: {
      teamId: teamId.value
    }
  };
});

const projectPath = computed(() => {
  return {
    path: "/item/project/stage",
    query: {
      teamId: teamId.value,
      projectId: project.value?._id
    }
  };
});
const activeSketch = computed<any>(() => {
  return groupDataList.value.find((item) => {
    return item._id === activeSketchId.value;
  });
});
const permission = computed<Permission | null>(() => {
  if (!team.value) {
    return null;
  }
  return team.value!.permission;
});
const share = () => {
  const breadcrumb = [team.value!.name];
  if (project.value) {
    breadcrumb.push(project.value.name);
  }
  if (activeSketch.value) {
    breadcrumb.push(activeSketch.value.pageName);
    breadcrumb.push(activeSketch.value.name);
  }
  handleShare(team.value!, Permission.PREVIEW, `/#/item/project/detail?id=${route.query.id}&teamId=${teamId.value}&`, breadcrumb);
};
const getSketchDetail = async (id: string, init = false) => {
  const args = {
    id: id
  };
  const res = await getSketchDetailById(args);
  if (res.code == 0) {
    const data = res.data;
    data.artboards = [JSON.parse(data.artboard)];
    data.slices = JSON.parse(data.slices);
    data.colors = JSON.parse(data.colors);
    store.initState(data);
    if (init) {
      getGroupListById(data.groupId);
      getProjectById(data.projectId);
    }
  }
};

const getProjectById = async (id: string) => {
  const res = await getProjectDetailById({
    id
  });
  if (res.code == 0) {
    project.value = res.data;
  }
};
const historyHandler = async () => {
  store.historyVisible = true;
  historyLoading.value = true;
  try {
    const args = {
      id: activeSketch.value.artId,
      groupId: route.query.groupId
    };
    const res = await getSketchHistory(args);
    if (res.code == 0) {
      const data = res.data;
      historyList.value = data;
    }
  } catch (e) {
    ElMessage.error((e as any).message);
  } finally {
    historyLoading.value = false;
  }
};
const getGroupListById = async (id: string) => {
  const args = {
    groupId: id
  };
  const res = await getSketchGroupList(args);
  if (res.code == 0) {
    groupDataList.value = res.data;
  }
};
// 切换画板
const handleArtBoardsChange = (id: string) => {
  if (id == activeSketchId.value) {
    return;
  }
  activeSketchId.value = id;
  getSketchDetail(id);
};
///  大小
const handleScale = (num: number) => {
  if (num < 0.25 || num > 4) {
    return;
  }
  store.state.zoom = num;
};

onMounted(() => {
  init();
  layerEvents();
});

const init = async () => {
  const query = route.query as ObjectAny;
  if (!query.id) {
    return;
  }
  if (query.iv_id) {
    const inviteTeamId = await invite(query.iv_id);
    if (!inviteTeamId) {
      return;
    }
    teamId.value = inviteTeamId;
    router.replace({
      path: route.path,
      query: {
        id: query.id,
        teamId: inviteTeamId
      }
    });
  } else {
    teamId.value = query.teamId;
  }
  await smbInfo.init();
  activeSketchId.value = query.id;
  return getSketchDetail(query.id, true);
};
const setHotKeys = () => {
  hotkeys("option+s", (event) => {
    event.preventDefault();
    store.slicesVisible = !store.slicesVisible;
  });
  hotkeys("option+c", (event) => {
    event.preventDefault();
    store.colorsVisible = !store.colorsVisible;
  });
  hotkeys("option+h", (event) => {
    event.preventDefault();
    if (store.historyVisible) {
      store.historyVisible = false;
    } else {
      historyHandler();
    }
  });
};
// const removeHotKeys = () => {
//   hotkeys.unbind("shift+n", () => openGroupDialog());
// };

function layerEvents() {
  document.body.addEventListener("click", documentClickEvent);
  document.body.addEventListener("mousemove", documentMouseMoveEvent);
  window.addEventListener("keydown", windowKeyDownEvent);
  window.addEventListener("keyup", windowKeyUpEvent);
  window.addEventListener("mousemove", windowMouseMoveEvent);
  window.addEventListener("mousedown", windowMouseDownEvent);
  window.addEventListener("mouseup", windowMouseUpEvent);
  setHotKeys();
}
</script>
<style>
.screen-viewer-inner {
  background-color: #f0f2f5;
}
</style>
<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}
.sketch-detail {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  .sketch-nav {
    height: 48px;
    background: #fff;
    display: flex;
    position: relative;
    z-index: 215;
    .nav-left {
      align-items: center;
      padding-left: 15px;
      display: flex;
      font-size: 14px;
      .nav-button {
        margin-right: 10px;
      }
    }
    .nav-button {
      img {
        width: 24px;
        height: 24px;
      }
      .iconfont {
        font-size: 14px;
      }
    }
    .sketch-nav-menu {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      li {
        position: relative;
        padding: 0 15px;
        height: 100%;
        display: inline-flex;
        align-items: center;
        &:not(:last-child) {
          &::after {
            content: "";
            height: 20px;
            width: 1px;
            background-color: #e3e6ec;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
    .nav-right {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-right: 50px;
    }
  }
  main {
    height: calc(100vh - 48px);
    position: relative;
  }
  .cursor {
    position: absolute;
    margin: -8px 0 0 -8px;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 17px;
    background-size: 16px 17px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAiCAMAAAAJbCvNAAABVlBMVEUAAAAGBgYBAQEBAQEAAAAAAAAAAAAnJycAAAA3NzcDAwMNDQ0AAAAAAAAAAAAhISEbGxsYGBgPDw8CAgIDAwMBAQEAAAAAAAAAAAAAAAAAAAAvLy8UFBQCAgIBAQEAAAAAAAAAAAAAAAAAAAA+Pj5BQUE/Pz8zMzMCAgIDAwMDAwMCAgIBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA7OzsAAAAAAAAAAAAAAAD///8AAABhYWEdHR36+vpVVVX9/f329vbz8/Pb29uBgYF0dHRjY2MsLCz39/ft7e3r6+vR0dFxcXFSUlJNTU3X19fKysq4uLh3d3dXV1dJSUlGRkZERETv7+/o6Ojj4+O8vLy1tbW0tLSnp6empqaTk5OKioqGhoZtbW1nZ2dcXFxPT0/Dw8PCwsKwsLCurq6pqamhoaGZmZmNjY16enpeXl5JnG7xAAAAPHRSTlMA/fj9B1EC/jf+/vw8Ew7+/fz85t26mpRoYyX9/OLMoHlKMiL+/f398enY0cC1tLGojouGVh4Z/qpyQy7UG/vLAAACHUlEQVQ4T33QVVdbURBA4bk3QjwQIIa7u9T2QDwQxx2K1+X/v/QhlzRpWN2v51tnZo1IS4ERx4b8J4epaht+5cHpiKzGRfzmxH55R0ftseERZwuIqGGY6xLTLKSNoTlV7W0WY7Z8qjRhflgxADrUOMjcarQJjGgZiqHecBfAuB4Cfb1NwKcHwJ0OdgKc6jWw1S0y5rLXgd18AIqGdtVBGdjpti+r9sTrYqkrBSS1H+BEz4GjnojmvnutQQ79AnxTL8CxFoCkqXnYmakD58AkkPEYADUPQFVDGajOWmsOaxboU4DnEEBN80Dfgoj4og7/pu0QeNQ08NxZ32QPEp41cS6rqvluqT8FX4MJIPcEkHQDn9Qh65rP7le1W/dpKVEAcraArHpTQE71nvZO50TWjAxARU9eAaGwiEtvARJTnQCpNKTPIFEEuNCoiCwYnwGKWYCKG7bGobILcK0+EfHPevcafz4FoboLW9sA9xoQEdmY6Si8gN9BSDZArad+xvf66wVsbTeBMyNiHXrIs9cMOixwp6MW8E9PWmBnG44scOFdlJdi+tMCXQ2QcE/HG8D5Vj8CkO+Axymo7HKoDvmbfd5zA5AoQeYcCqWsLSzNBeZtP2guZ/O3ALEvWlMoveksQ7JH/sk56K6DmjkQTHM8+C+Qof6rg8vLqweNumzHN+7uNhDWeisiMVN1oA3YR10un8+1KSISiI/Z28Dr/QHPa6Em8DfwfAAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    cursor: none;
    z-index: 1999;
  }

  .cursor.show {
    display: block;
  }

  .cursor.moving {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAiCAMAAAAJbCvNAAABMlBMVEUAAAAAAAA2NjYsLCwCAgIAAAAAAAAMDAwBAQECAgIBAQECAgIBAQEBAQEAAAAAAAAAAAA5OTkgICAICAgAAAAcHBwCAgIAAAAAAAAAAAAAAAAAAABDQ0M8PDwxMTE0NDQVFRUCAgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPDw8DAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8AAABhYWEcHBz6+vr39/dMTEzs7Oy8vLy4uLhWVlYqKiooKCj09PTw8PC1tbWnp6d2dnZZWVn8/Pzp6enj4+Pd3d3a2trNzc2tra2pqammpqaKioqAgIBzc3NwcHBmZmZdXV1TU1NHR0c+Pj7W1tbR0dHGxsbAwMCampqSkpKPj4+GhoZycnJsbGwkJCTy91wiAAAANnRSTlMAPP7+/ZcN/Pnr487Kv7QlEv7+/fz71jk2GAcE/v7+/fzzpaCUkGZZQTEsIAn826uEg3JtXFGryOxIAAABkElEQVR4AeXS1XLbQBjF8RXIDHbNdiBpOCnDOa4kg2Oww9w2KUPf/xW6s8q2sTXT6X1+t99/NJozK+6lR8ZmRPzDZpxc3sg9y2Sez3aNl+mUsx23TjpzxbJtmnaqMR08ZO8mGuNn4JA8AU75YiXl5ISW5SXwgTwAUGIfcAuM3thpoRnsAJhnC8DcY0gWj3HBmg5qPAdw1nMBfJ9Aai4AQ1aFllzENNcD2jSEsuOkEvyIEB1k44XrEicI6bCigtXoIbzFN5D2PMAbAG4L0im3VVD2AQyOIF3lgeY88MOC9JXBEEkf2rsHwHtLRup749jtikvTQU8Hflkor/hJB00ZdHUwKKwKJRLP70FRh+7ubXDOLRHY4GU4wEE0I7Q1foPyVh4mQeCay1nxx1OeqWC8K6MF4MrCmK/FX/W0PQKCgfr7gLd/ZDvirkiaX3DXRTE381wzHEFp5UvHQPeJmNFImlD8WOJXH35SzFpZGnba7eFPrleK1yMzEQocBtaEWI+R4aC+Va0YRrWm/jm7Uxf/5zeExWvZghlTSgAAAABJRU5ErkJggg==");
  }
}
.board {
  &-placeholder {
    z-index: 300;
    width: 100vw;
    height: 100vh;
    padding: 50px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
