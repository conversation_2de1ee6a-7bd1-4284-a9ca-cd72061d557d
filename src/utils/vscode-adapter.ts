/**
 * VSCode 环境适配器
 * 用于处理在 VSCode Webview 环境中的特殊逻辑
 */

interface VSCodeAPI {
  postMessage(message: any): void;
  setState(state: any): void;
  getState(): any;
}

declare global {
  interface Window {
    __VSCODE_ENV__?: boolean;
    __VSCODE_API__?: VSCodeAPI;
    acquireVsCodeApi?: () => VSCodeAPI;
  }
}

export class VSCodeAdapter {
  private static instance: VSCodeAdapter;
  private vscodeApi: VSCodeAPI | null = null;

  private constructor() {
    this.init();
  }

  public static getInstance(): VSCodeAdapter {
    if (!VSCodeAdapter.instance) {
      VSCodeAdapter.instance = new VSCodeAdapter();
    }
    return VSCodeAdapter.instance;
  }

  private init() {
    if (this.isVSCodeEnvironment()) {
      try {
        this.vscodeApi = window.__VSCODE_API__ || window.acquireVsCodeApi?.() || null;
        console.log('VSCode environment detected, API initialized');
      } catch (error) {
        console.warn('Failed to initialize VSCode API:', error);
      }
    }
  }

  /**
   * 检测是否运行在 VSCode 环境中
   */
  public isVSCodeEnvironment(): boolean {
    return Boolean(window.__VSCODE_ENV__ || window.acquireVsCodeApi);
  }

  /**
   * 向 VSCode 扩展发送消息
   */
  public postMessage(message: any): void {
    if (this.vscodeApi) {
      this.vscodeApi.postMessage(message);
    } else {
      console.warn('VSCode API not available, message not sent:', message);
    }
  }

  /**
   * 显示信息消息
   */
  public showInfo(message: string): void {
    if (this.isVSCodeEnvironment()) {
      this.postMessage({
        command: 'alert',
        text: message
      });
    } else {
      // 浏览器环境中的替代方案
      console.info(message);
    }
  }

  /**
   * 显示错误消息
   */
  public showError(message: string): void {
    if (this.isVSCodeEnvironment()) {
      this.postMessage({
        command: 'error',
        text: message
      });
    } else {
      // 浏览器环境中的替代方案
      console.error(message);
    }
  }

  /**
   * 保存状态到 VSCode
   */
  public setState(state: any): void {
    if (this.vscodeApi) {
      this.vscodeApi.setState(state);
    }
  }

  /**
   * 从 VSCode 获取状态
   */
  public getState(): any {
    if (this.vscodeApi) {
      return this.vscodeApi.getState();
    }
    return null;
  }

  /**
   * 获取环境信息
   */
  public getEnvironmentInfo() {
    return {
      isVSCode: this.isVSCodeEnvironment(),
      hasVSCodeApi: Boolean(this.vscodeApi),
      userAgent: navigator.userAgent,
      platform: navigator.platform
    };
  }
}

// 创建全局实例
export const vscodeAdapter = VSCodeAdapter.getInstance();
