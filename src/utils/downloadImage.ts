export const downlondImage = (item: any) => {
  const canvas = document.createElement("canvas");
  const ctx: any = canvas.getContext("2d");
  const ratio = window.devicePixelRatio || 1;
  ctx.scale(ratio, ratio);
  const image = new Image();
  image.setAttribute("crossOrigin", "anonymous");
  image.src = item.src;
  image.onload = () => {
    const imgScale = image.width / image.height;
    canvas.style.width = image.width + "px";
    canvas.style.height = image.height + "px";
    canvas.width = image.width;
    canvas.height = image.height;
    ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height);

    const url = canvas.toDataURL("image/png");
    const a = document.createElement("a");
    a.href = url;
    a.download = `${item.name}.png`;
    a.click();
  };
};
