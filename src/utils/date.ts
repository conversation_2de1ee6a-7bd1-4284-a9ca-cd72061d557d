export const formatDate = (date: any = new Date(), rule, utc = false) => {
  if (date.constructor !== Date) {
    date = new Date(date);
  }
  return rule
    .replace(/yyyy|Y/g, utc ? date.getUTCFullYear() : date.getFullYear())
    .replace(/yy|y/g, String(utc ? date.getUTCFullYear() : date.getFullYear()).substr(-2, 2))
    .replace(/MM/g, leftPad(String((utc ? date.getUTCMonth() : date.getMonth()) + 1), 2, "0"))
    .replace(/M/g, (utc ? date.getUTCMonth() : date.getMonth()) + 1)
    .replace(/dd/g, leftPad(String(utc ? date.getUTCDate() : date.getDate()), 2, "0"))
    .replace(/d/g, utc ? date.getUTCDate() : date.getDate())
    .replace(/D/g, utc ? date.getUTCDay() : date.getDay())
    .replace(/HH|hh/g, leftPad(String(utc ? date.getUTCHours() : date.getHours()), 2, "0"))
    .replace(/H|h/g, utc ? date.getUTCHours() : date.getHours())
    .replace(/ms/g, utc ? date.getUTCMilliseconds() : date.getMilliseconds())
    .replace(/mm/g, leftPad(String(utc ? date.getUTCMinutes() : date.getMinutes()), 2, "0"))
    .replace(/m/g, leftPad(String(utc ? date.getUTCMinutes() : date.getMinutes()), 2, "0"))
    .replace(/SS/g, leftPad(String(utc ? date.getUTCSeconds() : date.getSeconds()), 2, "0"))
    .replace(/S/g, utc ? date.getUTCSeconds() : date.getSeconds());
};
const cache = ["", " ", "  ", "   ", "    ", "     ", "      ", "       ", "        ", "         "];

const leftPad = (str, len, ch) => {
  str = str + "";
  len = len - str.length;
  if (len <= 0) return str;
  if (!ch && ch !== 0) ch = " ";
  ch = ch + "";
  if (ch === " " && len < 10) return cache[len] + str;
  let pad = "";
  // eslint-disable-next-line no-constant-condition
  while (true) {
    if (len & 1) pad += ch;
    len >>= 1;
    if (len) ch += ch;
    else break;
  }
  return pad + str;
};

/**
 * 一个通用的轮询函数
 * @param {Function} task - 要执行的异步任务 (async function)
 * @param {number} interval - 轮询间隔（毫秒）
 * @param {number} [maxAttempts] - 最大轮询次数（可选），不传则无限轮询
 * @return {Object} 返回一个对象 { start, stop }
 *   - start()：开始轮询
 *   - stop()：停止轮询
 */
export function createPoller(task, interval, maxAttempts) {
  let attempts = 0;
  let timerId: any = null;
  let stopped = false;

  async function execute() {
    try {
      await task(); // 执行具体的轮询逻辑
    } catch (err) {
      console.error("Polling task error:", err);
      // 根据需要处理错误，是否停止轮询？可视具体情况决定
    }

    attempts++;

    // 如果设置了最大次数，且已达上限，则停止
    if (maxAttempts && attempts >= maxAttempts) {
      stop();
      return;
    }

    // 如果外部调用了 stop()，则不再继续
    if (stopped) return;

    // 间隔 interval 毫秒后再次执行
    timerId = setTimeout(execute, interval);
  }

  function start() {
    if (stopped) {
      // 如果之前被 stop() 过，可以重置状态后重新 start
      stopped = false;
      attempts = 0;
    }
    execute();
  }

  function stop() {
    stopped = true;
    if (timerId) {
      clearTimeout(timerId);
      timerId = null;
    }
  }

  return { start, stop };
}
