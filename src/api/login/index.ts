import axios from "@/api";
import qs from "qs";
import { ErrorCode } from "@/model";
// 获取用户登录信息
export const getUserInfo = async (args) => {
  return await axios.get("/user/loginInfo", {
    params: args
  });
};

// 退出登录
export const setUserLogout = async () => {
  window.location.href = `${window.location.origin}/logout`
  // const res = await axios.get("/logout");
  // const { code, data } = res.data;
  // if (code === ErrorCode.OK) {
  //   if (data.redirect) {
  //     window.location.reload();
  //   }
  // }
};

export const userChecked = async (args) => {
  const res = await axios.get(`/user/check`, {
    params: args
  });
  return res.data;
};

export const userCheckPoling = async (args) => {
  const res = await axios.post("/user/poll", qs.stringify(args));
  return res.data;
};

export const getUserInfoByUid = async (args) => {
  const res = await axios.get(`/user/userInfo`, {
    params: args
  });
  return res.data;
};

export const rename = async (args) => {
  const res = await axios.get(`/user/rename`, {
    params: args
  });
  return res.data;
};
