import axios from "@/api";
import qs from "qs";

export const folderAdd = async (args) => {
  return await axios.get("/folder/add", {
    params: args
  });
};

export const folderNename = async (args) => {
  return await axios.get("/folder/rename", {
    params: args
  });
};

export const moduleList = async (args) => {
  return await axios.get("/apps/moduleList", {
    params: args
  });
};

export const aiGenerated = async (args) => {
  return await axios.get("/generated/g", {
    params: args
  });
};

export const materialAdd = async (args) => {
  return await axios.post("/material/add", qs.stringify(args));
};

export const materialAddAll = async (args) => {
  return await axios.post("/material/addAll", qs.stringify(args));
};
