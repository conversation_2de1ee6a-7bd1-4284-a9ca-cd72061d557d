import axios from "@/api";
import qs from "qs";

export const addInvite = async (args) => {
  const res = await axios.post("/invite/add", qs.stringify(args));
  return res.data;
};

export const linkInvite = async (args) => {
  const res = await axios.post("/invite/link", qs.stringify(args));
  return res.data;
};

export const getLinkInfo = async (args) => {
  const res = await axios.get("/invite/linkInfo", {
    params: args
  });
  return res.data;
};

export const joinLink = async (args) => {
  const res = await axios.get("/invite/join", {
    params: args
  });
  return res.data;
};

export const deleteInvite = async (args) => {
  const res = await axios.get("/invite/delete", {
    params: args
  });
  return res.data;
};

export const updateInvite = async (args) => {
  const res = await axios.get("/invite/update", {
    params: args
  });
  return res.data;
};
