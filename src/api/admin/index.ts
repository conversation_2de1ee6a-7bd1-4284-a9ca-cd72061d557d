import axios from "@/api";
import qs from "qs";
// 上传应用
export const appsAdd = async (args) => {
  return await axios.post("/apps/add", qs.stringify(args));
};

// 上传应用的版本
export const appsAddVersion = async (args) => {
  return await axios.get("/apps/addversion", {
    params: args
  });
};

// 上传模块
export const appsAddModule = async (args) => {
  return await axios.post("/apps/addModule", qs.stringify(args));
};

// 获取分类列表
export const GetCategoryList = async (args) => {
  const res = await axios.get("/category/list", {
    params: args
  });
  return res.data;
};

// 添加分类
export const AddCategory = async (args) => {
  const res = await axios.get("/category/add", {
    params: args
  });
  return res.data;
};

// 编辑分类
export const EditCategory = async (args) => {
  const res = await axios.get("/category/update", {
    params: args
  });
  return res.data;
};
export const GetAppList = async (args) => {
  const res = await axios.get("/apps/list", {
    params: args
  });
  return res.data;
};

// 添加应用
export const AddApp = async (args) => {
  const res = await axios.get("/apps/add", {
    params: args
  });
  return res.data;
};

// 编辑应用
export const EditApp = async (args) => {
  const res = await axios.get("/apps/update", {
    params: args
  });
  return res.data;
};

//
export const GetLverList = async (args) => {
  const res = await axios.get("/apps/lverList", {
    params: args
  });
  return res.data;
};

export const AddLver = async (args) => {
  const res = await axios.get("/apps/addlver", {
    params: args
  });
  return res.data;
};

export const GetModuleList = async (args) => {
  const res = await axios.get("/apps/moduleList", {
    params: args
  });
  return res.data;
};

export const AddModule = async (args) => {
  const res = await axios.post("/apps/addModule", qs.stringify(args));
  return res.data;
};

export const UpdateModule = async (args) => {
  const res = await axios.post("/apps/updateModule", qs.stringify(args));
  return res.data;
};

/**
 * 更新material
 */
export const UpdateMaterial = async (args) => {
  const res = await axios.post("/material/update", qs.stringify(args));
  return res.data;
};

/**
 * 删除material
 */
export const delMaterial = async (args) => {
  const res = await axios.get("/material/delete", {
    params: args
  });
  return res.data;
};
