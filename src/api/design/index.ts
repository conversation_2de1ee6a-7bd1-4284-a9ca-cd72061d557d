import axios from "@/api";
import qs from "qs";

// 获取smb 团队列表
export const getTeamList = async (args) => {
  const res = await axios.get("/smb/team/list", {
    params: args
  });
  return res.data;
};

// 新建团队
export const addTeam = async (args) => {
  const res = await axios.get("/smb/team/add", {
    params: args
  });
  return res.data;
};

export const updateTeam = async (args) => {
  const res = await axios.get("/smb/team/update", {
    params: args
  });
  return res.data;
};

export const getDefFolder = async (args) => {
  const res = await axios.get("/smb/folder/info", {
    params: args
  });
  return res.data;
};
// 添加sketch json 文件
export const addSketch = async (args) => {
  const res = await axios.post("/smb/sketch/add", qs.stringify(args));
  return res.data;
};

export const getProjectList = async (args) => {
  const res = await axios.get("/smb/project/list", {
    params: args
  });
  return res.data;
};

export const addProject = async (args) => {
  const res = await axios.get("/smb/project/add", {
    params: args
  });
  return res.data;
};

export const getGroupList = async (args) => {
  const res = await axios.get("/smb/group/list", {
    params: args
  });
  return res.data;
};
export const addGroup = async (args) => {
  const res = await axios.get("/smb/group/add", {
    params: args
  });
  return res.data;
};

export const deleteGroupById = async (args) => {
  const res = await axios.get("/smb/group/delete", {
    params: args
  });
  return res.data;
};

export const updateGroup = async (args) => {
  const res = await axios.get("/smb/group/update", {
    params: args
  });
  return res.data;
};

export const addSmb = async (args) => {
  const res = await axios.get("/smb/add", {
    params: args
  });
  return res.data;
};
export const getTree = async (args) => {
  const res = await axios.get("/smb/group/tree", {
    params: args
  });
  return res.data;
};

export const getSketchDetailById = async (args) => {
  const res = await axios.get("/smb/sketch/detail", {
    params: args
  });
  return res.data;
};

export const deleteSketchById = async (args) => {
  const res = await axios.get("/smb/sketch/delete", {
    params: args
  });
  return res.data;
};

export const updateSketch = async (args) => {
  const res = await axios.get("/smb/sketch/update", {
    params: args
  });
  return res.data;
};

export const getProjectDetailById = async (args) => {
  const res = await axios.get("/smb/project/detail", {
    params: args
  });
  return res.data;
};

export const getSketchHistory = async (args) => {
  const res = await axios.get("/smb/sketch/history", {
    params: args
  });
  return res.data;
};

export const setSketchPosition = async (args) => {
  const res = await axios.post("/smb/sketch/setPosition", qs.stringify(args));
  return res.data;
};

export const getSketchGroupList = async (args) => {
  const res = await axios.get("/smb/sketch/getSketchGroupList", {
    params: args
  });
  return res.data;
};

export const groupMove = async (args) => {
  const res = await axios.get("/smb/group/move", {
    params: args
  });
  return res.data;
};

export const sketchMove = async (args) => {
  const res = await axios.get("/smb/sketch/move", {
    params: args
  });
  return res.data;
};

// 获取团队结构树
export const getAllProjectTree = async (args) => {
  const res = await axios.get("/smb/project/projectList", {
    params: args
  });
  return res.data;
};

// 获取文件夹
export const getTeamTreeList = async (args) => {
  const res = await axios.get("/smb/team/tree", {
    params: args
  });
  return res.data;
};

// 添加项目文件夹
export const addProjectFolder = async (args) => {
  const res = await axios.get("/smb/folder/add", {
    params: args
  });
  return res.data;
};

// 更新文件夹
export const updateFolder = async (args) => {
  const res = await axios.post("smb/folder/update", args);
  return res.data;
};

// 更新项目状态
export const updateProject = async (args) => {
  const res = await axios.post("smb/project/update", args);
  return res.data;
};

export const sendStatus = async (args) => {
  const res = await axios.post("/smb/sketch/sendStatus", args);
  return res.data;
};

export const getMembers = async (args) => {
  const res = await axios.get("/smb/team/getMembers", {
    params: args
  });
  return res.data;
};

export const searchInfo = async (args) => {
  const res = await axios.get("/search/info", {
    params: args
  });
  return res.data;
};
