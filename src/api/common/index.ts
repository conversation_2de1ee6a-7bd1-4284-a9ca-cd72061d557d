import axios from "@/api";
import qs from "qs";
// 获取素材列表
export const materialList = async (args) => {
  return await axios.get("/material/list", {
    params: args
  });
};

export const materialDetail = async (args) => {
  const res = await axios.get("/material/find", {
    params: args
  });
  return res.data;
};
// 获取应用版本
export const versionList = async (args) => {
  return await axios.get("/apps/lverList", {
    params: args
  });
};

// 文件上传
export const upload = async (args) => {
  return await axios.post("/file/upload", args, {
    "Content-Type": "form-data"
  } as any);
};

// 素材收藏
export const collect = async (args) => {
  return await axios.get("/material/collect", {
    params: args
  });
};
// 获取文件夹列表
export const getfolderList = async (args) => {
  return await axios.get("/folder/list", {
    params: args
  });
};

// 删除文件夹
export const folderDelete = async (args) => {
  return await axios.get("/folder/delete", {
    params: args
  });
};

// 获取应用列表
export const appsList = async (args) => {
  return await axios.get("/apps/list", {
    params: args
  });
};

// 获取模块功能列表
export const appsModuleList = async (args) => {
  return await axios.get("/apps/moduleList", {
    params: args
  });
};

// 分享素材
export const shareMaterial = async (args) => {
  return await axios.get("/material/share", {
    params: args
  });
};
