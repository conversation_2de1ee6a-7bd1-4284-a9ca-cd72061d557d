import axios from "@/api";
import qs from "qs";
export const getStat = async (args) => {
  const res = await axios.get("/notify/stat", {
    params: args
  });
  return res.data;
};

export const getMsgList = async (args) => {
  const res = await axios.get("/notify/msgList", {
    params: args
  });
  return res.data;
};

export const updateMsg = async (args) => {
  const res = await axios.post("/notify/update", qs.stringify(args));
  return res.data;
};
