// import router from "@/router";

import axios from "axios";
const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 60000,
  headers: {
    "Access-Control-Allow-Origin": "*"
  },
  maxBodyLength: Infinity,
  maxContentLength: Infinity
});
// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在这里可以根据业务需求设置请求header，包括token等
    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (res) => {
    // 这里根据业务需求对响应进行处理，可以根据响应状态码进行不同的处理
    return res;
  },
  (err) => {
    return Promise.reject(err);
  }
);

export default instance;
