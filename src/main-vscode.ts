import { createApp } from "vue";
import App from "@/views/app.vue";
import router from "@/router/index";
import VueKonva from "vue-konva";
import { createPinia } from "pinia";
import "element-plus/dist/index.css";
import VueLazyload from 'vue-lazyload';
import { vscodeAdapter } from "@/utils/vscode-adapter";

// VSCode 环境专用的点击外部指令
const clickOutside = {
  beforeMount: (el: any, binding: any) => {
    el.clickOutsideEvent = (event: Event) => {
      if (!(el == event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    // 在 VSCode webview 中使用不同的事件监听策略
    if (vscodeAdapter.isVSCodeEnvironment()) {
      // VSCode webview 环境
      document.addEventListener("click", el.clickOutsideEvent);
    } else {
      // 常规浏览器环境
      document.body.addEventListener("click", el.clickOutsideEvent);
    }
  },
  unmounted: (el: any) => {
    if (vscodeAdapter.isVSCodeEnvironment()) {
      document.removeEventListener("click", el.clickOutsideEvent);
    } else {
      document.body.removeEventListener("click", el.clickOutsideEvent);
    }
  }
};

// 创建 Vue 应用
const app = createApp(App);

// 配置插件
app.use(VueKonva);
app.use(createPinia());
app.use(router);
app.directive("click-outside", clickOutside);

// 配置懒加载（在 VSCode 环境中需要特殊处理）
app.use(VueLazyload, {
  preLoad: 1.3,
  attempt: 1,
  filter: {
    webp(listener: any, options: any) {
      const isCDN = /img2.soyoung.com|static.soyoung.com/;
      const imageView2 = "?imageView2/0";
      if (isCDN.test(listener.src) && !listener.src.includes("?")) {
        if (!options.supportWebp) return;
        listener.src = `${listener.src}${imageView2}/format/webp`;
      }
    }
  }
});

// VSCode 环境初始化
if (vscodeAdapter.isVSCodeEnvironment()) {
  console.log('Initializing in VSCode environment');
  
  // 添加 VSCode 特定的全局错误处理
  window.addEventListener('unhandledrejection', (event) => {
    vscodeAdapter.showError(`未处理的 Promise 错误: ${event.reason}`);
  });
  
  // 添加全局属性，供组件中使用
  app.config.globalProperties.$vscode = vscodeAdapter;
  
  // 提供全局的 VSCode 适配器
  app.provide('vscodeAdapter', vscodeAdapter);
}

// 挂载应用
app.mount("#app");

// 通知 VSCode 扩展应用已加载
if (vscodeAdapter.isVSCodeEnvironment()) {
  vscodeAdapter.showInfo('新氧画廊已成功加载');
  vscodeAdapter.setState({ loaded: true, timestamp: Date.now() });
}
