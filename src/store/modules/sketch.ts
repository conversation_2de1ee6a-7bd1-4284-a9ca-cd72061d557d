import { GroupData, ProjectData, State } from "@/views/designCooperate/sketch/model";
import { ArtboardData } from "@/views/designCooperate/sketch/model/interfaces";
import { defineStore } from "pinia";
import { ref, computed } from "vue";
export const sketchStore = defineStore("sketchStore", () => {
  /* init */
  const state = reactive<State>({
    zoom: 1,
    unit: "px",
    scale: 1,
    artboardIndex: undefined,
    colorFormat: "color-hex",
    current: {} as ArtboardData,
    codeType: "css",
    unitName: "",
    targetIndex: 0
  });
  const initState = (data: ProjectData) => {
    state.scale = 1;
    state.colorFormat = data.colorFormat;
    state.unit = data.unit;
    state.unitName = "";
    state.current = data.artboards[0];
    state.selectedIndex = undefined;
    project.value = data;
    group.value = data.group;
    slicesVisible.value = false;
    colorsVisible.value = false;
    historyVisible.value = false;
  };
  const slicesVisible = ref<boolean>(false);
  const colorsVisible = ref<boolean>(false);
  const historyVisible = ref<boolean>(false);

  const project = ref<ProjectData>(<ProjectData>{});
  const group = ref<Partial<GroupData>>({});

  const curLayer = computed(() => {
    if (!state.current.layers) {
      return null;
    }
    return state.current.layers![state.selectedIndex!];
  });

  watch(
    () => slicesVisible.value,
    (isShow: boolean) => {
      if (isShow) {
        state.selectedIndex = undefined;
        colorsVisible.value = false;
        historyVisible.value = false;
      }
    }
  );
  watch(
    () => colorsVisible.value,
    (isShow: boolean) => {
      if (isShow) {
        state.selectedIndex = undefined;
        slicesVisible.value = false;
        historyVisible.value = false;
      }
    }
  );
  watch(
    () => historyVisible.value,
    (isShow: boolean) => {
      if (isShow) {
        state.selectedIndex = undefined;
        slicesVisible.value = false;
        colorsVisible.value = false;
      }
    }
  );
  return {
    state,
    project,
    group,
    curLayer,
    slicesVisible,
    colorsVisible,
    historyVisible,
    initState
  };
});
