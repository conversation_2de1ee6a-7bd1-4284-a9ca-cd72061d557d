import { defineStore } from "pinia";
import { ref } from "vue";

export const userInfoStore = defineStore("userInfoStore", () => {
  /* init */
  const syUserName = ref<string>(""); // 用户名（取自于新氧）
  const name = ref<string>(""); // 用户名称
  const syUid = ref<string>(""); // 用户ID（取自于新氧）
  const ssoId = ref<string>(""); // ssoId
  const type = ref<string>(""); // 用户权限
  const syData = ref<any>({}); // 用户数据（取自于新氧）
  const url = ref<string>(""); // 用户头像（取自于新氧）
  function updateInfo(payload: any) {
    syUserName.value = payload.syUserName;
    syUid.value = payload.syUid;
    ssoId.value = payload.ssoId;
    type.value = payload.type;
    syData.value = payload.syData;
    url.value = payload.url;
    name.value = payload.name;
  }

  function clearInfo() {
    syUserName.value = "";
    syUid.value = "";
    ssoId.value = "";
    type.value = "";
    syData.value = {};
    url.value = "";
    name.value = "";
  }

  return {
    syUserName,
    syUid,
    ssoId,
    syData,
    url,
    name,
    updateInfo,
    clearInfo
  };
});
