import { defineStore } from "pinia";
import { ref } from "vue";

export const materialStore = defineStore("materialStore", () => {
  const appId = ref<string>(""); // appID
  const versionId = ref<string[]>([]); // versionID
  const tagsId = ref<string[]>([]); // tags
  const source = ref<string>(""); // nav: 导航 it: 其他
  const ids = ref<string[]>([]); // ids集合
  const categoryId = ref<string>("");

  const updateMaterialInfo = (data: any) => {
    ids.value = [];
    appId.value = data._id;
    versionId.value = [];
    tagsId.value = [];
  };

  const updateCategoryId = (id: string) => {
    categoryId.value = id;
    appId.value = "";
  };

  const updateVersionInfo = (ids: string[]) => {
    versionId.value = ids;
  };

  const updateTagsInfo = (ids: string[]) => {
    tagsId.value = ids;
  };

  const getMaterialData = () => {
    return {
      appId: appId.value,
      versionId: versionId.value,
      tagsId: tagsId.value,
      categoryId: categoryId.value
    };
  };

  return {
    appId,
    versionId,
    tagsId,
    source,
    ids,
    categoryId,
    updateCategoryId,
    updateMaterialInfo,
    getMaterialData,
    updateVersionInfo,
    updateTagsInfo
  };
});
