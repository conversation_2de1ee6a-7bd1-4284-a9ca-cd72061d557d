import { getTeamList } from "@/api/design";
import { ObjectAny } from "@/types";
import { defineStore } from "pinia";
import { ref } from "vue";

export const smbStore = defineStore("smbStore", () => {
  const teamList = ref<ObjectAny[]>([]);

  const init = async () => {
    if (!teamList.value.length) {
      return refreshTeamList();
    }
  };
  const refreshTeamList = async () => {
    const res = await getTeamList({});
    teamList.value = res.data;
  };
  return {
    teamList,
    init,
    refreshTeamList
  };
});
