import { defineStore } from "pinia";
import { ref } from "vue";

// 主题模块
export const themeStore = defineStore("themeStore", () => {
  const themeShow = ref<boolean>(
    localStorage.getItem("theme") && localStorage.getItem("theme") == "light" ? false : true
  ); // 控制深浅模式的样式 false: 浅色  true: 深色
  // 用来更新深浅模式的主题样式
  function updateThemeValue(payload: any) {
    themeShow.value = payload;
  }

  return {
    themeShow,
    updateThemeValue
  };
});
