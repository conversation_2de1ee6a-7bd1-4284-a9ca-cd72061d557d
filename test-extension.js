#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 新氧画廊 VSCode 扩展测试脚本');
console.log('=====================================\n');

// 测试结果收集
const results = {
    passed: 0,
    failed: 0,
    tests: []
};

function test(name, condition, details = '') {
    const passed = condition;
    results.tests.push({ name, passed, details });
    
    if (passed) {
        results.passed++;
        console.log(`✅ ${name}`);
    } else {
        results.failed++;
        console.log(`❌ ${name}`);
        if (details) console.log(`   ${details}`);
    }
}

// 1. 检查项目结构
console.log('📁 检查项目结构...');
test('extension目录存在', fs.existsSync('extension'));
test('extension/package.json存在', fs.existsSync('extension/package.json'));
test('extension/src/extension.ts存在', fs.existsSync('extension/src/extension.ts'));
test('extension/out目录存在', fs.existsSync('extension/out'));
test('extension/out/extension.js存在', fs.existsSync('extension/out/extension.js'));

// 2. 检查构建输出
console.log('\n🏗️  检查构建输出...');
test('dist-vscode目录存在', fs.existsSync('dist-vscode'));

if (fs.existsSync('dist-vscode')) {
    const files = fs.readdirSync('dist-vscode');
    const jsFiles = files.filter(f => f.startsWith('index') && f.endsWith('.js'));
    const cssFiles = files.filter(f => f.startsWith('index') && f.endsWith('.css'));
    
    test('存在JS入口文件', jsFiles.length > 0, `找到 ${jsFiles.length} 个JS文件`);
    test('存在CSS样式文件', cssFiles.length > 0, `找到 ${cssFiles.length} 个CSS文件`);
    
    if (jsFiles.length > 0) {
        const jsFile = jsFiles[0];
        const jsPath = path.join('dist-vscode', jsFile);
        const jsSize = fs.statSync(jsPath).size;
        test('JS文件不为空', jsSize > 1000, `文件大小: ${Math.round(jsSize/1024)}KB`);
    }
}

// 3. 检查配置文件
console.log('\n⚙️  检查配置文件...');
test('.vscode/launch.json存在', fs.existsSync('.vscode/launch.json'));
test('vite.config.vscode.ts存在', fs.existsSync('vite.config.vscode.ts'));

// 4. 检查扩展配置
if (fs.existsSync('extension/package.json')) {
    try {
        const packageJson = JSON.parse(fs.readFileSync('extension/package.json', 'utf8'));
        test('扩展名称正确', packageJson.name === 'sy-gallery-vscode');
        test('扩展有激活事件', packageJson.activationEvents && packageJson.activationEvents.length > 0);
        test('扩展有命令定义', packageJson.contributes && packageJson.contributes.commands && packageJson.contributes.commands.length > 0);
        test('扩展入口文件正确', packageJson.main === './out/extension.js');
    } catch (error) {
        test('扩展package.json格式正确', false, error.message);
    }
}

// 5. 检查源码关键修改
console.log('\n🔧 检查源码修改...');
if (fs.existsSync('extension/src/extension.ts')) {
    const extensionCode = fs.readFileSync('extension/src/extension.ts', 'utf8');
    test('使用dist-vscode路径', extensionCode.includes('dist-vscode'), '检查路径配置');
    test('包含调试日志', extensionCode.includes('console.log'), '检查调试信息');
    test('正确配置localResourceRoots', extensionCode.includes('dist-vscode'), '检查资源访问权限');
}

if (fs.existsSync('src/views/app.vue')) {
    const appCode = fs.readFileSync('src/views/app.vue', 'utf8');
    test('包含VSCode环境检测', appCode.includes('vscodeAdapter.isVSCodeEnvironment'), '检查环境适配');
    test('包含错误处理', appCode.includes('try') && appCode.includes('catch'), '检查错误处理');
}

// 6. 生成报告
console.log('\n📊 测试报告');
console.log('=============');
console.log(`总测试数: ${results.tests.length}`);
console.log(`通过: ${results.passed}`);
console.log(`失败: ${results.failed}`);
console.log(`成功率: ${Math.round((results.passed / results.tests.length) * 100)}%`);

if (results.failed === 0) {
    console.log('\n🎉 所有测试通过！扩展应该可以正常工作。');
    console.log('\n📋 下一步操作:');
    console.log('1. 在VSCode中按F5启动调试');
    console.log('2. 在新窗口中按Cmd+Shift+P打开命令面板');
    console.log('3. 输入"打开新氧画廊"并执行命令');
    console.log('4. 检查开发者工具中的日志信息');
} else {
    console.log('\n⚠️  发现问题，请检查失败的测试项。');
    console.log('\n🔧 建议操作:');
    console.log('1. 运行 npm run vscode:build 重新构建');
    console.log('2. 检查失败的测试项并修复');
    console.log('3. 重新运行此测试脚本');
}

console.log('\n📖 详细调试指南请查看: debug-checklist.md');
