{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared"}, "label": "extension:compile", "detail": "编译 VSCode 扩展 TypeScript 代码", "options": {"cwd": "${workspaceFolder}/extension"}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared"}, "label": "extension:watch", "detail": "监视编译 VSCode 扩展 TypeScript 代码", "options": {"cwd": "${workspaceFolder}/extension"}, "problemMatcher": ["$tsc-watch"], "isBackground": true}, {"type": "npm", "script": "build:vscode", "group": "build", "presentation": {"panel": "shared"}, "label": "build:vscode", "detail": "构建 VSCode 版本的 Vue 应用", "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"type": "npm", "script": "vscode:build", "group": "build", "presentation": {"panel": "shared"}, "label": "vscode:build", "detail": "完整构建 VSCode 扩展（Vue 应用 + 扩展代码）", "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$tsc"]}]}