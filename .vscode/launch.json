{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "针对 localhost 启动 Chrome",
      "url": "http://localhost:8080",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "运行 VSCode 扩展",
      "type": "extensionHost",
      "request": "launch",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/extension"
      ],
      "outFiles": [
        "${workspaceFolder}/extension/out/**/*.js"
      ],
      "preLaunchTask": "extension:compile"
    },
    {
      "name": "VSCode 扩展测试",
      "type": "extensionHost",
      "request": "launch",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/extension",
        "--extensionTestsPath=${workspaceFolder}/extension/out/test/suite/index"
      ],
      "outFiles": [
        "${workspaceFolder}/extension/out/test/**/*.js"
      ]
    }
  ]
}
