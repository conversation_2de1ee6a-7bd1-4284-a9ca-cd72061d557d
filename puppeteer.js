import * as puppeteer from "puppeteer-core";
import * as fs from "fs";
(async () => {
  const browser = await puppeteer.launch({
    executablePath: "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    headless: false,
    devtools: false, // 打开开发者模式
    defaultViewport: null, // 不使用默认的固定大小，直接填满浏览器
    slowMo: 50 // slow down by 250ms
  });
  const page = await browser.newPage();
  // 需要执行的脚本的url
  const list = ["https://ui.meiye.art/album/1/2143"];
  const length = list.length;
  // 保存文件公共方法
  async function writeFile(path, content) {
    await new Promise((resolve) => {
      fs.writeFile(path, content, () => {
        resolve(true);
      });
    });
  }
  const getImage = async (index) => {
    //打开页面
    await page.goto(list[index], { waitUntil: "networkidle2" });
    // 保存图片src地址
    const imgArray = await page.$$eval("img", (els) =>
      Array.from(els).map((el) => {
        if (el.getAttribute("src")) {
          return `https:${el.getAttribute("src")}`;
        }
      })
    );
    await writeFile(`./cdn_image.json`, JSON.stringify({ imgArray }, null, 2));
    index++;
    if (index < length) {
      getImage(index);
    } else {
      // 关闭浏览器
      await browser.close();
    }
  };
  getImage(0);
})();
