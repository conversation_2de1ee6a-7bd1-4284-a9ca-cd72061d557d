module.exports = {
  extends: [
    "stylelint-config-standard", // 配置stylelint拓展插件, stylelint v15版本，不需要安装stylelint-config-prettier，因为不会存在冲突
    "stylelint-config-html/vue", // 配置 vue 中 template 样式格式化
    "stylelint-config-recommended",
    "stylelint-config-recommended-vue",
    "stylelint-prettier"
  ],
  overrides: [
    {
      files: ["**/*.(scss|css|vue|html)"],
      customSyntax: "postcss-scss"
    },
    {
      files: ["**/*.(html|vue)"],
      customSyntax: "postcss-html"
    }
  ],
  ignoreFiles: ["**/*.js", "**/*.jsx", "**/*.tsx", "**/*.ts", "**/*.json", "**/*.md", "**/*.yaml"],
  /**
   * null  => 关闭该规则
   * always => 必须
   */
  rules: {
    "value-keyword-case": null, // 在 css 中使用 v-bind，不报错
    "no-descending-specificity": null, // 禁止在具有较高优先级的选择器后出现被其覆盖的较低优先级的选择器
    "function-url-quotes": "always", // 要求或禁止 URL 的引号 "always(必须加上引号)"|"never(没有引号)"
    "no-empty-source": null, // 关闭禁止空源码
    "selector-class-pattern": null, // 关闭强制选择器类名的格式
    "property-no-unknown": null, // 禁止未知的属性(true 为不允许)
    "block-opening-brace-space-before": "always", // 在块的左括号之前要求或禁止空格
    "value-no-vendor-prefix": null, // 关闭 属性值前缀 --webkit-box
    "property-no-vendor-prefix": null, // 关闭 属性前缀 -webkit-mask
    "selector-pseudo-class-no-unknown": [
      // 不允许未知的选择器
      true,
      {
        ignorePseudoClasses: ["global", "v-deep", "deep"] // 忽略属性，修改element默认样式的时候能使用到
      }
    ]
  }
};
