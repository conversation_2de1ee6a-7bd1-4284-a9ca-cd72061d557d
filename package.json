{"name": "sy-gallery", "version": "1.0.0", "type": "module", "scripts": {"dev": "cross-env vite --mode development", "build-local": "cross-env vue-tsc  --noEmit && vite build --mode development", "build": "cross-env vue-tsc  --noEmit && vite build --mode production", "build:test": "cross-env vue-tsc  --noEmit && vite build --mode test", "local": "cross-env vite --mode localhost", "build:local": "cross-env vue-tsc && vite build", "test": "cross-env vue-tsc && vite build --mode test", "pre": "cross-env vue-tsc && vite build --mode pre", "build:vscode": "cross-env vue-tsc --noEmit && vite build --config vite.config.vscode.ts", "dev:vscode": "cross-env vite --config vite.config.vscode.ts --mode development", "extension:compile": "cd extension && npm run compile", "extension:watch": "cd extension && npm run watch", "extension:install": "cd extension && npm install", "vscode:build": "npm run build:vscode && npm run extension:compile", "vscode:dev": "concurrently \"npm run dev:vscode\" \"npm run extension:watch\"", "lint:script": "eslint --ext .jsx,.vue,.tsx,.ts,.js --fix", "lint:style": "stylelint src/**/*.{html,vue,css,scss} --fix", "format": "prettier --write \"./**/*.{html,vue,js,ts,json,md}\"", "lint": "npm run typecheck && npm run lint:script && npm run lint:style && npm run format", "typecheck": "cross-env vue-tsc --noEmit -p tsconfig.json --composite false", "iconfont": "node shell/iconfont.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.4.0", "@vueuse/core": "^8.2.0", "axios": "^1.5.1", "codemirror": "^5.65.16", "codemirror-editor-vue3": "^2.4.1", "element-plus": "^2.5.2", "hotkeys-js": "^3.13.5", "konva": "^9.2.3", "lodash": "^4.17.21", "pinia": "^2.1.7", "qs": "^6.11.2", "viewerjs": "^1.11.6", "vue": "^3.2.31", "vue-clipboard3": "^2.0.0", "vue-konva": "^3.0.2", "vue-lazyload": "^3.0.0", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/codemirror": "^5.60.15", "@types/node": "^18.18.6", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "@vue/compiler-sfc": "^3.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.51.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "less-loader": "^11.1.3", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "prettier": "^3.0.3", "sass": "^1.69.3", "sass-loader": "^13.3.2", "stylelint": "^15.10.3", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-prettier": "^4.0.2", "sy-cli": "^2.3.5", "ts-node": "^10.9.1", "typescript": "^5.2.2", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.18.1", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.11", "vite-plugin-eslint": "^1.8.1", "vite-plugin-inspect": "^0.8.1", "vite-plugin-stylelint": "^5.1.1", "vue-tsc": "^1.8.19"}}