# 新氧画屏前端工程开发指南

jenkins: http://jenkins.sy.soyoung.com:8080/job/toc-sy-gallery-master/
url: https://design.sy.soyoung.com
server ip: *************

## 技术栈

- 构建工具 —— vite@4.4
- 框架 —— vue@3.0
- 路由 —— vue-router@4.x
- 状态管理 —— pinia@2.x
- 组件库 —— element-plus @2.4
- 主题 —— 基于element官方主题定制
- 开发语言 —— TypeScript

## 主题定制

定义变量方式：
参考: https://element-plus.gitee.io/zh-CN/guide/theming.html
支持的全部变量可参考文件：node_modules/element-plus/packages/theme-chalk/src/common/var.scss。

## 组件按需引入

element-plus 组件库没有全量引入，采用按需引入的方式（vite已经配置好），
业务层面跟vue2的引入方式相同。

## 工程仓库

view/admin 画廊后台目录

- admin
  components ## 后台组件库目录

  config.ts ## 根据router配置的后台的左侧菜单

  index.vue ## 后台的入口文件

  dashBoard ## 后台首页

  categoryManage ## 分类管理页面

  router
  manage.ts ## 管理后台的路由都在这里

views/designCollaboration 设计协作目录

- components ---- 组件目录

  dashboard ---- 工作台页面

  web ---- 设计展示页（全部、单个）

view/layouts 画廊前台网页目录

- layouts
  components ## 组件目录
  home ## 涉及到的主页文件地址
  components ## 包含了body、header等组件
  page ## 入口文件
  workbench ## 工作台功能页面
  index.vue ## 主入口文件

## 本地开发

npm run local -- node 对应本地地址 修改hosts文件映射 127.0.0.1 design-local.sy.soyoung.com
npm run dev -- node 对应测试环境后端 design.sy.soyoung.com

## node路由syapi地址

https://syapi.sy.soyoung.com/project/1469/interface/api

画廊上测试环境流程
1、前端合并master代码，等待Jenkins构建完成
2、nest后端代码如果有改动提交master，并且等待前端项目Jenkins构建完成，需要pull一下后端项目
3、登录209机器，进入到sy-gallery-nest目录，拉取最新代码，执行npm run build，然后执行pm2 restart all代码这个时候就上到了design.sy.soyoung.com
