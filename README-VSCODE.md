# 新氧画廊 VSCode 扩展开发指南

## 项目概述

本项目将新氧画廊（sy-gallery）前端工程改造为支持在 VSCode 内部以插件形式运行的双模式应用。用户可以选择在浏览器中正常使用，也可以在 VSCode 中以扩展的形式使用。

## 技术架构

### 原有技术栈
- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite 4.4.11
- **路由**: Vue Router 4.2.5
- **状态管理**: Pinia 2.1.7
- **UI 组件**: Element Plus 2.5.2
- **图形处理**: Konva + Vue-Konva
- **代码编辑**: CodeMirror

### VSCode 扩展技术
- **VSCode API**: 基于官方扩展 API
- **Webview**: 承载 Vue 应用的容器
- **TypeScript**: 扩展开发语言
- **环境适配**: 双环境运行支持

## 项目结构

```
sy-gallery/
├── src/                          # Vue 应用源码
│   ├── main.ts                  # 浏览器环境入口
│   ├── main-vscode.ts           # VSCode 环境入口
│   ├── utils/
│   │   └── vscode-adapter.ts    # VSCode 环境适配器
│   └── ...                      # 其他 Vue 组件和资源
├── extension/                    # VSCode 扩展代码
│   ├── package.json             # 扩展配置清单
│   ├── src/
│   │   └── extension.ts         # 扩展主逻辑
│   ├── tsconfig.json           # TypeScript 配置
│   └── out/                     # 编译输出目录
├── dist/                        # 浏览器构建输出
├── dist-vscode/                 # VSCode 扩展构建输出
├── vite.config.ts              # 浏览器环境构建配置
├── vite.config.vscode.ts       # VSCode 环境构建配置
└── .vscode/
    └── launch.json              # 调试配置
```

## 安装与开发

### 环境要求

- Node.js >= 18
- VSCode >= 1.74.0
- npm 或 yarn

### 安装依赖

```bash
# 安装主项目依赖
npm install

# 安装扩展项目依赖  
npm run extension:install
```

### 开发模式

#### 1. 浏览器开发模式（原有模式）

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

#### 2. VSCode 扩展开发模式

```bash
# 方式一：并发开发模式（推荐）
npm run vscode:dev

# 方式二：分别启动
# 启动 Vue 应用开发服务器
npm run dev:vscode

# 启动扩展 TypeScript 监视编译
npm run extension:watch
```

#### 3. 调试扩展

1. 在 VSCode 中打开项目
2. 按 `F5` 或选择"运行 VSCode 扩展"调试配置
3. 这将启动一个新的扩展开发宿主窗口
4. 在新窗口中按 `Cmd+Shift+P`（macOS）或 `Ctrl+Shift+P`（Windows/Linux）
5. 输入"打开新氧画廊"命令

### 构建部署

#### 构建 VSCode 扩展

```bash
# 构建完整的 VSCode 扩展
npm run vscode:build
```

这会执行以下步骤：
1. 构建 Vue 应用（VSCode 版本）
2. 编译 TypeScript 扩展代码
3. 在 `extension/out/` 目录生成可用的扩展文件

#### 打包扩展

```bash
# 进入扩展目录
cd extension

# 安装 vsce（如果尚未安装）
npm install -g vsce

# 打包扩展
vsce package
```

这将生成 `.vsix` 文件，可以通过以下方式安装：
- VSCode 命令面板: `Extensions: Install from VSIX`
- 命令行: `code --install-extension sy-gallery-vscode-1.0.0.vsix`

## 环境适配

### VSCodeAdapter 类

`src/utils/vscode-adapter.ts` 提供了完整的环境适配功能：

```typescript
import { vscodeAdapter } from '@/utils/vscode-adapter';

// 检测运行环境
if (vscodeAdapter.isVSCodeEnvironment()) {
  // VSCode 环境特定逻辑
}

// 发送消息到扩展
vscodeAdapter.showInfo('操作成功');
vscodeAdapter.showError('操作失败');

// 状态管理
vscodeAdapter.setState({ key: 'value' });
const state = vscodeAdapter.getState();
```

### 环境差异处理

1. **API 调用**: 在 VSCode 环境中，某些 Web API 可能不可用
2. **样式适配**: 自动适配 VSCode 主题（深色/浅色）
3. **资源加载**: 使用 `webview.asWebviewUri()` 加载本地资源
4. **错误处理**: 错误信息会通过 VSCode 通知显示

## 功能特性

### VSCode 集成功能

- **命令面板支持**: 通过命令面板快速打开画廊
- **资源管理器集成**: 右键菜单中的"在新氧画廊中打开"选项
- **主题适配**: 自动适配 VSCode 深色/浅色主题
- **状态持久化**: 扩展状态在 VSCode 重启后保持
- **错误提示**: 错误信息通过 VSCode 原生通知显示

### 双环境兼容

- **无缝切换**: 同一套代码在浏览器和 VSCode 中都能正常运行
- **功能一致**: 核心功能在两个环境中保持一致
- **性能优化**: 针对不同环境进行了专门优化

## 开发指南

### 添加新功能

1. **检查环境**: 使用 `vscodeAdapter.isVSCodeEnvironment()` 检查运行环境
2. **适配差异**: 为不同环境提供适当的实现
3. **测试**: 在两个环境中都要进行测试

### 调试技巧

1. **浏览器调试**: 使用浏览器开发者工具
2. **VSCode 调试**: 使用 VSCode 内置调试器
3. **日志输出**: 使用 `vscodeAdapter.showInfo()` 在 VSCode 环境中显示日志

### 扩展配置

修改 `extension/package.json` 可以：
- 添加新的命令和菜单项
- 配置激活事件
- 设置扩展元数据

## 常见问题

### Q: 扩展无法激活？
A: 检查 `extension/out/extension.js` 是否存在，运行 `npm run extension:compile` 重新编译。

### Q: 资源加载失败？
A: 确保使用了 `webview.asWebviewUri()` 来处理本地资源 URI。

### Q: 样式显示异常？
A: 检查是否正确适配了 VSCode 主题变量。

### Q: 热重载不生效？
A: VSCode 扩展开发需要重新启动扩展宿主窗口，不支持热重载。

## 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 在 GitHub 上提交 Issue
3. 联系开发团队

---

**注意**: 这是一个双模式应用，既可以作为传统 Web 应用运行，也可以作为 VSCode 扩展使用。请根据您的使用场景选择合适的开发和部署方式。
