import{S as t,a as e}from"./chunk.ecdc8f95.js";import{d as i,af as n,r as a,y as o,g as s,o as r,c as l,b as h,u as c,e as u,i as d,B as m,C as g,t as v,m as f,f as p,n as b}from"./index.05904f40.js";import{c as w}from"./chunk.8df321e8.js";import{a as y,m as x}from"./chunk.805f473d.js";import{a as k,v as z}from"./chunk.984ebab6.js";import{E as T}from"./chunk.0db0b66d.js";import{E}from"./chunk.e117c129.js";import"./chunk.380b3154.js";import"./chunk.db8898e3.js";import"./chunk.034f7efa.js";import"./chunk.505381c5.js";import"./chunk.6eac9d60.js";import"./chunk.c83b4915.js";
/*!
 * Viewer.js v1.11.6
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-09-17T03:16:38.052Z
 */function D(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function I(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?D(Object(i),!0).forEach((function(e){C(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):D(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function A(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,O(n.key),n)}}function C(t,e,i){return(e=O(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function O(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}var _={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},F="undefined"!=typeof window&&void 0!==window.document,L=F?window:{},j=!(!F||!L.document.documentElement)&&"ontouchstart"in L.document.documentElement,N=!!F&&"PointerEvent"in L,R="viewer",M="move",Y="switch",X="zoom",q="".concat(R,"-active"),P="".concat(R,"-close"),W="".concat(R,"-fade"),B="".concat(R,"-fixed"),H="".concat(R,"-fullscreen"),V="".concat(R,"-fullscreen-exit"),U="".concat(R,"-hide"),$="".concat(R,"-hide-md-down"),K="".concat(R,"-hide-sm-down"),Z="".concat(R,"-hide-xs-down"),G="".concat(R,"-in"),J="".concat(R,"-invisible"),Q="".concat(R,"-loading"),tt="".concat(R,"-move"),et="".concat(R,"-open"),it="".concat(R,"-show"),nt="".concat(R,"-transition"),at="click",ot="dblclick",st="dragstart",rt="focusin",lt="keydown",ht="load",ct="error",ut=N?"pointerdown":j?"touchstart":"mousedown",dt=N?"pointermove":j?"touchmove":"mousemove",mt=N?"pointerup pointercancel":j?"touchend touchcancel":"mouseup",gt="resize",vt="transitionend",ft="wheel",pt="ready",bt="show",wt="shown",yt="hide",xt="hidden",kt="view",zt="viewed",Tt="move",Et="moved",Dt="rotate",It="rotated",St="scale",At="scaled",Ct="zoom",Ot="zoomed",_t="play",Ft="stop",Lt="".concat(R,"Action"),jt=/\s\s*/,Nt=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function Rt(t){return"string"==typeof t}var Mt=Number.isNaN||L.isNaN;function Yt(t){return"number"==typeof t&&!Mt(t)}function Xt(t){return void 0===t}function qt(t){return"object"===S(t)&&null!==t}var Pt=Object.prototype.hasOwnProperty;function Wt(t){if(!qt(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&Pt.call(i,"isPrototypeOf")}catch(n){return!1}}function Bt(t){return"function"==typeof t}function Ht(t,e){if(t&&Bt(e))if(Array.isArray(t)||Yt(t.length)){var i,n=t.length;for(i=0;i<n&&!1!==e.call(t,t[i],i,t);i+=1);}else qt(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}));return t}var Vt=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return qt(t)&&i.length>0&&i.forEach((function(e){qt(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},Ut=/^(?:width|height|left|top|marginLeft|marginTop)$/;function $t(t,e){var i=t.style;Ht(e,(function(t,e){Ut.test(e)&&Yt(t)&&(t+="px"),i[e]=t}))}function Kt(t,e){return!(!t||!e)&&(t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)}function Zt(t,e){if(t&&e)if(Yt(t.length))Ht(t,(function(t){Zt(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function Gt(t,e){t&&e&&(Yt(t.length)?Ht(t,(function(t){Gt(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function Jt(t,e,i){e&&(Yt(t.length)?Ht(t,(function(t){Jt(t,e,i)})):i?Zt(t,e):Gt(t,e))}var Qt=/([a-z\d])([A-Z])/g;function te(t){return t.replace(Qt,"$1-$2").toLowerCase()}function ee(t,e){return qt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(te(e)))}function ie(t,e,i){qt(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(te(e)),i)}var ne=function(){var t=!1;if(F){var e=!1,i=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});L.addEventListener("test",i,n),L.removeEventListener("test",i,n)}return t}();function ae(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=i;e.trim().split(jt).forEach((function(e){if(!ne){var o=t.listeners;o&&o[e]&&o[e][i]&&(a=o[e][i],delete o[e][i],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(e,a,n)}))}function oe(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=i;e.trim().split(jt).forEach((function(e){if(n.once&&!ne){var o=t.listeners,s=void 0===o?{}:o;a=function(){delete s[e][i],t.removeEventListener(e,a,n);for(var o=arguments.length,r=new Array(o),l=0;l<o;l++)r[l]=arguments[l];i.apply(t,r)},s[e]||(s[e]={}),s[e][i]&&t.removeEventListener(e,s[e][i],n),s[e][i]=a,t.listeners=s}t.addEventListener(e,a,n)}))}function se(t,e,i,n){var a;return Bt(Event)&&Bt(CustomEvent)?a=new CustomEvent(e,I({bubbles:!0,cancelable:!0,detail:i},n)):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function re(t){var e=t.rotate,i=t.scaleX,n=t.scaleY,a=t.translateX,o=t.translateY,s=[];Yt(a)&&0!==a&&s.push("translateX(".concat(a,"px)")),Yt(o)&&0!==o&&s.push("translateY(".concat(o,"px)")),Yt(e)&&0!==e&&s.push("rotate(".concat(e,"deg)")),Yt(i)&&1!==i&&s.push("scaleX(".concat(i,")")),Yt(n)&&1!==n&&s.push("scaleY(".concat(n,")"));var r=s.length?s.join(" "):"none";return{WebkitTransform:r,msTransform:r,transform:r}}var le=L.navigator&&/Version\/\d+(\.\d+)+?\s+Safari/i.test(L.navigator.userAgent);function he(t,e,i){var n=document.createElement("img");if(t.naturalWidth&&!le)return i(t.naturalWidth,t.naturalHeight),n;var a=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),le||a.removeChild(n)},Ht(e.inheritedAttributes,(function(e){var i=t.getAttribute(e);null!==i&&n.setAttribute(e,i)})),n.src=t.src,le||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",a.appendChild(n)),n}function ce(t){switch(t){case 2:return Z;case 3:return K;case 4:return $;default:return""}}function ue(t,e){var i=t.pageX,n=t.pageY,a={endX:i,endY:n};return e?a:I({timeStamp:Date.now(),startX:i,startY:n},a)}var de,me={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var t=this.element.ownerDocument,e=t.body||t.documentElement;this.body=e,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=e.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(e).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t,e=this.options,i=this.parent;e.inline&&(t={width:Math.max(i.offsetWidth,e.minWidth),height:Math.max(i.offsetHeight,e.minHeight)},this.parentData=t),!this.fulled&&t||(t=this.containerData),this.viewerData=Vt({},t)},renderViewer:function(){this.options.inline&&!this.fulled&&$t(this.viewer,this.viewerData)},initList:function(){var t=this,e=this.element,i=this.options,n=this.list,a=[];n.innerHTML="",Ht(this.images,(function(e,o){var s=e.src,r=e.alt||function(t){return Rt(t)?decodeURIComponent(t.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}(s),l=t.getImageURL(e);if(s||l){var h=document.createElement("li"),c=document.createElement("img");Ht(i.inheritedAttributes,(function(t){var i=e.getAttribute(t);null!==i&&c.setAttribute(t,i)})),i.navbar&&(c.src=s||l),c.alt=r,c.setAttribute("data-original-url",l||s),h.setAttribute("data-index",o),h.setAttribute("data-viewer-action","view"),h.setAttribute("role","button"),i.keyboard&&h.setAttribute("tabindex",0),h.appendChild(c),n.appendChild(h),a.push(h)}})),this.items=a,Ht(a,(function(e){var n,a,o=e.firstElementChild;ie(o,"filled",!0),i.loading&&Zt(e,Q),oe(o,ht,n=function(n){ae(o,ct,a),i.loading&&Gt(e,Q),t.loadImage(n)},{once:!0}),oe(o,ct,a=function(){ae(o,ht,n),i.loading&&Gt(e,Q)},{once:!0})})),i.transition&&oe(e,zt,(function(){Zt(n,nt)}),{once:!0})},renderList:function(){var t=this.index,e=this.items[t];if(e){var i=e.nextElementSibling,n=parseInt(window.getComputedStyle(i||e).marginLeft,10),a=e.offsetWidth,o=a+n;$t(this.list,Vt({width:o*this.length-n},re({translateX:(this.viewerData.width-a)/2-o*t})))}},resetList:function(){var t=this.list;t.innerHTML="",Gt(t,nt),$t(t,re({translateX:0}))},initImage:function(t){var e,i=this,n=this.options,a=this.image,o=this.viewerData,s=this.footer.offsetHeight,r=o.width,l=Math.max(o.height-s,s),h=this.imageData||{};this.imageInitializing={abort:function(){e.onload=null}},e=he(a,n,(function(e,a){var o=e/a,s=Math.max(0,Math.min(1,n.initialCoverage)),c=r,u=l;i.imageInitializing=!1,l*o>r?u=r/o:c=l*o,s=Yt(s)?s:.9,c=Math.min(c*s,e),u=Math.min(u*s,a);var d=(r-c)/2,m=(l-u)/2,g={left:d,top:m,x:d,y:m,width:c,height:u,oldRatio:1,ratio:c/e,aspectRatio:o,naturalWidth:e,naturalHeight:a},v=Vt({},g);n.rotatable&&(g.rotate=h.rotate||0,v.rotate=0),n.scalable&&(g.scaleX=h.scaleX||1,g.scaleY=h.scaleY||1,v.scaleX=1,v.scaleY=1),i.imageData=g,i.initialImageData=v,t&&t()}))},renderImage:function(t){var e=this,i=this.image,n=this.imageData;if($t(i,Vt({width:n.width,height:n.height,marginLeft:n.x,marginTop:n.y},re(n))),t)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&Kt(i,nt)){var a=function(){e.imageRendering=!1,t()};this.imageRendering={abort:function(){ae(i,vt,a)}},oe(i,vt,a,{once:!0})}else t()},resetImage:function(){var t=this.image;t&&(this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null,this.title.innerHTML="")}},ge={bind:function(){var t=this.options,e=this.viewer,i=this.canvas,n=this.element.ownerDocument;oe(e,at,this.onClick=this.click.bind(this)),oe(e,st,this.onDragStart=this.dragstart.bind(this)),oe(i,ut,this.onPointerDown=this.pointerdown.bind(this)),oe(n,dt,this.onPointerMove=this.pointermove.bind(this)),oe(n,mt,this.onPointerUp=this.pointerup.bind(this)),oe(n,lt,this.onKeyDown=this.keydown.bind(this)),oe(window,gt,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&oe(e,ft,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&oe(i,ot,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var t=this.options,e=this.viewer,i=this.canvas,n=this.element.ownerDocument;ae(e,at,this.onClick),ae(e,st,this.onDragStart),ae(i,ut,this.onPointerDown),ae(n,dt,this.onPointerMove),ae(n,mt,this.onPointerUp),ae(n,lt,this.onKeyDown),ae(window,gt,this.onResize),t.zoomable&&t.zoomOnWheel&&ae(e,ft,this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&ae(i,ot,this.onDblclick)}},ve={click:function(t){var e=this.options,i=this.imageData,n=t.target,a=ee(n,Lt);switch(a||"img"!==n.localName||"li"!==n.parentElement.localName||(a=ee(n=n.parentElement,Lt)),j&&t.isTrusted&&n===this.canvas&&clearTimeout(this.clickCanvasTimeout),a){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(ee(n,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-i.scaleX||-1);break;case"flip-vertical":this.scaleY(-i.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(t){t.preventDefault(),this.viewed&&t.target===this.image&&(j&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(t.isTrusted?t:t.detail&&t.detail.originalEvent))},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var e=this.element,i=this.options,n=this.image,a=this.index,o=this.viewerData;Gt(n,J),i.loading&&Gt(this.canvas,Q),n.style.cssText="height:0;"+"margin-left:".concat(o.width/2,"px;")+"margin-top:".concat(o.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage((function(){Jt(n,tt,i.movable),Jt(n,nt,i.transition),t.renderImage((function(){t.viewed=!0,t.viewing=!1,Bt(i.viewed)&&oe(e,zt,i.viewed,{once:!0}),se(e,zt,{originalImage:t.images[a],index:a,image:n},{cancelable:!1})}))}))},loadImage:function(t){var e=t.target,i=e.parentNode,n=i.offsetWidth||30,a=i.offsetHeight||50,o=!!ee(e,"filled");he(e,this.options,(function(t,i){var s=t/i,r=n,l=a;a*s>n?o?r=a*s:l=n/s:o?l=n/s:r=a*s,$t(e,Vt({width:r,height:l},re({translateX:(n-r)/2,translateY:(a-l)/2})))}))},keydown:function(t){var e=this.options;if(e.keyboard){var i=t.keyCode||t.which||t.charCode;if(13===i)this.viewer.contains(t.target)&&this.click(t);if(this.fulled)switch(i){case 27:this.played?this.stop():e.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(e.loop);break;case 38:t.preventDefault(),this.zoom(e.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(e.loop);break;case 40:t.preventDefault(),this.zoom(-e.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle())}}},dragstart:function(t){"img"===t.target.localName&&t.preventDefault()},pointerdown:function(t){var e=this.options,i=this.pointers,n=t.buttons,a=t.button;if(this.pointerMoved=!1,!(!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(Yt(n)&&1!==n||Yt(a)&&0!==a||t.ctrlKey))){t.preventDefault(),t.changedTouches?Ht(t.changedTouches,(function(t){i[t.identifier]=ue(t)})):i[t.pointerId||0]=ue(t);var o=!!e.movable&&M;e.zoomOnTouch&&e.zoomable&&Object.keys(i).length>1?o=X:e.slideOnTouch&&("touch"===t.pointerType||"touchstart"===t.type)&&this.isSwitchable()&&(o=Y),!e.transition||o!==M&&o!==X||Gt(this.image,nt),this.action=o}},pointermove:function(t){var e=this.pointers,i=this.action;this.viewed&&i&&(t.preventDefault(),t.changedTouches?Ht(t.changedTouches,(function(t){Vt(e[t.identifier]||{},ue(t,!0))})):Vt(e[t.pointerId||0]||{},ue(t,!0)),this.change(t))},pointerup:function(t){var e,i=this,n=this.options,a=this.action,o=this.pointers;t.changedTouches?Ht(t.changedTouches,(function(t){e=o[t.identifier],delete o[t.identifier]})):(e=o[t.pointerId||0],delete o[t.pointerId||0]),a&&(t.preventDefault(),!n.transition||a!==M&&a!==X||Zt(this.image,nt),this.action=!1,j&&a!==X&&e&&Date.now()-e.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout((function(){se(i.image,ot,{originalEvent:t})}),50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout((function(){i.imageClicked=!1}),500)):(this.imageClicked=!1,n.backdrop&&"static"!==n.backdrop&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout((function(){se(i.canvas,at,{originalEvent:t})}),50)))))},resize:function(){var t=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage()})),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();Ht(this.player.getElementsByTagName("img"),(function(e){oe(e,ht,t.loadImage.bind(t),{once:!0}),se(e,ht)}))}},wheel:function(t){var e=this;if(this.viewed&&(t.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50);var i=Number(this.options.zoomRatio)||.1,n=1;t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*i,!0,null,t)}}},fe={show:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.element,i=this.options;if(i.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(Bt(i.show)&&oe(e,bt,i.show,{once:!0}),!1===se(e,bt)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(Gt(n,U),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("aria-hidden"),i.transition&&!t){var a=this.shown.bind(this);this.transitioning={abort:function(){ae(n,vt,a),Gt(n,G)}},Zt(n,nt),n.initialOffsetWidth=n.offsetWidth,oe(n,vt,a,{once:!0}),Zt(n,G)}else Zt(n,G),this.shown();return this},hide:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.element,n=this.options;if(n.inline||this.hiding||!this.isShown&&!this.showing)return this;if(Bt(n.hide)&&oe(i,yt,n.hide,{once:!0}),!1===se(i,yt))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var a=this.viewer,o=this.image,s=function(){Gt(a,G),t.hidden()};if(n.transition&&!e){var r=function e(i){i&&i.target===a&&(ae(a,vt,e),t.hidden())},l=function(){Kt(a,nt)?(oe(a,vt,r),Gt(a,G)):s()};this.transitioning={abort:function(){t.viewed&&Kt(o,nt)?ae(o,vt,l):Kt(a,nt)&&ae(a,vt,r)}},this.viewed&&Kt(o,nt)?(oe(o,vt,l,{once:!0}),this.zoomTo(0,!1,null,null,!0)):l()}else s();return this},view:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.initialViewIndex;if(e=Number(e)||0,this.hiding||this.played||e<0||e>=this.length||this.viewed&&e===this.index)return this;if(!this.isShown)return this.index=e,this.show();this.viewing&&this.viewing.abort();var i=this.element,n=this.options,a=this.title,o=this.canvas,s=this.items[e],r=s.querySelector("img"),l=ee(r,"originalUrl"),h=r.getAttribute("alt"),c=document.createElement("img");if(Ht(n.inheritedAttributes,(function(t){var e=r.getAttribute(t);null!==e&&c.setAttribute(t,e)})),c.src=l,c.alt=h,Bt(n.view)&&oe(i,kt,n.view,{once:!0}),!1===se(i,kt,{originalImage:this.images[e],index:e,image:c})||!this.isShown||this.hiding||this.played)return this;var u=this.items[this.index];u&&(Gt(u,q),u.removeAttribute("aria-selected")),Zt(s,q),s.setAttribute("aria-selected",!0),n.focus&&s.focus(),this.image=c,this.viewed=!1,this.index=e,this.imageData={},Zt(c,J),n.loading&&Zt(o,Q),o.innerHTML="",o.appendChild(c),this.renderList(),a.innerHTML="";var d,m,g=function(){var e,i=t.imageData,o=Array.isArray(n.title)?n.title[1]:n.title;a.innerHTML=Rt(e=Bt(o)?o.call(t,c,i):"".concat(h," (").concat(i.naturalWidth," × ").concat(i.naturalHeight,")"))?e.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):e};return oe(i,zt,g,{once:!0}),this.viewing={abort:function(){ae(i,zt,g),c.complete?t.imageRendering?t.imageRendering.abort():t.imageInitializing&&t.imageInitializing.abort():(c.src="",ae(c,ht,d),t.timeout&&clearTimeout(t.timeout))}},c.complete?this.load():(oe(c,ht,d=function(){ae(c,ct,m),t.load()},{once:!0}),oe(c,ct,m=function(){ae(c,ht,d),t.timeout&&(clearTimeout(t.timeout),t.timeout=!1),Gt(c,J),n.loading&&Gt(t.canvas,Q)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){Gt(c,J),t.timeout=!1}),1e3)),this},prev:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.index-1;return e<0&&(e=t?this.length-1:0),this.view(e),this},next:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.length-1,i=this.index+1;return i>e&&(i=t?0:e),this.view(i),this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData;return this.moveTo(Xt(t)?t:i.x+Number(t),Xt(e)?e:i.y+Number(e)),this},moveTo:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=this.element,o=this.options,s=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&o.movable){var r=s.x,l=s.y,h=!1;if(Yt(t)?h=!0:t=r,Yt(i)?h=!0:i=l,h){if(Bt(o.move)&&oe(a,Tt,o.move,{once:!0}),!1===se(a,Tt,{x:t,y:i,oldX:r,oldY:l,originalEvent:n}))return this;s.x=t,s.y=i,s.left=t,s.top=i,this.moving=!0,this.renderImage((function(){e.moving=!1,Bt(o.moved)&&oe(a,Et,o.moved,{once:!0}),se(a,Et,{x:t,y:i,oldX:r,oldY:l,originalEvent:n},{cancelable:!1})}))}}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var e=this,i=this.element,n=this.options,a=this.imageData;if(Yt(t=Number(t))&&this.viewed&&!this.played&&n.rotatable){var o=a.rotate;if(Bt(n.rotate)&&oe(i,Dt,n.rotate,{once:!0}),!1===se(i,Dt,{degree:t,oldDegree:o}))return this;a.rotate=t,this.rotating=!0,this.renderImage((function(){e.rotating=!1,Bt(n.rotated)&&oe(i,It,n.rotated,{once:!0}),se(i,It,{degree:t,oldDegree:o},{cancelable:!1})}))}return this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=this.element,a=this.options,o=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&a.scalable){var s=o.scaleX,r=o.scaleY,l=!1;if(Yt(t)?l=!0:t=s,Yt(i)?l=!0:i=r,l){if(Bt(a.scale)&&oe(n,St,a.scale,{once:!0}),!1===se(n,St,{scaleX:t,scaleY:i,oldScaleX:s,oldScaleY:r}))return this;o.scaleX=t,o.scaleY=i,this.scaling=!0,this.renderImage((function(){e.scaling=!1,Bt(a.scaled)&&oe(n,At,a.scaled,{once:!0}),se(n,At,{scaleX:t,scaleY:i,oldScaleX:s,oldScaleY:r},{cancelable:!1})}))}}return this},zoom:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=this.imageData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(a.width*t/a.naturalWidth,e,i,n),this},zoomTo:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=this.element,r=this.options,l=this.pointers,h=this.imageData,c=h.x,u=h.y,d=h.width,m=h.height,g=h.naturalWidth,v=h.naturalHeight;if(Yt(t=Math.max(0,t))&&this.viewed&&!this.played&&(o||r.zoomable)){if(!o){var f=Math.max(.01,r.minZoomRatio),p=Math.min(100,r.maxZoomRatio);t=Math.min(Math.max(t,f),p)}if(a)switch(a.type){case"wheel":r.zoomRatio>=.055&&t>.95&&t<1.05&&(t=1);break;case"pointermove":case"touchmove":case"mousemove":t>.99&&t<1.01&&(t=1)}var b=g*t,w=v*t,y=b-d,x=w-m,k=h.ratio;if(Bt(r.zoom)&&oe(s,Ct,r.zoom,{once:!0}),!1===se(s,Ct,{ratio:t,oldRatio:k,originalEvent:a}))return this;if(this.zooming=!0,a){var z=function(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}(this.viewer),T=l&&Object.keys(l).length>0?function(t){var e=0,i=0,n=0;return Ht(t,(function(t){var a=t.startX,o=t.startY;e+=a,i+=o,n+=1})),{pageX:e/=n,pageY:i/=n}}(l):{pageX:a.pageX,pageY:a.pageY};h.x-=y*((T.pageX-z.left-c)/d),h.y-=x*((T.pageY-z.top-u)/m)}else Wt(n)&&Yt(n.x)&&Yt(n.y)?(h.x-=y*((n.x-c)/d),h.y-=x*((n.y-u)/m)):(h.x-=y/2,h.y-=x/2);h.left=h.x,h.top=h.y,h.width=b,h.height=w,h.oldRatio=k,h.ratio=t,this.renderImage((function(){e.zooming=!1,Bt(r.zoomed)&&oe(s,Ot,r.zoomed,{once:!0}),se(s,Ot,{ratio:t,oldRatio:k,originalEvent:a},{cancelable:!1})})),i&&this.tooltip()}return this},play:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var i=this.element,n=this.options;if(Bt(n.play)&&oe(i,_t,n.play,{once:!0}),!1===se(i,_t))return this;var a=this.player,o=this.loadImage.bind(this),s=[],r=0,l=0;if(this.played=!0,this.onLoadWhenPlay=o,e&&this.requestFullscreen(e),Zt(a,it),Ht(this.items,(function(t,e){var i=t.querySelector("img"),h=document.createElement("img");h.src=ee(i,"originalUrl"),h.alt=i.getAttribute("alt"),h.referrerPolicy=i.referrerPolicy,r+=1,Zt(h,W),Jt(h,nt,n.transition),Kt(t,q)&&(Zt(h,G),l=e),s.push(h),oe(h,ht,o,{once:!0}),a.appendChild(h)})),Yt(n.interval)&&n.interval>0){var h=function e(){clearTimeout(t.playing.timeout),Gt(s[l],G),Zt(s[l=(l+=1)<r?l:0],G),t.playing.timeout=setTimeout(e,n.interval)};r>1&&(this.playing={prev:function e(){clearTimeout(t.playing.timeout),Gt(s[l],G),Zt(s[l=(l-=1)>=0?l:r-1],G),t.playing.timeout=setTimeout(e,n.interval)},next:h,timeout:setTimeout(h,n.interval)})}return this},stop:function(){var t=this;if(!this.played)return this;var e=this.element,i=this.options;if(Bt(i.stop)&&oe(e,Ft,i.stop,{once:!0}),!1===se(e,Ft))return this;var n=this.player;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,Ht(n.getElementsByTagName("img"),(function(e){ae(e,ht,t.onLoadWhenPlay)})),Gt(n,it),n.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,e=this.options,i=this.viewer,n=this.image,a=this.list;return!this.isShown||this.played||this.fulled||!e.inline||(this.fulled=!0,this.open(),Zt(this.button,V),e.transition&&(Gt(a,nt),this.viewed&&Gt(n,nt)),Zt(i,B),i.setAttribute("role","dialog"),i.setAttribute("aria-labelledby",this.title.id),i.setAttribute("aria-modal",!0),i.removeAttribute("style"),$t(i,{zIndex:e.zIndex}),e.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=Vt({},this.containerData),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage((function(){e.transition&&setTimeout((function(){Zt(n,nt),Zt(a,nt)}),0)}))}))),this},exit:function(){var t=this,e=this.options,i=this.viewer,n=this.image,a=this.list;return this.isShown&&!this.played&&this.fulled&&e.inline?(this.fulled=!1,this.close(),Gt(this.button,V),e.transition&&(Gt(a,nt),this.viewed&&Gt(n,nt)),e.focus&&this.clearEnforceFocus(),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),Gt(i,B),$t(i,{zIndex:e.zIndexInline}),this.viewerData=Vt({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage((function(){e.transition&&setTimeout((function(){Zt(n,nt),Zt(a,nt)}),0)}))})),this):this},tooltip:function(){var t=this,e=this.options,i=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&e.tooltip?(i.textContent="".concat(Math.round(100*n.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):e.transition?(this.fading&&se(i,vt),Zt(i,it),Zt(i,W),Zt(i,nt),i.removeAttribute("aria-hidden"),i.initialOffsetWidth=i.offsetWidth,Zt(i,G)):(Zt(i,it),i.removeAttribute("aria-hidden")),this.tooltipping=setTimeout((function(){e.transition?(oe(i,vt,(function(){Gt(i,it),Gt(i,W),Gt(i,nt),i.setAttribute("aria-hidden",!0),t.fading=!1}),{once:!0}),Gt(i,G),t.fading=!0):(Gt(i,it),i.setAttribute("aria-hidden",!0)),t.tooltipping=!1}),1e3),this):this},toggle:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return 1===this.imageData.ratio?this.zoomTo(this.imageData.oldRatio,!0,null,t):this.zoomTo(1,!0,null,t),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=Vt({},this.initialImageData),this.renderImage()),this},update:function(){var t=this,e=this.element,i=this.options,n=this.isImg;if(n&&!e.parentNode)return this.destroy();var a=[];if(Ht(n?[e]:e.querySelectorAll("img"),(function(e){Bt(i.filter)?i.filter.call(t,e)&&a.push(e):t.getImageURL(e)&&a.push(e)})),!a.length)return this;if(this.images=a,this.length=a.length,this.ready){var o=[];if(Ht(this.items,(function(t,e){var i=t.querySelector("img"),n=a[e];n&&i&&n.src===i.src&&n.alt===i.alt||o.push(e)})),$t(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var s=o.indexOf(this.index);if(s>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-s,this.length-1),0));else{var r=this.items[this.index];Zt(r,q),r.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var t=this.element,e=this.options;return t[R]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),e.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):e.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),e.inline||ae(t,at,this.onStart),t[R]=void 0,this):this}},pe={getImageURL:function(t){var e=this.options.url;return e=Rt(e)?t.getAttribute(e):Bt(e)?e.call(this,t):""},enforceFocus:function(){var t=this;this.clearEnforceFocus(),oe(document,rt,this.onFocusin=function(e){var i=t.viewer,n=e.target;if(n!==document&&n!==i&&!i.contains(n)){for(;n;){if(null!==n.getAttribute("tabindex")||"true"===n.getAttribute("aria-modal"))return;n=n.parentElement}i.focus()}})},clearEnforceFocus:function(){this.onFocusin&&(ae(document,rt,this.onFocusin),this.onFocusin=null)},open:function(){var t=this.body;Zt(t,et),this.scrollbarWidth>0&&(t.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px"))},close:function(){var t=this.body;Gt(t,et),this.scrollbarWidth>0&&(t.style.paddingRight=this.initialBodyPaddingRight)},shown:function(){var t=this.element,e=this.options,i=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,e.focus&&(i.focus(),this.enforceFocus()),Bt(e.shown)&&oe(t,wt,e.shown,{once:!0}),!1!==se(t,wt)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,e=this.options,i=this.viewer;e.fucus&&this.clearEnforceFocus(),this.close(),this.unbind(),Zt(i,U),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),i.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.hiding=!1,this.destroyed||(Bt(e.hidden)&&oe(t,xt,e.hidden,{once:!0}),se(t,xt,null,{cancelable:!1}))},requestFullscreen:function(t){var e=this.element.ownerDocument;if(this.fulled&&!(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)){var i=e.documentElement;i.requestFullscreen?Wt(t)?i.requestFullscreen(t):i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):i.mozRequestFullScreen?i.mozRequestFullScreen():i.msRequestFullscreen&&i.msRequestFullscreen()}},exitFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change:function(t){var e=this.options,i=this.pointers,n=i[Object.keys(i)[0]];if(n){var a=n.endX-n.startX,o=n.endY-n.startY;switch(this.action){case M:0===a&&0===o||(this.pointerMoved=!0,this.move(a,o,t));break;case X:this.zoom(function(t){var e=I({},t),i=[];return Ht(t,(function(t,n){delete e[n],Ht(e,(function(e){var n=Math.abs(t.startX-e.startX),a=Math.abs(t.startY-e.startY),o=Math.abs(t.endX-e.endX),s=Math.abs(t.endY-e.endY),r=Math.sqrt(n*n+a*a),l=(Math.sqrt(o*o+s*s)-r)/r;i.push(l)}))})),i.sort((function(t,e){return Math.abs(t)<Math.abs(e)})),i[0]}(i),!1,null,t);break;case Y:this.action="switched";var s=Math.abs(a);s>1&&s>Math.abs(o)&&(this.pointers={},a>1?this.prev(e.loop):a<-1&&this.next(e.loop))}Ht(i,(function(t){t.startX=t.endX,t.startY=t.endY}))}},isSwitchable:function(){var t=this.imageData,e=this.viewerData;return this.length>1&&t.x>=0&&t.y>=0&&t.width<=e.width&&t.height<=e.height}},be=L.Viewer,we=(de=-1,function(){return de+=1}),ye=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e||1!==e.nodeType)throw new Error("The first argument is required and must be an element.");this.element=e,this.options=Vt({},_,Wt(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=we(),this.init()}var e,i,n;return e=t,n=[{key:"noConflict",value:function(){return window.Viewer=be,t}},{key:"setDefaults",value:function(t){Vt(_,Wt(t)&&t)}}],(i=[{key:"init",value:function(){var t=this,e=this.element,i=this.options;if(!e[R]){e[R]=this,i.focus&&!i.keyboard&&(i.focus=!1);var n="img"===e.localName,a=[];if(Ht(n?[e]:e.querySelectorAll("img"),(function(e){Bt(i.filter)?i.filter.call(t,e)&&a.push(e):t.getImageURL(e)&&a.push(e)})),this.isImg=n,this.length=a.length,this.images=a,this.initBody(),Xt(document.createElement(R).style.transition)&&(i.transition=!1),i.inline){var o=0,s=function(){var e;(o+=1)===t.length&&(t.initializing=!1,t.delaying={abort:function(){clearTimeout(e)}},e=setTimeout((function(){t.delaying=!1,t.build()}),0))};this.initializing={abort:function(){Ht(a,(function(t){t.complete||(ae(t,ht,s),ae(t,ct,s))}))}},Ht(a,(function(t){var e,i;t.complete?s():(oe(t,ht,e=function(){ae(t,ct,i),s()},{once:!0}),oe(t,ct,i=function(){ae(t,ht,e),s()},{once:!0}))}))}else oe(e,at,this.onStart=function(e){var n=e.target;"img"!==n.localName||Bt(i.filter)&&!i.filter.call(t,n)||t.view(t.images.indexOf(n))})}}},{key:"build",value:function(){if(!this.ready){var t=this.element,e=this.options,i=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>';var a=n.querySelector(".".concat(R,"-container")),o=a.querySelector(".".concat(R,"-title")),s=a.querySelector(".".concat(R,"-toolbar")),r=a.querySelector(".".concat(R,"-navbar")),l=a.querySelector(".".concat(R,"-button")),h=a.querySelector(".".concat(R,"-canvas"));if(this.parent=i,this.viewer=a,this.title=o,this.toolbar=s,this.navbar=r,this.button=l,this.canvas=h,this.footer=a.querySelector(".".concat(R,"-footer")),this.tooltipBox=a.querySelector(".".concat(R,"-tooltip")),this.player=a.querySelector(".".concat(R,"-player")),this.list=a.querySelector(".".concat(R,"-list")),a.id="".concat(R).concat(this.id),o.id="".concat(R,"Title").concat(this.id),Zt(o,e.title?ce(Array.isArray(e.title)?e.title[0]:e.title):U),Zt(r,e.navbar?ce(e.navbar):U),Jt(l,U,!e.button),e.keyboard&&l.setAttribute("tabindex",0),e.backdrop&&(Zt(a,"".concat(R,"-backdrop")),e.inline||"static"===e.backdrop||ie(h,Lt,"hide")),Rt(e.className)&&e.className&&e.className.split(jt).forEach((function(t){Zt(a,t)})),e.toolbar){var c=document.createElement("ul"),u=Wt(e.toolbar),d=Nt.slice(0,3),m=Nt.slice(7,9),g=Nt.slice(9);u||Zt(s,ce(e.toolbar)),Ht(u?e.toolbar:Nt,(function(t,i){var n=u&&Wt(t),a=u?te(i):t,o=n&&!Xt(t.show)?t.show:t;if(o&&(e.zoomable||-1===d.indexOf(a))&&(e.rotatable||-1===m.indexOf(a))&&(e.scalable||-1===g.indexOf(a))){var s=n&&!Xt(t.size)?t.size:t,r=n&&!Xt(t.click)?t.click:t,l=document.createElement("li");e.keyboard&&l.setAttribute("tabindex",0),l.setAttribute("role","button"),Zt(l,"".concat(R,"-").concat(a)),Bt(r)||ie(l,Lt,a),Yt(o)&&Zt(l,ce(o)),-1!==["small","large"].indexOf(s)?Zt(l,"".concat(R,"-").concat(s)):"play"===a&&Zt(l,"".concat(R,"-large")),Bt(r)&&oe(l,at,r),c.appendChild(l)}})),s.appendChild(c)}else Zt(s,U);if(!e.rotatable){var v=s.querySelectorAll('li[class*="rotate"]');Zt(v,J),Ht(v,(function(t){s.appendChild(t)}))}if(e.inline)Zt(l,H),$t(a,{zIndex:e.zIndexInline}),"static"===window.getComputedStyle(i).position&&$t(i,{position:"relative"}),i.insertBefore(a,t.nextSibling);else{Zt(l,P),Zt(a,B),Zt(a,W),Zt(a,U),$t(a,{zIndex:e.zIndex});var f=e.container;Rt(f)&&(f=t.ownerDocument.querySelector(f)),f||(f=this.body),f.appendChild(a)}e.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,Bt(e.ready)&&oe(t,pt,e.ready,{once:!0}),!1!==se(t,pt)?this.ready&&e.inline&&this.view(this.index):this.ready=!1}}}])&&A(e.prototype,i),n&&A(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();Vt(ye.prototype,me,ge,ve,fe,pe);const xe={class:"view-larger-image"},ke={class:"view-larger-image__left"},ze={class:"view-larger-image-left__header"},Te={class:"view-larger-image-header__right"},Ee={class:"view-large-image-left__body"},De={class:"view-large-image-left-body__cover"},Ie=["data-original","src"],Se={class:"view-larger-image__right"},Ae={class:"view-larger-image-right__content"},Ce=h("div",{class:"view-larger-image-right-content__title"},[h("span",null,"基础信息")],-1),Oe={class:"view-larger-image-right-content__item"},_e=h("label",null,"文件大小",-1),Fe={class:"view-larger-image-right-content__item"},Le=h("label",null,"尺寸",-1),je={class:"view-larger-image-right-content__item"},Ne=h("label",null,"文件格式",-1),Re={class:"view-larger-image-right-content__item"},Me=h("label",null,"添加日期",-1),Ye={class:"view-larger-image-right-content__item origin__path"},Xe=h("label",null,"来源路径",-1),qe={class:"origin-path__content"},Pe={class:"origin-path__item"},We=h("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/pic_1684138594288-1697631000719.png",alt:""},null,-1),Be={class:"origin-path__item"},He=h("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png",alt:""},null,-1),Ve={class:"view-larger-image-right__content"},Ue={class:"view-larger-image-right-content__title"},$e=i({__name:"viewLargerImage",setup(t){const e=n();let i=a({url:"",size:0,width:0,height:0,createTime:"",appName:"",appTags:[],appVersion:""}),v=a({});o();const f=async()=>{T.confirm("确认以此图喂给AI生成新图?时间有点长耐心等待啊","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const t=E.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"}),e=await y({name:i.value.appTags||"首页"});e?.data?.data&&(t.close(),window.open(e?.data?.data))})).catch((()=>{}))},p=a([]),b=a([]),D=a([]),I=(t,e)=>{if(console.log(t,e,"数据"),"app"===t)return p.value.filter((t=>t._id===e))[0].name;if("version"===t)return D.value.filter((t=>t._id===e))[0].name;{const t=b.value.filter((t=>t._id===e));return console.log(t,"标签"),t.map((t=>t.name)).join(",")}};s((async()=>{await(async()=>{const t=await k({});console.log(t,"数据"),0===t.data.code&&(p.value=t.data.data)})(),await(async()=>{const t=await x({});0===t.data.code&&(b.value=t.data.data)})(),await(async()=>{const t=await z({});0===t.data.code&&(D.value=t.data.data)})();const{query:t}=e;i.value={...t,appName:I("app",t.appId),appVersion:I("version",t.version),appTags:I("tags",t.tags)};const n=document.getElementById("imgTooles");v.value=new ye(n,{url:"data-original",show:function(){v.update()}})}));const S=()=>{let t="http://192.168.1.209:7777";window.open(t+=i.value.url)},A=t=>{v.value.zoomTo(parseInt(t)),v.value.show()};return(t,e)=>{const n=w;return r(),l("div",xe,[h("div",ke,[h("div",ze,[h("div",Te,[h("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/1rvzbhc4prwgs-1697808769379.png",alt:"",onClick:e[0]||(e[0]=t=>A(1))}),h("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/3g0qyyheb6e5d-1697808769379.png",alt:"",onClick:e[1]||(e[1]=t=>A(.5))}),h("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697808769379.png",alt:"",onClick:S})])]),h("div",Ee,[h("div",De,[h("img",{loading:"lazy",id:"imgTooles","data-original":`http://192.168.1.209:7777${c(i)?.url}`,src:`http://192.168.1.209:7777${c(i)?.url}`,"object-fit":"contain",alt:""},null,8,Ie)])])]),h("div",Se,[h("div",Ae,[Ce,h("div",Oe,[_e,h("span",null,u((c(i)?.size/1e3).toFixed(2))+"mb",1)]),h("div",Fe,[Le,h("span",null,u(c(i)?.width)+" * "+u(c(i)?.height),1)]),h("div",je,[Ne,h("span",null,u(c(i)?.url?.split(".")[1]),1)]),h("div",Re,[Me,h("span",null,u(new Date(parseInt(c(i)?.createTime)).toLocaleString().replace(/:\d{1,2}$/," ")),1)]),h("div",Ye,[Xe,h("div",qe,[h("div",Pe,[We,h("span",null,u(c(i)?.appName)+" V"+u(c(i).appVersion),1)]),h("div",Be,[He,h("span",null,u(c(i)?.appTags),1)])]),h("div",Ve,[h("div",Ue,[h("div",null,[d(n,{type:"primary",onClick:f},{default:m((()=>[g("AI灵感升级")])),_:1})])])])])])])])}}}),Ke={class:"home-body"},Ze={class:"home-body__right"},Ge=i({__name:"index",setup(i){const n=v();f(),a(1);const o=a([{_id:1,name:"全部应用"}]);return s((()=>{(async()=>{const t=await k({});console.log(t,"应用列表"),0===t.data.code&&(o.value=[...o.value,...t.data.data])})()})),(i,a)=>(r(),l("div",{class:b({home:!0,"home-theme":c(n).themeShow})},[d(e,{headBgColor:c(n).themeShow?"#26282B":"#FFFFFF"},{default:m((()=>[d(t)])),_:1},8,["headBgColor"]),h("div",{class:"home__line",style:p({background:c(n).themeShow?"#000000":"#F5F5F5"})},null,4),h("div",Ke,[h("div",Ze,[d($e)])])],2))}});export{Ge as default};
//# sourceMappingURL=chunk.09b40ee1.js.map
