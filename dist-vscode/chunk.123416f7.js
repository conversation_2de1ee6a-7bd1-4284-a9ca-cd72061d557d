import{h as e,A as t,m as a,o as n,k as r,E as l,x as i,y as o,c as s,_ as c,Q as d,$ as u,d as p}from"./chunk.8df321e8.js";import{E as m}from"./chunk.58688d79.js";import{d as f,r as h,L as y,g as v,o as g,c as b,O as x,n as k,u as w,X as _,Q as S,b as I,A as C,B as D,V as T,e as L,a4 as E,z as R,f as M,h as N,i as P,C as z,p as j,j as O,ax as $,F as A,a as F,w as B,q,k as V,H as U,I as W,Y as H,a6 as K,ay as X,M as G,y as Y,ag as Q,af as J,az as Z,aA as ee,aB as te,aC as ae}from"./index.05904f40.js";import{E as ne,a as re,b as le}from"./chunk.380b3154.js";import"./chunk.db8898e3.js";import"./chunk.034f7efa.js";import{h as ie,S as oe}from"./chunk.27187e73.js";/* empty css              */import{E as se}from"./chunk.1fa2ec58.js";import{E as ce,a as de,b as ue}from"./chunk.8e3d5584.js";/* empty css              */import{E as pe}from"./chunk.505381c5.js";import{l as me}from"./chunk.4ae50552.js";/* empty css              */import{E as fe}from"./chunk.159d03dd.js";import{v as he}from"./chunk.c83b4915.js";import{r as ye,a as ve,F as ge}from"./chunk.74ff160c.js";import{a as be,i as xe,h as ke}from"./chunk.8f74f9db.js";import{P as we}from"./chunk.6d705473.js";import{E as _e}from"./chunk.9a591dc8.js";import"./chunk.b2d62236.js";import"./chunk.0db0b66d.js";import"./chunk.6eac9d60.js";const Se=Symbol("breadcrumbKey"),Ie=e({separator:{type:String,default:"/"},separatorIcon:{type:t}}),Ce=f({name:"ElBreadcrumb"});var De=n(f({...Ce,props:Ie,setup(e){const t=e,n=a("breadcrumb"),r=h();return y(Se,t),v((()=>{const e=r.value.querySelectorAll(`.${n.e("item")}`);e.length&&e[e.length-1].setAttribute("aria-current","page")})),(e,t)=>(g(),b("div",{ref_key:"breadcrumb",ref:r,class:k(w(n).b()),"aria-label":"Breadcrumb",role:"navigation"},[x(e.$slots,"default")],2))}}),[["__file","breadcrumb.vue"]]);const Te=e({to:{type:r([String,Object]),default:""},replace:{type:Boolean,default:!1}}),Le=f({name:"ElBreadcrumbItem"});var Ee=n(f({...Le,props:Te,setup(e){const t=e,n=_(),r=S(Se,void 0),i=a("breadcrumb"),o=n.appContext.config.globalProperties.$router,s=h(),c=()=>{t.to&&o&&(t.replace?o.replace(t.to):o.push(t.to))};return(e,t)=>{var a,n;return g(),b("span",{class:k(w(i).e("item"))},[I("span",{ref_key:"link",ref:s,class:k([w(i).e("inner"),w(i).is("link",!!e.to)]),role:"link",onClick:c},[x(e.$slots,"default")],2),(null==(a=w(r))?void 0:a.separatorIcon)?(g(),C(w(l),{key:0,class:k(w(i).e("separator"))},{default:D((()=>[(g(),C(T(w(r).separatorIcon)))])),_:1},8,["class"])):(g(),b("span",{key:1,class:k(w(i).e("separator")),role:"presentation"},L(null==(n=w(r))?void 0:n.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const Re=i(De,{BreadcrumbItem:Ee}),Me=o(Ee),Ne=f({name:"ElTimeline",setup(e,{slots:t}){const n=a("timeline");return y("timeline",t),()=>E("ul",{class:[n.b()]},[x(t,"default")])}}),Pe=e({timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},center:{type:Boolean,default:!1},placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:t},hollow:{type:Boolean,default:!1}}),ze=f({name:"ElTimelineItem"});var je=n(f({...ze,props:Pe,setup(e){const t=e,n=a("timeline-item"),r=R((()=>[n.e("node"),n.em("node",t.size||""),n.em("node",t.type||""),n.is("hollow",t.hollow)]));return(e,t)=>(g(),b("li",{class:k([w(n).b(),{[w(n).e("center")]:e.center}])},[I("div",{class:k(w(n).e("tail"))},null,2),e.$slots.dot?N("v-if",!0):(g(),b("div",{key:0,class:k(w(r)),style:M({backgroundColor:e.color})},[e.icon?(g(),C(w(l),{key:0,class:k(w(n).e("icon"))},{default:D((()=>[(g(),C(T(e.icon)))])),_:1},8,["class"])):N("v-if",!0)],6)),e.$slots.dot?(g(),b("div",{key:1,class:k(w(n).e("dot"))},[x(e.$slots,"dot")],2)):N("v-if",!0),I("div",{class:k(w(n).e("wrapper"))},[e.hideTimestamp||"top"!==e.placement?N("v-if",!0):(g(),b("div",{key:0,class:k([w(n).e("timestamp"),w(n).is("top")])},L(e.timestamp),3)),I("div",{class:k(w(n).e("content"))},[x(e.$slots,"default")],2),e.hideTimestamp||"bottom"!==e.placement?N("v-if",!0):(g(),b("div",{key:1,class:k([w(n).e("timestamp"),w(n).is("bottom")])},L(e.timestamp),3))],2)],2))}}),[["__file","timeline-item.vue"]]);const Oe=i(Ne,{TimelineItem:je}),$e=o(je),Ae=e=>(j("data-v-37ca1a7c"),e=e(),O(),e),Fe={class:"scale-div"},Be=Ae((()=>I("i",{class:"iconfont sy-gicon-jian"},null,-1))),qe={class:"el-dropdown-link"},Ve=Ae((()=>I("div",{class:"dropdown-item-right"},[I("i",{class:"iconfont sy-gicon-vuesax-linear-command-square"}),z("  +  "),I("i",{class:"iconfont sy-gicon-plus_app"})],-1))),Ue=Ae((()=>I("div",{class:"dropdown-item-right"},[I("i",{class:"iconfont sy-gicon-vuesax-linear-command-square"}),z("  +  "),I("i",{class:"iconfont sy-gicon-jian1"})],-1))),We=Ae((()=>I("i",{class:"iconfont sy-gicon-plus-border"},null,-1))),He=c(f({__name:"scale",props:{num:{}},emits:["change"],setup(e,{emit:t}){const a=e,n=t;v((()=>{ie("command-=",{splitKey:"-"},(e=>{e.preventDefault(),l(.25)})),ie("command+-",(e=>{e.preventDefault(),l(-.25)}))}));const r=e=>{switch(e){case"1":l(.25);break;case"2":l(-.25);break;case"3":break;case"4":n("change",2);break;case"5":n("change",1.5);break;case"6":n("change",1);break;case"7":n("change",.75);break;case"8":n("change",.5);break;case"9":n("change",.25)}},l=e=>{const t=a.num+e;t<.25||t>4||n("change",t)};return(e,t)=>{const a=s,n=ne,i=re,o=le;return g(),b("div",Fe,[P(a,{type:"info",text:"",disabled:e.num<=.25,onClick:t[0]||(t[0]=e=>l(-.25))},{default:D((()=>[Be])),_:1},8,["disabled"]),P(o,{onCommand:r,class:"scale-num","popper-class":"scale-num-popper",trigger:"click"},{dropdown:D((()=>[P(i,null,{default:D((()=>[P(n,{command:"1"},{default:D((()=>[z(" 放大 "),Ve])),_:1}),P(n,{command:"2"},{default:D((()=>[z(" 缩小 "),Ue])),_:1}),P(n,{command:"3",disabled:""},{default:D((()=>[z("全览")])),_:1}),P(n,{command:"4",divided:""},{default:D((()=>[z("200%")])),_:1}),P(n,{command:"5"},{default:D((()=>[z("150%")])),_:1}),P(n,{command:"6"},{default:D((()=>[z("100%")])),_:1}),P(n,{command:"7"},{default:D((()=>[z("75%")])),_:1}),P(n,{command:"8"},{default:D((()=>[z("50%")])),_:1}),P(n,{command:"8"},{default:D((()=>[z("25%")])),_:1}),P(n,{disabled:"",divided:""},{default:D((()=>[z("适应屏幕")])),_:1}),P(n,{disabled:""},{default:D((()=>[z("实际大小")])),_:1})])),_:1})])),default:D((()=>[I("span",qe,L(100*e.num)+"% ",1)])),_:1}),P(a,{type:"info",text:"",disabled:e.num>=4,onClick:t[1]||(t[1]=e=>l(.25))},{default:D((()=>[We])),_:1},8,["disabled"])])}}}),[["__scopeId","data-v-37ca1a7c"]]),Ke={Coordinate:"坐标",Overlay:"高亮区域","Top Width":"宽度-顶部","Middle Width":"宽度-居中","Bottom Width":"宽度-底部","Left Height":"高度-左侧","Center Height":"高度-居中","Right Height":"高度-右侧","Vertical Distance":"垂直间距","Top Spacing":"上边距","Bottom Spacing":"下边距","Horizontal Distance":"水平间距","Left Spacing":"左边距","Right Spacing":"右边距","Label on top":"标记-顶部","Label on right":"标记-右侧","Make Note":"备注","Label on bottom":"标记-底部","Label on left":"标记-左侧",Influence:"影响范围","Sizing by influence":"根据影响范围标记尺寸",Percentage:"百分比","Sizing by percentage":"标记百分比尺寸","Toggle Hidden":"切换可见","Toggle Locked":"切换锁定","Clean Marks":"清除选定区域或全部标注",Settings:"设置","Design resolution":"设计分辨率","Unit switch":"切换单位","Device switch":"切换设备","Convert to pixels":"转换为像素值","Convert to rem":"转换为 rem 值",FLOW:"原型模式",NOTES:"备注",PROPERTIES:"属性",FILLS:"填充",TYPEFACE:"字体",BORDERS:"边框",SHADOWS:"阴影","CSS STYLE":"CSS 样式","CODE TEMPLATE":"代码模板",EXPORTABLE:"切图",Gradient:"渐变",Color:"颜色","Layer Name":"图层名称",Weight:"粗细","Style name":"样式名称",Custom:"自定义",Standard:"标准像素","iOS Devices":"iOS 设备",Points:"标准点",Retina:"视网膜","Retina HD":"高清视网膜","Android Devices":"安卓设备","Other Devices":"其他设备","Ubuntu Grid":"Ubuntu 网格","Web View":"网页",Scale:"倍率",Units:"单位","Device Unit":"设备单位","Design Resolution":"设计分辨率","%@px on Sketch = 1%@ on device":"%@px Sketch = 1%@ 设备","Color format":"颜色格式","Color hex":"色值","ARGB hex":"安卓色值","Artboard order":"画板排序","Order by artboard rows":"按画板行排序","Order by artboard columns":"按画板列排序","Order by alphabet":"按名称排序","Order by layer order":"按图层顺序",Positive:"正序",Reverse:"逆序",Save:"保存",Width:"宽度",Height:"高度",Top:"上面",Right:"右侧",Bottom:"下面",Left:"左侧","Fill / Color":"填充 / 颜色",Border:"边框",Opacity:"不透明度",Radius:"圆角",Shadow:"外(内)阴影",Style:"样式名称","Font size":"字号",Line:"行高",Typeface:"字体",Character:"字间距",Paragraph:"段落间距","Percentage of artboard":"基于画板百分比单位",Mark:"标注",All:"全选",None:"全不选","Select filtered":"选中过滤的","Selection of Sketch":"Sketch 的选中画板","Current of Sketch":"Sketch 的当前画板",Filter:" 过滤",Export:"导出",Position:"位置",Size:"大小",Family:"字体",Spacing:"空间",Content:"内容","All artboards":"全部画板","Start points":"起点画板","No slices added!":"未添加切图","No color names added!":"未添加颜色名称","Select 1 or 2 layers to mark!":"请选中 1 至 2 个图层!","Select a text layer to mark!":"请选中 1 个文本图层!","Select a layer to mark!":"请选中 1 个图层!","Select any layer to mark!":"请选中任意个图层!","Export spec":"导出规范","Export to:":"导出到:","Exporting...":"导出中...","Please wait for former task to exit":"请等待先前的任务完成","Cancelled by user":"用户取消了任务","Export complete! Takes %@ seconds":"导出完成! 耗时 %@ 秒","The slice not in current artboard.":"切图不在当前画板","Inside Border":"内边框","Outside Border":"外边框","Center Border":"中心边框","Inner Shadow":"内阴影","Outer Shadow":"外阴影",Offset:"偏移",Effect:"效果",Blur:"模糊",Spread:"扩散","No artboards!":"没有画板","You need add some artboards.":"您需要添加一些画板","No colors added!":"没有定义颜色","Select a layer to add exportable!":"请选中 1 个图层!","Import complete!":"导入完成!","Processing layer %@ of %@":"图层处理中... %@ / %@","Advanced mode":"高级模式","Export layer influence rect":"导出图层的影响尺寸","Keyboard shortcut":"快捷键"};function Xe(e){return Ke[e]?Ke[e]:e}var Ge=(e=>(e[e.vtop=32]="vtop",e[e.vbottom=16]="vbottom",e[e.vmiddle=8]="vmiddle",e[e.hleft=4]="hleft",e[e.hright=2]="hright",e[e.hcenter=1]="hcenter",e))(Ge||{});const Ye={class:"unit-div"},Qe={class:"unit-div-link"},Je=c(f({__name:"unit",setup(e){const t=$(),a=[{name:Xe("Device switch"),units:[{name:Xe("Web View")+" - px",unit:"px",scale:1},{name:Xe("iOS Devices")+" - pt",unit:"pt",scale:1},{name:Xe("Android Devices")+" - dp/sp",unit:"dp/sp",scale:1}]},{name:Xe("Convert to pixels"),units:[{name:"IOS "+Xe("Points")+" @1x",unit:"px",scale:1},{name:"IOS "+Xe("Retina")+" @2x",unit:"px",scale:2},{name:"IOS "+Xe("Retina HD")+" @3x",unit:"px",scale:3},{name:"Android LDPI @0.75x",unit:"px",scale:.75},{name:"Android MDPI @1x",unit:"px",scale:1},{name:"Android HDPI @1.5x",unit:"px",scale:1.5},{name:"Android XHDPI @2x",unit:"px",scale:2},{name:"Android XXHDPI @3x",unit:"px",scale:3},{name:"Android XXXHDPI @4x",unit:"px",scale:4}]},{name:Xe("Convert to others"),units:[{name:"CSS Rem 8px",unit:"rem",scale:1/8},{name:"CSS Rem 10px",unit:"rem",scale:.1},{name:"CSS Rem 12px",unit:"rem",scale:1/12},{name:"CSS Rem 14px",unit:"rem",scale:1/14},{name:"CSS Rem 16px",unit:"rem",scale:1/16},{name:Xe("Ubuntu Grid"),unit:"gu",scale:1/27}]}],n=R((()=>{let e=null;return t.state.unitName?(a.find((a=>{const n=a.units.find((e=>e.unit==t.state.unit&&e.name==t.state.unitName));if(n)return e=n,!0})),e):a[0].units[0]})),r=e=>{t.state.unit=e.unit,t.state.scale=e.scale,t.state.unitName=e.name};return(e,t)=>{const i=l,o=ne,s=re,c=le;return g(),b("div",Ye,[P(c,{trigger:"click",onCommand:r},{dropdown:D((()=>[P(s,null,{default:D((()=>[(g(),b(A,null,F(a,((e,t)=>(g(),b(A,{key:e.name},[P(o,{disabled:"",divided:0!=t},{default:D((()=>[z(L(e.name),1)])),_:2},1032,["divided"]),(g(!0),b(A,null,F(e.units,((e,t)=>(g(),C(o,{key:e.name,command:e,divided:0==t},{default:D((()=>[z(L(e.name),1)])),_:2},1032,["command","divided"])))),128))],64)))),64))])),_:1})])),default:D((()=>[I("span",Qe,[z(L(n.value?.name||"请选择"),1),P(i,{class:"el-icon--right"},{default:D((()=>[P(w(d))])),_:1})])])),_:1})])}}}),[["__scopeId","data-v-0a89eddd"]]);function Ze(e,t){const{state:a,project:n}=$(),r=e/n.resolution,l=Math.round(r*a.scale*100)/100,i=a.unit.split("/");let o=i[0];return i.length>1&&t&&(o=i[1]),l+o}function et(e){const{state:t}=$();return Math.round(e/t.scale*10)/10}function tt(e){const{state:t}=$();return t.current.layers[e].rect}function at(e){const{state:t,project:a}=$();return e*t.zoom/a.resolution}function nt(e,t){return Math.round(e/t*1e3)/10+"%"}function rt(){const{state:e}=$();if(void 0===e.selectedIndex)return;if(e.selectedIndex==e.targetIndex&&!e.tempTargetRect)return;const t=tt(e.selectedIndex),a=e.tempTargetRect||tt(e.targetIndex);let n,r,l,i;const o=at(t.x+t.width/2),s=at(t.y+t.height/2),c=t.x+t.width,d=t.y+t.height,u=a.x+a.width,p=a.y+a.height;if(function(e,t){const a=Math.max(e.x,t.x),n=Math.max(e.y,t.y),r=Math.min(e.x+e.width,t.x+t.width)-a,l=Math.min(e.y+e.height,t.y+t.height)-n;if(!(r<=0||l<=0))return{x:a,y:n,width:r,height:l}}(t,a)){const h=(f=a,{top:(m=t).y-f.y,right:f.x+f.width-m.x-m.width,bottom:f.y+f.height-m.y-m.height,left:m.x-f.x});0!=h.top&&(n={x:o,y:h.top>0?at(a.y):at(t.y),h:at(Math.abs(h.top)),distance:Ze(Math.abs(h.top)),percentageDistance:nt(Math.abs(h.top),e.current.height)}),0!=h.right&&(r={x:h.right>0?at(c):at(u),y:s,w:at(Math.abs(h.right)),distance:Ze(Math.abs(h.right)),percentageDistance:nt(Math.abs(h.right),e.current.width)}),0!=h.bottom&&(l={x:o,y:h.bottom>0?at(d):at(p),h:at(Math.abs(h.bottom)),distance:Ze(Math.abs(h.bottom)),percentageDistance:nt(Math.abs(h.bottom),e.current.height)}),0!=h.left&&(i={x:h.left>0?at(a.x):at(t.x),y:s,w:at(Math.abs(h.left)),distance:Ze(Math.abs(h.left)),percentageDistance:nt(Math.abs(h.left),e.current.width)})}else t.y>p&&(n={x:o,y:at(p),h:at(t.y-p),distance:Ze(t.y-p),percentageDistance:nt(t.y-p,e.current.height)}),c<a.x&&(r={x:at(c),y:s,w:at(a.x-c),distance:Ze(a.x-c),percentageDistance:nt(a.x-c,e.current.width)}),d<a.y&&(l={x:o,y:at(d),h:at(a.y-d),distance:Ze(a.y-d),percentageDistance:nt(a.y-d,e.current.height)}),t.x>u&&(i={x:at(u),y:s,w:at(t.x-u),distance:Ze(t.x-u),percentageDistance:nt(t.x-u,e.current.width)});var m,f;if(n){const e=document.querySelector("#td");e.style.left=n.x+"px",e.style.top=n.y+"px",e.style.height=n.h+"px",e.style.display="";const t=document.querySelector("#td div");t.setAttribute("data-height",n.distance),t.setAttribute("percentage-height",n.percentageDistance)}if(r){const e=document.querySelector("#rd");e.style.left=r.x+"px",e.style.top=r.y+"px",e.style.width=r.w+"px",e.style.display="";const t=document.querySelector("#rd div");t.setAttribute("data-width",r.distance),t.setAttribute("percentage-width",r.percentageDistance)}if(l){const e=document.querySelector("#bd");e.style.left=l.x+"px",e.style.top=l.y+"px",e.style.height=l.h+"px",e.style.display="";const t=document.querySelector("#bd div");t.setAttribute("data-height",l.distance),t.setAttribute("percentage-height",l.percentageDistance)}if(i){const e=document.querySelector("#ld");e.style.left=i.x+"px",e.style.top=i.y+"px",e.style.width=i.w+"px",e.style.display="";const t=document.querySelector("#ld div");t.setAttribute("data-width",i.distance),t.setAttribute("percentage-width",i.percentageDistance)}const h=document.querySelector(".selected");null!==h&&h.classList.add("hidden")}let lt,it=!1,ot=!1;function st(e){if(32!==e.which)return;const t=document.getElementById("cursor"),a=document.querySelector(".screen-viewer");t.style.display="",a.classList.add("moving-screen"),ft(),mt(),it=!0,e.preventDefault()}function ct(e){if(32!==e.which)return;const t=document.getElementById("cursor"),a=document.querySelector(".screen-viewer");t.style.display="none",t.classList.remove("moving"),a.classList.remove("moving-screen"),it=!1,ot=!1,e.preventDefault()}function dt(e){const t=document.getElementById("cursor");if(null!==t&&(t.style.left=e.clientX+"px",t.style.top=e.clientY-48+"px"),!ot)return;const a=document.querySelector(".screen-viewer");a.scrollLeft=lt.x-e.clientX+lt.scrollLeft,a.scrollTop=lt.y-e.clientY+lt.scrollTop,e.preventDefault()}function ut(e){if(!it)return;const t=document.getElementById("cursor"),a=document.querySelector(".screen-viewer");t.classList.add("moving"),lt={x:e.clientX,y:e.clientY,scrollLeft:a.scrollLeft,scrollTop:a.scrollTop},ot=!0,e.preventDefault()}function pt(e){if(!it||!ot)return;const t=document.getElementById("cursor");document.querySelector(".screen-viewer").classList.remove("moving-screen"),t.classList.remove("moving"),ot=!1,e.preventDefault()}function mt(){["#td","#rd","#bd","#ld"].forEach((e=>{const t=document.querySelector(e);null!==t&&(t.style.display="none")})),document.querySelector(".selected")?.classList.remove("hidden")}function ft(){document.querySelector(".hover")?.classList.remove("hover");const e=document.querySelector("#rulers");null!==e&&(e.style.display="none")}function ht(){const{state:e}=$();if(null==e.selectedIndex)return!1;document.querySelector(".selected")?.classList.remove("selected"),document.querySelector("#layer-"+e.selectedIndex)?.classList.add("selected");const t=document.querySelector("#rulers");null!==t&&(t.style.display="none")}const yt=function(e){const t=$();if(it)return;if(function(e,t,a){let n=t.target;for(;n&&n!==e;){if(n.matches(a))return n;n=n.parentElement}}(document.body,e,".sketch-nav,.sketch-artboards"))return void e.stopPropagation();t.slicesVisible=!1,t.historyVisible=!1,t.colorsVisible=!1;const a=e.target;if(a.classList.contains("layer")||a.classList.contains("slices-layer")){const e=a.classList.contains("slices-layer")?document.querySelector(".layer-"+a.dataset.objectid):a;return t.state.selectedIndex=parseInt(e.dataset.index),mt(),ft(),void ht()}!function(){const{state:e}=$();if(void 0===e.selectedIndex)return!1;document.querySelector("#layer-"+e.selectedIndex)?.classList.remove("selected");const t=document.querySelector("#rulers");null!==t&&(t.style.display="none"),document.querySelector("#inspector")?.classList.remove("active"),e.selectedIndex=void 0,e.tempTargetRect=void 0,mt()}()},vt=function(e){if(it)return;const{state:t}=$();ft(),mt();const a=e.target;a.classList.contains("screen-viewer")||a.classList.contains("screen-viewer-inner")?(t.tempTargetRect=function(e){const{state:t}=$(),a=document.querySelector("#screen").getBoundingClientRect();let n=(e.pageX-a.left)/t.zoom,r=(e.pageY-a.top)/t.zoom,l=10,i=10;const o=n>=0&&n<=t.current.width,s=r>=0&&r<=t.current.height;return n<=0&&r<=0?(n=-10,r=-10):n>=t.current.width&&r<=0?(n=t.current.width,r=-10):n>=t.current.width&&r>=t.current.height?(n=t.current.width,r=t.current.height):n<=0&&r>=t.current.height?(n=-10,r=t.current.height):r<=0&&o?(n=0,r=-10,l=t.current.width):n>=t.current.width&&s?(n=t.current.width,r=0,i=t.current.height):r>=t.current.height&&o?(n=0,r=t.current.height,l=t.current.width):n<=0&&s&&(n=-10,r=0,i=t.current.height),o&&s&&(n=0,r=0,l=t.current.width,i=t.current.height),{x:n,y:r,width:l,height:i}}(e),t.targetIndex=0,rt()):a.classList.contains("layer")?(t.targetIndex=parseInt(e.target.dataset.index),t.tempTargetRect=void 0,function(){const{state:e}=$();if(e.targetIndex&&e.selectedIndex==e.targetIndex)return!1;const t=document.querySelector("#layer-"+e.targetIndex);t.classList.add("hover");const a=document.querySelector("#rv");a.style.left=t.offsetLeft+"px",a.style.width=t.offsetWidth+"px";const n=document.querySelector("#rh");n.style.top=t.offsetTop+"px",n.style.height=t.offsetHeight+"px";const r=document.querySelector("#rulers");null!==r&&(r.style.display="")}(),rt()):t.tempTargetRect=void 0},gt={key:0,class:"slices-wrapper"},bt=["onClick"],xt={class:"slices-pic"},kt=["src"],wt={class:"slices-right"},_t={class:"slices-title"},St={class:"slices-info"},It={class:"slices-tips"},Ct=(e=>(j("data-v-5f2b5cce"),e=e(),O(),e))((()=>I("div",{class:"slices-placeholder"},"该画板暂无切片，若有需要，请联系 UI",-1))),Dt=c(f({__name:"slices",props:{slices:{}},emits:["copy"],setup(e,{emit:t}){const a=$(),n=e;B((()=>n.slices),(e=>{console.log(e)}));const r=t;return(e,t)=>{const n=s;return g(),b("div",{onClick:t[0]||(t[0]=q((()=>{}),["stop"])),class:k(["slices-div",{show:w(a).slicesVisible}])},[e.slices&&e.slices.length?(g(),b("ul",gt,[(g(!0),b(A,null,F(e.slices,((e,t)=>(g(),b(A,{key:t},[(g(!0),b(A,null,F(e.exportable,((t,l)=>(g(),b("li",{onClick:t=>(e=>{const t=document.querySelector(".layer-"+e);a.state.selectedIndex=parseInt(t.dataset.index),mt(),ft(),ht()})(e.objectID),class:"slices-item",key:l},[I("picture",xt,[I("img",{src:t.path,alt:""},null,8,kt)]),I("div",wt,[I("div",_t,L(t.name),1),I("div",St,[I("div",It,L(w(Ze)(e.rect.width))+"  x  "+L(w(Ze)(e.rect.height)),1),P(n,{type:"primary",size:"small",onClick:q((e=>{return a=t.path,void r("copy",a);var a}),["stop"]),round:""},{default:D((()=>[z("复制链接")])),_:2},1032,["onClick"])])])],8,bt)))),128))],64)))),128))])):N("",!0),Ct],2)}}}),[["__scopeId","data-v-5f2b5cce"]]),Tt={class:"colors-wrapper"},Lt={class:"colors-name"},Et={class:"colors-tips"},Rt=c(f({__name:"colors",props:{colors:{},colorFormat:{}},setup(e){const t=$(),a=e;return B((()=>a.colors),(e=>{console.log(e)})),(e,a)=>(g(),b("div",{onClick:a[0]||(a[0]=q((()=>{}),["stop"])),class:k(["colors-div",{show:w(t).colorsVisible}])},[I("ul",Tt,[(g(!0),b(A,null,F(e.colors,((t,a)=>(g(),b("li",{key:a,class:"colors-item"},[I("div",{class:"colors-show",style:M({background:t.color["css-rgba"]})},null,4),I("div",Lt,[I("b",null,L(t.name),1),I("div",Et,L(t.color[e.colorFormat]),1)])])))),128))])],2))}}),[["__scopeId","data-v-ae44c743"]]),Mt=e=>(j("data-v-1a7ef4a4"),e=e(),O(),e),Nt={class:"sketch-artboards"},Pt={class:"search-box"},zt={key:0,class:"search-name"},jt={key:1,class:"search-container"},Ot=[Mt((()=>I("i",{class:"iconfont icon-sousuo"},null,-1)))],$t={class:"list"},At={class:"tree-list-item"},Ft={class:"item-box"},Bt={class:"dnd-item"},qt=Mt((()=>I("i",{class:"iconfont icon-a-sucaiku3x"},null,-1))),Vt={class:"name"},Ut=["id","onClick"],Wt={class:"item"},Ht={class:"item-img-box"},Kt=["src"],Xt={class:"item-name"},Gt=["src"],Yt=c(f({__name:"artboards",props:{activeSketchId:{},group:{},groupDataList:{}},emits:["change"],setup(e,{emit:t}){const a=h([]),n=e,r=t,l=h(!0),i=h(""),o=h(null),s=h(!1);v((()=>{ie("command+f",(e=>{c(!0),e.preventDefault()}))})),B((()=>n.groupDataList),(()=>{p(),n.activeSketchId&&V((()=>{m(n.activeSketchId)}))}));const c=e=>{s.value=void 0===e?!s.value:e,V((()=>{s.value?o.value?.focus():m(n.activeSketchId)}))},d=()=>{l.value=!l.value},u=()=>{s.value=!1,i.value=""},p=()=>{a.value=n.groupDataList.filter((e=>e.slug.includes(i.value))),l.value=!0,i.value||V((()=>{m(n.activeSketchId)}))},m=e=>{};return(e,t)=>{const n=pe,m=ce,f=se,h=U("click-outside");return g(),b("div",Nt,[I("div",Pt,[s.value?W((g(),b("div",jt,[P(n,{clearable:"",style:{width:"224px"},onClear:p,ref_key:"inputRef",ref:o,modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=e=>i.value=e),placeholder:"搜索",onKeyup:H(p,["enter"])},null,8,["modelValue"])])),[[h,u]]):(g(),b("div",zt,"全部")),I("div",{class:"search-icon",onClick:t[1]||(t[1]=q((e=>c()),["stop"]))},Ot)]),P(m),I("div",$t,[I("div",At,[I("div",Ft,[I("div",Bt,[I("div",{class:"dnd-item-box",onClick:d},[I("i",{class:k({"iconfont icon-jiantouzhankai":!0,"active-item":l.value})},null,2),qt,I("div",Vt,L(e.group?.name||"未分组"),1)]),(g(!0),b(A,null,F(a.value,(t=>W((g(),b("div",{id:t._id,class:k({"item-box-child":!0,"active-item":e.activeSketchId===t._id}),key:t._id,onClick:e=>{return a=t._id,void r("change",a);var a}},[P(f,{"show-after":500,width:"220px",placement:"right",trigger:"hover"},{reference:D((()=>[I("div",Wt,[I("div",Ht,[I("img",{alt:"",src:t.imagePath},null,8,Kt)]),I("div",Xt,L(t.name),1)])])),default:D((()=>[I("img",{style:{width:"200px"},alt:"",src:t.imagePath},null,8,Gt)])),_:2},1024)],10,Ut)),[[K,l.value]]))),128))])])])])])}}}),[["__scopeId","data-v-1a7ef4a4"]]),Qt={},Jt=e=>(j("data-v-f94b492c"),e=e(),O(),e),Zt={id:"rulers",style:{display:"none"}},ea=[Jt((()=>I("div",{id:"rv",class:"ruler v"},null,-1))),Jt((()=>I("div",{id:"rh",class:"ruler h"},null,-1)))];const ta=c(Qt,[["render",function(e,t){return g(),b("div",Zt,ea)}],["__scopeId","data-v-f94b492c"]]);var aa=(e=>(e.text="text",e.symbol="symbol",e.slice="slice",e.shape="shape",e.group="group",e.hotspot="hotspot",e))(aa||{});const na={id:"layers"},ra=["percentage-width","percentage-height","data-width","data-height","data-index","id"],la=I("i",{class:"tl"},null,-1),ia=I("i",{class:"tr"},null,-1),oa=I("i",{class:"bl"},null,-1),sa=I("i",{class:"br"},null,-1),ca=I("b",{class:"et h"},null,-1),da=I("b",{class:"er v"},null,-1),ua=I("b",{class:"eb h"},null,-1),pa=I("b",{class:"el v"},null,-1),ma=f({__name:"layers",setup(e){const t=$(),a=R((()=>t.state.current)),n=e=>({left:`${at(e.rect.x)}px`,top:`${at(e.rect.y)}px`,width:`${at(e.rect.width)}px`,height:`${at(e.rect.height)}px`});return(e,r)=>(g(),b("div",na,[(g(!0),b(A,null,F(a.value.layers,((e,r)=>(g(),b(A,{key:e.objectID},[e.type!==w(aa).group&&e.type!==w(aa).hotspot?(g(),b("div",{key:0,"percentage-width":w(nt)(e.rect.width,a.value.width),"percentage-height":w(nt)(e.rect.height,a.value.height),"data-width":w(Ze)(e.rect.width),"data-height":w(Ze)(e.rect.height),"data-index":r,id:`layer-${r}`,style:M(n(e)),class:k(["layer",`layer-${e.objectID}`,w(t).state.selectedIndex==r?"selected":""])},[la,ia,oa,sa,z(),ca,da,ua,pa],14,ra)):N("",!0)],64)))),128))]))}}),fa={id:"notes"},ha=["data-index"],ya={style:{"white-space":"nowrap",display:"none"}},va=c(f({__name:"notes",setup(e){const t=$(),a=R((()=>t.state.current));return(e,t)=>(g(),b("div",fa,[(g(!0),b(A,null,F(a.value.notes,((e,t)=>(g(),b("div",{class:"note",key:t,"data-index":t+1,style:M({left:`${w(at)(e.rect.x)}px`,top:`${w(at)(e.rect.y)}px`})},[I("div",ya,L(e.note),1)],12,ha)))),128))]))}}),[["__scopeId","data-v-69bcc680"]]),ga={},ba=X('<div id="td" class="distance v" style="display:none;" data-v-de2c8623><div data-height="3" data-v-de2c8623></div></div><div id="rd" class="distance h" style="display:none;" data-v-de2c8623><div data-width="" data-v-de2c8623></div></div><div id="bd" class="distance v" style="display:none;" data-v-de2c8623><div data-height="" data-v-de2c8623></div></div><div id="ld" class="distance h" style="display:none;" data-v-de2c8623><div data-width="" data-v-de2c8623></div></div>',4);const xa=c(ga,[["render",function(e,t){return ba}],["__scopeId","data-v-de2c8623"]]),ka={class:"screen-viewer"},wa=(e=>(j("data-v-e81eac28"),e=e(),O(),e))((()=>I("div",{class:"overlay"},null,-1))),_a=c(f({__name:"screen",setup(e){const t=h(!0),a=$(),n=R((()=>a.state.current)),r=R((()=>{const e=n.value.imagePath;return{width:at(n.value.width)+"px",height:at(n.value.height)+"px",background:"#FFF url("+e+") no-repeat",backgroundSize:at(n.value.width)+"px "+at(n.value.height)+"px"}})),l=G({});return B((()=>n.value),(e=>{me.isEmpty(e)||V((()=>{(()=>{const e=document.querySelector(".screen-viewer"),t=5*Math.max(n.value.width,n.value.height,e.clientWidth,e.clientHeight);l.width=t+"px",l.height=t+"px",V((()=>{const a=document.querySelector("#screen");a.style.marginLeft=-at(n.value.width/2)+"px",a.style.marginTop=-at(n.value.height/2)+"px",e.scrollLeft=(t-e.clientWidth)/2;let r=a.clientHeight>e.clientHeight?a.clientHeight:e.clientHeight;e.scrollTop=(t-r)/2}))})()}))}),{immediate:!0}),B((()=>a.state.zoom),((e,a)=>{t.value=!1;let n=document.querySelector(".screen-viewer"),r=document.querySelector("#screen"),l=r.getBoundingClientRect(),i=function(e,t){let a=e.getBoundingClientRect(),n=t.getBoundingClientRect(),r=a.x+a.width/2,l=a.y+a.height/2;return{x:r-n.x,y:l-n.y}}(n,r),o=i.x*e/a,s=i.y*e/a;!function(e){const t=e.fromRect||e.target.getBoundingClientRect(),a=e.fromEdge,n=e.toEdge,r=!!(56&a),l=!!(56&n);let i=0,o=0;!!(7&a)&&!!(7&n)&&(i=e.toRect.x-t.x,a&Ge.hcenter&&(i-=t.width/2),a&Ge.hright&&(i-=t.width),n&Ge.hcenter&&(i+=e.toRect.width/2),n&Ge.hright&&(i+=e.toRect.width)),r&&l&&(o=e.toRect.y-t.y,a&Ge.vmiddle&&(o-=t.height/2),a&Ge.vbottom&&(o-=t.height),n&Ge.vmiddle&&(o+=e.toRect.height/2),n&Ge.vbottom&&(o+=e.toRect.height)),e.scroller.scrollTop-=o,e.scroller.scrollLeft-=i}({scroller:n,target:r,toRect:l,fromEdge:Ge.hleft|Ge.vtop,toEdge:Ge.hleft|Ge.vtop}),n.scrollLeft+=o-i.x,n.scrollTop+=s-i.y,t.value=!0})),(e,a)=>(g(),b("section",ka,[I("div",{class:"screen-viewer-inner",style:M(l)},[I("div",{id:"screen",style:M(r.value),class:"screen"},[P(ta),t.value?(g(),b(A,{key:0},[P(ma),P(va)],64)):N("",!0),P(xa)],4)],4),wa]))}}),[["__scopeId","data-v-e81eac28"]]),Sa=["onClick"],Ia={class:"history-item__user"},Ca=["src"],Da={class:"history-item__content"},Ta={class:"slug"},La={class:"time"},Ea=c(f({__name:"history",props:{history:{},id:{},loading:{type:Boolean}},emits:["click"],setup(e,{emit:t}){const a=$(),n=t;return(e,t)=>{const r=fe,l=$e,i=Oe,o=he;return W((g(),b("div",{onClick:t[0]||(t[0]=q((()=>{}),["stop"])),class:k(["history-div",{show:w(a).historyVisible}])},[P(i,null,{default:D((()=>[(g(!0),b(A,null,F(e.history,((t,a)=>(g(),C(l,{key:a},{default:D((()=>{return[I("div",{class:k(["history-item",{active:e.id==t._id}]),onClick:e=>n("click",t._id)},[I("div",Ia,[I("img",{src:t.user.url,alt:"",srcset:""},null,8,Ca),I("div",null,L(t.user.name),1)]),I("div",Da,[I("div",Ta,L(t.slug),1),I("div",La,[z(L((a=t.createTime,new Date(a).toLocaleString()))+" ",1),t.isDeleted?(g(),C(r,{key:0,type:"danger"},{default:D((()=>[z("已删除")])),_:1})):N("",!0)])])],10,Sa)];var a})),_:2},1024)))),128))])),_:1})],2)),[[o,e.loading]])}}}),[["__scopeId","data-v-95f674a3"]]),Ra={class:"properties"},Ma={class:"properties-title"},Na={class:"item"},Pa={class:"item-label"},za={class:"item-value"},ja={class:"value-label"},Oa={class:"value-label"},$a={class:"item"},Aa={class:"item-label"},Fa={class:"item-value"},Ba={class:"value-label"},qa={class:"value-label"},Va={key:0,class:"item"},Ua={class:"item-label"},Wa={class:"item-value"},Ha={key:1,class:"item"},Ka={class:"item-label"},Xa={class:"item-value"},Ga={key:2,class:"item"},Ya={class:"item-label"},Qa={class:"item-value"},Ja=c(f({__name:"properties",props:{layerData:{}},emits:["copy"],setup(e,{emit:t}){const a=t;return(e,t)=>(g(),b("section",Ra,[I("div",Ma,L(w(Xe)("PROPERTIES")),1),I("div",Na,[I("div",Pa,L(w(Xe)("Position")+":"),1),I("ul",za,[I("li",null,[I("div",{class:"value-input",onClick:t[0]||(t[0]=t=>a("copy",w(Ze)(e.layerData.rect.x)))},L(w(Ze)(e.layerData.rect.x)),1),I("div",ja,L(w(Xe)("X")),1)]),I("li",null,[I("div",{class:"value-input",onClick:t[1]||(t[1]=t=>a("copy",w(Ze)(e.layerData.rect.y)))},L(w(Ze)(e.layerData.rect.y)),1),I("div",Oa,L(w(Xe)("Y")),1)])])]),I("div",$a,[I("div",Aa,L(w(Xe)("Size")+":"),1),I("ul",Fa,[I("li",null,[I("div",{class:"value-input",onClick:t[2]||(t[2]=t=>a("copy",w(Ze)(e.layerData.rect.width)))},L(w(Ze)(e.layerData.rect.width)),1),I("div",Ba,L(w(Xe)("Width")),1)]),I("li",null,[I("div",{class:"value-input",onClick:t[3]||(t[3]=t=>a("copy",w(Ze)(e.layerData.rect.height)))},L(w(Ze)(e.layerData.rect.height)),1),I("div",qa,L(w(Xe)("Height")),1)])])]),"number"==typeof e.layerData.opacity?(g(),b("div",Va,[I("div",Ua,L(w(Xe)("Opacity")+":"),1),I("ul",Wa,[I("li",null,[I("div",{class:"value-input",onClick:t[4]||(t[4]=t=>a("copy",Math.round(1e4*e.layerData.opacity)/100+"%"))},L(Math.round(1e4*e.layerData.opacity)/100+"%"),1)])])])):N("",!0),e.layerData.radius?(g(),b("div",Ha,[I("div",Ka,L(w(Xe)("Radius")+":"),1),I("ul",Xa,[I("li",null,[I("div",{class:"value-input",onClick:t[5]||(t[5]=t=>a("copy",w(Ze)(e.layerData.radius[0])))},L(w(Ze)(e.layerData.radius[0])),1)])])])):N("",!0),e.layerData.styleName?(g(),b("div",Ga,[I("div",Ya,L(w(Xe)("Style")+":"),1),I("ul",Qa,[I("li",null,[I("div",{class:"value-input",onClick:t[6]||(t[6]=t=>a("copy",e.layerData.styleName))},L(e.layerData.styleName),1)])])])):N("",!0)]))}}),[["__scopeId","data-v-c2af4a0a"]]),Za=["data-name"],en={class:"value-input"},tn=f({__name:"colorItem",props:{color:{}},setup(e){const t=$();return(e,a)=>(g(),b("div",{class:"color","data-name":w(t).project.colorNames?w(t).project.colorNames[e.color["argb-hex"]]:null},[I("label",null,[I("em",null,[I("i",{style:M(`background-color: ${e.color["css-rgba"]}`)},null,4)])]),I("span",en,L(e.color[w(t).state.colorFormat]),1)],8,Za))}}),an={class:"fills"},nn={class:"fills-title"},rn=["data-label"],ln={class:"item-label"},on={key:1,class:"gradient"},sn=f({__name:"fills",props:{layerData:{}},setup:e=>(e,t)=>(g(),b("section",an,[I("div",nn,L(w(Xe)("FILLS")),1),(g(!0),b(A,null,F(e.layerData.fills,((e,t)=>(g(),b("div",{key:t,class:"item items-group","data-label":w(Xe)(e.fillType)+":"},[I("div",ln,L(w(Xe)(e.fillType)+":"),1),"color"==e.fillType.toLowerCase()?(g(),C(tn,{key:0,color:e.color},null,8,["color"])):e.gradient&&e.gradient.colorStops?(g(),b("div",on,[(g(!0),b(A,null,F(e.gradient.colorStops,((e,t)=>(g(),C(tn,{key:t,color:e.color},null,8,["color"])))),128))])):N("",!0)],8,rn)))),128))]))}),cn={class:"font-div"},dn={class:"font-title"},un={class:"item"},pn={class:"item-label"},mn={class:"item-value"},fn={class:"value-input"},hn={class:"item"},yn={class:"item-label"},vn={key:0,class:"item"},gn={class:"item-label"},bn={class:"item-value"},xn={class:"value-input"},kn={class:"item"},wn={class:"item-label"},_n={class:"item-value"},Sn={class:"value-input"},In={class:"value-label"},Cn={class:"value-input"},Dn={class:"value-label"},Tn={class:"item"},Ln={class:"item-label"},En={class:"item-value"},Rn=c(f({__name:"font",props:{layerData:{}},setup:e=>(e,t)=>{const a=pe;return g(),b("section",cn,[I("div",dn,L(w(Xe)("TYPEFACE")),1),I("div",un,[I("div",pn,L(w(Xe)("Family")+":"),1),I("ul",mn,[I("li",null,[I("div",fn,L(e.layerData.fontFace),1)])])]),I("div",hn,[I("div",yn,L(w(Xe)("Color")+":"),1),P(tn,{color:e.layerData.color},null,8,["color"])]),e.layerData.fontSize?(g(),b("div",vn,[I("div",gn,L(w(Xe)("Size")+":"),1),I("ul",bn,[I("li",null,[I("div",xn,L(w(Ze)(e.layerData.fontSize,!0)),1)])])])):N("",!0),I("div",kn,[I("div",wn,L(w(Xe)("Spacing")+":"),1),I("ul",_n,[I("li",null,[I("div",Sn,L(w(Ze)(e.layerData.letterSpacing,!0)),1),I("div",In,L(w(Xe)("Character")),1)]),I("li",null,[I("div",Cn,L(e.layerData.lineHeight?w(Ze)(e.layerData.lineHeight,!0):"Auto"),1),I("div",Dn,L(w(Xe)("Line")),1)])])]),I("div",Tn,[I("div",Ln,L(w(Xe)("Content")+":"),1),I("div",En,[P(a,{type:"textarea",resize:"none",value:e.layerData.content,id:"content",rows:2,readonly:"",style:M(`font-family: ${e.layerData.fontFace},sans-serif`)},null,8,["value","style"])])])])}}),[["__scopeId","data-v-13491cad"]]),Mn={class:"borders"},Nn={class:"borders-title"},Pn={class:"items-group-title"},zn={class:"item"},jn={class:"item-label"},On={class:"item-value"},$n={class:"value-input"},An={class:"item"},Fn={class:"item-label"},Bn={key:1,class:"colors gradient"},qn=c(f({__name:"borders",props:{layerData:{}},setup:e=>(e,t)=>(g(),b("section",Mn,[I("div",Nn,L(w(Xe)("BORDERS")),1),(g(!0),b(A,null,F(e.layerData.borders,((e,t)=>(g(),b("div",{key:t,class:"items-group"},[I("div",Pn,L(w(Xe)(e.position+" Border")),1),I("div",zn,[I("div",jn,L(w(Xe)("Weight")+":"),1),I("ul",On,[I("li",null,[I("div",$n,L(w(Ze)(e.thickness)),1)])])]),I("div",An,[I("div",Fn,L(w(Xe)(e.fillType)+":"),1),"color"==e.fillType.toLowerCase()?(g(),C(tn,{key:0,color:e.color},null,8,["color"])):e.gradient&&e.gradient.colorStops?(g(),b("div",Bn,[(g(!0),b(A,null,F(e.gradient.colorStops,((e,t)=>(g(),C(tn,{key:t,color:e.color},null,8,["color"])))),128))])):N("",!0)])])))),128))]))}),[["__scopeId","data-v-e97817d7"]]),Vn={class:"shadows-div"},Un={class:"shadows-title"},Wn={class:"items-group-title"},Hn={class:"item"},Kn={class:"item-label"},Xn={class:"item-value"},Gn={class:"value-input"},Yn={class:"value-label"},Qn={class:"value-input"},Jn={class:"value-label"},Zn={class:"item"},er={class:"item-label"},tr={class:"item-value"},ar={class:"value-input"},nr={class:"value-label"},rr={class:"value-input"},lr={class:"value-label"},ir={class:"item"},or={class:"item-label"},sr=c(f({__name:"shadows",props:{layerData:{}},setup:e=>(e,t)=>(g(),b("section",Vn,[I("div",Un,L(w(Xe)("SHADOWS")),1),(g(!0),b(A,null,F(e.layerData.shadows,((e,t)=>(g(),b("div",{key:t,class:"items-group"},[I("div",Wn,L(w(Xe)(e.type+" Shadow")),1),I("div",Hn,[I("div",Kn,L(w(Xe)("Offset")+":"),1),I("ul",Xn,[I("li",null,[I("div",Gn,L(w(Ze)(e.offsetX)),1),I("div",Yn,L(w(Xe)("X")),1)]),I("li",null,[I("div",Qn,L(w(Ze)(e.offsetY)),1),I("div",Jn,L(w(Xe)("Y")),1)])])]),I("div",Zn,[I("div",er,L(w(Xe)("Effect")+":"),1),I("ul",tr,[I("li",null,[I("div",ar,L(w(Ze)(e.blurRadius)),1),I("div",nr,L(w(Xe)("Blur")),1)]),I("li",null,[I("div",rr,L(w(Ze)(e.spread)),1),I("div",lr,L(w(Xe)("Spread")),1)])])]),I("div",ir,[I("div",or,L(w(Xe)("Color")+":"),1),P(tn,{color:e.color},null,8,["color"])])])))),128))]))}),[["__scopeId","data-v-67b4a424"]]);ye(),function(e){function t(e,t,a,n,r,l){this.indented=e,this.column=t,this.type=a,this.info=n,this.align=r,this.prev=l}function a(e,a,n,r){var l=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=n&&(l=e.context.indented),e.context=new t(l,a,n,r,null,e.context)}function n(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function r(e,t,a){return"variable"==t.prevToken||"type"==t.prevToken||!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,a))||!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0}function l(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function i(e){for(var t={},a=e.split(" "),n=0;n<a.length;++n)t[a[n]]=!0;return t}function o(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}e.defineMode("clike",(function(i,s){var c,d,u=i.indentUnit,p=s.statementIndentUnit||u,m=s.dontAlignCalls,f=s.keywords||{},h=s.types||{},y=s.builtin||{},v=s.blockKeywords||{},g=s.defKeywords||{},b=s.atoms||{},x=s.hooks||{},k=s.multiLineStrings,w=!1!==s.indentStatements,_=!1!==s.indentSwitch,S=s.namespaceSeparator,I=s.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,C=s.numberStart||/[\d\.]/,D=s.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,T=s.isOperatorChar||/[+\-*&%=<>!?|\/]/,L=s.isIdentifierChar||/[\w\$_\xa1-\uffff]/,E=s.isReservedIdentifier||!1;function R(e,t){var a=e.next();if(x[a]){var n=x[a](e,t);if(!1!==n)return n}if('"'==a||"'"==a)return t.tokenize=M(a),t.tokenize(e,t);if(C.test(a)){if(e.backUp(1),e.match(D))return"number";e.next()}if(I.test(a))return c=a,null;if("/"==a){if(e.eat("*"))return t.tokenize=N,N(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(T.test(a)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(T););return"operator"}if(e.eatWhile(L),S)for(;e.match(S);)e.eatWhile(L);var r=e.current();return o(f,r)?(o(v,r)&&(c="newstatement"),o(g,r)&&(d=!0),"keyword"):o(h,r)?"type":o(y,r)||E&&E(r)?(o(v,r)&&(c="newstatement"),"builtin"):o(b,r)?"atom":"variable"}function M(e){return function(t,a){for(var n,r=!1,l=!1;null!=(n=t.next());){if(n==e&&!r){l=!0;break}r=!r&&"\\"==n}return(l||!r&&!k)&&(a.tokenize=null),"string"}}function N(e,t){for(var a,n=!1;a=e.next();){if("/"==a&&n){t.tokenize=null;break}n="*"==a}return"comment"}function P(e,t){s.typeFirstDefinitions&&e.eol()&&l(t.context)&&(t.typeAtEndOfLine=r(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new t((e||0)-u,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var i=t.context;if(e.sol()&&(null==i.align&&(i.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return P(e,t),null;c=d=null;var o=(t.tokenize||R)(e,t);if("comment"==o||"meta"==o)return o;if(null==i.align&&(i.align=!0),";"==c||":"==c||","==c&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)n(t);else if("{"==c)a(t,e.column(),"}");else if("["==c)a(t,e.column(),"]");else if("("==c)a(t,e.column(),")");else if("}"==c){for(;"statement"==i.type;)i=n(t);for("}"==i.type&&(i=n(t));"statement"==i.type;)i=n(t)}else c==i.type?n(t):w&&(("}"==i.type||"top"==i.type)&&";"!=c||"statement"==i.type&&"newstatement"==c)&&a(t,e.column(),"statement",e.current());if("variable"==o&&("def"==t.prevToken||s.typeFirstDefinitions&&r(e,t,e.start)&&l(t.context)&&e.match(/^\s*\(/,!1))&&(o="def"),x.token){var u=x.token(e,t,o);void 0!==u&&(o=u)}return"def"==o&&!1===s.styleDefs&&(o="variable"),t.startOfLine=!1,t.prevToken=d?"def":o||c,P(e,t),o},indent:function(t,a){if(t.tokenize!=R&&null!=t.tokenize||t.typeAtEndOfLine&&l(t.context))return e.Pass;var n=t.context,r=a&&a.charAt(0),i=r==n.type;if("statement"==n.type&&"}"==r&&(n=n.prev),s.dontIndentStatements)for(;"statement"==n.type&&s.dontIndentStatements.test(n.info);)n=n.prev;if(x.indent){var o=x.indent(t,n,a,u);if("number"==typeof o)return o}var c=n.prev&&"switch"==n.prev.info;if(s.allmanIndentation&&/[{(]/.test(r)){for(;"top"!=n.type&&"}"!=n.type;)n=n.prev;return n.indented}return"statement"==n.type?n.indented+("{"==r?0:p):!n.align||m&&")"==n.type?")"!=n.type||i?n.indented+(i?0:u)+(i||!c||/^(?:case|default)\b/.test(a)?0:u):n.indented+p:n.column+(i?0:1)},electricInput:_?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}}));var s="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",c="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",d="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",u="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",p=i("int long char short double float unsigned signed void bool"),m=i("SEL instancetype id Class Protocol BOOL");function f(e){return o(p,e)||/.+_t$/.test(e)}function h(e){return f(e)||o(m,e)}var y="case do else for if switch while struct enum union",v="struct enum union";function g(e,t){if(!t.startOfLine)return!1;for(var a,n=null;a=e.peek();){if("\\"==a&&e.match(/^.$/)){n=g;break}if("/"==a&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function b(e,t){return"type"==t.prevToken&&"type"}function x(e){return!(!e||e.length<2||"_"!=e[0]||"_"!=e[1]&&e[1]===e[1].toLowerCase())}function k(e){return e.eatWhile(/[\w\.']/),"number"}function w(e,t){if(e.backUp(1),e.match(/^(?:R|u8R|uR|UR|LR)/)){var a=e.match(/^"([^\s\\()]{0,16})\(/);return!!a&&(t.cpp11RawStringDelim=a[1],t.tokenize=I,I(e,t))}return e.match(/^(?:u8|u|U|L)/)?!!e.match(/^["']/,!1)&&"string":(e.next(),!1)}function _(e){var t=/(\w+)::~?(\w+)$/.exec(e);return t&&t[1]==t[2]}function S(e,t){for(var a;null!=(a=e.next());)if('"'==a&&!e.eat('"')){t.tokenize=null;break}return"string"}function I(e,t){var a=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+a+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function C(t,a){"string"==typeof t&&(t=[t]);var n=[];function r(e){if(e)for(var t in e)e.hasOwnProperty(t)&&n.push(t)}r(a.keywords),r(a.types),r(a.builtin),r(a.atoms),n.length&&(a.helperType=t[0],e.registerHelper("hintWords",t[0],n));for(var l=0;l<t.length;++l)e.defineMIME(t[l],a)}function D(e,t){for(var a=!1;!e.eol();){if(!a&&e.match('"""')){t.tokenize=null;break}a="\\"==e.next()&&!a}return"string"}function T(e){return function(t,a){for(var n;n=t.next();){if("*"==n&&t.eat("/")){if(1==e){a.tokenize=null;break}return a.tokenize=T(e-1),a.tokenize(t,a)}if("/"==n&&t.eat("*"))return a.tokenize=T(e+1),a.tokenize(t,a)}return"comment"}}function L(e){return function(t,a){for(var n,r=!1,l=!1;!t.eol();){if(!e&&!r&&t.match('"')){l=!0;break}if(e&&t.match('"""')){l=!0;break}n=t.next(),!r&&"$"==n&&t.match("{")&&t.skipTo("}"),r=!r&&"\\"==n&&!e}return!l&&e||(a.tokenize=null),"string"}}C(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:i(s),types:f,blockKeywords:i(y),defKeywords:i(v),typeFirstDefinitions:!0,atoms:i("NULL true false"),isReservedIdentifier:x,hooks:{"#":g,"*":b},modeProps:{fold:["brace","include"]}}),C(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:i(s+" "+c),types:f,blockKeywords:i(y+" class try catch"),defKeywords:i(v+" class namespace"),typeFirstDefinitions:!0,atoms:i("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:x,hooks:{"#":g,"*":b,u:w,U:w,L:w,R:w,0:k,1:k,2:k,3:k,4:k,5:k,6:k,7:k,8:k,9:k,token:function(e,t,a){if("variable"==a&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&_(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),C("text/x-java",{name:"clike",keywords:i("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:i("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:i("catch class do else finally for if switch try while"),defKeywords:i("class interface enum @interface"),typeFirstDefinitions:!0,atoms:i("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")},'"':function(e,t){return!!e.match(/""$/)&&(t.tokenize=D,t.tokenize(e,t))}},modeProps:{fold:["brace","import"]}}),C("text/x-csharp",{name:"clike",keywords:i("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in init interface internal is lock namespace new operator out override params private protected public readonly record ref required return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:i("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:i("catch class do else finally for foreach if struct switch try while"),defKeywords:i("class interface namespace record struct var"),typeFirstDefinitions:!0,atoms:i("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=S,S(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),C("text/x-scala",{name:"clike",keywords:i("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:i("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:i("catch class enum do else finally for forSome if match switch try while"),defKeywords:i("class enum def object package trait type val var"),atoms:i("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=D,t.tokenize(e,t))},"'":function(e){return e.match(/^(\\[^'\s]+|[^\\'])'/)?"string-2":(e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom")},"=":function(e,a){var n=a.context;return!("}"!=n.type||!n.align||!e.eat(">"))&&(a.context=new t(n.indented,n.column,n.type,n.info,null,n.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=T(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),C("text/x-kotlin",{name:"clike",keywords:i("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:i("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:i("catch class do else finally for if where try while enum"),defKeywords:i("class val var object interface fun"),atoms:i("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){return t.tokenize=L(e.match('""')),t.tokenize(e,t)},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=T(1),t.tokenize(e,t))},indent:function(e,t,a,n){var r=a&&a.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=a?"operator"==e.prevToken&&"}"!=a&&"}"!=e.context.type||"variable"==e.prevToken&&"."==r||("}"==e.prevToken||")"==e.prevToken)&&"."==r?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(a||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),C(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:i("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:i("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:i("for while do if else struct"),builtin:i("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:i("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":g},modeProps:{fold:["brace","include"]}}),C("text/x-nesc",{name:"clike",keywords:i(s+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:f,blockKeywords:i(y),atoms:i("null true false"),hooks:{"#":g},modeProps:{fold:["brace","include"]}}),C("text/x-objectivec",{name:"clike",keywords:i(s+" "+d),types:h,builtin:i(u),blockKeywords:i(y+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:i(v+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:i("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:x,hooks:{"#":g,"*":b},modeProps:{fold:["brace","include"]}}),C("text/x-objectivec++",{name:"clike",keywords:i(s+" "+d+" "+c),types:h,builtin:i(u),blockKeywords:i(y+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:i(v+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:i("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:x,hooks:{"#":g,"*":b,u:w,U:w,L:w,R:w,0:k,1:k,2:k,3:k,4:k,5:k,6:k,7:k,8:k,9:k,token:function(e,t,a){if("variable"==a&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&_(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),C("text/x-squirrel",{name:"clike",keywords:i("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:f,blockKeywords:i("case catch class else for foreach if switch try while"),defKeywords:i("function local class"),typeFirstDefinitions:!0,atoms:i("true false null"),hooks:{"#":g},modeProps:{fold:["brace","include"]}});var E=null;function R(e){return function(t,a){for(var n,r=!1,l=!1;!t.eol();){if(!r&&t.match('"')&&("single"==e||t.match('""'))){l=!0;break}if(!r&&t.match("``")){E=R(e),l=!0;break}n=t.next(),r="single"==e&&!r&&"\\"==n}return l&&(a.tokenize=null),"string"}}C("text/x-ceylon",{name:"clike",keywords:i("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:i("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:i("class dynamic function interface module object package value"),builtin:i("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:i("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=R(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!E||!e.match("`"))&&(t.tokenize=E,E=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,a){if(("variable"==a||"type"==a)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}(ve()),function(e){function t(t,n){function r(){t.display.wrapper.offsetHeight?(a(t,n),t.display.lastWrapHeight!=t.display.wrapper.clientHeight&&t.refresh()):n.timeout=setTimeout(r,n.delay)}n.timeout=setTimeout(r,n.delay),n.hurry=function(){clearTimeout(n.timeout),n.timeout=setTimeout(r,50)},e.on(window,"mouseup",n.hurry),e.on(window,"keyup",n.hurry)}function a(t,a){clearTimeout(a.timeout),e.off(window,"mouseup",a.hurry),e.off(window,"keyup",a.hurry)}e.defineOption("autoRefresh",!1,(function(e,n){e.state.autoRefresh&&(a(e,e.state.autoRefresh),e.state.autoRefresh=null),n&&0==e.display.wrapper.offsetHeight&&t(e,e.state.autoRefresh={delay:n.delay||250})}))}(ve());const cr=f({__name:"codemirror",props:{code:{},mode:{}},setup(e){const t=G({smartIndent:!0,tabSize:4,mode:e.mode,lineNumbers:!1,autoRefresh:!0,lineWrapping:!0});return(e,a)=>(g(),C(w(ge),{value:e.code,options:t},null,8,["value","options"]))}}),dr=f({__name:"codeTemplate",props:{layerData:{}},emits:["copy"],setup(e,{emit:t}){const a=e,n=t,r=h("css"),l=R((()=>{const e={},t=a.layerData;switch(t.type){case"text":e.android=`<TextView\r\n${i(t)}android:text="${t.content}"\r\nandroid:textColor="${t.color["argb-hex"]}"\r\nandroid:textSize="${Ze(t.fontSize,!0)}"\r\n/>`,e.ios=`UILabel *label = [[UILabel alloc] init];\r\nlabel.frame = CGRectMake(${et(t.rect.x)},${et(t.rect.y)},${et(t.rect.width)},${et(t.rect.height)});\r\nlabel.text = @"${t.content}";\r\nlabel.font = [UIFont fontWithName:@"${t.fontFace}" size:${Ze(t.fontSize)}];\r\nlabel.textColor = [UIColor colorWithRed:${t.color.rgb.r}/255.0 green:${t.color.rgb.g}/255.0 blue:${t.color.rgb.b}/255.0 alpha:${t.color.alpha}/255.0];\r\n`;break;case"shape":e.android=`<View\r\n${i(t)}${function(e){let t,a="";if("shape"!=e.type||void 0===e.fills||0==e.fills.length)return a;for(t in e.fills)if("color"==e.fills[t].fillType.toLowerCase())return'android:background="'+e.fills[t].color["argb-hex"]+'"\r\n';return a}(t)}/>`,e.ios=`UIView *view = [[UIView alloc] init];\r\nview.frame = CGRectMake(${et(t.rect.x)},${et(t.rect.y)},${et(t.rect.width)},${et(t.rect.height)});\r\n${function(e){let t,a="";if("shape"!=e.type||void 0===e.fills||0==e.fills.length)return a;for(t in e.fills)if("color"==e.fills[t].fillType.toLowerCase())return"view.backgroundColor = [UIColor colorWithRed:"+e.fills[t].color.rgb.r+"/255.0 green:"+e.fills[t].color.rgb.g+"/255.0 blue:"+e.fills[t].color.rgb.b+"/255.0 alpha:"+e.fills[t].color.alpha+"/255.0];\r\n";return a}(t)}`;break;case"slice":e.android=`<ImageView\r\n${i(t)}${function(e){return"slice"!=e.type||void 0===e.exportable?"":'android:src="@mipmap/'+e.exportable[0].name+"."+e.exportable[0].format+'"\r\n'}(t)}/>`,e.ios=`UIImageView *imageView = [[UIImageView alloc] init];\r\nimageView.frame = CGRectMake(${et(t.rect.x)},${et(t.rect.y)},${et(t.rect.width)},${et(t.rect.height)});\r\n${function(e){return"slice"!=e.type||void 0===e.exportable?"":'imageView.image = [UIImage imageNamed:@"'+e.exportable[0].name+"."+e.exportable[0].format+'"];\r\n'}(t)}`}return e}));function i(e){return'android:layout_width="'+Ze(e.rect.width,!1)+'"\r\nandroid:layout_height="'+Ze(e.rect.height,!1)+'"\r\n'}return(e,t)=>{const a=de,i=ue;return g(),C(i,{modelValue:r.value,"onUpdate:modelValue":t[3]||(t[3]=e=>r.value=e),class:"demo-tabs"},{default:D((()=>[P(a,{label:"css",name:"css"},{default:D((()=>[I("div",{class:"css-panel code-item",onClick:t[0]||(t[0]=t=>n("copy",e.layerData.css.join("\r\n")))},[P(cr,{mode:"css",code:e.layerData.css.join("\r\n")},null,8,["code"])])])),_:1}),P(a,{label:"android",name:"android"},{default:D((()=>[P(cr,{mode:"css",code:l.value.android,onClick:t[1]||(t[1]=e=>n("copy",l.value.android))},null,8,["code"])])),_:1}),P(a,{label:"ios",name:"ios"},{default:D((()=>[P(cr,{mode:"clike",code:l.value.ios,onClick:t[2]||(t[2]=e=>n("copy",l.value.ios))},null,8,["code"])])),_:1})])),_:1},8,["modelValue"])}}}),ur={class:"exportable"},pr={class:"exportable-title"},mr={class:"item-image"},fr=["src"],hr={class:"item"},yr={class:"value-input"},vr=f({__name:"exportable",props:{layerData:{}},emits:["copy"],setup(e,{emit:t}){const a=t;return(e,t)=>{const n=s;return g(),b("section",ur,[I("div",pr,L(w(Xe)("EXPORTABLE")),1),I("div",mr,[I("img",{src:e.layerData.exportable[0].path,alt:""},null,8,fr)]),(g(!0),b(A,null,F(e.layerData.exportable,((e,r)=>(g(),b("div",{key:r,class:"item items-group"},[I("div",hr,[I("div",yr,L(e.name),1),P(n,{type:"primary",size:"small",onClick:[t[0]||(t[0]=q((()=>{}),["stop"])),t=>a("copy",e.path)],round:""},{default:D((()=>[z("复制链接")])),_:2},1032,["onClick"])])])))),128))])}}}),gr=c(f({__name:"inspector",emits:["copy"],setup(e,{emit:t}){const a=$(),n=R((()=>{const e=a.state,t=e.current;return void 0!==e.selectedIndex&&t&&t.layers&&e.current.layers[e.selectedIndex]?e.current.layers[e.selectedIndex]:null})),r=t,l=e=>{r("copy",e)};return(e,t)=>{const a=ce;return g(),b("div",{onClick:t[1]||(t[1]=q((()=>{}),["stop"])),class:k(["inspector",{show:!!n.value}]),id:"inspector"},[n.value?(g(),b(A,{key:0},[I("div",{class:"inspector-title",onClick:t[0]||(t[0]=q((e=>l(n.value.name)),["stop"]))},L(n.value.name),1),P(Ja,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a),n.value.fills&&n.value.fills.length?(g(),b(A,{key:0},[P(sn,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a)],64)):N("",!0),"text"==n.value.type?(g(),b(A,{key:1},[P(Rn,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a)],64)):N("",!0),n.value.borders&&n.value.borders.length?(g(),b(A,{key:2},[P(qn,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a)],64)):N("",!0),n.value.shadows&&n.value.shadows.length?(g(),b(A,{key:3},[P(sr,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a)],64)):N("",!0),n.value.css&&n.value.css.length?(g(),b(A,{key:4},[P(dr,{"layer-data":n.value,onCopy:l},null,8,["layer-data"]),P(a)],64)):N("",!0),n.value.exportable&&n.value.exportable.length?(g(),C(vr,{key:5,"layer-data":n.value,onCopy:l},null,8,["layer-data"])):N("",!0)],64)):N("",!0)],2)}}}),[["__scopeId","data-v-9cd58431"]]),br=e=>(j("data-v-f9ea3a5d"),e=e(),O(),e),xr={class:"sketch-detail"},kr={class:"sketch-nav"},wr={class:"nav-left"},_r={class:"sketch-nav-menu"},Sr=br((()=>I("img",{src:"https://static.soyoung.com/sy-design/7x8p5l27n6np1706495684052.png",alt:""},null,-1))),Ir=br((()=>I("img",{src:"https://static.soyoung.com/sy-pre/2own6t3xax3eb-1717146600634.png",alt:""},null,-1))),Cr=br((()=>I("img",{src:"https://static.soyoung.com/sy-design/9j6t50seztbc1706495684053.png",alt:""},null,-1))),Dr={class:"nav-right"},Tr={key:0},Lr=br((()=>I("div",{id:"cursor",class:"cursor",style:{display:"none"}},null,-1))),Er={key:1,class:"board-placeholder"},Rr=c(f({__name:"detail",setup(e){const{toClipboard:t}=be({appendToBody:!1}),a=Y();let n=null;const r=async e=>{n&&n.close(),await t(e),n=_e({title:"复制成功",type:"success"})},l=$(),i=Q(),o=J(),c=h([]),d=h([]),f=h(!1),y=h(null),x=h(""),k=h(""),_=R((()=>i.teamList.find((e=>e._id==k.value)))),S=R((()=>({path:"/item/project/index",query:{teamId:k.value}}))),T=R((()=>({path:"/item/project/stage",query:{teamId:k.value,projectId:y.value?._id}}))),E=R((()=>c.value.find((e=>e._id===x.value)))),M=R((()=>_.value?_.value.permission:null)),j=()=>{const e=[_.value.name];y.value&&e.push(y.value.name),E.value&&(e.push(E.value.pageName),e.push(E.value.name)),ke(_.value,we.PREVIEW,`/#/item/project/detail?id=${o.query.id}&teamId=${k.value}&`,e)},O=async(e,t=!1)=>{const a={id:e},n=await Z(a);if(0==n.code){const e=n.data;e.artboards=[JSON.parse(e.artboard)],e.slices=JSON.parse(e.slices),e.colors=JSON.parse(e.colors),l.initState(e),t&&(B(e.groupId),A(e.projectId))}},A=async e=>{const t=await ee({id:e});0==t.code&&(y.value=t.data)},F=async()=>{l.historyVisible=!0,f.value=!0;try{const e={id:E.value.artId,groupId:o.query.groupId},t=await te(e);if(0==t.code){const e=t.data;d.value=e}}catch(e){p.error(e.message)}finally{f.value=!1}},B=async e=>{const t={groupId:e},a=await ae(t);0==a.code&&(c.value=a.data)},V=e=>{e!=x.value&&(x.value=e,O(e))},U=e=>{e<.25||e>4||(l.state.zoom=e)};v((()=>{W(),document.body.addEventListener("click",yt),document.body.addEventListener("mousemove",vt),window.addEventListener("keydown",st),window.addEventListener("keyup",ct),window.addEventListener("mousemove",dt),window.addEventListener("mousedown",ut),window.addEventListener("mouseup",pt),ie("option+s",(e=>{e.preventDefault(),l.slicesVisible=!l.slicesVisible})),ie("option+c",(e=>{e.preventDefault(),l.colorsVisible=!l.colorsVisible})),ie("option+h",(e=>{e.preventDefault(),l.historyVisible?l.historyVisible=!1:F()}))}));const W=async()=>{const e=o.query;if(e.id){if(e.iv_id){const t=await xe(e.iv_id);if(!t)return;k.value=t,a.replace({path:o.path,query:{id:e.id,teamId:t}})}else k.value=e.teamId;return await i.init(),x.value=e.id,O(e.id,!0)}};return(e,t)=>{const a=Me,n=Re,i=s,o=m;return g(),b("div",xr,[I("header",kr,[I("div",wr,[P(n,{"separator-icon":w(u)},{default:D((()=>[_.value?(g(),C(a,{key:0,replace:"",to:S.value},{default:D((()=>[z(L(_.value?.name),1)])),_:1},8,["to"])):N("",!0),y.value?(g(),C(a,{key:1,replace:"",to:T.value},{default:D((()=>[z(L(y.value?.name),1)])),_:1},8,["to"])):N("",!0),P(a,null,{default:D((()=>[z(L(E.value?E.value.name:""),1)])),_:1})])),_:1},8,["separator-icon"])]),I("ul",_r,[I("li",null,[P(i,{onClick:F,class:"nav-button",type:"info",text:""},{default:D((()=>[Sr])),_:1})]),I("li",null,[P(i,{onClick:t[0]||(t[0]=q((e=>w(l).colorsVisible=!w(l).colorsVisible),["stop"])),class:"nav-button",type:"info",text:""},{default:D((()=>[Ir])),_:1})]),I("li",null,[P(i,{onClick:t[1]||(t[1]=q((e=>w(l).slicesVisible=!w(l).slicesVisible),["stop"])),class:"nav-button",type:"info",text:""},{default:D((()=>[Cr])),_:1})]),I("li",null,[P(He,{onChange:U,num:w(l).state.zoom},null,8,["num"])])]),I("div",Dr,[E.value?(g(),C(i,{key:0,style:{"margin-right":"25px",width:"60px"},type:"primary",onClick:j},{default:D((()=>[z("分享")])),_:1})):N("",!0),P(oe,{type:"detail"}),P(Je)])]),M.value?(g(),b("main",Tr,[P(Yt,{"active-sketch-id":x.value,group:w(l).group,"group-data-list":c.value,onChange:V},null,8,["active-sketch-id","group","group-data-list"]),P(Dt,{onCopy:r,slices:w(l).project.slices,onClose:t[2]||(t[2]=e=>w(l).slicesVisible=!1)},null,8,["slices"]),P(Rt,{"color-format":w(l).state.colorFormat,colors:w(l).project.colors,onClose:t[3]||(t[3]=e=>w(l).colorsVisible=!1)},null,8,["color-format","colors"]),P(_a),P(Ea,{id:x.value,loading:f.value,history:d.value,onClick:V},null,8,["id","loading","history"]),P(gr,{onCopy:r}),Lr])):(g(),b("div",Er,[P(o,{description:"暂无该项目权限，请联系项目管理员添加"})]))])}}}),[["__scopeId","data-v-f9ea3a5d"]]);export{Rr as default};
//# sourceMappingURL=chunk.123416f7.js.map
