/**
* @vue/shared v3.4.15
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
function t(t,e){const n=new Set(t.split(","));return e?t=>n.has(t.toLowerCase()):t=>n.has(t)}const e={},n=[],r=()=>{},i=()=>!1,o=t=>111===t.charCodeAt(0)&&110===t.charCodeAt(1)&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),a=t=>t.startsWith("onUpdate:"),s=Object.assign,l=(t,e)=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)},c=Object.prototype.hasOwnProperty,h=(t,e)=>c.call(t,e),u=Array.isArray,d=t=>"[object Map]"===w(t),f=t=>"[object Set]"===w(t),p=t=>"[object Date]"===w(t),g=t=>"function"==typeof t,m=t=>"string"==typeof t,y=t=>"symbol"==typeof t,v=t=>null!==t&&"object"==typeof t,b=t=>(v(t)||g(t))&&g(t.then)&&g(t.catch),_=Object.prototype.toString,w=t=>_.call(t),S=t=>w(t).slice(8,-1),x=t=>"[object Object]"===w(t),C=t=>m(t)&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,A=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=t=>{const e=Object.create(null);return n=>e[n]||(e[n]=t(n))},P=/-(\w)/g,k=E((t=>t.replace(P,((t,e)=>e?e.toUpperCase():"")))),O=/\B([A-Z])/g,T=E((t=>t.replace(O,"-$1").toLowerCase())),R=E((t=>t.charAt(0).toUpperCase()+t.slice(1))),F=E((t=>t?`on${R(t)}`:"")),M=(t,e)=>!Object.is(t,e),N=(t,e)=>{for(let n=0;n<t.length;n++)t[n](e)},L=(t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})},D=t=>{const e=parseFloat(t);return isNaN(e)?t:e},I=t=>{const e=m(t)?Number(t):NaN;return isNaN(e)?t:e};let j;const U=()=>j||(j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),G=t("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error");function B(t){if(u(t)){const e={};for(let n=0;n<t.length;n++){const r=t[n],i=m(r)?W(r):B(r);if(i)for(const t in i)e[t]=i[t]}return e}if(m(t)||v(t))return t}const V=/;(?![^(]*\))/g,H=/:([^]+)/,z=/\/\*[^]*?\*\//g;function W(t){const e={};return t.replace(z,"").split(V).forEach((t=>{if(t){const n=t.split(H);n.length>1&&(e[n[0].trim()]=n[1].trim())}})),e}function $(t){let e="";if(m(t))e=t;else if(u(t))for(let n=0;n<t.length;n++){const r=$(t[n]);r&&(e+=r+" ")}else if(v(t))for(const n in t)t[n]&&(e+=n+" ");return e.trim()}function K(t){if(!t)return null;let{class:e,style:n}=t;return e&&!m(e)&&(t.class=$(e)),n&&(t.style=B(n)),t}const q=t("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Y(t){return!!t||""===t}function X(t,e){if(t===e)return!0;let n=p(t),r=p(e);if(n||r)return!(!n||!r)&&t.getTime()===e.getTime();if(n=y(t),r=y(e),n||r)return t===e;if(n=u(t),r=u(e),n||r)return!(!n||!r)&&function(t,e){if(t.length!==e.length)return!1;let n=!0;for(let r=0;n&&r<t.length;r++)n=X(t[r],e[r]);return n}(t,e);if(n=v(t),r=v(e),n||r){if(!n||!r)return!1;if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t){const r=t.hasOwnProperty(n),i=e.hasOwnProperty(n);if(r&&!i||!r&&i||!X(t[n],e[n]))return!1}}return String(t)===String(e)}function Q(t,e){return t.findIndex((t=>X(t,e)))}const J=t=>m(t)?t:null==t?"":u(t)||v(t)&&(t.toString===_||!g(t.toString))?JSON.stringify(t,Z,2):String(t),Z=(t,e)=>e&&e.__v_isRef?Z(t,e.value):d(e)?{[`Map(${e.size})`]:[...e.entries()].reduce(((t,[e,n],r)=>(t[tt(e,r)+" =>"]=n,t)),{})}:f(e)?{[`Set(${e.size})`]:[...e.values()].map((t=>tt(t)))}:y(e)?tt(e):!v(e)||u(e)||x(e)?e:String(e),tt=(t,e="")=>{var n;return y(t)?`Symbol(${null!=(n=t.description)?n:e})`:t};
/**
* @vue/reactivity v3.4.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let et,nt;class rt{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=et,!t&&et&&(this.index=(et.scopes||(et.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const e=et;try{return et=this,t()}finally{et=e}}}on(){et=this}off(){et=this.parent}stop(t){if(this._active){let e,n;for(e=0,n=this.effects.length;e<n;e++)this.effects[e].stop();for(e=0,n=this.cleanups.length;e<n;e++)this.cleanups[e]();if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.parent=void 0,this._active=!1}}}function it(t){return new rt(t)}function ot(t,e=et){e&&e.active&&e.effects.push(t)}function at(){return et}function st(t){et&&et.cleanups.push(t)}class lt{constructor(t,e,n,r){this.fn=t,this.trigger=e,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=2,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,ot(this,r)}get dirty(){if(1===this._dirtyLevel){gt();for(let t=0;t<this._depsLength;t++){const e=this.deps[t];if(e.computed&&(e.computed.value,this._dirtyLevel>=2))break}this._dirtyLevel<2&&(this._dirtyLevel=0),mt()}return this._dirtyLevel>=2}set dirty(t){this._dirtyLevel=t?2:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=dt,e=nt;try{return dt=!0,nt=this,this._runnings++,ct(this),this.fn()}finally{ht(this),this._runnings--,nt=e,dt=t}}stop(){var t;this.active&&(ct(this),ht(this),null==(t=this.onStop)||t.call(this),this.active=!1)}}function ct(t){t._trackId++,t._depsLength=0}function ht(t){if(t.deps&&t.deps.length>t._depsLength){for(let e=t._depsLength;e<t.deps.length;e++)ut(t.deps[e],t);t.deps.length=t._depsLength}}function ut(t,e){const n=t.get(e);void 0!==n&&e._trackId!==n&&(t.delete(e),0===t.size&&t.cleanup())}let dt=!0,ft=0;const pt=[];function gt(){pt.push(dt),dt=!1}function mt(){const t=pt.pop();dt=void 0===t||t}function yt(){ft++}function vt(){for(ft--;!ft&&_t.length;)_t.shift()()}function bt(t,e,n){if(e.get(t)!==t._trackId){e.set(t,t._trackId);const n=t.deps[t._depsLength];n!==e?(n&&ut(n,t),t.deps[t._depsLength++]=e):t._depsLength++}}const _t=[];function wt(t,e,n){yt();for(const r of t.keys())if(r._dirtyLevel<e&&t.get(r)===r._trackId){const t=r._dirtyLevel;r._dirtyLevel=e,0===t&&(r._shouldSchedule=!0,r.trigger())}St(t),vt()}function St(t){for(const e of t.keys())e.scheduler&&e._shouldSchedule&&(!e._runnings||e.allowRecurse)&&t.get(e)===e._trackId&&(e._shouldSchedule=!1,_t.push(e.scheduler))}const xt=(t,e)=>{const n=new Map;return n.cleanup=t,n.computed=e,n},Ct=new WeakMap,At=Symbol(""),Et=Symbol("");function Pt(t,e,n){if(dt&&nt){let e=Ct.get(t);e||Ct.set(t,e=new Map);let r=e.get(n);r||e.set(n,r=xt((()=>e.delete(n)))),bt(nt,r)}}function kt(t,e,n,r,i,o){const a=Ct.get(t);if(!a)return;let s=[];if("clear"===e)s=[...a.values()];else if("length"===n&&u(t)){const t=Number(r);a.forEach(((e,n)=>{("length"===n||!y(n)&&n>=t)&&s.push(e)}))}else switch(void 0!==n&&s.push(a.get(n)),e){case"add":u(t)?C(n)&&s.push(a.get("length")):(s.push(a.get(At)),d(t)&&s.push(a.get(Et)));break;case"delete":u(t)||(s.push(a.get(At)),d(t)&&s.push(a.get(Et)));break;case"set":d(t)&&s.push(a.get(At))}yt();for(const l of s)l&&wt(l,2);vt()}const Ot=t("__proto__,__v_isRef,__isVue"),Tt=new Set(Object.getOwnPropertyNames(Symbol).filter((t=>"arguments"!==t&&"caller"!==t)).map((t=>Symbol[t])).filter(y)),Rt=Ft();function Ft(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=we(this);for(let e=0,i=this.length;e<i;e++)Pt(n,0,e+"");const r=n[e](...t);return-1===r||!1===r?n[e](...t.map(we)):r}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){gt(),yt();const n=we(this)[e].apply(this,t);return vt(),mt(),n}})),t}function Mt(t){const e=we(this);return Pt(e,0,t),e.hasOwnProperty(t)}class Nt{constructor(t=!1,e=!1){this._isReadonly=t,this._shallow=e}get(t,e,n){const r=this._isReadonly,i=this._shallow;if("__v_isReactive"===e)return!r;if("__v_isReadonly"===e)return r;if("__v_isShallow"===e)return i;if("__v_raw"===e)return n===(r?i?de:ue:i?he:ce).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const o=u(t);if(!r){if(o&&h(Rt,e))return Reflect.get(Rt,e,n);if("hasOwnProperty"===e)return Mt}const a=Reflect.get(t,e,n);return(y(e)?Tt.has(e):Ot(e))?a:(r||Pt(t,0,e),i?a:Oe(a)?o&&C(e)?a:a.value:v(a)?r?ge(a):fe(a):a)}}class Lt extends Nt{constructor(t=!1){super(!1,t)}set(t,e,n,r){let i=t[e];if(!this._shallow){const e=ve(i);if(be(n)||ve(n)||(i=we(i),n=we(n)),!u(t)&&Oe(i)&&!Oe(n))return!e&&(i.value=n,!0)}const o=u(t)&&C(e)?Number(e)<t.length:h(t,e),a=Reflect.set(t,e,n,r);return t===we(r)&&(o?M(n,i)&&kt(t,"set",e,n):kt(t,"add",e,n)),a}deleteProperty(t,e){const n=h(t,e);t[e];const r=Reflect.deleteProperty(t,e);return r&&n&&kt(t,"delete",e,void 0),r}has(t,e){const n=Reflect.has(t,e);return y(e)&&Tt.has(e)||Pt(t,0,e),n}ownKeys(t){return Pt(t,0,u(t)?"length":At),Reflect.ownKeys(t)}}class Dt extends Nt{constructor(t=!1){super(!0,t)}set(t,e){return!0}deleteProperty(t,e){return!0}}const It=new Lt,jt=new Dt,Ut=new Lt(!0),Gt=new Dt(!0),Bt=t=>t,Vt=t=>Reflect.getPrototypeOf(t);function Ht(t,e,n=!1,r=!1){const i=we(t=t.__v_raw),o=we(e);n||(M(e,o)&&Pt(i,0,e),Pt(i,0,o));const{has:a}=Vt(i),s=r?Bt:n?Ce:xe;return a.call(i,e)?s(t.get(e)):a.call(i,o)?s(t.get(o)):void(t!==i&&t.get(e))}function zt(t,e=!1){const n=this.__v_raw,r=we(n),i=we(t);return e||(M(t,i)&&Pt(r,0,t),Pt(r,0,i)),t===i?n.has(t):n.has(t)||n.has(i)}function Wt(t,e=!1){return t=t.__v_raw,!e&&Pt(we(t),0,At),Reflect.get(t,"size",t)}function $t(t){t=we(t);const e=we(this);return Vt(e).has.call(e,t)||(e.add(t),kt(e,"add",t,t)),this}function Kt(t,e){e=we(e);const n=we(this),{has:r,get:i}=Vt(n);let o=r.call(n,t);o||(t=we(t),o=r.call(n,t));const a=i.call(n,t);return n.set(t,e),o?M(e,a)&&kt(n,"set",t,e):kt(n,"add",t,e),this}function qt(t){const e=we(this),{has:n,get:r}=Vt(e);let i=n.call(e,t);i||(t=we(t),i=n.call(e,t)),r&&r.call(e,t);const o=e.delete(t);return i&&kt(e,"delete",t,void 0),o}function Yt(){const t=we(this),e=0!==t.size,n=t.clear();return e&&kt(t,"clear",void 0,void 0),n}function Xt(t,e){return function(n,r){const i=this,o=i.__v_raw,a=we(o),s=e?Bt:t?Ce:xe;return!t&&Pt(a,0,At),o.forEach(((t,e)=>n.call(r,s(t),s(e),i)))}}function Qt(t,e,n){return function(...r){const i=this.__v_raw,o=we(i),a=d(o),s="entries"===t||t===Symbol.iterator&&a,l="keys"===t&&a,c=i[t](...r),h=n?Bt:e?Ce:xe;return!e&&Pt(o,0,l?Et:At),{next(){const{value:t,done:e}=c.next();return e?{value:t,done:e}:{value:s?[h(t[0]),h(t[1])]:h(t),done:e}},[Symbol.iterator](){return this}}}}function Jt(t){return function(...e){return"delete"!==t&&("clear"===t?void 0:this)}}function Zt(){const t={get(t){return Ht(this,t)},get size(){return Wt(this)},has:zt,add:$t,set:Kt,delete:qt,clear:Yt,forEach:Xt(!1,!1)},e={get(t){return Ht(this,t,!1,!0)},get size(){return Wt(this)},has:zt,add:$t,set:Kt,delete:qt,clear:Yt,forEach:Xt(!1,!0)},n={get(t){return Ht(this,t,!0)},get size(){return Wt(this,!0)},has(t){return zt.call(this,t,!0)},add:Jt("add"),set:Jt("set"),delete:Jt("delete"),clear:Jt("clear"),forEach:Xt(!0,!1)},r={get(t){return Ht(this,t,!0,!0)},get size(){return Wt(this,!0)},has(t){return zt.call(this,t,!0)},add:Jt("add"),set:Jt("set"),delete:Jt("delete"),clear:Jt("clear"),forEach:Xt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{t[i]=Qt(i,!1,!1),n[i]=Qt(i,!0,!1),e[i]=Qt(i,!1,!0),r[i]=Qt(i,!0,!0)})),[t,n,e,r]}const[te,ee,ne,re]=Zt();function ie(t,e){const n=e?t?re:ne:t?ee:te;return(e,r,i)=>"__v_isReactive"===r?!t:"__v_isReadonly"===r?t:"__v_raw"===r?e:Reflect.get(h(n,r)&&r in e?n:e,r,i)}const oe={get:ie(!1,!1)},ae={get:ie(!1,!0)},se={get:ie(!0,!1)},le={get:ie(!0,!0)},ce=new WeakMap,he=new WeakMap,ue=new WeakMap,de=new WeakMap;function fe(t){return ve(t)?t:me(t,!1,It,oe,ce)}function pe(t){return me(t,!1,Ut,ae,he)}function ge(t){return me(t,!0,jt,se,ue)}function me(t,e,n,r,i){if(!v(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const o=i.get(t);if(o)return o;const a=(s=t).__v_skip||!Object.isExtensible(s)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(S(s));var s;if(0===a)return t;const l=new Proxy(t,2===a?r:n);return i.set(t,l),l}function ye(t){return ve(t)?ye(t.__v_raw):!(!t||!t.__v_isReactive)}function ve(t){return!(!t||!t.__v_isReadonly)}function be(t){return!(!t||!t.__v_isShallow)}function _e(t){return ye(t)||ve(t)}function we(t){const e=t&&t.__v_raw;return e?we(e):t}function Se(t){return L(t,"__v_skip",!0),t}const xe=t=>v(t)?fe(t):t,Ce=t=>v(t)?ge(t):t;class Ae{constructor(t,e,n,r){this._setter=e,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new lt((()=>t(this._value)),(()=>ke(this,1)),(()=>this.dep&&St(this.dep))),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){const t=we(this);return t._cacheable&&!t.effect.dirty||M(t._value,t._value=t.effect.run())&&ke(t,2),Pe(t),t.effect._dirtyLevel>=1&&ke(t,1),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function Ee(t,e,n=!1){let i,o;const a=g(t);a?(i=t,o=r):(i=t.get,o=t.set);return new Ae(i,o,a||!o,n)}function Pe(t){dt&&nt&&(t=we(t),bt(nt,t.dep||(t.dep=xt((()=>t.dep=void 0),t instanceof Ae?t:void 0))))}function ke(t,e=2,n){const r=(t=we(t)).dep;r&&wt(r,e)}function Oe(t){return!(!t||!0!==t.__v_isRef)}function Te(t){return Fe(t,!1)}function Re(t){return Fe(t,!0)}function Fe(t,e){return Oe(t)?t:new Me(t,e)}class Me{constructor(t,e){this.__v_isShallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:we(t),this._value=e?t:xe(t)}get value(){return Pe(this),this._value}set value(t){const e=this.__v_isShallow||be(t)||ve(t);t=e?t:we(t),M(t,this._rawValue)&&(this._rawValue=t,this._value=e?t:xe(t),ke(this,2))}}function Ne(t){return Oe(t)?t.value:t}const Le={get:(t,e,n)=>Ne(Reflect.get(t,e,n)),set:(t,e,n,r)=>{const i=t[e];return Oe(i)&&!Oe(n)?(i.value=n,!0):Reflect.set(t,e,n,r)}};function De(t){return ye(t)?t:new Proxy(t,Le)}class Ie{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:e,set:n}=t((()=>Pe(this)),(()=>ke(this)));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}function je(t){return new Ie(t)}function Ue(t){const e=u(t)?new Array(t.length):{};for(const n in t)e[n]=He(t,n);return e}class Ge{constructor(t,e,n){this._object=t,this._key=e,this._defaultValue=n,this.__v_isRef=!0}get value(){const t=this._object[this._key];return void 0===t?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return t=we(this._object),e=this._key,null==(n=Ct.get(t))?void 0:n.get(e);var t,e,n}}class Be{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function Ve(t,e,n){return Oe(t)?t:g(t)?new Be(t):v(t)&&arguments.length>1?He(t,e,n):Te(t)}function He(t,e,n){const r=t[e];return Oe(r)?r:new Ge(t,e,n)}const ze={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://github.com/vuejs/core ."};function We(t,e,n,r){let i;try{i=r?t(...r):t()}catch(o){Ke(o,e,n)}return i}function $e(t,e,n,r){if(g(t)){const i=We(t,e,n,r);return i&&b(i)&&i.catch((t=>{Ke(t,e,n)})),i}const i=[];for(let o=0;o<t.length;o++)i.push($e(t[o],e,n,r));return i}function Ke(t,e,n,r=!0){e&&e.vnode;if(e){let r=e.parent;const i=e.proxy,o=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){const e=r.ec;if(e)for(let n=0;n<e.length;n++)if(!1===e[n](t,i,o))return;r=r.parent}const a=e.appContext.config.errorHandler;if(a)return void We(a,null,10,[t,i,o])}!function(t,e,n,r=!0){console.error(t)}(t,0,0,r)}let qe=!1,Ye=!1;const Xe=[];let Qe=0;const Je=[];let Ze=null,tn=0;const en=Promise.resolve();let nn=null;function rn(t){const e=nn||en;return t?e.then(this?t.bind(this):t):e}function on(t){Xe.length&&Xe.includes(t,qe&&t.allowRecurse?Qe+1:Qe)||(null==t.id?Xe.push(t):Xe.splice(function(t){let e=Qe+1,n=Xe.length;for(;e<n;){const r=e+n>>>1,i=Xe[r],o=hn(i);o<t||o===t&&i.pre?e=r+1:n=r}return e}(t.id),0,t),an())}function an(){qe||Ye||(Ye=!0,nn=en.then(dn))}function sn(t){u(t)?Je.push(...t):Ze&&Ze.includes(t,t.allowRecurse?tn+1:tn)||Je.push(t),an()}function ln(t,e,n=(qe?Qe+1:0)){for(;n<Xe.length;n++){const e=Xe[n];if(e&&e.pre){if(t&&e.id!==t.uid)continue;Xe.splice(n,1),n--,e()}}}function cn(t){if(Je.length){const t=[...new Set(Je)].sort(((t,e)=>hn(t)-hn(e)));if(Je.length=0,Ze)return void Ze.push(...t);for(Ze=t,tn=0;tn<Ze.length;tn++)Ze[tn]();Ze=null,tn=0}}const hn=t=>null==t.id?1/0:t.id,un=(t,e)=>{const n=hn(t)-hn(e);if(0===n){if(t.pre&&!e.pre)return-1;if(e.pre&&!t.pre)return 1}return n};function dn(t){Ye=!1,qe=!0,Xe.sort(un);try{for(Qe=0;Qe<Xe.length;Qe++){const t=Xe[Qe];t&&!1!==t.active&&We(t,null,14)}}finally{Qe=0,Xe.length=0,cn(),qe=!1,nn=null,(Xe.length||Je.length)&&dn()}}let fn,pn=[];function gn(t,n,...r){if(t.isUnmounted)return;const i=t.vnode.props||e;let o=r;const a=n.startsWith("update:"),s=a&&n.slice(7);if(s&&s in i){const t=`${"modelValue"===s?"model":s}Modifiers`,{number:n,trim:a}=i[t]||e;a&&(o=r.map((t=>m(t)?t.trim():t))),n&&(o=r.map(D))}let l,c=i[l=F(n)]||i[l=F(k(n))];!c&&a&&(c=i[l=F(T(n))]),c&&$e(c,t,6,o);const h=i[l+"Once"];if(h){if(t.emitted){if(t.emitted[l])return}else t.emitted={};t.emitted[l]=!0,$e(h,t,6,o)}}function mn(t,e,n=!1){const r=e.emitsCache,i=r.get(t);if(void 0!==i)return i;const o=t.emits;let a={},l=!1;if(!g(t)){const r=t=>{const n=mn(t,e,!0);n&&(l=!0,s(a,n))};!n&&e.mixins.length&&e.mixins.forEach(r),t.extends&&r(t.extends),t.mixins&&t.mixins.forEach(r)}return o||l?(u(o)?o.forEach((t=>a[t]=null)):s(a,o),v(t)&&r.set(t,a),a):(v(t)&&r.set(t,null),null)}function yn(t,e){return!(!t||!o(e))&&(e=e.slice(2).replace(/Once$/,""),h(t,e[0].toLowerCase()+e.slice(1))||h(t,T(e))||h(t,e))}let vn=null,bn=null;function _n(t){const e=vn;return vn=t,bn=t&&t.type.__scopeId||null,e}function wn(t){bn=t}function Sn(){bn=null}function xn(t,e=vn,n){if(!e)return t;if(t._n)return t;const r=(...n)=>{r._d&&co(-1);const i=_n(e);let o;try{o=t(...n)}finally{_n(i),r._d&&co(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function Cn(t){const{type:e,vnode:n,proxy:r,withProxy:i,props:o,propsOptions:[s],slots:l,attrs:c,emit:h,render:u,renderCache:d,data:f,setupState:p,ctx:g,inheritAttrs:m}=t;let y,v;const b=_n(t);try{if(4&n.shapeFlag){const t=i||r,e=t;y=Eo(u.call(e,t,d,o,p,f,g)),v=c}else{const t=e;0,y=Eo(t.length>1?t(o,{attrs:c,slots:l,emit:h}):t(o,null)),v=e.props?c:An(c)}}catch(w){io.length=0,Ke(w,t,1),y=_o(no)}let _=y;if(v&&!1!==m){const t=Object.keys(v),{shapeFlag:e}=_;t.length&&7&e&&(s&&t.some(a)&&(v=En(v,s)),_=So(_,v))}return n.dirs&&(_=So(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),y=_,_n(b),y}const An=t=>{let e;for(const n in t)("class"===n||"style"===n||o(n))&&((e||(e={}))[n]=t[n]);return e},En=(t,e)=>{const n={};for(const r in t)a(r)&&r.slice(9)in e||(n[r]=t[r]);return n};function Pn(t,e,n){const r=Object.keys(e);if(r.length!==Object.keys(t).length)return!0;for(let i=0;i<r.length;i++){const o=r[i];if(e[o]!==t[o]&&!yn(n,o))return!0}return!1}function kn({vnode:t,parent:e},n){for(;e;){const r=e.subTree;if(r.suspense&&r.suspense.activeBranch===t&&(r.el=t.el),r!==t)break;(t=e.vnode).el=n,e=e.parent}}const On="components";function Tn(t,e){return Nn(On,t,!0,e)||t}const Rn=Symbol.for("v-ndc");function Fn(t){return m(t)?Nn(On,t,!1)||t:t||Rn}function Mn(t){return Nn("directives",t)}function Nn(t,e,n=!0,r=!1){const i=vn||No;if(i){const n=i.type;if(t===On){const t=Yo(n,!1);if(t&&(t===e||t===k(e)||t===R(k(e))))return n}const o=Ln(i[t]||n[t],e)||Ln(i.appContext[t],e);return!o&&r?n:o}}function Ln(t,e){return t&&(t[e]||t[k(e)]||t[R(k(e))])}const Dn=t=>t.__isSuspense;let In=0;const jn={name:"Suspense",__isSuspense:!0,process(t,e,n,r,i,o,a,s,l,c){if(null==t)!function(t,e,n,r,i,o,a,s,l){const{p:c,o:{createElement:h}}=l,u=h("div"),d=t.suspense=Gn(t,i,r,e,u,n,o,a,s,l);c(null,d.pendingBranch=t.ssContent,u,null,r,d,o,a),d.deps>0?(Un(t,"onPending"),Un(t,"onFallback"),c(null,t.ssFallback,e,n,r,null,o,a),Hn(d,t.ssFallback)):d.resolve(!1,!0)}(e,n,r,i,o,a,s,l,c);else{if(o&&o.deps>0)return void(e.suspense=t.suspense);!function(t,e,n,r,i,o,a,s,{p:l,um:c,o:{createElement:h}}){const u=e.suspense=t.suspense;u.vnode=e,e.el=t.el;const d=e.ssContent,f=e.ssFallback,{activeBranch:p,pendingBranch:g,isInFallback:m,isHydrating:y}=u;if(g)u.pendingBranch=d,go(d,g)?(l(g,d,u.hiddenContainer,null,i,u,o,a,s),u.deps<=0?u.resolve():m&&(y||(l(p,f,n,r,i,null,o,a,s),Hn(u,f)))):(u.pendingId=In++,y?(u.isHydrating=!1,u.activeBranch=g):c(g,i,u),u.deps=0,u.effects.length=0,u.hiddenContainer=h("div"),m?(l(null,d,u.hiddenContainer,null,i,u,o,a,s),u.deps<=0?u.resolve():(l(p,f,n,r,i,null,o,a,s),Hn(u,f))):p&&go(d,p)?(l(p,d,n,r,i,u,o,a,s),u.resolve(!0)):(l(null,d,u.hiddenContainer,null,i,u,o,a,s),u.deps<=0&&u.resolve()));else if(p&&go(d,p))l(p,d,n,r,i,u,o,a,s),Hn(u,d);else if(Un(e,"onPending"),u.pendingBranch=d,512&d.shapeFlag?u.pendingId=d.component.suspenseId:u.pendingId=In++,l(null,d,u.hiddenContainer,null,i,u,o,a,s),u.deps<=0)u.resolve();else{const{timeout:t,pendingId:e}=u;t>0?setTimeout((()=>{u.pendingId===e&&u.fallback(f)}),t):0===t&&u.fallback(f)}}(t,e,n,r,i,a,s,l,c)}},hydrate:function(t,e,n,r,i,o,a,s,l){const c=e.suspense=Gn(e,r,n,t.parentNode,document.createElement("div"),null,i,o,a,s,!0),h=l(t,c.pendingBranch=e.ssContent,n,c,o,a);0===c.deps&&c.resolve(!1,!0);return h},create:Gn,normalize:function(t){const{shapeFlag:e,children:n}=t,r=32&e;t.ssContent=Bn(r?n.default:n),t.ssFallback=r?Bn(n.fallback):_o(no)}};function Un(t,e){const n=t.props&&t.props[e];g(n)&&n()}function Gn(t,e,n,r,i,o,a,s,l,c,h=!1){const{p:u,m:d,um:f,n:p,o:{parentNode:g,remove:m}}=c;let y;const v=function(t){var e;return null!=(null==(e=t.props)?void 0:e.suspensible)&&!1!==t.props.suspensible}(t);v&&(null==e?void 0:e.pendingBranch)&&(y=e.pendingId,e.deps++);const b=t.props?I(t.props.timeout):void 0,_=o,w={vnode:t,parent:e,parentComponent:n,namespace:a,container:r,hiddenContainer:i,deps:0,pendingId:In++,timeout:"number"==typeof b?b:-1,activeBranch:null,pendingBranch:null,isInFallback:!h,isHydrating:h,isUnmounted:!1,effects:[],resolve(t=!1,n=!1){const{vnode:r,activeBranch:i,pendingBranch:a,pendingId:s,effects:l,parentComponent:c,container:h}=w;let u=!1;w.isHydrating?w.isHydrating=!1:t||(u=i&&a.transition&&"out-in"===a.transition.mode,u&&(i.transition.afterLeave=()=>{s===w.pendingId&&(d(a,h,o===_?p(i):o,0),sn(l))}),i&&(g(i.el)!==w.hiddenContainer&&(o=p(i)),f(i,c,w,!0)),u||d(a,h,o,0)),Hn(w,a),w.pendingBranch=null,w.isInFallback=!1;let m=w.parent,b=!1;for(;m;){if(m.pendingBranch){m.effects.push(...l),b=!0;break}m=m.parent}b||u||sn(l),w.effects=[],v&&e&&e.pendingBranch&&y===e.pendingId&&(e.deps--,0!==e.deps||n||e.resolve()),Un(r,"onResolve")},fallback(t){if(!w.pendingBranch)return;const{vnode:e,activeBranch:n,parentComponent:r,container:i,namespace:o}=w;Un(e,"onFallback");const a=p(n),c=()=>{w.isInFallback&&(u(null,t,i,a,r,null,o,s,l),Hn(w,t))},h=t.transition&&"out-in"===t.transition.mode;h&&(n.transition.afterLeave=c),w.isInFallback=!0,f(n,r,null,!0),h||c()},move(t,e,n){w.activeBranch&&d(w.activeBranch,t,e,n),w.container=t},next:()=>w.activeBranch&&p(w.activeBranch),registerDep(t,e){const n=!!w.pendingBranch;n&&w.deps++;const r=t.vnode.el;t.asyncDep.catch((e=>{Ke(e,t,0)})).then((i=>{if(t.isUnmounted||w.isUnmounted||w.pendingId!==t.suspenseId)return;t.asyncResolved=!0;const{vnode:o}=t;Wo(t,i,!1),r&&(o.el=r);const s=!r&&t.subTree.el;e(t,o,g(r||t.subTree.el),r?null:p(t.subTree),w,a,l),s&&m(s),kn(t,o.el),n&&0==--w.deps&&w.resolve()}))},unmount(t,e){w.isUnmounted=!0,w.activeBranch&&f(w.activeBranch,n,t,e),w.pendingBranch&&f(w.pendingBranch,n,t,e)}};return w}function Bn(t){let e;if(g(t)){const n=lo&&t._c;n&&(t._d=!1,ao()),t=t(),n&&(t._d=!0,e=oo,so())}if(u(t)){const e=function(t,e=!0){let n;for(let r=0;r<t.length;r++){const e=t[r];if(!po(e))return;if(e.type!==no||"v-if"===e.children){if(n)return;n=e}}return n}(t);t=e}return t=Eo(t),e&&!t.dynamicChildren&&(t.dynamicChildren=e.filter((e=>e!==t))),t}function Vn(t,e){e&&e.pendingBranch?u(t)?e.effects.push(...t):e.effects.push(t):sn(t)}function Hn(t,e){t.activeBranch=e;const{vnode:n,parentComponent:r}=t;let i=e.el;for(;!i&&e.component;)i=(e=e.component.subTree).el;n.el=i,r&&r.subTree===n&&(r.vnode.el=i,kn(r,i))}const zn=Symbol.for("v-scx"),Wn=()=>yi(zn);function $n(t,e){return Qn(t,null,e)}function Kn(t,e){return Qn(t,null,{flush:"post"})}function qn(t,e){return Qn(t,null,{flush:"sync"})}const Yn={};function Xn(t,e,n){return Qn(t,e,n)}function Qn(t,n,{immediate:i,deep:o,flush:a,once:s,onTrack:c,onTrigger:h}=e){if(n&&s){const t=n;n=(...e)=>{t(...e),E()}}const d=No,f=t=>!0===o?t:tr(t,!1===o?1:void 0);let p,m,y=!1,v=!1;if(Oe(t)?(p=()=>t.value,y=be(t)):ye(t)?(p=()=>f(t),y=!0):u(t)?(v=!0,y=t.some((t=>ye(t)||be(t))),p=()=>t.map((t=>Oe(t)?t.value:ye(t)?f(t):g(t)?We(t,d,2):void 0))):p=g(t)?n?()=>We(t,d,2):()=>(m&&m(),$e(t,d,3,[_])):r,n&&o){const t=p;p=()=>tr(t())}let b,_=t=>{m=C.onStop=()=>{We(t,d,4),m=C.onStop=void 0}};if(Ho){if(_=r,n?i&&$e(n,d,3,[p(),v?[]:void 0,_]):p(),"sync"!==a)return r;{const t=Wn();b=t.__watcherHandles||(t.__watcherHandles=[])}}let w=v?new Array(t.length).fill(Yn):Yn;const S=()=>{if(C.active&&C.dirty)if(n){const t=C.run();(o||y||(v?t.some(((t,e)=>M(t,w[e]))):M(t,w)))&&(m&&m(),$e(n,d,3,[t,w===Yn?void 0:v&&w[0]===Yn?[]:w,_]),w=t)}else C.run()};let x;S.allowRecurse=!!n,"sync"===a?x=S:"post"===a?x=()=>ji(S,d&&d.suspense):(S.pre=!0,d&&(S.id=d.uid),x=()=>on(S));const C=new lt(p,r,x),A=at(),E=()=>{C.stop(),A&&l(A.effects,C)};return n?i?S():w=C.run():"post"===a?ji(C.run.bind(C),d&&d.suspense):C.run(),b&&b.push(E),E}function Jn(t,e,n){const r=this.proxy,i=m(t)?t.includes(".")?Zn(r,t):()=>r[t]:t.bind(r,r);let o;g(e)?o=e:(o=e.handler,n=e);const a=jo(this),s=Qn(i,o.bind(r),n);return a(),s}function Zn(t,e){const n=e.split(".");return()=>{let e=t;for(let t=0;t<n.length&&e;t++)e=e[n[t]];return e}}function tr(t,e,n=0,r){if(!v(t)||t.__v_skip)return t;if(e&&e>0){if(n>=e)return t;n++}if((r=r||new Set).has(t))return t;if(r.add(t),Oe(t))tr(t.value,e,n,r);else if(u(t))for(let i=0;i<t.length;i++)tr(t[i],e,n,r);else if(f(t)||d(t))t.forEach((t=>{tr(t,e,n,r)}));else if(x(t))for(const i in t)tr(t[i],e,n,r);return t}function er(t,n){if(null===vn)return t;const r=qo(vn)||vn.proxy,i=t.dirs||(t.dirs=[]);for(let o=0;o<n.length;o++){let[t,a,s,l=e]=n[o];t&&(g(t)&&(t={mounted:t,updated:t}),t.deep&&tr(a),i.push({dir:t,instance:r,value:a,oldValue:void 0,arg:s,modifiers:l}))}return t}function nr(t,e,n,r){const i=t.dirs,o=e&&e.dirs;for(let a=0;a<i.length;a++){const s=i[a];o&&(s.oldValue=o[a].value);let l=s.dir[r];l&&(gt(),$e(l,n,8,[t.el,s,t,e]),mt())}}const rr=Symbol("_leaveCb"),ir=Symbol("_enterCb");function or(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Rr((()=>{t.isMounted=!0})),Nr((()=>{t.isUnmounting=!0})),t}const ar=[Function,Array],sr={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ar,onEnter:ar,onAfterEnter:ar,onEnterCancelled:ar,onBeforeLeave:ar,onLeave:ar,onAfterLeave:ar,onLeaveCancelled:ar,onBeforeAppear:ar,onAppear:ar,onAfterAppear:ar,onAppearCancelled:ar},lr={name:"BaseTransition",props:sr,setup(t,{slots:e}){const n=Lo(),r=or();let i;return()=>{const o=e.default&&pr(e.default(),!0);if(!o||!o.length)return;let a=o[0];if(o.length>1)for(const t of o)if(t.type!==no){a=t;break}const s=we(t),{mode:l}=s;if(r.isLeaving)return ur(a);const c=dr(a);if(!c)return ur(a);const h=hr(c,s,r,n);fr(c,h);const u=n.subTree,d=u&&dr(u);let f=!1;const{getTransitionKey:p}=c.type;if(p){const t=p();void 0===i?i=t:t!==i&&(i=t,f=!0)}if(d&&d.type!==no&&(!go(c,d)||f)){const t=hr(d,s,r,n);if(fr(d,t),"out-in"===l)return r.isLeaving=!0,t.afterLeave=()=>{r.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},ur(a);"in-out"===l&&c.type!==no&&(t.delayLeave=(t,e,n)=>{cr(r,d)[String(d.key)]=d,t[rr]=()=>{e(),t[rr]=void 0,delete h.delayedLeave},h.delayedLeave=n})}return a}}};function cr(t,e){const{leavingVNodes:n}=t;let r=n.get(e.type);return r||(r=Object.create(null),n.set(e.type,r)),r}function hr(t,e,n,r){const{appear:i,mode:o,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:h,onBeforeLeave:d,onLeave:f,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:m,onAppear:y,onAfterAppear:v,onAppearCancelled:b}=e,_=String(t.key),w=cr(n,t),S=(t,e)=>{t&&$e(t,r,9,e)},x=(t,e)=>{const n=e[1];S(t,e),u(t)?t.every((t=>t.length<=1))&&n():t.length<=1&&n()},C={mode:o,persisted:a,beforeEnter(e){let r=s;if(!n.isMounted){if(!i)return;r=m||s}e[rr]&&e[rr](!0);const o=w[_];o&&go(t,o)&&o.el[rr]&&o.el[rr](),S(r,[e])},enter(t){let e=l,r=c,o=h;if(!n.isMounted){if(!i)return;e=y||l,r=v||c,o=b||h}let a=!1;const s=t[ir]=e=>{a||(a=!0,S(e?o:r,[t]),C.delayedLeave&&C.delayedLeave(),t[ir]=void 0)};e?x(e,[t,s]):s()},leave(e,r){const i=String(t.key);if(e[ir]&&e[ir](!0),n.isUnmounting)return r();S(d,[e]);let o=!1;const a=e[rr]=n=>{o||(o=!0,r(),S(n?g:p,[e]),e[rr]=void 0,w[i]===t&&delete w[i])};w[i]=t,f?x(f,[e,a]):a()},clone:t=>hr(t,e,n,r)};return C}function ur(t){if(vr(t))return(t=So(t)).children=null,t}function dr(t){return vr(t)?t.children?t.children[0]:void 0:t}function fr(t,e){6&t.shapeFlag&&t.component?fr(t.component.subTree,e):128&t.shapeFlag?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function pr(t,e=!1,n){let r=[],i=0;for(let o=0;o<t.length;o++){let a=t[o];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:o);a.type===to?(128&a.patchFlag&&i++,r=r.concat(pr(a.children,e,s))):(e||a.type!==no)&&r.push(null!=s?So(a,{key:s}):a)}if(i>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */function gr(t,e){return g(t)?(()=>s({name:t.name},e,{setup:t}))():t}const mr=t=>!!t.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function yr(t,e){const{ref:n,props:r,children:i,ce:o}=e.vnode,a=_o(t,r,i);return a.ref=n,a.ce=o,delete e.vnode.ce,a}const vr=t=>t.type.__isKeepAlive,br={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(t,{slots:e}){const n=Lo(),r=n.ctx;if(!r.renderer)return()=>{const t=e.default&&e.default();return t&&1===t.length?t[0]:t};const i=new Map,o=new Set;let a=null;const s=n.suspense,{renderer:{p:l,m:c,um:h,o:{createElement:u}}}=r,d=u("div");function f(t){Er(t),h(t,n,s,!0)}function p(t){i.forEach(((e,n)=>{const r=Yo(e.type);!r||t&&t(r)||g(n)}))}function g(t){const e=i.get(t);a&&go(e,a)?a&&Er(a):f(e),i.delete(t),o.delete(t)}r.activate=(t,e,n,r,i)=>{const o=t.component;c(t,e,n,0,s),l(o.vnode,t,e,n,o,s,r,t.slotScopeIds,i),ji((()=>{o.isDeactivated=!1,o.a&&N(o.a);const e=t.props&&t.props.onVnodeMounted;e&&To(e,o.parent,t)}),s)},r.deactivate=t=>{const e=t.component;c(t,d,null,1,s),ji((()=>{e.da&&N(e.da);const n=t.props&&t.props.onVnodeUnmounted;n&&To(n,e.parent,t),e.isDeactivated=!0}),s)},Xn((()=>[t.include,t.exclude]),(([t,e])=>{t&&p((e=>wr(t,e))),e&&p((t=>!wr(e,t)))}),{flush:"post",deep:!0});let m=null;const y=()=>{null!=m&&i.set(m,Pr(n.subTree))};return Rr(y),Mr(y),Nr((()=>{i.forEach((t=>{const{subTree:e,suspense:r}=n,i=Pr(e);if(t.type!==i.type||t.key!==i.key)f(t);else{Er(i);const t=i.component.da;t&&ji(t,r)}}))})),()=>{if(m=null,!e.default)return null;const n=e.default(),r=n[0];if(n.length>1)return a=null,n;if(!(po(r)&&(4&r.shapeFlag||128&r.shapeFlag)))return a=null,r;let s=Pr(r);const l=s.type,c=Yo(mr(s)?s.type.__asyncResolved||{}:l),{include:h,exclude:u,max:d}=t;if(h&&(!c||!wr(h,c))||u&&c&&wr(u,c))return a=s,r;const f=null==s.key?l:s.key,p=i.get(f);return s.el&&(s=So(s),128&r.shapeFlag&&(r.ssContent=s)),m=f,p?(s.el=p.el,s.component=p.component,s.transition&&fr(s,s.transition),s.shapeFlag|=512,o.delete(f),o.add(f)):(o.add(f),d&&o.size>parseInt(d,10)&&g(o.values().next().value)),s.shapeFlag|=256,a=s,Dn(r.type)?r:s}}},_r=br;function wr(t,e){return u(t)?t.some((t=>wr(t,e))):m(t)?t.split(",").includes(e):"[object RegExp]"===w(t)&&t.test(e)}function Sr(t,e){Cr(t,"a",e)}function xr(t,e){Cr(t,"da",e)}function Cr(t,e,n=No){const r=t.__wdc||(t.__wdc=()=>{let e=n;for(;e;){if(e.isDeactivated)return;e=e.parent}return t()});if(kr(e,r,n),n){let t=n.parent;for(;t&&t.parent;)vr(t.parent.vnode)&&Ar(r,e,n,t),t=t.parent}}function Ar(t,e,n,r){const i=kr(e,t,r,!0);Lr((()=>{l(r[e],i)}),n)}function Er(t){t.shapeFlag&=-257,t.shapeFlag&=-513}function Pr(t){return 128&t.shapeFlag?t.ssContent:t}function kr(t,e,n=No,r=!1){if(n){const i=n[t]||(n[t]=[]),o=e.__weh||(e.__weh=(...r)=>{if(n.isUnmounted)return;gt();const i=jo(n),o=$e(e,n,t,r);return i(),mt(),o});return r?i.unshift(o):i.push(o),o}}const Or=t=>(e,n=No)=>(!Ho||"sp"===t)&&kr(t,((...t)=>e(...t)),n),Tr=Or("bm"),Rr=Or("m"),Fr=Or("bu"),Mr=Or("u"),Nr=Or("bum"),Lr=Or("um"),Dr=Or("sp"),Ir=Or("rtg"),jr=Or("rtc");function Ur(t,e=No){kr("ec",t,e)}function Gr(t,e,n,r){let i;const o=n&&n[r];if(u(t)||m(t)){i=new Array(t.length);for(let n=0,r=t.length;n<r;n++)i[n]=e(t[n],n,void 0,o&&o[n])}else if("number"==typeof t){i=new Array(t);for(let n=0;n<t;n++)i[n]=e(n+1,n,void 0,o&&o[n])}else if(v(t))if(t[Symbol.iterator])i=Array.from(t,((t,n)=>e(t,n,void 0,o&&o[n])));else{const n=Object.keys(t);i=new Array(n.length);for(let r=0,a=n.length;r<a;r++){const a=n[r];i[r]=e(t[a],a,r,o&&o[r])}}else i=[];return n&&(n[r]=i),i}function Br(t,e){for(let n=0;n<e.length;n++){const r=e[n];if(u(r))for(let e=0;e<r.length;e++)t[r[e].name]=r[e].fn;else r&&(t[r.name]=r.key?(...t)=>{const e=r.fn(...t);return e&&(e.key=r.key),e}:r.fn)}return t}function Vr(t,e,n={},r,i){if(vn.isCE||vn.parent&&mr(vn.parent)&&vn.parent.isCE)return"default"!==e&&(n.name=e),_o("slot",n,r&&r());let o=t[e];o&&o._c&&(o._d=!1),ao();const a=o&&Hr(o(n)),s=fo(to,{key:n.key||a&&a.key||`_${e}`},a||(r?r():[]),a&&1===t._?64:-2);return!i&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),o&&o._c&&(o._d=!0),s}function Hr(t){return t.some((t=>!po(t)||t.type!==no&&!(t.type===to&&!Hr(t.children))))?t:null}function zr(t,e){const n={};for(const r in t)n[e&&/[A-Z]/.test(r)?`on:${r}`:F(r)]=t[r];return n}const Wr=t=>t?Go(t)?qo(t)||t.proxy:Wr(t.parent):null,$r=s(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>Wr(t.parent),$root:t=>Wr(t.root),$emit:t=>t.emit,$options:t=>ii(t),$forceUpdate:t=>t.f||(t.f=()=>{t.effect.dirty=!0,on(t.update)}),$nextTick:t=>t.n||(t.n=rn.bind(t.proxy)),$watch:t=>Jn.bind(t)}),Kr=(t,n)=>t!==e&&!t.__isScriptSetup&&h(t,n),qr={get({_:t},n){const{ctx:r,setupState:i,data:o,props:a,accessCache:s,type:l,appContext:c}=t;let u;if("$"!==n[0]){const l=s[n];if(void 0!==l)switch(l){case 1:return i[n];case 2:return o[n];case 4:return r[n];case 3:return a[n]}else{if(Kr(i,n))return s[n]=1,i[n];if(o!==e&&h(o,n))return s[n]=2,o[n];if((u=t.propsOptions[0])&&h(u,n))return s[n]=3,a[n];if(r!==e&&h(r,n))return s[n]=4,r[n];ti&&(s[n]=0)}}const d=$r[n];let f,p;return d?("$attrs"===n&&Pt(t,0,n),d(t)):(f=l.__cssModules)&&(f=f[n])?f:r!==e&&h(r,n)?(s[n]=4,r[n]):(p=c.config.globalProperties,h(p,n)?p[n]:void 0)},set({_:t},n,r){const{data:i,setupState:o,ctx:a}=t;return Kr(o,n)?(o[n]=r,!0):i!==e&&h(i,n)?(i[n]=r,!0):!h(t.props,n)&&(("$"!==n[0]||!(n.slice(1)in t))&&(a[n]=r,!0))},has({_:{data:t,setupState:n,accessCache:r,ctx:i,appContext:o,propsOptions:a}},s){let l;return!!r[s]||t!==e&&h(t,s)||Kr(n,s)||(l=a[0])&&h(l,s)||h(i,s)||h($r,s)||h(o.config.globalProperties,s)},defineProperty(t,e,n){return null!=n.get?t._.accessCache[e]=0:h(n,"value")&&this.set(t,e,n.value,null),Reflect.defineProperty(t,e,n)}},Yr=s({},qr,{get(t,e){if(e!==Symbol.unscopables)return qr.get(t,e,t)},has:(t,e)=>"_"!==e[0]&&!G(e)});function Xr(){return Jr().slots}function Qr(){return Jr().attrs}function Jr(){const t=Lo();return t.setupContext||(t.setupContext=Ko(t))}function Zr(t){return u(t)?t.reduce(((t,e)=>(t[e]=null,t)),{}):t}let ti=!0;function ei(t){const e=ii(t),n=t.proxy,i=t.ctx;ti=!1,e.beforeCreate&&ni(e.beforeCreate,t,"bc");const{data:o,computed:a,methods:s,watch:l,provide:c,inject:h,created:d,beforeMount:f,mounted:p,beforeUpdate:m,updated:y,activated:b,deactivated:_,beforeDestroy:w,beforeUnmount:S,destroyed:x,unmounted:C,render:A,renderTracked:E,renderTriggered:P,errorCaptured:k,serverPrefetch:O,expose:T,inheritAttrs:R,components:F,directives:M,filters:N}=e;if(h&&function(t,e,n=r){u(t)&&(t=li(t));for(const r in t){const n=t[r];let i;i=v(n)?"default"in n?yi(n.from||r,n.default,!0):yi(n.from||r):yi(n),Oe(i)?Object.defineProperty(e,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:t=>i.value=t}):e[r]=i}}(h,i,null),s)for(const r in s){const t=s[r];g(t)&&(i[r]=t.bind(n))}if(o){const e=o.call(n,n);v(e)&&(t.data=fe(e))}if(ti=!0,a)for(const u in a){const t=a[u],e=g(t)?t.bind(n,n):g(t.get)?t.get.bind(n,n):r,o=!g(t)&&g(t.set)?t.set.bind(n):r,s=Xo({get:e,set:o});Object.defineProperty(i,u,{enumerable:!0,configurable:!0,get:()=>s.value,set:t=>s.value=t})}if(l)for(const r in l)ri(l[r],i,n,r);if(c){const t=g(c)?c.call(n):c;Reflect.ownKeys(t).forEach((e=>{mi(e,t[e])}))}function L(t,e){u(e)?e.forEach((e=>t(e.bind(n)))):e&&t(e.bind(n))}if(d&&ni(d,t,"c"),L(Tr,f),L(Rr,p),L(Fr,m),L(Mr,y),L(Sr,b),L(xr,_),L(Ur,k),L(jr,E),L(Ir,P),L(Nr,S),L(Lr,C),L(Dr,O),u(T))if(T.length){const e=t.exposed||(t.exposed={});T.forEach((t=>{Object.defineProperty(e,t,{get:()=>n[t],set:e=>n[t]=e})}))}else t.exposed||(t.exposed={});A&&t.render===r&&(t.render=A),null!=R&&(t.inheritAttrs=R),F&&(t.components=F),M&&(t.directives=M)}function ni(t,e,n){$e(u(t)?t.map((t=>t.bind(e.proxy))):t.bind(e.proxy),e,n)}function ri(t,e,n,r){const i=r.includes(".")?Zn(n,r):()=>n[r];if(m(t)){const n=e[t];g(n)&&Xn(i,n)}else if(g(t))Xn(i,t.bind(n));else if(v(t))if(u(t))t.forEach((t=>ri(t,e,n,r)));else{const r=g(t.handler)?t.handler.bind(n):e[t.handler];g(r)&&Xn(i,r,t)}}function ii(t){const e=t.type,{mixins:n,extends:r}=e,{mixins:i,optionsCache:o,config:{optionMergeStrategies:a}}=t.appContext,s=o.get(e);let l;return s?l=s:i.length||n||r?(l={},i.length&&i.forEach((t=>oi(l,t,a,!0))),oi(l,e,a)):l=e,v(e)&&o.set(e,l),l}function oi(t,e,n,r=!1){const{mixins:i,extends:o}=e;o&&oi(t,o,n,!0),i&&i.forEach((e=>oi(t,e,n,!0)));for(const a in e)if(r&&"expose"===a);else{const r=ai[a]||n&&n[a];t[a]=r?r(t[a],e[a]):e[a]}return t}const ai={data:si,props:ui,emits:ui,methods:hi,computed:hi,beforeCreate:ci,created:ci,beforeMount:ci,mounted:ci,beforeUpdate:ci,updated:ci,beforeDestroy:ci,beforeUnmount:ci,destroyed:ci,unmounted:ci,activated:ci,deactivated:ci,errorCaptured:ci,serverPrefetch:ci,components:hi,directives:hi,watch:function(t,e){if(!t)return e;if(!e)return t;const n=s(Object.create(null),t);for(const r in e)n[r]=ci(t[r],e[r]);return n},provide:si,inject:function(t,e){return hi(li(t),li(e))}};function si(t,e){return e?t?function(){return s(g(t)?t.call(this,this):t,g(e)?e.call(this,this):e)}:e:t}function li(t){if(u(t)){const e={};for(let n=0;n<t.length;n++)e[t[n]]=t[n];return e}return t}function ci(t,e){return t?[...new Set([].concat(t,e))]:e}function hi(t,e){return t?s(Object.create(null),t,e):e}function ui(t,e){return t?u(t)&&u(e)?[...new Set([...t,...e])]:s(Object.create(null),Zr(t),Zr(null!=e?e:{})):e}function di(){return{app:null,config:{isNativeTag:i,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let fi=0;function pi(t,e){return function(n,r=null){g(n)||(n=s({},n)),null==r||v(r)||(r=null);const i=di(),o=new WeakSet;let a=!1;const l=i.app={_uid:fi++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:Zo,get config(){return i.config},set config(t){},use:(t,...e)=>(o.has(t)||(t&&g(t.install)?(o.add(t),t.install(l,...e)):g(t)&&(o.add(t),t(l,...e))),l),mixin:t=>(i.mixins.includes(t)||i.mixins.push(t),l),component:(t,e)=>e?(i.components[t]=e,l):i.components[t],directive:(t,e)=>e?(i.directives[t]=e,l):i.directives[t],mount(o,s,c){if(!a){const h=_o(n,r);return h.appContext=i,!0===c?c="svg":!1===c&&(c=void 0),s&&e?e(h,o):t(h,o,c),a=!0,l._container=o,o.__vue_app__=l,qo(h.component)||h.component.proxy}},unmount(){a&&(t(null,l._container),delete l._container.__vue_app__)},provide:(t,e)=>(i.provides[t]=e,l),runWithContext(t){gi=l;try{return t()}finally{gi=null}}};return l}}let gi=null;function mi(t,e){if(No){let n=No.provides;const r=No.parent&&No.parent.provides;r===n&&(n=No.provides=Object.create(r)),n[t]=e}else;}function yi(t,e,n=!1){const r=No||vn;if(r||gi){const i=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:gi._context.provides;if(i&&t in i)return i[t];if(arguments.length>1)return n&&g(e)?e.call(r&&r.proxy):e}}function vi(){return!!(No||vn||gi)}function bi(t,n,r,i){const[o,a]=t.propsOptions;let s,l=!1;if(n)for(let e in n){if(A(e))continue;const c=n[e];let u;o&&h(o,u=k(e))?a&&a.includes(u)?(s||(s={}))[u]=c:r[u]=c:yn(t.emitsOptions,e)||e in i&&c===i[e]||(i[e]=c,l=!0)}if(a){const n=we(r),i=s||e;for(let e=0;e<a.length;e++){const s=a[e];r[s]=_i(o,n,s,i[s],t,!h(i,s))}}return l}function _i(t,e,n,r,i,o){const a=t[n];if(null!=a){const t=h(a,"default");if(t&&void 0===r){const t=a.default;if(a.type!==Function&&!a.skipFactory&&g(t)){const{propsDefaults:o}=i;if(n in o)r=o[n];else{const a=jo(i);r=o[n]=t.call(null,e),a()}}else r=t}a[0]&&(o&&!t?r=!1:!a[1]||""!==r&&r!==T(n)||(r=!0))}return r}function wi(t,r,i=!1){const o=r.propsCache,a=o.get(t);if(a)return a;const l=t.props,c={},d=[];let f=!1;if(!g(t)){const e=t=>{f=!0;const[e,n]=wi(t,r,!0);s(c,e),n&&d.push(...n)};!i&&r.mixins.length&&r.mixins.forEach(e),t.extends&&e(t.extends),t.mixins&&t.mixins.forEach(e)}if(!l&&!f)return v(t)&&o.set(t,n),n;if(u(l))for(let n=0;n<l.length;n++){const t=k(l[n]);Si(t)&&(c[t]=e)}else if(l)for(const e in l){const t=k(e);if(Si(t)){const n=l[e],r=c[t]=u(n)||g(n)?{type:n}:s({},n);if(r){const e=Ai(Boolean,r.type),n=Ai(String,r.type);r[0]=e>-1,r[1]=n<0||e<n,(e>-1||h(r,"default"))&&d.push(t)}}}const p=[c,d];return v(t)&&o.set(t,p),p}function Si(t){return"$"!==t[0]}function xi(t){const e=t&&t.toString().match(/^\s*(function|class) (\w+)/);return e?e[2]:null===t?"null":""}function Ci(t,e){return xi(t)===xi(e)}function Ai(t,e){return u(e)?e.findIndex((e=>Ci(e,t))):g(e)&&Ci(e,t)?0:-1}const Ei=t=>"_"===t[0]||"$stable"===t,Pi=t=>u(t)?t.map(Eo):[Eo(t)],ki=(t,e,n)=>{if(e._n)return e;const r=xn(((...t)=>Pi(e(...t))),n);return r._c=!1,r},Oi=(t,e,n)=>{const r=t._ctx;for(const i in t){if(Ei(i))continue;const n=t[i];if(g(n))e[i]=ki(0,n,r);else if(null!=n){const t=Pi(n);e[i]=()=>t}}},Ti=(t,e)=>{const n=Pi(e);t.slots.default=()=>n},Ri=(t,e)=>{if(32&t.vnode.shapeFlag){const n=e._;n?(t.slots=we(e),L(e,"_",n)):Oi(e,t.slots={})}else t.slots={},e&&Ti(t,e);L(t.slots,mo,1)},Fi=(t,n,r)=>{const{vnode:i,slots:o}=t;let a=!0,l=e;if(32&i.shapeFlag){const t=n._;t?r&&1===t?a=!1:(s(o,n),r||1!==t||delete o._):(a=!n.$stable,Oi(n,o)),l=n}else n&&(Ti(t,n),l={default:1});if(a)for(const e in o)Ei(e)||null!=l[e]||delete o[e]};function Mi(t,n,r,i,o=!1){if(u(t))return void t.forEach(((t,e)=>Mi(t,n&&(u(n)?n[e]:n),r,i,o)));if(mr(i)&&!o)return;const a=4&i.shapeFlag?qo(i.component)||i.component.proxy:i.el,s=o?null:a,{i:c,r:d}=t,f=n&&n.r,p=c.refs===e?c.refs={}:c.refs,y=c.setupState;if(null!=f&&f!==d&&(m(f)?(p[f]=null,h(y,f)&&(y[f]=null)):Oe(f)&&(f.value=null)),g(d))We(d,c,12,[s,p]);else{const e=m(d),n=Oe(d),i=t.f;if(e||n){const c=()=>{if(i){const n=e?h(y,d)?y[d]:p[d]:d.value;o?u(n)&&l(n,a):u(n)?n.includes(a)||n.push(a):e?(p[d]=[a],h(y,d)&&(y[d]=p[d])):(d.value=[a],t.k&&(p[t.k]=d.value))}else e?(p[d]=s,h(y,d)&&(y[d]=s)):n&&(d.value=s,t.k&&(p[t.k]=s))};o||i?c():(c.id=-1,ji(c,r))}}}let Ni=!1;const Li=t=>(t=>t.namespaceURI.includes("svg")&&"foreignObject"!==t.tagName)(t)?"svg":(t=>t.namespaceURI.includes("MathML"))(t)?"mathml":void 0,Di=t=>8===t.nodeType;function Ii(t){const{mt:e,p:n,o:{patchProp:r,createText:i,nextSibling:a,parentNode:s,remove:l,insert:c,createComment:h}}=t,u=(n,r,o,l,h,b=!1)=>{const _=Di(n)&&"["===n.data,w=()=>g(n,r,o,l,h,_),{type:S,ref:x,shapeFlag:C,patchFlag:A}=r;let E=n.nodeType;r.el=n,-2===A&&(b=!1,r.dynamicChildren=null);let P=null;switch(S){case eo:3!==E?""===r.children?(c(r.el=i(""),s(n),n),P=n):P=w():(n.data!==r.children&&(Ni=!0,n.data=r.children),P=a(n));break;case no:v(n)?(P=a(n),y(r.el=n.content.firstChild,n,o)):P=8!==E||_?w():a(n);break;case ro:if(_&&(E=(n=a(n)).nodeType),1===E||3===E){P=n;const t=!r.children.length;for(let e=0;e<r.staticCount;e++)t&&(r.children+=1===P.nodeType?P.outerHTML:P.data),e===r.staticCount-1&&(r.anchor=P),P=a(P);return _?a(P):P}w();break;case to:P=_?p(n,r,o,l,h,b):w();break;default:if(1&C)P=1===E&&r.type.toLowerCase()===n.tagName.toLowerCase()||v(n)?d(n,r,o,l,h,b):w();else if(6&C){r.slotScopeIds=h;const t=s(n);if(P=_?m(n):Di(n)&&"teleport start"===n.data?m(n,n.data,"teleport end"):a(n),e(r,t,null,o,l,Li(t),b),mr(r)){let e;_?(e=_o(to),e.anchor=P?P.previousSibling:t.lastChild):e=3===n.nodeType?xo(""):_o("div"),e.el=n,r.component.subTree=e}}else 64&C?P=8!==E?w():r.type.hydrate(n,r,o,l,h,b,t,f):128&C&&(P=r.type.hydrate(n,r,o,l,Li(s(n)),h,b,t,u))}return null!=x&&Mi(x,null,l,r),P},d=(t,e,n,i,a,s)=>{s=s||!!e.dynamicChildren;const{type:c,props:h,patchFlag:u,shapeFlag:d,dirs:p,transition:g}=e,m="input"===c||"option"===c;if(m||-1!==u){p&&nr(e,null,n,"created");let c,b=!1;if(v(t)){b=zi(i,g)&&n&&n.vnode.props&&n.vnode.props.appear;const r=t.content.firstChild;b&&g.beforeEnter(r),y(r,t,n),e.el=t=r}if(16&d&&(!h||!h.innerHTML&&!h.textContent)){let r=f(t.firstChild,e,t,n,i,a,s);for(;r;){Ni=!0;const t=r;r=r.nextSibling,l(t)}}else 8&d&&t.textContent!==e.children&&(Ni=!0,t.textContent=e.children);if(h)if(m||!s||48&u)for(const e in h)(m&&(e.endsWith("value")||"indeterminate"===e)||o(e)&&!A(e)||"."===e[0])&&r(t,e,null,h[e],void 0,void 0,n);else h.onClick&&r(t,"onClick",null,h.onClick,void 0,void 0,n);(c=h&&h.onVnodeBeforeMount)&&To(c,n,e),p&&nr(e,null,n,"beforeMount"),((c=h&&h.onVnodeMounted)||p||b)&&Vn((()=>{c&&To(c,n,e),b&&g.enter(t),p&&nr(e,null,n,"mounted")}),i)}return t.nextSibling},f=(t,e,r,i,o,a,s)=>{s=s||!!e.dynamicChildren;const l=e.children,c=l.length;for(let h=0;h<c;h++){const e=s?l[h]:l[h]=Eo(l[h]);if(t)t=u(t,e,i,o,a,s);else{if(e.type===eo&&!e.children)continue;Ni=!0,n(null,e,r,null,i,o,Li(r),a)}}return t},p=(t,e,n,r,i,o)=>{const{slotScopeIds:l}=e;l&&(i=i?i.concat(l):l);const u=s(t),d=f(a(t),e,u,n,r,i,o);return d&&Di(d)&&"]"===d.data?a(e.anchor=d):(Ni=!0,c(e.anchor=h("]"),u,d),d)},g=(t,e,r,i,o,c)=>{if(Ni=!0,e.el=null,c){const e=m(t);for(;;){const n=a(t);if(!n||n===e)break;l(n)}}const h=a(t),u=s(t);return l(t),n(null,e,u,h,r,i,Li(u),o),h},m=(t,e="[",n="]")=>{let r=0;for(;t;)if((t=a(t))&&Di(t)&&(t.data===e&&r++,t.data===n)){if(0===r)return a(t);r--}return t},y=(t,e,n)=>{const r=e.parentNode;r&&r.replaceChild(t,e);let i=n;for(;i;)i.vnode.el===e&&(i.vnode.el=i.subTree.el=t),i=i.parent},v=t=>1===t.nodeType&&"template"===t.tagName.toLowerCase();return[(t,e)=>{if(!e.hasChildNodes())return n(null,t,e),cn(),void(e._vnode=t);Ni=!1,u(e.firstChild,t,null,null,null),cn(),e._vnode=t,Ni&&console.error("Hydration completed but contains mismatches.")},u]}const ji=Vn;function Ui(t){return Bi(t)}function Gi(t){return Bi(t,Ii)}function Bi(t,i){U().__VUE__=!0;const{insert:o,remove:a,patchProp:s,createElement:l,createText:c,createComment:u,setText:d,setElementText:f,parentNode:p,nextSibling:g,setScopeId:m=r,insertStaticContent:y}=t,v=(t,e,n,r=null,i=null,o=null,a=void 0,s=null,l=!!e.dynamicChildren)=>{if(t===e)return;t&&!go(t,e)&&(r=Y(t),z(t,i,o,!0),t=null),-2===e.patchFlag&&(l=!1,e.dynamicChildren=null);const{type:c,ref:h,shapeFlag:u}=e;switch(c){case eo:b(t,e,n,r);break;case no:_(t,e,n,r);break;case ro:null==t&&w(e,n,r,a);break;case to:F(t,e,n,r,i,o,a,s,l);break;default:1&u?S(t,e,n,r,i,o,a,s,l):6&u?M(t,e,n,r,i,o,a,s,l):(64&u||128&u)&&c.process(t,e,n,r,i,o,a,s,l,J)}null!=h&&i&&Mi(h,t&&t.ref,o,e||t,!e)},b=(t,e,n,r)=>{if(null==t)o(e.el=c(e.children),n,r);else{const n=e.el=t.el;e.children!==t.children&&d(n,e.children)}},_=(t,e,n,r)=>{null==t?o(e.el=u(e.children||""),n,r):e.el=t.el},w=(t,e,n,r)=>{[t.el,t.anchor]=y(t.children,e,n,r,t.el,t.anchor)},S=(t,e,n,r,i,o,a,s,l)=>{"svg"===e.type?a="svg":"math"===e.type&&(a="mathml"),null==t?x(e,n,r,i,o,a,s,l):P(t,e,i,o,a,s,l)},x=(t,e,n,r,i,a,c,h)=>{let u,d;const{props:p,shapeFlag:g,transition:m,dirs:y}=t;if(u=t.el=l(t.type,a,p&&p.is,p),8&g?f(u,t.children):16&g&&E(t.children,u,null,r,i,Vi(t,a),c,h),y&&nr(t,null,r,"created"),C(u,t,t.scopeId,c,r),p){for(const e in p)"value"===e||A(e)||s(u,e,null,p[e],a,t.children,r,i,q);"value"in p&&s(u,"value",null,p.value,a),(d=p.onVnodeBeforeMount)&&To(d,r,t)}y&&nr(t,null,r,"beforeMount");const v=zi(i,m);v&&m.beforeEnter(u),o(u,e,n),((d=p&&p.onVnodeMounted)||v||y)&&ji((()=>{d&&To(d,r,t),v&&m.enter(u),y&&nr(t,null,r,"mounted")}),i)},C=(t,e,n,r,i)=>{if(n&&m(t,n),r)for(let o=0;o<r.length;o++)m(t,r[o]);if(i){if(e===i.subTree){const e=i.vnode;C(t,e,e.scopeId,e.slotScopeIds,i.parent)}}},E=(t,e,n,r,i,o,a,s,l=0)=>{for(let c=l;c<t.length;c++){const l=t[c]=s?Po(t[c]):Eo(t[c]);v(null,l,e,n,r,i,o,a,s)}},P=(t,n,r,i,o,a,l)=>{const c=n.el=t.el;let{patchFlag:h,dynamicChildren:u,dirs:d}=n;h|=16&t.patchFlag;const p=t.props||e,g=n.props||e;let m;if(r&&Hi(r,!1),(m=g.onVnodeBeforeUpdate)&&To(m,r,n,t),d&&nr(n,t,r,"beforeUpdate"),r&&Hi(r,!0),u?O(t.dynamicChildren,u,c,r,i,Vi(n,o),a):l||G(t,n,c,null,r,i,Vi(n,o),a,!1),h>0){if(16&h)R(c,n,p,g,r,i,o);else if(2&h&&p.class!==g.class&&s(c,"class",null,g.class,o),4&h&&s(c,"style",p.style,g.style,o),8&h){const e=n.dynamicProps;for(let n=0;n<e.length;n++){const a=e[n],l=p[a],h=g[a];h===l&&"value"!==a||s(c,a,l,h,o,t.children,r,i,q)}}1&h&&t.children!==n.children&&f(c,n.children)}else l||null!=u||R(c,n,p,g,r,i,o);((m=g.onVnodeUpdated)||d)&&ji((()=>{m&&To(m,r,n,t),d&&nr(n,t,r,"updated")}),i)},O=(t,e,n,r,i,o,a)=>{for(let s=0;s<e.length;s++){const l=t[s],c=e[s],h=l.el&&(l.type===to||!go(l,c)||70&l.shapeFlag)?p(l.el):n;v(l,c,h,null,r,i,o,a,!0)}},R=(t,n,r,i,o,a,l)=>{if(r!==i){if(r!==e)for(const e in r)A(e)||e in i||s(t,e,r[e],null,l,n.children,o,a,q);for(const e in i){if(A(e))continue;const c=i[e],h=r[e];c!==h&&"value"!==e&&s(t,e,h,c,l,n.children,o,a,q)}"value"in i&&s(t,"value",r.value,i.value,l)}},F=(t,e,n,r,i,a,s,l,h)=>{const u=e.el=t?t.el:c(""),d=e.anchor=t?t.anchor:c("");let{patchFlag:f,dynamicChildren:p,slotScopeIds:g}=e;g&&(l=l?l.concat(g):g),null==t?(o(u,n,r),o(d,n,r),E(e.children||[],n,d,i,a,s,l,h)):f>0&&64&f&&p&&t.dynamicChildren?(O(t.dynamicChildren,p,n,i,a,s,l),(null!=e.key||i&&e===i.subTree)&&Wi(t,e,!0)):G(t,e,n,d,i,a,s,l,h)},M=(t,e,n,r,i,o,a,s,l)=>{e.slotScopeIds=s,null==t?512&e.shapeFlag?i.ctx.activate(e,n,r,a,l):L(e,n,r,i,o,a,l):D(t,e,l)},L=(t,e,n,r,i,o,a)=>{const s=t.component=Mo(t,r,i);if(vr(t)&&(s.ctx.renderer=J),zo(s),s.asyncDep){if(i&&i.registerDep(s,I),!t.el){const t=s.subTree=_o(no);_(null,t,e,n)}}else I(s,t,e,n,i,o,a)},D=(t,e,n)=>{const r=e.component=t.component;if(function(t,e,n){const{props:r,children:i,component:o}=t,{props:a,children:s,patchFlag:l}=e,c=o.emitsOptions;if(e.dirs||e.transition)return!0;if(!(n&&l>=0))return!(!i&&!s||s&&s.$stable)||r!==a&&(r?!a||Pn(r,a,c):!!a);if(1024&l)return!0;if(16&l)return r?Pn(r,a,c):!!a;if(8&l){const t=e.dynamicProps;for(let e=0;e<t.length;e++){const n=t[e];if(a[n]!==r[n]&&!yn(c,n))return!0}}return!1}(t,e,n)){if(r.asyncDep&&!r.asyncResolved)return void j(r,e,n);r.next=e,function(t){const e=Xe.indexOf(t);e>Qe&&Xe.splice(e,1)}(r.update),r.effect.dirty=!0,r.update()}else e.el=t.el,r.vnode=e},I=(t,e,n,i,o,a,s)=>{const l=()=>{if(t.isMounted){let{next:e,bu:n,u:r,parent:i,vnode:c}=t;{const n=$i(t);if(n)return e&&(e.el=c.el,j(t,e,s)),void n.asyncDep.then((()=>{t.isUnmounted||l()}))}let h,u=e;Hi(t,!1),e?(e.el=c.el,j(t,e,s)):e=c,n&&N(n),(h=e.props&&e.props.onVnodeBeforeUpdate)&&To(h,i,e,c),Hi(t,!0);const d=Cn(t),f=t.subTree;t.subTree=d,v(f,d,p(f.el),Y(f),t,o,a),e.el=d.el,null===u&&kn(t,d.el),r&&ji(r,o),(h=e.props&&e.props.onVnodeUpdated)&&ji((()=>To(h,i,e,c)),o)}else{let r;const{el:s,props:l}=e,{bm:c,m:h,parent:u}=t,d=mr(e);if(Hi(t,!1),c&&N(c),!d&&(r=l&&l.onVnodeBeforeMount)&&To(r,u,e),Hi(t,!0),s&&tt){const n=()=>{t.subTree=Cn(t),tt(s,t.subTree,t,o,null)};d?e.type.__asyncLoader().then((()=>!t.isUnmounted&&n())):n()}else{const r=t.subTree=Cn(t);v(null,r,n,i,t,o,a),e.el=r.el}if(h&&ji(h,o),!d&&(r=l&&l.onVnodeMounted)){const t=e;ji((()=>To(r,u,t)),o)}(256&e.shapeFlag||u&&mr(u.vnode)&&256&u.vnode.shapeFlag)&&t.a&&ji(t.a,o),t.isMounted=!0,e=n=i=null}},c=t.effect=new lt(l,r,(()=>on(h)),t.scope),h=t.update=()=>{c.dirty&&c.run()};h.id=t.uid,Hi(t,!0),h()},j=(t,e,n)=>{e.component=t;const r=t.vnode.props;t.vnode=e,t.next=null,function(t,e,n,r){const{props:i,attrs:o,vnode:{patchFlag:a}}=t,s=we(i),[l]=t.propsOptions;let c=!1;if(!(r||a>0)||16&a){let r;bi(t,e,i,o)&&(c=!0);for(const o in s)e&&(h(e,o)||(r=T(o))!==o&&h(e,r))||(l?!n||void 0===n[o]&&void 0===n[r]||(i[o]=_i(l,s,o,void 0,t,!0)):delete i[o]);if(o!==s)for(const t in o)e&&h(e,t)||(delete o[t],c=!0)}else if(8&a){const n=t.vnode.dynamicProps;for(let r=0;r<n.length;r++){let a=n[r];if(yn(t.emitsOptions,a))continue;const u=e[a];if(l)if(h(o,a))u!==o[a]&&(o[a]=u,c=!0);else{const e=k(a);i[e]=_i(l,s,e,u,t,!1)}else u!==o[a]&&(o[a]=u,c=!0)}}c&&kt(t,"set","$attrs")}(t,e.props,r,n),Fi(t,e.children,n),gt(),ln(t),mt()},G=(t,e,n,r,i,o,a,s,l=!1)=>{const c=t&&t.children,h=t?t.shapeFlag:0,u=e.children,{patchFlag:d,shapeFlag:p}=e;if(d>0){if(128&d)return void V(c,u,n,r,i,o,a,s,l);if(256&d)return void B(c,u,n,r,i,o,a,s,l)}8&p?(16&h&&q(c,i,o),u!==c&&f(n,u)):16&h?16&p?V(c,u,n,r,i,o,a,s,l):q(c,i,o,!0):(8&h&&f(n,""),16&p&&E(u,n,r,i,o,a,s,l))},B=(t,e,r,i,o,a,s,l,c)=>{e=e||n;const h=(t=t||n).length,u=e.length,d=Math.min(h,u);let f;for(f=0;f<d;f++){const n=e[f]=c?Po(e[f]):Eo(e[f]);v(t[f],n,r,null,o,a,s,l,c)}h>u?q(t,o,a,!0,!1,d):E(e,r,i,o,a,s,l,c,d)},V=(t,e,r,i,o,a,s,l,c)=>{let h=0;const u=e.length;let d=t.length-1,f=u-1;for(;h<=d&&h<=f;){const n=t[h],i=e[h]=c?Po(e[h]):Eo(e[h]);if(!go(n,i))break;v(n,i,r,null,o,a,s,l,c),h++}for(;h<=d&&h<=f;){const n=t[d],i=e[f]=c?Po(e[f]):Eo(e[f]);if(!go(n,i))break;v(n,i,r,null,o,a,s,l,c),d--,f--}if(h>d){if(h<=f){const t=f+1,n=t<u?e[t].el:i;for(;h<=f;)v(null,e[h]=c?Po(e[h]):Eo(e[h]),r,n,o,a,s,l,c),h++}}else if(h>f)for(;h<=d;)z(t[h],o,a,!0),h++;else{const p=h,g=h,m=new Map;for(h=g;h<=f;h++){const t=e[h]=c?Po(e[h]):Eo(e[h]);null!=t.key&&m.set(t.key,h)}let y,b=0;const _=f-g+1;let w=!1,S=0;const x=new Array(_);for(h=0;h<_;h++)x[h]=0;for(h=p;h<=d;h++){const n=t[h];if(b>=_){z(n,o,a,!0);continue}let i;if(null!=n.key)i=m.get(n.key);else for(y=g;y<=f;y++)if(0===x[y-g]&&go(n,e[y])){i=y;break}void 0===i?z(n,o,a,!0):(x[i-g]=h+1,i>=S?S=i:w=!0,v(n,e[i],r,null,o,a,s,l,c),b++)}const C=w?function(t){const e=t.slice(),n=[0];let r,i,o,a,s;const l=t.length;for(r=0;r<l;r++){const l=t[r];if(0!==l){if(i=n[n.length-1],t[i]<l){e[r]=i,n.push(r);continue}for(o=0,a=n.length-1;o<a;)s=o+a>>1,t[n[s]]<l?o=s+1:a=s;l<t[n[o]]&&(o>0&&(e[r]=n[o-1]),n[o]=r)}}o=n.length,a=n[o-1];for(;o-- >0;)n[o]=a,a=e[a];return n}(x):n;for(y=C.length-1,h=_-1;h>=0;h--){const t=g+h,n=e[t],d=t+1<u?e[t+1].el:i;0===x[h]?v(null,n,r,d,o,a,s,l,c):w&&(y<0||h!==C[y]?H(n,r,d,2):y--)}}},H=(t,e,n,r,i=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:h}=t;if(6&h)return void H(t.component.subTree,e,n,r);if(128&h)return void t.suspense.move(e,n,r);if(64&h)return void s.move(t,e,n,J);if(s===to){o(a,e,n);for(let t=0;t<c.length;t++)H(c[t],e,n,r);return void o(t.anchor,e,n)}if(s===ro)return void(({el:t,anchor:e},n,r)=>{let i;for(;t&&t!==e;)i=g(t),o(t,n,r),t=i;o(e,n,r)})(t,e,n);if(2!==r&&1&h&&l)if(0===r)l.beforeEnter(a),o(a,e,n),ji((()=>l.enter(a)),i);else{const{leave:t,delayLeave:r,afterLeave:i}=l,s=()=>o(a,e,n),c=()=>{t(a,(()=>{s(),i&&i()}))};r?r(a,s,c):c()}else o(a,e,n)},z=(t,e,n,r=!1,i=!1)=>{const{type:o,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:h,patchFlag:u,dirs:d}=t;if(null!=s&&Mi(s,null,n,t,!0),256&h)return void e.ctx.deactivate(t);const f=1&h&&d,p=!mr(t);let g;if(p&&(g=a&&a.onVnodeBeforeUnmount)&&To(g,e,t),6&h)K(t.component,n,r);else{if(128&h)return void t.suspense.unmount(n,r);f&&nr(t,null,e,"beforeUnmount"),64&h?t.type.remove(t,e,n,i,J,r):c&&(o!==to||u>0&&64&u)?q(c,e,n,!1,!0):(o===to&&384&u||!i&&16&h)&&q(l,e,n),r&&W(t)}(p&&(g=a&&a.onVnodeUnmounted)||f)&&ji((()=>{g&&To(g,e,t),f&&nr(t,null,e,"unmounted")}),n)},W=t=>{const{type:e,el:n,anchor:r,transition:i}=t;if(e===to)return void $(n,r);if(e===ro)return void(({el:t,anchor:e})=>{let n;for(;t&&t!==e;)n=g(t),a(t),t=n;a(e)})(t);const o=()=>{a(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&t.shapeFlag&&i&&!i.persisted){const{leave:e,delayLeave:r}=i,a=()=>e(n,o);r?r(t.el,o,a):a()}else o()},$=(t,e)=>{let n;for(;t!==e;)n=g(t),a(t),t=n;a(e)},K=(t,e,n)=>{const{bum:r,scope:i,update:o,subTree:a,um:s}=t;r&&N(r),i.stop(),o&&(o.active=!1,z(a,t,e,n)),s&&ji(s,e),ji((()=>{t.isUnmounted=!0}),e),e&&e.pendingBranch&&!e.isUnmounted&&t.asyncDep&&!t.asyncResolved&&t.suspenseId===e.pendingId&&(e.deps--,0===e.deps&&e.resolve())},q=(t,e,n,r=!1,i=!1,o=0)=>{for(let a=o;a<t.length;a++)z(t[a],e,n,r,i)},Y=t=>6&t.shapeFlag?Y(t.component.subTree):128&t.shapeFlag?t.suspense.next():g(t.anchor||t.el);let X=!1;const Q=(t,e,n)=>{null==t?e._vnode&&z(e._vnode,null,null,!0):v(e._vnode||null,t,e,null,null,null,n),X||(X=!0,ln(),cn(),X=!1),e._vnode=t},J={p:v,um:z,m:H,r:W,mt:L,mc:E,pc:G,pbc:O,n:Y,o:t};let Z,tt;return i&&([Z,tt]=i(J)),{render:Q,hydrate:Z,createApp:pi(Q,Z)}}function Vi({type:t,props:e},n){return"svg"===n&&"foreignObject"===t||"mathml"===n&&"annotation-xml"===t&&e&&e.encoding&&e.encoding.includes("html")?void 0:n}function Hi({effect:t,update:e},n){t.allowRecurse=e.allowRecurse=n}function zi(t,e){return(!t||t&&!t.pendingBranch)&&e&&!e.persisted}function Wi(t,e,n=!1){const r=t.children,i=e.children;if(u(r)&&u(i))for(let o=0;o<r.length;o++){const t=r[o];let e=i[o];1&e.shapeFlag&&!e.dynamicChildren&&((e.patchFlag<=0||32===e.patchFlag)&&(e=i[o]=Po(i[o]),e.el=t.el),n||Wi(t,e)),e.type===eo&&(e.el=t.el)}}function $i(t){const e=t.subTree.component;if(e)return e.asyncDep&&!e.asyncResolved?e:$i(e)}const Ki=t=>t&&(t.disabled||""===t.disabled),qi=t=>"undefined"!=typeof SVGElement&&t instanceof SVGElement,Yi=t=>"function"==typeof MathMLElement&&t instanceof MathMLElement,Xi=(t,e)=>{const n=t&&t.to;if(m(n)){if(e){return e(n)}return null}return n};function Qi(t,e,n,{o:{insert:r},m:i},o=2){0===o&&r(t.targetAnchor,e,n);const{el:a,anchor:s,shapeFlag:l,children:c,props:h}=t,u=2===o;if(u&&r(a,e,n),(!u||Ki(h))&&16&l)for(let d=0;d<c.length;d++)i(c[d],e,n,2);u&&r(s,e,n)}const Ji={name:"Teleport",__isTeleport:!0,process(t,e,n,r,i,o,a,s,l,c){const{mc:h,pc:u,pbc:d,o:{insert:f,querySelector:p,createText:g,createComment:m}}=c,y=Ki(e.props);let{shapeFlag:v,children:b,dynamicChildren:_}=e;if(null==t){const t=e.el=g(""),c=e.anchor=g("");f(t,n,r),f(c,n,r);const u=e.target=Xi(e.props,p),d=e.targetAnchor=g("");u&&(f(d,u),"svg"===a||qi(u)?a="svg":("mathml"===a||Yi(u))&&(a="mathml"));const m=(t,e)=>{16&v&&h(b,t,e,i,o,a,s,l)};y?m(n,c):u&&m(u,d)}else{e.el=t.el;const r=e.anchor=t.anchor,h=e.target=t.target,f=e.targetAnchor=t.targetAnchor,g=Ki(t.props),m=g?n:h,v=g?r:f;if("svg"===a||qi(h)?a="svg":("mathml"===a||Yi(h))&&(a="mathml"),_?(d(t.dynamicChildren,_,m,i,o,a,s),Wi(t,e,!0)):l||u(t,e,m,v,i,o,a,s,!1),y)g?e.props&&t.props&&e.props.to!==t.props.to&&(e.props.to=t.props.to):Qi(e,n,r,c,1);else if((e.props&&e.props.to)!==(t.props&&t.props.to)){const t=e.target=Xi(e.props,p);t&&Qi(e,t,null,c,0)}else g&&Qi(e,h,f,c,1)}Zi(e)},remove(t,e,n,r,{um:i,o:{remove:o}},a){const{shapeFlag:s,children:l,anchor:c,targetAnchor:h,target:u,props:d}=t;if(u&&o(h),a&&o(c),16&s){const t=a||!Ki(d);for(let r=0;r<l.length;r++){const o=l[r];i(o,e,n,t,!!o.dynamicChildren)}}},move:Qi,hydrate:function(t,e,n,r,i,o,{o:{nextSibling:a,parentNode:s,querySelector:l}},c){const h=e.target=Xi(e.props,l);if(h){const l=h._lpa||h.firstChild;if(16&e.shapeFlag)if(Ki(e.props))e.anchor=c(a(t),e,s(t),n,r,i,o),e.targetAnchor=l;else{e.anchor=a(t);let s=l;for(;s;)if(s=a(s),s&&8===s.nodeType&&"teleport anchor"===s.data){e.targetAnchor=s,h._lpa=e.targetAnchor&&a(e.targetAnchor);break}c(l,e,h,n,r,i,o)}Zi(e)}return e.anchor&&a(e.anchor)}};function Zi(t){const e=t.ctx;if(e&&e.ut){let n=t.children[0].el;for(;n&&n!==t.targetAnchor;)1===n.nodeType&&n.setAttribute("data-v-owner",e.uid),n=n.nextSibling;e.ut()}}const to=Symbol.for("v-fgt"),eo=Symbol.for("v-txt"),no=Symbol.for("v-cmt"),ro=Symbol.for("v-stc"),io=[];let oo=null;function ao(t=!1){io.push(oo=t?null:[])}function so(){io.pop(),oo=io[io.length-1]||null}let lo=1;function co(t){lo+=t}function ho(t){return t.dynamicChildren=lo>0?oo||n:null,so(),lo>0&&oo&&oo.push(t),t}function uo(t,e,n,r,i,o){return ho(bo(t,e,n,r,i,o,!0))}function fo(t,e,n,r,i){return ho(_o(t,e,n,r,i,!0))}function po(t){return!!t&&!0===t.__v_isVNode}function go(t,e){return t.type===e.type&&t.key===e.key}const mo="__vInternal",yo=({key:t})=>null!=t?t:null,vo=({ref:t,ref_key:e,ref_for:n})=>("number"==typeof t&&(t=""+t),null!=t?m(t)||Oe(t)||g(t)?{i:vn,r:t,k:e,f:!!n}:t:null);function bo(t,e=null,n=null,r=0,i=null,o=(t===to?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&yo(e),ref:e&&vo(e),scopeId:bn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:vn};return s?(ko(l,n),128&o&&t.normalize(l)):n&&(l.shapeFlag|=m(n)?8:16),lo>0&&!a&&oo&&(l.patchFlag>0||6&o)&&32!==l.patchFlag&&oo.push(l),l}const _o=function(t,e=null,n=null,r=0,i=null,o=!1){t&&t!==Rn||(t=no);if(po(t)){const r=So(t,e,!0);return n&&ko(r,n),lo>0&&!o&&oo&&(6&r.shapeFlag?oo[oo.indexOf(t)]=r:oo.push(r)),r.patchFlag|=-2,r}a=t,g(a)&&"__vccOpts"in a&&(t=t.__vccOpts);var a;if(e){e=wo(e);let{class:t,style:n}=e;t&&!m(t)&&(e.class=$(t)),v(n)&&(_e(n)&&!u(n)&&(n=s({},n)),e.style=B(n))}const l=m(t)?1:Dn(t)?128:(t=>t.__isTeleport)(t)?64:v(t)?4:g(t)?2:0;return bo(t,e,n,r,i,l,o,!0)};function wo(t){return t?_e(t)||mo in t?s({},t):t:null}function So(t,e,n=!1){const{props:r,ref:i,patchFlag:o,children:a}=t,s=e?Oo(r||{},e):r;return{__v_isVNode:!0,__v_skip:!0,type:t.type,props:s,key:s&&yo(s),ref:e&&e.ref?n&&i?u(i)?i.concat(vo(e)):[i,vo(e)]:vo(e):i,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:a,target:t.target,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==to?-1===o?16:16|o:o,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:t.transition,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&So(t.ssContent),ssFallback:t.ssFallback&&So(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce}}function xo(t=" ",e=0){return _o(eo,null,t,e)}function Co(t,e){const n=_o(ro,null,t);return n.staticCount=e,n}function Ao(t="",e=!1){return e?(ao(),fo(no,null,t)):_o(no,null,t)}function Eo(t){return null==t||"boolean"==typeof t?_o(no):u(t)?_o(to,null,t.slice()):"object"==typeof t?Po(t):_o(eo,null,String(t))}function Po(t){return null===t.el&&-1!==t.patchFlag||t.memo?t:So(t)}function ko(t,e){let n=0;const{shapeFlag:r}=t;if(null==e)e=null;else if(u(e))n=16;else if("object"==typeof e){if(65&r){const n=e.default;return void(n&&(n._c&&(n._d=!1),ko(t,n()),n._c&&(n._d=!0)))}{n=32;const r=e._;r||mo in e?3===r&&vn&&(1===vn.slots._?e._=1:(e._=2,t.patchFlag|=1024)):e._ctx=vn}}else g(e)?(e={default:e,_ctx:vn},n=32):(e=String(e),64&r?(n=16,e=[xo(e)]):n=8);t.children=e,t.shapeFlag|=n}function Oo(...t){const e={};for(let n=0;n<t.length;n++){const r=t[n];for(const t in r)if("class"===t)e.class!==r.class&&(e.class=$([e.class,r.class]));else if("style"===t)e.style=B([e.style,r.style]);else if(o(t)){const n=e[t],i=r[t];!i||n===i||u(n)&&n.includes(i)||(e[t]=n?[].concat(n,i):i)}else""!==t&&(e[t]=r[t])}return e}function To(t,e,n,r=null){$e(t,e,7,[n,r])}const Ro=di();let Fo=0;function Mo(t,n,r){const i=t.type,o=(n?n.appContext:t.appContext)||Ro,a={uid:Fo++,vnode:t,type:i,parent:n,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new rt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wi(i,o),emitsOptions:mn(i,o),emit:null,emitted:null,propsDefaults:e,inheritAttrs:i.inheritAttrs,ctx:e,data:e,props:e,attrs:e,slots:e,refs:e,setupState:e,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return a.ctx={_:a},a.root=n?n.root:a,a.emit=gn.bind(null,a),t.ce&&t.ce(a),a}let No=null;const Lo=()=>No||vn;let Do,Io;{const t=U(),e=(e,n)=>{let r;return(r=t[e])||(r=t[e]=[]),r.push(n),t=>{r.length>1?r.forEach((e=>e(t))):r[0](t)}};Do=e("__VUE_INSTANCE_SETTERS__",(t=>No=t)),Io=e("__VUE_SSR_SETTERS__",(t=>Ho=t))}const jo=t=>{const e=No;return Do(t),t.scope.on(),()=>{t.scope.off(),Do(e)}},Uo=()=>{No&&No.scope.off(),Do(null)};function Go(t){return 4&t.vnode.shapeFlag}let Bo,Vo,Ho=!1;function zo(t,e=!1){e&&Io(e);const{props:n,children:r}=t.vnode,i=Go(t);!function(t,e,n,r=!1){const i={},o={};L(o,mo,1),t.propsDefaults=Object.create(null),bi(t,e,i,o);for(const a in t.propsOptions[0])a in i||(i[a]=void 0);n?t.props=r?i:pe(i):t.type.props?t.props=i:t.props=o,t.attrs=o}(t,n,i,e),Ri(t,r);const o=i?function(t,e){const n=t.type;t.accessCache=Object.create(null),t.proxy=Se(new Proxy(t.ctx,qr));const{setup:r}=n;if(r){const n=t.setupContext=r.length>1?Ko(t):null,i=jo(t);gt();const o=We(r,t,0,[t.props,n]);if(mt(),i(),b(o)){if(o.then(Uo,Uo),e)return o.then((n=>{Wo(t,n,e)})).catch((e=>{Ke(e,t,0)}));t.asyncDep=o}else Wo(t,o,e)}else $o(t,e)}(t,e):void 0;return e&&Io(!1),o}function Wo(t,e,n){g(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:v(e)&&(t.setupState=De(e)),$o(t,n)}function $o(t,e,n){const i=t.type;if(!t.render){if(!e&&Bo&&!i.render){const e=i.template||ii(t).template;if(e){const{isCustomElement:n,compilerOptions:r}=t.appContext.config,{delimiters:o,compilerOptions:a}=i,l=s(s({isCustomElement:n,delimiters:o},r),a);i.render=Bo(e,l)}}t.render=i.render||r,Vo&&Vo(t)}{const e=jo(t);gt();try{ei(t)}finally{mt(),e()}}}function Ko(t){const e=e=>{t.exposed=e||{}};return{get attrs(){return function(t){return t.attrsProxy||(t.attrsProxy=new Proxy(t.attrs,{get:(e,n)=>(Pt(t,0,"$attrs"),e[n])}))}(t)},slots:t.slots,emit:t.emit,expose:e}}function qo(t){if(t.exposed)return t.exposeProxy||(t.exposeProxy=new Proxy(De(Se(t.exposed)),{get:(e,n)=>n in e?e[n]:n in $r?$r[n](t):void 0,has:(t,e)=>e in t||e in $r}))}function Yo(t,e=!0){return g(t)?t.displayName||t.name:t.name||e&&t.__name}const Xo=(t,e)=>Ee(t,0,Ho);function Qo(t,e,n){const r=arguments.length;return 2===r?v(e)&&!u(e)?po(e)?_o(t,null,[e]):_o(t,e):_o(t,null,e):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&po(n)&&(n=[n]),_o(t,e,n))}function Jo(t,e){const n=t.memo;if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(M(n[r],e[r]))return!1;return lo>0&&oo&&oo.push(t),!0}const Zo="3.4.15",ta=r,ea=ze,na=fn,ra=function t(e,n){var r,i;if(fn=e,fn)fn.enabled=!0,pn.forEach((({event:t,args:e})=>fn.emit(t,...e))),pn=[];else if("undefined"!=typeof window&&window.HTMLElement&&!(null==(i=null==(r=window.navigator)?void 0:r.userAgent)?void 0:i.includes("jsdom"))){(n.__VUE_DEVTOOLS_HOOK_REPLAY__=n.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push((e=>{t(e,n)})),setTimeout((()=>{fn||(n.__VUE_DEVTOOLS_HOOK_REPLAY__=null,pn=[])}),3e3)}else pn=[]},ia={createComponentInstance:Mo,setupComponent:zo,renderComponentRoot:Cn,setCurrentRenderingInstance:_n,isVNode:po,normalizeVNode:Eo},oa="undefined"!=typeof document?document:null,aa=oa&&oa.createElement("template"),sa={insert:(t,e,n)=>{e.insertBefore(t,n||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,n,r)=>{const i="svg"===e?oa.createElementNS("http://www.w3.org/2000/svg",t):"mathml"===e?oa.createElementNS("http://www.w3.org/1998/Math/MathML",t):oa.createElement(t,n?{is:n}:void 0);return"select"===t&&r&&null!=r.multiple&&i.setAttribute("multiple",r.multiple),i},createText:t=>oa.createTextNode(t),createComment:t=>oa.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>oa.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,n,r,i,o){const a=n?n.previousSibling:e.lastChild;if(i&&(i===o||i.nextSibling))for(;e.insertBefore(i.cloneNode(!0),n),i!==o&&(i=i.nextSibling););else{aa.innerHTML="svg"===r?`<svg>${t}</svg>`:"mathml"===r?`<math>${t}</math>`:t;const i=aa.content;if("svg"===r||"mathml"===r){const t=i.firstChild;for(;t.firstChild;)i.appendChild(t.firstChild);i.removeChild(t)}e.insertBefore(i,n)}return[a?a.nextSibling:e.firstChild,n?n.previousSibling:e.lastChild]}},la="transition",ca="animation",ha=Symbol("_vtc"),ua=(t,{slots:e})=>Qo(lr,ma(t),e);ua.displayName="Transition";const da={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},fa=ua.props=s({},sr,da),pa=(t,e=[])=>{u(t)?t.forEach((t=>t(...e))):t&&t(...e)},ga=t=>!!t&&(u(t)?t.some((t=>t.length>1)):t.length>1);function ma(t){const e={};for(const s in t)s in da||(e[s]=t[s]);if(!1===t.css)return e;const{name:n="v",type:r,duration:i,enterFromClass:o=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:h=a,appearToClass:u=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=t,g=function(t){if(null==t)return null;if(v(t))return[ya(t.enter),ya(t.leave)];{const e=ya(t);return[e,e]}}(i),m=g&&g[0],y=g&&g[1],{onBeforeEnter:b,onEnter:_,onEnterCancelled:w,onLeave:S,onLeaveCancelled:x,onBeforeAppear:C=b,onAppear:A=_,onAppearCancelled:E=w}=e,P=(t,e,n)=>{ba(t,e?u:l),ba(t,e?h:a),n&&n()},k=(t,e)=>{t._isLeaving=!1,ba(t,d),ba(t,p),ba(t,f),e&&e()},O=t=>(e,n)=>{const i=t?A:_,a=()=>P(e,t,n);pa(i,[e,a]),_a((()=>{ba(e,t?c:o),va(e,t?u:l),ga(i)||Sa(e,r,m,a)}))};return s(e,{onBeforeEnter(t){pa(b,[t]),va(t,o),va(t,a)},onBeforeAppear(t){pa(C,[t]),va(t,c),va(t,h)},onEnter:O(!1),onAppear:O(!0),onLeave(t,e){t._isLeaving=!0;const n=()=>k(t,e);va(t,d),Ea(),va(t,f),_a((()=>{t._isLeaving&&(ba(t,d),va(t,p),ga(S)||Sa(t,r,y,n))})),pa(S,[t,n])},onEnterCancelled(t){P(t,!1),pa(w,[t])},onAppearCancelled(t){P(t,!0),pa(E,[t])},onLeaveCancelled(t){k(t),pa(x,[t])}})}function ya(t){return I(t)}function va(t,e){e.split(/\s+/).forEach((e=>e&&t.classList.add(e))),(t[ha]||(t[ha]=new Set)).add(e)}function ba(t,e){e.split(/\s+/).forEach((e=>e&&t.classList.remove(e)));const n=t[ha];n&&(n.delete(e),n.size||(t[ha]=void 0))}function _a(t){requestAnimationFrame((()=>{requestAnimationFrame(t)}))}let wa=0;function Sa(t,e,n,r){const i=t._endId=++wa,o=()=>{i===t._endId&&r()};if(n)return setTimeout(o,n);const{type:a,timeout:s,propCount:l}=xa(t,e);if(!a)return r();const c=a+"end";let h=0;const u=()=>{t.removeEventListener(c,d),o()},d=e=>{e.target===t&&++h>=l&&u()};setTimeout((()=>{h<l&&u()}),s+1),t.addEventListener(c,d)}function xa(t,e){const n=window.getComputedStyle(t),r=t=>(n[t]||"").split(", "),i=r(`${la}Delay`),o=r(`${la}Duration`),a=Ca(i,o),s=r(`${ca}Delay`),l=r(`${ca}Duration`),c=Ca(s,l);let h=null,u=0,d=0;e===la?a>0&&(h=la,u=a,d=o.length):e===ca?c>0&&(h=ca,u=c,d=l.length):(u=Math.max(a,c),h=u>0?a>c?la:ca:null,d=h?h===la?o.length:l.length:0);return{type:h,timeout:u,propCount:d,hasTransform:h===la&&/\b(transform|all)(,|$)/.test(r(`${la}Property`).toString())}}function Ca(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map(((e,n)=>Aa(e)+Aa(t[n]))))}function Aa(t){return"auto"===t?0:1e3*Number(t.slice(0,-1).replace(",","."))}function Ea(){return document.body.offsetHeight}const Pa=Symbol("_vod"),ka={beforeMount(t,{value:e},{transition:n}){t[Pa]="none"===t.style.display?"":t.style.display,n&&e?n.beforeEnter(t):Oa(t,e)},mounted(t,{value:e},{transition:n}){n&&e&&n.enter(t)},updated(t,{value:e,oldValue:n},{transition:r}){!e!=!n&&(r?e?(r.beforeEnter(t),Oa(t,!0),r.enter(t)):r.leave(t,(()=>{Oa(t,!1)})):Oa(t,e))},beforeUnmount(t,{value:e}){Oa(t,e)}};function Oa(t,e){t.style.display=e?t[Pa]:"none"}const Ta=Symbol("");function Ra(t,e){if(128&t.shapeFlag){const n=t.suspense;t=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((()=>{Ra(n.activeBranch,e)}))}for(;t.component;)t=t.component.subTree;if(1&t.shapeFlag&&t.el)Fa(t.el,e);else if(t.type===to)t.children.forEach((t=>Ra(t,e)));else if(t.type===ro){let{el:n,anchor:r}=t;for(;n&&(Fa(n,e),n!==r);)n=n.nextSibling}}function Fa(t,e){if(1===t.nodeType){const n=t.style;let r="";for(const t in e)n.setProperty(`--${t}`,e[t]),r+=`--${t}: ${e[t]};`;n[Ta]=r}}const Ma=/\s*!important$/;function Na(t,e,n){if(u(n))n.forEach((n=>Na(t,e,n)));else if(null==n&&(n=""),e.startsWith("--"))t.setProperty(e,n);else{const r=function(t,e){const n=Da[e];if(n)return n;let r=k(e);if("filter"!==r&&r in t)return Da[e]=r;r=R(r);for(let i=0;i<La.length;i++){const n=La[i]+r;if(n in t)return Da[e]=n}return e}(t,e);Ma.test(n)?t.setProperty(T(r),n.replace(Ma,""),"important"):t[r]=n}}const La=["Webkit","Moz","ms"],Da={};const Ia="http://www.w3.org/1999/xlink";function ja(t,e,n,r){t.addEventListener(e,n,r)}const Ua=Symbol("_vei");function Ga(t,e,n,r,i=null){const o=t[Ua]||(t[Ua]={}),a=o[e];if(r&&a)a.value=r;else{const[n,s]=function(t){let e;if(Ba.test(t)){let n;for(e={};n=t.match(Ba);)t=t.slice(0,t.length-n[0].length),e[n[0].toLowerCase()]=!0}const n=":"===t[2]?t.slice(3):T(t.slice(2));return[n,e]}(e);if(r){const a=o[e]=function(t,e){const n=t=>{if(t._vts){if(t._vts<=n.attached)return}else t._vts=Date.now();$e(function(t,e){if(u(e)){const n=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{n.call(t),t._stopped=!0},e.map((t=>e=>!e._stopped&&t&&t(e)))}return e}(t,n.value),e,5,[t])};return n.value=t,n.attached=za(),n}(r,i);ja(t,n,a,s)}else a&&(!function(t,e,n,r){t.removeEventListener(e,n,r)}(t,n,a,s),o[e]=void 0)}}const Ba=/(?:Once|Passive|Capture)$/;let Va=0;const Ha=Promise.resolve(),za=()=>Va||(Ha.then((()=>Va=0)),Va=Date.now());const Wa=t=>111===t.charCodeAt(0)&&110===t.charCodeAt(1)&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123;
/*! #__NO_SIDE_EFFECTS__ */
function $a(t,e){const n=gr(t);class r extends qa{constructor(t){super(n,t,e)}}return r.def=n,r}
/*! #__NO_SIDE_EFFECTS__ */const Ka="undefined"!=typeof HTMLElement?HTMLElement:class{};class qa extends Ka{constructor(t,e={},n){super(),this._def=t,this._props=e,this._instance=null,this._connected=!1,this._resolved=!1,this._numberProps=null,this._ob=null,this.shadowRoot&&n?n(this._createVNode(),this.shadowRoot):(this.attachShadow({mode:"open"}),this._def.__asyncLoader||this._resolveProps(this._def))}connectedCallback(){this._connected=!0,this._instance||(this._resolved?this._update():this._resolveDef())}disconnectedCallback(){this._connected=!1,this._ob&&(this._ob.disconnect(),this._ob=null),rn((()=>{this._connected||(Os(null,this.shadowRoot),this._instance=null)}))}_resolveDef(){this._resolved=!0;for(let n=0;n<this.attributes.length;n++)this._setAttr(this.attributes[n].name);this._ob=new MutationObserver((t=>{for(const e of t)this._setAttr(e.attributeName)})),this._ob.observe(this,{attributes:!0});const t=(t,e=!1)=>{const{props:n,styles:r}=t;let i;if(n&&!u(n))for(const o in n){const t=n[o];(t===Number||t&&t.type===Number)&&(o in this._props&&(this._props[o]=I(this._props[o])),(i||(i=Object.create(null)))[k(o)]=!0)}this._numberProps=i,e&&this._resolveProps(t),this._applyStyles(r),this._update()},e=this._def.__asyncLoader;e?e().then((e=>t(e,!0))):t(this._def)}_resolveProps(t){const{props:e}=t,n=u(e)?e:Object.keys(e||{});for(const r of Object.keys(this))"_"!==r[0]&&n.includes(r)&&this._setProp(r,this[r],!0,!1);for(const r of n.map(k))Object.defineProperty(this,r,{get(){return this._getProp(r)},set(t){this._setProp(r,t)}})}_setAttr(t){let e=this.getAttribute(t);const n=k(t);this._numberProps&&this._numberProps[n]&&(e=I(e)),this._setProp(n,e,!1)}_getProp(t){return this._props[t]}_setProp(t,e,n=!0,r=!0){e!==this._props[t]&&(this._props[t]=e,r&&this._instance&&this._update(),n&&(!0===e?this.setAttribute(T(t),""):"string"==typeof e||"number"==typeof e?this.setAttribute(T(t),e+""):e||this.removeAttribute(T(t))))}_update(){Os(this._createVNode(),this.shadowRoot)}_createVNode(){const t=_o(this._def,s({},this._props));return this._instance||(t.ce=t=>{this._instance=t,t.isCE=!0;const e=(t,e)=>{this.dispatchEvent(new CustomEvent(t,{detail:e}))};t.emit=(t,...n)=>{e(t,n),T(t)!==t&&e(T(t),n)};let n=this;for(;n=n&&(n.parentNode||n.host);)if(n instanceof qa){t.parent=n._instance,t.provides=n._instance.provides;break}}),t}_applyStyles(t){t&&t.forEach((t=>{const e=document.createElement("style");e.textContent=t,this.shadowRoot.appendChild(e)}))}}const Ya=new WeakMap,Xa=new WeakMap,Qa=Symbol("_moveCb"),Ja=Symbol("_enterCb"),Za={name:"TransitionGroup",props:s({},fa,{tag:String,moveClass:String}),setup(t,{slots:e}){const n=Lo(),r=or();let i,o;return Mr((()=>{if(!i.length)return;const e=t.moveClass||`${t.name||"v"}-move`;if(!function(t,e,n){const r=t.cloneNode(),i=t[ha];i&&i.forEach((t=>{t.split(/\s+/).forEach((t=>t&&r.classList.remove(t)))}));n.split(/\s+/).forEach((t=>t&&r.classList.add(t))),r.style.display="none";const o=1===e.nodeType?e:e.parentNode;o.appendChild(r);const{hasTransform:a}=xa(r);return o.removeChild(r),a}(i[0].el,n.vnode.el,e))return;i.forEach(es),i.forEach(ns);const r=i.filter(rs);Ea(),r.forEach((t=>{const n=t.el,r=n.style;va(n,e),r.transform=r.webkitTransform=r.transitionDuration="";const i=n[Qa]=t=>{t&&t.target!==n||t&&!/transform$/.test(t.propertyName)||(n.removeEventListener("transitionend",i),n[Qa]=null,ba(n,e))};n.addEventListener("transitionend",i)}))})),()=>{const a=we(t),s=ma(a);let l=a.tag||to;i=o,o=e.default?pr(e.default()):[];for(let t=0;t<o.length;t++){const e=o[t];null!=e.key&&fr(e,hr(e,s,r,n))}if(i)for(let t=0;t<i.length;t++){const e=i[t];fr(e,hr(e,s,r,n)),Ya.set(e,e.el.getBoundingClientRect())}return _o(l,null,o)}}},ts=Za;function es(t){const e=t.el;e[Qa]&&e[Qa](),e[Ja]&&e[Ja]()}function ns(t){Xa.set(t,t.el.getBoundingClientRect())}function rs(t){const e=Ya.get(t),n=Xa.get(t),r=e.left-n.left,i=e.top-n.top;if(r||i){const e=t.el.style;return e.transform=e.webkitTransform=`translate(${r}px,${i}px)`,e.transitionDuration="0s",t}}const is=t=>{const e=t.props["onUpdate:modelValue"]||!1;return u(e)?t=>N(e,t):e};function os(t){t.target.composing=!0}function as(t){const e=t.target;e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}const ss=Symbol("_assign"),ls={created(t,{modifiers:{lazy:e,trim:n,number:r}},i){t[ss]=is(i);const o=r||i.props&&"number"===i.props.type;ja(t,e?"change":"input",(e=>{if(e.target.composing)return;let r=t.value;n&&(r=r.trim()),o&&(r=D(r)),t[ss](r)})),n&&ja(t,"change",(()=>{t.value=t.value.trim()})),e||(ja(t,"compositionstart",os),ja(t,"compositionend",as),ja(t,"change",as))},mounted(t,{value:e}){t.value=null==e?"":e},beforeUpdate(t,{value:e,modifiers:{lazy:n,trim:r,number:i}},o){if(t[ss]=is(o),t.composing)return;const a=null==e?"":e;if((i||"number"===t.type?D(t.value):t.value)!==a){if(document.activeElement===t&&"range"!==t.type){if(n)return;if(r&&t.value.trim()===a)return}t.value=a}}},cs={deep:!0,created(t,e,n){t[ss]=is(n),ja(t,"change",(()=>{const e=t._modelValue,n=ps(t),r=t.checked,i=t[ss];if(u(e)){const t=Q(e,n),o=-1!==t;if(r&&!o)i(e.concat(n));else if(!r&&o){const n=[...e];n.splice(t,1),i(n)}}else if(f(e)){const t=new Set(e);r?t.add(n):t.delete(n),i(t)}else i(gs(t,r))}))},mounted:hs,beforeUpdate(t,e,n){t[ss]=is(n),hs(t,e,n)}};function hs(t,{value:e,oldValue:n},r){t._modelValue=e,u(e)?t.checked=Q(e,r.props.value)>-1:f(e)?t.checked=e.has(r.props.value):e!==n&&(t.checked=X(e,gs(t,!0)))}const us={created(t,{value:e},n){t.checked=X(e,n.props.value),t[ss]=is(n),ja(t,"change",(()=>{t[ss](ps(t))}))},beforeUpdate(t,{value:e,oldValue:n},r){t[ss]=is(r),e!==n&&(t.checked=X(e,r.props.value))}},ds={deep:!0,created(t,{value:e,modifiers:{number:n}},r){const i=f(e);ja(t,"change",(()=>{const e=Array.prototype.filter.call(t.options,(t=>t.selected)).map((t=>n?D(ps(t)):ps(t)));t[ss](t.multiple?i?new Set(e):e:e[0]),t._assigning=!0,rn((()=>{t._assigning=!1}))})),t[ss]=is(r)},mounted(t,{value:e,oldValue:n,modifiers:{number:r}}){fs(t,e,n,r)},beforeUpdate(t,e,n){t[ss]=is(n)},updated(t,{value:e,oldValue:n,modifiers:{number:r}}){t._assigning||fs(t,e,n,r)}};function fs(t,e,n,r){const i=t.multiple,o=u(e);if((!i||o||f(e))&&(!o||!X(e,n))){for(let n=0,a=t.options.length;n<a;n++){const a=t.options[n],s=ps(a);if(i)if(o){const t=typeof s;a.selected="string"===t||"number"===t?e.includes(r?D(s):s):Q(e,s)>-1}else a.selected=e.has(s);else if(X(ps(a),e))return void(t.selectedIndex!==n&&(t.selectedIndex=n))}i||-1===t.selectedIndex||(t.selectedIndex=-1)}}function ps(t){return"_value"in t?t._value:t.value}function gs(t,e){const n=e?"_trueValue":"_falseValue";return n in t?t[n]:e}const ms={created(t,e,n){vs(t,e,n,null,"created")},mounted(t,e,n){vs(t,e,n,null,"mounted")},beforeUpdate(t,e,n,r){vs(t,e,n,r,"beforeUpdate")},updated(t,e,n,r){vs(t,e,n,r,"updated")}};function ys(t,e){switch(t){case"SELECT":return ds;case"TEXTAREA":return ls;default:switch(e){case"checkbox":return cs;case"radio":return us;default:return ls}}}function vs(t,e,n,r,i){const o=ys(t.tagName,n.props&&n.props.type)[i];o&&o(t,e,n,r)}const bs=["ctrl","shift","alt","meta"],_s={stop:t=>t.stopPropagation(),prevent:t=>t.preventDefault(),self:t=>t.target!==t.currentTarget,ctrl:t=>!t.ctrlKey,shift:t=>!t.shiftKey,alt:t=>!t.altKey,meta:t=>!t.metaKey,left:t=>"button"in t&&0!==t.button,middle:t=>"button"in t&&1!==t.button,right:t=>"button"in t&&2!==t.button,exact:(t,e)=>bs.some((n=>t[`${n}Key`]&&!e.includes(n)))},ws=(t,e)=>{const n=t._withMods||(t._withMods={}),r=e.join(".");return n[r]||(n[r]=(n,...r)=>{for(let t=0;t<e.length;t++){const r=_s[e[t]];if(r&&r(n,e))return}return t(n,...r)})},Ss={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},xs=(t,e)=>{const n=t._withKeys||(t._withKeys={}),r=e.join(".");return n[r]||(n[r]=n=>{if(!("key"in n))return;const r=T(n.key);return e.some((t=>t===r||Ss[t]===r))?t(n):void 0})},Cs=s({patchProp:(t,e,n,r,i,s,l,c,h)=>{const u="svg"===i;"class"===e?function(t,e,n){const r=t[ha];r&&(e=(e?[e,...r]:[...r]).join(" ")),null==e?t.removeAttribute("class"):n?t.setAttribute("class",e):t.className=e}(t,r,u):"style"===e?function(t,e,n){const r=t.style,i=r.display,o=m(n);if(n&&!o){if(e&&!m(e))for(const t in e)null==n[t]&&Na(r,t,"");for(const t in n)Na(r,t,n[t])}else if(o){if(e!==n){const t=r[Ta];t&&(n+=";"+t),r.cssText=n}}else e&&t.removeAttribute("style");Pa in t&&(r.display=i)}(t,n,r):o(e)?a(e)||Ga(t,e,0,r,l):("."===e[0]?(e=e.slice(1),1):"^"===e[0]?(e=e.slice(1),0):function(t,e,n,r){if(r)return"innerHTML"===e||"textContent"===e||!!(e in t&&Wa(e)&&g(n));if("spellcheck"===e||"draggable"===e||"translate"===e)return!1;if("form"===e)return!1;if("list"===e&&"INPUT"===t.tagName)return!1;if("type"===e&&"TEXTAREA"===t.tagName)return!1;if("width"===e||"height"===e){const e=t.tagName;if("IMG"===e||"VIDEO"===e||"CANVAS"===e||"SOURCE"===e)return!1}if(Wa(e)&&m(n))return!1;return e in t}(t,e,r,u))?function(t,e,n,r,i,o,a){if("innerHTML"===e||"textContent"===e)return r&&a(r,i,o),void(t[e]=null==n?"":n);const s=t.tagName;if("value"===e&&"PROGRESS"!==s&&!s.includes("-")){t._value=n;const r=null==n?"":n;return("OPTION"===s?t.getAttribute("value"):t.value)!==r&&(t.value=r),void(null==n&&t.removeAttribute(e))}let l=!1;if(""===n||null==n){const r=typeof t[e];"boolean"===r?n=Y(n):null==n&&"string"===r?(n="",l=!0):"number"===r&&(n=0,l=!0)}try{t[e]=n}catch(c){}l&&t.removeAttribute(e)}(t,e,r,s,l,c,h):("true-value"===e?t._trueValue=r:"false-value"===e&&(t._falseValue=r),function(t,e,n,r,i){if(r&&e.startsWith("xlink:"))null==n?t.removeAttributeNS(Ia,e.slice(6,e.length)):t.setAttributeNS(Ia,e,n);else{const r=q(e);null==n||r&&!Y(n)?t.removeAttribute(e):t.setAttribute(e,r?"":n)}}(t,e,r,u))}},sa);let As,Es=!1;function Ps(){return As||(As=Ui(Cs))}function ks(){return As=Es?As:Gi(Cs),Es=!0,As}const Os=(...t)=>{Ps().render(...t)},Ts=(...t)=>{ks().hydrate(...t)},Rs=(...t)=>{const e=Ps().createApp(...t),{mount:n}=e;return e.mount=t=>{const r=Ms(t);if(!r)return;const i=e._component;g(i)||i.render||i.template||(i.template=r.innerHTML),r.innerHTML="";const o=n(r,!1,Fs(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},e};function Fs(t){return t instanceof SVGElement?"svg":"function"==typeof MathMLElement&&t instanceof MathMLElement?"mathml":void 0}function Ms(t){if(m(t)){return document.querySelector(t)}return t}let Ns=!1;const Ls=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:lr,BaseTransitionPropsValidators:sr,Comment:no,DeprecationTypes:null,EffectScope:rt,ErrorCodes:{SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER"},ErrorTypeStrings:ea,Fragment:to,KeepAlive:_r,ReactiveEffect:lt,Static:ro,Suspense:jn,Teleport:Ji,Text:eo,TrackOpTypes:{GET:"get",HAS:"has",ITERATE:"iterate"},Transition:ua,TransitionGroup:ts,TriggerOpTypes:{SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},VueElement:qa,assertNumber:function(t,e){},callWithAsyncErrorHandling:$e,callWithErrorHandling:We,camelize:k,capitalize:R,cloneVNode:So,compatUtils:null,compile:()=>{},computed:Xo,createApp:Rs,createBlock:fo,createCommentVNode:Ao,createElementBlock:uo,createElementVNode:bo,createHydrationRenderer:Gi,createPropsRestProxy:function(t,e){const n={};for(const r in t)e.includes(r)||Object.defineProperty(n,r,{enumerable:!0,get:()=>t[r]});return n},createRenderer:Ui,createSSRApp:(...t)=>{const e=ks().createApp(...t),{mount:n}=e;return e.mount=t=>{const e=Ms(t);if(e)return n(e,!0,Fs(e))},e},createSlots:Br,createStaticVNode:Co,createTextVNode:xo,createVNode:_o,customRef:je,defineAsyncComponent:function(t){g(t)&&(t={loader:t});const{loader:e,loadingComponent:n,errorComponent:r,delay:i=200,timeout:o,suspensible:a=!0,onError:s}=t;let l,c=null,h=0;const u=()=>{let t;return c||(t=c=e().catch((t=>{if(t=t instanceof Error?t:new Error(String(t)),s)return new Promise(((e,n)=>{s(t,(()=>e((h++,c=null,u()))),(()=>n(t)),h+1)}));throw t})).then((e=>t!==c&&c?c:(e&&(e.__esModule||"Module"===e[Symbol.toStringTag])&&(e=e.default),l=e,e))))};return gr({name:"AsyncComponentWrapper",__asyncLoader:u,get __asyncResolved(){return l},setup(){const t=No;if(l)return()=>yr(l,t);const e=e=>{c=null,Ke(e,t,13,!r)};if(a&&t.suspense||Ho)return u().then((e=>()=>yr(e,t))).catch((t=>(e(t),()=>r?_o(r,{error:t}):null)));const s=Te(!1),h=Te(),d=Te(!!i);return i&&setTimeout((()=>{d.value=!1}),i),null!=o&&setTimeout((()=>{if(!s.value&&!h.value){const t=new Error(`Async component timed out after ${o}ms.`);e(t),h.value=t}}),o),u().then((()=>{s.value=!0,t.parent&&vr(t.parent.vnode)&&(t.parent.effect.dirty=!0,on(t.parent.update))})).catch((t=>{e(t),h.value=t})),()=>s.value&&l?yr(l,t):h.value&&r?_o(r,{error:h.value}):n&&!d.value?_o(n):void 0}})},defineComponent:gr,defineCustomElement:$a,defineEmits:function(){return null},defineExpose:function(t){},defineModel:function(){},defineOptions:function(t){},defineProps:function(){return null},defineSSRCustomElement:t=>$a(t,Ts),defineSlots:function(){return null},devtools:na,effect:function(t,e){t.effect instanceof lt&&(t=t.effect.fn);const n=new lt(t,r,(()=>{n.dirty&&n.run()}));e&&(s(n,e),e.scope&&ot(n,e.scope)),e&&e.lazy||n.run();const i=n.run.bind(n);return i.effect=n,i},effectScope:it,getCurrentInstance:Lo,getCurrentScope:at,getTransitionRawChildren:pr,guardReactiveProps:wo,h:Qo,handleError:Ke,hasInjectionContext:vi,hydrate:Ts,initCustomFormatter:function(){},initDirectivesForSSR:()=>{Ns||(Ns=!0,ls.getSSRProps=({value:t})=>({value:t}),us.getSSRProps=({value:t},e)=>{if(e.props&&X(e.props.value,t))return{checked:!0}},cs.getSSRProps=({value:t},e)=>{if(u(t)){if(e.props&&Q(t,e.props.value)>-1)return{checked:!0}}else if(f(t)){if(e.props&&t.has(e.props.value))return{checked:!0}}else if(t)return{checked:!0}},ms.getSSRProps=(t,e)=>{if("string"!=typeof e.type)return;const n=ys(e.type.toUpperCase(),e.props&&e.props.type);return n.getSSRProps?n.getSSRProps(t,e):void 0},ka.getSSRProps=({value:t})=>{if(!t)return{style:{display:"none"}}})},inject:yi,isMemoSame:Jo,isProxy:_e,isReactive:ye,isReadonly:ve,isRef:Oe,isRuntimeOnly:()=>!Bo,isShallow:be,isVNode:po,markRaw:Se,mergeDefaults:function(t,e){const n=Zr(t);for(const r in e){if(r.startsWith("__skip"))continue;let t=n[r];t?u(t)||g(t)?t=n[r]={type:t,default:e[r]}:t.default=e[r]:null===t&&(t=n[r]={default:e[r]}),t&&e[`__skip_${r}`]&&(t.skipFactory=!0)}return n},mergeModels:function(t,e){return t&&e?u(t)&&u(e)?t.concat(e):s({},Zr(t),Zr(e)):t||e},mergeProps:Oo,nextTick:rn,normalizeClass:$,normalizeProps:K,normalizeStyle:B,onActivated:Sr,onBeforeMount:Tr,onBeforeUnmount:Nr,onBeforeUpdate:Fr,onDeactivated:xr,onErrorCaptured:Ur,onMounted:Rr,onRenderTracked:jr,onRenderTriggered:Ir,onScopeDispose:st,onServerPrefetch:Dr,onUnmounted:Lr,onUpdated:Mr,openBlock:ao,popScopeId:Sn,provide:mi,proxyRefs:De,pushScopeId:wn,queuePostFlushCb:sn,reactive:fe,readonly:ge,ref:Te,registerRuntimeCompiler:function(t){Bo=t,Vo=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,Yr))}},render:Os,renderList:Gr,renderSlot:Vr,resolveComponent:Tn,resolveDirective:Mn,resolveDynamicComponent:Fn,resolveFilter:null,resolveTransitionHooks:hr,setBlockTracking:co,setDevtoolsHook:ra,setTransitionHooks:fr,shallowReactive:pe,shallowReadonly:function(t){return me(t,!0,Gt,le,de)},shallowRef:Re,ssrContextKey:zn,ssrUtils:ia,stop:function(t){t.effect.stop()},toDisplayString:J,toHandlerKey:F,toHandlers:zr,toRaw:we,toRef:Ve,toRefs:Ue,toValue:function(t){return g(t)?t():Ne(t)},transformVNodeArgs:function(t){},triggerRef:function(t){ke(t,2)},unref:Ne,useAttrs:Qr,useCssModule:function(t="$style"){{const n=Lo();if(!n)return e;const r=n.type.__cssModules;if(!r)return e;const i=r[t];return i||e}},useCssVars:function(t){const e=Lo();if(!e)return;const n=e.ut=(n=t(e.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${e.uid}"]`)).forEach((t=>Fa(t,n)))},r=()=>{const r=t(e.proxy);Ra(e.subTree,r),n(r)};Kn(r),Rr((()=>{const t=new MutationObserver(r);t.observe(e.subTree.el.parentNode,{childList:!0}),Lr((()=>t.disconnect()))}))},useModel:function(t,n,r=e){const i=Lo(),o=k(n),a=T(n),s=je(((e,s)=>{let l;return qn((()=>{const e=t[n];M(l,e)&&(l=e,s())})),{get:()=>(e(),r.get?r.get(l):l),set(t){const e=i.vnode.props;e&&(n in e||o in e||a in e)&&(`onUpdate:${n}`in e||`onUpdate:${o}`in e||`onUpdate:${a}`in e)||!M(t,l)||(l=t,s()),i.emit(`update:${n}`,r.set?r.set(t):t)}}})),l="modelValue"===n?"modelModifiers":`${n}Modifiers`;return s[Symbol.iterator]=()=>{let e=0;return{next:()=>e<2?{value:e++?t[l]||{}:s,done:!1}:{done:!0}}},s},useSSRContext:Wn,useSlots:Xr,useTransitionState:or,vModelCheckbox:cs,vModelDynamic:ms,vModelRadio:us,vModelSelect:ds,vModelText:ls,vShow:ka,version:Zo,warn:ta,watch:Xn,watchEffect:$n,watchPostEffect:Kn,watchSyncEffect:qn,withAsyncContext:function(t){const e=Lo();let n=t();return Uo(),b(n)&&(n=n.catch((t=>{throw jo(e),t}))),[n,()=>jo(e)]},withCtx:xn,withDefaults:function(t,e){return null},withDirectives:er,withKeys:xs,withMemo:function(t,e,n,r){const i=n[r];if(i&&Jo(i,t))return i;const o=e();return o.memo=t.slice(),n[r]=o},withModifiers:ws,withScopeId:t=>xn},Symbol.toStringTag,{value:"Module"}));
/**
* vue v3.4.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ds(t,e){return function(){return t.apply(e,arguments)}}const{toString:Is}=Object.prototype,{getPrototypeOf:js}=Object,Us=(Gs=Object.create(null),t=>{const e=Is.call(t);return Gs[e]||(Gs[e]=e.slice(8,-1).toLowerCase())});var Gs;const Bs=t=>(t=t.toLowerCase(),e=>Us(e)===t),Vs=t=>e=>typeof e===t,{isArray:Hs}=Array,zs=Vs("undefined");const Ws=Bs("ArrayBuffer");const $s=Vs("string"),Ks=Vs("function"),qs=Vs("number"),Ys=t=>null!==t&&"object"==typeof t,Xs=t=>{if("object"!==Us(t))return!1;const e=js(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Symbol.toStringTag in t||Symbol.iterator in t)},Qs=Bs("Date"),Js=Bs("File"),Zs=Bs("Blob"),tl=Bs("FileList"),el=Bs("URLSearchParams");function nl(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,i;if("object"!=typeof t&&(t=[t]),Hs(t))for(r=0,i=t.length;r<i;r++)e.call(null,t[r],r,t);else{const i=n?Object.getOwnPropertyNames(t):Object.keys(t),o=i.length;let a;for(r=0;r<o;r++)a=i[r],e.call(null,t[a],a,t)}}function rl(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,i=n.length;for(;i-- >0;)if(r=n[i],e===r.toLowerCase())return r;return null}const il="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ol=t=>!zs(t)&&t!==il;const al=(sl="undefined"!=typeof Uint8Array&&js(Uint8Array),t=>sl&&t instanceof sl);var sl;const ll=Bs("HTMLFormElement"),cl=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),hl=Bs("RegExp"),ul=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};nl(n,((n,i)=>{let o;!1!==(o=e(n,i,t))&&(r[i]=o||n)})),Object.defineProperties(t,r)},dl="abcdefghijklmnopqrstuvwxyz",fl="0123456789",pl={DIGIT:fl,ALPHA:dl,ALPHA_DIGIT:dl+dl.toUpperCase()+fl};const gl=Bs("AsyncFunction"),ml={isArray:Hs,isArrayBuffer:Ws,isBuffer:function(t){return null!==t&&!zs(t)&&null!==t.constructor&&!zs(t.constructor)&&Ks(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||Ks(t.append)&&("formdata"===(e=Us(t))||"object"===e&&Ks(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&Ws(t.buffer),e},isString:$s,isNumber:qs,isBoolean:t=>!0===t||!1===t,isObject:Ys,isPlainObject:Xs,isUndefined:zs,isDate:Qs,isFile:Js,isBlob:Zs,isRegExp:hl,isFunction:Ks,isStream:t=>Ys(t)&&Ks(t.pipe),isURLSearchParams:el,isTypedArray:al,isFileList:tl,forEach:nl,merge:function t(){const{caseless:e}=ol(this)&&this||{},n={},r=(r,i)=>{const o=e&&rl(n,i)||i;Xs(n[o])&&Xs(r)?n[o]=t(n[o],r):Xs(r)?n[o]=t({},r):Hs(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&nl(arguments[i],r);return n},extend:(t,e,n,{allOwnKeys:r}={})=>(nl(e,((e,r)=>{n&&Ks(e)?t[r]=Ds(e,n):t[r]=e}),{allOwnKeys:r}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,r)=>{let i,o,a;const s={};if(e=e||{},null==t)return e;do{for(i=Object.getOwnPropertyNames(t),o=i.length;o-- >0;)a=i[o],r&&!r(a,t,e)||s[a]||(e[a]=t[a],s[a]=!0);t=!1!==n&&js(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:Us,kindOfTest:Bs,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(Hs(t))return t;let e=t.length;if(!qs(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[Symbol.iterator]).call(t);let r;for(;(r=n.next())&&!r.done;){const n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:ll,hasOwnProperty:cl,hasOwnProp:cl,reduceDescriptors:ul,freezeMethods:t=>{ul(t,((e,n)=>{if(Ks(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];Ks(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach((t=>{n[t]=!0}))};return Hs(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,n){return e.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(t,e)=>(t=+t,Number.isFinite(t)?t:e),findKey:rl,global:il,isContextDefined:ol,ALPHABET:pl,generateString:(t=16,e=pl.ALPHA_DIGIT)=>{let n="";const{length:r}=e;for(;t--;)n+=e[Math.random()*r|0];return n},isSpecCompliantForm:function(t){return!!(t&&Ks(t.append)&&"FormData"===t[Symbol.toStringTag]&&t[Symbol.iterator])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(Ys(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const i=Hs(t)?[]:{};return nl(t,((t,e)=>{const o=n(t,r+1);!zs(o)&&(i[e]=o)})),e[r]=void 0,i}}return t};return n(t,0)},isAsyncFn:gl,isThenable:t=>t&&(Ys(t)||Ks(t))&&Ks(t.then)&&Ks(t.catch)};function yl(t,e,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i)}ml.inherits(yl,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ml.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const vl=yl.prototype,bl={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{bl[t]={value:t}})),Object.defineProperties(yl,bl),Object.defineProperty(vl,"isAxiosError",{value:!0}),yl.from=(t,e,n,r,i,o)=>{const a=Object.create(vl);return ml.toFlatObject(t,a,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),yl.call(a,t.message,e,n,r,i),a.cause=t,a.name=t.name,o&&Object.assign(a,o),a};function _l(t){return ml.isPlainObject(t)||ml.isArray(t)}function wl(t){return ml.endsWith(t,"[]")?t.slice(0,-2):t}function Sl(t,e,n){return t?t.concat(e).map((function(t,e){return t=wl(t),!n&&e?"["+t+"]":t})).join(n?".":""):e}const xl=ml.toFlatObject(ml,{},null,(function(t){return/^is[A-Z]/.test(t)}));function Cl(t,e,n){if(!ml.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=ml.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!ml.isUndefined(e[t])}))).metaTokens,i=n.visitor||c,o=n.dots,a=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ml.isSpecCompliantForm(e);if(!ml.isFunction(i))throw new TypeError("visitor must be a function");function l(t){if(null===t)return"";if(ml.isDate(t))return t.toISOString();if(!s&&ml.isBlob(t))throw new yl("Blob is not supported. Use a Buffer instead.");return ml.isArrayBuffer(t)||ml.isTypedArray(t)?s&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function c(t,n,i){let s=t;if(t&&!i&&"object"==typeof t)if(ml.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(ml.isArray(t)&&function(t){return ml.isArray(t)&&!t.some(_l)}(t)||(ml.isFileList(t)||ml.endsWith(n,"[]"))&&(s=ml.toArray(t)))return n=wl(n),s.forEach((function(t,r){!ml.isUndefined(t)&&null!==t&&e.append(!0===a?Sl([n],r,o):null===a?n:n+"[]",l(t))})),!1;return!!_l(t)||(e.append(Sl(i,n,o),l(t)),!1)}const h=[],u=Object.assign(xl,{defaultVisitor:c,convertValue:l,isVisitable:_l});if(!ml.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!ml.isUndefined(n)){if(-1!==h.indexOf(n))throw Error("Circular reference detected in "+r.join("."));h.push(n),ml.forEach(n,(function(n,o){!0===(!(ml.isUndefined(n)||null===n)&&i.call(e,n,ml.isString(o)?o.trim():o,r,u))&&t(n,r?r.concat(o):[o])})),h.pop()}}(t),e}function Al(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function El(t,e){this._pairs=[],t&&Cl(t,this,e)}const Pl=El.prototype;function kl(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ol(t,e,n){if(!e)return t;const r=n&&n.encode||kl,i=n&&n.serialize;let o;if(o=i?i(e,n):ml.isURLSearchParams(e)?e.toString():new El(e,n).toString(r),o){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}Pl.append=function(t,e){this._pairs.push([t,e])},Pl.toString=function(t){const e=t?function(e){return t.call(this,e,Al)}:Al;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};const Tl=class{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){ml.forEach(this.handlers,(function(e){null!==e&&t(e)}))}},Rl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fl={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:El,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Ml="undefined"!=typeof window&&"undefined"!=typeof document,Nl=(Ll="undefined"!=typeof navigator&&navigator.product,Ml&&["ReactNative","NativeScript","NS"].indexOf(Ll)<0);var Ll;const Dl="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Il={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ml,hasStandardBrowserEnv:Nl,hasStandardBrowserWebWorkerEnv:Dl},Symbol.toStringTag,{value:"Module"})),...Fl};function jl(t){function e(t,n,r,i){let o=t[i++];if("__proto__"===o)return!0;const a=Number.isFinite(+o),s=i>=t.length;if(o=!o&&ml.isArray(r)?r.length:o,s)return ml.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!a;r[o]&&ml.isObject(r[o])||(r[o]=[]);return e(t,n,r[o],i)&&ml.isArray(r[o])&&(r[o]=function(t){const e={},n=Object.keys(t);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],e[o]=t[o];return e}(r[o])),!a}if(ml.isFormData(t)&&ml.isFunction(t.entries)){const n={};return ml.forEachEntry(t,((t,r)=>{e(function(t){return ml.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),r,n,0)})),n}return null}const Ul={transitional:Rl,adapter:["xhr","http"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,i=ml.isObject(t);i&&ml.isHTMLForm(t)&&(t=new FormData(t));if(ml.isFormData(t))return r&&r?JSON.stringify(jl(t)):t;if(ml.isArrayBuffer(t)||ml.isBuffer(t)||ml.isStream(t)||ml.isFile(t)||ml.isBlob(t))return t;if(ml.isArrayBufferView(t))return t.buffer;if(ml.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return Cl(t,new Il.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return Il.isNode&&ml.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((o=ml.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return Cl(o?{"files[]":t}:t,e&&new e,this.formSerializer)}}return i||r?(e.setContentType("application/json",!1),function(t,e,n){if(ml.isString(t))try{return(e||JSON.parse)(t),ml.trim(t)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||Ul.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(t&&ml.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(i){if(n){if("SyntaxError"===i.name)throw yl.from(i,yl.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Il.classes.FormData,Blob:Il.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ml.forEach(["delete","get","head","post","put","patch"],(t=>{Ul.headers[t]={}}));const Gl=Ul,Bl=ml.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Vl=Symbol("internals");function Hl(t){return t&&String(t).trim().toLowerCase()}function zl(t){return!1===t||null==t?t:ml.isArray(t)?t.map(zl):String(t)}function Wl(t,e,n,r,i){return ml.isFunction(r)?r.call(this,e,n):(i&&(e=n),ml.isString(e)?ml.isString(r)?-1!==e.indexOf(r):ml.isRegExp(r)?r.test(e):void 0:void 0)}class $l{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function i(t,e,n){const i=Hl(e);if(!i)throw new Error("header name must be a non-empty string");const o=ml.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||e]=zl(t))}const o=(t,e)=>ml.forEach(t,((t,n)=>i(t,n,e)));return ml.isPlainObject(t)||t instanceof this.constructor?o(t,e):ml.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim())?o((t=>{const e={};let n,r,i;return t&&t.split("\n").forEach((function(t){i=t.indexOf(":"),n=t.substring(0,i).trim().toLowerCase(),r=t.substring(i+1).trim(),!n||e[n]&&Bl[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)})),e})(t),e):null!=t&&i(e,t,n),this}get(t,e){if(t=Hl(t)){const n=ml.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(ml.isFunction(e))return e.call(this,t,n);if(ml.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=Hl(t)){const n=ml.findKey(this,t);return!(!n||void 0===this[n]||e&&!Wl(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function i(t){if(t=Hl(t)){const i=ml.findKey(n,t);!i||e&&!Wl(0,n[i],i,e)||(delete n[i],r=!0)}}return ml.isArray(t)?t.forEach(i):i(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const i=e[n];t&&!Wl(0,this[i],i,t,!0)||(delete this[i],r=!0)}return r}normalize(t){const e=this,n={};return ml.forEach(this,((r,i)=>{const o=ml.findKey(n,i);if(o)return e[o]=zl(r),void delete e[i];const a=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,n)=>e.toUpperCase()+n))}(i):String(i).trim();a!==i&&delete e[i],e[a]=zl(r),n[a]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return ml.forEach(this,((n,r)=>{null!=n&&!1!==n&&(e[r]=t&&ml.isArray(n)?n.join(", "):n)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach((t=>n.set(t))),n}static accessor(t){const e=(this[Vl]=this[Vl]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=Hl(t);e[r]||(!function(t,e){const n=ml.toCamelCase(" "+e);["get","set","has"].forEach((r=>{Object.defineProperty(t,r+n,{value:function(t,n,i){return this[r].call(this,e,t,n,i)},configurable:!0})}))}(n,t),e[r]=!0)}return ml.isArray(t)?t.forEach(r):r(t),this}}$l.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ml.reduceDescriptors($l.prototype,(({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}})),ml.freezeMethods($l);const Kl=$l;function ql(t,e){const n=this||Gl,r=e||n,i=Kl.from(r.headers);let o=r.data;return ml.forEach(t,(function(t){o=t.call(n,o,i.normalize(),e?e.status:void 0)})),i.normalize(),o}function Yl(t){return!(!t||!t.__CANCEL__)}function Xl(t,e,n){yl.call(this,null==t?"canceled":t,yl.ERR_CANCELED,e,n),this.name="CanceledError"}ml.inherits(Xl,yl,{__CANCEL__:!0});const Ql=Il.hasStandardBrowserEnv?{write(t,e,n,r,i,o){const a=[t+"="+encodeURIComponent(e)];ml.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),ml.isString(r)&&a.push("path="+r),ml.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Jl(t,e){return t&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const Zl=Il.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),e=document.createElement("a");let n;function r(n){let r=n;return t&&(e.setAttribute("href",r),r=e.href),e.setAttribute("href",r),{href:e.href,protocol:e.protocol?e.protocol.replace(/:$/,""):"",host:e.host,search:e.search?e.search.replace(/^\?/,""):"",hash:e.hash?e.hash.replace(/^#/,""):"",hostname:e.hostname,port:e.port,pathname:"/"===e.pathname.charAt(0)?e.pathname:"/"+e.pathname}}return n=r(window.location.href),function(t){const e=ml.isString(t)?r(t):t;return e.protocol===n.protocol&&e.host===n.host}}():function(){return!0};function tc(t,e){let n=0;const r=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let i,o=0,a=0;return e=void 0!==e?e:1e3,function(s){const l=Date.now(),c=r[a];i||(i=l),n[o]=s,r[o]=l;let h=a,u=0;for(;h!==o;)u+=n[h++],h%=t;if(o=(o+1)%t,o===a&&(a=(a+1)%t),l-i<e)return;const d=c&&l-c;return d?Math.round(1e3*u/d):void 0}}(50,250);return i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,s=o-n,l=r(s);n=o;const c={loaded:o,total:a,progress:a?o/a:void 0,bytes:s,rate:l||void 0,estimated:l&&a&&o<=a?(a-o)/l:void 0,event:i};c[e?"download":"upload"]=!0,t(c)}}const ec={http:null,xhr:"undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,n){let r=t.data;const i=Kl.from(t.headers).normalize();let o,a,{responseType:s,withXSRFToken:l}=t;function c(){t.cancelToken&&t.cancelToken.unsubscribe(o),t.signal&&t.signal.removeEventListener("abort",o)}if(ml.isFormData(r))if(Il.hasStandardBrowserEnv||Il.hasStandardBrowserWebWorkerEnv)i.setContentType(!1);else if(!1!==(a=i.getContentType())){const[t,...e]=a?a.split(";").map((t=>t.trim())).filter(Boolean):[];i.setContentType([t||"multipart/form-data",...e].join("; "))}let h=new XMLHttpRequest;if(t.auth){const e=t.auth.username||"",n=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";i.set("Authorization","Basic "+btoa(e+":"+n))}const u=Jl(t.baseURL,t.url);function d(){if(!h)return;const r=Kl.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());!function(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new yl("Request failed with status code "+n.status,[yl.ERR_BAD_REQUEST,yl.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}((function(t){e(t),c()}),(function(t){n(t),c()}),{data:s&&"text"!==s&&"json"!==s?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:t,request:h}),h=null}if(h.open(t.method.toUpperCase(),Ol(u,t.params,t.paramsSerializer),!0),h.timeout=t.timeout,"onloadend"in h?h.onloadend=d:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(d)},h.onabort=function(){h&&(n(new yl("Request aborted",yl.ECONNABORTED,t,h)),h=null)},h.onerror=function(){n(new yl("Network Error",yl.ERR_NETWORK,t,h)),h=null},h.ontimeout=function(){let e=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded";const r=t.transitional||Rl;t.timeoutErrorMessage&&(e=t.timeoutErrorMessage),n(new yl(e,r.clarifyTimeoutError?yl.ETIMEDOUT:yl.ECONNABORTED,t,h)),h=null},Il.hasStandardBrowserEnv&&(l&&ml.isFunction(l)&&(l=l(t)),l||!1!==l&&Zl(u))){const e=t.xsrfHeaderName&&t.xsrfCookieName&&Ql.read(t.xsrfCookieName);e&&i.set(t.xsrfHeaderName,e)}void 0===r&&i.setContentType(null),"setRequestHeader"in h&&ml.forEach(i.toJSON(),(function(t,e){h.setRequestHeader(e,t)})),ml.isUndefined(t.withCredentials)||(h.withCredentials=!!t.withCredentials),s&&"json"!==s&&(h.responseType=t.responseType),"function"==typeof t.onDownloadProgress&&h.addEventListener("progress",tc(t.onDownloadProgress,!0)),"function"==typeof t.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",tc(t.onUploadProgress)),(t.cancelToken||t.signal)&&(o=e=>{h&&(n(!e||e.type?new Xl(null,t,h):e),h.abort(),h=null)},t.cancelToken&&t.cancelToken.subscribe(o),t.signal&&(t.signal.aborted?o():t.signal.addEventListener("abort",o)));const f=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(u);f&&-1===Il.protocols.indexOf(f)?n(new yl("Unsupported protocol "+f+":",yl.ERR_BAD_REQUEST,t)):h.send(r||null)}))}};ml.forEach(ec,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(n){}Object.defineProperty(t,"adapterName",{value:e})}}));const nc=t=>`- ${t}`,rc=t=>ml.isFunction(t)||null===t||!1===t,ic=t=>{t=ml.isArray(t)?t:[t];const{length:e}=t;let n,r;const i={};for(let o=0;o<e;o++){let e;if(n=t[o],r=n,!rc(n)&&(r=ec[(e=String(n)).toLowerCase()],void 0===r))throw new yl(`Unknown adapter '${e}'`);if(r)break;i[e||"#"+o]=r}if(!r){const t=Object.entries(i).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new yl("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(nc).join("\n"):" "+nc(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function oc(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Xl(null,t)}function ac(t){oc(t),t.headers=Kl.from(t.headers),t.data=ql.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return ic(t.adapter||Gl.adapter)(t).then((function(e){return oc(t),e.data=ql.call(t,t.transformResponse,e),e.headers=Kl.from(e.headers),e}),(function(e){return Yl(e)||(oc(t),e&&e.response&&(e.response.data=ql.call(t,t.transformResponse,e.response),e.response.headers=Kl.from(e.response.headers))),Promise.reject(e)}))}const sc=t=>t instanceof Kl?t.toJSON():t;function lc(t,e){e=e||{};const n={};function r(t,e,n){return ml.isPlainObject(t)&&ml.isPlainObject(e)?ml.merge.call({caseless:n},t,e):ml.isPlainObject(e)?ml.merge({},e):ml.isArray(e)?e.slice():e}function i(t,e,n){return ml.isUndefined(e)?ml.isUndefined(t)?void 0:r(void 0,t,n):r(t,e,n)}function o(t,e){if(!ml.isUndefined(e))return r(void 0,e)}function a(t,e){return ml.isUndefined(e)?ml.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function s(n,i,o){return o in e?r(n,i):o in t?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(t,e)=>i(sc(t),sc(e),!0)};return ml.forEach(Object.keys(Object.assign({},t,e)),(function(r){const o=l[r]||i,a=o(t[r],e[r],r);ml.isUndefined(a)&&o!==s||(n[r]=a)})),n}const cc="1.6.5",hc={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{hc[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}}));const uc={};hc.transitional=function(t,e,n){function r(t,e){return"[Axios v1.6.5] Transitional option '"+t+"'"+e+(n?". "+n:"")}return(n,i,o)=>{if(!1===t)throw new yl(r(i," has been removed"+(e?" in "+e:"")),yl.ERR_DEPRECATED);return e&&!uc[i]&&(uc[i]=!0,console.warn(r(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(n,i,o)}};const dc={assertOptions:function(t,e,n){if("object"!=typeof t)throw new yl("options must be an object",yl.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let i=r.length;for(;i-- >0;){const o=r[i],a=e[o];if(a){const e=t[o],n=void 0===e||a(e,o,t);if(!0!==n)throw new yl("option "+o+" must be "+n,yl.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new yl("Unknown option "+o,yl.ERR_BAD_OPTION)}},validators:hc},fc=dc.validators;class pc{constructor(t){this.defaults=t,this.interceptors={request:new Tl,response:new Tl}}request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=lc(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:i}=e;void 0!==n&&dc.assertOptions(n,{silentJSONParsing:fc.transitional(fc.boolean),forcedJSONParsing:fc.transitional(fc.boolean),clarifyTimeoutError:fc.transitional(fc.boolean)},!1),null!=r&&(ml.isFunction(r)?e.paramsSerializer={serialize:r}:dc.assertOptions(r,{encode:fc.function,serialize:fc.function},!0)),e.method=(e.method||this.defaults.method||"get").toLowerCase();let o=i&&ml.merge(i.common,i[e.method]);i&&ml.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete i[t]})),e.headers=Kl.concat(o,i);const a=[];let s=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(s=s&&t.synchronous,a.unshift(t.fulfilled,t.rejected))}));const l=[];let c;this.interceptors.response.forEach((function(t){l.push(t.fulfilled,t.rejected)}));let h,u=0;if(!s){const t=[ac.bind(this),void 0];for(t.unshift.apply(t,a),t.push.apply(t,l),h=t.length,c=Promise.resolve(e);u<h;)c=c.then(t[u++],t[u++]);return c}h=a.length;let d=e;for(u=0;u<h;){const t=a[u++],e=a[u++];try{d=t(d)}catch(f){e.call(this,f);break}}try{c=ac.call(this,d)}catch(f){return Promise.reject(f)}for(u=0,h=l.length;u<h;)c=c.then(l[u++],l[u++]);return c}getUri(t){return Ol(Jl((t=lc(this.defaults,t)).baseURL,t.url),t.params,t.paramsSerializer)}}ml.forEach(["delete","get","head","options"],(function(t){pc.prototype[t]=function(e,n){return this.request(lc(n||{},{method:t,url:e,data:(n||{}).data}))}})),ml.forEach(["post","put","patch"],(function(t){function e(e){return function(n,r,i){return this.request(lc(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}pc.prototype[t]=e(),pc.prototype[t+"Form"]=e(!0)}));const gc=pc;class mc{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const n=this;this.promise.then((t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null})),this.promise.then=t=>{let e;const r=new Promise((t=>{n.subscribe(t),e=t})).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t((function(t,r,i){n.reason||(n.reason=new Xl(t,r,i),e(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}static source(){let t;return{token:new mc((function(e){t=e})),cancel:t}}}const yc=mc;const vc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(vc).forEach((([t,e])=>{vc[e]=t}));const bc=vc;const _c=function t(e){const n=new gc(e),r=Ds(gc.prototype.request,n);return ml.extend(r,gc.prototype,n,{allOwnKeys:!0}),ml.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return t(lc(e,n))},r}(Gl);_c.Axios=gc,_c.CanceledError=Xl,_c.CancelToken=yc,_c.isCancel=Yl,_c.VERSION=cc,_c.toFormData=Cl,_c.AxiosError=yl,_c.Cancel=_c.CanceledError,_c.all=function(t){return Promise.all(t)},_c.spread=function(t){return function(e){return t.apply(null,e)}},_c.isAxiosError=function(t){return ml.isObject(t)&&!0===t.isAxiosError},_c.mergeConfig=lc,_c.AxiosHeaders=Kl,_c.formToJSON=t=>jl(ml.isHTMLForm(t)?new FormData(t):t),_c.getAdapter=ic,_c.HttpStatusCode=bc,_c.default=_c;const wc=_c.create({baseURL:"/api",timeout:6e4,headers:{"Access-Control-Allow-Origin":"*"},maxBodyLength:1/0,maxContentLength:1/0});wc.interceptors.request.use((t=>t),(t=>Promise.reject(t))),wc.interceptors.response.use((t=>t),(t=>Promise.reject(t)));var Sc="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function xc(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Cc(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var n=function t(){return this instanceof t?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};n.prototype=e.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,r.get?r:{enumerable:!0,get:function(){return t[e]}})})),n}var Ac,Ec="undefined"!=typeof Symbol&&Symbol,Pc=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),n=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var r=Object.getOwnPropertySymbols(t);if(1!==r.length||r[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(t,e);if(42!==i.value||!0!==i.enumerable)return!1}return!0},kc={foo:{}},Oc=Object,Tc=Object.prototype.toString,Rc=Math.max,Fc=function(t,e){for(var n=[],r=0;r<t.length;r+=1)n[r]=t[r];for(var i=0;i<e.length;i+=1)n[i+t.length]=e[i];return n},Mc=function(t){var e=this;if("function"!=typeof e||"[object Function]"!==Tc.apply(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var n,r=function(t,e){for(var n=[],r=e||0,i=0;r<t.length;r+=1,i+=1)n[i]=t[r];return n}(arguments,1),i=Rc(0,e.length-r.length),o=[],a=0;a<i;a++)o[a]="$"+a;if(n=Function("binder","return function ("+function(t,e){for(var n="",r=0;r<t.length;r+=1)n+=t[r],r+1<t.length&&(n+=e);return n}(o,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof n){var i=e.apply(this,Fc(r,arguments));return Object(i)===i?i:this}return e.apply(t,Fc(r,arguments))})),e.prototype){var s=function(){};s.prototype=e.prototype,n.prototype=new s,s.prototype=null}return n},Nc=Function.prototype.bind||Mc,Lc=Function.prototype.call,Dc=Object.prototype.hasOwnProperty,Ic=Nc.call(Lc,Dc),jc=SyntaxError,Uc=Function,Gc=TypeError,Bc=function(t){try{return Uc('"use strict"; return ('+t+").constructor;")()}catch(e){}},Vc=Object.getOwnPropertyDescriptor;if(Vc)try{Vc({},"")}catch(bv){Vc=null}var Hc=function(){throw new Gc},zc=Vc?function(){try{return Hc}catch(t){try{return Vc(arguments,"callee").get}catch(e){return Hc}}}():Hc,Wc="function"==typeof Ec&&"function"==typeof Symbol&&"symbol"==typeof Ec("foo")&&"symbol"==typeof Symbol("bar")&&Pc(),$c={__proto__:kc}.foo===kc.foo&&!({__proto__:null}instanceof Oc),Kc=Object.getPrototypeOf||($c?function(t){return t.__proto__}:null),qc={},Yc="undefined"!=typeof Uint8Array&&Kc?Kc(Uint8Array):Ac,Xc={"%AggregateError%":"undefined"==typeof AggregateError?Ac:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?Ac:ArrayBuffer,"%ArrayIteratorPrototype%":Wc&&Kc?Kc([][Symbol.iterator]()):Ac,"%AsyncFromSyncIteratorPrototype%":Ac,"%AsyncFunction%":qc,"%AsyncGenerator%":qc,"%AsyncGeneratorFunction%":qc,"%AsyncIteratorPrototype%":qc,"%Atomics%":"undefined"==typeof Atomics?Ac:Atomics,"%BigInt%":"undefined"==typeof BigInt?Ac:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?Ac:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?Ac:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?Ac:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?Ac:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?Ac:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?Ac:FinalizationRegistry,"%Function%":Uc,"%GeneratorFunction%":qc,"%Int8Array%":"undefined"==typeof Int8Array?Ac:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?Ac:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?Ac:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Wc&&Kc?Kc(Kc([][Symbol.iterator]())):Ac,"%JSON%":"object"==typeof JSON?JSON:Ac,"%Map%":"undefined"==typeof Map?Ac:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&Wc&&Kc?Kc((new Map)[Symbol.iterator]()):Ac,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?Ac:Promise,"%Proxy%":"undefined"==typeof Proxy?Ac:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?Ac:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?Ac:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&Wc&&Kc?Kc((new Set)[Symbol.iterator]()):Ac,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Ac:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Wc&&Kc?Kc(""[Symbol.iterator]()):Ac,"%Symbol%":Wc?Symbol:Ac,"%SyntaxError%":jc,"%ThrowTypeError%":zc,"%TypedArray%":Yc,"%TypeError%":Gc,"%Uint8Array%":"undefined"==typeof Uint8Array?Ac:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Ac:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?Ac:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?Ac:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?Ac:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?Ac:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?Ac:WeakSet};if(Kc)try{null.error}catch(bv){var Qc=Kc(Kc(bv));Xc["%Error.prototype%"]=Qc}var Jc=function t(e){var n;if("%AsyncFunction%"===e)n=Bc("async function () {}");else if("%GeneratorFunction%"===e)n=Bc("function* () {}");else if("%AsyncGeneratorFunction%"===e)n=Bc("async function* () {}");else if("%AsyncGenerator%"===e){var r=t("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===e){var i=t("%AsyncGenerator%");i&&Kc&&(n=Kc(i.prototype))}return Xc[e]=n,n},Zc={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},th=Nc,eh=Ic,nh=th.call(Function.call,Array.prototype.concat),rh=th.call(Function.apply,Array.prototype.splice),ih=th.call(Function.call,String.prototype.replace),oh=th.call(Function.call,String.prototype.slice),ah=th.call(Function.call,RegExp.prototype.exec),sh=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,lh=/\\(\\)?/g,ch=function(t,e){var n,r=t;if(eh(Zc,r)&&(r="%"+(n=Zc[r])[0]+"%"),eh(Xc,r)){var i=Xc[r];if(i===qc&&(i=Jc(r)),void 0===i&&!e)throw new Gc("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:i}}throw new jc("intrinsic "+t+" does not exist!")},hh=function(t,e){if("string"!=typeof t||0===t.length)throw new Gc("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new Gc('"allowMissing" argument must be a boolean');if(null===ah(/^%?[^%]*%?$/,t))throw new jc("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=function(t){var e=oh(t,0,1),n=oh(t,-1);if("%"===e&&"%"!==n)throw new jc("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==e)throw new jc("invalid intrinsic syntax, expected opening `%`");var r=[];return ih(t,sh,(function(t,e,n,i){r[r.length]=n?ih(i,lh,"$1"):e||t})),r}(t),r=n.length>0?n[0]:"",i=ch("%"+r+"%",e),o=i.name,a=i.value,s=!1,l=i.alias;l&&(r=l[0],rh(n,nh([0,1],l)));for(var c=1,h=!0;c<n.length;c+=1){var u=n[c],d=oh(u,0,1),f=oh(u,-1);if(('"'===d||"'"===d||"`"===d||'"'===f||"'"===f||"`"===f)&&d!==f)throw new jc("property names with quotes must have matching quotes");if("constructor"!==u&&h||(s=!0),eh(Xc,o="%"+(r+="."+u)+"%"))a=Xc[o];else if(null!=a){if(!(u in a)){if(!e)throw new Gc("base intrinsic for "+t+" exists, but the property is not available.");return}if(Vc&&c+1>=n.length){var p=Vc(a,u);a=(h=!!p)&&"get"in p&&!("originalValue"in p.get)?p.get:a[u]}else h=eh(a,u),a=a[u];h&&!s&&(Xc[o]=a)}}return a},uh={exports:{}},dh=hh("%Object.defineProperty%",!0),fh=function(){if(dh)try{return dh({},"a",{value:1}),!0}catch(bv){return!1}return!1};fh.hasArrayLengthDefineBug=function(){if(!fh())return null;try{return 1!==dh([],"length",{value:1}).length}catch(bv){return!0}};var ph=fh,gh=hh("%Object.getOwnPropertyDescriptor%",!0);if(gh)try{gh([],"length")}catch(bv){gh=null}var mh=gh,yh=ph(),vh=hh,bh=yh&&vh("%Object.defineProperty%",!0);if(bh)try{bh({},"a",{value:1})}catch(bv){bh=!1}var _h=vh("%SyntaxError%"),wh=vh("%TypeError%"),Sh=mh,xh=hh,Ch=function(t,e,n){if(!t||"object"!=typeof t&&"function"!=typeof t)throw new wh("`obj` must be an object or a function`");if("string"!=typeof e&&"symbol"!=typeof e)throw new wh("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new wh("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new wh("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new wh("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new wh("`loose`, if provided, must be a boolean");var r=arguments.length>3?arguments[3]:null,i=arguments.length>4?arguments[4]:null,o=arguments.length>5?arguments[5]:null,a=arguments.length>6&&arguments[6],s=!!Sh&&Sh(t,e);if(bh)bh(t,e,{configurable:null===o&&s?s.configurable:!o,enumerable:null===r&&s?s.enumerable:!r,value:n,writable:null===i&&s?s.writable:!i});else{if(!a&&(r||i||o))throw new _h("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");t[e]=n}},Ah=ph(),Eh=mh,Ph=xh("%TypeError%"),kh=xh("%Math.floor%"),Oh=function(t,e){if("function"!=typeof t)throw new Ph("`fn` is not a function");if("number"!=typeof e||e<0||e>4294967295||kh(e)!==e)throw new Ph("`length` must be a positive 32-bit integer");var n=arguments.length>2&&!!arguments[2],r=!0,i=!0;if("length"in t&&Eh){var o=Eh(t,"length");o&&!o.configurable&&(r=!1),o&&!o.writable&&(i=!1)}return(r||i||!n)&&(Ah?Ch(t,"length",e,!0,!0):Ch(t,"length",e)),t};!function(t){var e=Nc,n=hh,r=Oh,i=n("%TypeError%"),o=n("%Function.prototype.apply%"),a=n("%Function.prototype.call%"),s=n("%Reflect.apply%",!0)||e.call(a,o),l=n("%Object.defineProperty%",!0),c=n("%Math.max%");if(l)try{l({},"a",{value:1})}catch(bv){l=null}t.exports=function(t){if("function"!=typeof t)throw new i("a function is required");var n=s(e,a,arguments);return r(n,1+c(0,t.length-(arguments.length-1)),!0)};var h=function(){return s(e,o,arguments)};l?l(t.exports,"apply",{value:h}):t.exports.apply=h}(uh);var Th=hh,Rh=uh.exports,Fh=Rh(Th("String.prototype.indexOf"));const Mh=Cc(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Nh="function"==typeof Map&&Map.prototype,Lh=Object.getOwnPropertyDescriptor&&Nh?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Dh=Nh&&Lh&&"function"==typeof Lh.get?Lh.get:null,Ih=Nh&&Map.prototype.forEach,jh="function"==typeof Set&&Set.prototype,Uh=Object.getOwnPropertyDescriptor&&jh?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Gh=jh&&Uh&&"function"==typeof Uh.get?Uh.get:null,Bh=jh&&Set.prototype.forEach,Vh="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,Hh="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,zh="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Wh=Boolean.prototype.valueOf,$h=Object.prototype.toString,Kh=Function.prototype.toString,qh=String.prototype.match,Yh=String.prototype.slice,Xh=String.prototype.replace,Qh=String.prototype.toUpperCase,Jh=String.prototype.toLowerCase,Zh=RegExp.prototype.test,tu=Array.prototype.concat,eu=Array.prototype.join,nu=Array.prototype.slice,ru=Math.floor,iu="function"==typeof BigInt?BigInt.prototype.valueOf:null,ou=Object.getOwnPropertySymbols,au="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,su="function"==typeof Symbol&&"object"==typeof Symbol.iterator,lu="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===su||"symbol")?Symbol.toStringTag:null,cu=Object.prototype.propertyIsEnumerable,hu=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function uu(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||Zh.call(/e/,e))return e;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var r=t<0?-ru(-t):ru(t);if(r!==t){var i=String(r),o=Yh.call(e,i.length+1);return Xh.call(i,n,"$&_")+"."+Xh.call(Xh.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Xh.call(e,n,"$&_")}var du=Mh,fu=du.custom,pu=bu(fu)?fu:null;function gu(t,e,n){var r="double"===(n.quoteStyle||e)?'"':"'";return r+t+r}function mu(t){return Xh.call(String(t),/"/g,"&quot;")}function yu(t){return!("[object Array]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}function vu(t){return!("[object RegExp]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}function bu(t){if(su)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!au)return!1;try{return au.call(t),!0}catch(bv){}return!1}var _u=Object.prototype.hasOwnProperty||function(t){return t in this};function wu(t,e){return _u.call(t,e)}function Su(t){return $h.call(t)}function xu(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}function Cu(t,e){if(t.length>e.maxStringLength){var n=t.length-e.maxStringLength,r="... "+n+" more character"+(n>1?"s":"");return Cu(Yh.call(t,0,e.maxStringLength),e)+r}return gu(Xh.call(Xh.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Au),"single",e)}function Au(t){var e=t.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return n?"\\"+n:"\\x"+(e<16?"0":"")+Qh.call(e.toString(16))}function Eu(t){return"Object("+t+")"}function Pu(t){return t+" { ? }"}function ku(t,e,n,r){return t+" ("+e+") {"+(r?Ou(n,r):eu.call(n,", "))+"}"}function Ou(t,e){if(0===t.length)return"";var n="\n"+e.prev+e.base;return n+eu.call(t,","+n)+"\n"+e.prev}function Tu(t,e){var n=yu(t),r=[];if(n){r.length=t.length;for(var i=0;i<t.length;i++)r[i]=wu(t,i)?e(t[i],t):""}var o,a="function"==typeof ou?ou(t):[];if(su){o={};for(var s=0;s<a.length;s++)o["$"+a[s]]=a[s]}for(var l in t)wu(t,l)&&(n&&String(Number(l))===l&&l<t.length||su&&o["$"+l]instanceof Symbol||(Zh.call(/[^\w$]/,l)?r.push(e(l,t)+": "+e(t[l],t)):r.push(l+": "+e(t[l],t))));if("function"==typeof ou)for(var c=0;c<a.length;c++)cu.call(t,a[c])&&r.push("["+e(a[c])+"]: "+e(t[a[c]],t));return r}var Ru=hh,Fu=function(t,e){var n=Th(t,!!e);return"function"==typeof n&&Fh(t,".prototype.")>-1?Rh(n):n},Mu=function t(e,n,r,i){var o=n||{};if(wu(o,"quoteStyle")&&"single"!==o.quoteStyle&&"double"!==o.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(wu(o,"maxStringLength")&&("number"==typeof o.maxStringLength?o.maxStringLength<0&&o.maxStringLength!==1/0:null!==o.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!wu(o,"customInspect")||o.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(wu(o,"indent")&&null!==o.indent&&"\t"!==o.indent&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(wu(o,"numericSeparator")&&"boolean"!=typeof o.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=o.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return Cu(e,o);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var l=String(e);return s?uu(e,l):l}if("bigint"==typeof e){var c=String(e)+"n";return s?uu(e,c):c}var h=void 0===o.depth?5:o.depth;if(void 0===r&&(r=0),r>=h&&h>0&&"object"==typeof e)return yu(e)?"[Array]":"[Object]";var u=function(t,e){var n;if("\t"===t.indent)n="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;n=eu.call(Array(t.indent+1)," ")}return{base:n,prev:eu.call(Array(e+1),n)}}(o,r);if(void 0===i)i=[];else if(xu(i,e)>=0)return"[Circular]";function d(e,n,a){if(n&&(i=nu.call(i)).push(n),a){var s={depth:o.depth};return wu(o,"quoteStyle")&&(s.quoteStyle=o.quoteStyle),t(e,s,r+1,i)}return t(e,o,r+1,i)}if("function"==typeof e&&!vu(e)){var f=function(t){if(t.name)return t.name;var e=qh.call(Kh.call(t),/^function\s*([\w$]+)/);if(e)return e[1];return null}(e),p=Tu(e,d);return"[Function"+(f?": "+f:" (anonymous)")+"]"+(p.length>0?" { "+eu.call(p,", ")+" }":"")}if(bu(e)){var g=su?Xh.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):au.call(e);return"object"!=typeof e||su?g:Eu(g)}if(function(t){if(!t||"object"!=typeof t)return!1;if("undefined"!=typeof HTMLElement&&t instanceof HTMLElement)return!0;return"string"==typeof t.nodeName&&"function"==typeof t.getAttribute}(e)){for(var m="<"+Jh.call(String(e.nodeName)),y=e.attributes||[],v=0;v<y.length;v++)m+=" "+y[v].name+"="+gu(mu(y[v].value),"double",o);return m+=">",e.childNodes&&e.childNodes.length&&(m+="..."),m+="</"+Jh.call(String(e.nodeName))+">"}if(yu(e)){if(0===e.length)return"[]";var b=Tu(e,d);return u&&!function(t){for(var e=0;e<t.length;e++)if(xu(t[e],"\n")>=0)return!1;return!0}(b)?"["+Ou(b,u)+"]":"[ "+eu.call(b,", ")+" ]"}if(function(t){return!("[object Error]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}(e)){var _=Tu(e,d);return"cause"in Error.prototype||!("cause"in e)||cu.call(e,"cause")?0===_.length?"["+String(e)+"]":"{ ["+String(e)+"] "+eu.call(_,", ")+" }":"{ ["+String(e)+"] "+eu.call(tu.call("[cause]: "+d(e.cause),_),", ")+" }"}if("object"==typeof e&&a){if(pu&&"function"==typeof e[pu]&&du)return du(e,{depth:h-r});if("symbol"!==a&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!Dh||!t||"object"!=typeof t)return!1;try{Dh.call(t);try{Gh.call(t)}catch(m){return!0}return t instanceof Map}catch(bv){}return!1}(e)){var w=[];return Ih&&Ih.call(e,(function(t,n){w.push(d(n,e,!0)+" => "+d(t,e))})),ku("Map",Dh.call(e),w,u)}if(function(t){if(!Gh||!t||"object"!=typeof t)return!1;try{Gh.call(t);try{Dh.call(t)}catch(e){return!0}return t instanceof Set}catch(bv){}return!1}(e)){var S=[];return Bh&&Bh.call(e,(function(t){S.push(d(t,e))})),ku("Set",Gh.call(e),S,u)}if(function(t){if(!Vh||!t||"object"!=typeof t)return!1;try{Vh.call(t,Vh);try{Hh.call(t,Hh)}catch(m){return!0}return t instanceof WeakMap}catch(bv){}return!1}(e))return Pu("WeakMap");if(function(t){if(!Hh||!t||"object"!=typeof t)return!1;try{Hh.call(t,Hh);try{Vh.call(t,Vh)}catch(m){return!0}return t instanceof WeakSet}catch(bv){}return!1}(e))return Pu("WeakSet");if(function(t){if(!zh||!t||"object"!=typeof t)return!1;try{return zh.call(t),!0}catch(bv){}return!1}(e))return Pu("WeakRef");if(function(t){return!("[object Number]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}(e))return Eu(d(Number(e)));if(function(t){if(!t||"object"!=typeof t||!iu)return!1;try{return iu.call(t),!0}catch(bv){}return!1}(e))return Eu(d(iu.call(e)));if(function(t){return!("[object Boolean]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}(e))return Eu(Wh.call(e));if(function(t){return!("[object String]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}(e))return Eu(d(String(e)));if("undefined"!=typeof window&&e===window)return"{ [object Window] }";if(e===Sc)return"{ [object globalThis] }";if(!function(t){return!("[object Date]"!==Su(t)||lu&&"object"==typeof t&&lu in t)}(e)&&!vu(e)){var x=Tu(e,d),C=hu?hu(e)===Object.prototype:e instanceof Object||e.constructor===Object,A=e instanceof Object?"":"null prototype",E=!C&&lu&&Object(e)===e&&lu in e?Yh.call(Su(e),8,-1):A?"Object":"",P=(C||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(E||A?"["+eu.call(tu.call([],E||[],A||[]),": ")+"] ":"");return 0===x.length?P+"{}":u?P+"{"+Ou(x,u)+"}":P+"{ "+eu.call(x,", ")+" }"}return String(e)},Nu=Ru("%TypeError%"),Lu=Ru("%WeakMap%",!0),Du=Ru("%Map%",!0),Iu=Fu("WeakMap.prototype.get",!0),ju=Fu("WeakMap.prototype.set",!0),Uu=Fu("WeakMap.prototype.has",!0),Gu=Fu("Map.prototype.get",!0),Bu=Fu("Map.prototype.set",!0),Vu=Fu("Map.prototype.has",!0),Hu=function(t,e){for(var n,r=t;null!==(n=r.next);r=n)if(n.key===e)return r.next=n.next,n.next=t.next,t.next=n,n},zu=String.prototype.replace,Wu=/%20/g,$u="RFC3986",Ku={default:$u,formatters:{RFC1738:function(t){return zu.call(t,Wu,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:$u},qu=Ku,Yu=Object.prototype.hasOwnProperty,Xu=Array.isArray,Qu=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),Ju=function(t,e){for(var n=e&&e.plainObjects?Object.create(null):{},r=0;r<t.length;++r)void 0!==t[r]&&(n[r]=t[r]);return n},Zu={arrayToObject:Ju,assign:function(t,e){return Object.keys(e).reduce((function(t,n){return t[n]=e[n],t}),t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],n=[],r=0;r<e.length;++r)for(var i=e[r],o=i.obj[i.prop],a=Object.keys(o),s=0;s<a.length;++s){var l=a[s],c=o[l];"object"==typeof c&&null!==c&&-1===n.indexOf(c)&&(e.push({obj:o,prop:l}),n.push(c))}return function(t){for(;t.length>1;){var e=t.pop(),n=e.obj[e.prop];if(Xu(n)){for(var r=[],i=0;i<n.length;++i)void 0!==n[i]&&r.push(n[i]);e.obj[e.prop]=r}}}(e),t},decode:function(t,e,n){var r=t.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(bv){return r}},encode:function(t,e,n,r,i){if(0===t.length)return t;var o=t;if("symbol"==typeof t?o=Symbol.prototype.toString.call(t):"string"!=typeof t&&(o=String(t)),"iso-8859-1"===n)return escape(o).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var a="",s=0;s<o.length;++s){var l=o.charCodeAt(s);45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||i===qu.RFC1738&&(40===l||41===l)?a+=o.charAt(s):l<128?a+=Qu[l]:l<2048?a+=Qu[192|l>>6]+Qu[128|63&l]:l<55296||l>=57344?a+=Qu[224|l>>12]+Qu[128|l>>6&63]+Qu[128|63&l]:(s+=1,l=65536+((1023&l)<<10|1023&o.charCodeAt(s)),a+=Qu[240|l>>18]+Qu[128|l>>12&63]+Qu[128|l>>6&63]+Qu[128|63&l])}return a},isBuffer:function(t){return!(!t||"object"!=typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(Xu(t)){for(var n=[],r=0;r<t.length;r+=1)n.push(e(t[r]));return n}return e(t)},merge:function t(e,n,r){if(!n)return e;if("object"!=typeof n){if(Xu(e))e.push(n);else{if(!e||"object"!=typeof e)return[e,n];(r&&(r.plainObjects||r.allowPrototypes)||!Yu.call(Object.prototype,n))&&(e[n]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(n);var i=e;return Xu(e)&&!Xu(n)&&(i=Ju(e,r)),Xu(e)&&Xu(n)?(n.forEach((function(n,i){if(Yu.call(e,i)){var o=e[i];o&&"object"==typeof o&&n&&"object"==typeof n?e[i]=t(o,n,r):e.push(n)}else e[i]=n})),e):Object.keys(n).reduce((function(e,i){var o=n[i];return Yu.call(e,i)?e[i]=t(e[i],o,r):e[i]=o,e}),i)}},td=function(){var t,e,n,r={assert:function(t){if(!r.has(t))throw new Nu("Side channel does not contain "+Mu(t))},get:function(r){if(Lu&&r&&("object"==typeof r||"function"==typeof r)){if(t)return Iu(t,r)}else if(Du){if(e)return Gu(e,r)}else if(n)return function(t,e){var n=Hu(t,e);return n&&n.value}(n,r)},has:function(r){if(Lu&&r&&("object"==typeof r||"function"==typeof r)){if(t)return Uu(t,r)}else if(Du){if(e)return Vu(e,r)}else if(n)return function(t,e){return!!Hu(t,e)}(n,r);return!1},set:function(r,i){Lu&&r&&("object"==typeof r||"function"==typeof r)?(t||(t=new Lu),ju(t,r,i)):Du?(e||(e=new Du),Bu(e,r,i)):(n||(n={key:{},next:null}),function(t,e,n){var r=Hu(t,e);r?r.value=n:t.next={key:e,next:t.next,value:n}}(n,r,i))}};return r},ed=Zu,nd=Ku,rd=Object.prototype.hasOwnProperty,id={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},od=Array.isArray,ad=Array.prototype.push,sd=function(t,e){ad.apply(t,od(e)?e:[e])},ld=Date.prototype.toISOString,cd=nd.default,hd={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:ed.encode,encodeValuesOnly:!1,format:cd,formatter:nd.formatters[cd],indices:!1,serializeDate:function(t){return ld.call(t)},skipNulls:!1,strictNullHandling:!1},ud={},dd=function t(e,n,r,i,o,a,s,l,c,h,u,d,f,p,g,m){for(var y,v=e,b=m,_=0,w=!1;void 0!==(b=b.get(ud))&&!w;){var S=b.get(e);if(_+=1,void 0!==S){if(S===_)throw new RangeError("Cyclic object value");w=!0}void 0===b.get(ud)&&(_=0)}if("function"==typeof l?v=l(n,v):v instanceof Date?v=u(v):"comma"===r&&od(v)&&(v=ed.maybeMap(v,(function(t){return t instanceof Date?u(t):t}))),null===v){if(o)return s&&!p?s(n,hd.encoder,g,"key",d):n;v=""}if("string"==typeof(y=v)||"number"==typeof y||"boolean"==typeof y||"symbol"==typeof y||"bigint"==typeof y||ed.isBuffer(v))return s?[f(p?n:s(n,hd.encoder,g,"key",d))+"="+f(s(v,hd.encoder,g,"value",d))]:[f(n)+"="+f(String(v))];var x,C=[];if(void 0===v)return C;if("comma"===r&&od(v))p&&s&&(v=ed.maybeMap(v,s)),x=[{value:v.length>0?v.join(",")||null:void 0}];else if(od(l))x=l;else{var A=Object.keys(v);x=c?A.sort(c):A}for(var E=i&&od(v)&&1===v.length?n+"[]":n,P=0;P<x.length;++P){var k=x[P],O="object"==typeof k&&void 0!==k.value?k.value:v[k];if(!a||null!==O){var T=od(v)?"function"==typeof r?r(E,k):E:E+(h?"."+k:"["+k+"]");m.set(e,_);var R=td();R.set(ud,m),sd(C,t(O,T,r,i,o,a,"comma"===r&&p&&od(v)?null:s,l,c,h,u,d,f,p,g,R))}}return C},fd=Zu,pd=Object.prototype.hasOwnProperty,gd=Array.isArray,md={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:fd.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},yd=function(t){return t.replace(/&#(\d+);/g,(function(t,e){return String.fromCharCode(parseInt(e,10))}))},vd=function(t,e){return t&&"string"==typeof t&&e.comma&&t.indexOf(",")>-1?t.split(","):t},bd=function(t,e,n,r){if(t){var i=n.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,o=/(\[[^[\]]*])/g,a=n.depth>0&&/(\[[^[\]]*])/.exec(i),s=a?i.slice(0,a.index):i,l=[];if(s){if(!n.plainObjects&&pd.call(Object.prototype,s)&&!n.allowPrototypes)return;l.push(s)}for(var c=0;n.depth>0&&null!==(a=o.exec(i))&&c<n.depth;){if(c+=1,!n.plainObjects&&pd.call(Object.prototype,a[1].slice(1,-1))&&!n.allowPrototypes)return;l.push(a[1])}return a&&l.push("["+i.slice(a.index)+"]"),function(t,e,n,r){for(var i=r?e:vd(e,n),o=t.length-1;o>=0;--o){var a,s=t[o];if("[]"===s&&n.parseArrays)a=[].concat(i);else{a=n.plainObjects?Object.create(null):{};var l="["===s.charAt(0)&&"]"===s.charAt(s.length-1)?s.slice(1,-1):s,c=parseInt(l,10);n.parseArrays||""!==l?!isNaN(c)&&s!==l&&String(c)===l&&c>=0&&n.parseArrays&&c<=n.arrayLimit?(a=[])[c]=i:"__proto__"!==l&&(a[l]=i):a={0:i}}i=a}return i}(l,e,n,r)}};const _d=xc({formats:Ku,parse:function(t,e){var n=function(t){if(!t)return md;if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var e=void 0===t.charset?md.charset:t.charset;return{allowDots:void 0===t.allowDots?md.allowDots:!!t.allowDots,allowPrototypes:"boolean"==typeof t.allowPrototypes?t.allowPrototypes:md.allowPrototypes,allowSparse:"boolean"==typeof t.allowSparse?t.allowSparse:md.allowSparse,arrayLimit:"number"==typeof t.arrayLimit?t.arrayLimit:md.arrayLimit,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:md.charsetSentinel,comma:"boolean"==typeof t.comma?t.comma:md.comma,decoder:"function"==typeof t.decoder?t.decoder:md.decoder,delimiter:"string"==typeof t.delimiter||fd.isRegExp(t.delimiter)?t.delimiter:md.delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:md.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof t.interpretNumericEntities?t.interpretNumericEntities:md.interpretNumericEntities,parameterLimit:"number"==typeof t.parameterLimit?t.parameterLimit:md.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"==typeof t.plainObjects?t.plainObjects:md.plainObjects,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:md.strictNullHandling}}(e);if(""===t||null==t)return n.plainObjects?Object.create(null):{};for(var r="string"==typeof t?function(t,e){var n,r={__proto__:null},i=e.ignoreQueryPrefix?t.replace(/^\?/,""):t,o=e.parameterLimit===1/0?void 0:e.parameterLimit,a=i.split(e.delimiter,o),s=-1,l=e.charset;if(e.charsetSentinel)for(n=0;n<a.length;++n)0===a[n].indexOf("utf8=")&&("utf8=%E2%9C%93"===a[n]?l="utf-8":"utf8=%26%2310003%3B"===a[n]&&(l="iso-8859-1"),s=n,n=a.length);for(n=0;n<a.length;++n)if(n!==s){var c,h,u=a[n],d=u.indexOf("]="),f=-1===d?u.indexOf("="):d+1;-1===f?(c=e.decoder(u,md.decoder,l,"key"),h=e.strictNullHandling?null:""):(c=e.decoder(u.slice(0,f),md.decoder,l,"key"),h=fd.maybeMap(vd(u.slice(f+1),e),(function(t){return e.decoder(t,md.decoder,l,"value")}))),h&&e.interpretNumericEntities&&"iso-8859-1"===l&&(h=yd(h)),u.indexOf("[]=")>-1&&(h=gd(h)?[h]:h),pd.call(r,c)?r[c]=fd.combine(r[c],h):r[c]=h}return r}(t,n):t,i=n.plainObjects?Object.create(null):{},o=Object.keys(r),a=0;a<o.length;++a){var s=o[a],l=bd(s,r[s],n,"string"==typeof t);i=fd.merge(i,l,n)}return!0===n.allowSparse?i:fd.compact(i)},stringify:function(t,e){var n,r=t,i=function(t){if(!t)return hd;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||hd.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=nd.default;if(void 0!==t.format){if(!rd.call(nd.formatters,t.format))throw new TypeError("Unknown format option provided.");n=t.format}var r=nd.formatters[n],i=hd.filter;return("function"==typeof t.filter||od(t.filter))&&(i=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:hd.addQueryPrefix,allowDots:void 0===t.allowDots?hd.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:hd.charsetSentinel,delimiter:void 0===t.delimiter?hd.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:hd.encode,encoder:"function"==typeof t.encoder?t.encoder:hd.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:hd.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:hd.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:hd.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:hd.strictNullHandling}}(e);"function"==typeof i.filter?r=(0,i.filter)("",r):od(i.filter)&&(n=i.filter);var o,a=[];if("object"!=typeof r||null===r)return"";o=e&&e.arrayFormat in id?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var s=id[o];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var l="comma"===s&&e&&e.commaRoundTrip;n||(n=Object.keys(r)),i.sort&&n.sort(i.sort);for(var c=td(),h=0;h<n.length;++h){var u=n[h];i.skipNulls&&null===r[u]||sd(a,dd(r[u],u,s,l,i.strictNullHandling,i.skipNulls,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}var d=a.join(i.delimiter),f=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?f+="utf8=%26%2310003%3B&":f+="utf8=%E2%9C%93&"),d.length>0?f+d:""}}),wd=async t=>await wc.get("/user/loginInfo",{params:t}),Sd=async()=>{window.location.href=`${window.location.origin}/logout`},xd=async t=>(await wc.get("/user/check",{params:t})).data,Cd=async t=>(await wc.post("/user/poll",_d.stringify(t))).data;
/*!
 * pinia v2.1.7
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */
let Ad;const Ed=t=>Ad=t,Pd=Symbol();function kd(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var Od,Td;(Td=Od||(Od={})).direct="direct",Td.patchObject="patch object",Td.patchFunction="patch function";const Rd=()=>{};function Fd(t,e,n,r=Rd){t.push(e);const i=()=>{const n=t.indexOf(e);n>-1&&(t.splice(n,1),r())};return!n&&at()&&st(i),i}function Md(t,...e){t.slice().forEach((t=>{t(...e)}))}const Nd=t=>t();function Ld(t,e){t instanceof Map&&e instanceof Map&&e.forEach(((e,n)=>t.set(n,e))),t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const n in e){if(!e.hasOwnProperty(n))continue;const r=e[n],i=t[n];kd(i)&&kd(r)&&t.hasOwnProperty(n)&&!Oe(r)&&!ye(r)?t[n]=Ld(i,r):t[n]=r}return t}const Dd=Symbol();const{assign:Id}=Object;function jd(t,e,n={},r,i,o){let a;const s=Id({actions:{}},n),l={deep:!0};let c,h,u,d=[],f=[];const p=r.state.value[t];let g;function m(e){let n;c=h=!1,"function"==typeof e?(e(r.state.value[t]),n={type:Od.patchFunction,storeId:t,events:u}):(Ld(r.state.value[t],e),n={type:Od.patchObject,payload:e,storeId:t,events:u});const i=g=Symbol();rn().then((()=>{g===i&&(c=!0)})),h=!0,Md(d,n,r.state.value[t])}o||p||(r.state.value[t]={}),Te({});const y=o?function(){const{state:t}=n,e=t?t():{};this.$patch((t=>{Id(t,e)}))}:Rd;function v(e,n){return function(){Ed(r);const i=Array.from(arguments),o=[],a=[];let s;Md(f,{args:i,name:e,store:b,after:function(t){o.push(t)},onError:function(t){a.push(t)}});try{s=n.apply(this&&this.$id===t?this:b,i)}catch(l){throw Md(a,l),l}return s instanceof Promise?s.then((t=>(Md(o,t),t))).catch((t=>(Md(a,t),Promise.reject(t)))):(Md(o,s),s)}}const b=fe({_p:r,$id:t,$onAction:Fd.bind(null,f),$patch:m,$reset:y,$subscribe(e,n={}){const i=Fd(d,e,n.detached,(()=>o())),o=a.run((()=>Xn((()=>r.state.value[t]),(r=>{("sync"===n.flush?h:c)&&e({storeId:t,type:Od.direct,events:u},r)}),Id({},l,n))));return i},$dispose:function(){a.stop(),d=[],f=[],r._s.delete(t)}});r._s.set(t,b);const _=(r._a&&r._a.runWithContext||Nd)((()=>r._e.run((()=>(a=it()).run(e)))));for(const x in _){const e=_[x];if(Oe(e)&&(!Oe(S=e)||!S.effect)||ye(e))o||(!p||kd(w=e)&&w.hasOwnProperty(Dd)||(Oe(e)?e.value=p[x]:Ld(e,p[x])),r.state.value[t][x]=e);else if("function"==typeof e){const t=v(x,e);_[x]=t,s.actions[x]=e}}var w,S;return Id(b,_),Id(we(b),_),Object.defineProperty(b,"$state",{get:()=>r.state.value[t],set:t=>{m((e=>{Id(e,t)}))}}),r._p.forEach((t=>{Id(b,a.run((()=>t({store:b,app:r._a,pinia:r,options:s}))))})),p&&o&&n.hydrate&&n.hydrate(b.$state,p),c=!0,h=!0,b}function Ud(t,e,n){let r,i;const o="function"==typeof e;function a(t,n){const a=vi();(t=t||(a?yi(Pd,null):null))&&Ed(t),(t=Ad)._s.has(r)||(o?jd(r,e,i,t):function(t,e,n,r){const{state:i,actions:o,getters:a}=e,s=n.state.value[t];let l;l=jd(t,(function(){s||(n.state.value[t]=i?i():{});const e=Ue(n.state.value[t]);return Id(e,o,Object.keys(a||{}).reduce(((e,r)=>(e[r]=Se(Xo((()=>{Ed(n);const e=n._s.get(t);return a[r].call(e,e)}))),e)),{}))}),e,n,0,!0)}(r,i,t));return t._s.get(r)}return"string"==typeof t?(r=t,i=o?n:e):(i=t,r=t.id),a.$id=r,a}const Gd=Ud("userInfoStore",(()=>{const t=Te(""),e=Te(""),n=Te(""),r=Te(""),i=Te(""),o=Te({}),a=Te("");return{syUserName:t,syUid:n,ssoId:r,syData:o,url:a,name:e,updateInfo:function(s){t.value=s.syUserName,n.value=s.syUid,r.value=s.ssoId,i.value=s.type,o.value=s.syData,a.value=s.url,e.value=s.name},clearInfo:function(){t.value="",n.value="",r.value="",i.value="",o.value={},a.value="",e.value=""}}})),Bd=Ud("themeStore",(()=>{const t=Te(!localStorage.getItem("theme")||"light"!=localStorage.getItem("theme"));return{themeShow:t,updateThemeValue:function(e){t.value=e}}})),Vd=Ud("materialStore",(()=>{const t=Te(""),e=Te([]),n=Te([]),r=Te(""),i=Te([]),o=Te("");return{appId:t,versionId:e,tagsId:n,source:r,ids:i,categoryId:o,updateCategoryId:e=>{o.value=e,t.value=""},updateMaterialInfo:r=>{i.value=[],t.value=r._id,e.value=[],n.value=[]},getMaterialData:()=>({appId:t.value,versionId:e.value,tagsId:n.value,categoryId:o.value}),updateVersionInfo:t=>{e.value=t},updateTagsInfo:t=>{n.value=t}}})),Hd=Ud("sketchStore",(()=>{const t=fe({zoom:1,unit:"px",scale:1,artboardIndex:void 0,colorFormat:"color-hex",current:{},codeType:"css",unitName:"",targetIndex:0}),e=Te(!1),n=Te(!1),r=Te(!1),i=Te({}),o=Te({}),a=Xo((()=>t.current.layers?t.current.layers[t.selectedIndex]:null));return Xn((()=>e.value),(e=>{e&&(t.selectedIndex=void 0,n.value=!1,r.value=!1)})),Xn((()=>n.value),(n=>{n&&(t.selectedIndex=void 0,e.value=!1,r.value=!1)})),Xn((()=>r.value),(r=>{r&&(t.selectedIndex=void 0,e.value=!1,n.value=!1)})),{state:t,project:i,group:o,curLayer:a,slicesVisible:e,colorsVisible:n,historyVisible:r,initState:a=>{t.scale=1,t.colorFormat=a.colorFormat,t.unit=a.unit,t.unitName="",t.current=a.artboards[0],t.selectedIndex=void 0,i.value=a,o.value=a.group,e.value=!1,n.value=!1,r.value=!1}}})),zd=async t=>(await wc.get("/smb/team/list",{params:t})).data,Wd=async t=>(await wc.get("/smb/team/add",{params:t})).data,$d=async t=>(await wc.get("/smb/team/update",{params:t})).data,Kd=async t=>(await wc.post("/smb/sketch/add",_d.stringify(t))).data,qd=async t=>(await wc.get("/smb/project/list",{params:t})).data,Yd=async t=>(await wc.get("/smb/project/add",{params:t})).data,Xd=async t=>(await wc.get("/smb/group/list",{params:t})).data,Qd=async t=>(await wc.get("/smb/group/add",{params:t})).data,Jd=async t=>(await wc.get("/smb/group/delete",{params:t})).data,Zd=async t=>(await wc.get("/smb/group/update",{params:t})).data,tf=async t=>(await wc.get("/smb/group/tree",{params:t})).data,ef=async t=>(await wc.get("/smb/sketch/detail",{params:t})).data,nf=async t=>(await wc.get("/smb/sketch/delete",{params:t})).data,rf=async t=>(await wc.get("/smb/sketch/update",{params:t})).data,of=async t=>(await wc.get("/smb/project/detail",{params:t})).data,af=async t=>(await wc.get("/smb/sketch/history",{params:t})).data,sf=async t=>(await wc.post("/smb/sketch/setPosition",_d.stringify(t))).data,lf=async t=>(await wc.get("/smb/sketch/getSketchGroupList",{params:t})).data,cf=async t=>(await wc.get("/smb/group/move",{params:t})).data,hf=async t=>(await wc.get("/smb/sketch/move",{params:t})).data,uf=async t=>(await wc.get("/smb/team/tree",{params:t})).data,df=async t=>(await wc.get("/smb/folder/add",{params:t})).data,ff=async t=>(await wc.post("smb/folder/update",t)).data,pf=async t=>(await wc.post("smb/project/update",t)).data,gf=async t=>(await wc.post("/smb/sketch/sendStatus",t)).data,mf=async t=>(await wc.get("/smb/team/getMembers",{params:t})).data,yf=async t=>(await wc.get("/search/info",{params:t})).data,vf=Ud("smbStore",(()=>{const t=Te([]),e=async()=>{const e=await zd({});t.value=e.data};return{teamList:t,init:async()=>{if(!t.value.length)return e()},refreshTeamList:e}})),bf=gr({__name:"app",setup(t){const e=Te(!1),n=Bd(),r=Gd();Rr((async()=>{const t=await wd({});if(200==t.status&&0==t.data.code){const e=t.data.data;r.updateInfo({syUserName:e.syUserName,syUid:e.syUid,ssoId:e.ssoId,type:e.type,syData:e.syData,url:e.url,name:e.name})}e.value=!0}));return mi("reload",(()=>{e.value=!1,rn((()=>{e.value=!0}))})),(t,r)=>{const i=Tn("RouterView");return ao(),uo("div",{class:$({"sp-hl":Ne(n).themeShow})},[e.value?(ao(),fo(i,{key:0})):Ao("",!0)],2)}}}),_f="undefined"!=typeof window;const wf=Object.assign;function Sf(t,e){const n={};for(const r in e){const i=e[r];n[r]=Cf(i)?i.map(t):t(i)}return n}const xf=()=>{},Cf=Array.isArray,Af=/\/$/,Ef=t=>t.replace(Af,"");function Pf(t,e,n="/"){let r,i={},o="",a="";const s=e.indexOf("#");let l=e.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(r=e.slice(0,l),o=e.slice(l+1,s>-1?s:e.length),i=t(o)),s>-1&&(r=r||e.slice(0,s),a=e.slice(s,e.length)),r=function(t,e){if(t.startsWith("/"))return t;if(!t)return e;const n=e.split("/"),r=t.split("/"),i=r[r.length-1];".."!==i&&"."!==i||r.push("");let o,a,s=n.length-1;for(o=0;o<r.length;o++)if(a=r[o],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}(null!=r?r:e,n),{fullPath:r+(o&&"?")+o+a,path:r,query:i,hash:a}}function kf(t,e){return e&&t.toLowerCase().startsWith(e.toLowerCase())?t.slice(e.length)||"/":t}function Of(t,e){return(t.aliasOf||t)===(e.aliasOf||e)}function Tf(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(!Rf(t[n],e[n]))return!1;return!0}function Rf(t,e){return Cf(t)?Ff(t,e):Cf(e)?Ff(e,t):t===e}function Ff(t,e){return Cf(e)?t.length===e.length&&t.every(((t,n)=>t===e[n])):1===t.length&&t[0]===e}var Mf,Nf,Lf,Df;(Nf=Mf||(Mf={})).pop="pop",Nf.push="push",(Df=Lf||(Lf={})).back="back",Df.forward="forward",Df.unknown="";const If=/^[^#]+#/;function jf(t,e){return t.replace(If,"#")+e}const Uf=()=>({left:window.pageXOffset,top:window.pageYOffset});function Gf(t){let e;if("el"in t){const n=t.el,r="string"==typeof n&&n.startsWith("#"),i="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;e=function(t,e){const n=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{behavior:e.behavior,left:r.left-n.left-(e.left||0),top:r.top-n.top-(e.top||0)}}(i,t)}else e=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(null!=e.left?e.left:window.pageXOffset,null!=e.top?e.top:window.pageYOffset)}function Bf(t,e){return(history.state?history.state.position-e:-1)+t}const Vf=new Map;let Hf=()=>location.protocol+"//"+location.host;function zf(t,e){const{pathname:n,search:r,hash:i}=e,o=t.indexOf("#");if(o>-1){let e=i.includes(t.slice(o))?t.slice(o).length:1,n=i.slice(e);return"/"!==n[0]&&(n="/"+n),kf(n,"")}return kf(n,t)+r+i}function Wf(t,e,n,r=!1,i=!1){return{back:t,current:e,forward:n,replaced:r,position:window.history.length,scroll:i?Uf():null}}function $f(t){const e=function(t){const{history:e,location:n}=window,r={value:zf(t,n)},i={value:e.state};function o(r,o,a){const s=t.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?t:t.slice(s))+r:Hf()+t+r;try{e[a?"replaceState":"pushState"](o,"",l),i.value=o}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return i.value||o(r.value,{back:null,current:r.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0),{location:r,state:i,push:function(t,n){const a=wf({},i.value,e.state,{forward:t,scroll:Uf()});o(a.current,a,!0),o(t,wf({},Wf(r.value,t,null),{position:a.position+1},n),!1),r.value=t},replace:function(t,n){o(t,wf({},e.state,Wf(i.value.back,t,i.value.forward,!0),n,{position:i.value.position}),!0),r.value=t}}}(t=function(t){if(!t)if(_f){const e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return"/"!==t[0]&&"#"!==t[0]&&(t="/"+t),Ef(t)}(t)),n=function(t,e,n,r){let i=[],o=[],a=null;const s=({state:o})=>{const s=zf(t,location),l=n.value,c=e.value;let h=0;if(o){if(n.value=s,e.value=o,a&&a===l)return void(a=null);h=c?o.position-c.position:0}else r(s);i.forEach((t=>{t(n.value,l,{delta:h,type:Mf.pop,direction:h?h>0?Lf.forward:Lf.back:Lf.unknown})}))};function l(){const{history:t}=window;t.state&&t.replaceState(wf({},t.state,{scroll:Uf()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(t){i.push(t);const e=()=>{const e=i.indexOf(t);e>-1&&i.splice(e,1)};return o.push(e),e},destroy:function(){for(const t of o)t();o=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(t,e.state,e.location,e.replace);const r=wf({location:"",base:t,go:function(t,e=!0){e||n.pauseListeners(),history.go(t)},createHref:jf.bind(null,t)},e,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>e.state.value}),r}function Kf(t){return"string"==typeof t||"symbol"==typeof t}const qf={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},Yf=Symbol("");var Xf,Qf;function Jf(t,e){return wf(new Error,{type:t,[Yf]:!0},e)}function Zf(t,e){return t instanceof Error&&Yf in t&&(null==e||!!(t.type&e))}(Qf=Xf||(Xf={}))[Qf.aborted=4]="aborted",Qf[Qf.cancelled=8]="cancelled",Qf[Qf.duplicated=16]="duplicated";const tp="[^/]+?",ep={sensitive:!1,strict:!1,start:!0,end:!0},np=/[.+*?^${}()[\]/\\]/g;function rp(t,e){let n=0;for(;n<t.length&&n<e.length;){const r=e[n]-t[n];if(r)return r;n++}return t.length<e.length?1===t.length&&80===t[0]?-1:1:t.length>e.length?1===e.length&&80===e[0]?1:-1:0}function ip(t,e){let n=0;const r=t.score,i=e.score;for(;n<r.length&&n<i.length;){const t=rp(r[n],i[n]);if(t)return t;n++}if(1===Math.abs(i.length-r.length)){if(op(r))return 1;if(op(i))return-1}return i.length-r.length}function op(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0}const ap={type:0,value:""},sp=/[a-zA-Z0-9_]/;function lp(t,e,n){const r=function(t,e){const n=wf({},ep,e),r=[];let i=n.start?"^":"";const o=[];for(const l of t){const t=l.length?[]:[90];n.strict&&!l.length&&(i+="/");for(let e=0;e<l.length;e++){const r=l[e];let a=40+(n.sensitive?.25:0);if(0===r.type)e||(i+="/"),i+=r.value.replace(np,"\\$&"),a+=40;else if(1===r.type){const{value:t,repeatable:n,optional:c,regexp:h}=r;o.push({name:t,repeatable:n,optional:c});const u=h||tp;if(u!==tp){a+=10;try{new RegExp(`(${u})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${t}" (${u}): `+s.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;e||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),i+=d,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===u&&(a+=-50)}t.push(a)}r.push(t)}if(n.strict&&n.end){const t=r.length-1;r[t][r[t].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&(i+="(?:/|$)");const a=new RegExp(i,n.sensitive?"":"i");return{re:a,score:r,keys:o,parse:function(t){const e=t.match(a),n={};if(!e)return null;for(let r=1;r<e.length;r++){const t=e[r]||"",i=o[r-1];n[i.name]=t&&i.repeatable?t.split("/"):t}return n},stringify:function(e){let n="",r=!1;for(const i of t){r&&n.endsWith("/")||(n+="/"),r=!1;for(const t of i)if(0===t.type)n+=t.value;else if(1===t.type){const{value:o,repeatable:a,optional:s}=t,l=o in e?e[o]:"";if(Cf(l)&&!a)throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);const c=Cf(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${o}"`);i.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=c}}return n||"/"}}}(function(t){if(!t)return[[]];if("/"===t)return[[ap]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function e(t){throw new Error(`ERR (${n})/"${c}": ${t}`)}let n=0,r=n;const i=[];let o;function a(){o&&i.push(o),o=[]}let s,l=0,c="",h="";function u(){c&&(0===n?o.push({type:0,value:c}):1===n||2===n||3===n?(o.length>1&&("*"===s||"+"===s)&&e(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:h,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):e("Invalid state to consume buffer"),c="")}function d(){c+=s}for(;l<t.length;)if(s=t[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&u(),a()):":"===s?(u(),n=1):d();break;case 4:d(),n=r;break;case 1:"("===s?n=2:sp.test(s)?d():(u(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==h[h.length-1]?h=h.slice(0,-1)+s:n=3:h+=s;break;case 3:u(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,h="";break;default:e("Unknown state")}else r=n,n=4;return 2===n&&e(`Unfinished custom RegExp for param "${c}"`),u(),a(),i}(t.path),n),i=wf(r,{record:t,parent:e,children:[],alias:[]});return e&&!i.record.aliasOf==!e.record.aliasOf&&e.children.push(i),i}function cp(t,e){const n=[],r=new Map;function i(t,n,r){const s=!r,l=function(t){return{path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:void 0,beforeEnter:t.beforeEnter,props:up(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}}}(t);l.aliasOf=r&&r.record;const c=pp(e,t),h=[l];if("alias"in t){const e="string"==typeof t.alias?[t.alias]:t.alias;for(const t of e)h.push(wf({},l,{components:r?r.record.components:l.components,path:t,aliasOf:r?r.record:l}))}let u,d;for(const e of h){const{path:h}=e;if(n&&"/"!==h[0]){const t=n.record.path,r="/"===t[t.length-1]?"":"/";e.path=n.record.path+(h&&r+h)}if(u=lp(e,n,c),r?r.alias.push(u):(d=d||u,d!==u&&d.alias.push(u),s&&t.name&&!dp(u)&&o(t.name)),l.children){const t=l.children;for(let e=0;e<t.length;e++)i(t[e],u,r&&r.children[e])}r=r||u,(u.record.components&&Object.keys(u.record.components).length||u.record.name||u.record.redirect)&&a(u)}return d?()=>{o(d)}:xf}function o(t){if(Kf(t)){const e=r.get(t);e&&(r.delete(t),n.splice(n.indexOf(e),1),e.children.forEach(o),e.alias.forEach(o))}else{const e=n.indexOf(t);e>-1&&(n.splice(e,1),t.record.name&&r.delete(t.record.name),t.children.forEach(o),t.alias.forEach(o))}}function a(t){let e=0;for(;e<n.length&&ip(t,n[e])>=0&&(t.record.path!==n[e].record.path||!gp(t,n[e]));)e++;n.splice(e,0,t),t.record.name&&!dp(t)&&r.set(t.record.name,t)}return e=pp({strict:!1,end:!0,sensitive:!1},e),t.forEach((t=>i(t))),{addRoute:i,resolve:function(t,e){let i,o,a,s={};if("name"in t&&t.name){if(i=r.get(t.name),!i)throw Jf(1,{location:t});a=i.record.name,s=wf(hp(e.params,i.keys.filter((t=>!t.optional)).map((t=>t.name))),t.params&&hp(t.params,i.keys.map((t=>t.name)))),o=i.stringify(s)}else if("path"in t)o=t.path,i=n.find((t=>t.re.test(o))),i&&(s=i.parse(o),a=i.record.name);else{if(i=e.name?r.get(e.name):n.find((t=>t.re.test(e.path))),!i)throw Jf(1,{location:t,currentLocation:e});a=i.record.name,s=wf({},e.params,t.params),o=i.stringify(s)}const l=[];let c=i;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:o,params:s,matched:l,meta:fp(l)}},removeRoute:o,getRoutes:function(){return n},getRecordMatcher:function(t){return r.get(t)}}}function hp(t,e){const n={};for(const r of e)r in t&&(n[r]=t[r]);return n}function up(t){const e={},n=t.props||!1;if("component"in t)e.default=n;else for(const r in t.components)e[r]="object"==typeof n?n[r]:n;return e}function dp(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function fp(t){return t.reduce(((t,e)=>wf(t,e.meta)),{})}function pp(t,e){const n={};for(const r in t)n[r]=r in e?e[r]:t[r];return n}function gp(t,e){return e.children.some((e=>e===t||gp(t,e)))}const mp=/#/g,yp=/&/g,vp=/\//g,bp=/=/g,_p=/\?/g,wp=/\+/g,Sp=/%5B/g,xp=/%5D/g,Cp=/%5E/g,Ap=/%60/g,Ep=/%7B/g,Pp=/%7C/g,kp=/%7D/g,Op=/%20/g;function Tp(t){return encodeURI(""+t).replace(Pp,"|").replace(Sp,"[").replace(xp,"]")}function Rp(t){return Tp(t).replace(wp,"%2B").replace(Op,"+").replace(mp,"%23").replace(yp,"%26").replace(Ap,"`").replace(Ep,"{").replace(kp,"}").replace(Cp,"^")}function Fp(t){return null==t?"":function(t){return Tp(t).replace(mp,"%23").replace(_p,"%3F")}(t).replace(vp,"%2F")}function Mp(t){try{return decodeURIComponent(""+t)}catch(e){}return""+t}function Np(t){const e={};if(""===t||"?"===t)return e;const n=("?"===t[0]?t.slice(1):t).split("&");for(let r=0;r<n.length;++r){const t=n[r].replace(wp," "),i=t.indexOf("="),o=Mp(i<0?t:t.slice(0,i)),a=i<0?null:Mp(t.slice(i+1));if(o in e){let t=e[o];Cf(t)||(t=e[o]=[t]),t.push(a)}else e[o]=a}return e}function Lp(t){let e="";for(let n in t){const r=t[n];if(n=Rp(n).replace(bp,"%3D"),null==r){void 0!==r&&(e+=(e.length?"&":"")+n);continue}(Cf(r)?r.map((t=>t&&Rp(t))):[r&&Rp(r)]).forEach((t=>{void 0!==t&&(e+=(e.length?"&":"")+n,null!=t&&(e+="="+t))}))}return e}function Dp(t){const e={};for(const n in t){const r=t[n];void 0!==r&&(e[n]=Cf(r)?r.map((t=>null==t?null:""+t)):null==r?r:""+r)}return e}const Ip=Symbol(""),jp=Symbol(""),Up=Symbol(""),Gp=Symbol(""),Bp=Symbol("");function Vp(){let t=[];return{add:function(e){return t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}},list:()=>t.slice(),reset:function(){t=[]}}}function Hp(t,e,n,r,i){const o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise(((a,s)=>{const l=t=>{var l;!1===t?s(Jf(4,{from:n,to:e})):t instanceof Error?s(t):"string"==typeof(l=t)||l&&"object"==typeof l?s(Jf(2,{from:e,to:t})):(o&&r.enterCallbacks[i]===o&&"function"==typeof t&&o.push(t),a())},c=t.call(r&&r.instances[i],e,n,l);let h=Promise.resolve(c);t.length<3&&(h=h.then(l)),h.catch((t=>s(t)))}))}function zp(t,e,n,r){const i=[];for(const a of t)for(const t in a.components){let s=a.components[t];if("beforeRouteEnter"===e||a.instances[t])if("object"==typeof(o=s)||"displayName"in o||"props"in o||"__vccOpts"in o){const o=(s.__vccOpts||s)[e];o&&i.push(Hp(o,n,r,a,t))}else{let o=s();i.push((()=>o.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${t}" at "${a.path}"`));const o=(s=i).__esModule||"Module"===s[Symbol.toStringTag]?i.default:i;var s;a.components[t]=o;const l=(o.__vccOpts||o)[e];return l&&Hp(l,n,r,a,t)()}))))}}var o;return i}function Wp(t){const e=yi(Up),n=yi(Gp),r=Xo((()=>e.resolve(Ne(t.to)))),i=Xo((()=>{const{matched:t}=r.value,{length:e}=t,i=t[e-1],o=n.matched;if(!i||!o.length)return-1;const a=o.findIndex(Of.bind(null,i));if(a>-1)return a;const s=Kp(t[e-2]);return e>1&&Kp(i)===s&&o[o.length-1].path!==s?o.findIndex(Of.bind(null,t[e-2])):a})),o=Xo((()=>i.value>-1&&function(t,e){for(const n in e){const r=e[n],i=t[n];if("string"==typeof r){if(r!==i)return!1}else if(!Cf(i)||i.length!==r.length||r.some(((t,e)=>t!==i[e])))return!1}return!0}(n.params,r.value.params))),a=Xo((()=>i.value>-1&&i.value===n.matched.length-1&&Tf(n.params,r.value.params)));return{route:r,href:Xo((()=>r.value.href)),isActive:o,isExactActive:a,navigate:function(n={}){return function(t){if(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)return;if(t.defaultPrevented)return;if(void 0!==t.button&&0!==t.button)return;if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}t.preventDefault&&t.preventDefault();return!0}(n)?e[Ne(t.replace)?"replace":"push"](Ne(t.to)).catch(xf):Promise.resolve()}}}const $p=gr({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Wp,setup(t,{slots:e}){const n=fe(Wp(t)),{options:r}=yi(Up),i=Xo((()=>({[qp(t.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[qp(t.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const r=e.default&&e.default(n);return t.custom?r:Qo("a",{"aria-current":n.isExactActive?t.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}});function Kp(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const qp=(t,e,n)=>null!=t?t:null!=e?e:n;function Yp(t,e){if(!t)return null;const n=t(e);return 1===n.length?n[0]:n}const Xp=gr({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:n}){const r=yi(Bp),i=Xo((()=>t.route||r.value)),o=yi(jp,0),a=Xo((()=>{let t=Ne(o);const{matched:e}=i.value;let n;for(;(n=e[t])&&!n.components;)t++;return t})),s=Xo((()=>i.value.matched[a.value]));mi(jp,Xo((()=>a.value+1))),mi(Ip,s),mi(Bp,i);const l=Te();return Xn((()=>[l.value,s.value,t.name]),(([t,e,n],[r,i,o])=>{e&&(e.instances[n]=t,i&&i!==e&&t&&t===r&&(e.leaveGuards.size||(e.leaveGuards=i.leaveGuards),e.updateGuards.size||(e.updateGuards=i.updateGuards))),!t||!e||i&&Of(e,i)&&r||(e.enterCallbacks[n]||[]).forEach((e=>e(t)))}),{flush:"post"}),()=>{const r=i.value,o=t.name,a=s.value,c=a&&a.components[o];if(!c)return Yp(n.default,{Component:c,route:r});const h=a.props[o],u=h?!0===h?r.params:"function"==typeof h?h(r):h:null,d=Qo(c,wf({},u,e,{onVnodeUnmounted:t=>{t.component.isUnmounted&&(a.instances[o]=null)},ref:l}));return Yp(n.default,{Component:d,route:r})||d}}});function Qp(){return yi(Up)}function Jp(){return yi(Gp)}const Zp={},tg=function(t,e,n){if(!e||0===e.length)return t();const r=document.getElementsByTagName("link");return Promise.all(e.map((t=>{if(t=function(t,e){return new URL(t,e).href}(t,n),t in Zp)return;Zp[t]=!0;const e=t.endsWith(".css"),i=e?'[rel="stylesheet"]':"";if(!!n)for(let n=r.length-1;n>=0;n--){const i=r[n];if(i.href===t&&(!e||"stylesheet"===i.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const o=document.createElement("link");return o.rel=e?"stylesheet":"modulepreload",e||(o.as="script",o.crossOrigin=""),o.href=t,document.head.appendChild(o),e?new Promise(((e,n)=>{o.addEventListener("load",e),o.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t())).catch((t=>{const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=t,window.dispatchEvent(e),!e.defaultPrevented)throw t}))},eg=[{path:"/",name:"home",redirect:"/item/project",children:[{path:"/home/<USER>",name:"home",component:()=>tg((()=>import("./chunk.45618571.js")),["./chunk.45618571.js","./chunk.49d958ff.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.1fa2ec58.js","./index.ba529bc9.css","./index.3c272e2b.css","./chunk.984ebab6.js","./chunk.805f473d.js","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.9a591dc8.js","./chunk.78434e43.js","./chunk.0db0b66d.js","./chunk.505381c5.js","./chunk.6eac9d60.js","./chunk.e117c129.js","./chunk.c83b4915.js","./chunk.4ae50552.js","./index.511b5537.css","./index.19fa9561.css"],import.meta.url)},{path:"/home/<USER>",name:"workbench",component:()=>tg((()=>import("./chunk.669deb0a.js")),["./chunk.669deb0a.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.159d03dd.js","./index.a68d7c0c.css","./chunk.a7215213.js","./chunk.4e1e4273.js","./chunk.505381c5.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.167d386e.css","./chunk.78434e43.js","./index.7488447a.css","./chunk.984ebab6.js","./chunk.805f473d.js","./chunk.49d958ff.js","./chunk.1fa2ec58.js","./index.ba529bc9.css","./index.3c272e2b.css","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.9a591dc8.js","./chunk.6eac9d60.js","./chunk.c83b4915.js","./index.686bba10.css","./index.ff055fcd.css","./index.3a32e493.css"],import.meta.url)}]},{path:"/item/project",name:"item",redirect:"/item/project/index",component:()=>tg((()=>import("./chunk.f397b713.js")),[],import.meta.url),children:[{path:"/item/project/index",name:"index",component:()=>tg((()=>import("./chunk.5a065794.js")),["./chunk.5a065794.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.f4e340b9.js","./chunk.505381c5.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.4e1e4273.js","./chunk.034f7efa.js","./index.df5cb325.css","./index.167d386e.css","./index.268af879.css","./chunk.8e3d5584.js","./chunk.159d03dd.js","./index.a68d7c0c.css","./index.34a14432.css","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.380b3154.js","./index.4e8fdaa5.css","./chunk.6d705473.js","./chunk.c83b4915.js","./chunk.8f74f9db.js","./chunk.b2d62236.js","./chunk.0db0b66d.js","./chunk.6eac9d60.js","./chunk.a7215213.js","./chunk.78434e43.js","./index.7488447a.css","./chunk.1fa2ec58.js","./index.ba529bc9.css","./chunk.6e3f10b3.js","./index.cfd63833.css","./chunk.e117c129.js","./index.fb51f024.css","./index.3a32e493.css","./index.19fa9561.css","./index.ff055fcd.css"],import.meta.url),meta:{name:"设计协作工作台"}},{path:"/item/project/stage",name:"stage",component:()=>tg((()=>import("./chunk.4544d6f0.js")),["./chunk.4544d6f0.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.4e1e4273.js","./chunk.505381c5.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.167d386e.css","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.380b3154.js","./index.4e8fdaa5.css","./chunk.27187e73.js","./index.eba67f65.css","./chunk.4ae50552.js","./chunk.6d705473.js","./chunk.c83b4915.js","./chunk.f4e340b9.js","./index.268af879.css","./chunk.0db0b66d.js","./chunk.6eac9d60.js","./chunk.8f74f9db.js","./chunk.b2d62236.js","./index.8a36bfba.css","./index.3a32e493.css","./index.19fa9561.css","./index.ff055fcd.css"],import.meta.url),meta:{name:"设计协作工作台"}},{path:"/item/project/detail",name:"detail",component:()=>tg((()=>import("./chunk.123416f7.js")),["./chunk.123416f7.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.380b3154.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.4e8fdaa5.css","./chunk.27187e73.js","./index.eba67f65.css","./chunk.1fa2ec58.js","./index.ba529bc9.css","./chunk.8e3d5584.js","./chunk.159d03dd.js","./index.a68d7c0c.css","./chunk.505381c5.js","./index.34a14432.css","./chunk.4ae50552.js","./chunk.c83b4915.js","./chunk.74ff160c.js","./index.68f87feb.css","./chunk.8f74f9db.js","./chunk.6d705473.js","./chunk.b2d62236.js","./chunk.0db0b66d.js","./chunk.6eac9d60.js","./chunk.9a591dc8.js","./index.742085c0.css","./index.19fa9561.css","./index.3a32e493.css","./index.ff055fcd.css"],import.meta.url),meta:{name:"设计协作工作台"}},{path:"/item/project/check",name:"authCheck",component:()=>tg((()=>import("./chunk.fb70e5a7.js")),["./chunk.fb70e5a7.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.c83b4915.js","./index.8c371732.css","./index.ff055fcd.css"],import.meta.url)}]},{path:"/viewImage",name:"viewImage",component:()=>tg((()=>import("./chunk.09b40ee1.js")),["./chunk.09b40ee1.js","./chunk.ecdc8f95.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.380b3154.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.4e8fdaa5.css","./index.b867d299.css","./chunk.805f473d.js","./chunk.984ebab6.js","./chunk.0db0b66d.js","./chunk.505381c5.js","./chunk.6eac9d60.js","./chunk.e117c129.js","./chunk.c83b4915.js","./index.aa8e4a67.css"],import.meta.url)},{path:"/screenCode",name:"screenCode",component:()=>tg((()=>import("./chunk.f8c4f303.js")),["./chunk.f8c4f303.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.6d705473.js","./chunk.ecdc8f95.js","./chunk.380b3154.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.4e8fdaa5.css","./index.b867d299.css","./chunk.4ae50552.js","./chunk.74ff160c.js","./index.68f87feb.css","./chunk.984ebab6.js","./index.55db8c8e.css"],import.meta.url)},{path:"/sketch",name:"Sketch",children:[{path:"/sketch/folderChoose",name:"folderChoose",component:()=>tg((()=>import("./chunk.4de45d87.js")),["./chunk.4de45d87.js","./chunk.8df321e8.js","./index.188bb514.css","./chunk.4e1e4273.js","./chunk.505381c5.js","./chunk.db8898e3.js","./index.672cdf86.css","./chunk.034f7efa.js","./index.df5cb325.css","./index.167d386e.css","./chunk.58688d79.js","./index.ac6245f3.css","./chunk.380b3154.js","./index.4e8fdaa5.css","./chunk.984ebab6.js","./chunk.f4e340b9.js","./index.268af879.css","./chunk.6e3f10b3.js","./index.cfd63833.css","./chunk.c83b4915.js","./chunk.6d705473.js","./chunk.b2d62236.js","./index.3aa3b86d.css","./index.3a32e493.css","./index.19fa9561.css","./index.ff055fcd.css"],import.meta.url)}]},{path:"/:w+",name:"*",redirect:"/home/<USER>"}],ng=function(t){const e=cp(t.routes,t),n=t.parseQuery||Np,r=t.stringifyQuery||Lp,i=t.history,o=Vp(),a=Vp(),s=Vp(),l=Re(qf);let c=qf;_f&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const h=Sf.bind(null,(t=>""+t)),u=Sf.bind(null,Fp),d=Sf.bind(null,Mp);function f(t,o){if(o=wf({},o||l.value),"string"==typeof t){const r=Pf(n,t,o.path),a=e.resolve({path:r.path},o),s=i.createHref(r.fullPath);return wf(r,a,{params:d(a.params),hash:Mp(r.hash),redirectedFrom:void 0,href:s})}let a;if("path"in t)a=wf({},t,{path:Pf(n,t.path,o.path).path});else{const e=wf({},t.params);for(const t in e)null==e[t]&&delete e[t];a=wf({},t,{params:u(e)}),o.params=u(o.params)}const s=e.resolve(a,o),c=t.hash||"";s.params=h(d(s.params));const f=function(t,e){const n=e.query?t(e.query):"";return e.path+(n&&"?")+n+(e.hash||"")}(r,wf({},t,{hash:(p=c,Tp(p).replace(Ep,"{").replace(kp,"}").replace(Cp,"^")),path:s.path}));var p;const g=i.createHref(f);return wf({fullPath:f,hash:c,query:r===Lp?Dp(t.query):t.query||{}},s,{redirectedFrom:void 0,href:g})}function p(t){return"string"==typeof t?Pf(n,t,l.value.path):wf({},t)}function g(t,e){if(c!==t)return Jf(8,{from:e,to:t})}function m(t){return v(t)}function y(t){const e=t.matched[t.matched.length-1];if(e&&e.redirect){const{redirect:n}=e;let r="function"==typeof n?n(t):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=p(r):{path:r},r.params={}),wf({query:t.query,hash:t.hash,params:"path"in r?{}:t.params},r)}}function v(t,e){const n=c=f(t),i=l.value,o=t.state,a=t.force,s=!0===t.replace,h=y(n);if(h)return v(wf(p(h),{state:"object"==typeof h?wf({},o,h.state):o,force:a,replace:s}),e||n);const u=n;let d;return u.redirectedFrom=e,!a&&function(t,e,n){const r=e.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Of(e.matched[r],n.matched[i])&&Tf(e.params,n.params)&&t(e.query)===t(n.query)&&e.hash===n.hash}(r,i,n)&&(d=Jf(16,{to:u,from:i}),R(i,i,!0,!1)),(d?Promise.resolve(d):w(u,i)).catch((t=>Zf(t)?Zf(t,2)?t:T(t):O(t,u,i))).then((t=>{if(t){if(Zf(t,2))return v(wf({replace:s},p(t.to),{state:"object"==typeof t.to?wf({},o,t.to.state):o,force:a}),e||u)}else t=x(u,i,!0,s,o);return S(u,i,t),t}))}function b(t,e){const n=g(t,e);return n?Promise.reject(n):Promise.resolve()}function _(t){const e=N.values().next().value;return e&&"function"==typeof e.runWithContext?e.runWithContext(t):t()}function w(t,e){let n;const[r,i,s]=function(t,e){const n=[],r=[],i=[],o=Math.max(e.matched.length,t.matched.length);for(let a=0;a<o;a++){const o=e.matched[a];o&&(t.matched.find((t=>Of(t,o)))?r.push(o):n.push(o));const s=t.matched[a];s&&(e.matched.find((t=>Of(t,s)))||i.push(s))}return[n,r,i]}(t,e);n=zp(r.reverse(),"beforeRouteLeave",t,e);for(const o of r)o.leaveGuards.forEach((r=>{n.push(Hp(r,t,e))}));const l=b.bind(null,t,e);return n.push(l),D(n).then((()=>{n=[];for(const r of o.list())n.push(Hp(r,t,e));return n.push(l),D(n)})).then((()=>{n=zp(i,"beforeRouteUpdate",t,e);for(const r of i)r.updateGuards.forEach((r=>{n.push(Hp(r,t,e))}));return n.push(l),D(n)})).then((()=>{n=[];for(const r of s)if(r.beforeEnter)if(Cf(r.beforeEnter))for(const i of r.beforeEnter)n.push(Hp(i,t,e));else n.push(Hp(r.beforeEnter,t,e));return n.push(l),D(n)})).then((()=>(t.matched.forEach((t=>t.enterCallbacks={})),n=zp(s,"beforeRouteEnter",t,e),n.push(l),D(n)))).then((()=>{n=[];for(const r of a.list())n.push(Hp(r,t,e));return n.push(l),D(n)})).catch((t=>Zf(t,8)?t:Promise.reject(t)))}function S(t,e,n){s.list().forEach((r=>_((()=>r(t,e,n)))))}function x(t,e,n,r,o){const a=g(t,e);if(a)return a;const s=e===qf,c=_f?history.state:{};n&&(r||s?i.replace(t.fullPath,wf({scroll:s&&c&&c.scroll},o)):i.push(t.fullPath,o)),l.value=t,R(t,e,n,s),T()}let C;function A(){C||(C=i.listen(((t,e,n)=>{if(!L.listening)return;const r=f(t),o=y(r);if(o)return void v(wf(o,{replace:!0}),r).catch(xf);c=r;const a=l.value;var s,h;_f&&(s=Bf(a.fullPath,n.delta),h=Uf(),Vf.set(s,h)),w(r,a).catch((t=>Zf(t,12)?t:Zf(t,2)?(v(t.to,r).then((t=>{Zf(t,20)&&!n.delta&&n.type===Mf.pop&&i.go(-1,!1)})).catch(xf),Promise.reject()):(n.delta&&i.go(-n.delta,!1),O(t,r,a)))).then((t=>{(t=t||x(r,a,!1))&&(n.delta&&!Zf(t,8)?i.go(-n.delta,!1):n.type===Mf.pop&&Zf(t,20)&&i.go(-1,!1)),S(r,a,t)})).catch(xf)})))}let E,P=Vp(),k=Vp();function O(t,e,n){T(t);const r=k.list();return r.length?r.forEach((r=>r(t,e,n))):console.error(t),Promise.reject(t)}function T(t){return E||(E=!t,A(),P.list().forEach((([e,n])=>t?n(t):e())),P.reset()),t}function R(e,n,r,i){const{scrollBehavior:o}=t;if(!_f||!o)return Promise.resolve();const a=!r&&function(t){const e=Vf.get(t);return Vf.delete(t),e}(Bf(e.fullPath,0))||(i||!r)&&history.state&&history.state.scroll||null;return rn().then((()=>o(e,n,a))).then((t=>t&&Gf(t))).catch((t=>O(t,e,n)))}const F=t=>i.go(t);let M;const N=new Set,L={currentRoute:l,listening:!0,addRoute:function(t,n){let r,i;return Kf(t)?(r=e.getRecordMatcher(t),i=n):i=t,e.addRoute(i,r)},removeRoute:function(t){const n=e.getRecordMatcher(t);n&&e.removeRoute(n)},hasRoute:function(t){return!!e.getRecordMatcher(t)},getRoutes:function(){return e.getRoutes().map((t=>t.record))},resolve:f,options:t,push:m,replace:function(t){return m(wf(p(t),{replace:!0}))},go:F,back:()=>F(-1),forward:()=>F(1),beforeEach:o.add,beforeResolve:a.add,afterEach:s.add,onError:k.add,isReady:function(){return E&&l.value!==qf?Promise.resolve():new Promise(((t,e)=>{P.add([t,e])}))},install(t){t.component("RouterLink",$p),t.component("RouterView",Xp),t.config.globalProperties.$router=this,Object.defineProperty(t.config.globalProperties,"$route",{enumerable:!0,get:()=>Ne(l)}),_f&&!M&&l.value===qf&&(M=!0,m(i.location).catch((t=>{})));const e={};for(const r in qf)Object.defineProperty(e,r,{get:()=>l.value[r],enumerable:!0});t.provide(Up,this),t.provide(Gp,pe(e)),t.provide(Bp,l);const n=t.unmount;N.add(t),t.unmount=function(){N.delete(t),N.size<1&&(c=qf,C&&C(),C=null,l.value=qf,M=!1,E=!1),n()}}};function D(t){return t.reduce(((t,e)=>t.then((()=>_(e)))),Promise.resolve())}return L}({history:((rg=location.host?rg||location.pathname+location.search:"").includes("#")||(rg+="#"),$f(rg)),routes:eg});var rg,ig={exports:{}};const og=Cc(Ls);var ag,sg={exports:{}},lg={},cg={},hg={};function ug(){return ag||(ag=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t._registerNode=t.Konva=t.glob=void 0;const e=Math.PI/180;t.glob=void 0!==Sc?Sc:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},t.Konva={_global:t.glob,version:"9.3.1",isBrowser:"undefined"!=typeof window&&("[object Window]"==={}.toString.call(window)||"[object global]"==={}.toString.call(window)),isUnminified:/param/.test(function(t){}.toString()),dblClickWindow:400,getAngle:n=>t.Konva.angleDeg?n*e:n,enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,pixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:()=>t.Konva.DD.isDragging,isDragReady:()=>!!t.Konva.DD.node,releaseCanvasOnDestroy:!0,document:t.glob.document,_injectGlobal(e){t.glob.Konva=e}};t._registerNode=e=>{t.Konva[e.prototype.getClassName()]=e},t.Konva._injectGlobal(t.Konva)}(hg)),hg}var dg,fg={};function pg(){return dg||(dg=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Util=t.Transform=void 0;const e=ug();class n{constructor(t=[1,0,0,1,0,0]){this.dirty=!1,this.m=t&&t.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new n(this.m)}copyInto(t){t.m[0]=this.m[0],t.m[1]=this.m[1],t.m[2]=this.m[2],t.m[3]=this.m[3],t.m[4]=this.m[4],t.m[5]=this.m[5]}point(t){var e=this.m;return{x:e[0]*t.x+e[2]*t.y+e[4],y:e[1]*t.x+e[3]*t.y+e[5]}}translate(t,e){return this.m[4]+=this.m[0]*t+this.m[2]*e,this.m[5]+=this.m[1]*t+this.m[3]*e,this}scale(t,e){return this.m[0]*=t,this.m[1]*=t,this.m[2]*=e,this.m[3]*=e,this}rotate(t){var e=Math.cos(t),n=Math.sin(t),r=this.m[0]*e+this.m[2]*n,i=this.m[1]*e+this.m[3]*n,o=this.m[0]*-n+this.m[2]*e,a=this.m[1]*-n+this.m[3]*e;return this.m[0]=r,this.m[1]=i,this.m[2]=o,this.m[3]=a,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(t,e){var n=this.m[0]+this.m[2]*e,r=this.m[1]+this.m[3]*e,i=this.m[2]+this.m[0]*t,o=this.m[3]+this.m[1]*t;return this.m[0]=n,this.m[1]=r,this.m[2]=i,this.m[3]=o,this}multiply(t){var e=this.m[0]*t.m[0]+this.m[2]*t.m[1],n=this.m[1]*t.m[0]+this.m[3]*t.m[1],r=this.m[0]*t.m[2]+this.m[2]*t.m[3],i=this.m[1]*t.m[2]+this.m[3]*t.m[3],o=this.m[0]*t.m[4]+this.m[2]*t.m[5]+this.m[4],a=this.m[1]*t.m[4]+this.m[3]*t.m[5]+this.m[5];return this.m[0]=e,this.m[1]=n,this.m[2]=r,this.m[3]=i,this.m[4]=o,this.m[5]=a,this}invert(){var t=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),e=this.m[3]*t,n=-this.m[1]*t,r=-this.m[2]*t,i=this.m[0]*t,o=t*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),a=t*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=e,this.m[1]=n,this.m[2]=r,this.m[3]=i,this.m[4]=o,this.m[5]=a,this}getMatrix(){return this.m}decompose(){var e=this.m[0],n=this.m[1],r=this.m[2],i=this.m[3],o=e*i-n*r;let a={x:this.m[4],y:this.m[5],rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!=e||0!=n){var s=Math.sqrt(e*e+n*n);a.rotation=n>0?Math.acos(e/s):-Math.acos(e/s),a.scaleX=s,a.scaleY=o/s,a.skewX=(e*r+n*i)/o,a.skewY=0}else if(0!=r||0!=i){var l=Math.sqrt(r*r+i*i);a.rotation=Math.PI/2-(i>0?Math.acos(-r/l):-Math.acos(r/l)),a.scaleX=o/l,a.scaleY=l,a.skewX=0,a.skewY=(e*r+n*i)/o}return a.rotation=t.Util._getRotation(a.rotation),a}}t.Transform=n;var r=Math.PI/180,i=180/Math.PI,o="Konva error: ",a={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},s=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/,l=[];const c="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||function(t){setTimeout(t,60)};t.Util={_isElement:t=>!(!t||1!=t.nodeType),_isFunction:t=>!!(t&&t.constructor&&t.call&&t.apply),_isPlainObject:t=>!!t&&t.constructor===Object,_isArray:t=>"[object Array]"===Object.prototype.toString.call(t),_isNumber:t=>"[object Number]"===Object.prototype.toString.call(t)&&!isNaN(t)&&isFinite(t),_isString:t=>"[object String]"===Object.prototype.toString.call(t),_isBoolean:t=>"[object Boolean]"===Object.prototype.toString.call(t),isObject:t=>t instanceof Object,isValidSelector(t){if("string"!=typeof t)return!1;var e=t[0];return"#"===e||"."===e||e===e.toUpperCase()},_sign:t=>0===t||t>0?1:-1,requestAnimFrame(t){l.push(t),1===l.length&&c((function(){const t=l;l=[],t.forEach((function(t){t()}))}))},createCanvasElement(){var t=document.createElement("canvas");try{t.style=t.style||{}}catch(bv){}return t},createImageElement:()=>document.createElement("img"),_isInDocument(t){for(;t=t.parentNode;)if(t==document)return!0;return!1},_urlToImage(e,n){var r=t.Util.createImageElement();r.onload=function(){n(r)},r.src=e},_rgbToHex:(t,e,n)=>((1<<24)+(t<<16)+(e<<8)+n).toString(16).slice(1),_hexToRgb(t){t=t.replace("#","");var e=parseInt(t,16);return{r:e>>16&255,g:e>>8&255,b:255&e}},getRandomColor(){for(var t=(16777215*Math.random()<<0).toString(16);t.length<6;)t="0"+t;return"#"+t},getRGB(t){var e;return t in a?{r:(e=a[t])[0],g:e[1],b:e[2]}:"#"===t[0]?this._hexToRgb(t.substring(1)):"rgb("===t.substr(0,4)?(e=s.exec(t.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA:e=>(e=e||"black",t.Util._namedColorToRBA(e)||t.Util._hex3ColorToRGBA(e)||t.Util._hex4ColorToRGBA(e)||t.Util._hex6ColorToRGBA(e)||t.Util._hex8ColorToRGBA(e)||t.Util._rgbColorToRGBA(e)||t.Util._rgbaColorToRGBA(e)||t.Util._hslColorToRGBA(e)),_namedColorToRBA(t){var e=a[t.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA(t){if(0===t.indexOf("rgb(")){var e=(t=t.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA(t){if(0===t.indexOf("rgba(")){var e=(t=t.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map(((t,e)=>"%"===t.slice(-1)?3===e?parseInt(t)/100:parseInt(t)/100*255:Number(t)));return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex8ColorToRGBA(t){if("#"===t[0]&&9===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:parseInt(t.slice(7,9),16)/255}},_hex6ColorToRGBA(t){if("#"===t[0]&&7===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:1}},_hex4ColorToRGBA(t){if("#"===t[0]&&5===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:parseInt(t[4]+t[4],16)/255}},_hex3ColorToRGBA(t){if("#"===t[0]&&4===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:1}},_hslColorToRGBA(t){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(t)){const[e,...n]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t),r=Number(n[0])/360,i=Number(n[1])/100,o=Number(n[2])/100;let a,s,l;if(0===i)return l=255*o,{r:Math.round(l),g:Math.round(l),b:Math.round(l),a:1};a=o<.5?o*(1+i):o+i-o*i;const c=2*o-a,h=[0,0,0];for(let t=0;t<3;t++)s=r+1/3*-(t-1),s<0&&s++,s>1&&s--,l=6*s<1?c+6*(a-c)*s:2*s<1?a:3*s<2?c+(a-c)*(2/3-s)*6:c,h[t]=255*l;return{r:Math.round(h[0]),g:Math.round(h[1]),b:Math.round(h[2]),a:1}}},haveIntersection:(t,e)=>!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y),cloneObject(t){var e={};for(var n in t)this._isPlainObject(t[n])?e[n]=this.cloneObject(t[n]):this._isArray(t[n])?e[n]=this.cloneArray(t[n]):e[n]=t[n];return e},cloneArray:t=>t.slice(0),degToRad:t=>t*r,radToDeg:t=>t*i,_degToRad:e=>(t.Util.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),t.Util.degToRad(e)),_radToDeg:e=>(t.Util.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),t.Util.radToDeg(e)),_getRotation:n=>e.Konva.angleDeg?t.Util.radToDeg(n):n,_capitalize:t=>t.charAt(0).toUpperCase()+t.slice(1),throw(t){throw new Error(o+t)},error(t){console.error(o+t)},warn(t){e.Konva.showWarnings&&console.warn("Konva warning: "+t)},each(t,e){for(var n in t)e(n,t[n])},_inRange:(t,e,n)=>e<=t&&t<n,_getProjectionToSegment(t,e,n,r,i,o){var a,s,l,c=(t-n)*(t-n)+(e-r)*(e-r);if(0==c)a=t,s=e,l=(i-n)*(i-n)+(o-r)*(o-r);else{var h=((i-t)*(n-t)+(o-e)*(r-e))/c;h<0?(a=t,s=e,l=(t-i)*(t-i)+(e-o)*(e-o)):h>1?(a=n,s=r,l=(n-i)*(n-i)+(r-o)*(r-o)):l=((a=t+h*(n-t))-i)*(a-i)+((s=e+h*(r-e))-o)*(s-o)}return[a,s,l]},_getProjectionToLine(e,n,r){var i=t.Util.cloneObject(e),o=Number.MAX_VALUE;return n.forEach((function(a,s){if(r||s!==n.length-1){var l=n[(s+1)%n.length],c=t.Util._getProjectionToSegment(a.x,a.y,l.x,l.y,e.x,e.y),h=c[0],u=c[1],d=c[2];d<o&&(i.x=h,i.y=u,o=d)}})),i},_prepareArrayForTween(e,n,r){var i,o=[],a=[];if(e.length>n.length){var s=n;n=e,e=s}for(i=0;i<e.length;i+=2)o.push({x:e[i],y:e[i+1]});for(i=0;i<n.length;i+=2)a.push({x:n[i],y:n[i+1]});var l=[];return a.forEach((function(e){var n=t.Util._getProjectionToLine(e,o,r);l.push(n.x),l.push(n.y)})),l},_prepareToStringify(e){var n;for(var r in e.visitedByCircularReferenceRemoval=!0,e)if(e.hasOwnProperty(r)&&e[r]&&"object"==typeof e[r])if(n=Object.getOwnPropertyDescriptor(e,r),e[r].visitedByCircularReferenceRemoval||t.Util._isElement(e[r])){if(!n.configurable)return null;delete e[r]}else if(null===t.Util._prepareToStringify(e[r])){if(!n.configurable)return null;delete e[r]}return delete e.visitedByCircularReferenceRemoval,e},_assign(t,e){for(var n in e)t[n]=e[n];return t},_getFirstPointerId:t=>t.touches?t.changedTouches[0].identifier:t.pointerId||999,releaseCanvas(...t){e.Konva.releaseCanvasOnDestroy&&t.forEach((t=>{t.width=0,t.height=0}))},drawRoundedRectPath(t,e,n,r){let i=0,o=0,a=0,s=0;"number"==typeof r?i=o=a=s=Math.min(r,e/2,n/2):(i=Math.min(r[0]||0,e/2,n/2),o=Math.min(r[1]||0,e/2,n/2),s=Math.min(r[2]||0,e/2,n/2),a=Math.min(r[3]||0,e/2,n/2)),t.moveTo(i,0),t.lineTo(e-o,0),t.arc(e-o,o,o,3*Math.PI/2,0,!1),t.lineTo(e,n-s),t.arc(e-s,n-s,s,0,Math.PI/2,!1),t.lineTo(a,n),t.arc(a,n-a,a,Math.PI/2,Math.PI,!1),t.lineTo(0,i),t.arc(i,i,i,Math.PI,3*Math.PI/2,!1)}}}(fg)),fg}var gg,mg,yg={},vg={},bg={};function _g(){if(gg)return bg;gg=1,Object.defineProperty(bg,"__esModule",{value:!0}),bg.getComponentValidator=bg.getBooleanValidator=bg.getNumberArrayValidator=bg.getFunctionValidator=bg.getStringOrGradientValidator=bg.getStringValidator=bg.getNumberOrAutoValidator=bg.getNumberOrArrayOfNumbersValidator=bg.getNumberValidator=bg.alphaComponent=bg.RGBComponent=void 0;const t=ug(),e=pg();function n(t){return e.Util._isString(t)?'"'+t+'"':"[object Number]"===Object.prototype.toString.call(t)||e.Util._isBoolean(t)?t:Object.prototype.toString.call(t)}return bg.RGBComponent=function(t){return t>255?255:t<0?0:Math.round(t)},bg.alphaComponent=function(t){return t>1?1:t<1e-4?1e-4:t},bg.getNumberValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a number.'),t}},bg.getNumberOrArrayOfNumbersValidator=function(r){if(t.Konva.isUnminified)return function(t,i){let o=e.Util._isNumber(t),a=e.Util._isArray(t)&&t.length==r;return o||a||e.Util.warn(n(t)+' is a not valid value for "'+i+'" attribute. The value should be a number or Array<number>('+r+")"),t}},bg.getNumberOrAutoValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||"auto"===t||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a number or "auto".'),t}},bg.getStringValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isString(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a string.'),t}},bg.getStringOrGradientValidator=function(){if(t.Konva.isUnminified)return function(t,r){const i=e.Util._isString(t),o="[object CanvasGradient]"===Object.prototype.toString.call(t)||t&&t.addColorStop;return i||o||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a string or a native gradient.'),t}},bg.getFunctionValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isFunction(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a function.'),t}},bg.getNumberArrayValidator=function(){if(t.Konva.isUnminified)return function(t,r){const i=Int8Array?Object.getPrototypeOf(Int8Array):null;return i&&t instanceof i||(e.Util._isArray(t)?t.forEach((function(t){e.Util._isNumber(t)||e.Util.warn('"'+r+'" attribute has non numeric element '+t+". Make sure that all elements are numbers.")})):e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a array of numbers.')),t}},bg.getBooleanValidator=function(){if(t.Konva.isUnminified)return function(t,r){return!0===t||!1===t||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a boolean.'),t}},bg.getComponentValidator=function(r){if(t.Konva.isUnminified)return function(t,i){return null==t||e.Util.isObject(t)||e.Util.warn(n(t)+' is a not valid value for "'+i+'" attribute. The value should be an object with properties '+r),t}},bg}function wg(){return mg||(mg=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Factory=void 0;const e=pg(),n=_g();var r="get",i="set";t.Factory={addGetterSetter(e,n,r,i,o){t.Factory.addGetter(e,n,r),t.Factory.addSetter(e,n,i,o),t.Factory.addOverloadedGetterSetter(e,n)},addGetter(t,n,i){var o=r+e.Util._capitalize(n);t.prototype[o]=t.prototype[o]||function(){var t=this.attrs[n];return void 0===t?i:t}},addSetter(n,r,o,a){var s=i+e.Util._capitalize(r);n.prototype[s]||t.Factory.overWriteSetter(n,r,o,a)},overWriteSetter(t,n,r,o){var a=i+e.Util._capitalize(n);t.prototype[a]=function(t){return r&&null!=t&&(t=r.call(this,t,n)),this._setAttr(n,t),o&&o.call(this),this}},addComponentsGetterSetter(o,a,s,l,c){var h,u,d=s.length,f=e.Util._capitalize,p=r+f(a),g=i+f(a);o.prototype[p]=function(){var t={};for(h=0;h<d;h++)t[u=s[h]]=this.getAttr(a+f(u));return t};var m=(0,n.getComponentValidator)(s);o.prototype[g]=function(t){var e,n=this.attrs[a];for(e in l&&(t=l.call(this,t)),m&&m.call(this,t,a),t)t.hasOwnProperty(e)&&this._setAttr(a+f(e),t[e]);return t||s.forEach((t=>{this._setAttr(a+f(t),void 0)})),this._fireChangeEvent(a,n,t),c&&c.call(this),this},t.Factory.addOverloadedGetterSetter(o,a)},addOverloadedGetterSetter(t,n){var o=e.Util._capitalize(n),a=i+o,s=r+o;t.prototype[n]=function(){return arguments.length?(this[a](arguments[0]),this):this[s]()}},addDeprecatedGetterSetter(n,i,o,a){e.Util.error("Adding deprecated "+i);var s=r+e.Util._capitalize(i),l=i+" property is deprecated and will be removed soon. Look at Konva change log for more information.";n.prototype[s]=function(){e.Util.error(l);var t=this.attrs[i];return void 0===t?o:t},t.Factory.addSetter(n,i,a,(function(){e.Util.error(l)})),t.Factory.addOverloadedGetterSetter(n,i)},backCompat(t,n){e.Util.each(n,(function(n,o){var a=t.prototype[o],s=r+e.Util._capitalize(n),l=i+e.Util._capitalize(n);function c(){a.apply(this,arguments),e.Util.error('"'+n+'" method is deprecated and will be removed soon. Use ""'+o+'" instead.')}t.prototype[n]=c,t.prototype[s]=c,t.prototype[l]=c}))},afterSetFilter(){this._filterUpToDate=!1}}}(vg)),vg}var Sg,xg,Cg={},Ag={};function Eg(){if(Sg)return Ag;Sg=1,Object.defineProperty(Ag,"__esModule",{value:!0}),Ag.HitContext=Ag.SceneContext=Ag.Context=void 0;const t=pg(),e=ug();var n=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","roundRect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"];let r=class{constructor(t){this.canvas=t,e.Konva.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(t){t.fillEnabled()&&this._fill(t)}_fill(t){}strokeShape(t){t.hasStroke()&&this._stroke(t)}_stroke(t){}fillStrokeShape(t){t.attrs.fillAfterStrokeEnabled?(this.strokeShape(t),this.fillShape(t)):(this.fillShape(t),this.strokeShape(t))}getTrace(e,n){var r,i,o,a,s=this.traceArr,l=s.length,c="";for(r=0;r<l;r++)(o=(i=s[r]).method)?(a=i.args,c+=o,e?c+="()":t.Util._isArray(a[0])?c+="(["+a.join(",")+"])":(n&&(a=a.map((t=>"number"==typeof t?Math.floor(t):t))),c+="("+a.join(",")+")")):(c+=i.property,e||(c+="="+i.val)),c+=";";return c}clearTrace(){this.traceArr=[]}_trace(t){var e=this.traceArr;e.push(t),e.length>=100&&e.shift()}reset(){var t=this.getCanvas().getPixelRatio();this.setTransform(1*t,0,0,1*t,0,0)}getCanvas(){return this.canvas}clear(t){var e=this.getCanvas();t?this.clearRect(t.x||0,t.y||0,t.width||0,t.height||0):this.clearRect(0,0,e.getWidth()/e.pixelRatio,e.getHeight()/e.pixelRatio)}_applyLineCap(t){const e=t.attrs.lineCap;e&&this.setAttr("lineCap",e)}_applyOpacity(t){var e=t.getAbsoluteOpacity();1!==e&&this.setAttr("globalAlpha",e)}_applyLineJoin(t){const e=t.attrs.lineJoin;e&&this.setAttr("lineJoin",e)}setAttr(t,e){this._context[t]=e}arc(t,e,n,r,i,o){this._context.arc(t,e,n,r,i,o)}arcTo(t,e,n,r,i){this._context.arcTo(t,e,n,r,i)}beginPath(){this._context.beginPath()}bezierCurveTo(t,e,n,r,i,o){this._context.bezierCurveTo(t,e,n,r,i,o)}clearRect(t,e,n,r){this._context.clearRect(t,e,n,r)}clip(...t){this._context.clip.apply(this._context,t)}closePath(){this._context.closePath()}createImageData(t,e){var n=arguments;return 2===n.length?this._context.createImageData(t,e):1===n.length?this._context.createImageData(t):void 0}createLinearGradient(t,e,n,r){return this._context.createLinearGradient(t,e,n,r)}createPattern(t,e){return this._context.createPattern(t,e)}createRadialGradient(t,e,n,r,i,o){return this._context.createRadialGradient(t,e,n,r,i,o)}drawImage(t,e,n,r,i,o,a,s,l){var c=arguments,h=this._context;3===c.length?h.drawImage(t,e,n):5===c.length?h.drawImage(t,e,n,r,i):9===c.length&&h.drawImage(t,e,n,r,i,o,a,s,l)}ellipse(t,e,n,r,i,o,a,s){this._context.ellipse(t,e,n,r,i,o,a,s)}isPointInPath(t,e,n,r){return n?this._context.isPointInPath(n,t,e,r):this._context.isPointInPath(t,e,r)}fill(...t){this._context.fill.apply(this._context,t)}fillRect(t,e,n,r){this._context.fillRect(t,e,n,r)}strokeRect(t,e,n,r){this._context.strokeRect(t,e,n,r)}fillText(t,e,n,r){r?this._context.fillText(t,e,n,r):this._context.fillText(t,e,n)}measureText(t){return this._context.measureText(t)}getImageData(t,e,n,r){return this._context.getImageData(t,e,n,r)}lineTo(t,e){this._context.lineTo(t,e)}moveTo(t,e){this._context.moveTo(t,e)}rect(t,e,n,r){this._context.rect(t,e,n,r)}roundRect(t,e,n,r,i){this._context.roundRect(t,e,n,r,i)}putImageData(t,e,n){this._context.putImageData(t,e,n)}quadraticCurveTo(t,e,n,r){this._context.quadraticCurveTo(t,e,n,r)}restore(){this._context.restore()}rotate(t){this._context.rotate(t)}save(){this._context.save()}scale(t,e){this._context.scale(t,e)}setLineDash(t){this._context.setLineDash?this._context.setLineDash(t):"mozDash"in this._context?this._context.mozDash=t:"webkitLineDash"in this._context&&(this._context.webkitLineDash=t)}getLineDash(){return this._context.getLineDash()}setTransform(t,e,n,r,i,o){this._context.setTransform(t,e,n,r,i,o)}stroke(t){t?this._context.stroke(t):this._context.stroke()}strokeText(t,e,n,r){this._context.strokeText(t,e,n,r)}transform(t,e,n,r,i,o){this._context.transform(t,e,n,r,i,o)}translate(t,e){this._context.translate(t,e)}_enableTrace(){var e,r,i=this,o=n.length,a=this.setAttr,s=function(e){var n,o=i[e];i[e]=function(){return r=function(e){var n,r,i=[],o=e.length,a=t.Util;for(n=0;n<o;n++)r=e[n],a._isNumber(r)?r=Math.round(1e3*r)/1e3:a._isString(r)||(r+=""),i.push(r);return i}(Array.prototype.slice.call(arguments,0)),n=o.apply(i,arguments),i._trace({method:e,args:r}),n}};for(e=0;e<o;e++)s(n[e]);i.setAttr=function(){a.apply(i,arguments);var t=arguments[0],e=arguments[1];"shadowOffsetX"!==t&&"shadowOffsetY"!==t&&"shadowBlur"!==t||(e/=this.canvas.getPixelRatio()),i._trace({property:t,val:e})}}_applyGlobalCompositeOperation(t){const e=t.attrs.globalCompositeOperation;!e||"source-over"===e||this.setAttr("globalCompositeOperation",e)}};Ag.Context=r,["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","letterSpacing","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","direction","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach((function(t){Object.defineProperty(r.prototype,t,{get(){return this._context[t]},set(e){this._context[t]=e}})}));Ag.SceneContext=class extends r{constructor(t,{willReadFrequently:e=!1}={}){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:e})}_fillColor(t){var e=t.fill();this.setAttr("fillStyle",e),t._fillFunc(this)}_fillPattern(t){this.setAttr("fillStyle",t._getFillPattern()),t._fillFunc(this)}_fillLinearGradient(t){var e=t._getLinearGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fillRadialGradient(t){const e=t._getRadialGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fill(t){const e=t.fill(),n=t.getFillPriority();if(e&&"color"===n)return void this._fillColor(t);const r=t.getFillPatternImage();if(r&&"pattern"===n)return void this._fillPattern(t);const i=t.getFillLinearGradientColorStops();if(i&&"linear-gradient"===n)return void this._fillLinearGradient(t);const o=t.getFillRadialGradientColorStops();o&&"radial-gradient"===n?this._fillRadialGradient(t):e?this._fillColor(t):r?this._fillPattern(t):i?this._fillLinearGradient(t):o&&this._fillRadialGradient(t)}_strokeLinearGradient(t){const e=t.getStrokeLinearGradientStartPoint(),n=t.getStrokeLinearGradientEndPoint(),r=t.getStrokeLinearGradientColorStops(),i=this.createLinearGradient(e.x,e.y,n.x,n.y);if(r){for(var o=0;o<r.length;o+=2)i.addColorStop(r[o],r[o+1]);this.setAttr("strokeStyle",i)}}_stroke(t){var e=t.dash(),n=t.getStrokeScaleEnabled();if(t.hasStroke()){if(!n){this.save();var r=this.getCanvas().getPixelRatio();this.setTransform(r,0,0,r,0,0)}this._applyLineCap(t),e&&t.dashEnabled()&&(this.setLineDash(e),this.setAttr("lineDashOffset",t.dashOffset())),this.setAttr("lineWidth",t.strokeWidth()),t.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)"),t.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(t):this.setAttr("strokeStyle",t.stroke()),t._strokeFunc(this),n||this.restore()}}_applyShadow(t){var e,n,r,i=null!==(e=t.getShadowRGBA())&&void 0!==e?e:"black",o=null!==(n=t.getShadowBlur())&&void 0!==n?n:5,a=null!==(r=t.getShadowOffset())&&void 0!==r?r:{x:0,y:0},s=t.getAbsoluteScale(),l=this.canvas.getPixelRatio(),c=s.x*l,h=s.y*l;this.setAttr("shadowColor",i),this.setAttr("shadowBlur",o*Math.min(Math.abs(c),Math.abs(h))),this.setAttr("shadowOffsetX",a.x*c),this.setAttr("shadowOffsetY",a.y*h)}};return Ag.HitContext=class extends r{constructor(t){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:!0})}_fill(t){this.save(),this.setAttr("fillStyle",t.colorKey),t._fillFuncHit(this),this.restore()}strokeShape(t){t.hasHitStroke()&&this._stroke(t)}_stroke(t){if(t.hasHitStroke()){const i=t.getStrokeScaleEnabled();if(!i){this.save();var e=this.getCanvas().getPixelRatio();this.setTransform(e,0,0,e,0,0)}this._applyLineCap(t);var n=t.hitStrokeWidth(),r="auto"===n?t.strokeWidth():n;this.setAttr("lineWidth",r),this.setAttr("strokeStyle",t.colorKey),t._strokeFuncHit(this),i||this.restore()}}},Ag}function Pg(){if(xg)return Cg;xg=1,Object.defineProperty(Cg,"__esModule",{value:!0}),Cg.HitCanvas=Cg.SceneCanvas=Cg.Canvas=void 0;const t=pg(),e=Eg(),n=ug(),r=wg(),i=_g();var o;let a=class{constructor(e){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;var r=(e||{}).pixelRatio||n.Konva.pixelRatio||function(){if(o)return o;var e=t.Util.createCanvasElement(),r=e.getContext("2d");return o=(n.Konva._global.devicePixelRatio||1)/(r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1),t.Util.releaseCanvas(e),o}();this.pixelRatio=r,this._canvas=t.Util.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(t){var e=this.pixelRatio;this.pixelRatio=t,this.setSize(this.getWidth()/e,this.getHeight()/e)}setWidth(t){this.width=this._canvas.width=t*this.pixelRatio,this._canvas.style.width=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)}setHeight(t){this.height=this._canvas.height=t*this.pixelRatio,this._canvas.style.height=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)}getWidth(){return this.width}getHeight(){return this.height}setSize(t,e){this.setWidth(t||0),this.setHeight(e||0)}toDataURL(e,n){try{return this._canvas.toDataURL(e,n)}catch(bv){try{return this._canvas.toDataURL()}catch(r){return t.Util.error("Unable to get data URL. "+r.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}};Cg.Canvas=a,r.Factory.addGetterSetter(a,"pixelRatio",void 0,(0,i.getNumberValidator)());Cg.SceneCanvas=class extends a{constructor(t={width:0,height:0,willReadFrequently:!1}){super(t),this.context=new e.SceneContext(this,{willReadFrequently:t.willReadFrequently}),this.setSize(t.width,t.height)}};return Cg.HitCanvas=class extends a{constructor(t={width:0,height:0}){super(t),this.hitCanvas=!0,this.context=new e.HitContext(this),this.setSize(t.width,t.height)}},Cg}var kg,Og,Tg={};function Rg(){return kg||(kg=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.DD=void 0;const e=ug(),n=pg();t.DD={get isDragging(){var e=!1;return t.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&(e=!0)})),e},justDragged:!1,get node(){var e;return t.DD._dragElements.forEach((t=>{e=t.node})),e},_dragElements:new Map,_drag(e){const r=[];t.DD._dragElements.forEach(((t,i)=>{const{node:o}=t,a=o.getStage();a.setPointersPositions(e),void 0===t.pointerId&&(t.pointerId=n.Util._getFirstPointerId(e));const s=a._changedPointerPositions.find((e=>e.id===t.pointerId));if(s){if("dragging"!==t.dragStatus){var l=o.dragDistance();if(Math.max(Math.abs(s.x-t.startPointerPos.x),Math.abs(s.y-t.startPointerPos.y))<l)return;if(o.startDrag({evt:e}),!o.isDragging())return}o._setDragPosition(e,t),r.push(o)}})),r.forEach((t=>{t.fire("dragmove",{type:"dragmove",target:t,evt:e},!0)}))},_endDragBefore(n){const r=[];t.DD._dragElements.forEach((i=>{const{node:o}=i,a=o.getStage();n&&a.setPointersPositions(n);if(!a._changedPointerPositions.find((t=>t.id===i.pointerId)))return;"dragging"!==i.dragStatus&&"stopped"!==i.dragStatus||(t.DD.justDragged=!0,e.Konva._mouseListenClick=!1,e.Konva._touchListenClick=!1,e.Konva._pointerListenClick=!1,i.dragStatus="stopped");const s=i.node.getLayer()||i.node instanceof e.Konva.Stage&&i.node;s&&-1===r.indexOf(s)&&r.push(s)})),r.forEach((t=>{t.draw()}))},_endDragAfter(e){t.DD._dragElements.forEach(((n,r)=>{"stopped"===n.dragStatus&&n.node.fire("dragend",{type:"dragend",target:n.node,evt:e},!0),"dragging"!==n.dragStatus&&t.DD._dragElements.delete(r)}))}},e.Konva.isBrowser&&(window.addEventListener("mouseup",t.DD._endDragBefore,!0),window.addEventListener("touchend",t.DD._endDragBefore,!0),window.addEventListener("mousemove",t.DD._drag),window.addEventListener("touchmove",t.DD._drag),window.addEventListener("mouseup",t.DD._endDragAfter,!1),window.addEventListener("touchend",t.DD._endDragAfter,!1))}(Tg)),Tg}function Fg(){if(Og)return yg;Og=1,Object.defineProperty(yg,"__esModule",{value:!0}),yg.Node=void 0;const t=pg(),e=wg(),n=Pg(),r=ug(),i=Rg(),o=_g();var a="absoluteOpacity",s="allEventListeners",l="absoluteTransform",c="absoluteScale",h="canvas",u="listening",d="mouseenter",f="mouseleave",p="Shape",g=" ",m="stage",y="transform",v="visible",b=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(g);let _=1,w=class e{constructor(t){this._id=_++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(t),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(t){t!==y&&t!==l||!this._cache.get(t)?t?this._cache.delete(t):this._cache.clear():this._cache.get(t).dirty=!0}_getCache(t,e){var n=this._cache.get(t);return(void 0===n||(t===y||t===l)&&!0===n.dirty)&&(n=e.call(this),this._cache.set(t,n)),n}_calculate(t,e,n){if(!this._attachedDepsListeners.get(t)){const n=e.map((t=>t+"Change.konva")).join(g);this.on(n,(()=>{this._clearCache(t)})),this._attachedDepsListeners.set(t,!0)}return this._getCache(t,n)}_getCanvasCache(){return this._cache.get(h)}_clearSelfAndDescendantCache(t){this._clearCache(t),t===l&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(h)){const{scene:e,filter:n,hit:r}=this._cache.get(h);t.Util.releaseCanvas(e,n,r),this._cache.delete(h)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(e){var r=e||{},i={};void 0!==r.x&&void 0!==r.y&&void 0!==r.width&&void 0!==r.height||(i=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()||void 0}));var o=Math.ceil(r.width||i.width),s=Math.ceil(r.height||i.height),l=r.pixelRatio,u=void 0===r.x?Math.floor(i.x):r.x,d=void 0===r.y?Math.floor(i.y):r.y,f=r.offset||0,p=r.drawBorder||!1,g=r.hitCanvasPixelRatio||1;if(!o||!s)return void t.Util.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");o+=2*f+(Math.abs(Math.round(i.x)-u)>.5?1:0),s+=2*f+(Math.abs(Math.round(i.y)-d)>.5?1:0),u-=f,d-=f;var m=new n.SceneCanvas({pixelRatio:l,width:o,height:s}),y=new n.SceneCanvas({pixelRatio:l,width:0,height:0,willReadFrequently:!0}),v=new n.HitCanvas({pixelRatio:g,width:o,height:s}),b=m.getContext(),_=v.getContext();return v.isCache=!0,m.isCache=!0,this._cache.delete(h),this._filterUpToDate=!1,!1===r.imageSmoothingEnabled&&(m.getContext()._context.imageSmoothingEnabled=!1,y.getContext()._context.imageSmoothingEnabled=!1),b.save(),_.save(),b.translate(-u,-d),_.translate(-u,-d),this._isUnderCache=!0,this._clearSelfAndDescendantCache(a),this._clearSelfAndDescendantCache(c),this.drawScene(m,this),this.drawHit(v,this),this._isUnderCache=!1,b.restore(),_.restore(),p&&(b.save(),b.beginPath(),b.rect(0,0,o,s),b.closePath(),b.setAttr("strokeStyle","red"),b.setAttr("lineWidth",5),b.stroke(),b.restore()),this._cache.set(h,{scene:m,filter:y,hit:v,x:u,y:d}),this._requestDraw(),this}isCached(){return this._cache.has(h)}getClientRect(t){throw new Error('abstract "getClientRect" method call')}_transformedRect(t,e){var n=[{x:t.x,y:t.y},{x:t.x+t.width,y:t.y},{x:t.x+t.width,y:t.y+t.height},{x:t.x,y:t.y+t.height}],r=1/0,i=1/0,o=-1/0,a=-1/0,s=this.getAbsoluteTransform(e);return n.forEach((function(t){var e=s.point(t);void 0===r&&(r=o=e.x,i=a=e.y),r=Math.min(r,e.x),i=Math.min(i,e.y),o=Math.max(o,e.x),a=Math.max(a,e.y)})),{x:r,y:i,width:o-r,height:a-i}}_drawCachedSceneCanvas(t){t.save(),t._applyOpacity(this),t._applyGlobalCompositeOperation(this);const e=this._getCanvasCache();t.translate(e.x,e.y);var n=this._getCachedSceneCanvas(),r=n.pixelRatio;t.drawImage(n._canvas,0,0,n.width/r,n.height/r),t.restore()}_drawCachedHitCanvas(t){var e=this._getCanvasCache(),n=e.hit;t.save(),t.translate(e.x,e.y),t.drawImage(n._canvas,0,0,n.width/n.pixelRatio,n.height/n.pixelRatio),t.restore()}_getCachedSceneCanvas(){var e,n,r,i,o=this.filters(),a=this._getCanvasCache(),s=a.scene,l=a.filter,c=l.getContext();if(o){if(!this._filterUpToDate){var h=s.pixelRatio;l.setSize(s.width/s.pixelRatio,s.height/s.pixelRatio);try{for(e=o.length,c.clear(),c.drawImage(s._canvas,0,0,s.getWidth()/h,s.getHeight()/h),n=c.getImageData(0,0,l.getWidth(),l.getHeight()),r=0;r<e;r++)"function"==typeof(i=o[r])?(i.call(this,n),c.putImageData(n,0,0)):t.Util.error("Filter should be type of function, but got "+typeof i+" instead. Please check correct filters")}catch(bv){t.Util.error("Unable to apply filter. "+bv.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return l}return s}on(t,e){if(this._cache&&this._cache.delete(s),3===arguments.length)return this._delegate.apply(this,arguments);var n,r,i,o,a=t.split(g),l=a.length;for(n=0;n<l;n++)i=(r=a[n].split("."))[0],o=r[1]||"",this.eventListeners[i]||(this.eventListeners[i]=[]),this.eventListeners[i].push({name:o,handler:e});return this}off(t,e){var n,r,i,o,a,l=(t||"").split(g),c=l.length;if(this._cache&&this._cache.delete(s),!t)for(r in this.eventListeners)this._off(r);for(n=0;n<c;n++)if(o=(i=l[n].split("."))[0],a=i[1],o)this.eventListeners[o]&&this._off(o,a,e);else for(r in this.eventListeners)this._off(r,a,e);return this}dispatchEvent(t){var e={target:this,type:t.type,evt:t};return this.fire(t.type,e),this}addEventListener(t,e){return this.on(t,(function(t){e.call(this,t.evt)})),this}removeEventListener(t){return this.off(t),this}_delegate(e,n,r){var i=this;this.on(e,(function(e){for(var o=e.target.findAncestors(n,!0,i),a=0;a<o.length;a++)(e=t.Util.cloneObject(e)).currentTarget=o[a],r.call(o[a],e)}))}remove(){return this.isDragging()&&this.stopDrag(),i.DD._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(l),this._clearSelfAndDescendantCache(a),this._clearSelfAndDescendantCache(c),this._clearSelfAndDescendantCache(m),this._clearSelfAndDescendantCache(v),this._clearSelfAndDescendantCache(u)}_remove(){this._clearCaches();var t=this.getParent();t&&t.children&&(t.children.splice(this.index,1),t._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(e){var n="get"+t.Util._capitalize(e);return t.Util._isFunction(this[n])?this[n]():this.attrs[e]}getAncestors(){for(var t=this.getParent(),e=[];t;)e.push(t),t=t.getParent();return e}getAttrs(){return this.attrs||{}}setAttrs(e){return this._batchTransformChanges((()=>{var n,r;if(!e)return this;for(n in e)"children"!==n&&(r="set"+t.Util._capitalize(n),t.Util._isFunction(this[r])?this[r](e[n]):this._setAttr(n,e[n]))})),this}isListening(){return this._getCache(u,this._isListening)}_isListening(t){if(!this.listening())return!1;const e=this.getParent();return!e||e===t||this===t||e._isListening(t)}isVisible(){return this._getCache(v,this._isVisible)}_isVisible(t){if(!this.visible())return!1;const e=this.getParent();return!e||e===t||this===t||e._isVisible(t)}shouldDrawHit(t,e=!1){if(t)return this._isVisible(t)&&this._isListening(t);var n=this.getLayer(),o=!1;i.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&("Stage"===t.node.nodeType||t.node.getLayer()===n)&&(o=!0)}));var a=!e&&!r.Konva.hitOnDragEnabled&&o;return this.isListening()&&this.isVisible()&&!a}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){var t,e,n,r,i=this.getDepth(),o=this,a=0;const s=this.getStage();return"Stage"!==o.nodeType&&s&&function s(l){for(t=[],e=l.length,n=0;n<e;n++)r=l[n],a++,r.nodeType!==p&&(t=t.concat(r.getChildren().slice())),r._id===o._id&&(n=e);t.length>0&&t[0].getDepth()<=i&&s(t)}(s.getChildren()),a}getDepth(){for(var t=0,e=this.parent;e;)t++,e=e.parent;return t}_batchTransformChanges(t){this._batchingTransformChange=!0,t(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(y),this._clearSelfAndDescendantCache(l)),this._needClearTransformCache=!1}setPosition(t){return this._batchTransformChanges((()=>{this.x(t.x),this.y(t.y)})),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){const t=this.getStage();if(!t)return null;var e=t.getPointerPosition();if(!e)return null;var n=this.getAbsoluteTransform().copy();return n.invert(),n.point(e)}getAbsolutePosition(e){let n=!1,r=this.parent;for(;r;){if(r.isCached()){n=!0;break}r=r.parent}n&&!e&&(e=!0);var i=this.getAbsoluteTransform(e).getMatrix(),o=new t.Transform,a=this.offset();return o.m=i.slice(),o.translate(a.x,a.y),o.getTranslation()}setAbsolutePosition(t){const{x:e,y:n,...r}=this._clearTransform();this.attrs.x=e,this.attrs.y=n,this._clearCache(y);var i=this._getAbsoluteTransform().copy();return i.invert(),i.translate(t.x,t.y),t={x:this.attrs.x+i.getTranslation().x,y:this.attrs.y+i.getTranslation().y},this._setTransform(r),this.setPosition({x:t.x,y:t.y}),this._clearCache(y),this._clearSelfAndDescendantCache(l),this}_setTransform(t){var e;for(e in t)this.attrs[e]=t[e]}_clearTransform(){var t={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,t}move(t){var e=t.x,n=t.y,r=this.x(),i=this.y();return void 0!==e&&(r+=e),void 0!==n&&(i+=n),this.setPosition({x:r,y:i}),this}_eachAncestorReverse(t,e){var n,r,i=[],o=this.getParent();if(!e||e._id!==this._id){for(i.unshift(this);o&&(!e||o._id!==e._id);)i.unshift(o),o=o.parent;for(n=i.length,r=0;r<n;r++)t(i[r])}}rotate(t){return this.rotation(this.rotation()+t),this}moveToTop(){if(!this.parent)return t.Util.warn("Node has no parent. moveToTop function is ignored."),!1;var e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0)}moveUp(){if(!this.parent)return t.Util.warn("Node has no parent. moveUp function is ignored."),!1;var e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.splice(e+1,0,this),this.parent._setChildrenIndices(),!0)}moveDown(){if(!this.parent)return t.Util.warn("Node has no parent. moveDown function is ignored."),!1;var e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.splice(e-1,0,this),this.parent._setChildrenIndices(),!0)}moveToBottom(){if(!this.parent)return t.Util.warn("Node has no parent. moveToBottom function is ignored."),!1;var e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)}setZIndex(e){if(!this.parent)return t.Util.warn("Node has no parent. zIndex parameter is ignored."),this;(e<0||e>=this.parent.children.length)&&t.Util.warn("Unexpected value "+e+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");var n=this.index;return this.parent.children.splice(n,1),this.parent.children.splice(e,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(a,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){var t=this.opacity(),e=this.getParent();return e&&!e._isUnderCache&&(t*=e.getAbsoluteOpacity()),t}moveTo(t){return this.getParent()!==t&&(this._remove(),t.add(this)),this}toObject(){var e,n,r,i,o=this.getAttrs();const a={attrs:{},className:this.getClassName()};for(e in o)n=o[e],t.Util.isObject(n)&&!t.Util._isPlainObject(n)&&!t.Util._isArray(n)||(r="function"==typeof this[e]&&this[e],delete o[e],i=r?r.call(this):null,o[e]=n,i!==n&&(a.attrs[e]=n));return t.Util._prepareToStringify(a)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(t,e,n){var r=[];e&&this._isMatch(t)&&r.push(this);for(var i=this.parent;i;){if(i===n)return r;i._isMatch(t)&&r.push(i),i=i.parent}return r}isAncestorOf(t){return!1}findAncestor(t,e,n){return this.findAncestors(t,e,n)[0]}_isMatch(e){if(!e)return!1;if("function"==typeof e)return e(this);var n,r,i=e.replace(/ /g,"").split(","),o=i.length;for(n=0;n<o;n++)if(r=i[n],t.Util.isValidSelector(r)||(t.Util.warn('Selector "'+r+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),t.Util.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),t.Util.warn("Konva is awesome, right?")),"#"===r.charAt(0)){if(this.id()===r.slice(1))return!0}else if("."===r.charAt(0)){if(this.hasName(r.slice(1)))return!0}else if(this.className===r||this.nodeType===r)return!0;return!1}getLayer(){var t=this.getParent();return t?t.getLayer():null}getStage(){return this._getCache(m,this._getStage)}_getStage(){var t=this.getParent();return t?t.getStage():null}fire(t,e={},n){return e.target=e.target||this,n?this._fireAndBubble(t,e):this._fire(t,e),this}getAbsoluteTransform(t){return t?this._getAbsoluteTransform(t):this._getCache(l,this._getAbsoluteTransform)}_getAbsoluteTransform(e){var n;if(e)return n=new t.Transform,this._eachAncestorReverse((function(t){var e=t.transformsEnabled();"all"===e?n.multiply(t.getTransform()):"position"===e&&n.translate(t.x()-t.offsetX(),t.y()-t.offsetY())}),e),n;n=this._cache.get(l)||new t.Transform,this.parent?this.parent.getAbsoluteTransform().copyInto(n):n.reset();var r=this.transformsEnabled();if("all"===r)n.multiply(this.getTransform());else if("position"===r){const t=this.attrs.x||0,e=this.attrs.y||0,r=this.attrs.offsetX||0,i=this.attrs.offsetY||0;n.translate(t-r,e-i)}return n.dirty=!1,n}getAbsoluteScale(t){for(var e=this;e;)e._isUnderCache&&(t=e),e=e.getParent();const n=this.getAbsoluteTransform(t).decompose();return{x:n.scaleX,y:n.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(y,this._getTransform)}_getTransform(){var e,n,i=this._cache.get(y)||new t.Transform;i.reset();var o=this.x(),a=this.y(),s=r.Konva.getAngle(this.rotation()),l=null!==(e=this.attrs.scaleX)&&void 0!==e?e:1,c=null!==(n=this.attrs.scaleY)&&void 0!==n?n:1,h=this.attrs.skewX||0,u=this.attrs.skewY||0,d=this.attrs.offsetX||0,f=this.attrs.offsetY||0;return 0===o&&0===a||i.translate(o,a),0!==s&&i.rotate(s),0===h&&0===u||i.skew(h,u),1===l&&1===c||i.scale(l,c),0===d&&0===f||i.translate(-1*d,-1*f),i.dirty=!1,i}clone(e){var n,r,i,o,a,s=t.Util.cloneObject(this.attrs);for(n in e)s[n]=e[n];var l=new this.constructor(s);for(n in this.eventListeners)for(i=(r=this.eventListeners[n]).length,o=0;o<i;o++)(a=r[o]).name.indexOf("konva")<0&&(l.eventListeners[n]||(l.eventListeners[n]=[]),l.eventListeners[n].push(a));return l}_toKonvaCanvas(t){t=t||{};var e=this.getClientRect(),r=this.getStage(),i=void 0!==t.x?t.x:Math.floor(e.x),o=void 0!==t.y?t.y:Math.floor(e.y),a=t.pixelRatio||1,s=new n.SceneCanvas({width:t.width||Math.ceil(e.width)||(r?r.width():0),height:t.height||Math.ceil(e.height)||(r?r.height():0),pixelRatio:a}),l=s.getContext();const c=new n.SceneCanvas({width:s.width,height:s.height,pixelRatio:s.pixelRatio});return!1===t.imageSmoothingEnabled&&(l._context.imageSmoothingEnabled=!1),l.save(),(i||o)&&l.translate(-1*i,-1*o),this.drawScene(s,void 0,c),l.restore(),s}toCanvas(t){return this._toKonvaCanvas(t)._canvas}toDataURL(t){var e=(t=t||{}).mimeType||null,n=t.quality||null,r=this._toKonvaCanvas(t).toDataURL(e,n);return t.callback&&t.callback(r),r}toImage(e){return new Promise(((n,r)=>{try{const r=null==e?void 0:e.callback;r&&delete e.callback,t.Util._urlToImage(this.toDataURL(e),(function(t){n(t),null==r||r(t)}))}catch(i){r(i)}}))}toBlob(t){return new Promise(((e,n)=>{try{const n=null==t?void 0:t.callback;n&&delete t.callback,this.toCanvas(t).toBlob((t=>{e(t),null==n||n(t)}),null==t?void 0:t.mimeType,null==t?void 0:t.quality)}catch(r){n(r)}}))}setSize(t){return this.width(t.width),this.height(t.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():r.Konva.dragDistance}_off(t,e,n){var r,i,o,a=this.eventListeners[t];for(r=0;r<a.length;r++)if(i=a[r].name,o=a[r].handler,!("konva"===i&&"konva"!==e||e&&i!==e||n&&n!==o)){if(a.splice(r,1),0===a.length){delete this.eventListeners[t];break}r--}}_fireChangeEvent(t,e,n){this._fire(t+"Change",{oldVal:e,newVal:n})}addName(t){if(!this.hasName(t)){var e=this.name(),n=e?e+" "+t:t;this.name(n)}return this}hasName(t){if(!t)return!1;const e=this.name();return!!e&&-1!==(e||"").split(/\s/g).indexOf(t)}removeName(t){var e=(this.name()||"").split(/\s/g),n=e.indexOf(t);return-1!==n&&(e.splice(n,1),this.name(e.join(" "))),this}setAttr(e,n){var r=this["set"+t.Util._capitalize(e)];return t.Util._isFunction(r)?r.call(this,n):this._setAttr(e,n),this}_requestDraw(){if(r.Konva.autoDrawEnabled){const t=this.getLayer()||this.getStage();null==t||t.batchDraw()}}_setAttr(e,n){var r=this.attrs[e];(r!==n||t.Util.isObject(n))&&(null==n?delete this.attrs[e]:this.attrs[e]=n,this._shouldFireChangeEvents&&this._fireChangeEvent(e,r,n),this._requestDraw())}_setComponentAttr(t,e,n){var r;void 0!==n&&((r=this.attrs[t])||(this.attrs[t]=this.getAttr(t)),this.attrs[t][e]=n,this._fireChangeEvent(t,r,n))}_fireAndBubble(t,e,n){if(e&&this.nodeType===p&&(e.target=this),!((t===d||t===f)&&(n&&(this===n||this.isAncestorOf&&this.isAncestorOf(n))||"Stage"===this.nodeType&&!n))){this._fire(t,e);var r=(t===d||t===f)&&n&&n.isAncestorOf&&n.isAncestorOf(this)&&!n.isAncestorOf(this.parent);(e&&!e.cancelBubble||!e)&&this.parent&&this.parent.isListening()&&!r&&(n&&n.parent?this._fireAndBubble.call(this.parent,t,e,n):this._fireAndBubble.call(this.parent,t,e))}}_getProtoListeners(t){var e,n,r;const i=null!==(e=this._cache.get(s))&&void 0!==e?e:{};let o=null==i?void 0:i[t];if(void 0===o){o=[];let e=Object.getPrototypeOf(this);for(;e;){const i=null!==(r=null===(n=e.eventListeners)||void 0===n?void 0:n[t])&&void 0!==r?r:[];o.push(...i),e=Object.getPrototypeOf(e)}i[t]=o,this._cache.set(s,i)}return o}_fire(t,e){(e=e||{}).currentTarget=this,e.type=t;const n=this._getProtoListeners(t);if(n)for(var r=0;r<n.length;r++)n[r].handler.call(this,e);const i=this.eventListeners[t];if(i)for(r=0;r<i.length;r++)i[r].handler.call(this,e)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(t){var e=t?t.pointerId:void 0,n=this.getStage(),r=this.getAbsolutePosition();if(n){var o=n._getPointerById(e)||n._changedPointerPositions[0]||r;i.DD._dragElements.set(this._id,{node:this,startPointerPos:o,offset:{x:o.x-r.x,y:o.y-r.y},dragStatus:"ready",pointerId:e})}}startDrag(t,e=!0){i.DD._dragElements.has(this._id)||this._createDragElement(t);i.DD._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:t&&t.evt},e)}_setDragPosition(e,n){const r=this.getStage()._getPointerById(n.pointerId);if(r){var i={x:r.x-n.offset.x,y:r.y-n.offset.y},o=this.dragBoundFunc();if(void 0!==o){const n=o.call(this,i,e);n?i=n:t.Util.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}this._lastPos&&this._lastPos.x===i.x&&this._lastPos.y===i.y||(this.setAbsolutePosition(i),this._requestDraw()),this._lastPos=i}}stopDrag(t){const e=i.DD._dragElements.get(this._id);e&&(e.dragStatus="stopped"),i.DD._endDragBefore(t),i.DD._endDragAfter(t)}setDraggable(t){this._setAttr("draggable",t),this._dragChange()}isDragging(){const t=i.DD._dragElements.get(this._id);return!!t&&"dragging"===t.dragStatus}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",(function(t){if((!(void 0!==t.evt.button)||r.Konva.dragButtons.indexOf(t.evt.button)>=0)&&!this.isDragging()){var e=!1;i.DD._dragElements.forEach((t=>{this.isAncestorOf(t.node)&&(e=!0)})),e||this._createDragElement(t)}}))}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{if(this._dragCleanup(),!this.getStage())return;const t=i.DD._dragElements.get(this._id),e=t&&"dragging"===t.dragStatus,n=t&&"ready"===t.dragStatus;e?this.stopDrag():n&&i.DD._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(e={x:0,y:0}){const n=this.getStage();if(!n)return!1;const r={x:-e.x,y:-e.y,width:n.width()+2*e.x,height:n.height()+2*e.y};return t.Util.haveIntersection(r,this.getClientRect())}static create(e,n){return t.Util._isString(e)&&(e=JSON.parse(e)),this._createNode(e,n)}static _createNode(n,i){var o,a,s,l=e.prototype.getClassName.call(n),c=n.children;i&&(n.attrs.container=i),r.Konva[l]||(t.Util.warn('Can not find a node with class name "'+l+'". Fallback to "Shape".'),l="Shape");if(o=new(0,r.Konva[l])(n.attrs),c)for(a=c.length,s=0;s<a;s++)o.add(e._createNode(c[s]));return o}};yg.Node=w,w.prototype.nodeType="Node",w.prototype._attrsAffectingSize=[],w.prototype.eventListeners={},w.prototype.on.call(w.prototype,b,(function(){this._batchingTransformChange?this._needClearTransformCache=!0:(this._clearCache(y),this._clearSelfAndDescendantCache(l))})),w.prototype.on.call(w.prototype,"visibleChange.konva",(function(){this._clearSelfAndDescendantCache(v)})),w.prototype.on.call(w.prototype,"listeningChange.konva",(function(){this._clearSelfAndDescendantCache(u)})),w.prototype.on.call(w.prototype,"opacityChange.konva",(function(){this._clearSelfAndDescendantCache(a)}));const S=e.Factory.addGetterSetter;return S(w,"zIndex"),S(w,"absolutePosition"),S(w,"position"),S(w,"x",0,(0,o.getNumberValidator)()),S(w,"y",0,(0,o.getNumberValidator)()),S(w,"globalCompositeOperation","source-over",(0,o.getStringValidator)()),S(w,"opacity",1,(0,o.getNumberValidator)()),S(w,"name","",(0,o.getStringValidator)()),S(w,"id","",(0,o.getStringValidator)()),S(w,"rotation",0,(0,o.getNumberValidator)()),e.Factory.addComponentsGetterSetter(w,"scale",["x","y"]),S(w,"scaleX",1,(0,o.getNumberValidator)()),S(w,"scaleY",1,(0,o.getNumberValidator)()),e.Factory.addComponentsGetterSetter(w,"skew",["x","y"]),S(w,"skewX",0,(0,o.getNumberValidator)()),S(w,"skewY",0,(0,o.getNumberValidator)()),e.Factory.addComponentsGetterSetter(w,"offset",["x","y"]),S(w,"offsetX",0,(0,o.getNumberValidator)()),S(w,"offsetY",0,(0,o.getNumberValidator)()),S(w,"dragDistance",null,(0,o.getNumberValidator)()),S(w,"width",0,(0,o.getNumberValidator)()),S(w,"height",0,(0,o.getNumberValidator)()),S(w,"listening",!0,(0,o.getBooleanValidator)()),S(w,"preventDefault",!0,(0,o.getBooleanValidator)()),S(w,"filters",null,(function(t){return this._filterUpToDate=!1,t})),S(w,"visible",!0,(0,o.getBooleanValidator)()),S(w,"transformsEnabled","all",(0,o.getStringValidator)()),S(w,"size"),S(w,"dragBoundFunc"),S(w,"draggable",!1,(0,o.getBooleanValidator)()),e.Factory.backCompat(w,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"}),yg}var Mg,Ng={};function Lg(){if(Mg)return Ng;Mg=1,Object.defineProperty(Ng,"__esModule",{value:!0}),Ng.Container=void 0;const t=wg(),e=Fg(),n=_g();let r=class extends e.Node{constructor(){super(...arguments),this.children=[]}getChildren(t){if(!t)return this.children||[];const e=this.children||[];var n=[];return e.forEach((function(e){t(e)&&n.push(e)})),n}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.remove()})),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.destroy()})),this.children=[],this._requestDraw(),this}add(...t){if(0===t.length)return this;if(t.length>1){for(var e=0;e<t.length;e++)this.add(t[e]);return this}const n=t[0];return n.getParent()?(n.moveTo(this),this):(this._validateAdd(n),n.index=this.getChildren().length,n.parent=this,n._clearCaches(),this.getChildren().push(n),this._fire("add",{child:n}),this._requestDraw(),this)}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(t){return this._generalFind(t,!1)}findOne(t){var e=this._generalFind(t,!0);return e.length>0?e[0]:void 0}_generalFind(t,e){var n=[];return this._descendants((r=>{const i=r._isMatch(t);return i&&n.push(r),!(!i||!e)})),n}_descendants(t){let e=!1;const n=this.getChildren();for(const r of n){if(e=t(r),e)return!0;if(r.hasChildren()&&(e=r._descendants(t),e))return!0}return!1}toObject(){var t=e.Node.prototype.toObject.call(this);return t.children=[],this.getChildren().forEach((e=>{t.children.push(e.toObject())})),t}isAncestorOf(t){for(var e=t.getParent();e;){if(e._id===this._id)return!0;e=e.getParent()}return!1}clone(t){var n=e.Node.prototype.clone.call(this,t);return this.getChildren().forEach((function(t){n.add(t.clone())})),n}getAllIntersections(t){var e=[];return this.find("Shape").forEach((n=>{n.isVisible()&&n.intersects(t)&&e.push(n)})),e}_clearSelfAndDescendantCache(t){var e;super._clearSelfAndDescendantCache(t),this.isCached()||null===(e=this.children)||void 0===e||e.forEach((function(e){e._clearSelfAndDescendantCache(t)}))}_setChildrenIndices(){var t;null===(t=this.children)||void 0===t||t.forEach((function(t,e){t.index=e})),this._requestDraw()}drawScene(t,e,n){var r=this.getLayer(),i=t||r&&r.getCanvas(),o=i&&i.getContext(),a=this._getCanvasCache(),s=a&&a.scene,l=i&&i.isCache;if(!this.isVisible()&&!l)return this;if(s){o.save();var c=this.getAbsoluteTransform(e).getMatrix();o.transform(c[0],c[1],c[2],c[3],c[4],c[5]),this._drawCachedSceneCanvas(o),o.restore()}else this._drawChildren("drawScene",i,e,n);return this}drawHit(t,e){if(!this.shouldDrawHit(e))return this;var n=this.getLayer(),r=t||n&&n.hitCanvas,i=r&&r.getContext(),o=this._getCanvasCache();if(o&&o.hit){i.save();var a=this.getAbsoluteTransform(e).getMatrix();i.transform(a[0],a[1],a[2],a[3],a[4],a[5]),this._drawCachedHitCanvas(i),i.restore()}else this._drawChildren("drawHit",r,e);return this}_drawChildren(t,e,n,r){var i,o=e&&e.getContext(),a=this.clipWidth(),s=this.clipHeight(),l=this.clipFunc(),c=a&&s||l;const h=n===this;if(c){o.save();var u=this.getAbsoluteTransform(n),d=u.getMatrix();let t;if(o.transform(d[0],d[1],d[2],d[3],d[4],d[5]),o.beginPath(),l)t=l.call(this,o,this);else{var f=this.clipX(),p=this.clipY();o.rect(f,p,a,s)}o.clip.apply(o,t),d=u.copy().invert().getMatrix(),o.transform(d[0],d[1],d[2],d[3],d[4],d[5])}var g=!h&&"source-over"!==this.globalCompositeOperation()&&"drawScene"===t;g&&(o.save(),o._applyGlobalCompositeOperation(this)),null===(i=this.children)||void 0===i||i.forEach((function(i){i[t](e,n,r)})),g&&o.restore(),c&&o.restore()}getClientRect(t={}){var e,n,r,i,o,a=t.skipTransform,s=t.relativeTo,l={x:1/0,y:1/0,width:0,height:0},c=this;null===(e=this.children)||void 0===e||e.forEach((function(e){if(e.visible()){var a=e.getClientRect({relativeTo:c,skipShadow:t.skipShadow,skipStroke:t.skipStroke});0===a.width&&0===a.height||(void 0===n?(n=a.x,r=a.y,i=a.x+a.width,o=a.y+a.height):(n=Math.min(n,a.x),r=Math.min(r,a.y),i=Math.max(i,a.x+a.width),o=Math.max(o,a.y+a.height)))}}));for(var h=this.find("Shape"),u=!1,d=0;d<h.length;d++){if(h[d]._isVisible(this)){u=!0;break}}return l=u&&void 0!==n?{x:n,y:r,width:i-n,height:o-r}:{x:0,y:0,width:0,height:0},a?l:this._transformedRect(l,s)}};return Ng.Container=r,t.Factory.addComponentsGetterSetter(r,"clip",["x","y","width","height"]),t.Factory.addGetterSetter(r,"clipX",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipY",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipWidth",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipHeight",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipFunc"),Ng}var Dg,Ig,jg={},Ug={};function Gg(){if(Dg)return Ug;Dg=1,Object.defineProperty(Ug,"__esModule",{value:!0}),Ug.releaseCapture=Ug.setPointerCapture=Ug.hasPointerCapture=Ug.createEvent=Ug.getCapturedShape=void 0;const t=ug(),e=new Map,n=void 0!==t.Konva._global.PointerEvent;function r(t){return{evt:t,pointerId:t.pointerId}}function i(t,i){const o=e.get(t);if(!o)return;const a=o.getStage();a&&a.content,e.delete(t),n&&o._fire("lostpointercapture",r(new PointerEvent("lostpointercapture")))}return Ug.getCapturedShape=function(t){return e.get(t)},Ug.createEvent=r,Ug.hasPointerCapture=function(t,n){return e.get(t)===n},Ug.setPointerCapture=function(t,o){i(t),o.getStage()&&(e.set(t,o),n&&o._fire("gotpointercapture",r(new PointerEvent("gotpointercapture"))))},Ug.releaseCapture=i,Ug}var Bg,Vg,Hg={},zg={};function Wg(){return Bg||(Bg=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Shape=t.shapes=void 0;const e=ug(),n=pg(),r=wg(),i=Fg(),o=_g(),a=ug(),s=Gg();var l="hasShadow",c="shadowRGBA",h="patternImage",u="linearGradient",d="radialGradient";let f;function p(){return f||(f=n.Util.createCanvasElement().getContext("2d"),f)}t.shapes={};class g extends i.Node{constructor(e){let r;for(super(e);r=n.Util.getRandomColor(),!r||r in t.shapes;);this.colorKey=r,t.shapes[r]=this}getContext(){return n.Util.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return n.Util.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(l,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(h,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){const t=p().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(t&&t.setTransform){const r=new n.Transform;r.translate(this.fillPatternX(),this.fillPatternY()),r.rotate(e.Konva.getAngle(this.fillPatternRotation())),r.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),r.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());const i=r.getMatrix(),o="undefined"==typeof DOMMatrix?{a:i[0],b:i[1],c:i[2],d:i[3],e:i[4],f:i[5]}:new DOMMatrix(i);t.setTransform(o)}return t}}_getLinearGradient(){return this._getCache(u,this.__getLinearGradient)}__getLinearGradient(){var t=this.fillLinearGradientColorStops();if(t){for(var e=p(),n=this.fillLinearGradientStartPoint(),r=this.fillLinearGradientEndPoint(),i=e.createLinearGradient(n.x,n.y,r.x,r.y),o=0;o<t.length;o+=2)i.addColorStop(t[o],t[o+1]);return i}}_getRadialGradient(){return this._getCache(d,this.__getRadialGradient)}__getRadialGradient(){var t=this.fillRadialGradientColorStops();if(t){for(var e=p(),n=this.fillRadialGradientStartPoint(),r=this.fillRadialGradientEndPoint(),i=e.createRadialGradient(n.x,n.y,this.fillRadialGradientStartRadius(),r.x,r.y,this.fillRadialGradientEndRadius()),o=0;o<t.length;o+=2)i.addColorStop(t[o],t[o+1]);return i}}getShadowRGBA(){return this._getCache(c,this._getShadowRGBA)}_getShadowRGBA(){if(this.hasShadow()){var t=n.Util.colorToRGBA(this.shadowColor());return t?"rgba("+t.r+","+t.g+","+t.b+","+t.a*(this.shadowOpacity()||1)+")":void 0}}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],(()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops())))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],(()=>this.strokeEnabled()&&this.strokeWidth()&&!(!this.stroke()&&!this.strokeLinearGradientColorStops())))}hasHitStroke(){const t=this.hitStrokeWidth();return"auto"===t?this.hasStroke():this.strokeEnabled()&&!!t}intersects(t){var e=this.getStage();if(!e)return!1;const n=e.bufferHitCanvas;n.getContext().clear(),this.drawHit(n,void 0,!0);return n.context.getImageData(Math.round(t.x),Math.round(t.y),1,1).data[3]>0}destroy(){return i.Node.prototype.destroy.call(this),delete t.shapes[this.colorKey],delete this.colorKey,this}_useBufferCanvas(t){var e;if(!(null===(e=this.attrs.perfectDrawEnabled)||void 0===e||e))return!1;const n=t||this.hasFill(),r=this.hasStroke(),i=1!==this.getAbsoluteOpacity();if(n&&r&&i)return!0;const o=this.hasShadow(),a=this.shadowForStrokeEnabled();return!!(n&&r&&o&&a)}setStrokeHitEnabled(t){n.Util.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),t?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return 0!==this.hitStrokeWidth()}getSelfRect(){var t=this.size();return{x:this._centroid?-t.width/2:0,y:this._centroid?-t.height/2:0,width:t.width,height:t.height}}getClientRect(t={}){const e=t.skipTransform,n=t.relativeTo,r=this.getSelfRect(),i=!t.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,o=r.width+i,a=r.height+i,s=!t.skipShadow&&this.hasShadow(),l=s?this.shadowOffsetX():0,c=s?this.shadowOffsetY():0,h=o+Math.abs(l),u=a+Math.abs(c),d=s&&this.shadowBlur()||0,f={width:h+2*d,height:u+2*d,x:-(i/2+d)+Math.min(l,0)+r.x,y:-(i/2+d)+Math.min(c,0)+r.y};return e?f:this._transformedRect(f,n)}drawScene(t,e,n){var r,i,o=this.getLayer(),a=t||o.getCanvas(),s=a.getContext(),l=this._getCanvasCache(),c=this.getSceneFunc(),h=this.hasShadow(),u=a.isCache,d=e===this;if(!this.isVisible()&&!d)return this;if(l){s.save();var f=this.getAbsoluteTransform(e).getMatrix();return s.transform(f[0],f[1],f[2],f[3],f[4],f[5]),this._drawCachedSceneCanvas(s),s.restore(),this}if(!c)return this;if(s.save(),this._useBufferCanvas()&&!u){r=this.getStage();const t=n||r.bufferCanvas;(i=t.getContext()).clear(),i.save(),i._applyLineJoin(this);var p=this.getAbsoluteTransform(e).getMatrix();i.transform(p[0],p[1],p[2],p[3],p[4],p[5]),c.call(this,i,this),i.restore();var g=t.pixelRatio;h&&s._applyShadow(this),s._applyOpacity(this),s._applyGlobalCompositeOperation(this),s.drawImage(t._canvas,0,0,t.width/g,t.height/g)}else{if(s._applyLineJoin(this),!d){p=this.getAbsoluteTransform(e).getMatrix();s.transform(p[0],p[1],p[2],p[3],p[4],p[5]),s._applyOpacity(this),s._applyGlobalCompositeOperation(this)}h&&s._applyShadow(this),c.call(this,s,this)}return s.restore(),this}drawHit(t,e,r=!1){if(!this.shouldDrawHit(e,r))return this;var i=this.getLayer(),o=t||i.hitCanvas,a=o&&o.getContext(),s=this.hitFunc()||this.sceneFunc(),l=this._getCanvasCache(),c=l&&l.hit;if(this.colorKey||n.Util.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),c){a.save();var h=this.getAbsoluteTransform(e).getMatrix();return a.transform(h[0],h[1],h[2],h[3],h[4],h[5]),this._drawCachedHitCanvas(a),a.restore(),this}if(!s)return this;a.save(),a._applyLineJoin(this);if(!(this===e)){var u=this.getAbsoluteTransform(e).getMatrix();a.transform(u[0],u[1],u[2],u[3],u[4],u[5])}return s.call(this,a,this),a.restore(),this}drawHitFromCache(t=0){var e,r,i,o,a,s=this._getCanvasCache(),l=this._getCachedSceneCanvas(),c=s.hit,h=c.getContext(),u=c.getWidth(),d=c.getHeight();h.clear(),h.drawImage(l._canvas,0,0,u,d);try{for(i=(r=(e=h.getImageData(0,0,u,d)).data).length,o=n.Util._hexToRgb(this.colorKey),a=0;a<i;a+=4)r[a+3]>t?(r[a]=o.r,r[a+1]=o.g,r[a+2]=o.b,r[a+3]=255):r[a+3]=0;h.putImageData(e,0,0)}catch(bv){n.Util.error("Unable to draw hit graph from cached scene canvas. "+bv.message)}return this}hasPointerCapture(t){return s.hasPointerCapture(t,this)}setPointerCapture(t){s.setPointerCapture(t,this)}releaseCapture(t){s.releaseCapture(t,this)}}t.Shape=g,g.prototype._fillFunc=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},g.prototype._strokeFunc=function(t){t.stroke()},g.prototype._fillFuncHit=function(t){t.fill()},g.prototype._strokeFuncHit=function(t){t.stroke()},g.prototype._centroid=!1,g.prototype.nodeType="Shape",(0,a._registerNode)(g),g.prototype.eventListeners={},g.prototype.on.call(g.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(l)})),g.prototype.on.call(g.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(c)})),g.prototype.on.call(g.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",(function(){this._clearCache(h)})),g.prototype.on.call(g.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",(function(){this._clearCache(u)})),g.prototype.on.call(g.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",(function(){this._clearCache(d)})),r.Factory.addGetterSetter(g,"stroke",void 0,(0,o.getStringOrGradientValidator)()),r.Factory.addGetterSetter(g,"strokeWidth",2,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillAfterStrokeEnabled",!1),r.Factory.addGetterSetter(g,"hitStrokeWidth","auto",(0,o.getNumberOrAutoValidator)()),r.Factory.addGetterSetter(g,"strokeHitEnabled",!0,(0,o.getBooleanValidator)()),r.Factory.addGetterSetter(g,"perfectDrawEnabled",!0,(0,o.getBooleanValidator)()),r.Factory.addGetterSetter(g,"shadowForStrokeEnabled",!0,(0,o.getBooleanValidator)()),r.Factory.addGetterSetter(g,"lineJoin"),r.Factory.addGetterSetter(g,"lineCap"),r.Factory.addGetterSetter(g,"sceneFunc"),r.Factory.addGetterSetter(g,"hitFunc"),r.Factory.addGetterSetter(g,"dash"),r.Factory.addGetterSetter(g,"dashOffset",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"shadowColor",void 0,(0,o.getStringValidator)()),r.Factory.addGetterSetter(g,"shadowBlur",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"shadowOpacity",1,(0,o.getNumberValidator)()),r.Factory.addComponentsGetterSetter(g,"shadowOffset",["x","y"]),r.Factory.addGetterSetter(g,"shadowOffsetX",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"shadowOffsetY",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillPatternImage"),r.Factory.addGetterSetter(g,"fill",void 0,(0,o.getStringOrGradientValidator)()),r.Factory.addGetterSetter(g,"fillPatternX",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillPatternY",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillLinearGradientColorStops"),r.Factory.addGetterSetter(g,"strokeLinearGradientColorStops"),r.Factory.addGetterSetter(g,"fillRadialGradientStartRadius",0),r.Factory.addGetterSetter(g,"fillRadialGradientEndRadius",0),r.Factory.addGetterSetter(g,"fillRadialGradientColorStops"),r.Factory.addGetterSetter(g,"fillPatternRepeat","repeat"),r.Factory.addGetterSetter(g,"fillEnabled",!0),r.Factory.addGetterSetter(g,"strokeEnabled",!0),r.Factory.addGetterSetter(g,"shadowEnabled",!0),r.Factory.addGetterSetter(g,"dashEnabled",!0),r.Factory.addGetterSetter(g,"strokeScaleEnabled",!0),r.Factory.addGetterSetter(g,"fillPriority","color"),r.Factory.addComponentsGetterSetter(g,"fillPatternOffset",["x","y"]),r.Factory.addGetterSetter(g,"fillPatternOffsetX",0,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillPatternOffsetY",0,(0,o.getNumberValidator)()),r.Factory.addComponentsGetterSetter(g,"fillPatternScale",["x","y"]),r.Factory.addGetterSetter(g,"fillPatternScaleX",1,(0,o.getNumberValidator)()),r.Factory.addGetterSetter(g,"fillPatternScaleY",1,(0,o.getNumberValidator)()),r.Factory.addComponentsGetterSetter(g,"fillLinearGradientStartPoint",["x","y"]),r.Factory.addComponentsGetterSetter(g,"strokeLinearGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(g,"fillLinearGradientStartPointX",0),r.Factory.addGetterSetter(g,"strokeLinearGradientStartPointX",0),r.Factory.addGetterSetter(g,"fillLinearGradientStartPointY",0),r.Factory.addGetterSetter(g,"strokeLinearGradientStartPointY",0),r.Factory.addComponentsGetterSetter(g,"fillLinearGradientEndPoint",["x","y"]),r.Factory.addComponentsGetterSetter(g,"strokeLinearGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(g,"fillLinearGradientEndPointX",0),r.Factory.addGetterSetter(g,"strokeLinearGradientEndPointX",0),r.Factory.addGetterSetter(g,"fillLinearGradientEndPointY",0),r.Factory.addGetterSetter(g,"strokeLinearGradientEndPointY",0),r.Factory.addComponentsGetterSetter(g,"fillRadialGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(g,"fillRadialGradientStartPointX",0),r.Factory.addGetterSetter(g,"fillRadialGradientStartPointY",0),r.Factory.addComponentsGetterSetter(g,"fillRadialGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(g,"fillRadialGradientEndPointX",0),r.Factory.addGetterSetter(g,"fillRadialGradientEndPointY",0),r.Factory.addGetterSetter(g,"fillPatternRotation",0),r.Factory.addGetterSetter(g,"fillRule",void 0,(0,o.getStringValidator)()),r.Factory.backCompat(g,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"})}(zg)),zg}function $g(){if(Vg)return Hg;Vg=1,Object.defineProperty(Hg,"__esModule",{value:!0}),Hg.Layer=void 0;const t=pg(),e=Lg(),n=Fg(),r=wg(),i=Pg(),o=_g(),a=Wg(),s=ug();var l=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],c=l.length;let h=class extends e.Container{constructor(t){super(t),this.canvas=new i.SceneCanvas,this.hitCanvas=new i.HitCanvas({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(t){return this.getContext().clear(t),this.getHitCanvas().getContext().clear(t),this}setZIndex(t){super.setZIndex(t);var e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),t<e.children.length-1?e.content.insertBefore(this.getNativeCanvasElement(),e.children[t+1].getCanvas()._canvas):e.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){n.Node.prototype.moveToTop.call(this);var t=this.getStage();return t&&t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){if(!n.Node.prototype.moveUp.call(this))return!1;var t=this.getStage();return!(!t||!t.content)&&(t.content.removeChild(this.getNativeCanvasElement()),this.index<t.children.length-1?t.content.insertBefore(this.getNativeCanvasElement(),t.children[this.index+1].getCanvas()._canvas):t.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(n.Node.prototype.moveDown.call(this)){var t=this.getStage();if(t){var e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(n.Node.prototype.moveToBottom.call(this)){var t=this.getStage();if(t){var e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){var e=this.getNativeCanvasElement();return n.Node.prototype.remove.call(this),e&&e.parentNode&&t.Util._isInDocument(e)&&e.parentNode.removeChild(e),this}getStage(){return this.parent}setSize({width:t,height:e}){return this.canvas.setSize(t,e),this.hitCanvas.setSize(t,e),this._setSmoothEnabled(),this}_validateAdd(e){var n=e.getType();"Group"!==n&&"Shape"!==n&&t.Util.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(t){return(t=t||{}).width=t.width||this.getWidth(),t.height=t.height||this.getHeight(),t.x=void 0!==t.x?t.x:this.x(),t.y=void 0!==t.y?t.y:this.y(),n.Node.prototype._toKonvaCanvas.call(this,t)}_checkVisibility(){const t=this.visible();this.canvas._canvas.style.display=t?"block":"none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){t.Util.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){t.Util.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,t.Util.requestAnimFrame((()=>{this.draw(),this._waitingForDraw=!1}))),this}getIntersection(t){if(!this.isListening()||!this.isVisible())return null;for(var e=1,n=!1;;){for(let r=0;r<c;r++){const i=l[r],o=this._getIntersection({x:t.x+i.x*e,y:t.y+i.y*e}),a=o.shape;if(a)return a;if(n=!!o.antialiased,!o.antialiased)break}if(!n)return null;e+=1}}_getIntersection(e){const n=this.hitCanvas.pixelRatio,r=this.hitCanvas.context.getImageData(Math.round(e.x*n),Math.round(e.y*n),1,1).data,i=r[3];if(255===i){const e=t.Util._rgbToHex(r[0],r[1],r[2]),n=a.shapes["#"+e];return n?{shape:n}:{antialiased:!0}}return i>0?{antialiased:!0}:{}}drawScene(t,n){var r=this.getLayer(),i=t||r&&r.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&i.getContext().clear(),e.Container.prototype.drawScene.call(this,i,n),this._fire("draw",{node:this}),this}drawHit(t,n){var r=this.getLayer(),i=t||r&&r.hitCanvas;return r&&r.clearBeforeDraw()&&r.getHitCanvas().getContext().clear(),e.Container.prototype.drawHit.call(this,i,n),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(e){t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(e)}getHitGraphEnabled(e){return t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(this.parent&&this.parent.content){var t=this.parent;!!this.hitCanvas._canvas.parentNode?t.content.removeChild(this.hitCanvas._canvas):t.content.appendChild(this.hitCanvas._canvas)}}destroy(){return t.Util.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}};return Hg.Layer=h,h.prototype.nodeType="Layer",(0,s._registerNode)(h),r.Factory.addGetterSetter(h,"imageSmoothingEnabled",!0),r.Factory.addGetterSetter(h,"clearBeforeDraw",!0),r.Factory.addGetterSetter(h,"hitGraphEnabled",!0,(0,o.getBooleanValidator)()),Hg}var Kg,qg={};var Yg,Xg={};function Qg(){if(Yg)return Xg;Yg=1,Object.defineProperty(Xg,"__esModule",{value:!0}),Xg.Group=void 0;const t=pg(),e=Lg(),n=ug();let r=class extends e.Container{_validateAdd(e){var n=e.getType();"Group"!==n&&"Shape"!==n&&t.Util.throw("You may only add groups and shapes to groups.")}};return Xg.Group=r,r.prototype.nodeType="Group",(0,n._registerNode)(r),Xg}var Jg,Zg={};function tm(){if(Jg)return Zg;Jg=1,Object.defineProperty(Zg,"__esModule",{value:!0}),Zg.Animation=void 0;const t=ug(),e=pg(),n=t.glob.performance&&t.glob.performance.now?function(){return t.glob.performance.now()}:function(){return(new Date).getTime()};let r=class t{constructor(e,r){this.id=t.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:n(),frameRate:0},this.func=e,this.setLayers(r)}setLayers(t){let e=[];return t&&(e=Array.isArray(t)?t:[t]),this.layers=e,this}getLayers(){return this.layers}addLayer(t){const e=this.layers,n=e.length;for(let r=0;r<n;r++)if(e[r]._id===t._id)return!1;return this.layers.push(t),!0}isRunning(){const e=t.animations,n=e.length;for(let t=0;t<n;t++)if(e[t].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=n(),t._addAnimation(this),this}stop(){return t._removeAnimation(this),this}_updateFrameObject(t){this.frame.timeDiff=t-this.frame.lastTime,this.frame.lastTime=t,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(t){this.animations.push(t),this._handleAnimation()}static _removeAnimation(t){const e=t.id,n=this.animations,r=n.length;for(let i=0;i<r;i++)if(n[i].id===e){this.animations.splice(i,1);break}}static _runFrames(){const t={},e=this.animations;for(let r=0;r<e.length;r++){const i=e[r],o=i.layers,a=i.func;i._updateFrameObject(n());const s=o.length;let l;if(l=!a||!1!==a.call(i,i.frame),l)for(let e=0;e<s;e++){const n=o[e];void 0!==n._id&&(t[n._id]=n)}}for(let n in t)t.hasOwnProperty(n)&&t[n].batchDraw()}static _animationLoop(){const n=t;n.animations.length?(n._runFrames(),e.Util.requestAnimFrame(n._animationLoop)):n.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,e.Util.requestAnimFrame(this._animationLoop))}};return Zg.Animation=r,r.animations=[],r.animIdCounter=0,r.animRunning=!1,Zg}var em,nm,rm={};function im(){return nm||(nm=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;const e=ug(),n=pg(),r=Fg(),i=Lg(),o=(Ig||(Ig=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Stage=t.stages=void 0;const e=pg(),n=wg(),r=Lg(),i=ug(),o=Pg(),a=Rg(),s=ug(),l=Gg();var c="mouseleave",h="mouseover",u="mouseenter",d="mousemove",f="mousedown",p="mouseup",g="pointermove",m="pointerdown",y="pointerup",v="pointercancel",b="pointerout",_="pointerleave",w="pointerover",S="pointerenter",x="contextmenu",C="touchstart",A="touchend",E="touchmove",P="touchcancel",k="wheel",O=[[u,"_pointerenter"],[f,"_pointerdown"],[d,"_pointermove"],[p,"_pointerup"],[c,"_pointerleave"],[C,"_pointerdown"],[E,"_pointermove"],[A,"_pointerup"],[P,"_pointercancel"],[h,"_pointerover"],[k,"_wheel"],[x,"_contextmenu"],[m,"_pointerdown"],[g,"_pointermove"],[y,"_pointerup"],[v,"_pointercancel"],["lostpointercapture","_lostpointercapture"]];const T={mouse:{[b]:"mouseout",[_]:c,[w]:h,[S]:u,[g]:d,[m]:f,[y]:p,[v]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[b]:"touchout",[_]:"touchleave",[w]:"touchover",[S]:"touchenter",[g]:E,[m]:C,[y]:A,[v]:P,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[b]:b,[_]:_,[w]:w,[S]:S,[g]:g,[m]:m,[y]:y,[v]:v,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},R=t=>t.indexOf("pointer")>=0?"pointer":t.indexOf("touch")>=0?"touch":"mouse",F=t=>{const e=R(t);return"pointer"===e?i.Konva.pointerEventsEnabled&&T.pointer:"touch"===e?T.touch:"mouse"===e?T.mouse:void 0};function M(t={}){return(t.clipFunc||t.clipWidth||t.clipHeight)&&e.Util.warn("Stage does not support clipping. Please use clip for Layers or Groups."),t}t.stages=[];class N extends r.Container{constructor(e){super(M(e)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),t.stages.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",(()=>{M(this.attrs)})),this._checkVisibility()}_validateAdd(t){const n="Layer"===t.getType(),r="FastLayer"===t.getType();n||r||e.Util.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;const t=this.visible()?"":"none";this.content.style.display=t}setContainer(t){if("string"==typeof t){if("."===t.charAt(0)){var e=t.slice(1);t=document.getElementsByClassName(e)[0]}else{var n;n="#"!==t.charAt(0)?t:t.slice(1),t=document.getElementById(n)}if(!t)throw"Can not find container in document with id "+n}return this._setAttr("container",t),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),t.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){var t,e=this.children,n=e.length;for(t=0;t<n;t++)e[t].clear();return this}clone(t){return t||(t={}),t.container="undefined"!=typeof document&&document.createElement("div"),r.Container.prototype.clone.call(this,t)}destroy(){super.destroy();var n=this.content;n&&e.Util._isInDocument(n)&&this.container().removeChild(n);var r=t.stages.indexOf(this);return r>-1&&t.stages.splice(r,1),e.Util.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){const t=this._pointerPositions[0]||this._changedPointerPositions[0];return t?{x:t.x,y:t.y}:(e.Util.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)}_getPointerById(t){return this._pointerPositions.find((e=>e.id===t))}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(t){(t=t||{}).x=t.x||0,t.y=t.y||0,t.width=t.width||this.width(),t.height=t.height||this.height();var e=new o.SceneCanvas({width:t.width,height:t.height,pixelRatio:t.pixelRatio||1}),n=e.getContext()._context,r=this.children;return(t.x||t.y)&&n.translate(-1*t.x,-1*t.y),r.forEach((function(e){if(e.isVisible()){var r=e._toKonvaCanvas(t);n.drawImage(r._canvas,t.x,t.y,r.getWidth()/r.getPixelRatio(),r.getHeight()/r.getPixelRatio())}})),e}getIntersection(t){if(!t)return null;var e,n=this.children;for(e=n.length-1;e>=0;e--){const r=n[e].getIntersection(t);if(r)return r}return null}_resizeDOM(){var t=this.width(),e=this.height();this.content&&(this.content.style.width=t+"px",this.content.style.height=e+"px"),this.bufferCanvas.setSize(t,e),this.bufferHitCanvas.setSize(t,e),this.children.forEach((n=>{n.setSize({width:t,height:e}),n.draw()}))}add(t,...n){if(arguments.length>1){for(var r=0;r<arguments.length;r++)this.add(arguments[r]);return this}super.add(t);var o=this.children.length;return o>5&&e.Util.warn("The stage has "+o+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),t.setSize({width:this.width(),height:this.height()}),t.draw(),i.Konva.isBrowser&&this.content.appendChild(t.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(t){return l.hasPointerCapture(t,this)}setPointerCapture(t){l.setPointerCapture(t,this)}releaseCapture(t){l.releaseCapture(t,this)}getLayers(){return this.children}_bindContentEvents(){i.Konva.isBrowser&&O.forEach((([t,e])=>{this.content.addEventListener(t,(t=>{this[e](t)}),{passive:!1})}))}_pointerenter(t){this.setPointersPositions(t);const e=F(t.type);e&&this._fire(e.pointerenter,{evt:t,target:this,currentTarget:this})}_pointerover(t){this.setPointersPositions(t);const e=F(t.type);e&&this._fire(e.pointerover,{evt:t,target:this,currentTarget:this})}_getTargetShape(t){let e=this[t+"targetShape"];return e&&!e.getStage()&&(e=null),e}_pointerleave(t){const e=F(t.type),n=R(t.type);if(e){this.setPointersPositions(t);var r=this._getTargetShape(n),o=!a.DD.isDragging||i.Konva.hitOnDragEnabled;r&&o?(r._fireAndBubble(e.pointerout,{evt:t}),r._fireAndBubble(e.pointerleave,{evt:t}),this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this[n+"targetShape"]=null):o&&(this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this._fire(e.pointerout,{evt:t,target:this,currentTarget:this})),this.pointerPos=null,this._pointerPositions=[]}}_pointerdown(t){const e=F(t.type),n=R(t.type);if(e){this.setPointersPositions(t);var r=!1;this._changedPointerPositions.forEach((o=>{var s=this.getIntersection(o);if(a.DD.justDragged=!1,i.Konva["_"+n+"ListenClick"]=!0,!s||!s.isListening())return;i.Konva.capturePointerEventsEnabled&&s.setPointerCapture(o.id),this[n+"ClickStartShape"]=s,s._fireAndBubble(e.pointerdown,{evt:t,pointerId:o.id}),r=!0;const l=t.type.indexOf("touch")>=0;s.preventDefault()&&t.cancelable&&l&&t.preventDefault()})),r||this._fire(e.pointerdown,{evt:t,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}}_pointermove(t){const e=F(t.type),n=R(t.type);if(!e)return;if(a.DD.isDragging&&a.DD.node.preventDefault()&&t.cancelable&&t.preventDefault(),this.setPointersPositions(t),a.DD.isDragging&&!i.Konva.hitOnDragEnabled)return;var r={};let o=!1;var s=this._getTargetShape(n);this._changedPointerPositions.forEach((i=>{const a=l.getCapturedShape(i.id)||this.getIntersection(i),c=i.id,h={evt:t,pointerId:c};var u=s!==a;if(u&&s&&(s._fireAndBubble(e.pointerout,{...h},a),s._fireAndBubble(e.pointerleave,{...h},a)),a){if(r[a._id])return;r[a._id]=!0}a&&a.isListening()?(o=!0,u&&(a._fireAndBubble(e.pointerover,{...h},s),a._fireAndBubble(e.pointerenter,{...h},s),this[n+"targetShape"]=a),a._fireAndBubble(e.pointermove,{...h})):s&&(this._fire(e.pointerover,{evt:t,target:this,currentTarget:this,pointerId:c}),this[n+"targetShape"]=null)})),o||this._fire(e.pointermove,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(t){const e=F(t.type),n=R(t.type);if(!e)return;this.setPointersPositions(t);const r=this[n+"ClickStartShape"],o=this[n+"ClickEndShape"];var s={};let c=!1;this._changedPointerPositions.forEach((h=>{const u=l.getCapturedShape(h.id)||this.getIntersection(h);if(u){if(u.releaseCapture(h.id),s[u._id])return;s[u._id]=!0}const d=h.id,f={evt:t,pointerId:d};let p=!1;i.Konva["_"+n+"InDblClickWindow"]?(p=!0,clearTimeout(this[n+"DblTimeout"])):a.DD.justDragged||(i.Konva["_"+n+"InDblClickWindow"]=!0,clearTimeout(this[n+"DblTimeout"])),this[n+"DblTimeout"]=setTimeout((function(){i.Konva["_"+n+"InDblClickWindow"]=!1}),i.Konva.dblClickWindow),u&&u.isListening()?(c=!0,this[n+"ClickEndShape"]=u,u._fireAndBubble(e.pointerup,{...f}),i.Konva["_"+n+"ListenClick"]&&r&&r===u&&(u._fireAndBubble(e.pointerclick,{...f}),p&&o&&o===u&&u._fireAndBubble(e.pointerdblclick,{...f}))):(this[n+"ClickEndShape"]=null,i.Konva["_"+n+"ListenClick"]&&this._fire(e.pointerclick,{evt:t,target:this,currentTarget:this,pointerId:d}),p&&this._fire(e.pointerdblclick,{evt:t,target:this,currentTarget:this,pointerId:d}))})),c||this._fire(e.pointerup,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),i.Konva["_"+n+"ListenClick"]=!1,t.cancelable&&"touch"!==n&&t.preventDefault()}_contextmenu(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(x,{evt:t}):this._fire(x,{evt:t,target:this,currentTarget:this})}_wheel(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(k,{evt:t}):this._fire(k,{evt:t,target:this,currentTarget:this})}_pointercancel(t){this.setPointersPositions(t);const e=l.getCapturedShape(t.pointerId)||this.getIntersection(this.getPointerPosition());e&&e._fireAndBubble(y,l.createEvent(t)),l.releaseCapture(t.pointerId)}_lostpointercapture(t){l.releaseCapture(t.pointerId)}setPointersPositions(t){var n=this._getContentPosition(),r=null,i=null;void 0!==(t=t||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(t.touches,(t=>{this._pointerPositions.push({id:t.identifier,x:(t.clientX-n.left)/n.scaleX,y:(t.clientY-n.top)/n.scaleY})})),Array.prototype.forEach.call(t.changedTouches||t.touches,(t=>{this._changedPointerPositions.push({id:t.identifier,x:(t.clientX-n.left)/n.scaleX,y:(t.clientY-n.top)/n.scaleY})}))):(r=(t.clientX-n.left)/n.scaleX,i=(t.clientY-n.top)/n.scaleY,this.pointerPos={x:r,y:i},this._pointerPositions=[{x:r,y:i,id:e.Util._getFirstPointerId(t)}],this._changedPointerPositions=[{x:r,y:i,id:e.Util._getFirstPointerId(t)}])}_setPointerPosition(t){e.Util.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(t)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};var t=this.content.getBoundingClientRect();return{top:t.top,left:t.left,scaleX:t.width/this.content.clientWidth||1,scaleY:t.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new o.SceneCanvas({width:this.width(),height:this.height()}),this.bufferHitCanvas=new o.HitCanvas({pixelRatio:1,width:this.width(),height:this.height()}),i.Konva.isBrowser){var t=this.container();if(!t)throw"Stage has no container. A container is required.";t.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),t.appendChild(this.content),this._resizeDOM()}}cache(){return e.Util.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach((function(t){t.batchDraw()})),this}}t.Stage=N,N.prototype.nodeType="Stage",(0,s._registerNode)(N),n.Factory.addGetterSetter(N,"container")}(jg)),jg),a=$g(),s=function(){if(Kg)return qg;Kg=1,Object.defineProperty(qg,"__esModule",{value:!0}),qg.FastLayer=void 0;const t=pg(),e=$g(),n=ug();let r=class extends e.Layer{constructor(e){super(e),this.listening(!1),t.Util.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}};return qg.FastLayer=r,r.prototype.nodeType="FastLayer",(0,n._registerNode)(r),qg}(),l=Qg(),c=Rg(),h=Wg(),u=tm(),d=(em||(em=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Easings=t.Tween=void 0;const e=pg(),n=tm(),r=Fg(),i=ug();var o={node:1,duration:1,easing:1,onFinish:1,yoyo:1},a=0,s=["fill","stroke","shadowColor"];class l{constructor(t,e,n,r,i,o,a){this.prop=t,this.propFunc=e,this.begin=r,this._pos=r,this.duration=o,this._change=0,this.prevPos=0,this.yoyo=a,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=n,this._change=i-this.begin,this.pause()}fire(t){var e=this[t];e&&e()}setTime(t){t>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():t<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=t,this.update())}getTime(){return this._time}setPosition(t){this.prevPos=this._pos,this.propFunc(t),this._pos=t}getPosition(t){return void 0===t&&(t=this._time),this.func(t,this.begin,this._change,this.duration)}play(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(t){this.pause(),this._time=t,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){var t=this.getTimer()-this._startTime;2===this.state?this.setTime(t):3===this.state&&this.setTime(this.duration-t)}pause(){this.state=1,this.fire("onPause")}getTimer(){return(new Date).getTime()}}class c{constructor(r){var s,h,u=this,d=r.node,f=d._id,p=r.easing||t.Easings.Linear,g=!!r.yoyo;s=void 0===r.duration?.3:0===r.duration?.001:r.duration,this.node=d,this._id=a++;var m=d.getLayer()||(d instanceof i.Konva.Stage?d.getLayers():null);for(h in m||e.Util.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new n.Animation((function(){u.tween.onEnterFrame()}),m),this.tween=new l(h,(function(t){u._tweenFunc(t)}),p,0,1,1e3*s,g),this._addListeners(),c.attrs[f]||(c.attrs[f]={}),c.attrs[f][this._id]||(c.attrs[f][this._id]={}),c.tweens[f]||(c.tweens[f]={}),r)void 0===o[h]&&this._addAttr(h,r[h]);this.reset(),this.onFinish=r.onFinish,this.onReset=r.onReset,this.onUpdate=r.onUpdate}_addAttr(t,n){var r,i,o,a,l,h,u,d,f=this.node,p=f._id;if((o=c.tweens[p][t])&&delete c.attrs[p][o][t],r=f.getAttr(t),e.Util._isArray(n))if(i=[],l=Math.max(n.length,r.length),"points"===t&&n.length!==r.length&&(n.length>r.length?(u=r,r=e.Util._prepareArrayForTween(r,n,f.closed())):(h=n,n=e.Util._prepareArrayForTween(n,r,f.closed()))),0===t.indexOf("fill"))for(a=0;a<l;a++)if(a%2==0)i.push(n[a]-r[a]);else{var g=e.Util.colorToRGBA(r[a]);d=e.Util.colorToRGBA(n[a]),r[a]=g,i.push({r:d.r-g.r,g:d.g-g.g,b:d.b-g.b,a:d.a-g.a})}else for(a=0;a<l;a++)i.push(n[a]-r[a]);else-1!==s.indexOf(t)?(r=e.Util.colorToRGBA(r),i={r:(d=e.Util.colorToRGBA(n)).r-r.r,g:d.g-r.g,b:d.b-r.b,a:d.a-r.a}):i=n-r;c.attrs[p][this._id][t]={start:r,diff:i,end:n,trueEnd:h,trueStart:u},c.tweens[p][t]=this._id}_tweenFunc(t){var n,r,i,o,a,l,h,u,d=this.node,f=c.attrs[d._id][this._id];for(n in f){if(i=(r=f[n]).start,o=r.diff,u=r.end,e.Util._isArray(i))if(a=[],h=Math.max(i.length,u.length),0===n.indexOf("fill"))for(l=0;l<h;l++)l%2==0?a.push((i[l]||0)+o[l]*t):a.push("rgba("+Math.round(i[l].r+o[l].r*t)+","+Math.round(i[l].g+o[l].g*t)+","+Math.round(i[l].b+o[l].b*t)+","+(i[l].a+o[l].a*t)+")");else for(l=0;l<h;l++)a.push((i[l]||0)+o[l]*t);else a=-1!==s.indexOf(n)?"rgba("+Math.round(i.r+o.r*t)+","+Math.round(i.g+o.g*t)+","+Math.round(i.b+o.b*t)+","+(i.a+o.a*t)+")":i+o*t;d.setAttr(n,a)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{var t=this.node,e=c.attrs[t._id][this._id];e.points&&e.points.trueEnd&&t.setAttr("points",e.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{var t=this.node,e=c.attrs[t._id][this._id];e.points&&e.points.trueStart&&t.points(e.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(t){return this.tween.seek(1e3*t),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){var t,e=this.node._id,n=this._id,r=c.tweens[e];for(t in this.pause(),r)delete c.tweens[e][t];delete c.attrs[e][n]}}t.Tween=c,c.attrs={},c.tweens={},r.Node.prototype.to=function(t){var e=t.onFinish;t.node=this,t.onFinish=function(){this.destroy(),e&&e()},new c(t).play()},t.Easings={BackEaseIn(t,e,n,r){var i=1.70158;return n*(t/=r)*t*((i+1)*t-i)+e},BackEaseOut(t,e,n,r){var i=1.70158;return n*((t=t/r-1)*t*((i+1)*t+i)+1)+e},BackEaseInOut(t,e,n,r){var i=1.70158;return(t/=r/2)<1?n/2*(t*t*((1+(i*=1.525))*t-i))+e:n/2*((t-=2)*t*((1+(i*=1.525))*t+i)+2)+e},ElasticEaseIn(t,e,n,r,i,o){var a=0;return 0===t?e:1==(t/=r)?e+n:(o||(o=.3*r),!i||i<Math.abs(n)?(i=n,a=o/4):a=o/(2*Math.PI)*Math.asin(n/i),-i*Math.pow(2,10*(t-=1))*Math.sin((t*r-a)*(2*Math.PI)/o)+e)},ElasticEaseOut(t,e,n,r,i,o){var a=0;return 0===t?e:1==(t/=r)?e+n:(o||(o=.3*r),!i||i<Math.abs(n)?(i=n,a=o/4):a=o/(2*Math.PI)*Math.asin(n/i),i*Math.pow(2,-10*t)*Math.sin((t*r-a)*(2*Math.PI)/o)+n+e)},ElasticEaseInOut(t,e,n,r,i,o){var a=0;return 0===t?e:2==(t/=r/2)?e+n:(o||(o=r*(.3*1.5)),!i||i<Math.abs(n)?(i=n,a=o/4):a=o/(2*Math.PI)*Math.asin(n/i),t<1?i*Math.pow(2,10*(t-=1))*Math.sin((t*r-a)*(2*Math.PI)/o)*-.5+e:i*Math.pow(2,-10*(t-=1))*Math.sin((t*r-a)*(2*Math.PI)/o)*.5+n+e)},BounceEaseOut:(t,e,n,r)=>(t/=r)<1/2.75?n*(7.5625*t*t)+e:t<2/2.75?n*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?n*(7.5625*(t-=2.25/2.75)*t+.9375)+e:n*(7.5625*(t-=2.625/2.75)*t+.984375)+e,BounceEaseIn:(e,n,r,i)=>r-t.Easings.BounceEaseOut(i-e,0,r,i)+n,BounceEaseInOut:(e,n,r,i)=>e<i/2?.5*t.Easings.BounceEaseIn(2*e,0,r,i)+n:.5*t.Easings.BounceEaseOut(2*e-i,0,r,i)+.5*r+n,EaseIn:(t,e,n,r)=>n*(t/=r)*t+e,EaseOut:(t,e,n,r)=>-n*(t/=r)*(t-2)+e,EaseInOut:(t,e,n,r)=>(t/=r/2)<1?n/2*t*t+e:-n/2*(--t*(t-2)-1)+e,StrongEaseIn:(t,e,n,r)=>n*(t/=r)*t*t*t*t+e,StrongEaseOut:(t,e,n,r)=>n*((t=t/r-1)*t*t*t*t+1)+e,StrongEaseInOut:(t,e,n,r)=>(t/=r/2)<1?n/2*t*t*t*t*t+e:n/2*((t-=2)*t*t*t*t+2)+e,Linear:(t,e,n,r)=>n*t/r+e}}(rm)),rm),f=Eg(),p=Pg();t.Konva=n.Util._assign(e.Konva,{Util:n.Util,Transform:n.Transform,Node:r.Node,Container:i.Container,Stage:o.Stage,stages:o.stages,Layer:a.Layer,FastLayer:s.FastLayer,Group:l.Group,DD:c.DD,Shape:h.Shape,shapes:h.shapes,Animation:u.Animation,Tween:d.Tween,Easings:d.Easings,Context:f.Context,Canvas:p.Canvas}),t.default=t.Konva}(cg)),cg}var om,am={};var sm,lm={},cm={};function hm(){if(sm)return cm;sm=1,Object.defineProperty(cm,"__esModule",{value:!0}),cm.Line=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();function i(t,e,n,r,i,o,a){var s=Math.sqrt(Math.pow(n-t,2)+Math.pow(r-e,2)),l=Math.sqrt(Math.pow(i-n,2)+Math.pow(o-r,2)),c=a*s/(s+l),h=a*l/(s+l);return[n-c*(i-t),r-c*(o-e),n+h*(i-t),r+h*(o-e)]}function o(t,e){var n,r,o=t.length,a=[];for(n=2;n<o-2;n+=2)r=i(t[n-2],t[n-1],t[n],t[n+1],t[n+2],t[n+3],e),isNaN(r[0])||(a.push(r[0]),a.push(r[1]),a.push(t[n]),a.push(t[n+1]),a.push(r[2]),a.push(r[3]));return a}let a=class extends e.Shape{constructor(t){super(t),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",(function(){this._clearCache("tensionPoints")}))}_sceneFunc(t){var e,n,r,i=this.points(),o=i.length,a=this.tension(),s=this.closed(),l=this.bezier();if(o){if(t.beginPath(),t.moveTo(i[0],i[1]),0!==a&&o>4){for(n=(e=this.getTensionPoints()).length,r=s?0:4,s||t.quadraticCurveTo(e[0],e[1],e[2],e[3]);r<n-2;)t.bezierCurveTo(e[r++],e[r++],e[r++],e[r++],e[r++],e[r++]);s||t.quadraticCurveTo(e[n-2],e[n-1],i[o-2],i[o-1])}else if(l)for(r=2;r<o;)t.bezierCurveTo(i[r++],i[r++],i[r++],i[r++],i[r++],i[r++]);else for(r=2;r<o;r+=2)t.lineTo(i[r],i[r+1]);s?(t.closePath(),t.fillStrokeShape(this)):t.strokeShape(this)}}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():o(this.points(),this.tension())}_getTensionPointsClosed(){var t=this.points(),e=t.length,n=this.tension(),r=i(t[e-2],t[e-1],t[0],t[1],t[2],t[3],n),a=i(t[e-4],t[e-3],t[e-2],t[e-1],t[0],t[1],n),s=o(t,n);return[r[2],r[3]].concat(s).concat([a[0],a[1],t[e-2],t[e-1],a[2],a[3],r[0],r[1],t[0],t[1]])}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){var t=this.points();if(t.length<4)return{x:t[0]||0,y:t[1]||0,width:0,height:0};for(var e,n,r=(t=0!==this.tension()?[t[0],t[1],...this._getTensionPoints(),t[t.length-2],t[t.length-1]]:this.points())[0],i=t[0],o=t[1],a=t[1],s=0;s<t.length/2;s++)e=t[2*s],n=t[2*s+1],r=Math.min(r,e),i=Math.max(i,e),o=Math.min(o,n),a=Math.max(a,n);return{x:r,y:o,width:i-r,height:a-o}}};return cm.Line=a,a.prototype.className="Line",a.prototype._attrsAffectingSize=["points","bezier","tension"],(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"closed",!1),t.Factory.addGetterSetter(a,"bezier",!1),t.Factory.addGetterSetter(a,"tension",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(a,"points",[],(0,n.getNumberArrayValidator)()),cm}var um,dm,fm,pm={},gm={};function mm(){if(dm)return pm;dm=1,Object.defineProperty(pm,"__esModule",{value:!0}),pm.Path=void 0;const t=wg(),e=Wg(),n=ug(),r=(um||(um=1,function(t){function e(t,e,r){const i=n(1,r,t),o=n(1,r,e),a=i*i+o*o;return Math.sqrt(a)}Object.defineProperty(t,"__esModule",{value:!0}),t.t2length=t.getQuadraticArcLength=t.getCubicArcLength=t.binomialCoefficients=t.cValues=t.tValues=void 0,t.tValues=[[],[],[-.5773502691896257,.5773502691896257],[0,-.7745966692414834,.7745966692414834],[-.33998104358485626,.33998104358485626,-.8611363115940526,.8611363115940526],[0,-.5384693101056831,.5384693101056831,-.906179845938664,.906179845938664],[.6612093864662645,-.6612093864662645,-.2386191860831969,.2386191860831969,-.932469514203152,.932469514203152],[0,.4058451513773972,-.4058451513773972,-.7415311855993945,.7415311855993945,-.9491079123427585,.9491079123427585],[-.1834346424956498,.1834346424956498,-.525532409916329,.525532409916329,-.7966664774136267,.7966664774136267,-.9602898564975363,.9602898564975363],[0,-.8360311073266358,.8360311073266358,-.9681602395076261,.9681602395076261,-.3242534234038089,.3242534234038089,-.6133714327005904,.6133714327005904],[-.14887433898163122,.14887433898163122,-.4333953941292472,.4333953941292472,-.6794095682990244,.6794095682990244,-.8650633666889845,.8650633666889845,-.9739065285171717,.9739065285171717],[0,-.26954315595234496,.26954315595234496,-.5190961292068118,.5190961292068118,-.7301520055740494,.7301520055740494,-.8870625997680953,.8870625997680953,-.978228658146057,.978228658146057],[-.1252334085114689,.1252334085114689,-.3678314989981802,.3678314989981802,-.5873179542866175,.5873179542866175,-.7699026741943047,.7699026741943047,-.9041172563704749,.9041172563704749,-.9815606342467192,.9815606342467192],[0,-.2304583159551348,.2304583159551348,-.44849275103644687,.44849275103644687,-.6423493394403402,.6423493394403402,-.8015780907333099,.8015780907333099,-.9175983992229779,.9175983992229779,-.9841830547185881,.9841830547185881],[-.10805494870734367,.10805494870734367,-.31911236892788974,.31911236892788974,-.5152486363581541,.5152486363581541,-.6872929048116855,.6872929048116855,-.827201315069765,.827201315069765,-.9284348836635735,.9284348836635735,-.9862838086968123,.9862838086968123],[0,-.20119409399743451,.20119409399743451,-.3941513470775634,.3941513470775634,-.5709721726085388,.5709721726085388,-.7244177313601701,.7244177313601701,-.8482065834104272,.8482065834104272,-.937273392400706,.937273392400706,-.9879925180204854,.9879925180204854],[-.09501250983763744,.09501250983763744,-.2816035507792589,.2816035507792589,-.45801677765722737,.45801677765722737,-.6178762444026438,.6178762444026438,-.755404408355003,.755404408355003,-.8656312023878318,.8656312023878318,-.9445750230732326,.9445750230732326,-.9894009349916499,.9894009349916499],[0,-.17848418149584785,.17848418149584785,-.3512317634538763,.3512317634538763,-.5126905370864769,.5126905370864769,-.6576711592166907,.6576711592166907,-.7815140038968014,.7815140038968014,-.8802391537269859,.8802391537269859,-.9506755217687678,.9506755217687678,-.9905754753144174,.9905754753144174],[-.0847750130417353,.0847750130417353,-.2518862256915055,.2518862256915055,-.41175116146284263,.41175116146284263,-.5597708310739475,.5597708310739475,-.6916870430603532,.6916870430603532,-.8037049589725231,.8037049589725231,-.8926024664975557,.8926024664975557,-.9558239495713977,.9558239495713977,-.9915651684209309,.9915651684209309],[0,-.16035864564022537,.16035864564022537,-.31656409996362983,.31656409996362983,-.46457074137596094,.46457074137596094,-.600545304661681,.600545304661681,-.7209661773352294,.7209661773352294,-.8227146565371428,.8227146565371428,-.9031559036148179,.9031559036148179,-.96020815213483,.96020815213483,-.9924068438435844,.9924068438435844],[-.07652652113349734,.07652652113349734,-.22778585114164507,.22778585114164507,-.37370608871541955,.37370608871541955,-.5108670019508271,.5108670019508271,-.636053680726515,.636053680726515,-.7463319064601508,.7463319064601508,-.8391169718222188,.8391169718222188,-.912234428251326,.912234428251326,-.9639719272779138,.9639719272779138,-.9931285991850949,.9931285991850949],[0,-.1455618541608951,.1455618541608951,-.2880213168024011,.2880213168024011,-.4243421202074388,.4243421202074388,-.5516188358872198,.5516188358872198,-.6671388041974123,.6671388041974123,-.7684399634756779,.7684399634756779,-.8533633645833173,.8533633645833173,-.9200993341504008,.9200993341504008,-.9672268385663063,.9672268385663063,-.9937521706203895,.9937521706203895],[-.06973927331972223,.06973927331972223,-.20786042668822127,.20786042668822127,-.34193582089208424,.34193582089208424,-.469355837986757,.469355837986757,-.5876404035069116,.5876404035069116,-.6944872631866827,.6944872631866827,-.7878168059792081,.7878168059792081,-.8658125777203002,.8658125777203002,-.926956772187174,.926956772187174,-.9700604978354287,.9700604978354287,-.9942945854823992,.9942945854823992],[0,-.1332568242984661,.1332568242984661,-.26413568097034495,.26413568097034495,-.3903010380302908,.3903010380302908,-.5095014778460075,.5095014778460075,-.6196098757636461,.6196098757636461,-.7186613631319502,.7186613631319502,-.8048884016188399,.8048884016188399,-.8767523582704416,.8767523582704416,-.9329710868260161,.9329710868260161,-.9725424712181152,.9725424712181152,-.9947693349975522,.9947693349975522],[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213]],t.cValues=[[],[],[1,1],[.8888888888888888,.5555555555555556,.5555555555555556],[.6521451548625461,.6521451548625461,.34785484513745385,.34785484513745385],[.5688888888888889,.47862867049936647,.47862867049936647,.23692688505618908,.23692688505618908],[.3607615730481386,.3607615730481386,.46791393457269104,.46791393457269104,.17132449237917036,.17132449237917036],[.4179591836734694,.3818300505051189,.3818300505051189,.27970539148927664,.27970539148927664,.1294849661688697,.1294849661688697],[.362683783378362,.362683783378362,.31370664587788727,.31370664587788727,.22238103445337448,.22238103445337448,.10122853629037626,.10122853629037626],[.3302393550012598,.1806481606948574,.1806481606948574,.08127438836157441,.08127438836157441,.31234707704000286,.31234707704000286,.26061069640293544,.26061069640293544],[.29552422471475287,.29552422471475287,.26926671930999635,.26926671930999635,.21908636251598204,.21908636251598204,.1494513491505806,.1494513491505806,.06667134430868814,.06667134430868814],[.2729250867779006,.26280454451024665,.26280454451024665,.23319376459199048,.23319376459199048,.18629021092773426,.18629021092773426,.1255803694649046,.1255803694649046,.05566856711617366,.05566856711617366],[.24914704581340277,.24914704581340277,.2334925365383548,.2334925365383548,.20316742672306592,.20316742672306592,.16007832854334622,.16007832854334622,.10693932599531843,.10693932599531843,.04717533638651183,.04717533638651183],[.2325515532308739,.22628318026289723,.22628318026289723,.2078160475368885,.2078160475368885,.17814598076194574,.17814598076194574,.13887351021978725,.13887351021978725,.09212149983772845,.09212149983772845,.04048400476531588,.04048400476531588],[.2152638534631578,.2152638534631578,.2051984637212956,.2051984637212956,.18553839747793782,.18553839747793782,.15720316715819355,.15720316715819355,.12151857068790319,.12151857068790319,.08015808715976021,.08015808715976021,.03511946033175186,.03511946033175186],[.2025782419255613,.19843148532711158,.19843148532711158,.1861610000155622,.1861610000155622,.16626920581699392,.16626920581699392,.13957067792615432,.13957067792615432,.10715922046717194,.10715922046717194,.07036604748810812,.07036604748810812,.03075324199611727,.03075324199611727],[.1894506104550685,.1894506104550685,.18260341504492358,.18260341504492358,.16915651939500254,.16915651939500254,.14959598881657674,.14959598881657674,.12462897125553388,.12462897125553388,.09515851168249279,.09515851168249279,.062253523938647894,.062253523938647894,.027152459411754096,.027152459411754096],[.17944647035620653,.17656270536699264,.17656270536699264,.16800410215645004,.16800410215645004,.15404576107681028,.15404576107681028,.13513636846852548,.13513636846852548,.11188384719340397,.11188384719340397,.08503614831717918,.08503614831717918,.0554595293739872,.0554595293739872,.02414830286854793,.02414830286854793],[.1691423829631436,.1691423829631436,.16427648374583273,.16427648374583273,.15468467512626524,.15468467512626524,.14064291467065065,.14064291467065065,.12255520671147846,.12255520671147846,.10094204410628717,.10094204410628717,.07642573025488905,.07642573025488905,.0497145488949698,.0497145488949698,.02161601352648331,.02161601352648331],[.1610544498487837,.15896884339395434,.15896884339395434,.15276604206585967,.15276604206585967,.1426067021736066,.1426067021736066,.12875396253933621,.12875396253933621,.11156664554733399,.11156664554733399,.09149002162245,.09149002162245,.06904454273764123,.06904454273764123,.0448142267656996,.0448142267656996,.019461788229726478,.019461788229726478],[.15275338713072584,.15275338713072584,.14917298647260374,.14917298647260374,.14209610931838204,.14209610931838204,.13168863844917664,.13168863844917664,.11819453196151841,.11819453196151841,.10193011981724044,.10193011981724044,.08327674157670475,.08327674157670475,.06267204833410907,.06267204833410907,.04060142980038694,.04060142980038694,.017614007139152118,.017614007139152118],[.14608113364969041,.14452440398997005,.14452440398997005,.13988739479107315,.13988739479107315,.13226893863333747,.13226893863333747,.12183141605372853,.12183141605372853,.10879729916714838,.10879729916714838,.09344442345603386,.09344442345603386,.0761001136283793,.0761001136283793,.057134425426857205,.057134425426857205,.036953789770852494,.036953789770852494,.016017228257774335,.016017228257774335],[.13925187285563198,.13925187285563198,.13654149834601517,.13654149834601517,.13117350478706238,.13117350478706238,.12325237681051242,.12325237681051242,.11293229608053922,.11293229608053922,.10041414444288096,.10041414444288096,.08594160621706773,.08594160621706773,.06979646842452049,.06979646842452049,.052293335152683286,.052293335152683286,.03377490158481415,.03377490158481415,.0146279952982722,.0146279952982722],[.13365457218610619,.1324620394046966,.1324620394046966,.12890572218808216,.12890572218808216,.12304908430672953,.12304908430672953,.11499664022241136,.11499664022241136,.10489209146454141,.10489209146454141,.09291576606003515,.09291576606003515,.07928141177671895,.07928141177671895,.06423242140852585,.06423242140852585,.04803767173108467,.04803767173108467,.030988005856979445,.030988005856979445,.013411859487141771,.013411859487141771],[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872]],t.binomialCoefficients=[[1],[1,1],[1,2,1],[1,3,3,1]],t.getCubicArcLength=(n,r,i)=>{let o,a,s;o=i/2,a=0;for(let l=0;l<20;l++)s=o*t.tValues[20][l]+o,a+=t.cValues[20][l]*e(n,r,s);return o*a},t.getQuadraticArcLength=(t,e,n)=>{void 0===n&&(n=1);const r=t[0]-2*t[1]+t[2],i=e[0]-2*e[1]+e[2],o=2*t[1]-2*t[0],a=2*e[1]-2*e[0],s=4*(r*r+i*i),l=4*(r*o+i*a),c=o*o+a*a;if(0===s)return n*Math.sqrt(Math.pow(t[2]-t[0],2)+Math.pow(e[2]-e[0],2));const h=l/(2*s),u=n+h,d=c/s-h*h,f=u*u+d>0?Math.sqrt(u*u+d):0,p=h*h+d>0?Math.sqrt(h*h+d):0,g=h+Math.sqrt(h*h+d)!==0?d*Math.log(Math.abs((u+f)/(h+p))):0;return Math.sqrt(s)/2*(u*f-h*p+g)};const n=(e,r,i)=>{const o=i.length-1;let a,s;if(0===o)return 0;if(0===e){s=0;for(let e=0;e<=o;e++)s+=t.binomialCoefficients[o][e]*Math.pow(1-r,o-e)*Math.pow(r,e)*i[e];return s}a=new Array(o);for(let t=0;t<o;t++)a[t]=o*(i[t+1]-i[t]);return n(e-1,r,a)};t.t2length=(t,e,n)=>{let r=1,i=t/e,o=(t-n(i))/e,a=0;for(;r>.001;){const s=n(i+o),l=Math.abs(t-s)/e;if(l<r)r=l,i+=o;else{const a=n(i-o),s=Math.abs(t-a)/e;s<r?(r=s,i-=o):o/=2}if(a++,a>500)break}return i}}(gm)),gm);let i=class t extends e.Shape{constructor(t){super(t),this.dataArray=[],this.pathLength=0,this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute()}))}_readDataAttribute(){this.dataArray=t.parsePathData(this.data()),this.pathLength=t.getPathLength(this.dataArray)}_sceneFunc(t){var e=this.dataArray;t.beginPath();for(var n=!1,r=0;r<e.length;r++){var i=e[r].command,o=e[r].points;switch(i){case"L":t.lineTo(o[0],o[1]);break;case"M":t.moveTo(o[0],o[1]);break;case"C":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"Q":t.quadraticCurveTo(o[0],o[1],o[2],o[3]);break;case"A":var a=o[0],s=o[1],l=o[2],c=o[3],h=o[4],u=o[5],d=o[6],f=o[7],p=l>c?l:c,g=l>c?1:l/c,m=l>c?c/l:1;t.translate(a,s),t.rotate(d),t.scale(g,m),t.arc(0,0,p,h,h+u,1-f),t.scale(1/g,1/m),t.rotate(-d),t.translate(-a,-s);break;case"z":n=!0,t.closePath()}}n||this.hasFill()?t.fillStrokeShape(this):t.strokeShape(this)}getSelfRect(){var e=[];this.dataArray.forEach((function(n){if("A"===n.command){var r=n.points[4],i=n.points[5],o=n.points[4]+i,a=Math.PI/180;if(Math.abs(r-o)<a&&(a=Math.abs(r-o)),i<0)for(let i=r-a;i>o;i-=a){const r=t.getPointOnEllipticalArc(n.points[0],n.points[1],n.points[2],n.points[3],i,0);e.push(r.x,r.y)}else for(let i=r+a;i<o;i+=a){const r=t.getPointOnEllipticalArc(n.points[0],n.points[1],n.points[2],n.points[3],i,0);e.push(r.x,r.y)}}else if("C"===n.command)for(let s=0;s<=1;s+=.01){const r=t.getPointOnCubicBezier(s,n.start.x,n.start.y,n.points[0],n.points[1],n.points[2],n.points[3],n.points[4],n.points[5]);e.push(r.x,r.y)}else e=e.concat(n.points)}));for(var n,r,i=e[0],o=e[0],a=e[1],s=e[1],l=0;l<e.length/2;l++)n=e[2*l],r=e[2*l+1],isNaN(n)||(i=Math.min(i,n),o=Math.max(o,n)),isNaN(r)||(a=Math.min(a,r),s=Math.max(s,r));return{x:i,y:a,width:o-i,height:s-a}}getLength(){return this.pathLength}getPointAtLength(e){return t.getPointAtLengthOfDataArray(e,this.dataArray)}static getLineLength(t,e,n,r){return Math.sqrt((n-t)*(n-t)+(r-e)*(r-e))}static getPathLength(t){let e=0;for(var n=0;n<t.length;++n)e+=t[n].pathLength;return e}static getPointAtLengthOfDataArray(e,n){var i,o=0,a=n.length;if(!a)return null;for(;o<a&&e>n[o].pathLength;)e-=n[o].pathLength,++o;if(o===a)return{x:(i=n[o-1].points.slice(-2))[0],y:i[1]};if(e<.01)return{x:(i=n[o].points.slice(0,2))[0],y:i[1]};var s=n[o],l=s.points;switch(s.command){case"L":return t.getPointOnLine(e,s.start.x,s.start.y,l[0],l[1]);case"C":return t.getPointOnCubicBezier((0,r.t2length)(e,t.getPathLength(n),(t=>(0,r.getCubicArcLength)([s.start.x,l[0],l[2],l[4]],[s.start.y,l[1],l[3],l[5]],t))),s.start.x,s.start.y,l[0],l[1],l[2],l[3],l[4],l[5]);case"Q":return t.getPointOnQuadraticBezier((0,r.t2length)(e,t.getPathLength(n),(t=>(0,r.getQuadraticArcLength)([s.start.x,l[0],l[2]],[s.start.y,l[1],l[3]],t))),s.start.x,s.start.y,l[0],l[1],l[2],l[3]);case"A":var c=l[0],h=l[1],u=l[2],d=l[3],f=l[4],p=l[5],g=l[6];return f+=p*e/s.pathLength,t.getPointOnEllipticalArc(c,h,u,d,f,g)}return null}static getPointOnLine(t,e,n,r,i,o,a){void 0===o&&(o=e),void 0===a&&(a=n);var s=(i-n)/(r-e+1e-8),l=Math.sqrt(t*t/(1+s*s));r<e&&(l*=-1);var c,h=s*l;if(r===e)c={x:o,y:a+h};else if((a-n)/(o-e+1e-8)===s)c={x:o+l,y:a+h};else{var u,d,f=this.getLineLength(e,n,r,i),p=(o-e)*(r-e)+(a-n)*(i-n);u=e+(p/=f*f)*(r-e),d=n+p*(i-n);var g=this.getLineLength(o,a,u,d),m=Math.sqrt(t*t-g*g);l=Math.sqrt(m*m/(1+s*s)),r<e&&(l*=-1),c={x:u+l,y:d+(h=s*l)}}return c}static getPointOnCubicBezier(t,e,n,r,i,o,a,s,l){function c(t){return t*t*t}function h(t){return 3*t*t*(1-t)}function u(t){return 3*t*(1-t)*(1-t)}function d(t){return(1-t)*(1-t)*(1-t)}return{x:s*c(t)+o*h(t)+r*u(t)+e*d(t),y:l*c(t)+a*h(t)+i*u(t)+n*d(t)}}static getPointOnQuadraticBezier(t,e,n,r,i,o,a){function s(t){return t*t}function l(t){return 2*t*(1-t)}function c(t){return(1-t)*(1-t)}return{x:o*s(t)+r*l(t)+e*c(t),y:a*s(t)+i*l(t)+n*c(t)}}static getPointOnEllipticalArc(t,e,n,r,i,o){var a=Math.cos(o),s=Math.sin(o),l=n*Math.cos(i),c=r*Math.sin(i);return{x:t+(l*a-c*s),y:e+(l*s+c*a)}}static parsePathData(t){if(!t)return[];var e=t,n=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];e=e.replace(new RegExp(" ","g"),",");for(var r=0;r<n.length;r++)e=e.replace(new RegExp(n[r],"g"),"|"+n[r]);var i,o=e.split("|"),a=[],s=[],l=0,c=0,h=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi;for(r=1;r<o.length;r++){var u=o[r],d=u.charAt(0);for(u=u.slice(1),s.length=0;i=h.exec(u);)s.push(i[0]);for(var f=[],p=0,g=s.length;p<g;p++)if("00"!==s[p]){var m=parseFloat(s[p]);isNaN(m)?f.push(0):f.push(m)}else f.push(0,0);for(;f.length>0&&!isNaN(f[0]);){var y,v,b,_,w,S,x,C,A,E,P="",k=[],O=l,T=c;switch(d){case"l":l+=f.shift(),c+=f.shift(),P="L",k.push(l,c);break;case"L":l=f.shift(),c=f.shift(),k.push(l,c);break;case"m":var R=f.shift(),F=f.shift();if(l+=R,c+=F,P="M",a.length>2&&"z"===a[a.length-1].command)for(var M=a.length-2;M>=0;M--)if("M"===a[M].command){l=a[M].points[0]+R,c=a[M].points[1]+F;break}k.push(l,c),d="l";break;case"M":l=f.shift(),c=f.shift(),P="M",k.push(l,c),d="L";break;case"h":l+=f.shift(),P="L",k.push(l,c);break;case"H":l=f.shift(),P="L",k.push(l,c);break;case"v":c+=f.shift(),P="L",k.push(l,c);break;case"V":c=f.shift(),P="L",k.push(l,c);break;case"C":k.push(f.shift(),f.shift(),f.shift(),f.shift()),l=f.shift(),c=f.shift(),k.push(l,c);break;case"c":k.push(l+f.shift(),c+f.shift(),l+f.shift(),c+f.shift()),l+=f.shift(),c+=f.shift(),P="C",k.push(l,c);break;case"S":v=l,b=c,"C"===(y=a[a.length-1]).command&&(v=l+(l-y.points[2]),b=c+(c-y.points[3])),k.push(v,b,f.shift(),f.shift()),l=f.shift(),c=f.shift(),P="C",k.push(l,c);break;case"s":v=l,b=c,"C"===(y=a[a.length-1]).command&&(v=l+(l-y.points[2]),b=c+(c-y.points[3])),k.push(v,b,l+f.shift(),c+f.shift()),l+=f.shift(),c+=f.shift(),P="C",k.push(l,c);break;case"Q":k.push(f.shift(),f.shift()),l=f.shift(),c=f.shift(),k.push(l,c);break;case"q":k.push(l+f.shift(),c+f.shift()),l+=f.shift(),c+=f.shift(),P="Q",k.push(l,c);break;case"T":v=l,b=c,"Q"===(y=a[a.length-1]).command&&(v=l+(l-y.points[0]),b=c+(c-y.points[1])),l=f.shift(),c=f.shift(),P="Q",k.push(v,b,l,c);break;case"t":v=l,b=c,"Q"===(y=a[a.length-1]).command&&(v=l+(l-y.points[0]),b=c+(c-y.points[1])),l+=f.shift(),c+=f.shift(),P="Q",k.push(v,b,l,c);break;case"A":_=f.shift(),w=f.shift(),S=f.shift(),x=f.shift(),C=f.shift(),A=l,E=c,l=f.shift(),c=f.shift(),P="A",k=this.convertEndpointToCenterParameterization(A,E,l,c,x,C,_,w,S);break;case"a":_=f.shift(),w=f.shift(),S=f.shift(),x=f.shift(),C=f.shift(),A=l,E=c,l+=f.shift(),c+=f.shift(),P="A",k=this.convertEndpointToCenterParameterization(A,E,l,c,x,C,_,w,S)}a.push({command:P||d,points:k,start:{x:O,y:T},pathLength:this.calcLength(O,T,P||d,k)})}"z"!==d&&"Z"!==d||a.push({command:"z",points:[],start:void 0,pathLength:0})}return a}static calcLength(e,n,i,o){var a,s,l,c,h=t;switch(i){case"L":return h.getLineLength(e,n,o[0],o[1]);case"C":return(0,r.getCubicArcLength)([e,o[0],o[2],o[4]],[n,o[1],o[3],o[5]],1);case"Q":return(0,r.getQuadraticArcLength)([e,o[0],o[2]],[n,o[1],o[3]],1);case"A":a=0;var u=o[4],d=o[5],f=o[4]+d,p=Math.PI/180;if(Math.abs(u-f)<p&&(p=Math.abs(u-f)),s=h.getPointOnEllipticalArc(o[0],o[1],o[2],o[3],u,0),d<0)for(c=u-p;c>f;c-=p)l=h.getPointOnEllipticalArc(o[0],o[1],o[2],o[3],c,0),a+=h.getLineLength(s.x,s.y,l.x,l.y),s=l;else for(c=u+p;c<f;c+=p)l=h.getPointOnEllipticalArc(o[0],o[1],o[2],o[3],c,0),a+=h.getLineLength(s.x,s.y,l.x,l.y),s=l;return l=h.getPointOnEllipticalArc(o[0],o[1],o[2],o[3],f,0),a+=h.getLineLength(s.x,s.y,l.x,l.y)}return 0}static convertEndpointToCenterParameterization(t,e,n,r,i,o,a,s,l){var c=l*(Math.PI/180),h=Math.cos(c)*(t-n)/2+Math.sin(c)*(e-r)/2,u=-1*Math.sin(c)*(t-n)/2+Math.cos(c)*(e-r)/2,d=h*h/(a*a)+u*u/(s*s);d>1&&(a*=Math.sqrt(d),s*=Math.sqrt(d));var f=Math.sqrt((a*a*(s*s)-a*a*(u*u)-s*s*(h*h))/(a*a*(u*u)+s*s*(h*h)));i===o&&(f*=-1),isNaN(f)&&(f=0);var p=f*a*u/s,g=f*-s*h/a,m=(t+n)/2+Math.cos(c)*p-Math.sin(c)*g,y=(e+r)/2+Math.sin(c)*p+Math.cos(c)*g,v=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},b=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(v(t)*v(e))},_=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(b(t,e))},w=_([1,0],[(h-p)/a,(u-g)/s]),S=[(h-p)/a,(u-g)/s],x=[(-1*h-p)/a,(-1*u-g)/s],C=_(S,x);return b(S,x)<=-1&&(C=Math.PI),b(S,x)>=1&&(C=0),0===o&&C>0&&(C-=2*Math.PI),1===o&&C<0&&(C+=2*Math.PI),[m,y,a,s,w,C,c,o]}};return pm.Path=i,i.prototype.className="Path",i.prototype._attrsAffectingSize=["data"],(0,n._registerNode)(i),t.Factory.addGetterSetter(i,"data"),pm}var ym,vm={};var bm,_m={};var wm,Sm={};var xm,Cm={};var Am,Em={};function Pm(){if(Am)return Em;Am=1,Object.defineProperty(Em,"__esModule",{value:!0}),Em.Rect=void 0;const t=wg(),e=Wg(),n=ug(),r=pg(),i=_g();let o=class extends e.Shape{_sceneFunc(t){var e=this.cornerRadius(),n=this.width(),i=this.height();t.beginPath(),e?r.Util.drawRoundedRectPath(t,n,i,e):t.rect(0,0,n,i),t.closePath(),t.fillStrokeShape(this)}};return Em.Rect=o,o.prototype.className="Rect",(0,n._registerNode)(o),t.Factory.addGetterSetter(o,"cornerRadius",0,(0,i.getNumberOrArrayOfNumbersValidator)(4)),Em}var km,Om={};var Tm,Rm={};var Fm,Mm={};var Nm,Lm={};var Dm,Im={};function jm(){if(Dm)return Im;Dm=1,Object.defineProperty(Im,"__esModule",{value:!0}),Im.Text=Im.stringToArray=void 0;const t=pg(),e=wg(),n=Wg(),r=_g(),i=ug();function o(t){return Array.from(t)}Im.stringToArray=o;var a,s="auto",l="inherit",c="justify",h="left",u="middle",d="normal",f=" ",p="none",g=["direction","fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],m=g.length;function y(){return a||(a=t.Util.createCanvasElement().getContext("2d"))}let v=class extends n.Shape{constructor(t){super(function(t){return(t=t||{}).fillLinearGradientColorStops||t.fillRadialGradientColorStops||t.fillPatternImage||(t.fill=t.fill||"black"),t}(t)),this._partialTextX=0,this._partialTextY=0;for(var e=0;e<m;e++)this.on(g[e]+"Change.konva",this._setTextData);this._setTextData()}_sceneFunc(t){var e=this.textArr,n=e.length;if(this.text()){var r,i=this.padding(),a=this.fontSize(),s=this.lineHeight()*a,d=this.verticalAlign(),f=this.direction(),p=0,g=this.align(),m=this.getWidth(),y=this.letterSpacing(),v=this.fill(),b=this.textDecoration(),_=-1!==b.indexOf("underline"),w=-1!==b.indexOf("line-through"),S=0,x=(S=s/2,0),C=0;for("rtl"===(f=f===l?t.direction:f)&&t.setAttr("direction",f),t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",u),t.setAttr("textAlign",h),d===u?p=(this.getHeight()-n*s-2*i)/2:"bottom"===d&&(p=this.getHeight()-n*s-2*i),t.translate(i,p+i),r=0;r<n;r++){x=0,C=0;var A,E,P,k=e[r],O=k.text,T=k.width,R=k.lastInParagraph;if(t.save(),"right"===g?x+=m-T-2*i:"center"===g&&(x+=(m-T-2*i)/2),_){t.save(),t.beginPath(),t.moveTo(x,S+C+Math.round(a/2)),E=0===(A=O.split(" ").length-1),P=g!==c||R?T:m-2*i,t.lineTo(x+Math.round(P),S+C+Math.round(a/2)),t.lineWidth=a/15;const e=this._getLinearGradient();t.strokeStyle=e||v,t.stroke(),t.restore()}if(w){t.save(),t.beginPath(),t.moveTo(x,S+C),E=0===(A=O.split(" ").length-1),P=g===c&&R&&!E?m-2*i:T,t.lineTo(x+Math.round(P),S+C),t.lineWidth=a/15;const e=this._getLinearGradient();t.strokeStyle=e||v,t.stroke(),t.restore()}if("rtl"===f||0===y&&g!==c)0!==y&&t.setAttr("letterSpacing",`${y}px`),this._partialTextX=x,this._partialTextY=S+C,this._partialText=O,t.fillStrokeShape(this);else{A=O.split(" ").length-1;for(var F=o(O),M=0;M<F.length;M++){var N=F[M];" "!==N||R||g!==c||(x+=(m-2*i-T)/A),this._partialTextX=x,this._partialTextY=S+C,this._partialText=N,t.fillStrokeShape(this),x+=this.measureSize(N).width+y}}t.restore(),n>1&&(S+=s)}}}_hitFunc(t){var e=this.getWidth(),n=this.getHeight();t.beginPath(),t.rect(0,0,e,n),t.closePath(),t.fillStrokeShape(this)}setText(e){var n=t.Util._isString(e)?e:null==e?"":e+"";return this._setAttr("text",n),this}getWidth(){return this.attrs.width===s||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width}getHeight(){return this.attrs.height===s||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(t){var e,n=y(),r=this.fontSize();return n.save(),n.font=this._getContextFont(),e=n.measureText(t),n.restore(),{width:e.width,height:r}}_getContextFont(){return this.fontStyle()+f+this.fontVariant()+f+(this.fontSize()+"px ")+this.fontFamily().split(",").map((t=>{const e=(t=t.trim()).indexOf(" ")>=0,n=t.indexOf('"')>=0||t.indexOf("'")>=0;return e&&!n&&(t=`"${t}"`),t})).join(", ")}_addTextLine(t){this.align()===c&&(t=t.trim());var e=this._getTextWidth(t);return this.textArr.push({text:t,width:e,lastInParagraph:!1})}_getTextWidth(t){var e=this.letterSpacing(),n=t.length;return y().measureText(t).width+(n?e*(n-1):0)}_setTextData(){var t=this.text().split("\n"),e=+this.fontSize(),n=0,r=this.lineHeight()*e,i=this.attrs.width,o=this.attrs.height,a=i!==s&&void 0!==i,l=o!==s&&void 0!==o,c=this.padding(),h=i-2*c,u=o-2*c,d=0,g=this.wrap(),m="char"!==g&&g!==p,v=this.ellipsis();this.textArr=[],y().font=this._getContextFont();for(var b=v?this._getTextWidth("…"):0,_=0,w=t.length;_<w;++_){var S=t[_],x=this._getTextWidth(S);if(a&&x>h)for(;S.length>0;){for(var C=0,A=S.length,E="",P=0;C<A;){var k=C+A>>>1,O=S.slice(0,k+1),T=this._getTextWidth(O)+b;T<=h?(C=k+1,E=O,P=T):A=k}if(!E)break;if(m){var R,F=S[E.length];(R=(F===f||"-"===F)&&P<=h?E.length:Math.max(E.lastIndexOf(f),E.lastIndexOf("-"))+1)>0&&(C=R,E=E.slice(0,C),P=this._getTextWidth(E))}if(E=E.trimRight(),this._addTextLine(E),n=Math.max(n,P),d+=r,this._shouldHandleEllipsis(d)){this._tryToAddEllipsisToLastLine();break}if((S=(S=S.slice(C)).trimLeft()).length>0&&(x=this._getTextWidth(S))<=h){this._addTextLine(S),d+=r,n=Math.max(n,x);break}}else this._addTextLine(S),d+=r,n=Math.max(n,x),this._shouldHandleEllipsis(d)&&_<w-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),l&&d+r>u)break}this.textHeight=e,this.textWidth=n}_shouldHandleEllipsis(t){var e=+this.fontSize(),n=this.lineHeight()*e,r=this.attrs.height,i=r!==s&&void 0!==r,o=r-2*this.padding();return!(this.wrap()!==p)||i&&t+n>o}_tryToAddEllipsisToLastLine(){var t=this.attrs.width,e=t!==s&&void 0!==t,n=t-2*this.padding(),r=this.ellipsis(),i=this.textArr[this.textArr.length-1];if(i&&r){if(e)this._getTextWidth(i.text+"…")<n||(i.text=i.text.slice(0,i.text.length-3));this.textArr.splice(this.textArr.length-1,1),this._addTextLine(i.text+"…")}}getStrokeScaleEnabled(){return!0}_useBufferCanvas(){const t=-1!==this.textDecoration().indexOf("underline")||-1!==this.textDecoration().indexOf("line-through"),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas()}};return Im.Text=v,v.prototype._fillFunc=function(t){t.fillText(this._partialText,this._partialTextX,this._partialTextY)},v.prototype._strokeFunc=function(t){t.setAttr("miterLimit",2),t.strokeText(this._partialText,this._partialTextX,this._partialTextY)},v.prototype.className="Text",v.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"],(0,i._registerNode)(v),e.Factory.overWriteSetter(v,"width",(0,r.getNumberOrAutoValidator)()),e.Factory.overWriteSetter(v,"height",(0,r.getNumberOrAutoValidator)()),e.Factory.addGetterSetter(v,"direction",l),e.Factory.addGetterSetter(v,"fontFamily","Arial"),e.Factory.addGetterSetter(v,"fontSize",12,(0,r.getNumberValidator)()),e.Factory.addGetterSetter(v,"fontStyle",d),e.Factory.addGetterSetter(v,"fontVariant",d),e.Factory.addGetterSetter(v,"padding",0,(0,r.getNumberValidator)()),e.Factory.addGetterSetter(v,"align",h),e.Factory.addGetterSetter(v,"verticalAlign","top"),e.Factory.addGetterSetter(v,"lineHeight",1,(0,r.getNumberValidator)()),e.Factory.addGetterSetter(v,"wrap","word"),e.Factory.addGetterSetter(v,"ellipsis",!1,(0,r.getBooleanValidator)()),e.Factory.addGetterSetter(v,"letterSpacing",0,(0,r.getNumberValidator)()),e.Factory.addGetterSetter(v,"text","",(0,r.getStringValidator)()),e.Factory.addGetterSetter(v,"textDecoration",""),Im}var Um,Gm={};var Bm,Vm={};function Hm(){if(Bm)return Vm;Bm=1,Object.defineProperty(Vm,"__esModule",{value:!0}),Vm.Transformer=void 0;const t=pg(),e=wg(),n=Fg(),r=Wg(),i=Pm(),o=Qg(),a=ug(),s=_g(),l=ug();var c="tr-konva",h=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange","anchorStyleFuncChange"].map((t=>t+`.${c}`)).join(" "),u="nodesRect",d=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],f={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135};const p="ontouchstart"in a.Konva._global;var g=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function m(t,e,n){const r=n.x+(t.x-n.x)*Math.cos(e)-(t.y-n.y)*Math.sin(e),i=n.y+(t.x-n.x)*Math.sin(e)+(t.y-n.y)*Math.cos(e);return{...t,rotation:t.rotation+e,x:r,y:i}}function y(t,e){const n=function(t){return{x:t.x+t.width/2*Math.cos(t.rotation)+t.height/2*Math.sin(-t.rotation),y:t.y+t.height/2*Math.cos(t.rotation)+t.width/2*Math.sin(t.rotation)}}(t);return m(t,e,n)}let v=class extends o.Group{constructor(t){super(t),this._movingAnchorName=null,this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(h,this.update),this.getNode()&&this.update()}attachTo(t){return this.setNode(t),this}setNode(e){return t.Util.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([e])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return c+this._id}setNodes(e=[]){this._nodes&&this._nodes.length&&this.detach();const n=e.filter((e=>!e.isAncestorOf(this)||(t.Util.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1)));return this._nodes=e=n,1===e.length&&this.useSingleNodeRotation()?this.rotation(e[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach((t=>{const e=()=>{1===this.nodes().length&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),this._transforming||this.isDragging()||this.update()},n=t._attrsAffectingSize.map((t=>t+"Change."+this._getEventNamespace())).join(" ");t.on(n,e),t.on(d.map((t=>t+`.${this._getEventNamespace()}`)).join(" "),e),t.on(`absoluteTransformChange.${this._getEventNamespace()}`,e),this._proxyDrag(t)})),this._resetTransformCache(),!!this.findOne(".top-left")&&this.update(),this}_proxyDrag(t){let e;t.on(`dragstart.${this._getEventNamespace()}`,(n=>{e=t.getAbsolutePosition(),this.isDragging()||t===this.findOne(".back")||this.startDrag(n,!1)})),t.on(`dragmove.${this._getEventNamespace()}`,(n=>{if(!e)return;const r=t.getAbsolutePosition(),i=r.x-e.x,o=r.y-e.y;this.nodes().forEach((e=>{if(e===t)return;if(e.isDragging())return;const r=e.getAbsolutePosition();e.setAbsolutePosition({x:r.x+i,y:r.y+o}),e.startDrag(n)})),e=null}))}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach((t=>{t.off("."+this._getEventNamespace())})),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(u),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(u,this.__getNodeRect)}__getNodeShape(t,e=this.rotation(),n){var r=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),i=t.getAbsoluteScale(n),o=t.getAbsolutePosition(n),s=r.x*i.x-t.offsetX()*i.x,l=r.y*i.y-t.offsetY()*i.y;const c=(a.Konva.getAngle(t.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return m({x:o.x+s*Math.cos(c)+l*Math.sin(-c),y:o.y+l*Math.cos(c)+s*Math.sin(c),width:r.width*i.x,height:r.height*i.y,rotation:c},-a.Konva.getAngle(e),{x:0,y:0})}__getNodeRect(){if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};const e=[];this.nodes().map((t=>{const n=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()});var r=[{x:n.x,y:n.y},{x:n.x+n.width,y:n.y},{x:n.x+n.width,y:n.y+n.height},{x:n.x,y:n.y+n.height}],i=t.getAbsoluteTransform();r.forEach((function(t){var n=i.point(t);e.push(n)}))}));const n=new t.Transform;n.rotate(-a.Konva.getAngle(this.rotation()));var r=1/0,i=1/0,o=-1/0,s=-1/0;e.forEach((function(t){var e=n.point(t);void 0===r&&(r=o=e.x,i=s=e.y),r=Math.min(r,e.x),i=Math.min(i,e.y),o=Math.max(o,e.x),s=Math.max(s,e.y)})),n.invert();const l=n.point({x:r,y:i});return{x:l.x,y:l.y,width:o-r,height:s-i,rotation:a.Konva.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),g.forEach((t=>{this._createAnchor(t)})),this._createAnchor("rotater")}_createAnchor(e){var n=new i.Rect({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:e+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:p?10:"auto"}),r=this;n.on("mousedown touchstart",(function(t){r._handleMouseDown(t)})),n.on("dragstart",(t=>{n.stopDrag(),t.cancelBubble=!0})),n.on("dragend",(t=>{t.cancelBubble=!0})),n.on("mouseenter",(()=>{var r=a.Konva.getAngle(this.rotation()),i=this.rotateAnchorCursor(),o=function(e,n,r){if("rotater"===e)return r;n+=t.Util.degToRad(f[e]||0);var i=(t.Util.radToDeg(n)%360+360)%360;return t.Util._inRange(i,337.5,360)||t.Util._inRange(i,0,22.5)?"ns-resize":t.Util._inRange(i,22.5,67.5)?"nesw-resize":t.Util._inRange(i,67.5,112.5)?"ew-resize":t.Util._inRange(i,112.5,157.5)?"nwse-resize":t.Util._inRange(i,157.5,202.5)?"ns-resize":t.Util._inRange(i,202.5,247.5)?"nesw-resize":t.Util._inRange(i,247.5,292.5)?"ew-resize":t.Util._inRange(i,292.5,337.5)?"nwse-resize":(t.Util.error("Transformer has unknown angle for cursor detection: "+i),"pointer")}(e,r,i);n.getStage().content&&(n.getStage().content.style.cursor=o),this._cursorChange=!0})),n.on("mouseout",(()=>{n.getStage().content&&(n.getStage().content.style.cursor=""),this._cursorChange=!1})),this.add(n)}_createBack(){var e=new r.Shape({name:"back",width:0,height:0,draggable:!0,sceneFunc(e,n){var r=n.getParent(),i=r.padding();e.beginPath(),e.rect(-i,-i,n.width()+2*i,n.height()+2*i),e.moveTo(n.width()/2,-i),r.rotateEnabled()&&r.rotateLineVisible()&&e.lineTo(n.width()/2,-r.rotateAnchorOffset()*t.Util._sign(n.height())-i),e.fillStrokeShape(n)},hitFunc:(t,e)=>{if(this.shouldOverdrawWholeArea()){var n=this.padding();t.beginPath(),t.rect(-n,-n,e.width()+2*n,e.height()+2*n),t.fillStrokeShape(e)}}});this.add(e),this._proxyDrag(e),e.on("dragstart",(t=>{t.cancelBubble=!0})),e.on("dragmove",(t=>{t.cancelBubble=!0})),e.on("dragend",(t=>{t.cancelBubble=!0})),this.on("dragmove",(t=>{this.update()}))}_handleMouseDown(t){this._movingAnchorName=t.target.name().split(" ")[0];var e=this._getNodeRect(),n=e.width,r=e.height,i=Math.sqrt(Math.pow(n,2)+Math.pow(r,2));this.sin=Math.abs(r/i),this.cos=Math.abs(n/i),"undefined"!=typeof window&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;var o=t.target.getAbsolutePosition(),a=t.target.getStage().getPointerPosition();this._anchorDragOffset={x:a.x-o.x,y:a.y-o.y},this._fire("transformstart",{evt:t.evt,target:this.getNode()}),this._nodes.forEach((e=>{e._fire("transformstart",{evt:t.evt,target:e})}))}_handleMouseMove(t){var e,n,r,i=this.findOne("."+this._movingAnchorName),o=i.getStage();o.setPointersPositions(t);const s=o.getPointerPosition();let l={x:s.x-this._anchorDragOffset.x,y:s.y-this._anchorDragOffset.y};const c=i.getAbsolutePosition();this.anchorDragBoundFunc()&&(l=this.anchorDragBoundFunc()(c,l,t)),i.setAbsolutePosition(l);const h=i.getAbsolutePosition();if(c.x!==h.x||c.y!==h.y)if("rotater"!==this._movingAnchorName){var u,d=this.shiftBehavior();u="inverted"===d?this.keepRatio()&&!t.shiftKey:"none"===d?this.keepRatio():this.keepRatio()||t.shiftKey;var f=this.centeredScaling()||t.altKey;if("top-left"===this._movingAnchorName){if(u){var p=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};r=Math.sqrt(Math.pow(p.x-i.x(),2)+Math.pow(p.y-i.y(),2));var g=this.findOne(".top-left").x()>p.x?-1:1,m=this.findOne(".top-left").y()>p.y?-1:1;e=r*this.cos*g,n=r*this.sin*m,this.findOne(".top-left").x(p.x-e),this.findOne(".top-left").y(p.y-n)}}else if("top-center"===this._movingAnchorName)this.findOne(".top-left").y(i.y());else if("top-right"===this._movingAnchorName){if(u){p=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};r=Math.sqrt(Math.pow(i.x()-p.x,2)+Math.pow(p.y-i.y(),2));g=this.findOne(".top-right").x()<p.x?-1:1,m=this.findOne(".top-right").y()>p.y?-1:1;e=r*this.cos*g,n=r*this.sin*m,this.findOne(".top-right").x(p.x+e),this.findOne(".top-right").y(p.y-n)}var v=i.position();this.findOne(".top-left").y(v.y),this.findOne(".bottom-right").x(v.x)}else if("middle-left"===this._movingAnchorName)this.findOne(".top-left").x(i.x());else if("middle-right"===this._movingAnchorName)this.findOne(".bottom-right").x(i.x());else if("bottom-left"===this._movingAnchorName){if(u){p=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};r=Math.sqrt(Math.pow(p.x-i.x(),2)+Math.pow(i.y()-p.y,2));g=p.x<i.x()?-1:1,m=i.y()<p.y?-1:1;e=r*this.cos*g,n=r*this.sin*m,i.x(p.x-e),i.y(p.y+n)}v=i.position(),this.findOne(".top-left").x(v.x),this.findOne(".bottom-right").y(v.y)}else if("bottom-center"===this._movingAnchorName)this.findOne(".bottom-right").y(i.y());else if("bottom-right"===this._movingAnchorName){if(u){p=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};r=Math.sqrt(Math.pow(i.x()-p.x,2)+Math.pow(i.y()-p.y,2));g=this.findOne(".bottom-right").x()<p.x?-1:1,m=this.findOne(".bottom-right").y()<p.y?-1:1;e=r*this.cos*g,n=r*this.sin*m,this.findOne(".bottom-right").x(p.x+e),this.findOne(".bottom-right").y(p.y+n)}}else console.error(new Error("Wrong position argument of selection resizer: "+this._movingAnchorName));if(f=this.centeredScaling()||t.altKey){var b=this.findOne(".top-left"),_=this.findOne(".bottom-right"),w=b.x(),S=b.y(),x=this.getWidth()-_.x(),C=this.getHeight()-_.y();_.move({x:-w,y:-S}),b.move({x:x,y:C})}var A=this.findOne(".top-left").getAbsolutePosition();e=A.x,n=A.y;var E=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),P=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:e,y:n,width:E,height:P,rotation:a.Konva.getAngle(this.rotation())},t)}else{var k=this._getNodeRect();e=i.x()-k.width/2,n=-i.y()+k.height/2;let r=Math.atan2(-n,e)+Math.PI/2;k.height<0&&(r-=Math.PI);const o=a.Konva.getAngle(this.rotation())+r,s=a.Konva.getAngle(this.rotationSnapTolerance()),l=y(k,function(t,e,n){let r=e;for(let i=0;i<t.length;i++){const o=a.Konva.getAngle(t[i]),s=Math.abs(o-e)%(2*Math.PI);Math.min(s,2*Math.PI-s)<n&&(r=o)}return r}(this.rotationSnaps(),o,s)-k.rotation);this._fitNodesInto(l,t)}}_handleMouseUp(t){this._removeEvents(t)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(t){if(this._transforming){this._transforming=!1,"undefined"!=typeof window&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));var e=this.getNode();this._fire("transformend",{evt:t,target:e}),e&&this._nodes.forEach((e=>{e._fire("transformend",{evt:t,target:e})})),this._movingAnchorName=null}}_fitNodesInto(e,n){var r=this._getNodeRect();if(t.Util._inRange(e.width,2*-this.padding()-1,1))return void this.update();if(t.Util._inRange(e.height,2*-this.padding()-1,1))return void this.update();var i=new t.Transform;if(i.rotate(a.Konva.getAngle(this.rotation())),this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("left")>=0){const t=i.point({x:2*-this.padding(),y:0});e.x+=t.x,e.y+=t.y,e.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y}else if(this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("right")>=0){const t=i.point({x:2*this.padding(),y:0});this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.width+=2*this.padding()}if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("top")>=0){const t=i.point({x:0,y:2*-this.padding()});e.x+=t.x,e.y+=t.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}else if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("bottom")>=0){const t=i.point({x:0,y:2*this.padding()});this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}if(this.boundBoxFunc()){const n=this.boundBoxFunc()(r,e);n?e=n:t.Util.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}const o=1e7,s=new t.Transform;s.translate(r.x,r.y),s.rotate(r.rotation),s.scale(r.width/o,r.height/o);const l=new t.Transform,c=e.width/o,h=e.height/o;!1===this.flipEnabled()?(l.translate(e.x,e.y),l.rotate(e.rotation),l.translate(e.width<0?e.width:0,e.height<0?e.height:0),l.scale(Math.abs(c),Math.abs(h))):(l.translate(e.x,e.y),l.rotate(e.rotation),l.scale(c,h));const u=l.multiply(s.invert());this._nodes.forEach((e=>{var r;const i=e.getParent().getAbsoluteTransform(),o=e.getTransform().copy();o.translate(e.offsetX(),e.offsetY());const a=new t.Transform;a.multiply(i.copy().invert()).multiply(u).multiply(i).multiply(o);const s=a.decompose();e.setAttrs(s),this._fire("transform",{evt:n,target:e}),e._fire("transform",{evt:n,target:e}),null===(r=e.getLayer())||void 0===r||r.batchDraw()})),this.rotation(t.Util._getRotation(e.rotation)),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(t,e){this.findOne(t).setAttrs(e)}update(){var e,n=this._getNodeRect();this.rotation(t.Util._getRotation(n.rotation));var r=n.width,i=n.height,o=this.enabledAnchors(),a=this.resizeEnabled(),s=this.padding(),l=this.anchorSize();const c=this.find("._anchor");c.forEach((t=>{t.setAttrs({width:l,height:l,offsetX:l/2,offsetY:l/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})})),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:l/2+s,offsetY:l/2+s,visible:a&&o.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:r/2,y:0,offsetY:l/2+s,visible:a&&o.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:r,y:0,offsetX:l/2-s,offsetY:l/2+s,visible:a&&o.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:i/2,offsetX:l/2+s,visible:a&&o.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:r,y:i/2,offsetX:l/2-s,visible:a&&o.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:i,offsetX:l/2+s,offsetY:l/2-s,visible:a&&o.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:r/2,y:i,offsetY:l/2-s,visible:a&&o.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:r,y:i,offsetX:l/2-s,offsetY:l/2-s,visible:a&&o.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:r/2,y:-this.rotateAnchorOffset()*t.Util._sign(i)-s,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:r,height:i,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0});const h=this.anchorStyleFunc();h&&c.forEach((t=>{h(t)})),null===(e=this.getLayer())||void 0===e||e.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();var t=this.findOne("."+this._movingAnchorName);t&&t.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),o.Group.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return n.Node.prototype.toObject.call(this)}clone(t){return n.Node.prototype.clone.call(this,t)}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}};return Vm.Transformer=v,v.prototype.className="Transformer",(0,l._registerNode)(v),e.Factory.addGetterSetter(v,"enabledAnchors",g,(function(e){return e instanceof Array||t.Util.warn("enabledAnchors value should be an array"),e instanceof Array&&e.forEach((function(e){-1===g.indexOf(e)&&t.Util.warn("Unknown anchor name: "+e+". Available names are: "+g.join(", "))})),e||[]})),e.Factory.addGetterSetter(v,"flipEnabled",!0,(0,s.getBooleanValidator)()),e.Factory.addGetterSetter(v,"resizeEnabled",!0),e.Factory.addGetterSetter(v,"anchorSize",10,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"rotateEnabled",!0),e.Factory.addGetterSetter(v,"rotateLineVisible",!0),e.Factory.addGetterSetter(v,"rotationSnaps",[]),e.Factory.addGetterSetter(v,"rotateAnchorOffset",50,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"rotateAnchorCursor","crosshair"),e.Factory.addGetterSetter(v,"rotationSnapTolerance",5,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"borderEnabled",!0),e.Factory.addGetterSetter(v,"anchorStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(v,"anchorStrokeWidth",1,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"anchorFill","white"),e.Factory.addGetterSetter(v,"anchorCornerRadius",0,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"borderStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(v,"borderStrokeWidth",1,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"borderDash"),e.Factory.addGetterSetter(v,"keepRatio",!0),e.Factory.addGetterSetter(v,"shiftBehavior","default"),e.Factory.addGetterSetter(v,"centeredScaling",!1),e.Factory.addGetterSetter(v,"ignoreStroke",!1),e.Factory.addGetterSetter(v,"padding",0,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(v,"node"),e.Factory.addGetterSetter(v,"nodes"),e.Factory.addGetterSetter(v,"boundBoxFunc"),e.Factory.addGetterSetter(v,"anchorDragBoundFunc"),e.Factory.addGetterSetter(v,"anchorStyleFunc"),e.Factory.addGetterSetter(v,"shouldOverdrawWholeArea",!1),e.Factory.addGetterSetter(v,"useSingleNodeRotation",!0),e.Factory.backCompat(v,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"}),Vm}var zm,Wm={};var $m,Km={};function qm(){if($m)return Km;$m=1,Object.defineProperty(Km,"__esModule",{value:!0}),Km.Blur=void 0;const t=wg(),e=Fg(),n=_g();function r(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}var i=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],o=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];return Km.Blur=function(t){var e=Math.round(this.blurRadius());e>0&&function(t,e){var n,a,s,l,c,h,u,d,f,p,g,m,y,v,b,_,w,S,x,C,A,E,P,k,O=t.data,T=t.width,R=t.height,F=e+e+1,M=T-1,N=R-1,L=e+1,D=L*(L+1)/2,I=new r,j=null,U=I,G=null,B=null,V=i[e],H=o[e];for(s=1;s<F;s++)U=U.next=new r,s===L&&(j=U);for(U.next=I,u=h=0,a=0;a<R;a++){for(_=w=S=x=d=f=p=g=0,m=L*(C=O[h]),y=L*(A=O[h+1]),v=L*(E=O[h+2]),b=L*(P=O[h+3]),d+=D*C,f+=D*A,p+=D*E,g+=D*P,U=I,s=0;s<L;s++)U.r=C,U.g=A,U.b=E,U.a=P,U=U.next;for(s=1;s<L;s++)l=h+((M<s?M:s)<<2),d+=(U.r=C=O[l])*(k=L-s),f+=(U.g=A=O[l+1])*k,p+=(U.b=E=O[l+2])*k,g+=(U.a=P=O[l+3])*k,_+=C,w+=A,S+=E,x+=P,U=U.next;for(G=I,B=j,n=0;n<T;n++)O[h+3]=P=g*V>>H,0!==P?(P=255/P,O[h]=(d*V>>H)*P,O[h+1]=(f*V>>H)*P,O[h+2]=(p*V>>H)*P):O[h]=O[h+1]=O[h+2]=0,d-=m,f-=y,p-=v,g-=b,m-=G.r,y-=G.g,v-=G.b,b-=G.a,l=u+((l=n+e+1)<M?l:M)<<2,d+=_+=G.r=O[l],f+=w+=G.g=O[l+1],p+=S+=G.b=O[l+2],g+=x+=G.a=O[l+3],G=G.next,m+=C=B.r,y+=A=B.g,v+=E=B.b,b+=P=B.a,_-=C,w-=A,S-=E,x-=P,B=B.next,h+=4;u+=T}for(n=0;n<T;n++){for(w=S=x=_=f=p=g=d=0,m=L*(C=O[h=n<<2]),y=L*(A=O[h+1]),v=L*(E=O[h+2]),b=L*(P=O[h+3]),d+=D*C,f+=D*A,p+=D*E,g+=D*P,U=I,s=0;s<L;s++)U.r=C,U.g=A,U.b=E,U.a=P,U=U.next;for(c=T,s=1;s<=e;s++)h=c+n<<2,d+=(U.r=C=O[h])*(k=L-s),f+=(U.g=A=O[h+1])*k,p+=(U.b=E=O[h+2])*k,g+=(U.a=P=O[h+3])*k,_+=C,w+=A,S+=E,x+=P,U=U.next,s<N&&(c+=T);for(h=n,G=I,B=j,a=0;a<R;a++)O[3+(l=h<<2)]=P=g*V>>H,P>0?(P=255/P,O[l]=(d*V>>H)*P,O[l+1]=(f*V>>H)*P,O[l+2]=(p*V>>H)*P):O[l]=O[l+1]=O[l+2]=0,d-=m,f-=y,p-=v,g-=b,m-=G.r,y-=G.g,v-=G.b,b-=G.a,l=n+((l=a+L)<N?l:N)*T<<2,d+=_+=G.r=O[l],f+=w+=G.g=O[l+1],p+=S+=G.b=O[l+2],g+=x+=G.a=O[l+3],G=G.next,m+=C=B.r,y+=A=B.g,v+=E=B.b,b+=P=B.a,_-=C,w-=A,S-=E,x-=P,B=B.next,h+=T}}(t,e)},t.Factory.addGetterSetter(e.Node,"blurRadius",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Km}var Ym,Xm={};var Qm,Jm={};var Zm,ty={};var ey,ny={};var ry,iy={};var oy,ay={};var sy,ly={};var cy,hy={};var uy,dy={};function fy(){if(uy)return dy;uy=1,Object.defineProperty(dy,"__esModule",{value:!0}),dy.Kaleidoscope=void 0;const t=wg(),e=Fg(),n=pg(),r=_g();return dy.Kaleidoscope=function(t){var e,r,i,o,a,s,l,c,h,u=t.width,d=t.height,f=Math.round(this.kaleidoscopePower()),p=Math.round(this.kaleidoscopeAngle()),g=Math.floor(u*(p%360)/360);if(!(f<1)){var m=n.Util.createCanvasElement();m.width=u,m.height=d;var y=m.getContext("2d").getImageData(0,0,u,d);n.Util.releaseCanvas(m),function(t,e,n){var r,i,o,a,s=t.data,l=e.data,c=t.width,h=t.height,u=n.polarCenterX||c/2,d=n.polarCenterY||h/2,f=0,p=0,g=0,m=0,y=Math.sqrt(u*u+d*d);i=c-u,o=h-d,y=(a=Math.sqrt(i*i+o*o))>y?a:y;var v,b,_,w,S=h,x=c,C=360/x*Math.PI/180;for(b=0;b<x;b+=1)for(_=Math.sin(b*C),w=Math.cos(b*C),v=0;v<S;v+=1)i=Math.floor(u+y*v/S*w),f=s[0+(r=4*((o=Math.floor(d+y*v/S*_))*c+i))],p=s[r+1],g=s[r+2],m=s[r+3],l[0+(r=4*(b+v*c))]=f,l[r+1]=p,l[r+2]=g,l[r+3]=m}(t,y,{polarCenterX:u/2,polarCenterY:d/2});for(var v=u/Math.pow(2,f);v<=8;)v*=2,f-=1;var b=v=Math.ceil(v),_=0,w=b,S=1;for(g+v>u&&(_=b,w=0,S=-1),r=0;r<d;r+=1)for(e=_;e!==w;e+=S)c=4*(u*r+Math.round(e+g)%u),o=y.data[c+0],a=y.data[c+1],s=y.data[c+2],l=y.data[c+3],h=4*(u*r+e),y.data[h+0]=o,y.data[h+1]=a,y.data[h+2]=s,y.data[h+3]=l;for(r=0;r<d;r+=1)for(b=Math.floor(v),i=0;i<f;i+=1){for(e=0;e<b+1;e+=1)c=4*(u*r+e),o=y.data[c+0],a=y.data[c+1],s=y.data[c+2],l=y.data[c+3],h=4*(u*r+2*b-e-1),y.data[h+0]=o,y.data[h+1]=a,y.data[h+2]=s,y.data[h+3]=l;b*=2}!function(t,e,n){var r,i,o,a,s,l,c=t.data,h=e.data,u=t.width,d=t.height,f=n.polarCenterX||u/2,p=n.polarCenterY||d/2,g=0,m=0,y=0,v=0,b=Math.sqrt(f*f+p*p);i=u-f,o=d-p,b=(l=Math.sqrt(i*i+o*o))>b?l:b;var _,w,S,x=d,C=u,A=n.polarRotation||0;for(i=0;i<u;i+=1)for(o=0;o<d;o+=1)a=i-f,s=o-p,_=Math.sqrt(a*a+s*s)*x/b,w=(w=(180*Math.atan2(s,a)/Math.PI+360+A)%360)*C/360,S=Math.floor(w),g=c[0+(r=4*(Math.floor(_)*u+S))],m=c[r+1],y=c[r+2],v=c[r+3],h[0+(r=4*(o*u+i))]=g,h[r+1]=m,h[r+2]=y,h[r+3]=v}(y,t,{polarRotation:0})}},t.Factory.addGetterSetter(e.Node,"kaleidoscopePower",2,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"kaleidoscopeAngle",0,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),dy}var py,gy={};function my(){if(py)return gy;py=1,Object.defineProperty(gy,"__esModule",{value:!0}),gy.Mask=void 0;const t=wg(),e=Fg(),n=_g();function r(t,e,n){var r=4*(n*t.width+e),i=[];return i.push(t.data[r++],t.data[r++],t.data[r++],t.data[r++]),i}function i(t,e){return Math.sqrt(Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2))}return gy.Mask=function(t){var e=function(t,e){var n=r(t,0,0),o=r(t,t.width-1,0),a=r(t,0,t.height-1),s=r(t,t.width-1,t.height-1),l=e||10;if(i(n,o)<l&&i(o,s)<l&&i(s,a)<l&&i(a,n)<l){for(var c=function(t){for(var e=[0,0,0],n=0;n<t.length;n++)e[0]+=t[n][0],e[1]+=t[n][1],e[2]+=t[n][2];return e[0]/=t.length,e[1]/=t.length,e[2]/=t.length,e}([o,n,s,a]),h=[],u=0;u<t.width*t.height;u++){var d=i(c,[t.data[4*u],t.data[4*u+1],t.data[4*u+2]]);h[u]=d<l?0:255}return h}}(t,this.threshold());return e&&function(t,e){for(var n=0;n<t.width*t.height;n++)t.data[4*n+3]=e[n]}(t,e=function(t,e,n){for(var r=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],i=Math.round(Math.sqrt(r.length)),o=Math.floor(i/2),a=[],s=0;s<n;s++)for(var l=0;l<e;l++){for(var c=s*e+l,h=0,u=0;u<i;u++)for(var d=0;d<i;d++){var f=s+u-o,p=l+d-o;if(f>=0&&f<n&&p>=0&&p<e){var g=r[u*i+d];h+=t[f*e+p]*g}}a[c]=h}return a}(e=function(t,e,n){for(var r=[1,1,1,1,1,1,1,1,1],i=Math.round(Math.sqrt(r.length)),o=Math.floor(i/2),a=[],s=0;s<n;s++)for(var l=0;l<e;l++){for(var c=s*e+l,h=0,u=0;u<i;u++)for(var d=0;d<i;d++){var f=s+u-o,p=l+d-o;if(f>=0&&f<n&&p>=0&&p<e){var g=r[u*i+d];h+=t[f*e+p]*g}}a[c]=h>=1020?255:0}return a}(e=function(t,e,n){for(var r=[1,1,1,1,0,1,1,1,1],i=Math.round(Math.sqrt(r.length)),o=Math.floor(i/2),a=[],s=0;s<n;s++)for(var l=0;l<e;l++){for(var c=s*e+l,h=0,u=0;u<i;u++)for(var d=0;d<i;d++){var f=s+u-o,p=l+d-o;if(f>=0&&f<n&&p>=0&&p<e){var g=r[u*i+d];h+=t[f*e+p]*g}}a[c]=2040===h?255:0}return a}(e,t.width,t.height),t.width,t.height),t.width,t.height)),t},t.Factory.addGetterSetter(e.Node,"threshold",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),gy}var yy,vy={};var by,_y={};var wy,Sy={};var xy,Cy={};var Ay,Ey={};var Py,ky={};var Oy,Ty={};var Ry,Fy,My={};function Ny(){if(Fy)return lg;Fy=1,Object.defineProperty(lg,"__esModule",{value:!0}),lg.Konva=void 0;const t=im(),e=function(){if(om)return am;om=1,Object.defineProperty(am,"__esModule",{value:!0}),am.Arc=void 0;const t=wg(),e=Wg(),n=ug(),r=_g(),i=ug();let o=class extends e.Shape{_sceneFunc(t){var e=n.Konva.getAngle(this.angle()),r=this.clockwise();t.beginPath(),t.arc(0,0,this.outerRadius(),0,e,r),t.arc(0,0,this.innerRadius(),e,0,!r),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}getSelfRect(){const t=this.innerRadius(),e=this.outerRadius(),r=this.clockwise(),i=n.Konva.getAngle(r?360-this.angle():this.angle()),o=Math.cos(Math.min(i,Math.PI)),a=Math.sin(Math.min(Math.max(Math.PI,i),3*Math.PI/2)),s=Math.sin(Math.min(i,Math.PI/2)),l=o*(o>0?t:e),c=a*(a>0?t:e),h=s*(s>0?e:t);return{x:l,y:r?-1*h:c,width:1*e-l,height:h-c}}};return am.Arc=o,o.prototype._centroid=!0,o.prototype.className="Arc",o.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,i._registerNode)(o),t.Factory.addGetterSetter(o,"innerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"outerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"clockwise",!1,(0,r.getBooleanValidator)()),am}(),n=function(){if(fm)return lm;fm=1,Object.defineProperty(lm,"__esModule",{value:!0}),lm.Arrow=void 0;const t=wg(),e=hm(),n=_g(),r=ug(),i=mm();let o=class extends e.Line{_sceneFunc(t){super._sceneFunc(t);var e=2*Math.PI,n=this.points(),r=n,o=0!==this.tension()&&n.length>4;o&&(r=this.getTensionPoints());var a,s,l=this.pointerLength(),c=n.length;if(o){const t=[r[r.length-4],r[r.length-3],r[r.length-2],r[r.length-1],n[c-2],n[c-1]],e=i.Path.calcLength(r[r.length-4],r[r.length-3],"C",t),o=i.Path.getPointOnQuadraticBezier(Math.min(1,1-l/e),t[0],t[1],t[2],t[3],t[4],t[5]);a=n[c-2]-o.x,s=n[c-1]-o.y}else a=n[c-2]-n[c-4],s=n[c-1]-n[c-3];var h=(Math.atan2(s,a)+e)%e,u=this.pointerWidth();this.pointerAtEnding()&&(t.save(),t.beginPath(),t.translate(n[c-2],n[c-1]),t.rotate(h),t.moveTo(0,0),t.lineTo(-l,u/2),t.lineTo(-l,-u/2),t.closePath(),t.restore(),this.__fillStroke(t)),this.pointerAtBeginning()&&(t.save(),t.beginPath(),t.translate(n[0],n[1]),o?(a=(r[0]+r[2])/2-n[0],s=(r[1]+r[3])/2-n[1]):(a=n[2]-n[0],s=n[3]-n[1]),t.rotate((Math.atan2(-s,-a)+e)%e),t.moveTo(0,0),t.lineTo(-l,u/2),t.lineTo(-l,-u/2),t.closePath(),t.restore(),this.__fillStroke(t))}__fillStroke(t){var e=this.dashEnabled();e&&(this.attrs.dashEnabled=!1,t.setLineDash([])),t.fillStrokeShape(this),e&&(this.attrs.dashEnabled=!0)}getSelfRect(){const t=super.getSelfRect(),e=this.pointerWidth()/2;return{x:t.x-e,y:t.y-e,width:t.width+2*e,height:t.height+2*e}}};return lm.Arrow=o,o.prototype.className="Arrow",(0,r._registerNode)(o),t.Factory.addGetterSetter(o,"pointerLength",10,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(o,"pointerWidth",10,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(o,"pointerAtBeginning",!1),t.Factory.addGetterSetter(o,"pointerAtEnding",!0),lm}(),r=function(){if(ym)return vm;ym=1,Object.defineProperty(vm,"__esModule",{value:!0}),vm.Circle=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();let i=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.attrs.radius||0,0,2*Math.PI,!1),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius()!==t/2&&this.radius(t/2)}setHeight(t){this.radius()!==t/2&&this.radius(t/2)}};return vm.Circle=i,i.prototype._centroid=!0,i.prototype.className="Circle",i.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"radius",0,(0,n.getNumberValidator)()),vm}(),i=function(){if(bm)return _m;bm=1,Object.defineProperty(_m,"__esModule",{value:!0}),_m.Ellipse=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();let i=class extends e.Shape{_sceneFunc(t){var e=this.radiusX(),n=this.radiusY();t.beginPath(),t.save(),e!==n&&t.scale(1,n/e),t.arc(0,0,e,0,2*Math.PI,!1),t.restore(),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radiusX()}getHeight(){return 2*this.radiusY()}setWidth(t){this.radiusX(t/2)}setHeight(t){this.radiusY(t/2)}};return _m.Ellipse=i,i.prototype.className="Ellipse",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["radiusX","radiusY"],(0,r._registerNode)(i),t.Factory.addComponentsGetterSetter(i,"radius",["x","y"]),t.Factory.addGetterSetter(i,"radiusX",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"radiusY",0,(0,n.getNumberValidator)()),_m}(),o=function(){if(wm)return Sm;wm=1,Object.defineProperty(Sm,"__esModule",{value:!0}),Sm.Image=void 0;const t=pg(),e=wg(),n=Wg(),r=ug(),i=_g();class o extends n.Shape{constructor(t){super(t),this.on("imageChange.konva",(()=>{this._setImageLoad()})),this._setImageLoad()}_setImageLoad(){const t=this.image();t&&t.complete||t&&4===t.readyState||t&&t.addEventListener&&t.addEventListener("load",(()=>{this._requestDraw()}))}_useBufferCanvas(){return super._useBufferCanvas(!0)}_sceneFunc(e){const n=this.getWidth(),r=this.getHeight(),i=this.cornerRadius(),o=this.attrs.image;let a;if(o){const t=this.attrs.cropWidth,e=this.attrs.cropHeight;a=t&&e?[o,this.cropX(),this.cropY(),t,e,0,0,n,r]:[o,0,0,n,r]}(this.hasFill()||this.hasStroke()||i)&&(e.beginPath(),i?t.Util.drawRoundedRectPath(e,n,r,i):e.rect(0,0,n,r),e.closePath(),e.fillStrokeShape(this)),o&&(i&&e.clip(),e.drawImage.apply(e,a))}_hitFunc(e){var n=this.width(),r=this.height(),i=this.cornerRadius();e.beginPath(),i?t.Util.drawRoundedRectPath(e,n,r,i):e.rect(0,0,n,r),e.closePath(),e.fillStrokeShape(this)}getWidth(){var t,e;return null!==(t=this.attrs.width)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.width}getHeight(){var t,e;return null!==(t=this.attrs.height)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.height}static fromURL(e,n,r=null){var i=t.Util.createImageElement();i.onload=function(){var t=new o({image:i});n(t)},i.onerror=r,i.crossOrigin="Anonymous",i.src=e}}return Sm.Image=o,o.prototype.className="Image",(0,r._registerNode)(o),e.Factory.addGetterSetter(o,"cornerRadius",0,(0,i.getNumberOrArrayOfNumbersValidator)(4)),e.Factory.addGetterSetter(o,"image"),e.Factory.addComponentsGetterSetter(o,"crop",["x","y","width","height"]),e.Factory.addGetterSetter(o,"cropX",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(o,"cropY",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(o,"cropWidth",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(o,"cropHeight",0,(0,i.getNumberValidator)()),Sm}(),a=function(){if(xm)return Cm;xm=1,Object.defineProperty(Cm,"__esModule",{value:!0}),Cm.Tag=Cm.Label=void 0;const t=wg(),e=Wg(),n=Qg(),r=_g(),i=ug();var o=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],a="up",s="right",l="down",c="left",h=o.length;let u=class extends n.Group{constructor(t){super(t),this.on("add.konva",(function(t){this._addListeners(t.child),this._sync()}))}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(t){var e,n=this,r=function(){n._sync()};for(e=0;e<h;e++)t.on(o[e]+"Change.konva",r)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){var t,e,n,r,i,o,h,u=this.getText(),d=this.getTag();if(u&&d){switch(t=u.width(),e=u.height(),n=d.pointerDirection(),r=d.pointerWidth(),h=d.pointerHeight(),i=0,o=0,n){case a:i=t/2,o=-1*h;break;case s:i=t+r,o=e/2;break;case l:i=t/2,o=e+h;break;case c:i=-1*r,o=e/2}d.setAttrs({x:-1*i,y:-1*o,width:t,height:e}),u.setAttrs({x:-1*i,y:-1*o})}}};Cm.Label=u,u.prototype.className="Label",(0,i._registerNode)(u);class d extends e.Shape{_sceneFunc(t){var e=this.width(),n=this.height(),r=this.pointerDirection(),i=this.pointerWidth(),o=this.pointerHeight(),h=this.cornerRadius();let u=0,d=0,f=0,p=0;"number"==typeof h?u=d=f=p=Math.min(h,e/2,n/2):(u=Math.min(h[0]||0,e/2,n/2),d=Math.min(h[1]||0,e/2,n/2),p=Math.min(h[2]||0,e/2,n/2),f=Math.min(h[3]||0,e/2,n/2)),t.beginPath(),t.moveTo(u,0),r===a&&(t.lineTo((e-i)/2,0),t.lineTo(e/2,-1*o),t.lineTo((e+i)/2,0)),t.lineTo(e-d,0),t.arc(e-d,d,d,3*Math.PI/2,0,!1),r===s&&(t.lineTo(e,(n-o)/2),t.lineTo(e+i,n/2),t.lineTo(e,(n+o)/2)),t.lineTo(e,n-p),t.arc(e-p,n-p,p,0,Math.PI/2,!1),r===l&&(t.lineTo((e+i)/2,n),t.lineTo(e/2,n+o),t.lineTo((e-i)/2,n)),t.lineTo(f,n),t.arc(f,n-f,f,Math.PI/2,Math.PI,!1),r===c&&(t.lineTo(0,(n+o)/2),t.lineTo(-1*i,n/2),t.lineTo(0,(n-o)/2)),t.lineTo(0,u),t.arc(u,u,u,Math.PI,3*Math.PI/2,!1),t.closePath(),t.fillStrokeShape(this)}getSelfRect(){var t=0,e=0,n=this.pointerWidth(),r=this.pointerHeight(),i=this.pointerDirection(),o=this.width(),h=this.height();return i===a?(e-=r,h+=r):i===l?h+=r:i===c?(t-=1.5*n,o+=n):i===s&&(o+=1.5*n),{x:t,y:e,width:o,height:h}}}return Cm.Tag=d,d.prototype.className="Tag",(0,i._registerNode)(d),t.Factory.addGetterSetter(d,"pointerDirection","none"),t.Factory.addGetterSetter(d,"pointerWidth",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(d,"pointerHeight",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(d,"cornerRadius",0,(0,r.getNumberOrArrayOfNumbersValidator)(4)),Cm}(),s=hm(),l=mm(),c=Pm(),h=function(){if(km)return Om;km=1,Object.defineProperty(Om,"__esModule",{value:!0}),Om.RegularPolygon=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();let i=class extends e.Shape{_sceneFunc(t){const e=this._getPoints();t.beginPath(),t.moveTo(e[0].x,e[0].y);for(var n=1;n<e.length;n++)t.lineTo(e[n].x,e[n].y);t.closePath(),t.fillStrokeShape(this)}_getPoints(){const t=this.attrs.sides,e=this.attrs.radius||0,n=[];for(var r=0;r<t;r++)n.push({x:e*Math.sin(2*r*Math.PI/t),y:-1*e*Math.cos(2*r*Math.PI/t)});return n}getSelfRect(){const t=this._getPoints();var e=t[0].x,n=t[0].y,r=t[0].x,i=t[0].y;return t.forEach((t=>{e=Math.min(e,t.x),n=Math.max(n,t.x),r=Math.min(r,t.y),i=Math.max(i,t.y)})),{x:e,y:r,width:n-e,height:i-r}}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return Om.RegularPolygon=i,i.prototype.className="RegularPolygon",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"radius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"sides",0,(0,n.getNumberValidator)()),Om}(),u=function(){if(Tm)return Rm;Tm=1,Object.defineProperty(Rm,"__esModule",{value:!0}),Rm.Ring=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();var i=2*Math.PI;let o=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.innerRadius(),0,i,!1),t.moveTo(this.outerRadius(),0),t.arc(0,0,this.outerRadius(),i,0,!0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return Rm.Ring=o,o.prototype.className="Ring",o.prototype._centroid=!0,o.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(o),t.Factory.addGetterSetter(o,"innerRadius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(o,"outerRadius",0,(0,n.getNumberValidator)()),Rm}(),d=function(){if(Fm)return Mm;Fm=1,Object.defineProperty(Mm,"__esModule",{value:!0}),Mm.Sprite=void 0;const t=wg(),e=Wg(),n=tm(),r=_g(),i=ug();let o=class extends e.Shape{constructor(t){super(t),this._updated=!0,this.anim=new n.Animation((()=>{var t=this._updated;return this._updated=!1,t})),this.on("animationChange.konva",(function(){this.frameIndex(0)})),this.on("frameIndexChange.konva",(function(){this._updated=!0})),this.on("frameRateChange.konva",(function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())}))}_sceneFunc(t){var e=this.animation(),n=this.frameIndex(),r=4*n,i=this.animations()[e],o=this.frameOffsets(),a=i[r+0],s=i[r+1],l=i[r+2],c=i[r+3],h=this.image();if((this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,l,c),t.closePath(),t.fillStrokeShape(this)),h)if(o){var u=o[e],d=2*n;t.drawImage(h,a,s,l,c,u[d+0],u[d+1],l,c)}else t.drawImage(h,a,s,l,c,0,0,l,c)}_hitFunc(t){var e=this.animation(),n=this.frameIndex(),r=4*n,i=this.animations()[e],o=this.frameOffsets(),a=i[r+2],s=i[r+3];if(t.beginPath(),o){var l=o[e],c=2*n;t.rect(l[c+0],l[c+1],a,s)}else t.rect(0,0,a,s);t.closePath(),t.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){var t=this;this.interval=setInterval((function(){t._updateIndex()}),1e3/this.frameRate())}start(){if(!this.isRunning()){var t=this.getLayer();this.anim.setLayers(t),this._setInterval(),this.anim.start()}}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){var t=this.frameIndex(),e=this.animation();t<this.animations()[e].length/4-1?this.frameIndex(t+1):this.frameIndex(0)}};return Mm.Sprite=o,o.prototype.className="Sprite",(0,i._registerNode)(o),t.Factory.addGetterSetter(o,"animation"),t.Factory.addGetterSetter(o,"animations"),t.Factory.addGetterSetter(o,"frameOffsets"),t.Factory.addGetterSetter(o,"image"),t.Factory.addGetterSetter(o,"frameIndex",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"frameRate",17,(0,r.getNumberValidator)()),t.Factory.backCompat(o,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"}),Mm}(),f=function(){if(Nm)return Lm;Nm=1,Object.defineProperty(Lm,"__esModule",{value:!0}),Lm.Star=void 0;const t=wg(),e=Wg(),n=_g(),r=ug();let i=class extends e.Shape{_sceneFunc(t){var e=this.innerRadius(),n=this.outerRadius(),r=this.numPoints();t.beginPath(),t.moveTo(0,0-n);for(var i=1;i<2*r;i++){var o=i%2==0?n:e,a=o*Math.sin(i*Math.PI/r),s=-1*o*Math.cos(i*Math.PI/r);t.lineTo(a,s)}t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return Lm.Star=i,i.prototype.className="Star",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"numPoints",5,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"innerRadius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"outerRadius",0,(0,n.getNumberValidator)()),Lm}(),p=jm(),g=function(){if(Um)return Gm;Um=1,Object.defineProperty(Gm,"__esModule",{value:!0}),Gm.TextPath=void 0;const t=pg(),e=wg(),n=Wg(),r=mm(),i=jm(),o=_g(),a=ug();var s="normal";function l(t){t.fillText(this.partialText,0,0)}function c(t){t.strokeText(this.partialText,0,0)}let h=class extends n.Shape{constructor(e){super(e),this.dummyCanvas=t.Util.createCanvasElement(),this.dataArray=[],this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute(),this._setTextData()})),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_getTextPathLength(){return r.Path.getPathLength(this.dataArray)}_getPointAtLength(t){return this.attrs.data?t-1>this.pathLength?null:r.Path.getPointAtLengthOfDataArray(t,this.dataArray):null}_readDataAttribute(){this.dataArray=r.Path.parsePathData(this.attrs.data),this.pathLength=this._getTextPathLength()}_sceneFunc(t){t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",this.textBaseline()),t.setAttr("textAlign","left"),t.save();var e=this.textDecoration(),n=this.fill(),r=this.fontSize(),i=this.glyphInfo;"underline"===e&&t.beginPath();for(var o=0;o<i.length;o++){t.save();var a=i[o].p0;t.translate(a.x,a.y),t.rotate(i[o].rotation),this.partialText=i[o].text,t.fillStrokeShape(this),"underline"===e&&(0===o&&t.moveTo(0,r/2+1),t.lineTo(r,r/2+1)),t.restore()}"underline"===e&&(t.strokeStyle=n,t.lineWidth=r/20,t.stroke()),t.restore()}_hitFunc(t){t.beginPath();var e=this.glyphInfo;if(e.length>=1){var n=e[0].p0;t.moveTo(n.x,n.y)}for(var r=0;r<e.length;r++){var i=e[r].p1;t.lineTo(i.x,i.y)}t.setAttr("lineWidth",this.fontSize()),t.setAttr("strokeStyle",this.colorKey),t.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(t){return i.Text.prototype.setText.call(this,t)}_getContextFont(){return i.Text.prototype._getContextFont.call(this)}_getTextSize(t){var e=this.dummyCanvas.getContext("2d");e.save(),e.font=this._getContextFont();var n=e.measureText(t);return e.restore(),{width:n.width,height:parseInt(`${this.fontSize()}`,10)}}_setTextData(){const{width:t,height:e}=this._getTextSize(this.attrs.text);if(this.textWidth=t,this.textHeight=e,this.glyphInfo=[],!this.attrs.data)return null;const n=this.letterSpacing(),o=this.align(),a=this.kerningFunc(),s=Math.max(this.textWidth+((this.attrs.text||"").length-1)*n,0);let l=0;"center"===o&&(l=Math.max(0,this.pathLength/2-s/2)),"right"===o&&(l=Math.max(0,this.pathLength-s));const c=(0,i.stringToArray)(this.text());let h=l;for(var u=0;u<c.length;u++){const t=this._getPointAtLength(h);if(!t)return;let e=this._getTextSize(c[u]).width+n;if(" "===c[u]&&"justify"===o){const t=this.text().split(" ").length-1;e+=(this.pathLength-s)/t}const i=this._getPointAtLength(h+e);if(!i)return;const l=r.Path.getLineLength(t.x,t.y,i.x,i.y);let d=0;if(a)try{d=a(c[u-1],c[u])*this.fontSize()}catch(bv){d=0}t.x+=d,i.x+=d,this.textWidth+=d;const f=r.Path.getPointOnLine(d+l/2,t.x,t.y,i.x,i.y),p=Math.atan2(i.y-t.y,i.x-t.x);this.glyphInfo.push({transposeX:f.x,transposeY:f.y,text:c[u],rotation:p,p0:t,p1:i}),h+=e}}getSelfRect(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};var t=[];this.glyphInfo.forEach((function(e){t.push(e.p0.x),t.push(e.p0.y),t.push(e.p1.x),t.push(e.p1.y)}));for(var e,n,r=t[0]||0,i=t[0]||0,o=t[1]||0,a=t[1]||0,s=0;s<t.length/2;s++)e=t[2*s],n=t[2*s+1],r=Math.min(r,e),i=Math.max(i,e),o=Math.min(o,n),a=Math.max(a,n);var l=this.fontSize();return{x:r-l/2,y:o-l/2,width:i-r+l,height:a-o+l}}destroy(){return t.Util.releaseCanvas(this.dummyCanvas),super.destroy()}};return Gm.TextPath=h,h.prototype._fillFunc=l,h.prototype._strokeFunc=c,h.prototype._fillFuncHit=l,h.prototype._strokeFuncHit=c,h.prototype.className="TextPath",h.prototype._attrsAffectingSize=["text","fontSize","data"],(0,a._registerNode)(h),e.Factory.addGetterSetter(h,"data"),e.Factory.addGetterSetter(h,"fontFamily","Arial"),e.Factory.addGetterSetter(h,"fontSize",12,(0,o.getNumberValidator)()),e.Factory.addGetterSetter(h,"fontStyle",s),e.Factory.addGetterSetter(h,"align","left"),e.Factory.addGetterSetter(h,"letterSpacing",0,(0,o.getNumberValidator)()),e.Factory.addGetterSetter(h,"textBaseline","middle"),e.Factory.addGetterSetter(h,"fontVariant",s),e.Factory.addGetterSetter(h,"text",""),e.Factory.addGetterSetter(h,"textDecoration",null),e.Factory.addGetterSetter(h,"kerningFunc",null),Gm}(),m=Hm(),y=function(){if(zm)return Wm;zm=1,Object.defineProperty(Wm,"__esModule",{value:!0}),Wm.Wedge=void 0;const t=wg(),e=Wg(),n=ug(),r=_g(),i=ug();let o=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.radius(),0,n.Konva.getAngle(this.angle()),this.clockwise()),t.lineTo(0,0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return Wm.Wedge=o,o.prototype.className="Wedge",o.prototype._centroid=!0,o.prototype._attrsAffectingSize=["radius"],(0,i._registerNode)(o),t.Factory.addGetterSetter(o,"radius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"clockwise",!1),t.Factory.backCompat(o,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"}),Wm}(),v=qm(),b=function(){if(Ym)return Xm;Ym=1,Object.defineProperty(Xm,"__esModule",{value:!0}),Xm.Brighten=void 0;const t=wg(),e=Fg(),n=_g();return Xm.Brighten=function(t){var e,n=255*this.brightness(),r=t.data,i=r.length;for(e=0;e<i;e+=4)r[e]+=n,r[e+1]+=n,r[e+2]+=n},t.Factory.addGetterSetter(e.Node,"brightness",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Xm}(),_=function(){if(Qm)return Jm;Qm=1,Object.defineProperty(Jm,"__esModule",{value:!0}),Jm.Contrast=void 0;const t=wg(),e=Fg(),n=_g();return Jm.Contrast=function(t){var e,n=Math.pow((this.contrast()+100)/100,2),r=t.data,i=r.length,o=150,a=150,s=150;for(e=0;e<i;e+=4)o=r[e],a=r[e+1],s=r[e+2],o/=255,o-=.5,o*=n,o+=.5,a/=255,a-=.5,a*=n,a+=.5,s/=255,s-=.5,s*=n,s+=.5,o=(o*=255)<0?0:o>255?255:o,a=(a*=255)<0?0:a>255?255:a,s=(s*=255)<0?0:s>255?255:s,r[e]=o,r[e+1]=a,r[e+2]=s},t.Factory.addGetterSetter(e.Node,"contrast",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Jm}(),w=function(){if(Zm)return ty;Zm=1,Object.defineProperty(ty,"__esModule",{value:!0}),ty.Emboss=void 0;const t=wg(),e=Fg(),n=pg(),r=_g();return ty.Emboss=function(t){var e=10*this.embossStrength(),r=255*this.embossWhiteLevel(),i=this.embossDirection(),o=this.embossBlend(),a=0,s=0,l=t.data,c=t.width,h=t.height,u=4*c,d=h;switch(i){case"top-left":a=-1,s=-1;break;case"top":a=-1,s=0;break;case"top-right":a=-1,s=1;break;case"right":a=0,s=1;break;case"bottom-right":a=1,s=1;break;case"bottom":a=1,s=0;break;case"bottom-left":a=1,s=-1;break;case"left":a=0,s=-1;break;default:n.Util.error("Unknown emboss direction: "+i)}do{var f=(d-1)*u,p=a;d+p<1&&(p=0),d+p>h&&(p=0);var g=(d-1+p)*c*4,m=c;do{var y=f+4*(m-1),v=s;m+v<1&&(v=0),m+v>c&&(v=0);var b=g+4*(m-1+v),_=l[y]-l[b],w=l[y+1]-l[b+1],S=l[y+2]-l[b+2],x=_,C=x>0?x:-x;if((w>0?w:-w)>C&&(x=w),(S>0?S:-S)>C&&(x=S),x*=e,o){var A=l[y]+x,E=l[y+1]+x,P=l[y+2]+x;l[y]=A>255?255:A<0?0:A,l[y+1]=E>255?255:E<0?0:E,l[y+2]=P>255?255:P<0?0:P}else{var k=r-x;k<0?k=0:k>255&&(k=255),l[y]=l[y+1]=l[y+2]=k}}while(--m)}while(--d)},t.Factory.addGetterSetter(e.Node,"embossStrength",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossWhiteLevel",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossDirection","top-left",null,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossBlend",!1,null,t.Factory.afterSetFilter),ty}(),S=function(){if(ey)return ny;ey=1,Object.defineProperty(ny,"__esModule",{value:!0}),ny.Enhance=void 0;const t=wg(),e=Fg(),n=_g();function r(t,e,n,r,i){var o=n-e,a=i-r;return 0===o?r+a/2:0===a?r:a*((t-e)/o)+r}return ny.Enhance=function(t){var e,n,i,o,a=t.data,s=a.length,l=a[0],c=l,h=a[1],u=h,d=a[2],f=d,p=this.enhance();if(0!==p){for(o=0;o<s;o+=4)(e=a[o+0])<l?l=e:e>c&&(c=e),(n=a[o+1])<h?h=n:n>u&&(u=n),(i=a[o+2])<d?d=i:i>f&&(f=i);var g,m,y,v,b,_,w,S,x;for(c===l&&(c=255,l=0),u===h&&(u=255,h=0),f===d&&(f=255,d=0),p>0?(m=c+p*(255-c),y=l-p*(l-0),b=u+p*(255-u),_=h-p*(h-0),S=f+p*(255-f),x=d-p*(d-0)):(m=c+p*(c-(g=.5*(c+l))),y=l+p*(l-g),b=u+p*(u-(v=.5*(u+h))),_=h+p*(h-v),S=f+p*(f-(w=.5*(f+d))),x=d+p*(d-w)),o=0;o<s;o+=4)a[o+0]=r(a[o+0],l,c,y,m),a[o+1]=r(a[o+1],h,u,_,b),a[o+2]=r(a[o+2],d,f,x,S)}},t.Factory.addGetterSetter(e.Node,"enhance",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ny}(),x=(ry||(ry=1,Object.defineProperty(iy,"__esModule",{value:!0}),iy.Grayscale=void 0,iy.Grayscale=function(t){var e,n,r=t.data,i=r.length;for(e=0;e<i;e+=4)n=.34*r[e]+.5*r[e+1]+.16*r[e+2],r[e]=n,r[e+1]=n,r[e+2]=n}),iy),C=function(){if(oy)return ay;oy=1,Object.defineProperty(ay,"__esModule",{value:!0}),ay.HSL=void 0;const t=wg(),e=Fg(),n=_g();return t.Factory.addGetterSetter(e.Node,"hue",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"luminance",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ay.HSL=function(t){var e,n,r,i,o,a=t.data,s=a.length,l=Math.pow(2,this.saturation()),c=Math.abs(this.hue()+360)%360,h=127*this.luminance(),u=1*l*Math.cos(c*Math.PI/180),d=1*l*Math.sin(c*Math.PI/180),f=.299+.701*u+.167*d,p=.587-.587*u+.33*d,g=.114-.114*u-.497*d,m=.299-.299*u-.328*d,y=.587+.413*u+.035*d,v=.114-.114*u+.293*d,b=.299-.3*u+1.25*d,_=.587-.586*u-1.05*d,w=.114+.886*u-.2*d;for(e=0;e<s;e+=4)n=a[e+0],r=a[e+1],i=a[e+2],o=a[e+3],a[e+0]=f*n+p*r+g*i+h,a[e+1]=m*n+y*r+v*i+h,a[e+2]=b*n+_*r+w*i+h,a[e+3]=o},ay}(),A=function(){if(sy)return ly;sy=1,Object.defineProperty(ly,"__esModule",{value:!0}),ly.HSV=void 0;const t=wg(),e=Fg(),n=_g();return ly.HSV=function(t){var e,n,r,i,o,a=t.data,s=a.length,l=Math.pow(2,this.value()),c=Math.pow(2,this.saturation()),h=Math.abs(this.hue()+360)%360,u=l*c*Math.cos(h*Math.PI/180),d=l*c*Math.sin(h*Math.PI/180),f=.299*l+.701*u+.167*d,p=.587*l-.587*u+.33*d,g=.114*l-.114*u-.497*d,m=.299*l-.299*u-.328*d,y=.587*l+.413*u+.035*d,v=.114*l-.114*u+.293*d,b=.299*l-.3*u+1.25*d,_=.587*l-.586*u-1.05*d,w=.114*l+.886*u-.2*d;for(e=0;e<s;e+=4)n=a[e+0],r=a[e+1],i=a[e+2],o=a[e+3],a[e+0]=f*n+p*r+g*i,a[e+1]=m*n+y*r+v*i,a[e+2]=b*n+_*r+w*i,a[e+3]=o},t.Factory.addGetterSetter(e.Node,"hue",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"value",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ly}(),E=(cy||(cy=1,Object.defineProperty(hy,"__esModule",{value:!0}),hy.Invert=void 0,hy.Invert=function(t){var e,n=t.data,r=n.length;for(e=0;e<r;e+=4)n[e]=255-n[e],n[e+1]=255-n[e+1],n[e+2]=255-n[e+2]}),hy),P=fy(),k=my(),O=function(){if(yy)return vy;yy=1,Object.defineProperty(vy,"__esModule",{value:!0}),vy.Noise=void 0;const t=wg(),e=Fg(),n=_g();return vy.Noise=function(t){var e,n=255*this.noise(),r=t.data,i=r.length,o=n/2;for(e=0;e<i;e+=4)r[e+0]+=o-2*o*Math.random(),r[e+1]+=o-2*o*Math.random(),r[e+2]+=o-2*o*Math.random()},t.Factory.addGetterSetter(e.Node,"noise",.2,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),vy}(),T=function(){if(by)return _y;by=1,Object.defineProperty(_y,"__esModule",{value:!0}),_y.Pixelate=void 0;const t=wg(),e=pg(),n=Fg(),r=_g();return _y.Pixelate=function(t){var n,r,i,o,a,s,l,c,h,u,d,f,p,g,m=Math.ceil(this.pixelSize()),y=t.width,v=t.height,b=Math.ceil(y/m),_=Math.ceil(v/m),w=t.data;if(m<=0)e.Util.error("pixelSize value can not be <= 0");else for(f=0;f<b;f+=1)for(p=0;p<_;p+=1){for(o=0,a=0,s=0,l=0,h=(c=f*m)+m,d=(u=p*m)+m,g=0,n=c;n<h;n+=1)if(!(n>=y))for(r=u;r<d;r+=1)r>=v||(o+=w[0+(i=4*(y*r+n))],a+=w[i+1],s+=w[i+2],l+=w[i+3],g+=1);for(o/=g,a/=g,s/=g,l/=g,n=c;n<h;n+=1)if(!(n>=y))for(r=u;r<d;r+=1)r>=v||(w[0+(i=4*(y*r+n))]=o,w[i+1]=a,w[i+2]=s,w[i+3]=l)}},t.Factory.addGetterSetter(n.Node,"pixelSize",8,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),_y}(),R=function(){if(wy)return Sy;wy=1,Object.defineProperty(Sy,"__esModule",{value:!0}),Sy.Posterize=void 0;const t=wg(),e=Fg(),n=_g();return Sy.Posterize=function(t){var e,n=Math.round(254*this.levels())+1,r=t.data,i=r.length,o=255/n;for(e=0;e<i;e+=1)r[e]=Math.floor(r[e]/o)*o},t.Factory.addGetterSetter(e.Node,"levels",.5,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Sy}(),F=function(){if(xy)return Cy;xy=1,Object.defineProperty(Cy,"__esModule",{value:!0}),Cy.RGB=void 0;const t=wg(),e=Fg(),n=_g();return Cy.RGB=function(t){var e,n,r=t.data,i=r.length,o=this.red(),a=this.green(),s=this.blue();for(e=0;e<i;e+=4)n=(.34*r[e]+.5*r[e+1]+.16*r[e+2])/255,r[e]=n*o,r[e+1]=n*a,r[e+2]=n*s,r[e+3]=r[e+3]},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,n.RGBComponent,t.Factory.afterSetFilter),Cy}(),M=function(){if(Ay)return Ey;Ay=1,Object.defineProperty(Ey,"__esModule",{value:!0}),Ey.RGBA=void 0;const t=wg(),e=Fg(),n=_g();return Ey.RGBA=function(t){var e,n,r=t.data,i=r.length,o=this.red(),a=this.green(),s=this.blue(),l=this.alpha();for(e=0;e<i;e+=4)n=1-l,r[e]=o*l+r[e]*n,r[e+1]=a*l+r[e+1]*n,r[e+2]=s*l+r[e+2]*n},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,n.RGBComponent,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"alpha",1,(function(t){return this._filterUpToDate=!1,t>1?1:t<0?0:t})),Ey}(),N=(Py||(Py=1,Object.defineProperty(ky,"__esModule",{value:!0}),ky.Sepia=void 0,ky.Sepia=function(t){var e,n,r,i,o=t.data,a=o.length;for(e=0;e<a;e+=4)n=o[e+0],r=o[e+1],i=o[e+2],o[e+0]=Math.min(255,.393*n+.769*r+.189*i),o[e+1]=Math.min(255,.349*n+.686*r+.168*i),o[e+2]=Math.min(255,.272*n+.534*r+.131*i)}),ky),L=(Oy||(Oy=1,Object.defineProperty(Ty,"__esModule",{value:!0}),Ty.Solarize=void 0,Ty.Solarize=function(t){var e=t.data,n=t.width,r=4*n,i=t.height;do{var o=(i-1)*r,a=n;do{var s=o+4*(a-1),l=e[s],c=e[s+1],h=e[s+2];l>127&&(l=255-l),c>127&&(c=255-c),h>127&&(h=255-h),e[s]=l,e[s+1]=c,e[s+2]=h}while(--a)}while(--i)}),Ty),D=function(){if(Ry)return My;Ry=1,Object.defineProperty(My,"__esModule",{value:!0}),My.Threshold=void 0;const t=wg(),e=Fg(),n=_g();return My.Threshold=function(t){var e,n=255*this.threshold(),r=t.data,i=r.length;for(e=0;e<i;e+=1)r[e]=r[e]<n?0:255},t.Factory.addGetterSetter(e.Node,"threshold",.5,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),My}();return lg.Konva=t.Konva.Util._assign(t.Konva,{Arc:e.Arc,Arrow:n.Arrow,Circle:r.Circle,Ellipse:i.Ellipse,Image:o.Image,Label:a.Label,Tag:a.Tag,Line:s.Line,Path:l.Path,Rect:c.Rect,RegularPolygon:h.RegularPolygon,Ring:u.Ring,Sprite:d.Sprite,Star:f.Star,Text:p.Text,TextPath:g.TextPath,Transformer:m.Transformer,Wedge:y.Wedge,Filters:{Blur:v.Blur,Brighten:b.Brighten,Contrast:_.Contrast,Emboss:w.Emboss,Enhance:S.Enhance,Grayscale:x.Grayscale,HSL:C.HSL,HSV:A.HSV,Invert:E.Invert,Kaleidoscope:P.Kaleidoscope,Mask:k.Mask,Noise:O.Noise,Pixelate:T.Pixelate,Posterize:R.Posterize,RGB:F.RGB,RGBA:M.RGBA,Sepia:N.Sepia,Solarize:L.Solarize,Threshold:D.Threshold}}),lg}var Ly,Dy=sg.exports;const Iy=xc(ig.exports=function(t,e){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=2)}([function(e,n){e.exports=t},function(t,n){t.exports=e},function(t,e,n){t.exports=n(3)},function(t,e,n){n.r(e);var r=n(0),i=n(1),o=n.n(i);function a(t){if(!o.a.autoDrawEnabled){var e=t.getLayer()||t.getStage();e&&e.batchDraw()}}var s={key:!0,style:!0,elm:!0,isRootInsert:!0},l=".vue-konva-event";function c(t,e,n,r){void 0===e&&(e={}),void 0===n&&(n={});var i=t.__konvaNode,o={},c=!1;for(var h in n)if(!s[h]){var u="on"===h.slice(0,2),d=n[h]!==e[h];if(u&&d){var f=h.substr(2).toLowerCase();"content"===f.substr(0,7)&&(f="content"+f.substr(7,1).toUpperCase()+f.substr(8)),i.off(f+l,n[h])}!e.hasOwnProperty(h)&&i.setAttr(h,void 0)}for(var p in e)if(!s[p]){var g="on"===p.slice(0,2),m=n[p]!==e[p];if(g&&m){var y=p.substr(2).toLowerCase();"content"===y.substr(0,7)&&(y="content"+y.substr(7,1).toUpperCase()+y.substr(8)),e[p]&&(i.off(y+l),i.on(y+l,e[p]))}!g&&(e[p]!==n[p]||r&&e[p]!==i.getAttr(p))&&(c=!0,o[p]=e[p])}c&&(i.setAttrs(o),a(i))}function h(t){if(null==t?void 0:t.component)return t.component.__konvaNode||h(t.component.subTree)}function u(t){var e=t.el,n=t.component,r=h(t);if((null==e?void 0:e.tagName)&&n&&!r){var i=e&&e.tagName.toLowerCase();return console.error('vue-konva error: You are trying to render "'+i+'" inside your component tree. Looks like it is not a Konva node. You can render only Konva components inside the Stage.'),null}return r}function d(t,e){var n,r,i=(r=[],(n=t).children&&n.children.forEach((function(t){!t.component&&Array.isArray(t.children)&&t.children.forEach((function(t){!t.component&&Array.isArray(t.children)?r.push.apply(r,t.children):r.push(t)})),t.component&&r.push(t)})),r),o=[];i.forEach((function(t){var e=u(t);e&&o.push(e)}));var s=!1;o.forEach((function(t,e){t.getZIndex()!==e&&(t.setZIndex(e),s=!0)})),s&&a(e)}function f(){return f=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},f.apply(this,arguments)}var p={props:{config:{type:Object,default:function(){return{}}},__useStrictMode:{type:Boolean}},inheritAttrs:!1,setup:function(t,e){var n=e.attrs,i=e.slots,o=e.expose;e.emits;var a=Object(r.getCurrentInstance)(),s=Object(r.reactive)({}),l=Object(r.ref)(null),h=new window.Konva.Stage({width:t.config.width,height:t.config.height,container:document.createElement("div")});function u(){var e=s||{},r=f({},n,t.config);c(a,r,e,t.__useStrictMode),Object.assign(s,r)}return a.__konvaNode=h,u(),Object(r.onMounted)((function(){l.value.innerHTML="",h.container(l.value),u()})),Object(r.onUpdated)((function(){u(),d(a.subTree,h)})),Object(r.onBeforeUnmount)((function(){h.destroy()})),Object(r.watch)((function(){return t.config}),u,{deep:!0}),o({getStage:function(){return a.__konvaNode},getNode:function(){return a.__konvaNode}}),function(){var t;return Object(r.h)("div",{ref:l},null===(t=i.default)||void 0===t?void 0:t.call(i))}}};function g(){return g=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},g.apply(this,arguments)}var m={Group:!0,Layer:!0,FastLayer:!0,Label:!0},y=function(t){return{props:{config:{type:Object,default:function(){return{}}},__useStrictMode:{type:Boolean}},setup:function(e,n){var i=n.attrs,o=n.slots,s=n.expose,l=Object(r.getCurrentInstance)(),h=Object(r.reactive)({}),u=window.Konva[t];if(u){var f=new u;return l.__konvaNode=f,l.vnode.__konvaNode=f,p(),Object(r.onMounted)((function(){var t=function(t){return function t(e){return e.__konvaNode?e:e.parent?t(e.parent):(console.error("vue-konva error: Can not find parent node"),{})}(t.parent)}(l).__konvaNode;t.add(f),a(f)})),Object(r.onUnmounted)((function(){a(f),f.destroy(),f.off(".vue-konva-event")})),Object(r.onUpdated)((function(){p(),d(l.subTree,f)})),Object(r.watch)((function(){return e.config}),p,{deep:!0}),s({getStage:function(){return l.__konvaNode},getNode:function(){return l.__konvaNode}}),m[t]?function(){var t;return Object(r.h)("template",{},null===(t=o.default)||void 0===t?void 0:t.call(o))}:function(){return null}}function p(){var t={};for(var n in l.vnode.props)"on"===n.slice(0,2)&&(t[n]=l.vnode.props[n]);var r=h||{},o=g({},i,e.config,t);c(l,o,r,e.__useStrictMode),Object.assign(h,o)}console.error("vue-konva error: Can not find node "+t)}}};"undefined"==typeof window||window.Konva||n(1);var v=[{name:"Stage",component:p}].concat(["Layer","FastLayer","Group","Label","Rect","Circle","Ellipse","Wedge","Line","Sprite","Image","Text","TextPath","Star","Ring","Arc","Tag","Path","RegularPolygon","Arrow","Shape","Transformer"].map((function(t){return{name:t,component:y(t)}}))),b={install:function(t,e){var n="v";e&&e.prefix&&(n=e.prefix),v.forEach((function(e){t.component(""+n+e.name,e.component)}))}};e.default=b}]).default}(og,function(){if(Ly)return sg.exports;Ly=1,Object.defineProperty(Dy,"__esModule",{value:!0});const t=Ny();return sg.exports=t.Konva,sg.exports}
/*!
 * vue-konva v3.0.1 - https://github.com/konvajs/vue-konva#readme
 * MIT Licensed
 */()));
/*!
 * Vue-Lazyload.js v3.0.0
 * (c) 2023 Awe <<EMAIL>>
 * Released under the MIT License.
 */
function jy(t,e){return t(e={exports:{}},e.exports),e.exports}var Uy=jy((function(t){const e=Object.prototype.toString,n=Object.prototype.propertyIsEnumerable,r=Object.getOwnPropertySymbols;t.exports=(t,...i)=>{if("function"!=typeof(o=t)&&"[object Object]"!==e.call(o)&&!Array.isArray(o))throw new TypeError("expected the first argument to be an object");var o;if(0===i.length||"function"!=typeof Symbol||"function"!=typeof r)return t;for(let e of i){let i=r(e);for(let r of i)n.call(e,r)&&(t[r]=e[r])}return t}})),Gy=Object.freeze({__proto__:null,default:Uy,__moduleExports:Uy}),By=Gy&&Uy||Gy,Vy=jy((function(t){const e=Object.prototype.toString,n=t=>"__proto__"!==t&&"constructor"!==t&&"prototype"!==t,r=t.exports=(t,...e)=>{let o=0;var a;for(("object"==typeof(a=t)?null===a:"function"!=typeof a)&&(t=e[o++]),t||(t={});o<e.length;o++)if(i(e[o])){for(const a of Object.keys(e[o]))n(a)&&(i(t[a])&&i(e[o][a])?r(t[a],e[o][a]):t[a]=e[o][a]);By(t,e[o])}return t};function i(t){return"function"==typeof t||"[object Object]"===e.call(t)}}));const Hy="undefined"!=typeof window&&null!==window,zy=function(){if(Hy&&"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}}),!0;return!1}();const Wy="event",$y="observer";function Ky(t,e){if(!t.length)return;const n=t.indexOf(e);return n>-1?t.splice(n,1):void 0}function qy(t,e){if("IMG"!==t.tagName||!t.getAttribute("data-srcset"))return"";let n=t.getAttribute("data-srcset").trim().split(",");const r=[],i=t.parentNode.offsetWidth*e;let o,a,s;n.forEach((t=>{t=t.trim(),o=t.lastIndexOf(" "),-1===o?(a=t,s=99999):(a=t.substr(0,o),s=parseInt(t.substr(o+1,t.length-o-2),10)),r.push([s,a])})),r.sort(((t,e)=>{if(t[0]<e[0])return 1;if(t[0]>e[0])return-1;if(t[0]===e[0]){if(-1!==e[1].indexOf(".webp",e[1].length-5))return 1;if(-1!==t[1].indexOf(".webp",t[1].length-5))return-1}return 0}));let l,c="";for(let h=0;h<r.length;h++){l=r[h],c=l[1];const t=r[h+1];if(t&&t[0]<i){c=l[1];break}if(!t){c=l[1];break}}return c}const Yy=(t=1)=>Hy&&window.devicePixelRatio||t;function Xy(){if(!Hy)return!1;let t=!0;function e(t,e){const n=new Image;n.onload=function(){const t=n.width>0&&n.height>0;e(t)},n.onerror=function(){e(!1)},n.src="data:image/webp;base64,"+{lossy:"UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",lossless:"UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==",alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",animation:"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"}[t]}return e("lossy",(e=>{t=e})),e("lossless",(e=>{t=e})),e("alpha",(e=>{t=e})),e("animation",(e=>{t=e})),t}const Qy=function(){if(!Hy)return!1;let t=!1;try{const e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",nv,e)}catch(bv){}return t}(),Jy={on(t,e,n,r=!1){Qy?t.addEventListener(e,n,{capture:r,passive:!0}):t.addEventListener(e,n,r)},off(t,e,n,r=!1){t.removeEventListener(e,n,r)}},Zy=(t,e,n)=>{let r=new Image;if(!t||!t.src){const t=new Error("image src is required");return n(t)}t.cors&&(r.crossOrigin=t.cors),r.src=t.src,r.onload=function(){e({naturalHeight:r.naturalHeight,naturalWidth:r.naturalWidth,src:r.src}),r=null},r.onerror=function(t){n(t)}},tv=(t,e)=>"undefined"!=typeof getComputedStyle?getComputedStyle(t,null).getPropertyValue(e):t.style[e],ev=t=>tv(t,"overflow")+tv(t,"overflowY")+tv(t,"overflowX");function nv(){}class rv{constructor(t){this.max=t||100,this._caches=[]}has(t){return this._caches.indexOf(t)>-1}add(t){this.has(t)||(this._caches.push(t),this._caches.length>this.max&&this.free())}free(){this._caches.shift()}}class iv{constructor(t,e,n,r,i,o,a,s,l,c){this.el=t,this.src=e,this.error=n,this.loading=r,this.bindType=i,this.attempt=0,this.cors=s,this.naturalHeight=0,this.naturalWidth=0,this.options=a,this.rect={},this.$parent=o,this.elRenderer=l,this._imageCache=c,this.performanceData={init:Date.now(),loadStart:0,loadEnd:0},this.filter(),this.initState(),this.render("loading",!1)}initState(){"dataset"in this.el?this.el.dataset.src=this.src:this.el.setAttribute("data-src",this.src),this.state={loading:!1,error:!1,loaded:!1,rendered:!1}}record(t){this.performanceData[t]=Date.now()}update(t){const e=this.src;this.src=t.src,this.loading=t.loading,this.error=t.error,this.filter(),e!==this.src&&(this.attempt=0,this.initState())}getRect(){this.rect=this.el.getBoundingClientRect()}checkInView(){return this.getRect(),this.rect.top<window.innerHeight*this.options.preLoad&&this.rect.bottom>this.options.preLoadTop&&this.rect.left<window.innerWidth*this.options.preLoad&&this.rect.right>0}filter(){for(const t in this.options.filter)this.options.filter[t](this,this.options)}renderLoading(t){this.state.loading=!0,Zy({src:this.loading,cors:this.cors},(()=>{this.render("loading",!1),this.state.loading=!1,t()}),(()=>{t(),this.state.loading=!1,this.options.silent||console.warn(`VueLazyload log: load failed with loading image(${this.loading})`)}))}load(t=nv){return this.attempt>this.options.attempt-1&&this.state.error?(this.options.silent||console.log(`VueLazyload log: ${this.src} tried too more than ${this.options.attempt} times`),void t()):this.state.rendered&&this.state.loaded?void 0:this._imageCache.has(this.src)?(this.state.loaded=!0,this.render("loaded",!0),this.state.rendered=!0,t()):void this.renderLoading((()=>{this.attempt++,this.options.adapter.beforeLoad&&this.options.adapter.beforeLoad(this,this.options),this.record("loadStart"),Zy({src:this.src,cors:this.cors},(e=>{this.naturalHeight=e.naturalHeight,this.naturalWidth=e.naturalWidth,this.state.loaded=!0,this.state.error=!1,this.record("loadEnd"),this.render("loaded",!1),this.state.rendered=!0,this._imageCache.add(this.src),t()}),(t=>{!this.options.silent&&console.error(t),this.state.error=!0,this.state.loaded=!1,this.render("error",!1)}))}))}render(t,e){this.elRenderer(this,t,e)}performance(){let t="loading",e=0;return this.state.loaded&&(t="loaded",e=(this.performanceData.loadEnd-this.performanceData.loadStart)/1e3),this.state.error&&(t="error"),{src:this.src,state:t,time:e}}$destroy(){this.el=null,this.src="",this.error=null,this.loading="",this.bindType=null,this.attempt=0}}const ov="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",av=["scroll","wheel","mousewheel","resize","animationend","transitionend","touchmove"],sv={rootMargin:"0px",threshold:0};class lv{constructor({preLoad:t,error:e,throttleWait:n,preLoadTop:r,dispatchEvent:i,loading:o,attempt:a,silent:s=!0,scale:l,listenEvents:c,filter:h,adapter:u,observer:d,observerOptions:f}){this.version='"3.0.0"',this.lazyContainerMananger=null,this.mode=Wy,this.ListenerQueue=[],this.TargetIndex=0,this.TargetQueue=[],this.options={silent:s,dispatchEvent:!!i,throttleWait:n||200,preLoad:t||1.3,preLoadTop:r||0,error:e||ov,loading:o||ov,attempt:a||3,scale:l||Yy(l),listenEvents:c||av,supportWebp:Xy(),filter:h||{},adapter:u||{},observer:!!d,observerOptions:f||sv},this._initEvent(),this._imageCache=new rv(200),this.lazyLoadHandler=function(t,e){let n=null,r=0;return function(){if(n)return;const i=Date.now()-r,o=this,a=arguments,s=function(){r=Date.now(),n=!1,t.apply(o,a)};i>=e?s():n=setTimeout(s,e)}}(this._lazyLoadHandler.bind(this),this.options.throttleWait),this.setMode(this.options.observer?$y:Wy)}performance(){const t=[];return this.ListenerQueue.map((e=>t.push(e.performance()))),t}addLazyBox(t){this.ListenerQueue.push(t),Hy&&(this._addListenerTarget(window),this._observer&&this._observer.observe(t.el),t.$el&&t.$el.parentNode&&this._addListenerTarget(t.$el.parentNode))}add(t,e,n){if(this.ListenerQueue.some((e=>e.el===t)))return this.update(t,e),rn(this.lazyLoadHandler);let{src:r,loading:i,error:o,cors:a}=this._valueFormatter(e.value);rn((()=>{r=qy(t,this.options.scale)||r,this._observer&&this._observer.observe(t);const n=Object.keys(e.modifiers)[0];let s;n&&(s=e.instance.$refs[n],s=s?s.el||s:document.getElementById(n)),s||(s=(t=>{if(!Hy)return;if(!(t instanceof Element))return window;let e=t;for(;e&&e!==document.body&&e!==document.documentElement&&e.parentNode;){if(/(scroll|auto)/.test(ev(e)))return e;e=e.parentNode}return window})(t));const l=new iv(t,r,o,i,e.arg,s,this.options,a,this._elRenderer.bind(this),this._imageCache);this.ListenerQueue.push(l),Hy&&(this._addListenerTarget(window),this._addListenerTarget(s)),rn(this.lazyLoadHandler)}))}update(t,e,n){let{src:r,loading:i,error:o}=this._valueFormatter(e.value);r=qy(t,this.options.scale)||r;const a=this.ListenerQueue.find((e=>e.el===t));a?a.update({src:r,loading:i,error:o}):"loaded"===t.getAttribute("lazy")&&t.dataset.src===r||this.add(t,e,n),this._observer&&(this._observer.unobserve(t),this._observer.observe(t)),rn(this.lazyLoadHandler)}remove(t){if(!t)return;this._observer&&this._observer.unobserve(t);const e=this.ListenerQueue.find((e=>e.el===t));e&&(this._removeListenerTarget(e.$parent),this._removeListenerTarget(window),Ky(this.ListenerQueue,e),e.$destroy&&e.$destroy())}removeComponent(t){t&&(Ky(this.ListenerQueue,t),this._observer&&this._observer.unobserve(t.el),t.$parent&&t.$el.parentNode&&this._removeListenerTarget(t.$el.parentNode),this._removeListenerTarget(window))}setMode(t){zy||t!==$y||(t=Wy),this.mode=t,t===Wy?(this._observer&&(this.ListenerQueue.forEach((t=>{this._observer.unobserve(t.el)})),this._observer=null),this.TargetQueue.forEach((t=>{this._initListen(t.el,!0)}))):(this.TargetQueue.forEach((t=>{this._initListen(t.el,!1)})),this._initIntersectionObserver())}_addListenerTarget(t){if(!t)return;let e=this.TargetQueue.find((e=>e.el===t));return e?e.childrenCount++:(e={el:t,id:++this.TargetIndex,childrenCount:1,listened:!0},this.mode===Wy&&this._initListen(e.el,!0),this.TargetQueue.push(e)),this.TargetIndex}_removeListenerTarget(t){this.TargetQueue.forEach(((e,n)=>{e.el===t&&(e.childrenCount--,e.childrenCount||(this._initListen(e.el,!1),this.TargetQueue.splice(n,1),e=null))}))}_initListen(t,e){this.options.listenEvents.forEach((n=>Jy[e?"on":"off"](t,n,this.lazyLoadHandler)))}_initEvent(){this.Event={listeners:{loading:[],loaded:[],error:[]}},this.$on=(t,e)=>{this.Event.listeners[t]||(this.Event.listeners[t]=[]),this.Event.listeners[t].push(e)},this.$once=(t,e)=>{const n=this;this.$on(t,(function r(){n.$off(t,r),e.apply(n,arguments)}))},this.$off=(t,e)=>{if(e)Ky(this.Event.listeners[t],e);else{if(!this.Event.listeners[t])return;this.Event.listeners[t].length=0}},this.$emit=(t,e,n)=>{this.Event.listeners[t]&&this.Event.listeners[t].forEach((t=>t(e,n)))}}_lazyLoadHandler(){const t=[];this.ListenerQueue.forEach(((e,n)=>{e.el&&e.el.parentNode&&!e.state.loaded||t.push(e);e.checkInView()&&(e.state.loaded||e.load())})),t.forEach((t=>{Ky(this.ListenerQueue,t),t.$destroy&&t.$destroy()}))}_initIntersectionObserver(){zy&&(this._observer=new IntersectionObserver(this._observerHandler.bind(this),this.options.observerOptions),this.ListenerQueue.length&&this.ListenerQueue.forEach((t=>{this._observer.observe(t.el)})))}_observerHandler(t){t.forEach((t=>{t.isIntersecting&&this.ListenerQueue.forEach((e=>{if(e.el===t.target){if(e.state.loaded)return this._observer.unobserve(e.el);e.load()}}))}))}_elRenderer(t,e,n){if(!t.el)return;const{el:r,bindType:i}=t;let o;switch(e){case"loading":o=t.loading;break;case"error":o=t.error;break;default:o=t.src}if(i?r.style[i]='url("'+o+'")':r.getAttribute("src")!==o&&r.setAttribute("src",o),r.setAttribute("lazy",e),this.$emit(e,t,n),this.options.adapter[e]&&this.options.adapter[e](t,this.options),this.options.dispatchEvent){const n=new CustomEvent(e,{detail:t});r.dispatchEvent(n)}}_valueFormatter(t){return null!==(e=t)&&"object"==typeof e?(t.src||this.options.silent||console.error("Vue Lazyload warning: miss src with "+t),{src:t.src,loading:t.loading||this.options.loading,error:t.error||this.options.error,cors:this.options.cors}):{src:t,loading:this.options.loading,error:this.options.error,cors:this.options.cors};var e}}const cv=(t,e)=>{let n=fe({});return{rect:n,checkInView:()=>(n=t.value.getBoundingClientRect(),Hy&&n.top<window.innerHeight*e&&n.bottom>0&&n.left<window.innerWidth*e&&n.right>0)}};class hv{constructor(t){this.lazy=t,t.lazyContainerMananger=this,this._queue=[]}bind(t,e,n){const r=new dv(t,e,n,this.lazy);this._queue.push(r)}update(t,e,n){const r=this._queue.find((e=>e.el===t));r&&r.update(t,e)}unbind(t,e,n){const r=this._queue.find((e=>e.el===t));r&&(r.clear(),Ky(this._queue,r))}}const uv={selector:"img",error:"",loading:""};class dv{constructor(t,e,n,r){this.el=t,this.vnode=n,this.binding=e,this.options={},this.lazy=r,this._queue=[],this.update(t,e)}update(t,e){this.el=t,this.options=Vy({},uv,e.value);this.getImgs().forEach((t=>{this.lazy.add(t,Vy({},this.binding,{value:{src:t.getAttribute("data-src")||t.dataset.src,error:t.getAttribute("data-error")||t.dataset.error||this.options.error,loading:t.getAttribute("data-loading")||t.dataset.loading||this.options.loading}}),this.vnode)}))}getImgs(){return Array.from(this.el.querySelectorAll(this.options.selector))}clear(){this.getImgs().forEach((t=>this.lazy.remove(t))),this.vnode=null,this.binding=null,this.lazy=null}}var fv=t=>gr({setup(e,{slots:n}){const r=Te(),i=fe({src:"",error:"",loading:"",attempt:t.options.attempt}),o=fe({loaded:!1,error:!1,attempt:0}),{rect:a,checkInView:s}=cv(r,t.options.preLoad),l=Te(""),c=(e=nv)=>{if(o.attempt>i.attempt-1&&o.error)return t.options.silent||console.log(`VueLazyload log: ${i.src} tried too more than ${i.attempt} times`),e();const n=i.src;Zy({src:n},(({src:t})=>{l.value=t,o.loaded=!0}),(()=>{o.attempt++,l.value=i.error,o.error=!0}))},h=Xo((()=>({el:r.value,rect:a,checkInView:s,load:c,state:o})));Rr((()=>{t.addLazyBox(h.value),t.lazyLoadHandler()})),Lr((()=>{t.removeComponent(h.value)}));return Xn((()=>e.src),(()=>{(()=>{const{src:n,loading:r,error:a}=t._valueFormatter(e.src);o.loaded=!1,i.src=n,i.error=a,i.loading=r,l.value=i.loading})(),t.addLazyBox(h.value),t.lazyLoadHandler()}),{immediate:!0}),()=>{var t;return _o(e.tag||"img",{src:l.value,ref:r},[null===(t=n.default)||void 0===t?void 0:t.call(n)])}}}),pv={install(t,e={}){const n=new lv(e),r=new hv(n);if(Number(t.version.split(".")[0])<3)return new Error("Vue version at least 3.0");t.config.globalProperties.$Lazyload=n,t.provide("Lazyload",n),e.lazyComponent&&t.component("lazy-component",(t=>gr({props:{tag:{type:String,default:"div"}},emits:["show"],setup(e,{emit:n,slots:r}){const i=Te(),o=fe({loaded:!1,error:!1,attempt:0}),a=Te(!1),{rect:s,checkInView:l}=cv(i,t.options.preLoad),c=()=>{a.value=!0,o.loaded=!0,n("show",a.value)},h=Xo((()=>({el:i.value,rect:s,checkInView:l,load:c,state:o})));return Rr((()=>{t.addLazyBox(h.value),t.lazyLoadHandler()})),Lr((()=>{t.removeComponent(h.value)})),()=>{var t;return _o(e.tag,{ref:i},[a.value&&(null===(t=r.default)||void 0===t?void 0:t.call(r))])}}}))(n)),e.lazyImage&&t.component("lazy-image",fv(n)),t.directive("lazy",{beforeMount:n.add.bind(n),beforeUpdate:n.update.bind(n),updated:n.lazyLoadHandler.bind(n),unmounted:n.remove.bind(n)}),t.directive("lazy-container",{beforeMount:r.bind.bind(r),updated:r.update.bind(r),unmounted:r.unbind.bind(r)})}};class gv{static instance;vscodeApi=null;constructor(){this.init()}static getInstance(){return gv.instance||(gv.instance=new gv),gv.instance}init(){if(this.isVSCodeEnvironment())try{this.vscodeApi=window.__VSCODE_API__||window.acquireVsCodeApi?.()||null,console.log("VSCode environment detected, API initialized")}catch(t){console.warn("Failed to initialize VSCode API:",t)}}isVSCodeEnvironment(){return Boolean(window.__VSCODE_ENV__||window.acquireVsCodeApi)}postMessage(t){this.vscodeApi?this.vscodeApi.postMessage(t):console.warn("VSCode API not available, message not sent:",t)}showInfo(t){this.isVSCodeEnvironment()?this.postMessage({command:"alert",text:t}):console.info(t)}showError(t){this.isVSCodeEnvironment()?this.postMessage({command:"error",text:t}):console.error(t)}setState(t){this.vscodeApi&&this.vscodeApi.setState(t)}getState(){return this.vscodeApi?this.vscodeApi.getState():null}getEnvironmentInfo(){return{isVSCode:this.isVSCodeEnvironment(),hasVSCodeApi:Boolean(this.vscodeApi),userAgent:navigator.userAgent,platform:navigator.platform}}}const mv=gv.getInstance(),yv={beforeMount:(t,e)=>{t.clickOutsideEvent=n=>{t==n.target||t.contains(n.target)||e.value()},mv.isVSCodeEnvironment()?document.addEventListener("click",t.clickOutsideEvent):document.body.addEventListener("click",t.clickOutsideEvent)},unmounted:t=>{mv.isVSCodeEnvironment()?document.removeEventListener("click",t.clickOutsideEvent):document.body.removeEventListener("click",t.clickOutsideEvent)}},vv=Rs(bf);vv.use(Iy),vv.use(function(){const t=it(!0),e=t.run((()=>Te({})));let n=[],r=[];const i=Se({install(t){Ed(i),i._a=t,t.provide(Pd,i),t.config.globalProperties.$pinia=i,r.forEach((t=>n.push(t))),r=[]},use(t){return this._a?n.push(t):r.push(t),this},_p:n,_a:null,_e:t,_s:new Map,state:e});return i}()),vv.use(ng),vv.directive("click-outside",yv),vv.use(pv,{preLoad:1.3,attempt:1,filter:{webp(t,e){if(/img2.soyoung.com|static.soyoung.com/.test(t.src)&&!t.src.includes("?")){if(!e.supportWebp)return;t.src=`${t.src}?imageView2/0/format/webp`}}}}),mv.isVSCodeEnvironment()&&(console.log("Initializing in VSCode environment"),window.addEventListener("unhandledrejection",(t=>{mv.showError(`未处理的 Promise 错误: ${t.reason}`)})),vv.config.globalProperties.$vscode=mv,vv.provide("vscodeAdapter",mv)),vv.mount("#app"),mv.isVSCodeEnvironment()&&(mv.showInfo("新氧画廊已成功加载"),mv.setState({loaded:!0,timestamp:Date.now()}));export{Os as $,fo as A,xn as B,xo as C,Ji as D,Tn as E,to as F,wc as G,Mn as H,er as I,u as J,m as K,mi as L,fe as M,Ue as N,Vr as O,g as P,yi as Q,Nr as R,Mr as S,ua as T,Xr as U,Fn as V,ts as W,Lo as X,xs as Y,b as Z,h as _,Gr as a,Rs as a$,v as a0,Oe as a1,Tr as a2,Lr as a3,Qo as a4,$n as a5,ka as a6,no as a7,Yd as a8,Sd as a9,of as aA,af as aB,lf as aC,po as aD,Re as aE,R as aF,Oo as aG,_d as aH,Sc as aI,xc as aJ,xd as aK,Se as aL,us as aM,Xd as aN,Kd as aO,gf as aP,zd as aQ,Wd as aR,Cd as aS,wd as aT,cs as aU,zr as aV,Br as aW,Ee as aX,r as aY,Qr as aZ,Ve as a_,yf as aa,mf as ab,$d as ac,pf as ad,ff as ae,Jp as af,vf as ag,uf as ah,df as ai,we as aj,S as ak,ls as al,Ud as am,sf as an,Jd as ao,Zd as ap,nf as aq,rf as ar,tf as as,hf as at,cf as au,Qd as av,qd as aw,Hd as ax,Co as ay,ef as az,bo as b,T as b0,ge as b1,K as b2,wo as b3,So as b4,eo as b5,xr as b6,k as b7,ta as b8,pe as b9,uo as c,gr as d,J as e,B as f,Rr as g,Ao as h,_o as i,Sn as j,rn as k,Gd as l,Vd as m,$ as n,ao as o,wn as p,ws as q,Te as r,at as s,Bd as t,Ne as u,st as v,Xn as w,it as x,Qp as y,Xo as z};
//# sourceMappingURL=index.05904f40.js.map
