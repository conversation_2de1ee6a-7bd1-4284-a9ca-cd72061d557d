{"version": 3, "file": "chunk.2711b925.js", "sources": ["../src/views/designCooperate/authCheck.vue"], "sourcesContent": ["<template>\n  <div v-if=\"userInfo.ssoId && pageShow\" :class=\"['home']\">\n    <!-- <Header /> -->\n    <div class=\"home-header\">\n      <img src=\"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png\" alt=\"\" />\n    </div>\n    <div class=\"authCheck\">\n      <div class=\"authCheck-content\">\n        <div class=\"authCheck-title\">您正在登录 SoYoung 画廊 Sketch 插件</div>\n        <div class=\"authCheck-card\">\n          <div class=\"authCheck-card-img\">\n            <img :src=\"userInfo.url\" alt=\"\" />\n          </div>\n          <div class=\"authCheck-card-name\">\n            {{ userInfo.name }}\n          </div>\n          <el-button @click=\"handleChecked\" v-loading=\"loading\" :disabled=\"countDown !== null\" color=\"#626aef\" size=\"large\">登录</el-button>\n          <el-button @click=\"handleLogout\" size=\"large\">切换账号</el-button>\n          <div v-if=\"countDown !== null\" class=\"authCheck-card-countdown\">{{ countDown }}s后自动跳转</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { setUserLogout, userChecked } from \"@/api/login\";\nimport { onMounted, ref } from \"vue\";\nimport { userInfoStore } from \"@/store\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nconst userInfo = userInfoStore();\n\nconst route = useRoute();\nconst router = useRouter();\nconst pageShow = ref<boolean>(false);\nconst loading = ref<boolean>(false);\nconst countDown = ref<number | null>(null);\nonMounted(() => {\n  const { token, from } = route.query;\n  if (from === \"gallery-sketch\" && token) {\n    pageShow.value = true;\n  }\n});\nconst handleLogout = async () => {\n  await setUserLogout();\n  userInfo.clearInfo();\n  ElMessage({\n    type: \"success\",\n    message: \"退出成功\"\n  });\n};\n\nconst handleChecked = async () => {\n  const { token } = route.query;\n  if (!token) {\n    ElMessage({\n      type: \"error\",\n      message: \"授权失败\"\n    });\n    return;\n  }\n  loading.value = true;\n  const res = await userChecked({\n    token\n  });\n  loading.value = false;\n  if (res.code !== 0) {\n    ElMessage({\n      type: \"error\",\n      message: res.msg || \"授权失败\"\n    });\n    return;\n  }\n  countDown.value = 5;\n  getCountDown();\n};\nlet timer;\nconst getCountDown = () => {\n  timer && clearTimeout(timer);\n  timer = setTimeout(() => {\n    (countDown.value as number)--;\n    if (countDown.value === 0) {\n      clearTimeout(timer);\n      countDown.value = null;\n      // window.open(\"sketch://\");\n      router.push({\n        path: \"/\"\n      });\n      return;\n    }\n    getCountDown();\n  }, 1000);\n};\n</script>\n<style lang=\"less\" scoped>\n.home {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  &::-webkit-scrollbar {\n    display: none;\n  }\n  &.home-theme {\n    background: #26282b;\n  }\n  &-header {\n    height: 48px;\n    padding-left: 20px;\n    img {\n      height: 38px;\n      margin-top: 5px;\n    }\n  }\n  .authCheck {\n    height: calc(100vh - 48px);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    margin-top: -30px;\n    &-title {\n      font-size: 18px;\n      font-weight: 500;\n      font-family: PingFangSC-Medium;\n      color: #131336;\n      letter-spacing: 0.74px;\n    }\n    &-card {\n      margin-top: 20px;\n      border-radius: 4px;\n      background-color: #fff;\n      overflow: hidden;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      padding: 30px 0 60px;\n      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);\n      &-img {\n        width: 80px;\n        height: 80px;\n        overflow: hidden;\n        border-radius: 50%;\n        img {\n          width: 100%;\n          height: 100%;\n          object-fit: contain;\n        }\n      }\n      &-name {\n        margin-top: 20px;\n        font-size: 14px;\n        font-weight: 500;\n        font-family: PingFangSC-Medium;\n        color: #131336;\n      }\n      .el-button {\n        width: 240px;\n        margin: 10px 0;\n      }\n    }\n  }\n}\n</style>\n"], "names": ["userInfo", "userInfoStore", "route", "useRoute", "router", "useRouter", "pageShow", "ref", "loading", "countDown", "onMounted", "token", "from", "query", "value", "handleLogout", "async", "setUserLogout", "clearInfo", "ElMessage", "type", "message", "handleChecked", "res", "userChecked", "code", "msg", "timer", "getCountDown", "clearTimeout", "setTimeout", "push", "path"], "mappings": "+zBA8BA,MAAAA,EAAAC,IAEAC,EAAAC,IACAC,EAAAC,IACAC,EAAAC,GAAA,GACAC,EAAAD,GAAA,GACAE,EAAAF,EAAA,MACAG,GAAA,KACE,MAAAC,MAAAA,EAAAC,KAAAA,GAAAV,EAAAW,MACA,mBAAAD,GAAAD,IACEL,EAAAQ,OAAA,EAAiB,IAGrB,MAAAC,EAAAC,gBACEC,IACAjB,EAAAkB,YACAC,EAAA,CAAUC,KAAA,UACFC,QAAA,QACG,EAIbC,EAAAN,UACE,MAAAL,MAAAA,GAAAT,EAAAW,MACA,IAAAF,EAKE,YAJAQ,EAAA,CAAUC,KAAA,QACFC,QAAA,SAKVb,EAAAM,OAAA,EACA,MAAAS,QAAAC,EAAA,CAA8Bb,UAG9BH,EAAAM,OAAA,EACA,IAAAS,EAAAE,MAOAhB,EAAAK,MAAA,OANEK,EAAA,CAAUC,KAAA,QACFC,QAAAE,EAAAG,KAAA,UAQZ,IAAAC,EACA,MAAAC,EAAA,KACED,GAAAE,aAAAF,GACAA,EAAAG,YAAA,KAEE,GADArB,EAAAK,QACA,IAAAL,EAAAK,MAOE,OANAe,aAAAF,GACAlB,EAAAK,MAAA,UAEAV,EAAA2B,KAAA,CAAYC,KAAA,YAKD,IAAA"}