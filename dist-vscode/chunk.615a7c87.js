import{f as e,h as a,o as l,m as t,Y as r,O as s,k as o,B as i,v as n,q as u,x as v}from"./chunk.25a51fc3.js";import{P as c,d as f,Q as m,r as d,z as p,R as h,a_ as y,o as g,A as w,B as b,I as S,b as z,n as x,u as _,f as E,a6 as L,T as k,c as T,i as B,F as H,w as R,k as C,L as N,M as j,g as A,S as M,O,V as Y,h as W,a0 as X}from"./index.7c7944d0.js";import{t as $}from"./chunk.c5fb43ac.js";function P(){if(!arguments.length)return[];var a=arguments[0];return e(a)?a:[a]}const q=(...e)=>a=>{e.forEach((e=>{c(e)?e(a):e.value=a}))},I={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},K=Symbol("scrollbarContextKey"),F=a({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean});var Q=l(f({__name:"thumb",props:F,setup(e){const a=e,l=m(K),o=t("scrollbar");l||$("Thumb","can not inject scrollbar context");const i=d(),n=d(),u=d({}),v=d(!1);let c=!1,f=!1,T=s?document.onselectstart:null;const B=p((()=>I[a.vertical?"vertical":"horizontal"])),H=p((()=>(({move:e,size:a,bar:l})=>({[l.size]:a,transform:`translate${l.axis}(${e}%)`}))({size:a.size,move:a.move,bar:B.value}))),R=p((()=>i.value[B.value.offset]**2/l.wrapElement[B.value.scrollSize]/a.ratio/n.value[B.value.offset])),C=e=>{var a;if(e.stopPropagation(),e.ctrlKey||[1,2].includes(e.button))return;null==(a=window.getSelection())||a.removeAllRanges(),j(e);const l=e.currentTarget;l&&(u.value[B.value.axis]=l[B.value.offset]-(e[B.value.client]-l.getBoundingClientRect()[B.value.direction]))},N=e=>{if(!n.value||!i.value||!l.wrapElement)return;const a=100*(Math.abs(e.target.getBoundingClientRect()[B.value.direction]-e[B.value.client])-n.value[B.value.offset]/2)*R.value/i.value[B.value.offset];l.wrapElement[B.value.scroll]=a*l.wrapElement[B.value.scrollSize]/100},j=e=>{e.stopImmediatePropagation(),c=!0,document.addEventListener("mousemove",A),document.addEventListener("mouseup",M),T=document.onselectstart,document.onselectstart=()=>!1},A=e=>{if(!i.value||!n.value)return;if(!1===c)return;const a=u.value[B.value.axis];if(!a)return;const t=100*(-1*(i.value.getBoundingClientRect()[B.value.direction]-e[B.value.client])-(n.value[B.value.offset]-a))*R.value/i.value[B.value.offset];l.wrapElement[B.value.scroll]=t*l.wrapElement[B.value.scrollSize]/100},M=()=>{c=!1,u.value[B.value.axis]=0,document.removeEventListener("mousemove",A),document.removeEventListener("mouseup",M),O(),f&&(v.value=!1)};h((()=>{O(),document.removeEventListener("mouseup",M)}));const O=()=>{document.onselectstart!==T&&(document.onselectstart=T)};return r(y(l,"scrollbarElement"),"mousemove",(()=>{f=!1,v.value=!!a.size})),r(y(l,"scrollbarElement"),"mouseleave",(()=>{f=!0,v.value=c})),(e,a)=>(g(),w(k,{name:_(o).b("fade"),persisted:""},{default:b((()=>[S(z("div",{ref_key:"instance",ref:i,class:x([_(o).e("bar"),_(o).is(_(B).key)]),onMousedown:N},[z("div",{ref_key:"thumb",ref:n,class:x(_(o).e("thumb")),style:E(_(H)),onMousedown:C},null,38)],34),[[L,e.always||v.value]])])),_:1},8,["name"]))}}),[["__file","thumb.vue"]]);var V=l(f({__name:"bar",props:a({always:{type:Boolean,default:!0},width:String,height:String,ratioX:{type:Number,default:1},ratioY:{type:Number,default:1}}),setup(e,{expose:a}){const l=e,t=d(0),r=d(0);return a({handleScroll:e=>{if(e){const a=e.offsetHeight-4,s=e.offsetWidth-4;r.value=100*e.scrollTop/a*l.ratioY,t.value=100*e.scrollLeft/s*l.ratioX}}}),(e,a)=>(g(),T(H,null,[B(Q,{move:t.value,ratio:e.ratioX,size:e.width,always:e.always},null,8,["move","ratio","size","always"]),B(Q,{move:r.value,ratio:e.ratioY,size:e.height,vertical:"",always:e.always},null,8,["move","ratio","size","always"])],64))}}),[["__file","bar.vue"]]);const D=a({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:o([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},id:String,role:String,ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical"]}}),G={scroll:({scrollTop:e,scrollLeft:a})=>[e,a].every(i)},J=f({name:"ElScrollbar"});const U=v(l(f({...J,props:D,emits:G,setup(e,{expose:a,emit:l}){const s=e,o=t("scrollbar");let v,c;const f=d(),m=d(),h=d(),y=d("0"),S=d("0"),L=d(),k=d(1),B=d(1),H=p((()=>{const e={};return s.height&&(e.height=n(s.height)),s.maxHeight&&(e.maxHeight=n(s.maxHeight)),[s.wrapStyle,e]})),$=p((()=>[s.wrapClass,o.e("wrap"),{[o.em("wrap","hidden-default")]:!s.native}])),P=p((()=>[o.e("view"),s.viewClass])),q=()=>{var e;m.value&&(null==(e=L.value)||e.handleScroll(m.value),l("scroll",{scrollTop:m.value.scrollTop,scrollLeft:m.value.scrollLeft}))};const I=()=>{if(!m.value)return;const e=m.value.offsetHeight-4,a=m.value.offsetWidth-4,l=e**2/m.value.scrollHeight,t=a**2/m.value.scrollWidth,r=Math.max(l,s.minSize),o=Math.max(t,s.minSize);k.value=l/(e-l)/(r/(e-r)),B.value=t/(a-t)/(o/(a-o)),S.value=r+4<e?`${r}px`:"",y.value=o+4<a?`${o}px`:""};return R((()=>s.noresize),(e=>{e?(null==v||v(),null==c||c()):(({stop:v}=u(h,I)),c=r("resize",I))}),{immediate:!0}),R((()=>[s.maxHeight,s.height]),(()=>{s.native||C((()=>{var e;I(),m.value&&(null==(e=L.value)||e.handleScroll(m.value))}))})),N(K,j({scrollbarElement:f,wrapElement:m})),A((()=>{s.native||C((()=>{I()}))})),M((()=>I())),a({wrapRef:m,update:I,scrollTo:function(e,a){X(e)?m.value.scrollTo(e):i(e)&&i(a)&&m.value.scrollTo(e,a)},setScrollTop:e=>{i(e)&&(m.value.scrollTop=e)},setScrollLeft:e=>{i(e)&&(m.value.scrollLeft=e)},handleScroll:q}),(e,a)=>(g(),T("div",{ref_key:"scrollbarRef",ref:f,class:x(_(o).b())},[z("div",{ref_key:"wrapRef",ref:m,class:x(_($)),style:E(_(H)),onScroll:q},[(g(),w(Y(e.tag),{id:e.id,ref_key:"resizeRef",ref:h,class:x(_(P)),style:E(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:b((()=>[O(e.$slots,"default")])),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],38),e.native?W("v-if",!0):(g(),w(V,{key:0,ref_key:"barRef",ref:L,height:S.value,width:y.value,always:e.always,"ratio-x":B.value,"ratio-y":k.value},null,8,["height","width","always","ratio-x","ratio-y"]))],2))}}),[["__file","scrollbar.vue"]]));export{U as E,q as a,P as c};
//# sourceMappingURL=chunk.615a7c87.js.map
