{"version": 3, "file": "chunk.a6a39d8d.js", "sources": ["../src/views/layouts/components/tabs.vue", "../src/views/layouts/components/spHeader.vue", "../src/views/layouts/components/spLogin.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"{\n      'home-body__content': true,\n      'home-body-theme__active': store.themeShow\n    }\"\n  >\n    <div\n      :class=\"{\n        'home-body-tabs__item': true,\n        'home-body-tabs-item__span': item.id === currentId\n      }\"\n      v-for=\"item in tabsList\"\n      :key=\"item.id\"\n      @click=\"tabsChange(item)\"\n    >\n      <span class=\"active-name\">{{ item.name }}</span>\n      <img v-if=\"item.iconImg\" src=\"https://static.soyoung.com/sy-pre/<EMAIL>\" />\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { themeStore, userInfoStore } from \"@/store\";\nimport { ref, watch } from \"vue\";\nimport { useRoute, useRouter } from \"vue-router\";\nconst store = themeStore();\nconst route = useRoute();\nconst router = useRouter();\nconst userInfo = userInfoStore();\n// tabs 数据集\nconst tabsList = ref<any[]>([\n  { id: 1, name: \"灵感集\", hasIcon: false, iconImg: \"\" },\n  { id: 2, name: \"设计协作\", hasIcon: true, iconImg: \"https://static.soyoung.com/sy-pre/<EMAIL>\" }\n]);\nconst barLeft = ref<number>(0); // 初始偏移量\nconst currentId = ref<number>(route.fullPath.indexOf(\"/item/project/index\") > -1 ? 2 : 1); // 默认渲染样式\n// 登陆\n// const ssoLogin = async () => {\n//   window.location.href = \"/api/user/login?return_url=\" + encodeURIComponent(window.location.href);\n// };\nconst tabsChange = (item: any) => {\n  if (item.id === 2) {\n    // if (!userInfo.syUid) {\n    //   ssoLogin();\n    //   return;\n    // }\n    const route = router.resolve({ path: \"/item/project/index\" });\n    window.open(route.href, \"_blank\");\n  } else {\n    barLeft.value = item.left;\n    currentId.value = item.id;\n  }\n};\n\nwatch(\n  () => route.fullPath,\n  (newVal, oldVal) => {\n    console.log(\"Route changed from\", oldVal, \"to\", newVal);\n  }\n);\n</script>\n<style lang=\"less\" scoped>\n.home-body__content {\n  box-sizing: border-box;\n  position: relative;\n  padding-left: 10px;\n  .home-body-tabs__item {\n    height: 38px;\n    line-height: 38px;\n    text-align: center;\n    font-size: 16px;\n    color: #595959;\n    font-weight: 500;\n    cursor: pointer;\n    z-index: 1;\n    display: inline-block;\n    position: relative;\n    padding: 0 20px;\n    &.home-body-tabs-item__span {\n      color: #262626;\n      &::after {\n        content: \"\";\n        position: absolute;\n        bottom: -6px;\n        left: 50%;\n        width: 24px;\n        height: 3px;\n        transform: translateX(-50%);\n        background: #5c54f0;\n        border-radius: 2px;\n      }\n    }\n    img {\n      width: 24px;\n      height: 12px;\n      position: absolute;\n    }\n  }\n  &.home-body-theme__active {\n    .home-body-tabs__item {\n      color: #ffffff;\n      .active-name {\n        opacity: 0.5;\n      }\n      &.home-body-tabs-item__span {\n        color: #ffffff;\n        .active-name {\n          opacity: 1;\n        }\n      }\n    }\n  }\n}\n</style>\n", "<template>\r\n  <header\r\n    :class=\"{\r\n      home__header: true,\r\n      'home-header__active': store.themeShow\r\n    }\"\r\n    :style=\"{ background: store.themeShow ? '#26282B' : '#FFFFFF' }\"\r\n  >\r\n    <div class=\"header__left\">\r\n      <slot name=\"left-slot\">\r\n        <div class=\"home__logo\" @click=\"handleClickLogo\">\r\n          <img loading=\"lazy\" class=\"home__logo-img\" :src=\"store.themeShow ? 'https://static.soyoung.com/sy-pre/pft7c5v6tl9w-1709797007858.png' : 'https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png'\" alt=\"新氧画廊LOGO\" />\r\n        </div>\r\n      </slot>\r\n    </div>\r\n    <div class=\"header__center\">\r\n      <slot name=\"center-slot\">\r\n        <SpTabs></SpTabs>\r\n      </slot>\r\n    </div>\r\n    <div class=\"header__right\">\r\n      <slot></slot>\r\n    </div>\r\n  </header>\r\n  <div class=\"home__line\" :style=\"{ background: store.themeShow ? '#000000' : '#F5F5F5' }\"></div>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { useRouter } from \"vue-router\";\r\nimport { themeStore } from \"@/store\";\r\nimport SpTabs from \"@/views/layouts/components/tabs.vue\";\r\nconst store = themeStore();\r\n\r\nconst router = useRouter();\r\nconst handleClickLogo = () => {\r\n  router.push({\r\n    path: \"/\"\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.home__header {\r\n  height: 64px;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-flow: row nowrap;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  user-select: none;\r\n  .header__left {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    .home__logo {\r\n      display: inline-block;\r\n      padding-left: 25px;\r\n      margin-top: 4px;\r\n      cursor: pointer;\r\n      width: 250px;\r\n      box-sizing: border-box;\r\n      .home__logo-img {\r\n        width: 84px;\r\n        display: inline-block;\r\n      }\r\n    }\r\n    .header__title {\r\n      display: inline-block;\r\n      .header__title__title {\r\n        font-family: PingFangSC-Regular;\r\n        font-size: 16px;\r\n        color: #606266;\r\n        position: relative;\r\n        line-height: 22px;\r\n        margin-left: 30px;\r\n        &:before {\r\n          content: \"\";\r\n          width: 1px;\r\n          height: 16px;\r\n          position: absolute;\r\n          left: -15px;\r\n          top: 4px;\r\n          background: #d8d8d8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .header__center {\r\n    flex: 1;\r\n  }\r\n\r\n  &.home-header__active {\r\n    .header__left {\r\n      .home__logo {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n    .header__right {\r\n      .home__user--login .home__username {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n}\r\n.home__line {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #f5f5f5;\r\n}\r\n</style>\r\n", "<template>\r\n  <div\r\n    :class=\"{\r\n      'home__user--login': true,\r\n      'home-user__active': store.themeShow\r\n    }\"\r\n  >\r\n    <div class=\"home-user__body\">\r\n      <div class=\"home-user-body__item\" @click=\"themeChange\">\r\n        <img loading=\"lazy\" :src=\"store.themeShow ? 'https://static.soyoung.com/sy-pre/q66gwg2hjhf4-1697533800712.png' : 'https://static.soyoung.com/sy-pre/2zls11axnhtgk-1697530200725.png'\" alt=\"\" />\r\n        <span>{{ store.themeShow ? \"浅色模式\" : \"深色模式\" }}</span>\r\n      </div>\r\n      <div class=\"home-user-body__item\" @click=\"handleUpload\">\r\n        <i class=\"iconfont icon-wodegongzuotai\" />\r\n        <span>工作台</span>\r\n      </div>\r\n    </div>\r\n    <div class=\"home__userInfo\" v-if=\"userInfo.ssoId\">\r\n      <el-dropdown :popper-class=\"'home-user__dropdown'\" :hide-on-click=\"false\">\r\n        <div>\r\n          <img loading=\"lazy\" class=\"home__avatar\" src=\"https://static.soyoung.com/sy-pre/<EMAIL>\" alt=\"\" />\r\n          <span class=\"home__username\">{{ userInfo.name }}</span>\r\n        </div>\r\n        <template #dropdown>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item @click=\"handleCollection\">\r\n              <i class=\"iconfont icon-shoucang1\"></i>\r\n              <span>我的收藏</span>\r\n            </el-dropdown-item>\r\n            <el-dropdown-item @click=\"handleLogout\">\r\n              <i class=\"iconfont icon-tuichudenglu\"></i>\r\n              <span>退出登录</span>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </template>\r\n      </el-dropdown>\r\n    </div>\r\n    <div class=\"home__user--unlogin\" v-else>\r\n      <!-- <el-button @click=\"ssoLogin\" type=\"primary\" size=\"large\">新氧登录</el-button> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { setUserLogout } from \"@/api/login\";\r\nimport { themeStore, userInfoStore } from \"@/store\";\r\nimport { ElButton, ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage } from \"element-plus\";\r\nimport { computed, onMounted } from \"vue\";\r\nimport { useRouter } from \"vue-router\";\r\nexport default {\r\n  setup() {\r\n    const router = useRouter();\r\n    const userInfo = userInfoStore();\r\n    // 深浅模式切换store\r\n    const store = themeStore();\r\n\r\n    console.log(\"store\", store.themeShow);\r\n    // 退出登录\r\n    const handleLogout = async () => {\r\n      await setUserLogout();\r\n      userInfo.clearInfo();\r\n      ElMessage({\r\n        type: \"success\",\r\n        message: \"退出成功\"\r\n      });\r\n    };\r\n    // 切换主题\r\n    const themeChange = () => {\r\n      // 使用本地存储来持久化主题设置\r\n      localStorage.setItem(\"theme\", store.themeShow ? \"light\" : \"dark\");\r\n      store.updateThemeValue(!store.themeShow);\r\n    };\r\n\r\n    const handleUpload = () => {\r\n      // if (!userInfo.syUid) {\r\n      //   ssoLogin();\r\n      //   return;\r\n      // }\r\n      router.push({\r\n        path: \"/home/<USER>\"\r\n      });\r\n    };\r\n\r\n    const handleCollection = () => {\r\n      router.push({\r\n        path: \"/home/<USER>\"\r\n      });\r\n    };\r\n    // 登陆\r\n    // const ssoLogin = async () => {\r\n    //   window.location.href = \"/api/user/login?return_url=\" + encodeURIComponent(window.location.href);\r\n    // };\r\n\r\n    onMounted(() => {\r\n      const element = document.getElementsByTagName(\"html\")[0];\r\n      if (localStorage.getItem(\"theme\") && localStorage.getItem(\"theme\") == \"light\") {\r\n        store.updateThemeValue(false);\r\n      } else {\r\n        store.updateThemeValue(true);\r\n      }\r\n      element.setAttribute(\"class\", store.themeShow ? \"dark\" : \"light\");\r\n    });\r\n\r\n    return {\r\n      handleLogout,\r\n      handleUpload,\r\n      store,\r\n      // ssoLogin,\r\n      handleCollection,\r\n      userInfo: computed(() => userInfo),\r\n      updateLoginStatus: (payload) => userInfo.updateInfo(payload),\r\n      themeChange\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听路由是否变化\r\n    $route(to, from) {\r\n      if (to.query.id != from.query.id) {\r\n        this.$router.go(0);\r\n      }\r\n    }\r\n  },\r\n  components: {\r\n    ElButton,\r\n    ElDropdown,\r\n    ElDropdownMenu,\r\n    ElDropdownItem\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\">\r\n.home__user--login {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  .home-user__body {\r\n    display: flex;\r\n    flex-direction: row;\r\n    .home-user-body__item {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      margin-right: 24px;\r\n      cursor: pointer;\r\n      img {\r\n        width: 18px;\r\n        height: 18px;\r\n      }\r\n      span {\r\n        font-size: 14px;\r\n        color: #303233;\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n  .home__userInfo {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    margin-right: 50px;\r\n    padding: 22px 0;\r\n  }\r\n  .home__avatar {\r\n    width: 20px;\r\n    height: 20px;\r\n    box-sizing: border-box;\r\n    border: 1px solid #005dfc;\r\n    border-radius: 10px 10px 1px 10px;\r\n    margin-right: 10px;\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n  .home__username {\r\n    font-family: PingFangSC-Regular;\r\n    font-size: 14px;\r\n    color: #303233;\r\n    letter-spacing: 0;\r\n    font-weight: 400;\r\n    line-height: 20px;\r\n    vertical-align: middle;\r\n    display: inline-block;\r\n    max-width: 180px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n  &.home-user__active {\r\n    .home__username {\r\n      color: #ffffff;\r\n    }\r\n  }\r\n}\r\n.home__user--unlogin {\r\n  margin-right: 50px;\r\n  padding: 11px 0;\r\n}\r\n.home-user__dropdown {\r\n  .el-dropdown-menu {\r\n    width: 144px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .el-dropdown-menu__item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 10px 16px;\r\n      i {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/*主题激活样式*/\r\n.home-user__active {\r\n  .home-user__body {\r\n    .home-user-body__item {\r\n      span {\r\n        color: #ffffff;\r\n      }\r\n      i {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["store", "themeStore", "route", "useRoute", "router", "useRouter", "userInfoStore", "tabsList", "ref", "id", "name", "hasIcon", "iconImg", "barLeft", "currentId", "fullPath", "indexOf", "watch", "newVal", "oldVal", "console", "log", "item", "route2", "resolve", "path", "window", "open", "href", "value", "left", "handleClickLogo", "push", "_sfc_main", "setup", "userInfo", "themeShow", "onMounted", "element", "document", "getElementsByTagName", "localStorage", "getItem", "updateThemeValue", "setAttribute", "handleLogout", "async", "setUserLogout", "clearInfo", "ElMessage", "type", "message", "handleUpload", "handleCollection", "computed", "updateLoginStatus", "payload", "updateInfo", "themeChange", "setItem", "$route", "to", "from", "query", "this", "$router", "go", "components", "ElButton", "ElDropdown", "ElDropdownMenu", "ElDropdownItem", "_hoisted_1", "class", "key", "loading", "src", "_hoisted_8", "_hoisted_10", "_createElementVNode", "_hoisted_12", "__unplugin_components_2", "_normalizeClass", "$setup", "alt", "_hoisted_2", "onClick", "_cache", "args", "_hoisted_5", "ssoId", "_openBlock", "_createElementBlock", "_hoisted_6", "_createVNode", "_component_el_dropdown", "_component_el_dropdown_menu", "default", "_withCtx", "_component_el_dropdown_item", "_hoisted_9", "_", "_hoisted_11", "_hoisted_7"], "mappings": "wfAyBA,MAAAA,EAAAC,IACAC,EAAAC,IACAC,EAAAC,IACAC,IAEA,MAAAC,EAAAC,EAAA,CAA4B,CAAAC,GAAA,EAAAC,KAAA,MAAAC,SAAA,EAAAC,QAAA,IACwB,CAAAH,GAAA,EAAAC,KAAA,OAAAC,SAAA,EAAAC,QAAA,gEAGpDC,EAAAL,EAAA,GACAM,EAAAN,EAAAN,EAAAa,SAAAC,QAAA,wBAAA,EAAA,EAAA,UAmBAC,GAAA,IAAAf,EAAAa,WACc,CAAAG,EAAAC,KAEVC,QAAAC,IAAA,qBAAAF,EAAA,KAAAD,EAAA,uPAjBJ,CAAAI,IACE,GAAA,IAAAA,EAAAb,GAAA,CAKE,MAAAc,EAAAnB,EAAAoB,QAAA,CAAAC,KAAA,wBACAC,OAAAC,KAAAJ,EAAAK,KAAA,SAAgC,MAEhCf,EAAAgB,MAAAP,EAAAQ,KACAhB,EAAAe,MAAAP,EAAAb,EAAuB,yPCnB3B,MAAAT,EAAAC,IAEAG,EAAAC,IACA0B,EAAA,KACE3B,EAAA4B,KAAA,CAAYP,KAAA,KACJ,6tBCaVQ,EAAA,CAAe,KAAAC,GAEX,MAAA9B,EAAAC,IACA8B,EAAA7B,IAEAN,EAAAC,IAEAmB,QAAAC,IAAA,QAAArB,EAAAoC,WA+CA,OAVAC,GAAA,KACE,MAAAC,EAAAC,SAAAC,qBAAA,QAAA,GACAC,aAAAC,QAAA,UAAA,SAAAD,aAAAC,QAAA,SACE1C,EAAA2C,kBAAA,GAEA3C,EAAA2C,kBAAA,GAEFL,EAAAM,aAAA,QAAA5C,EAAAoC,UAAA,OAAA,QAAA,IAGF,CAAOS,aA7CPC,gBACEC,IACAZ,EAAAa,YACAC,EAAA,CAAUC,KAAA,UACFC,QAAA,QACG,EAyCXC,aA/BF,KAKEhD,EAAA4B,KAAA,CAAYP,KAAA,mBACJ,EA0BRzB,QACAqD,iBAvBF,KACEjD,EAAA4B,KAAA,CAAYP,KAAA,mBACJ,EAuBRU,SAAAmB,GAAA,IAAAnB,IACiCoB,kBAAAC,GAAArB,EAAAsB,WAAAD,GAC0BE,YA3C7D,KAEEjB,aAAAkB,QAAA,QAAA3D,EAAAoC,UAAA,QAAA,QACApC,EAAA2C,kBAAA3C,EAAAoC,UAAA,EA0CF,EACFnB,MAAA,CACO,MAAA2C,CAAAC,EAAAC,GAGHD,EAAAE,MAAAtD,IAAAqD,EAAAC,MAAAtD,IACEuD,KAAAC,QAAAC,GAAA,EACF,GAEJC,WAAA,CACYC,WACVC,aACAC,iBACAC,mBAhHIC,EAAA,CAAAC,MAAA,kHAIOC,IAAA,qCAGmCC,QAAA,OAAKF,MAAA,eAAiFG,IAAA,4FAO1HC,EAAA,CAAAJ,MAAA,qEAIAK,EAAAC,EAAA,OAAA,KAAA,QAAA,yDA/BdC,EAAAD,EAAA,OAAA,KAAA,QAAA,MAqCeL,IAAA,4FApCbO,sBAC0ER,MAAAS,EAAA,CAAA,qBAAA,2CAKxE,YACmCH,EAAA,MAAA,CAAON,MAAA,wFACyJ,CAA/JM,EAAA,MAAA,CAAAJ,QAAA,OAA4JC,IAAAO,EAAAnF,MAAAoC,UAAA,mEAAA,oEATpMgD,IAAA,IAUQ,KAAA,EAAAC,yDAEsCN,EAAA,MAAA,CAAAN,MAAA,uBAZ9Ca,QAAAC,EAAA,KAAAA,EAAA,GAAA,IAAAC,IAAAL,EAAA/B,cAAA+B,EAAA/B,gBAAAoC,KAiBsCC,KAChCN,EAAAhD,SAAAuD,OAAAC,IAAAC,EAAA,MAAAC,EAAA,CAAiDC,EAAAC,EAAA,CAAuB,eAAA,0CAKnD,kBAKID,EAAAE,EAAA,KAAA,CAHAC,QAAAC,GAAA,IAAA,CACsBJ,EAAAK,EAAA,CAAAb,QAAAH,EAAA9B,kBAAA,CAAA4C,QAAAC,GAAA,IAAA,CACtBE,EA3B/BtB,KA6BYuB,EAAA,GAAA,EAAA,CAAA,YA7BZP,EAAAK,EAAA,CAAAb,QAAAH,EAAAtC,cAAA,CA8BwDoD,QAAAC,GAAA,IAAA,CACzBI,EA/B/BtB,UAAA,EAAA,CAAA,wCAoB2ID,EAAA,MAAA,KAAA,CACjIwB"}