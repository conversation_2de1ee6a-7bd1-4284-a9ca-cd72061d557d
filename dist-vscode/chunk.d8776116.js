import{G as a}from"./index.7c7944d0.js";const s=async s=>await a.get("/material/list",{params:s}),t=async s=>(await a.get("/material/find",{params:s})).data,e=async s=>await a.get("/apps/lverList",{params:s}),i=async s=>await a.post("/file/upload",s,{"Content-Type":"form-data"}),r=async s=>await a.get("/material/collect",{params:s}),p=async s=>await a.get("/folder/list",{params:s}),m=async s=>await a.get("/folder/delete",{params:s}),l=async s=>await a.get("/apps/list",{params:s}),n=async s=>await a.get("/apps/moduleList",{params:s}),c=async s=>await a.get("/material/share",{params:s});export{l as a,n as b,r as c,t as d,m as f,p as g,s as m,c as s,i as u,e as v};
//# sourceMappingURL=chunk.d8776116.js.map
