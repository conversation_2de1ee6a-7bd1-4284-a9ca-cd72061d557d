const e=(e=new Date,t,r=!1)=>(e.constructor!==Date&&(e=new Date(e)),t.replace(/yyyy|Y/g,r?e.getUTCFullYear():e.getFullYear()).replace(/yy|y/g,String(r?e.getUTCFullYear():e.getFullYear()).substr(-2,2)).replace(/MM/g,g(String((r?e.getUTCMonth():e.getMonth())+1),2,"0")).replace(/M/g,(r?e.getUTCMonth():e.getMonth())+1).replace(/dd/g,g(String(r?e.getUTCDate():e.getDate()),2,"0")).replace(/d/g,r?e.getUTCDate():e.getDate()).replace(/D/g,r?e.getUTCDay():e.getDay()).replace(/HH|hh/g,g(String(r?e.getUTCHours():e.getHours()),2,"0")).replace(/H|h/g,r?e.getUTCHours():e.getHours()).replace(/ms/g,r?e.getUTCMilliseconds():e.getMilliseconds()).replace(/mm/g,g(String(r?e.getUTCMinutes():e.getMinutes()),2,"0")).replace(/m/g,g(String(r?e.getUTCMinutes():e.getMinutes()),2,"0")).replace(/SS/g,g(String(r?e.getUTCSeconds():e.getSeconds()),2,"0")).replace(/S/g,r?e.getUTCSeconds():e.getSeconds())),t=[""," ","  ","   ","    ","     ","      ","       ","        ","         "],g=(e,g,r)=>{if((g-=(e+="").length)<=0)return e;if(r||0===r||(r=" ")," "===(r+="")&&g<10)return t[g]+e;let n="";for(;1&g&&(n+=r),g>>=1;)r+=r;return n+e};function r(e,t,g){let r=0,n=null,l=!1;async function a(){try{await e()}catch(o){console.error("Polling task error:",o)}r++,g&&r>=g?c():l||(n=setTimeout(a,t))}function c(){l=!0,n&&(clearTimeout(n),n=null)}return{start:function(){l&&(l=!1,r=0),a()},stop:c}}export{r as c,e as f};
//# sourceMappingURL=chunk.b2d62236.js.map
