{"version": 3, "file": "chunk.8f74f9db.js", "sources": ["../src/api/invite/index.ts", "../node_modules/clipboard/dist/clipboard.js", "../node_modules/vue-clipboard3/dist/esm/index.js", "../src/views/designCooperate/utils/index.ts"], "sourcesContent": ["import axios from \"@/api\";\nimport qs from \"qs\";\n\nexport const addInvite = async (args) => {\n  const res = await axios.post(\"/invite/add\", qs.stringify(args));\n  return res.data;\n};\n\nexport const linkInvite = async (args) => {\n  const res = await axios.post(\"/invite/link\", qs.stringify(args));\n  return res.data;\n};\n\nexport const getLinkInfo = async (args) => {\n  const res = await axios.get(\"/invite/linkInfo\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const joinLink = async (args) => {\n  const res = await axios.get(\"/invite/join\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const deleteInvite = async (args) => {\n  const res = await axios.get(\"/invite/delete\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const updateInvite = async (args) => {\n  const res = await axios.get(\"/invite/update\", {\n    params: args\n  });\n  return res.data;\n};\n", "/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardJS\"] = factory();\n\telse\n\t\troot[\"ClipboardJS\"] = factory();\n})(this, function() {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 686:\n/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ clipboard; }\n});\n\n// EXTERNAL MODULE: ./node_modules/tiny-emitter/index.js\nvar tiny_emitter = __webpack_require__(279);\nvar tiny_emitter_default = /*#__PURE__*/__webpack_require__.n(tiny_emitter);\n// EXTERNAL MODULE: ./node_modules/good-listener/src/listen.js\nvar listen = __webpack_require__(370);\nvar listen_default = /*#__PURE__*/__webpack_require__.n(listen);\n// EXTERNAL MODULE: ./node_modules/select/src/select.js\nvar src_select = __webpack_require__(817);\nvar select_default = /*#__PURE__*/__webpack_require__.n(src_select);\n;// CONCATENATED MODULE: ./src/common/command.js\n/**\n * Executes a given operation type.\n * @param {String} type\n * @return {Boolean}\n */\nfunction command(type) {\n  try {\n    return document.execCommand(type);\n  } catch (err) {\n    return false;\n  }\n}\n;// CONCATENATED MODULE: ./src/actions/cut.js\n\n\n/**\n * Cut action wrapper.\n * @param {String|HTMLElement} target\n * @return {String}\n */\n\nvar ClipboardActionCut = function ClipboardActionCut(target) {\n  var selectedText = select_default()(target);\n  command('cut');\n  return selectedText;\n};\n\n/* harmony default export */ var actions_cut = (ClipboardActionCut);\n;// CONCATENATED MODULE: ./src/common/create-fake-element.js\n/**\n * Creates a fake textarea element with a value.\n * @param {String} value\n * @return {HTMLElement}\n */\nfunction createFakeElement(value) {\n  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';\n  var fakeElement = document.createElement('textarea'); // Prevent zooming on iOS\n\n  fakeElement.style.fontSize = '12pt'; // Reset box model\n\n  fakeElement.style.border = '0';\n  fakeElement.style.padding = '0';\n  fakeElement.style.margin = '0'; // Move element out of screen horizontally\n\n  fakeElement.style.position = 'absolute';\n  fakeElement.style[isRTL ? 'right' : 'left'] = '-9999px'; // Move element to the same position vertically\n\n  var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n  fakeElement.style.top = \"\".concat(yPosition, \"px\");\n  fakeElement.setAttribute('readonly', '');\n  fakeElement.value = value;\n  return fakeElement;\n}\n;// CONCATENATED MODULE: ./src/actions/copy.js\n\n\n\n/**\n * Create fake copy action wrapper using a fake element.\n * @param {String} target\n * @param {Object} options\n * @return {String}\n */\n\nvar fakeCopyAction = function fakeCopyAction(value, options) {\n  var fakeElement = createFakeElement(value);\n  options.container.appendChild(fakeElement);\n  var selectedText = select_default()(fakeElement);\n  command('copy');\n  fakeElement.remove();\n  return selectedText;\n};\n/**\n * Copy action wrapper.\n * @param {String|HTMLElement} target\n * @param {Object} options\n * @return {String}\n */\n\n\nvar ClipboardActionCopy = function ClipboardActionCopy(target) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    container: document.body\n  };\n  var selectedText = '';\n\n  if (typeof target === 'string') {\n    selectedText = fakeCopyAction(target, options);\n  } else if (target instanceof HTMLInputElement && !['text', 'search', 'url', 'tel', 'password'].includes(target === null || target === void 0 ? void 0 : target.type)) {\n    // If input type doesn't support `setSelectionRange`. Simulate it. https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n    selectedText = fakeCopyAction(target.value, options);\n  } else {\n    selectedText = select_default()(target);\n    command('copy');\n  }\n\n  return selectedText;\n};\n\n/* harmony default export */ var actions_copy = (ClipboardActionCopy);\n;// CONCATENATED MODULE: ./src/actions/default.js\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n\n\n/**\n * Inner function which performs selection from either `text` or `target`\n * properties and then executes copy or cut operations.\n * @param {Object} options\n */\n\nvar ClipboardActionDefault = function ClipboardActionDefault() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  // Defines base properties passed from constructor.\n  var _options$action = options.action,\n      action = _options$action === void 0 ? 'copy' : _options$action,\n      container = options.container,\n      target = options.target,\n      text = options.text; // Sets the `action` to be performed which can be either 'copy' or 'cut'.\n\n  if (action !== 'copy' && action !== 'cut') {\n    throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n  } // Sets the `target` property using an element that will be have its content copied.\n\n\n  if (target !== undefined) {\n    if (target && _typeof(target) === 'object' && target.nodeType === 1) {\n      if (action === 'copy' && target.hasAttribute('disabled')) {\n        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n      }\n\n      if (action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n        throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n      }\n    } else {\n      throw new Error('Invalid \"target\" value, use a valid Element');\n    }\n  } // Define selection strategy based on `text` property.\n\n\n  if (text) {\n    return actions_copy(text, {\n      container: container\n    });\n  } // Defines which selection strategy based on `target` property.\n\n\n  if (target) {\n    return action === 'cut' ? actions_cut(target) : actions_copy(target, {\n      container: container\n    });\n  }\n};\n\n/* harmony default export */ var actions_default = (ClipboardActionDefault);\n;// CONCATENATED MODULE: ./src/clipboard.js\nfunction clipboard_typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { clipboard_typeof = function _typeof(obj) { return typeof obj; }; } else { clipboard_typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return clipboard_typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (clipboard_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\n\n\n\n/**\n * Helper function to retrieve attribute value.\n * @param {String} suffix\n * @param {Element} element\n */\n\nfunction getAttributeValue(suffix, element) {\n  var attribute = \"data-clipboard-\".concat(suffix);\n\n  if (!element.hasAttribute(attribute)) {\n    return;\n  }\n\n  return element.getAttribute(attribute);\n}\n/**\n * Base class which takes one or more elements, adds event listeners to them,\n * and instantiates a new `ClipboardAction` on each click.\n */\n\n\nvar Clipboard = /*#__PURE__*/function (_Emitter) {\n  _inherits(Clipboard, _Emitter);\n\n  var _super = _createSuper(Clipboard);\n\n  /**\n   * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n   * @param {Object} options\n   */\n  function Clipboard(trigger, options) {\n    var _this;\n\n    _classCallCheck(this, Clipboard);\n\n    _this = _super.call(this);\n\n    _this.resolveOptions(options);\n\n    _this.listenClick(trigger);\n\n    return _this;\n  }\n  /**\n   * Defines if attributes would be resolved using internal setter functions\n   * or custom functions that were passed in the constructor.\n   * @param {Object} options\n   */\n\n\n  _createClass(Clipboard, [{\n    key: \"resolveOptions\",\n    value: function resolveOptions() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n      this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n      this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n      this.container = clipboard_typeof(options.container) === 'object' ? options.container : document.body;\n    }\n    /**\n     * Adds a click event listener to the passed trigger.\n     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n     */\n\n  }, {\n    key: \"listenClick\",\n    value: function listenClick(trigger) {\n      var _this2 = this;\n\n      this.listener = listen_default()(trigger, 'click', function (e) {\n        return _this2.onClick(e);\n      });\n    }\n    /**\n     * Defines a new `ClipboardAction` on each click event.\n     * @param {Event} e\n     */\n\n  }, {\n    key: \"onClick\",\n    value: function onClick(e) {\n      var trigger = e.delegateTarget || e.currentTarget;\n      var action = this.action(trigger) || 'copy';\n      var text = actions_default({\n        action: action,\n        container: this.container,\n        target: this.target(trigger),\n        text: this.text(trigger)\n      }); // Fires an event based on the copy operation result.\n\n      this.emit(text ? 'success' : 'error', {\n        action: action,\n        text: text,\n        trigger: trigger,\n        clearSelection: function clearSelection() {\n          if (trigger) {\n            trigger.focus();\n          }\n\n          window.getSelection().removeAllRanges();\n        }\n      });\n    }\n    /**\n     * Default `action` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultAction\",\n    value: function defaultAction(trigger) {\n      return getAttributeValue('action', trigger);\n    }\n    /**\n     * Default `target` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultTarget\",\n    value: function defaultTarget(trigger) {\n      var selector = getAttributeValue('target', trigger);\n\n      if (selector) {\n        return document.querySelector(selector);\n      }\n    }\n    /**\n     * Allow fire programmatically a copy action\n     * @param {String|HTMLElement} target\n     * @param {Object} options\n     * @returns Text copied.\n     */\n\n  }, {\n    key: \"defaultText\",\n\n    /**\n     * Default `text` lookup function.\n     * @param {Element} trigger\n     */\n    value: function defaultText(trigger) {\n      return getAttributeValue('text', trigger);\n    }\n    /**\n     * Destroy lifecycle.\n     */\n\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.listener.destroy();\n    }\n  }], [{\n    key: \"copy\",\n    value: function copy(target) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        container: document.body\n      };\n      return actions_copy(target, options);\n    }\n    /**\n     * Allow fire programmatically a cut action\n     * @param {String|HTMLElement} target\n     * @returns Text cutted.\n     */\n\n  }, {\n    key: \"cut\",\n    value: function cut(target) {\n      return actions_cut(target);\n    }\n    /**\n     * Returns the support of the given action, or all actions if no action is\n     * given.\n     * @param {String} [action]\n     */\n\n  }, {\n    key: \"isSupported\",\n    value: function isSupported() {\n      var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n      var actions = typeof action === 'string' ? [action] : action;\n      var support = !!document.queryCommandSupported;\n      actions.forEach(function (action) {\n        support = support && !!document.queryCommandSupported(action);\n      });\n      return support;\n    }\n  }]);\n\n  return Clipboard;\n}((tiny_emitter_default()));\n\n/* harmony default export */ var clipboard = (Clipboard);\n\n/***/ }),\n\n/***/ 828:\n/***/ (function(module) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ }),\n\n/***/ 438:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar closest = __webpack_require__(828);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n\n/***/ 879:\n/***/ (function(__unused_webpack_module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n\n/***/ 370:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar is = __webpack_require__(879);\nvar delegate = __webpack_require__(438);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n\n/***/ 817:\n/***/ (function(module) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ }),\n\n/***/ 279:\n/***/ (function(module) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(__webpack_module_cache__[moduleId]) {\n/******/ \t\t\treturn __webpack_module_cache__[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\n/******/ \t// module exports must be returned from runtime so entry inlining is disabled\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(686);\n/******/ })()\n.default;\n});", "import Clipboard from 'clipboard';\nexport default (opts) => {\n    // default appendToBody true\n    const appendToBody = (opts === null || opts === void 0 ? void 0 : opts.appendToBody) === undefined ? true : opts.appendToBody;\n    return {\n        toClipboard(text, container) {\n            return new Promise((resolve, reject) => {\n                // make fake element\n                const fakeEl = document.createElement('button');\n                // setup a new Clipboard.js\n                const clipboard = new Clipboard(fakeEl, {\n                    text: () => text,\n                    action: () => 'copy',\n                    container: container !== undefined ? container : document.body\n                });\n                clipboard.on('success', (e) => {\n                    clipboard.destroy();\n                    resolve(e);\n                });\n                clipboard.on('error', (e) => {\n                    clipboard.destroy();\n                    reject(e);\n                });\n                // appendToBody fixes IE\n                if (appendToBody)\n                    document.body.appendChild(fakeEl);\n                // simulate click\n                fakeEl.click();\n                // remove from body if appended\n                if (appendToBody)\n                    document.body.removeChild(fakeEl);\n            });\n        }\n    };\n};\n//# sourceMappingURL=index.js.map", "import { getLinkInfo, joinLink, linkInvite } from \"@/api/invite\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { ErrorCode, InviteCategory, Permission, permissionName } from \"@/model\";\nimport { ObjectAny } from \"@/types\";\nimport { userInfoStore } from \"@/store\";\nimport { formatDate } from \"@/utils/date\";\nimport useClipboard from \"vue-clipboard3\";\n\nconst { toClipboard } = useClipboard({\n  appendToBody: false\n});\n\nexport const invite = async (iv_id?: string) => {\n  if (!iv_id) {\n    return;\n  }\n  try {\n    const res = await getLinkInfo({\n      iv_id\n    });\n    if (res.code !== ErrorCode.OK) {\n      throw res.msg;\n    }\n    const { resource, permission, isDeleted } = res.data;\n    if (isDeleted) {\n      ElMessage.error(\"用户已被管理员踢出团队，如有异议请寻找管理员\");\n      return;\n    }\n    if (permission) {\n      await ElMessageBox.confirm(\n        `\n          <p>邀请您 加入 <b>${resource.name}</b></p>\n          <div style=\"margin-top: 12px\">\n              您可在加入后，对其进行 <b>${permissionName[permission]}</b>\n          </div>\n        `,\n        \"注意\",\n        {\n          confirmButtonText: \"确 认\",\n          cancelButtonText: \"取 消\",\n          dangerouslyUseHTMLString: true,\n          type: \"warning\"\n        }\n      );\n      await join(iv_id);\n    }\n    return resource._id;\n  } catch (e) {\n    ElMessage.error(e || \"系统错误！\");\n  }\n};\n\nconst join = async (iv_id: string) => {\n  const res = await joinLink({\n    iv_id\n  });\n  if (res.code !== ErrorCode.OK) {\n    throw res.msg;\n  }\n  ElMessage.success(\"接受邀请成功\");\n};\n\nexport const handleShare = async (\n  team: ObjectAny,\n  permission = Permission.PREVIEW,\n  url: string,\n  breadcrumb: string[]\n) => {\n  if (!team) {\n    return;\n  }\n  if (team.permission !== Permission.OWNER && team.permission !== Permission.MANAGE) {\n    ElMessage.error(\"当前角色暂不支持分享\");\n    return;\n  }\n  const userInfo = userInfoStore();\n  const res = await linkInvite({\n    category: InviteCategory.SMB,\n    permission,\n    resourceId: team._id\n  });\n  if (res.code == 0) {\n    const template = `${window.location.origin + url + \"iv_id=\" + res.data._id}\n  分享人: ${userInfo.name}\n  分享信息: ${breadcrumb.join(\" >> \")}\n  分享权限: ${permissionName[permission]}\n  链接有效期至 ${formatDate(res.data.expiredTime, \"yyyy-MM-dd HH:mm:SS\")}`;\n    await toClipboard(template);\n    ElMessageBox.alert(\n      `\n      <p style=\"color: #f00;\">请注意：不要把链接随意分享给陌生人</p>\n      <p>分享人: ${userInfo.name}</p>\n      <p>分享信息: ${breadcrumb.join(\" >> \")}</p>\n      <p>分享类型: ${permissionName[permission]}</p>\n      <p>链接有效期至 ${formatDate(res.data.expiredTime, \"yyyy-MM-dd HH:mm:SS\")}</p>\n      `,\n      \"复制成功\",\n      {\n        dangerouslyUseHTMLString: true,\n        confirmButtonText: \"确定\"\n      }\n    );\n  }\n};\n\nexport function findItem(list: ObjectAny[], target: any, key: string) {\n  for (let i = 0; i < list.length; i++) {\n    const element = list[i];\n    if (element[key] === target) {\n      return element;\n    }\n    if (element.children) {\n      const res = findItem(element.children, target, key);\n      if (res) {\n        return res;\n      }\n    }\n  }\n}\n"], "names": ["deleteInvite", "async", "args", "axios", "get", "params", "data", "updateInvite", "exports", "__webpack_modules__", "__unused_webpack_module", "__webpack_exports__", "__webpack_require__", "d", "default", "tiny_emitter", "tiny_emitter_default", "n", "listen", "listen_default", "src_select", "select_default", "command", "type", "document", "execCommand", "err", "actions_cut", "target", "selectedText", "fakeCopyAction", "value", "options", "fakeElement", "isRTL", "documentElement", "getAttribute", "createElement", "style", "fontSize", "border", "padding", "margin", "position", "yPosition", "window", "pageYOffset", "scrollTop", "top", "concat", "setAttribute", "createFakeElement", "container", "append<PERSON><PERSON><PERSON>", "remove", "actions_copy", "arguments", "length", "body", "HTMLInputElement", "includes", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "actions_default", "_options$action", "action", "text", "Error", "nodeType", "hasAttribute", "clipboard_typeof", "_defineProperties", "props", "i", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Date", "toString", "call", "e", "_isNativeReflectConstruct", "result", "self", "Super", "_getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON>", "this", "apply", "ReferenceError", "_assertThisInitialized", "getPrototypeOf", "getAttributeValue", "suffix", "element", "attribute", "Clipboard", "_Emitter", "subClass", "superClass", "TypeError", "create", "_inherits", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_super", "trigger", "_this", "instance", "_classCallCheck", "resolveOptions", "listenClick", "defaultAction", "defaultTarget", "defaultText", "_this2", "listener", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "currentTarget", "emit", "clearSelection", "focus", "getSelection", "removeAllRanges", "selector", "querySelector", "destroy", "actions", "support", "queryCommandSupported", "for<PERSON>ach", "clipboard", "module", "Element", "matches", "proto", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "parentNode", "__unused_webpack_exports", "closest", "_delegate", "callback", "useCapture", "listenerFn", "addEventListener", "removeEventListener", "elements", "bind", "querySelectorAll", "Array", "map", "node", "HTMLElement", "nodeList", "string", "String", "fn", "is", "delegate", "listenNode", "listenNodeList", "listenSelector", "nodeName", "isReadOnly", "select", "setSelectionRange", "removeAttribute", "selection", "range", "createRange", "selectNodeContents", "addRange", "E", "on", "name", "ctx", "push", "once", "off", "_", "slice", "evtArr", "len", "evts", "liveEvents", "TinyEmitter", "__webpack_module_cache__", "moduleId", "getter", "__esModule", "a", "definition", "prop", "hasOwnProperty", "factory", "useClipboard", "opts", "appendToBody", "toClipboard", "Promise", "resolve", "reject", "fakeEl", "click", "<PERSON><PERSON><PERSON><PERSON>", "invite", "iv_id", "res", "getLinkInfo", "code", "ErrorCode", "OK", "msg", "resource", "permission", "isDeleted", "ElMessage", "error", "ElMessageBox", "confirm", "permissionName", "confirmButtonText", "cancelButtonText", "dangerouslyUseHTMLString", "join", "_id", "joinLink", "success", "handleShare", "team", "Permission", "PREVIEW", "url", "breadcrumb", "OWNER", "MANAGE", "userInfo", "userInfoStore", "post", "qs", "stringify", "linkInvite", "category", "InviteCategory", "SMB", "resourceId", "template", "location", "origin", "formatDate", "expiredTime", "alert", "findItem", "list", "children"], "mappings": "4PAQa,MAmBAA,EAAeC,MAAOC,UACfC,EAAMC,IAAI,iBAAkB,CAC5CC,OAAQH,KAECI,KAGAC,EAAeN,MAAOC,UACfC,EAAMC,IAAI,iBAAkB,CAC5CC,OAAQH,KAECI;;;;;;eC9BXE,QAOO,WACT,OAAiB,WACP,IAAIC,EAAuB,CAE/B,IAAA,SACUC,EAAyBC,EAAqBC,GAK9DA,EAAoBC,EAAEF,EAAqB,CACzCG,QAAW,WAAa,OAAA,CAAiC,IAIvD,IAAAC,EAAeH,EAAoB,KACnCI,EAAoCJ,EAAoBK,EAAEF,GAE1DG,EAASN,EAAoB,KAC7BO,EAA8BP,EAAoBK,EAAEC,GAEpDE,EAAaR,EAAoB,KACjCS,EAA8BT,EAAoBK,EAAEG,GAOxD,SAASE,EAAQC,GACX,IACK,OAAAC,SAASC,YAAYF,EAC7B,OAAQG,GACA,OAAA,CACR,CACF,CAUG,IAM6BC,EANR,SAA4BC,GAC/C,IAAAC,EAAeR,IAAiBO,GAE7B,OADPN,EAAQ,OACDO,CACT,EAuCIC,EAAiB,SAAwBC,EAAOC,GAC9C,IAAAC,EA/BN,SAA2BF,GACzB,IAAIG,EAAyD,QAAjDV,SAASW,gBAAgBC,aAAa,OAC9CH,EAAcT,SAASa,cAAc,YAEzCJ,EAAYK,MAAMC,SAAW,OAE7BN,EAAYK,MAAME,OAAS,IAC3BP,EAAYK,MAAMG,QAAU,IAC5BR,EAAYK,MAAMI,OAAS,IAE3BT,EAAYK,MAAMK,SAAW,WAC7BV,EAAYK,MAAMJ,EAAQ,QAAU,QAAU,UAE9C,IAAIU,EAAYC,OAAOC,aAAetB,SAASW,gBAAgBY,UAIxD,OAHPd,EAAYK,MAAMU,IAAM,GAAGC,OAAOL,EAAW,MACjCX,EAAAiB,aAAa,WAAY,IACrCjB,EAAYF,MAAQA,EACbE,CACR,CAamBkB,CAAkBpB,GAC5BC,EAAAoB,UAAUC,YAAYpB,GAC1B,IAAAJ,EAAeR,IAAiBY,GAG7B,OAFPX,EAAQ,QACRW,EAAYqB,SACLzB,CACT,EA4BiC0B,EAnBP,SAA6B3B,GACjD,IAAAI,EAAUwB,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAChFJ,UAAW5B,SAASkC,MAElB7B,EAAe,GAYZ,MAVe,iBAAXD,EACMC,EAAAC,EAAeF,EAAQI,GAC7BJ,aAAkB+B,mBAAqB,CAAC,OAAQ,SAAU,MAAO,MAAO,YAAYC,SAAShC,aAAuC,EAASA,EAAOL,MAE9IM,EAAAC,EAAeF,EAAOG,MAAOC,IAE7BH,EAAAR,IAAiBO,GAChCN,EAAQ,SAGHO,CACT,EAIA,SAASgC,EAAQC,GAAmV,OAA5ND,EAA/D,mBAAXE,QAAoD,iBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,GAA2B,SAAiBA,GAAcA,OAAAA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAI,GAAqBA,EAAO,CAUtX,IA2C6BK,EA3CJ,WACvB,IAAAnC,EAAUwB,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAE9EY,EAAkBpC,EAAQqC,OAC1BA,OAA6B,IAApBD,EAA6B,OAASA,EAC/ChB,EAAYpB,EAAQoB,UACpBxB,EAASI,EAAQJ,OACjB0C,EAAOtC,EAAQsC,KAEf,GAAW,SAAXD,GAAgC,QAAXA,EACjB,MAAA,IAAIE,MAAM,sDAIlB,QAAe,IAAX3C,EAAsB,CACxB,IAAIA,GAA8B,WAApBiC,EAAQjC,IAA4C,IAApBA,EAAO4C,SAS7C,MAAA,IAAID,MAAM,+CARhB,GAAe,SAAXF,GAAqBzC,EAAO6C,aAAa,YACrC,MAAA,IAAIF,MAAM,qFAGd,GAAW,QAAXF,IAAqBzC,EAAO6C,aAAa,aAAe7C,EAAO6C,aAAa,aACxE,MAAA,IAAIF,MAAM,yGAKrB,CAGD,OAAID,EACKf,EAAae,EAAM,CACxBlB,cAKAxB,EACgB,QAAXyC,EAAmB1C,EAAYC,GAAU2B,EAAa3B,EAAQ,CACnEwB,mBAFJ,CAKF,EAIA,SAASsB,EAAiBZ,GAAqW,OAArOY,EAAxE,mBAAXX,QAAoD,iBAApBA,OAAOC,SAA4C,SAAiBF,GAAO,cAAcA,GAAoC,SAAiBA,GAAcA,OAAAA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAI,GAA8BA,EAAO,CAIrZ,SAAAa,EAAkB/C,EAAQgD,GAAS,IAAA,IAASC,EAAI,EAAGA,EAAID,EAAMnB,OAAQoB,IAAK,CAAM,IAAAC,EAAaF,EAAMC,GAAeC,EAAAC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMC,OAAOC,eAAevD,EAAQkD,EAAWM,IAAKN,GAAgB,CAMpT,SAAAO,EAAgBC,EAAGC,GAAsH,OAAjHF,EAAkBH,OAAOM,gBAAkB,SAAyBF,EAAGC,GAA6BD,OAAxBA,EAAEG,UAAYF,EAAUD,CAAE,GAA2BA,EAAGC,EAAK,CAE1K,SAASG,EAAaC,GAAW,IAAIC,EAMrC,WAAuC,GAAuB,oBAAZC,UAA4BA,QAAQC,UAAkB,OAAA,EAAO,GAAID,QAAQC,UAAUC,KAAa,OAAA,EAAO,GAAqB,mBAAVC,MAA6B,OAAA,EAAU,IAAoF,OAA7EC,KAAA/B,UAAUgC,SAASC,KAAKN,QAAQC,UAAUG,KAAM,IAAI,WAAY,MAAa,CAAK,OAAUG,GAAY,OAAA,EAAU,CANnQC,GAA6B,OAAO,WAAsC,IAAkCC,EAEzIC,EAAMJ,EAFiGK,EAAQC,EAAgBd,GAAkB,GAAIC,EAA2B,CAAM,IAAAc,EAAYD,EAAgBE,MAAM1C,YAAaqC,EAAST,QAAQC,UAAUU,EAAOhD,UAAWkD,QAA8BJ,EAAAE,EAAMI,MAAMD,KAAMnD,WAAqB,OAEvV+C,EAFkXI,OAE5WR,EAFkXG,IAElU,WAA3B5B,EAAiByB,IAAsC,mBAATA,EAE7G,SAAgCI,GAAQ,QAAa,IAATA,EAAyB,MAAA,IAAIM,eAAe,6DAAuE,OAAAN,CAAO,CAFXO,CAAuBP,GAAtCJ,CAFwR,CAAK,CAQza,SAASM,EAAgBnB,GAAwJ,OAAnJmB,EAAkBvB,OAAOM,eAAiBN,OAAO6B,eAAiB,SAAyBzB,GAAK,OAAOA,EAAEG,WAAaP,OAAO6B,eAAezB,EAAK,GAAyBA,EAAK,CAapM,SAAA0B,EAAkBC,EAAQC,GAC7B,IAAAC,EAAY,kBAAkBlE,OAAOgE,GAEzC,GAAKC,EAAQzC,aAAa0C,GAInB,OAAAD,EAAQ9E,aAAa+E,EAC7B,CAOGC,IAAAA,WAAmCC,IAxC9B,SAAUC,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAA6B,MAAA,IAAIC,UAAU,sDAAyDF,EAASpD,UAAYgB,OAAOuC,OAAOF,GAAcA,EAAWrD,UAAW,CAAED,YAAa,CAAElC,MAAOuF,EAAUrC,UAAU,EAAMD,cAAc,KAAeuC,GAAYlC,EAAgBiC,EAAUC,EAAc,CAyC/XG,CAAUN,EAAWC,GAEjB,IA7CgBM,EAAaC,EAAYC,EA6CzCC,EAASpC,EAAa0B,GAMjBA,SAAAA,EAAUW,EAAS/F,GACtB,IAAAgG,EAUG,OAlEF,SAAgBC,EAAUN,GAAmB,KAAEM,aAAoBN,GAAsB,MAAA,IAAIH,UAAU,oCAAyC,CA0DrJU,CAAgBvB,KAAMS,IAEdY,EAAAF,EAAO3B,KAAKQ,OAEdwB,eAAenG,GAErBgG,EAAMI,YAAYL,GAEXC,CACR,CAqJMZ,OApNaO,EAuEPP,EAvEoBQ,EAuET,CAAC,CACvBxC,IAAK,iBACLrD,MAAO,WACD,IAAAC,EAAUwB,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAClFmD,KAAKtC,OAAmC,mBAAnBrC,EAAQqC,OAAwBrC,EAAQqC,OAASsC,KAAK0B,cAC3E1B,KAAK/E,OAAmC,mBAAnBI,EAAQJ,OAAwBI,EAAQJ,OAAS+E,KAAK2B,cAC3E3B,KAAKrC,KAA+B,mBAAjBtC,EAAQsC,KAAsBtC,EAAQsC,KAAOqC,KAAK4B,YAChE5B,KAAAvD,UAAoD,WAAxCsB,EAAiB1C,EAAQoB,WAA0BpB,EAAQoB,UAAY5B,SAASkC,IAClG,GAMA,CACD0B,IAAK,cACLrD,MAAO,SAAqBgG,GAC1B,IAAIS,EAAS7B,KAEbA,KAAK8B,SAAWtH,IAAiB4G,EAAS,SAAS,SAAU3B,GACpD,OAAAoC,EAAOE,QAAQtC,EAC9B,GACK,GAMA,CACDhB,IAAK,UACLrD,MAAO,SAAiBqE,GAClB,IAAA2B,EAAU3B,EAAEuC,gBAAkBvC,EAAEwC,cAChCvE,EAASsC,KAAKtC,OAAO0D,IAAY,OACjCzD,EAAOH,EAAgB,CACzBE,SACAjB,UAAWuD,KAAKvD,UAChBxB,OAAQ+E,KAAK/E,OAAOmG,GACpBzD,KAAMqC,KAAKrC,KAAKyD,KAGbpB,KAAAkC,KAAKvE,EAAO,UAAY,QAAS,CACpCD,SACAC,OACAyD,UACAe,eAAgB,WACVf,GACFA,EAAQgB,QAGHlG,OAAAmG,eAAeC,iBACvB,GAEJ,GAMA,CACD7D,IAAK,gBACLrD,MAAO,SAAuBgG,GACrB,OAAAf,EAAkB,SAAUe,EACpC,GAMA,CACD3C,IAAK,gBACLrD,MAAO,SAAuBgG,GACxB,IAAAmB,EAAWlC,EAAkB,SAAUe,GAE3C,GAAImB,EACK,OAAA1H,SAAS2H,cAAcD,EAEjC,GAQA,CACD9D,IAAK,cAMLrD,MAAO,SAAqBgG,GACnB,OAAAf,EAAkB,OAAQe,EAClC,GAKA,CACD3C,IAAK,UACLrD,MAAO,WACL4E,KAAK8B,SAASW,SACf,IA7K0CvB,EA8KzC,CAAC,CACHzC,IAAK,OACLrD,MAAO,SAAcH,GACf,IAAAI,EAAUwB,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAChFJ,UAAW5B,SAASkC,MAEf,OAAAH,EAAa3B,EAAQI,EAC7B,GAOA,CACDoD,IAAK,MACLrD,MAAO,SAAaH,GAClB,OAAOD,EAAYC,EACpB,GAOA,CACDwD,IAAK,cACLrD,MAAO,WACL,IAAIsC,EAASb,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAC,OAAQ,OACtF6F,EAA4B,iBAAXhF,EAAsB,CAACA,GAAUA,EAClDiF,IAAY9H,SAAS+H,sBAIlB,OAHCF,EAAAG,SAAQ,SAAUnF,GACxBiF,EAAUA,KAAa9H,SAAS+H,sBAAsBlF,EAC9D,IACaiF,CACR,IAjN6D1B,GAA8BjD,EAAAgD,EAAYzD,UAAW0D,GAAiBC,GAAalD,EAAkBgD,EAAaE,GAoN3KT,CACT,EAAGpG,KAE8ByI,EAAarC,CAE9C,EAEM,IAAA,SACUsC,GAOhB,GAAuB,oBAAZC,UAA4BA,QAAQzF,UAAU0F,QAAS,CAC9D,IAAIC,EAAQF,QAAQzF,UAEd2F,EAAAD,QAAUC,EAAMC,iBACND,EAAME,oBACNF,EAAMG,mBACNH,EAAMI,kBACNJ,EAAMK,qBACzB,CAmBDR,EAAOlJ,QAVE,SAAS0G,EAASgC,GAChB,KAAAhC,GAvBc,IAuBHA,EAAQ1C,UAAiC,CACvD,GAA+B,mBAApB0C,EAAQ0C,SACf1C,EAAQ0C,QAAQV,GACX,OAAAhC,EAETA,EAAUA,EAAQiD,UACrB,CACJ,CAKD,EAEM,IAAA,SACUT,EAAQU,EAA0BxJ,GAE9C,IAAAyJ,EAAUzJ,EAAoB,KAYlC,SAAS0J,EAAUpD,EAASgC,EAAU3H,EAAMgJ,EAAUC,GAClD,IAAIC,EAAahC,EAAS7B,MAAMD,KAAMnD,WAI/B,OAFC0D,EAAAwD,iBAAiBnJ,EAAMkJ,EAAYD,GAEpC,CACHpB,QAAS,WACGlC,EAAAyD,oBAAoBpJ,EAAMkJ,EAAYD,EACjD,EAER,CA6CD,SAAS/B,EAASvB,EAASgC,EAAU3H,EAAMgJ,GACvC,OAAO,SAASnE,GACZA,EAAEuC,eAAiB0B,EAAQjE,EAAExE,OAAQsH,GAEjC9C,EAAEuC,gBACO4B,EAAApE,KAAKe,EAASd,EAE9B,CACJ,CAEDsD,EAAOlJ,QA3CP,SAAkBoK,EAAU1B,EAAU3H,EAAMgJ,EAAUC,GAE9C,MAAqC,mBAA9BI,EAASF,iBACTJ,EAAU1D,MAAM,KAAMpD,WAIb,mBAATjC,EAGA+I,EAAUO,KAAK,KAAMrJ,UAAUoF,MAAM,KAAMpD,YAI9B,iBAAboH,IACIA,EAAApJ,SAASsJ,iBAAiBF,IAIlCG,MAAM7G,UAAU8G,IAAI7E,KAAKyE,GAAU,SAAU1D,GAChD,OAAOoD,EAAUpD,EAASgC,EAAU3H,EAAMgJ,EAAUC,EAC5D,IACC,CAwBD,EAEM,IAAA,SACU9J,EAAyBF,GAQzCA,EAAQyK,KAAO,SAASlJ,GACpB,YAAiB,IAAVA,GACAA,aAAiBmJ,aACE,IAAnBnJ,EAAMyC,QACjB,EAQAhE,EAAQ2K,SAAW,SAASpJ,GACxB,IAAIR,EAAO2D,OAAOhB,UAAUgC,SAASC,KAAKpE,GAE1C,YAAiB,IAAVA,IACU,sBAATR,GAAyC,4BAATA,IAChC,WAAYQ,IACK,IAAjBA,EAAM0B,QAAgBjD,EAAQyK,KAAKlJ,EAAM,IACrD,EAQAvB,EAAQ4K,OAAS,SAASrJ,GACf,MAAiB,iBAAVA,GACPA,aAAiBsJ,MAC5B,EAQA7K,EAAQ8K,GAAK,SAASvJ,GAGlB,MAAgB,sBAFLmD,OAAOhB,UAAUgC,SAASC,KAAKpE,EAG9C,CAGA,EAEM,IAAA,SACU2H,EAAQU,EAA0BxJ,GAE9C,IAAA2K,EAAK3K,EAAoB,KACzB4K,EAAW5K,EAAoB,KA6FnC8I,EAAOlJ,QAlFE,SAAOoB,EAAQL,EAAMgJ,GAC1B,IAAK3I,IAAWL,IAASgJ,EACf,MAAA,IAAIhG,MAAM,8BAGpB,IAAKgH,EAAGH,OAAO7J,GACL,MAAA,IAAIiG,UAAU,oCAGxB,IAAK+D,EAAGD,GAAGf,GACD,MAAA,IAAI/C,UAAU,qCAGpB,GAAA+D,EAAGN,KAAKrJ,GACD,OAsBN,SAAWqJ,EAAM1J,EAAMgJ,GAGrB,OAFFU,EAAAP,iBAAiBnJ,EAAMgJ,GAErB,CACHnB,QAAS,WACA6B,EAAAN,oBAAoBpJ,EAAMgJ,EAClC,EAER,CA9BckB,CAAW7J,EAAQL,EAAMgJ,GAE3B,GAAAgB,EAAGJ,SAASvJ,GACV,OAsCN,SAAeuJ,EAAU5J,EAAMgJ,GAK7B,OAJPQ,MAAM7G,UAAUsF,QAAQrD,KAAKgF,GAAU,SAASF,GACvCA,EAAAP,iBAAiBnJ,EAAMgJ,EACpC,IAEW,CACHnB,QAAS,WACL2B,MAAM7G,UAAUsF,QAAQrD,KAAKgF,GAAU,SAASF,GACvCA,EAAAN,oBAAoBpJ,EAAMgJ,EAC/C,GACS,EAER,CAlDcmB,CAAe9J,EAAQL,EAAMgJ,GAE/B,GAAAgB,EAAGH,OAAOxJ,GACR,OA0DN,SAAesH,EAAU3H,EAAMgJ,GACpC,OAAOiB,EAAShK,SAASkC,KAAMwF,EAAU3H,EAAMgJ,EAClD,CA5DcoB,CAAe/J,EAAQL,EAAMgJ,GAG9B,MAAA,IAAI/C,UAAU,4EAE3B,CA4DD,EAEM,IAAA,SACUkC,GA4ChBA,EAAOlJ,QA1CP,SAAgB0G,GACR,IAAArF,EAEA,GAAqB,WAArBqF,EAAQ0E,SACR1E,EAAQ6B,QAERlH,EAAeqF,EAAQnF,cAEG,UAArBmF,EAAQ0E,UAA6C,aAArB1E,EAAQ0E,SAAyB,CAClE,IAAAC,EAAa3E,EAAQzC,aAAa,YAEjCoH,GACO3E,EAAAhE,aAAa,WAAY,IAGrCgE,EAAQ4E,SACR5E,EAAQ6E,kBAAkB,EAAG7E,EAAQnF,MAAM0B,QAEtCoI,GACD3E,EAAQ8E,gBAAgB,YAG5BnK,EAAeqF,EAAQnF,KAC1B,KACI,CACGmF,EAAQzC,aAAa,oBACrByC,EAAQ6B,QAGR,IAAAkD,EAAYpJ,OAAOmG,eACnBkD,EAAQ1K,SAAS2K,cAErBD,EAAME,mBAAmBlF,GACzB+E,EAAUhD,kBACVgD,EAAUI,SAASH,GAEnBrK,EAAeoK,EAAU/F,UAC5B,CAEM,OAAArE,CACV,CAKD,EAEM,IAAA,SACU6H,GAEhB,SAAS4C,IAGR,CAEDA,EAAEpI,UAAY,CACZqI,GAAI,SAAUC,EAAMjC,EAAUkC,GAC5B,IAAIrG,EAAIO,KAAKP,IAAMO,KAAKP,EAAI,CAAA,GAOrB,OALNA,EAAEoG,KAAUpG,EAAEoG,GAAQ,KAAKE,KAAK,CAC/BpB,GAAIf,EACJkC,QAGK9F,IACR,EAEDgG,KAAM,SAAUH,EAAMjC,EAAUkC,GAC9B,IAAIlG,EAAOI,KACX,SAAS8B,IACFlC,EAAAqG,IAAIJ,EAAM/D,GACN8B,EAAA3D,MAAM6F,EAAKjJ,UAE1B,CAEI,OADAiF,EAASoE,EAAItC,EACN5D,KAAK4F,GAAGC,EAAM/D,EAAUgE,EAChC,EAED5D,KAAM,SAAU2D,GAMT,IALL,IAAIlM,EAAO,GAAGwM,MAAM3G,KAAK3C,UAAW,GAChCuJ,IAAWpG,KAAKP,IAAMO,KAAKP,EAAI,CAAA,IAAKoG,IAAS,IAAIM,QACjDjI,EAAI,EACJmI,EAAMD,EAAOtJ,OAEToB,EAAImI,EAAKnI,IACRkI,EAAAlI,GAAGyG,GAAG1E,MAAMmG,EAAOlI,GAAG4H,IAAKnM,GAG7B,OAAAqG,IACR,EAEDiG,IAAK,SAAUJ,EAAMjC,GACnB,IAAInE,EAAIO,KAAKP,IAAMO,KAAKP,EAAI,CAAA,GACxB6G,EAAO7G,EAAEoG,GACTU,EAAa,GAEjB,GAAID,GAAQ1C,EACV,IAAA,IAAS1F,EAAI,EAAGmI,EAAMC,EAAKxJ,OAAQoB,EAAImI,EAAKnI,IACtCoI,EAAKpI,GAAGyG,KAAOf,GAAY0C,EAAKpI,GAAGyG,GAAGuB,IAAMtC,GACnC2C,EAAAR,KAAKO,EAAKpI,IAYpB,OAJNqI,EAAWzJ,OACR2C,EAAEoG,GAAQU,SACH9G,EAAEoG,GAEN7F,IACR,GAGH+C,EAAOlJ,QAAU8L,EACjB5C,EAAOlJ,QAAQ2M,YAAcb,CAG7B,GAKcc,EAA2B,CAAA,EAG/B,SAASxM,EAAoByM,GAEzB,GAAAD,EAAyBC,GACpB,OAAAD,EAAyBC,GAAU7M,QAGvCkJ,IAAAA,EAAS0D,EAAyBC,GAAY,CAGjD7M,QAAS,CAAE,GAOZ,OAHAC,EAAoB4M,GAAU3D,EAAQA,EAAOlJ,QAASI,GAG/C8I,EAAOlJ,OACd,CAoCD,OA9BqBI,EAAAK,EAAI,SAASyI,GAC5B,IAAA4D,EAAS5D,GAAUA,EAAO6D,WAAA,WAChB,OAAO7D,EAAgB,OAAI,EAAA,WACpBA,OAAAA,GAEd,OADP9I,EAAoBC,EAAEyM,EAAQ,CAAEE,EAAGF,IAC5BA,CACnB,EAM+B1M,EAAAC,EAAI,SAASL,EAASiN,GACzC,IAAA,IAAQrI,KAAOqI,EACX7M,EAAoB0E,EAAEmI,EAAYrI,KAASxE,EAAoB0E,EAAE9E,EAAS4E,IACrEF,OAAAC,eAAe3E,EAAS4E,EAAK,CAAEL,YAAY,EAAM3E,IAAKqN,EAAWrI,IAGtF,EAK+BxE,EAAA0E,EAAI,SAASxB,EAAK4J,GAAQ,OAAOxI,OAAOhB,UAAUyJ,eAAexH,KAAKrC,EAAK4J,EAAQ,EAOjG9M,EAAoB,IACrC,CAv2BiB,GAw2BhB,OACD,CAj3BmBgN,ICPJC,EAACC,IAEN,MAAAC,OAAmF,KAAnED,aAAmC,EAASA,EAAKC,eAAqCD,EAAKC,aAC1G,MAAA,CACHC,YAAA,CAAY1J,EAAMlB,IACP,IAAI6K,SAAQ,CAACC,EAASC,KAEnB,MAAAC,EAAS5M,SAASa,cAAc,UAEhCoH,EAAY,IAAIrC,EAAUgH,EAAQ,CACpC9J,KAAM,IAAMA,EACZD,OAAQ,IAAM,OACdjB,eAAyB,IAAdA,EAA0BA,EAAY5B,SAASkC,OAE9D+F,EAAU8C,GAAG,WAAYnG,IACrBqD,EAAUL,UACV8E,EAAQ9H,EAAC,IAEbqD,EAAU8C,GAAG,SAAUnG,IACnBqD,EAAUL,UACV+E,EAAO/H,EAAC,IAGR2H,GACSvM,SAAAkC,KAAKL,YAAY+K,GAE9BA,EAAOC,QAEHN,GACSvM,SAAAkC,KAAK4K,YAAYF,EAAM,IAGpD,GCzBMJ,YAAEA,GAAgBH,EAAa,CACnCE,cAAc,IAGHQ,EAAStO,MAAOuO,IAC3B,GAAKA,EAGD,IACI,MAAAC,OHJiBxO,OAAOC,UACdC,EAAMC,IAAI,mBAAoB,CAC9CC,OAAQH,KAECI,KGASoO,CAAY,CAC5BF,UAEE,GAAAC,EAAIE,OAASC,EAAUC,GACzB,MAAMJ,EAAIK,IAEZ,MAAMC,SAAEA,EAAAC,WAAUA,EAAYC,UAAAA,GAAcR,EAAInO,KAChD,OAAI2O,OACFC,EAAUC,MAAM,2BAGdH,UACII,EAAaC,QACjB,4BACiBN,EAASvC,wFAEH8C,EAAeN,qCAGtC,KACA,CACEO,kBAAmB,MACnBC,iBAAkB,MAClBC,0BAA0B,EAC1BlO,KAAM,kBAGJmO,EAAKlB,IAENO,EAASY,WACTvJ,GACG8I,EAAAC,MAAM/I,GAAK,QACvB,GAGIsJ,EAAOzP,MAAOuO,IACZ,MAAAC,OHjCgBxO,OAAOC,UACXC,EAAMC,IAAI,eAAgB,CAC1CC,OAAQH,KAECI,KG6BOsP,CAAS,CACzBpB,UAEE,GAAAC,EAAIE,OAASC,EAAUC,GACzB,MAAMJ,EAAIK,IAEZI,EAAUW,QAAQ,SAAQ,EAGfC,EAAc7P,MACzB8P,EACAf,EAAagB,EAAWC,QACxBC,EACAC,KAEA,IAAKJ,EACH,OAEF,GAAIA,EAAKf,aAAegB,EAAWI,OAASL,EAAKf,aAAegB,EAAWK,OAEzE,YADAnB,EAAUC,MAAM,cAGlB,MAAMmB,EAAWC,IACX9B,OHpEkBxO,OAAOC,UACbC,EAAMqQ,KAAK,eAAgBC,EAAGC,UAAUxQ,KAC/CI,KGkEOqQ,CAAW,CAC3BC,SAAUC,EAAeC,IACzB9B,aACA+B,WAAYhB,EAAKJ,MAEf,GAAY,GAAZlB,EAAIE,KAAW,CACX,MAAAqC,EAAW,GAAGnO,OAAOoO,SAASC,OAAShB,EAAM,SAAWzB,EAAInO,KAAKqP,eAClEW,EAAS9D,iBACR2D,EAAWT,KAAK,oBAChBJ,EAAeN,gBACdmC,EAAW1C,EAAInO,KAAK8Q,YAAa,+BAClCpD,EAAYgD,GACL5B,EAAAiC,MACX,wEAEUf,EAAS9D,4BACR2D,EAAWT,KAAK,+BAChBJ,EAAeN,2BACdmC,EAAW1C,EAAInO,KAAK8Q,YAAa,qCAE7C,OACA,CACE3B,0BAA0B,EAC1BF,kBAAmB,MAGzB,GAGc,SAAA+B,EAASC,EAAmB3P,EAAawD,GACvD,IAAA,IAASP,EAAI,EAAGA,EAAI0M,EAAK9N,OAAQoB,IAAK,CAC9B,MAAAqC,EAAUqK,EAAK1M,GACjB,GAAAqC,EAAQ9B,KAASxD,EACZ,OAAAsF,EAET,GAAIA,EAAQsK,SAAU,CACpB,MAAM/C,EAAM6C,EAASpK,EAAQsK,SAAU5P,EAAQwD,GAC/C,GAAIqJ,EACK,OAAAA,CAEX,CACF,CACF", "x_google_ignoreList": [1, 2]}