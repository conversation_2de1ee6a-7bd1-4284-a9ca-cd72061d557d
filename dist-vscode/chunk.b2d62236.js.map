{"version": 3, "file": "chunk.b2d62236.js", "sources": ["../src/utils/date.ts"], "sourcesContent": ["export const formatDate = (date: any = new Date(), rule, utc = false) => {\n  if (date.constructor !== Date) {\n    date = new Date(date);\n  }\n  return rule\n    .replace(/yyyy|Y/g, utc ? date.getUTCFullYear() : date.getFullYear())\n    .replace(/yy|y/g, String(utc ? date.getUTCFullYear() : date.getFullYear()).substr(-2, 2))\n    .replace(/MM/g, leftPad(String((utc ? date.getUTCMonth() : date.getMonth()) + 1), 2, \"0\"))\n    .replace(/M/g, (utc ? date.getUTCMonth() : date.getMonth()) + 1)\n    .replace(/dd/g, leftPad(String(utc ? date.getUTCDate() : date.getDate()), 2, \"0\"))\n    .replace(/d/g, utc ? date.getUTCDate() : date.getDate())\n    .replace(/D/g, utc ? date.getUTCDay() : date.getDay())\n    .replace(/HH|hh/g, leftPad(String(utc ? date.getUTCHours() : date.getHours()), 2, \"0\"))\n    .replace(/H|h/g, utc ? date.getUTCHours() : date.getHours())\n    .replace(/ms/g, utc ? date.getUTCMilliseconds() : date.getMilliseconds())\n    .replace(/mm/g, leftPad(String(utc ? date.getUTCMinutes() : date.getMinutes()), 2, \"0\"))\n    .replace(/m/g, leftPad(String(utc ? date.getUTCMinutes() : date.getMinutes()), 2, \"0\"))\n    .replace(/SS/g, leftPad(String(utc ? date.getUTCSeconds() : date.getSeconds()), 2, \"0\"))\n    .replace(/S/g, utc ? date.getUTCSeconds() : date.getSeconds());\n};\nconst cache = [\"\", \" \", \"  \", \"   \", \"    \", \"     \", \"      \", \"       \", \"        \", \"         \"];\n\nconst leftPad = (str, len, ch) => {\n  str = str + \"\";\n  len = len - str.length;\n  if (len <= 0) return str;\n  if (!ch && ch !== 0) ch = \" \";\n  ch = ch + \"\";\n  if (ch === \" \" && len < 10) return cache[len] + str;\n  let pad = \"\";\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (len & 1) pad += ch;\n    len >>= 1;\n    if (len) ch += ch;\n    else break;\n  }\n  return pad + str;\n};\n\n/**\n * 一个通用的轮询函数\n * @param {Function} task - 要执行的异步任务 (async function)\n * @param {number} interval - 轮询间隔（毫秒）\n * @param {number} [maxAttempts] - 最大轮询次数（可选），不传则无限轮询\n * @return {Object} 返回一个对象 { start, stop }\n *   - start()：开始轮询\n *   - stop()：停止轮询\n */\nexport function createPoller(task, interval, maxAttempts) {\n  let attempts = 0;\n  let timerId: any = null;\n  let stopped = false;\n\n  async function execute() {\n    try {\n      await task(); // 执行具体的轮询逻辑\n    } catch (err) {\n      console.error(\"Polling task error:\", err);\n      // 根据需要处理错误，是否停止轮询？可视具体情况决定\n    }\n\n    attempts++;\n\n    // 如果设置了最大次数，且已达上限，则停止\n    if (maxAttempts && attempts >= maxAttempts) {\n      stop();\n      return;\n    }\n\n    // 如果外部调用了 stop()，则不再继续\n    if (stopped) return;\n\n    // 间隔 interval 毫秒后再次执行\n    timerId = setTimeout(execute, interval);\n  }\n\n  function start() {\n    if (stopped) {\n      // 如果之前被 stop() 过，可以重置状态后重新 start\n      stopped = false;\n      attempts = 0;\n    }\n    execute();\n  }\n\n  function stop() {\n    stopped = true;\n    if (timerId) {\n      clearTimeout(timerId);\n      timerId = null;\n    }\n  }\n\n  return { start, stop };\n}\n"], "names": ["formatDate", "date", "Date", "rule", "utc", "constructor", "replace", "getUTCFullYear", "getFullYear", "String", "substr", "leftPad", "getUTCMonth", "getMonth", "getUTCDate", "getDate", "getUTCDay", "getDay", "getUTCHours", "getHours", "getUTCMilliseconds", "getMilliseconds", "getUTCMinutes", "getMinutes", "getUTCSeconds", "getSeconds", "cache", "str", "len", "ch", "length", "pad", "<PERSON><PERSON><PERSON><PERSON>", "task", "interval", "maxAttempts", "attempts", "timerId", "stopped", "async", "execute", "err", "console", "error", "setTimeout", "stop", "clearTimeout", "start"], "mappings": "AAAa,MAAAA,EAAa,CAACC,EAAY,IAAIC,KAAQC,EAAMC,GAAM,KACzDH,EAAKI,cAAgBH,OAChBD,EAAA,IAAIC,KAAKD,IAEXE,EACJG,QAAQ,UAAWF,EAAMH,EAAKM,iBAAmBN,EAAKO,eACtDF,QAAQ,QAASG,OAAOL,EAAMH,EAAKM,iBAAmBN,EAAKO,eAAeE,UAAW,IACrFJ,QAAQ,MAAOK,EAAQF,QAAQL,EAAMH,EAAKW,cAAgBX,EAAKY,YAAc,GAAI,EAAG,MACpFP,QAAQ,MAAOF,EAAMH,EAAKW,cAAgBX,EAAKY,YAAc,GAC7DP,QAAQ,MAAOK,EAAQF,OAAOL,EAAMH,EAAKa,aAAeb,EAAKc,WAAY,EAAG,MAC5ET,QAAQ,KAAMF,EAAMH,EAAKa,aAAeb,EAAKc,WAC7CT,QAAQ,KAAMF,EAAMH,EAAKe,YAAcf,EAAKgB,UAC5CX,QAAQ,SAAUK,EAAQF,OAAOL,EAAMH,EAAKiB,cAAgBjB,EAAKkB,YAAa,EAAG,MACjFb,QAAQ,OAAQF,EAAMH,EAAKiB,cAAgBjB,EAAKkB,YAChDb,QAAQ,MAAOF,EAAMH,EAAKmB,qBAAuBnB,EAAKoB,mBACtDf,QAAQ,MAAOK,EAAQF,OAAOL,EAAMH,EAAKqB,gBAAkBrB,EAAKsB,cAAe,EAAG,MAClFjB,QAAQ,KAAMK,EAAQF,OAAOL,EAAMH,EAAKqB,gBAAkBrB,EAAKsB,cAAe,EAAG,MACjFjB,QAAQ,MAAOK,EAAQF,OAAOL,EAAMH,EAAKuB,gBAAkBvB,EAAKwB,cAAe,EAAG,MAClFnB,QAAQ,KAAMF,EAAMH,EAAKuB,gBAAkBvB,EAAKwB,eAE/CC,EAAQ,CAAC,GAAI,IAAK,KAAM,MAAO,OAAQ,QAAS,SAAU,UAAW,WAAY,aAEjFf,EAAU,CAACgB,EAAKC,EAAKC,KAGzB,IADAD,IADAD,GAAY,IACIG,SACL,EAAU,OAAAH,EAGjB,GAFCE,GAAa,IAAPA,IAAeA,EAAA,KAEf,OADXA,GAAU,KACQD,EAAM,GAAW,OAAAF,EAAME,GAAOD,EAChD,IAAII,EAAM,GAEV,KACY,EAANH,IAAgBG,GAAAF,GACZD,IAAA,GACOC,GAAAA,EAGjB,OAAOE,EAAMJ,CAAA,EAYC,SAAAK,EAAaC,EAAMC,EAAUC,GAC3C,IAAIC,EAAW,EACXC,EAAe,KACfC,GAAU,EAEdC,eAAeC,IACT,UACIP,UACCQ,GACCC,QAAAC,MAAM,sBAAuBF,EAEvC,CAEAL,IAGID,GAAeC,GAAYD,MAM3BG,IAGMD,EAAAO,WAAWJ,EAASN,GAChC,CAWA,SAASW,IACGP,GAAA,EACND,IACFS,aAAaT,GACHA,EAAA,KAEd,CAEO,MAAA,CAAEU,MAjBT,WACMT,IAEQA,GAAA,EACCF,EAAA,MAGf,EAUgBS,OAClB"}