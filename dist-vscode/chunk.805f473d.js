import{G as a,aH as s}from"./index.05904f40.js";const t=async s=>await a.get("/folder/add",{params:s}),e=async s=>await a.get("/folder/rename",{params:s}),r=async s=>await a.get("/apps/moduleList",{params:s}),i=async s=>await a.get("/generated/g",{params:s}),m=async t=>await a.post("/material/addAll",s.stringify(t));export{i as a,t as b,m as c,e as f,r as m};
//# sourceMappingURL=chunk.805f473d.js.map
