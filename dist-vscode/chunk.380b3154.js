import{h as e,k as o,a8 as n,o as t,Y as l,s as r,c as a,E as i,Q as s,m as d,T as u,v as c,u as p,x as v,y as f}from"./chunk.8df321e8.js";import{g as m,h as g,j as b,E as w,O as h,c as y,k as I,C as E,l as F,m as C,w as k,n as R,F as T,o as _,p as x,L as S}from"./chunk.db8898e3.js";import{E as B,c as D,a as $}from"./chunk.034f7efa.js";import{d as P,r as K,Q as M,z as L,L as G,b1 as O,a_ as z,u as A,w as H,O as j,E as N,o as U,A as W,B as Y,i as J,b2 as Q,b3 as V,k as q,X,R as Z,c as ee,aW as oe,aG as ne,n as te,h as le,b as re,V as ae,q as ie,F as se,f as de}from"./index.05904f40.js";const ue=e({style:{type:o([String,Array,Object])},currentTabId:{type:o(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:o(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:ce,ElCollectionItem:pe,COLLECTION_INJECTION_KEY:ve,COLLECTION_ITEM_INJECTION_KEY:fe}=m("RovingFocusGroup"),me=Symbol("elRovingFocusGroup"),ge=Symbol("elRovingFocusGroupItem"),be={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},we=(e,o,t)=>{const l=((e,o)=>{if("rtl"!==o)return e;switch(e){case n.right:return n.left;case n.left:return n.right;default:return e}})(e.key,t);if(!("vertical"===o&&[n.left,n.right].includes(l)||"horizontal"===o&&[n.up,n.down].includes(l)))return be[l]},he=e=>{const{activeElement:o}=document;for(const n of e){if(n===o)return;if(n.focus(),o!==document.activeElement)return}},ye="currentTabIdChange",Ie="rovingFocusGroup.entryFocus",Ee={bubbles:!1,cancelable:!0},Fe=P({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:ue,emits:[ye,"entryFocus"],setup(e,{emit:o}){var n;const t=K(null!=(n=e.currentTabId||e.defaultCurrentTabId)?n:null),r=K(!1),a=K(!1),i=K(null),{getItems:s}=M(ve,void 0),d=L((()=>[{outline:"none"},e.style])),u=g((o=>{var n;null==(n=e.onMousedown)||n.call(e,o)}),(()=>{a.value=!0})),c=g((o=>{var n;null==(n=e.onFocus)||n.call(e,o)}),(e=>{const o=!A(a),{target:n,currentTarget:l}=e;if(n===l&&o&&!A(r)){const e=new Event(Ie,Ee);if(null==l||l.dispatchEvent(e),!e.defaultPrevented){const e=s().filter((e=>e.focusable)),o=[e.find((e=>e.active)),e.find((e=>e.id===A(t))),...e].filter(Boolean).map((e=>e.ref));he(o)}}a.value=!1})),p=g((o=>{var n;null==(n=e.onBlur)||n.call(e,o)}),(()=>{r.value=!1}));G(me,{currentTabbedId:O(t),loop:z(e,"loop"),tabIndex:L((()=>A(r)?-1:0)),rovingFocusGroupRef:i,rovingFocusGroupRootStyle:d,orientation:z(e,"orientation"),dir:z(e,"dir"),onItemFocus:e=>{o(ye,e)},onItemShiftTab:()=>{r.value=!0},onBlur:p,onFocus:c,onMousedown:u}),H((()=>e.currentTabId),(e=>{t.value=null!=e?e:null})),l(i,Ie,((...e)=>{o("entryFocus",...e)}))}});var Ce=t(P({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:ce,ElRovingFocusGroupImpl:t(Fe,[["render",function(e,o,n,t,l,r){return j(e.$slots,"default")}],["__file","roving-focus-group-impl.vue"]])}}),[["render",function(e,o,n,t,l,r){const a=N("el-roving-focus-group-impl"),i=N("el-focus-group-collection");return U(),W(i,null,{default:Y((()=>[J(a,Q(V(e.$attrs)),{default:Y((()=>[j(e.$slots,"default")])),_:3},16)])),_:3})}],["__file","roving-focus-group.vue"]]);var ke=t(P({components:{ElRovingFocusCollectionItem:pe},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:t,loop:l,onItemFocus:a,onItemShiftTab:i}=M(me,void 0),{getItems:s}=M(ve,void 0),d=r(),u=K(null),c=g((e=>{o("mousedown",e)}),(o=>{e.focusable?a(A(d)):o.preventDefault()})),p=g((e=>{o("focus",e)}),(()=>{a(A(d))})),v=g((e=>{o("keydown",e)}),(e=>{const{key:o,shiftKey:t,target:r,currentTarget:a}=e;if(o===n.tab&&t)return void i();if(r!==a)return;const d=we(e);if(d){e.preventDefault();let o=s().filter((e=>e.focusable)).map((e=>e.ref));switch(d){case"last":o.reverse();break;case"prev":case"next":{"prev"===d&&o.reverse();const e=o.indexOf(a);o=l.value?(c=e+1,(u=o).map(((e,o)=>u[(o+c)%u.length]))):o.slice(e+1);break}}q((()=>{he(o)}))}var u,c})),f=L((()=>t.value===A(d)));return G(ge,{rovingFocusGroupItemRef:u,tabIndex:L((()=>A(f)?0:-1)),handleMousedown:c,handleFocus:p,handleKeydown:v}),{id:d,handleKeydown:v,handleFocus:p,handleMousedown:c}}}),[["render",function(e,o,n,t,l,r){const a=N("el-roving-focus-collection-item");return U(),W(a,{id:e.id,focusable:e.focusable,active:e.active},{default:Y((()=>[j(e.$slots,"default")])),_:3},8,["id","focusable","active"])}],["__file","roving-focus-item.vue"]]);const Re=Symbol("elDropdown"),{ButtonGroup:Te}=a;var _e=t(P({name:"ElDropdown",components:{ElButton:a,ElButtonGroup:Te,ElScrollbar:B,ElDropdownCollection:b,ElTooltip:w,ElRovingFocusGroup:Ce,ElOnlyChild:h,ElIcon:i,ArrowDown:s},props:y,emits:["visible-change","click","command"],setup(e,{emit:o}){const t=X(),l=d("dropdown"),{t:a}=u(),i=K(),s=K(),v=K(null),f=K(null),m=K(null),g=K(null),b=K(!1),w=[n.enter,n.space,n.down],h=L((()=>({maxHeight:c(e.maxHeight)}))),y=L((()=>[l.m(k.value)])),I=L((()=>D(e.trigger))),E=r().value,F=L((()=>e.id||E));function C(){var e;null==(e=v.value)||e.onClose()}H([i,I],(([e,o],[n])=>{var t,l,r;(null==(t=null==n?void 0:n.$el)?void 0:t.removeEventListener)&&n.$el.removeEventListener("pointerenter",R),(null==(l=null==e?void 0:e.$el)?void 0:l.removeEventListener)&&e.$el.removeEventListener("pointerenter",R),(null==(r=null==e?void 0:e.$el)?void 0:r.addEventListener)&&o.includes("hover")&&e.$el.addEventListener("pointerenter",R)}),{immediate:!0}),Z((()=>{var e,o;(null==(o=null==(e=i.value)?void 0:e.$el)?void 0:o.removeEventListener)&&i.value.$el.removeEventListener("pointerenter",R)}));const k=p();function R(){var e,o;null==(o=null==(e=i.value)?void 0:e.$el)||o.focus()}G(Re,{contentRef:f,role:L((()=>e.role)),triggerId:F,isUsingKeyboard:b,onItemEnter:function(){},onItemLeave:function(){const e=A(f);I.value.includes("hover")&&(null==e||e.focus()),g.value=null}}),G("elDropdown",{instance:t,dropdownSize:k,handleClick:function(){C()},commandHandler:function(...e){o("command",...e)},trigger:z(e,"trigger"),hideOnClick:z(e,"hideOnClick")});return{t:a,ns:l,scrollbar:m,wrapStyle:h,dropdownTriggerKls:y,dropdownSize:k,triggerId:F,triggerKeys:w,currentTabId:g,handleCurrentTabIdChange:function(e){g.value=e},handlerMainButtonClick:e=>{o("click",e)},handleEntryFocus:function(e){b.value||(e.preventDefault(),e.stopImmediatePropagation())},handleClose:C,handleOpen:function(){var e;null==(e=v.value)||e.onOpen()},handleBeforeShowTooltip:function(){o("visible-change",!0)},handleShowTooltip:function(e){"keydown"===(null==e?void 0:e.type)&&f.value.focus()},handleBeforeHideTooltip:function(){o("visible-change",!1)},onFocusAfterTrapped:e=>{var o,n;e.preventDefault(),null==(n=null==(o=f.value)?void 0:o.focus)||n.call(o,{preventScroll:!0})},popperRef:v,contentRef:f,triggeringElementRef:i,referenceElementRef:s}}}),[["render",function(e,o,n,t,l,r){var a;const i=N("el-dropdown-collection"),s=N("el-roving-focus-group"),d=N("el-scrollbar"),u=N("el-only-child"),c=N("el-tooltip"),p=N("el-button"),v=N("arrow-down"),f=N("el-icon"),m=N("el-button-group");return U(),ee("div",{class:te([e.ns.b(),e.ns.is("disabled",e.disabled)])},[J(c,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":"hover"===e.trigger?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":null==(a=e.referenceElementRef)?void 0:a.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":"hover"===e.trigger?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},oe({content:Y((()=>[J(d,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:Y((()=>[J(s,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:Y((()=>[J(i,null,{default:Y((()=>[j(e.$slots,"dropdown")])),_:3})])),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])])),_:3},8,["wrap-style","view-class"])])),_:2},[e.splitButton?void 0:{name:"default",fn:Y((()=>[J(u,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:Y((()=>[j(e.$slots,"default")])),_:3},8,["id","tabindex"])]))}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(U(),W(m,{key:0},{default:Y((()=>[J(p,ne({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:Y((()=>[j(e.$slots,"default")])),_:3},16,["size","type","disabled","tabindex","onClick"]),J(p,ne({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:Y((()=>[J(f,{class:te(e.ns.e("icon"))},{default:Y((()=>[J(v)])),_:1},8,["class"])])),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])])),_:3})):le("v-if",!0)],2)}],["__file","dropdown.vue"]]);const xe=P({name:"DropdownItemImpl",components:{ElIcon:i},props:I,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const t=d("dropdown"),{role:l}=M(Re,void 0),{collectionItemRef:r}=M(E,void 0),{collectionItemRef:a}=M(fe,void 0),{rovingFocusGroupItemRef:i,tabIndex:s,handleFocus:u,handleKeydown:c,handleMousedown:p}=M(ge,void 0),v=$(r,a,i),f=L((()=>"menu"===l.value?"menuitem":"navigation"===l.value?"link":"button")),m=g((e=>{const{code:t}=e;if(t===n.enter||t===n.space)return e.preventDefault(),e.stopImmediatePropagation(),o("clickimpl",e),!0}),c);return{ns:t,itemRef:v,dataset:{[F]:""},role:f,tabIndex:s,handleFocus:u,handleKeydown:m,handleMousedown:p}}}),Se=["aria-disabled","tabindex","role"];const Be=()=>{const e=M("elDropdown",{}),o=L((()=>null==e?void 0:e.dropdownSize));return{elDropdown:e,_elDropdownSize:o}};var De=t(P({name:"ElDropdownItem",components:{ElDropdownCollectionItem:C,ElRovingFocusItem:ke,ElDropdownItemImpl:t(xe,[["render",function(e,o,n,t,l,r){const a=N("el-icon");return U(),ee(se,null,[e.divided?(U(),ee("li",ne({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):le("v-if",!0),re("li",ne({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o[0]||(o[0]=o=>e.$emit("clickimpl",o)),onFocus:o[1]||(o[1]=(...o)=>e.handleFocus&&e.handleFocus(...o)),onKeydown:o[2]||(o[2]=ie(((...o)=>e.handleKeydown&&e.handleKeydown(...o)),["self"])),onMousedown:o[3]||(o[3]=(...o)=>e.handleMousedown&&e.handleMousedown(...o)),onPointermove:o[4]||(o[4]=o=>e.$emit("pointermove",o)),onPointerleave:o[5]||(o[5]=o=>e.$emit("pointerleave",o))}),[e.icon?(U(),W(a,{key:0},{default:Y((()=>[(U(),W(ae(e.icon)))])),_:1})):le("v-if",!0),j(e.$slots,"default")],16,Se)],64)}],["__file","dropdown-item-impl.vue"]])},inheritAttrs:!1,props:I,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:n}){const{elDropdown:t}=Be(),l=X(),r=K(null),a=L((()=>{var e,o;return null!=(o=null==(e=A(r))?void 0:e.textContent)?o:""})),{onItemEnter:i,onItemLeave:s}=M(Re,void 0),d=g((e=>(o("pointermove",e),e.defaultPrevented)),k((o=>{if(e.disabled)return void s(o);const n=o.currentTarget;n===document.activeElement||n.contains(document.activeElement)||(i(o),o.defaultPrevented||null==n||n.focus())}))),u=g((e=>(o("pointerleave",e),e.defaultPrevented)),k((e=>{s(e)})));return{handleClick:g((n=>{if(!e.disabled)return o("click",n),"keydown"!==n.type&&n.defaultPrevented}),(o=>{var n,r,a;e.disabled?o.stopImmediatePropagation():((null==(n=null==t?void 0:t.hideOnClick)?void 0:n.value)&&(null==(r=t.handleClick)||r.call(t)),null==(a=t.commandHandler)||a.call(t,e.command,l,o))})),handlePointerMove:d,handlePointerLeave:u,textContent:a,propsAndAttrs:L((()=>({...e,...n})))}}}),[["render",function(e,o,n,t,l,r){var a;const i=N("el-dropdown-item-impl"),s=N("el-roving-focus-item"),d=N("el-dropdown-collection-item");return U(),W(d,{disabled:e.disabled,"text-value":null!=(a=e.textValue)?a:e.textContent},{default:Y((()=>[J(s,{focusable:!e.disabled},{default:Y((()=>[J(i,ne(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:Y((()=>[j(e.$slots,"default")])),_:3},16,["onPointerleave","onPointermove","onClickimpl"])])),_:3},8,["focusable"])])),_:3},8,["disabled","text-value"])}],["__file","dropdown-item.vue"]]);const $e=P({name:"ElDropdownMenu",props:R,setup(e){const o=d("dropdown"),{_elDropdownSize:t}=Be(),l=t.value,{focusTrapRef:r,onKeydown:a}=M(T,void 0),{contentRef:i,role:s,triggerId:u}=M(Re,void 0),{collectionRef:c,getItems:p}=M(_,void 0),{rovingFocusGroupRef:v,rovingFocusGroupRootStyle:f,tabIndex:m,onBlur:b,onFocus:w,onMousedown:h}=M(me,void 0),{collectionRef:y}=M(ve,void 0),I=L((()=>[o.b("menu"),o.bm("menu",null==l?void 0:l.value)])),E=$(i,c,r,v,y),F=g((o=>{var n;null==(n=e.onKeydown)||n.call(e,o)}),(e=>{const{currentTarget:o,code:t,target:l}=e;if(o.contains(l),n.tab===t&&e.stopImmediatePropagation(),e.preventDefault(),l!==A(i))return;if(!x.includes(t))return;const r=p().filter((e=>!e.disabled)).map((e=>e.ref));S.includes(t)&&r.reverse(),he(r)}));return{size:l,rovingFocusGroupRootStyle:f,tabIndex:m,dropdownKls:I,role:s,triggerId:u,dropdownListWrapperRef:E,handleKeydown:e=>{F(e),a(e)},onBlur:b,onFocus:w,onMousedown:h}}}),Pe=["role","aria-labelledby"];var Ke=t($e,[["render",function(e,o,n,t,l,r){return U(),ee("ul",{ref:e.dropdownListWrapperRef,class:te(e.dropdownKls),style:de(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:o[0]||(o[0]=(...o)=>e.onBlur&&e.onBlur(...o)),onFocus:o[1]||(o[1]=(...o)=>e.onFocus&&e.onFocus(...o)),onKeydown:o[2]||(o[2]=ie(((...o)=>e.handleKeydown&&e.handleKeydown(...o)),["self"])),onMousedown:o[3]||(o[3]=ie(((...o)=>e.onMousedown&&e.onMousedown(...o)),["self"]))},[j(e.$slots,"default")],46,Pe)}],["__file","dropdown-menu.vue"]]);const Me=v(_e,{DropdownItem:De,DropdownMenu:Ke}),Le=f(De),Ge=f(Ke);export{Le as E,Ge as a,Me as b};
//# sourceMappingURL=chunk.380b3154.js.map
