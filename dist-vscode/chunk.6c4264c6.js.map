{"version": 3, "file": "chunk.6c4264c6.js", "sources": ["../node_modules/lodash-es/throttle.js", "../src/views/layouts/components/spScreenTabs.vue", "../src/views/layouts/home/<USER>/homeBodyHeader.vue", "../src/views/layouts/home/<USER>/templateCard.vue", "../node_modules/@vueuse/core/node_modules/@vueuse/shared/index.mjs", "../node_modules/@vueuse/core/index.mjs", "../src/views/layouts/home/<USER>/lazyImage.vue", "../src/views/layouts/components/image-view.vue", "../src/views/layouts/home/<USER>/index.vue", "../src/api/admin/index.ts"], "sourcesContent": ["import debounce from './debounce.js';\nimport isObject from './isObject.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [<PERSON>'s article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\nexport default throttle;\n", "<template>\n  <div\n    :class=\"{\n      'sp-screen-tabs': true,\n      'sp-screen-tabs-theme__active': store.themeShow\n    }\"\n    id=\"tabsContainer\"\n    :style=\"{ justifyContent: !isToggle ? 'center' : 'flex-start' }\"\n    @scroll=\"scroll\"\n  >\n    <div\n      v-for=\"item in data\"\n      :key=\"item._id\"\n      :class=\"{\n        'sp-screen-tabs__item': true,\n        'sp-screen-tabs-item__active': selectedId.includes(item._id)\n      }\"\n      @click=\"screenChange(item)\"\n    >\n      <span>{{ item.name }}</span>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { materialStore, themeStore } from \"@/store\";\nimport { defineEmits, ref, watch } from \"vue\";\nconst store = themeStore();\nconst material = materialStore();\n\nlet emits = defineEmits([\"updateScrollWidth\"]);\n\nlet props = defineProps({\n  isToggle: {\n    type: Boolean,\n    default: false\n  },\n  data: {\n    type: Array as any,\n    default: () => []\n  },\n  materialType: {\n    type: String,\n    default: \"version\"\n  }\n});\n\nconst selectedId = ref<string[]>([]); // 已选ID集合\n\nconst screenChange = (item: any) => {\n  if (selectedId.value.includes(item._id)) {\n    selectedId.value = selectedId.value.filter((e: string) => e !== item._id);\n  } else {\n    selectedId.value.push(item._id);\n  }\n  console.log(selectedId.value, \"valueIDS\");\n  if (props.materialType === \"version\") {\n    material.updateVersionInfo(selectedId.value);\n  } else {\n    material.updateTagsInfo(selectedId.value);\n  }\n};\n\nconst scroll = (event: any) => {\n  emits(\"updateScrollWidth\", event.target.scrollLeft);\n};\n\nwatch(\n  () => material.ids,\n  (n: string[]) => {\n    selectedId.value = n;\n  }\n);\n</script>\n<style lang=\"less\" scoped>\n.sp-screen-tabs {\n  flex: 1;\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: flex-start;\n  overflow-y: hidden;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scroll-snap-type: x proximity;\n  scrollbar-width: none;\n  &.sp-screen-tabs-theme__active {\n    .sp-screen-tabs__item {\n      background: #262626;\n      color: #ffffff;\n    }\n  }\n  &::-webkit-scrollbar {\n    display: none;\n  }\n  .sp-screen-tabs__item {\n    width: 85px;\n    height: 30px;\n    line-height: 30px;\n    text-align: center;\n    font-size: 14px;\n    color: #303233;\n    background: #f0f0f0;\n    border-radius: 20px;\n    margin-right: 10px;\n    cursor: pointer;\n    flex-shrink: 0;\n    &.sp-screen-tabs-item__active {\n      background: #5c54f0;\n      color: #ffffff;\n    }\n  }\n}\n</style>\n", "<template>\r\n  <div\r\n    :class=\"{\r\n      'home-body-right__header': true,\r\n      'home-body-right-header__active': store.themeShow\r\n    }\"\r\n    id=\"container\"\r\n  >\r\n    <div class=\"home-body-right-header__one\" v-if=\"appOrDesign === 1 || appOrDesign === 0\">\r\n      <div class=\"swiper-list__tool swiper-list__tool--previous\" v-if=\"isToggle\" @click=\"preChange\">\r\n        <div class=\"swiper-list-tool__bg\"></div>\r\n        <span class=\"previous-icon\">\r\n          <i class=\"iconfont icon-zuojiantou\"></i>\r\n        </span>\r\n      </div>\r\n      <div class=\"home-body-right-scroll__tabs\">\r\n        <sp-screen-tabs :is-toggle=\"isToggle\" :data=\"appTabsData\" @update-scroll-width=\"updateScrollWidth\" :material-type=\"'module'\"></sp-screen-tabs>\r\n      </div>\r\n      <div class=\"swiper-list__tool swiper-list__tool--next\" v-if=\"isToggle\" @click=\"nextChange\">\r\n        <div class=\"swiper-list-tool__bg\"></div>\r\n        <span class=\"previous-icon\">\r\n          <i class=\"iconfont icon-zuojiantou\"></i>\r\n        </span>\r\n      </div>\r\n    </div>\r\n    <div class=\"home-body-right-header__more\" v-else>\r\n      <div class=\"more-list__item\">\r\n        <label>版本筛选：</label>\r\n        <sp-screen-tabs :is-toggle=\"true\" :data=\"versionTabsData\" :material-type=\"'version'\"></sp-screen-tabs>\r\n      </div>\r\n      <div class=\"more-list__item\">\r\n        <label>功能筛选：</label>\r\n        <sp-screen-tabs :is-toggle=\"true\" :data=\"appTabsData\" :material-type=\"'module'\"></sp-screen-tabs>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { versionList } from \"@/api/common\";\r\nimport { moduleList } from \"@/api/upload\";\r\nimport { themeStore } from \"@/store\";\r\nimport SpScreenTabs from \"@/views/layouts/components/spScreenTabs.vue\";\r\nimport { defineProps, onMounted, ref, watch, nextTick } from \"vue\";\r\nimport { materialStore } from \"@/store\";\r\n\r\ndefineProps({\r\n  appOrDesign: {\r\n    type: Number,\r\n    default: 1\r\n  }\r\n});\r\n\r\nconst store = themeStore();\r\nconst materialStoreData = materialStore();\r\n\r\nconst isToggle = ref<boolean>(false); // 是否展示左右切换导航\r\n\r\nconst container = ref<any>(0); // 总宽度\r\nconst clientWidth = ref<any>(0); // 容器宽度\r\nconst scrollWidth = ref<any>(0); // 容器滚动宽度\r\nconst listItemW = ref<any>(0); // 内容宽度\r\nconst scrollListItemW = ref<any>(0); // 内容滚动宽度\r\nconst underWayScrollWidth = ref<number>(0); // 正在滚动的距离默认值为零\r\nconst appTabsData = ref<any[]>([]);\r\nconst versionTabsData = ref<any[]>([]);\r\n\r\n/*\r\n * @description 点击左侧按钮实现Tab平缓向左滑动\r\n * */\r\nconst preChange = () => {\r\n  const header: any = document.getElementById(\"tabsContainer\");\r\n  if (+header.scrollLeft > 0) {\r\n    underWayScrollWidth.value -= 400;\r\n    header.scrollLeft = underWayScrollWidth.value;\r\n  }\r\n};\r\n\r\n/*\r\n * @description 点击右侧按钮实现Tab平缓向右滑动\r\n * */\r\nconst nextChange = () => {\r\n  const design: any = document.getElementById(\"tabsContainer\");\r\n  if (+design.scrollLeft < +scrollWidth.value) {\r\n    // 每次滚动增加固定距离\r\n    underWayScrollWidth.value += 400;\r\n    // 给当前的Element增加滚动距离\r\n    design.scrollLeft = underWayScrollWidth.value;\r\n  }\r\n};\r\n\r\n/*\r\n * @description 获取自滚动距离，用来实现主动触发的时候\r\n * 计算需要平滑滚动的距离\r\n */\r\nconst updateScrollWidth = (distance: number) => {\r\n  underWayScrollWidth.value = distance;\r\n};\r\n\r\n/*\r\n * @description 获取标签,用来实现header标签筛选\r\n * */\r\nconst getModuleList = async () => {\r\n  const data: any = await moduleList({});\r\n  if (data.data.code === 0 && data.data.data.length > 0) {\r\n    appTabsData.value = data.data.data;\r\n    container.value = document.getElementById(\"container\")?.clientWidth;\r\n    clientWidth.value = document.getElementById(\"tabsContainer\")?.clientWidth;\r\n    await nextTick();\r\n    let cur: any = document.querySelectorAll(\".sp-screen-tabs__item\");\r\n    scrollWidth.value = cur[0].offsetWidth * data.data.data.length + data.data.data.length * 10;\r\n    listItemW.value = document.getElementById(\"tabsContainer\")?.clientWidth;\r\n    scrollListItemW.value = document.getElementById(\"tabsContainer\")?.scrollWidth;\r\n    if (container.value - 20 < +scrollWidth.value && +clientWidth.value <= +scrollWidth.value) {\r\n      isToggle.value = true;\r\n    } else {\r\n      isToggle.value = false;\r\n    }\r\n  } else {\r\n    appTabsData.value = [];\r\n  }\r\n};\r\n\r\n/*\r\n * @description 获取版本号,用来实现header版本号的筛选\r\n * */\r\nconst getVersionList = async (_id: string) => {\r\n  const data: any = await versionList({ appId: _id });\r\n  if (data.data.code === 0) {\r\n    versionTabsData.value = data.data.data;\r\n  } else {\r\n    versionTabsData.value = [];\r\n  }\r\n};\r\n\r\n/*\r\n * 数据初始化\r\n * */\r\nconst initData = () => {\r\n  getModuleList();\r\n  getVersionList(\"\");\r\n};\r\n\r\nwatch(\r\n  () => materialStoreData.appId,\r\n  (newId: string) => {\r\n    getVersionList(newId);\r\n  }\r\n);\r\n\r\nonMounted(() => {\r\n  initData();\r\n});\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.home-body-right__header {\r\n  width: 100%;\r\n  padding: 15px 20px;\r\n  box-sizing: border-box;\r\n  background: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .home-body-right-header__one {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  .home-body-right-header__more {\r\n    display: flex;\r\n    flex-direction: column;\r\n    .more-list__item {\r\n      width: 100%;\r\n      height: 30px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: flex-start;\r\n      margin-bottom: 12px;\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n      label {\r\n        font-size: 14px;\r\n        color: #303233;\r\n      }\r\n    }\r\n  }\r\n\r\n  .swiper-list__tool {\r\n    position: relative;\r\n    width: 40px;\r\n    height: 40px;\r\n    margin-right: 10px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #5c54f0;\r\n    cursor: pointer;\r\n\r\n    .swiper-list-tool__bg {\r\n      opacity: 0.1;\r\n      background: #5c54f0;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n      z-index: 0;\r\n    }\r\n\r\n    &.swiper-list__tool--next {\r\n      transform: rotateY(180deg);\r\n      margin-left: 10px;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .home-body-right-scroll__tabs {\r\n    width: 100%;\r\n    overflow: hidden;\r\n  }\r\n  &.home-body-right-header__active {\r\n    background-color: #303233;\r\n  }\r\n}\r\n</style>\r\n", "<template>\r\n  <div class=\"ai-home-template-card\" ref=\"templateCard\" @click=\"handleClick\">\r\n    <div :class=\"`template-card-img-wrap ${needCenter ? 'need-center' : ''}`\" :style=\"`${templateInfo.type === 'imgWaterfall' ? 'height:' + ((templateInfo.size.h * width) / templateInfo.size.w ? (templateInfo.size.h * width) / templateInfo.size.w : 200) + 'px;background:' + background + ';' : ''}`\">\r\n      <!-- 收藏&下载按钮 start -->\r\n      <div class=\"group__btn_start\">\r\n        <div class=\"group-btn-start__item\" @click.stop=\"collectionChange(templateInfo)\" v-if=\"userInfo.syUid !== templateInfo.userId\">\r\n          <img loading=\"lazy\" :src=\"isCollection ? 'https://static.soyoung.com/sy-pre/23ust432yoq4s-1697804341094.png' : 'https://static.soyoung.com/sy-pre/1nubvuocuun6k-1697804341094.png'\" alt=\"\" />\r\n        </div>\r\n        <div class=\"group-btn-start__item\" @click.stop=\"download(templateInfo)\">\r\n          <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697804341094.png\" alt=\"\" />\r\n        </div>\r\n      </div>\r\n      <!-- 收藏&下载按钮 end -->\r\n      <img loading=\"lazy\" class=\"template-card-cover-img\" :src=\"templateInfo.src && templateInfo.src.indexOf('?') < 0 ? `${templateInfo.src}?imageView2/0/format/webp` : templateInfo.src\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted, defineEmits } from \"vue\";\r\nimport { collect } from \"@/api/common\";\r\nimport { downlondImage } from \"@/utils/downloadImage\";\r\nimport { userInfoStore } from \"@/store\";\r\nimport { ElNotification } from \"element-plus\";\r\nconst userInfo = userInfoStore();\r\ndefineProps({\r\n  templateInfo: {\r\n    type: Object as any,\r\n    default: () => {\r\n      return {\r\n        key: \"1111111\",\r\n        src: \"https://static.soyoung.com/sy-pre/8d15af2b60d957d86961c0fbaa5b0cb7-1637511000713.jpeg\",\r\n        name: \"name\",\r\n        collection: false,\r\n        designCategory: { _id: \"\", name: \"\" },\r\n        size: { w: \"\", h: \"\" },\r\n        type: \"\",\r\n        styleType: \"girl\"\r\n      };\r\n    }\r\n  },\r\n  params: {\r\n    type: Object,\r\n    default: () => {\r\n      return {};\r\n    }\r\n  },\r\n  noBorder: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  needCenter: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  designType: {\r\n    type: String,\r\n    default: \"\"\r\n  },\r\n  maxHeight: {\r\n    type: Number,\r\n    default: 0\r\n  }\r\n});\r\n\r\nconst emit = defineEmits([\"preview\"]);\r\nconst templateCard = ref<any>(null);\r\n\r\n// 收藏\r\nconst isCollection = ref<boolean>(false);\r\nconst collectionChange = async (item: any) => {\r\n  const data = await collect({\r\n    userId: item.userId,\r\n    id: item.key,\r\n    isCollect: 1\r\n  });\r\n  if (data.data.code === 0) {\r\n    isCollection.value = true;\r\n    ElNotification({\r\n      type: \"success\",\r\n      message: \"收藏成功\",\r\n      duration: 3000\r\n    });\r\n  } else {\r\n    ElNotification({\r\n      type: \"error\",\r\n      message: data.data.msg,\r\n      duration: 3000\r\n    });\r\n  }\r\n};\r\n\r\n// 下载\r\nconst download = (item: any) => {\r\n  console.log(item, \"下载\");\r\n  downlondImage(item);\r\n};\r\nlet width = ref(0);\r\nconst bgArr = ref([\"#F0F7FE\", \"#FEF0F0\", \"#FEFBF0\", \"#EDF6E7\", \"#F0EAF7\"]);\r\nlet background = ref(\"\");\r\n\r\nonMounted(() => {\r\n  width.value = templateCard.value.offsetWidth;\r\n  background.value = bgArr.value[Math.floor(Math.random() * 5)];\r\n  window.addEventListener(\"resize\", () => {\r\n    templateCard.value && (width.value = templateCard.value.offsetWidth);\r\n  });\r\n});\r\n\r\nconst handleClick = () => {\r\n  emit(\"preview\");\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ai-home-template-card {\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  border-radius: 8px;\r\n  transition: all 0.5s ease;\r\n  z-index: 10;\r\n  &:hover .group__btn_start {\r\n    display: flex;\r\n    transition: all 0.5s ease;\r\n  }\r\n  .group__btn_start {\r\n    width: 100%;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    position: absolute;\r\n    bottom: 10px;\r\n    right: 10px;\r\n    display: none;\r\n    transition: all 0.5s ease;\r\n    .group-btn-start__item {\r\n      margin-right: 10px;\r\n      &:last-child {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n    img {\r\n      width: 22px;\r\n      height: 22px;\r\n    }\r\n  }\r\n  &:hover {\r\n    transform: scale(0.99);\r\n    transition:\r\n      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.2s;\r\n  }\r\n  &:hover {\r\n    border: 1px solid #675efc;\r\n    box-shadow: 0 2px 4px 0 rgba(34, 31, 84, 0.2);\r\n    box-sizing: border-box;\r\n  }\r\n  &:hover .ai-home-template-card__btn {\r\n    z-index: 2;\r\n    opacity: 1;\r\n    transition:\r\n      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.3s;\r\n  }\r\n  &:hover .ai-home-template-card__danger {\r\n    z-index: 2;\r\n    opacity: 1;\r\n    transition:\r\n      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.3s;\r\n  }\r\n  &:hover &__avatar {\r\n    z-index: 2;\r\n    opacity: 1;\r\n    transition:\r\n      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.3s;\r\n  }\r\n  &__avatar {\r\n    width: 80%;\r\n    position: absolute;\r\n    top: 10px;\r\n    left: 10px;\r\n    box-sizing: border-box;\r\n    transition:\r\n      all 300ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.3s;\r\n    z-index: 10;\r\n    opacity: 0;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    img {\r\n      width: 30px;\r\n      height: 30px;\r\n      border-radius: 50%;\r\n      border: 1px solid #ffffff;\r\n    }\r\n    &-text {\r\n      color: #ffffff;\r\n      font-size: 12px;\r\n      width: 60%;\r\n      margin-left: 10px;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n  .template-card-img-wrap {\r\n    position: relative;\r\n    box-sizing: border-box;\r\n    transition:\r\n      all 30000ms cubic-bezier(0, 0, 1, 1) 0ms,\r\n      0.3s;\r\n    border-radius: 5px;\r\n    overflow: hidden;\r\n    width: 100%;\r\n    background: transparent;\r\n    &.need-center {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n  .template-title {\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 8px;\r\n    font-family: PingFangSC-Regular;\r\n    font-size: 14px;\r\n    color: #303233;\r\n    letter-spacing: 0;\r\n    font-weight: 400;\r\n    line-height: 20px;\r\n    padding: 8px 0 0;\r\n    text-align: left;\r\n    overflow: hidden;\r\n    -o-text-overflow: ellipsis;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 1;\r\n    line-clamp: 1;\r\n    -webkit-box-orient: vertical;\r\n    &.hover {\r\n      display: none;\r\n    }\r\n  }\r\n  .temp__sec__imgUp {\r\n    position: absolute;\r\n    top: 0;\r\n    bottom: 50%;\r\n    left: 0;\r\n    right: 0;\r\n    // background: rgba(0, 0, 0, 0.3);\r\n    z-index: 2;\r\n  }\r\n  .temp__sec__imgDown {\r\n    position: absolute;\r\n    top: 50%;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    // background: rgba(0, 0, 0, 0.6);\r\n    z-index: 2;\r\n  }\r\n  &:hover .template-card-cover-img {\r\n    //transform: scale(0.8);\r\n    //transition: all 300ms cubic-bezier(0,0,1,1) 0ms, .2s;\r\n  }\r\n  .transparent-cover {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    cursor: pointer;\r\n    z-index: 1;\r\n  }\r\n  &.no-border {\r\n    border: none;\r\n    border-radius: 0;\r\n    .template-card-cover-img {\r\n      // width: 100%;\r\n      // height: 100% !important;\r\n      // object-fit: cover;\r\n    }\r\n    .el-image__inner {\r\n      width: 100%;\r\n      height: 100% !important;\r\n      object-fit: cover;\r\n    }\r\n  }\r\n  &:hover {\r\n    .template-card-preview-tools,\r\n    .tools-use-btn {\r\n      display: block;\r\n    }\r\n  }\r\n  .template-card-preview-tools {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 10px;\r\n    line-height: 30px;\r\n    z-index: 2;\r\n    display: none;\r\n  }\r\n  .tools-use-btn {\r\n    position: absolute;\r\n    left: 5px;\r\n    top: 5px;\r\n    z-index: 2;\r\n    font-size: 12px;\r\n    height: 30px;\r\n    padding: 7px 15px;\r\n    display: none;\r\n  }\r\n  .tools-icon-btn {\r\n    width: 30px;\r\n    height: 30px;\r\n    vertical-align: middle;\r\n    color: #fff;\r\n    background: rgba(0, 0, 0, 0.3);\r\n    display: inline-block;\r\n    line-height: 30px;\r\n    border-radius: 4px;\r\n    margin-right: 5px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    &:last-child {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .icon-shoucang {\r\n    color: #c0c2cc;\r\n    &:hover {\r\n      color: #f4f5fa;\r\n    }\r\n    &.active {\r\n      color: #f33155;\r\n      &:hover {\r\n        color: #fd7890;\r\n      }\r\n    }\r\n  }\r\n\r\n  .template-card-cover {\r\n    width: 100%;\r\n    vertical-align: middle;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  .el-image__inner {\r\n    height: auto !important;\r\n  }\r\n\r\n  .template-card-cover-img {\r\n    width: 100%;\r\n    height: 100%;\r\n    //object-fit: cover;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n    transition: transform 0.2s ease;\r\n  }\r\n}\r\n</style>\r\n", "import { shallowRef, watchEffect, readonly, ref, unref, isVue3, watch, customRef, effectScope, provide, inject, getCurrentScope, onScopeDispose, isRef, computed, reactive, toRefs as toRefs$1, toRef, isVue2, set as set$1, getCurrentInstance, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue-demi';\n\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, __spreadProps$6(__spreadValues$9({}, options), {\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  }));\n  return readonly(result);\n}\n\nvar _a;\nconst isClient = typeof window !== \"undefined\";\nconst isDef = (val) => typeof val !== \"undefined\";\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isBoolean = (val) => typeof val === \"boolean\";\nconst isFunction = (val) => typeof val === \"function\";\nconst isNumber = (val) => typeof val === \"number\";\nconst isString = (val) => typeof val === \"string\";\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst isWindow = (val) => typeof window !== \"undefined\" && toString.call(val) === \"[object Window]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst isIOS = isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    filter(() => fn.apply(this, args), { fn, thisArg: this, args });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  const filter = (invoke) => {\n    const duration = unref(ms);\n    const maxDuration = unref(options.maxWait);\n    if (timer)\n      clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return invoke();\n    }\n    if (maxDuration && !maxTimer) {\n      maxTimer = setTimeout(() => {\n        if (timer)\n          clearTimeout(timer);\n        maxTimer = null;\n        invoke();\n      }, maxDuration);\n    }\n    timer = setTimeout(() => {\n      if (maxTimer)\n        clearTimeout(maxTimer);\n      maxTimer = null;\n      invoke();\n    }, duration);\n  };\n  return filter;\n}\nfunction throttleFilter(ms, trailing = true, leading = true) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n  };\n  const filter = (invoke) => {\n    const duration = unref(ms);\n    const elapsed = Date.now() - lastExec;\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      timer = setTimeout(() => {\n        lastExec = Date.now();\n        isLeading = true;\n        clear();\n        invoke();\n      }, duration);\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter) {\n  const isActive = ref(true);\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive, pause, resume, eventFilter };\n}\n\nfunction __onlyVue3(name = \"this function\") {\n  if (isVue3)\n    return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 3.`);\n}\nconst directiveHooks = {\n  mounted: isVue3 ? \"mounted\" : \"inserted\",\n  updated: isVue3 ? \"updated\" : \"componentUpdated\",\n  unmounted: isVue3 ? \"unmounted\" : \"unbind\"\n};\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?[0-9]+\\.?[0-9]*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = ref(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = isFunction(fn) ? fn : fn.get;\n  const set = isFunction(fn) ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get();\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction createEventHook() {\n  const fns = [];\n  const off = (fn) => {\n    const index = fns.indexOf(fn);\n    if (index !== -1)\n      fns.splice(index, 1);\n  };\n  const on = (fn) => {\n    fns.push(fn);\n    return {\n      off: () => off(fn)\n    };\n  };\n  const trigger = (param) => {\n    fns.forEach((fn) => fn(param));\n  };\n  return {\n    on,\n    off,\n    trigger\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return () => {\n    if (!initialized) {\n      state = scope.run(stateFactory);\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nfunction createInjectionState(composable) {\n  const key = Symbol(\"InjectionState\");\n  const useProvidingState = (...args) => {\n    provide(key, composable(...args));\n  };\n  const useInjectedState = () => inject(key);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!state) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  __onlyVue3();\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nfunction logicAnd(...args) {\n  return computed(() => args.every((i) => unref(i)));\n}\n\nfunction logicNot(v) {\n  return computed(() => !unref(v));\n}\n\nfunction logicOr(...args) {\n  return computed(() => args.some((i) => unref(i)));\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = __spreadValues$8({}, obj);\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn) {\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unref(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(keys.map((key) => {\n    const value = obj[key];\n    return [\n      key,\n      typeof value === \"function\" ? reactify(value.bind(obj)) : value\n    ];\n  }));\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactiveComputed(() => Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactive(Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = defaultValue;\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = defaultValue;\n      trigger();\n    }, unref(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(debounceFilter(ms, options), fn);\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  if (ms <= 0)\n    return value;\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = true, leading = true) {\n  return createFilterWrapper(throttleFilter(ms, trailing, leading), fn);\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(ref, {\n    get,\n    set,\n    untrackedGet,\n    silentSet,\n    peek,\n    lay\n  }, { enumerable: true });\n}\nconst controlledRef = refWithControl;\n\nfunction resolveRef(r) {\n  return typeof r === \"function\" ? computed(r) : ref(r);\n}\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    if (isVue2) {\n      set$1(...args);\n    } else {\n      const [target, key, value] = args;\n      target[key] = value;\n    }\n  }\n}\n\nfunction syncRef(left, right, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\"\n  } = options;\n  let stop1, stop2;\n  if (direction === \"both\" || direction === \"ltr\") {\n    stop1 = watch(left, (newValue) => right.value = newValue, { flush, deep, immediate });\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    stop2 = watch(right, (newValue) => left.value = newValue, { flush, deep, immediate });\n  }\n  return () => {\n    stop1 == null ? void 0 : stop1();\n    stop2 == null ? void 0 : stop2();\n  };\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  if (!Array.isArray(targets))\n    targets = [targets];\n  return watch(source, (newValue) => targets.forEach((target) => target.value = newValue), { flush, deep, immediate });\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction toRefs(objectRef) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? new Array(objectRef.value.length) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        if (Array.isArray(objectRef.value)) {\n          const copy = [...objectRef.value];\n          copy[key] = v;\n          objectRef.value = copy;\n        } else {\n          const newObject = __spreadProps$5(__spreadValues$7({}, objectRef.value), { [key]: v });\n          Object.setPrototypeOf(newObject, objectRef.value);\n          objectRef.value = newObject;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nfunction tryOnBeforeMount(fn, sync = true) {\n  if (getCurrentInstance())\n    onBeforeMount(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn) {\n  if (getCurrentInstance())\n    onBeforeUnmount(fn);\n}\n\nfunction tryOnMounted(fn, sync = true) {\n  if (getCurrentInstance())\n    onMounted(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn) {\n  if (getCurrentInstance())\n    onUnmounted(fn);\n}\n\nfunction until(r) {\n  let isNot = false;\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(r, (v) => {\n        if (condition(v) !== isNot) {\n          stop == null ? void 0 : stop();\n          resolve(v);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => unref(r)).finally(() => stop == null ? void 0 : stop()));\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch([r, value], ([v1, v2]) => {\n        if (isNot !== (v1 === v2)) {\n          stop == null ? void 0 : stop();\n          resolve(v1);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => unref(r)).finally(() => {\n        stop == null ? void 0 : stop();\n        return unref(r);\n      }));\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(unref(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(unref(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        isNot = !isNot;\n        return this;\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        isNot = !isNot;\n        return this;\n      }\n    };\n    return instance;\n  }\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  const count = ref(initialValue);\n  const {\n    max = Infinity,\n    min = -Infinity\n  } = options;\n  const inc = (delta = 1) => count.value = Math.min(max, count.value + delta);\n  const dec = (delta = 1) => count.value = Math.max(min, count.value - delta);\n  const get = () => count.value;\n  const set = (val) => count.value = val;\n  const reset = (val = initialValue) => {\n    initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/;\nconst REGEX_FORMAT = /\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;\nconst formatDate = (date, formatStr) => {\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const matches = {\n    YY: String(years).slice(-2),\n    YYYY: years,\n    M: month + 1,\n    MM: `${month + 1}`.padStart(2, \"0\"),\n    D: String(days),\n    DD: `${days}`.padStart(2, \"0\"),\n    H: String(hours),\n    HH: `${hours}`.padStart(2, \"0\"),\n    h: `${hours % 12 || 12}`.padStart(1, \"0\"),\n    hh: `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: String(minutes),\n    mm: `${minutes}`.padStart(2, \"0\"),\n    s: String(seconds),\n    ss: `${seconds}`.padStart(2, \"0\"),\n    SSS: `${milliseconds}`.padStart(3, \"0\"),\n    d: day\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => $1 || matches[match]);\n};\nconst normalizeDate = (date) => {\n  if (date === null)\n    return new Date(NaN);\n  if (date === void 0)\n    return new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n};\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\") {\n  return computed(() => formatDate(normalizeDate(unref(date)), unref(formatStr)));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = ref(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    if (unref(interval) <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    timer = setInterval(cb, unref(interval));\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval)) {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$8.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(b)) {\n      if (__propIsEnum$8.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true\n  } = options;\n  const counter = ref(0);\n  const controls = useIntervalFn(() => counter.value += 1, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues$6({\n      counter\n    }, controls);\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = ref((_a = options.initialValue) != null ? _a : null);\n  watch(source, () => ms.value = timestamp(), options);\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true\n  } = options;\n  const isPending = ref(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, unref(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending,\n    start,\n    stop\n  };\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false\n  } = options;\n  const controls = useTimeoutFn(noop, interval, options);\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return __spreadValues$5({\n      ready\n    }, controls);\n  } else {\n    return ready;\n  }\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const innerValue = ref(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      innerValue.value = value;\n      return innerValue.value;\n    } else {\n      innerValue.value = innerValue.value === unref(truthyValue) ? unref(falsyValue) : unref(truthyValue);\n      return innerValue.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [innerValue, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [\n    ...source instanceof Function ? source() : Array.isArray(source) ? source : unref(source)\n  ];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = new Array(oldList.length);\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __objRest$5 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$6.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$6.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchWithFilter(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$5(_a, [\n    \"eventFilter\"\n  ]);\n  return watch(source, createFilterWrapper(eventFilter, cb), watchOptions);\n}\n\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __objRest$4 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$5.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$5.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchAtMost(source, cb, options) {\n  const _a = options, {\n    count\n  } = _a, watchOptions = __objRest$4(_a, [\n    \"count\"\n  ]);\n  const current = ref(0);\n  const stop = watchWithFilter(source, (...args) => {\n    current.value += 1;\n    if (current.value >= unref(count))\n      nextTick(() => stop());\n    cb(...args);\n  }, watchOptions);\n  return { count: current, stop };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nvar __objRest$3 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$4.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$4.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchDebounced(source, cb, options = {}) {\n  const _a = options, {\n    debounce = 0,\n    maxWait = void 0\n  } = _a, watchOptions = __objRest$3(_a, [\n    \"debounce\",\n    \"maxWait\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$4(__spreadValues$4({}, watchOptions), {\n    eventFilter: debounceFilter(debounce, { maxWait })\n  }));\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$3.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$3.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchIgnorable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$2(_a, [\n    \"eventFilter\"\n  ]);\n  const filteredCb = createFilterWrapper(eventFilter, cb);\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = ref(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(source, (...args) => {\n      if (!ignore.value)\n        filteredCb(...args);\n    }, watchOptions);\n  } else {\n    const disposables = [];\n    const ignoreCounter = ref(0);\n    const syncCounter = ref(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(watch(source, () => {\n      syncCounter.value++;\n    }, __spreadProps$3(__spreadValues$3({}, watchOptions), { flush: \"sync\" })));\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(watch(source, (...args) => {\n      const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n      ignoreCounter.value = 0;\n      syncCounter.value = 0;\n      if (ignore)\n        return;\n      filteredCb(...args);\n    }, watchOptions));\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchPausable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter: filter\n  } = _a, watchOptions = __objRest$1(_a, [\n    \"eventFilter\"\n  ]);\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter);\n  const stop = watchWithFilter(source, cb, __spreadProps$2(__spreadValues$2({}, watchOptions), {\n    eventFilter\n  }));\n  return { stop, pause, resume, isActive };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$1.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$1.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchThrottled(source, cb, options = {}) {\n  const _a = options, {\n    throttle = 0,\n    trailing = true,\n    leading = true\n  } = _a, watchOptions = __objRest(_a, [\n    \"throttle\",\n    \"trailing\",\n    \"leading\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$1(__spreadValues$1({}, watchOptions), {\n    eventFilter: throttleFilter(throttle, trailing, leading)\n  }));\n}\n\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return __spreadProps(__spreadValues({}, res), {\n    trigger\n  });\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => getOneWatchSource(item));\n  return getOneWatchSource(sources);\n}\nfunction getOneWatchSource(source) {\n  return typeof source === \"function\" ? source() : unref(source);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  return watch(source, (v, ov, onInvalidate) => {\n    if (v)\n      cb(v, ov, onInvalidate);\n  }, options);\n}\n\nexport { __onlyVue3, logicAnd as and, assert, refAutoReset as autoResetRef, bypassFilter, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, directiveHooks, computedEager as eagerComputed, extendRef, formatDate, get, identity, watchIgnorable as ignorableWatch, increaseWithUnit, invoke, isBoolean, isClient, isDef, isDefined, isFunction, isIOS, isNumber, isObject, isString, isWindow, logicAnd, logicNot, logicOr, makeDestructurable, noop, normalizeDate, logicNot as not, now, objectPick, logicOr as or, pausableFilter, watchPausable as pausableWatch, promiseTimeout, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toReactive, toRefs, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToggle, watchArray, watchAtMost, watchDebounced, watchIgnorable, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, isClient, isString, tryOnScopeDispose, tryOnMounted, promiseTimeout, tryOnBeforeMount, increaseWithUnit, clamp, useTimeoutFn, pausableWatch, createEventHook, isFunction, timestamp, pausableFilter, watchIgnorable, debounceFilter, createFilterWrapper, bypassFilter, createSingletonPromise, toRefs, containsProp, until, isDef, throttleFilter, useDebounceFn, useThrottleFn, isObject, isNumber, useIntervalFn, syncRef, objectPick, tryOnUnmounted, isIOS, watchWithFilter, identity } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, ref, watchEffect, computed, inject, unref, watch, getCurrentInstance, customRef, onUpdated, reactive, shallowRef, onMounted, markRaw, getCurrentScope, readonly, nextTick, isVue2, set, del, onBeforeUpdate } from 'vue-demi';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    onError = noop\n  } = options;\n  const started = ref(!lazy);\n  const current = ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nconst createUnrefFn = (fn) => {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => unref(i)));\n  };\n};\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = unref(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction useEventListener(...args) {\n  let target;\n  let event;\n  let listener;\n  let options;\n  if (isString(args[0])) {\n    [event, listener, options] = args;\n    target = defaultWindow;\n  } else {\n    [target, event, listener, options] = args;\n  }\n  if (!target)\n    return noop;\n  let cleanup = noop;\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (!el)\n      return;\n    el.addEventListener(event, listener, options);\n    cleanup = () => {\n      el.removeEventListener(event, listener, options);\n      cleanup = noop;\n    };\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return stop;\n}\n\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore, capture = true, detectIframe = false } = options;\n  if (!window)\n    return;\n  const shouldListen = ref(true);\n  let fallback;\n  const listener = (event) => {\n    window.clearTimeout(fallback);\n    const el = unrefElement(target);\n    const composedPath = event.composedPath();\n    if (!el || el === event.target || composedPath.includes(el) || !shouldListen.value)\n      return;\n    if (ignore && ignore.length > 0) {\n      if (ignore.some((target2) => {\n        const el2 = unrefElement(target2);\n        return el2 && (event.target === el2 || composedPath.includes(el2));\n      }))\n        return;\n    }\n    handler(event);\n  };\n  const cleanup = [\n    useEventListener(window, \"click\", listener, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen.value = !!el && !e.composedPath().includes(el);\n    }, { passive: true }),\n    useEventListener(window, \"pointerup\", (e) => {\n      if (e.button === 0) {\n        const path = e.composedPath();\n        e.composedPath = () => path;\n        fallback = window.setTimeout(() => listener(e), 50);\n      }\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      var _a;\n      const el = unrefElement(target);\n      if (((_a = document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(document.activeElement)))\n        handler(event);\n    })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nvar __defProp$j = Object.defineProperty;\nvar __defProps$8 = Object.defineProperties;\nvar __getOwnPropDescs$8 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$l = Object.getOwnPropertySymbols;\nvar __hasOwnProp$l = Object.prototype.hasOwnProperty;\nvar __propIsEnum$l = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$j = (obj, key, value) => key in obj ? __defProp$j(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$j = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$l.call(b, prop))\n      __defNormalProp$j(a, prop, b[prop]);\n  if (__getOwnPropSymbols$l)\n    for (var prop of __getOwnPropSymbols$l(b)) {\n      if (__propIsEnum$l.call(b, prop))\n        __defNormalProp$j(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$8 = (a, b) => __defProps$8(a, __getOwnPropDescs$8(b));\nconst createKeyPredicate = (keyFilter) => {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  else if (keyFilter)\n    return () => true;\n  else\n    return () => false;\n};\nfunction onKeyStroke(key, handler, options = {}) {\n  const { target = defaultWindow, eventName = \"keydown\", passive = false } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$8(__spreadValues$j({}, options), { eventName: \"keydown\" }));\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$8(__spreadValues$j({}, options), { eventName: \"keypress\" }));\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$8(__spreadValues$j({}, options), { eventName: \"keyup\" }));\n}\n\nconst DEFAULT_DELAY = 500;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout = null;\n  function clear() {\n    if (timeout != null) {\n      clearTimeout(timeout);\n      timeout = null;\n    }\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    timeout = setTimeout(() => handler(ev), (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY);\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions);\n  useEventListener(elementRef, \"pointerup\", clear, listenerOptions);\n  useEventListener(elementRef, \"pointerleave\", clear, listenerOptions);\n}\n\nconst isFocusedElementEditable = () => {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n};\nconst isTypedCharValid = ({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) => {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n};\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    !isFocusedElementEditable() && isTypedCharValid(event) && callback(event);\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  const { window = defaultWindow } = options;\n  const counter = ref(0);\n  if (window) {\n    useEventListener(window, \"blur\", () => counter.value += 1, true);\n    useEventListener(window, \"focus\", () => counter.value += 1, true);\n  }\n  return computed(() => {\n    counter.value;\n    return window == null ? void 0 : window.document.activeElement;\n  });\n}\n\nfunction useAsyncQueue(tasks, options = {}) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop\n  } = options;\n  const promiseState = {\n    pending: \"pending\",\n    rejected: \"rejected\",\n    fulfilled: \"fulfilled\"\n  };\n  const initialResult = Array.from(new Array(tasks.length), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = ref(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      return curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        activeIndex.value === tasks.length - 1 && onFinished();\n        return currentRes;\n      });\n    }).catch((e) => {\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    resetOnExecute = true,\n    shallow = true\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = ref(false);\n  const isLoading = ref(false);\n  const error = ref(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n    } catch (e) {\n      error.value = e;\n      onError(e);\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate)\n    execute(delay);\n  return {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = ref(\"\");\n  const promise = ref();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = unref(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => base64.value = res);\n    return promise.value;\n  }\n  if (isRef(target))\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useBattery({ navigator = defaultNavigator } = {}) {\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = navigator && \"getBattery\" in navigator;\n  const charging = ref(false);\n  const chargingTime = ref(0);\n  const dischargingTime = ref(0);\n  const level = ref(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      for (const event of events)\n        useEventListener(battery, event, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = navigator && \"bluetooth\" in navigator;\n  const device = ref(void 0);\n  const error = ref(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = ref();\n  const isConnected = computed(() => {\n    var _a;\n    return ((_a = server.value) == null ? void 0 : _a.connected) || false;\n  });\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      device.value.addEventListener(\"gattserverdisconnected\", () => {\n      });\n      try {\n        server.value = await device.value.gatt.connect();\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected,\n    device,\n    requestDevice,\n    server,\n    error\n  };\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = Boolean(window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  let mediaQuery;\n  const matches = ref(false);\n  const update = () => {\n    if (!isSupported)\n      return;\n    if (!mediaQuery)\n      mediaQuery = window.matchMedia(query);\n    matches.value = mediaQuery.matches;\n  };\n  tryOnBeforeMount(() => {\n    update();\n    if (!mediaQuery)\n      return;\n    if (\"addEventListener\" in mediaQuery)\n      mediaQuery.addEventListener(\"change\", update);\n    else\n      mediaQuery.addListener(update);\n    tryOnScopeDispose(() => {\n      if (\"removeEventListener\" in mediaQuery)\n        mediaQuery.removeEventListener(\"change\", update);\n      else\n        mediaQuery.removeListener(update);\n    });\n  });\n  return matches;\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetify = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904\n};\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 600,\n  sm: 1024,\n  md: 1440,\n  lg: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\n\nvar __defProp$i = Object.defineProperty;\nvar __getOwnPropSymbols$k = Object.getOwnPropertySymbols;\nvar __hasOwnProp$k = Object.prototype.hasOwnProperty;\nvar __propIsEnum$k = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$i = (obj, key, value) => key in obj ? __defProp$i(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$i = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$k.call(b, prop))\n      __defNormalProp$i(a, prop, b[prop]);\n  if (__getOwnPropSymbols$k)\n    for (var prop of __getOwnPropSymbols$k(b)) {\n      if (__propIsEnum$k.call(b, prop))\n        __defNormalProp$i(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = breakpoints[k];\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow } = options;\n  function match(query) {\n    if (!window)\n      return false;\n    return window.matchMedia(query).matches;\n  }\n  const greater = (k) => {\n    return useMediaQuery(`(min-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => greater(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  return __spreadValues$i({\n    greater,\n    smaller(k) {\n      return useMediaQuery(`(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(`(min-width: ${getValue(k)})`);\n    },\n    isSmaller(k) {\n      return match(`(max-width: ${getValue(k, -0.1)})`);\n    },\n    isInBetween(a, b) {\n      return match(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`);\n    }\n  }, shortcutMethods);\n}\n\nconst useBroadcastChannel = (options) => {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = window && \"BroadcastChannel\" in window;\n  const isClosed = ref(false);\n  const channel = ref();\n  const data = ref();\n  const error = ref(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      channel.value.addEventListener(\"message\", (e) => {\n        data.value = e.data;\n      }, { passive: true });\n      channel.value.addEventListener(\"messageerror\", (e) => {\n        error.value = e;\n      }, { passive: true });\n      channel.value.addEventListener(\"close\", () => {\n        isClosed.value = true;\n      });\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n};\n\nfunction useBrowserLocation({ window = defaultWindow } = {}) {\n  const buildState = (trigger) => {\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { hash, host, hostname, href, origin, pathname, port, protocol, search } = (window == null ? void 0 : window.location) || {};\n    return {\n      trigger,\n      state: state2,\n      length,\n      hash,\n      host,\n      hostname,\n      href,\n      origin,\n      pathname,\n      port,\n      protocol,\n      search\n    };\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), { passive: true });\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), { passive: true });\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, watchOptions) {\n  const cachedValue = ref(refValue.value);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction useClamp(value, min, max) {\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, unref(min), unref(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, unref(min), unref(max));\n    }\n  });\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500\n  } = options;\n  const events = [\"copy\", \"cut\"];\n  const isSupported = Boolean(navigator && \"clipboard\" in navigator);\n  const text = ref(\"\");\n  const copied = ref(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring);\n  function updateText() {\n    navigator.clipboard.readText().then((value) => {\n      text.value = value;\n    });\n  }\n  if (isSupported && read) {\n    for (const event of events)\n      useEventListener(event, updateText);\n  }\n  async function copy(value = unref(source)) {\n    if (isSupported && value != null) {\n      await navigator.clipboard.writeText(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\n_global[globalKey] = _global[globalKey] || {};\nconst handlers = _global[globalKey];\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : Array.isArray(rawInit) ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nfunction useStorage(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const data = (shallow ? shallowRef : ref)(initialValue);\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = unref(initialValue);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(data, () => write(data.value), { flush, deep, eventFilter });\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", update);\n  update();\n  return data;\n  function write(v) {\n    try {\n      if (v == null)\n        storage.removeItem(key);\n      else\n        storage.setItem(key, serializer.write(v));\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    if (event && event.key !== key)\n      return;\n    pauseWatch();\n    try {\n      const rawValue = event ? event.newValue : storage.getItem(key);\n      if (rawValue == null) {\n        if (writeDefaults && rawInit !== null)\n          storage.setItem(key, serializer.write(rawInit));\n        return rawInit;\n      } else if (typeof rawValue !== \"string\") {\n        return rawValue;\n      } else {\n        return serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    } finally {\n      resumeWatch();\n    }\n  }\n  function update(event) {\n    if (event && event.key !== key)\n      return;\n    data.value = read(event);\n  }\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nvar __defProp$h = Object.defineProperty;\nvar __getOwnPropSymbols$j = Object.getOwnPropertySymbols;\nvar __hasOwnProp$j = Object.prototype.hasOwnProperty;\nvar __propIsEnum$j = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$h = (obj, key, value) => key in obj ? __defProp$h(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$h = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$j.call(b, prop))\n      __defNormalProp$h(a, prop, b[prop]);\n  if (__getOwnPropSymbols$j)\n    for (var prop of __getOwnPropSymbols$j(b)) {\n      if (__propIsEnum$j.call(b, prop))\n        __defNormalProp$h(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto\n  } = options;\n  const modes = __spreadValues$h({\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\"\n  }, options.modes || {});\n  const preferredDark = usePreferredDark({ window });\n  const preferredMode = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? ref(\"auto\") : useStorage(storageKey, \"auto\", storage, { window, listenToStorageChanges }));\n  const state = computed({\n    get() {\n      return store.value === \"auto\" && !emitAuto ? preferredMode.value : store.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  const updateHTMLAttrs = getSSRHandler(\"updateHTMLAttrs\", (selector2, attribute2, value) => {\n    const el = window == null ? void 0 : window.document.querySelector(selector2);\n    if (!el)\n      return;\n    if (attribute2 === \"class\") {\n      const current = value.split(/\\s/g);\n      Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n        if (current.includes(v))\n          el.classList.add(v);\n        else\n          el.classList.remove(v);\n      });\n    } else {\n      el.setAttribute(attribute2, value);\n    }\n  });\n  function defaultOnChanged(mode) {\n    var _a;\n    const resolvedMode = mode === \"auto\" ? preferredMode.value : mode;\n    updateHTMLAttrs(selector, attribute, (_a = modes[resolvedMode]) != null ? _a : resolvedMode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  return state;\n}\n\nfunction useConfirmDialog(revealed = ref(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCssVar(prop, target, { window = defaultWindow, initialValue = \"\" } = {}) {\n  const variable = ref(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  watch([elRef, () => unref(prop)], ([el, prop2]) => {\n    var _a;\n    if (el && window) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(prop2)) == null ? void 0 : _a.trim();\n      variable.value = value || initialValue;\n    }\n  }, { immediate: true });\n  watch(variable, (val) => {\n    var _a;\n    if ((_a = elRef.value) == null ? void 0 : _a.style)\n      elRef.value.style.setProperty(unref(prop), val);\n  });\n  return variable;\n}\n\nfunction useCurrentElement() {\n  const vm = getCurrentInstance();\n  const count = ref(0);\n  onUpdated(() => {\n    count.value += 1;\n  });\n  onMounted(() => {\n    count.value += 1;\n  });\n  return computed(() => {\n    count.value;\n    return vm.proxy.$el;\n  });\n}\n\nfunction useCycleList(list, options) {\n  var _a;\n  const state = shallowRef((_a = options == null ? void 0 : options.initialValue) != null ? _a : list[0]);\n  const index = computed({\n    get() {\n      var _a2;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, list) : list.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a2 = options == null ? void 0 : options.fallbackIndex) != null ? _a2 : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const length = list.length;\n    const index2 = (i % length + length) % length;\n    const value = list[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  return {\n    state,\n    index,\n    next,\n    prev\n  };\n}\n\nvar __defProp$g = Object.defineProperty;\nvar __defProps$7 = Object.defineProperties;\nvar __getOwnPropDescs$7 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$i = Object.getOwnPropertySymbols;\nvar __hasOwnProp$i = Object.prototype.hasOwnProperty;\nvar __propIsEnum$i = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$g = (obj, key, value) => key in obj ? __defProp$g(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$g = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$i.call(b, prop))\n      __defNormalProp$g(a, prop, b[prop]);\n  if (__getOwnPropSymbols$i)\n    for (var prop of __getOwnPropSymbols$i(b)) {\n      if (__propIsEnum$i.call(b, prop))\n        __defNormalProp$g(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$7 = (a, b) => __defProps$7(a, __getOwnPropDescs$7(b));\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\",\n    window = defaultWindow\n  } = options;\n  const mode = useColorMode(__spreadProps$7(__spreadValues$g({}, options), {\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\");\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  }));\n  const preferredDark = usePreferredDark({ window });\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      if (v === preferredDark.value)\n        mode.value = \"auto\";\n      else\n        mode.value = v ? \"dark\" : \"light\";\n    }\n  });\n  return isDark;\n}\n\nconst fnClone = (v) => JSON.parse(JSON.stringify(v));\nconst fnBypass = (v) => v;\nconst fnSetSource = (source, value) => source.value = value;\nfunction defaultDump(clone) {\n  return clone ? isFunction(clone) ? clone : fnClone : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? isFunction(clone) ? clone : fnClone : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Infinity);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nvar __defProp$f = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$h = Object.getOwnPropertySymbols;\nvar __hasOwnProp$h = Object.prototype.hasOwnProperty;\nvar __propIsEnum$h = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$f = (obj, key, value) => key in obj ? __defProp$f(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$f = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$h.call(b, prop))\n      __defNormalProp$f(a, prop, b[prop]);\n  if (__getOwnPropSymbols$h)\n    for (var prop of __getOwnPropSymbols$h(b)) {\n      if (__propIsEnum$h.call(b, prop))\n        __defNormalProp$f(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(source, commit, { deep, flush, eventFilter: composedFilter });\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, __spreadProps$6(__spreadValues$f({}, options), { clone: options.clone || deep, setSource }));\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return __spreadProps$6(__spreadValues$f({}, manualHistory), {\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  });\n}\n\nvar __defProp$e = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$g = Object.getOwnPropertySymbols;\nvar __hasOwnProp$g = Object.prototype.hasOwnProperty;\nvar __propIsEnum$g = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$e = (obj, key, value) => key in obj ? __defProp$e(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$e = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$g.call(b, prop))\n      __defNormalProp$e(a, prop, b[prop]);\n  if (__getOwnPropSymbols$g)\n    for (var prop of __getOwnPropSymbols$g(b)) {\n      if (__propIsEnum$g.call(b, prop))\n        __defNormalProp$e(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, __spreadProps$5(__spreadValues$e({}, options), { eventFilter: filter }));\n  return __spreadValues$e({}, history);\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    eventFilter = bypassFilter\n  } = options;\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = ref(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  if (window) {\n    const onDeviceMotion = createFilterWrapper(eventFilter, (event) => {\n      acceleration.value = event.acceleration;\n      accelerationIncludingGravity.value = event.accelerationIncludingGravity;\n      rotationRate.value = event.rotationRate;\n      interval.value = event.interval;\n    });\n    useEventListener(window, \"devicemotion\", onDeviceMotion);\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = Boolean(window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = ref(false);\n  const alpha = ref(null);\n  const beta = ref(null);\n  const gamma = ref(null);\n  if (window && isSupported) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nconst DEVICE_PIXEL_RATIO_SCALES = [\n  1,\n  1.325,\n  1.4,\n  1.5,\n  1.8,\n  2,\n  2.4,\n  2.5,\n  2.75,\n  3,\n  3.5,\n  4\n];\nfunction useDevicePixelRatio({\n  window = defaultWindow\n} = {}) {\n  if (!window) {\n    return {\n      pixelRatio: ref(1)\n    };\n  }\n  const pixelRatio = ref(window.devicePixelRatio);\n  const handleDevicePixelRatio = () => {\n    pixelRatio.value = window.devicePixelRatio;\n  };\n  useEventListener(window, \"resize\", handleDevicePixelRatio, { passive: true });\n  DEVICE_PIXEL_RATIO_SCALES.forEach((dppx) => {\n    const mqlMin = useMediaQuery(`screen and (min-resolution: ${dppx}dppx)`);\n    const mqlMax = useMediaQuery(`screen and (max-resolution: ${dppx}dppx)`);\n    watch([mqlMin, mqlMax], handleDevicePixelRatio);\n  });\n  return { pixelRatio };\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = Boolean(navigator && \"permissions\" in navigator);\n  let permissionStatus;\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = ref();\n  const onChange = () => {\n    if (permissionStatus)\n      state.value = permissionStatus.state;\n  };\n  const query = createSingletonPromise(async () => {\n    if (!isSupported)\n      return;\n    if (!permissionStatus) {\n      try {\n        permissionStatus = await navigator.permissions.query(desc);\n        useEventListener(permissionStatus, \"change\", onChange);\n        onChange();\n      } catch (e) {\n        state.value = \"prompt\";\n      }\n    }\n    return permissionStatus;\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  let isSupported = false;\n  const permissionGranted = ref(false);\n  async function update() {\n    if (!isSupported)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n  }\n  async function ensurePermissions() {\n    if (!isSupported)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(\"camera\", { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      stream.getTracks().forEach((t) => t.stop());\n      update();\n      permissionGranted.value = true;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (navigator) {\n    isSupported = Boolean(navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n    if (isSupported) {\n      if (requestPermissions)\n        ensurePermissions();\n      useEventListener(navigator.mediaDevices, \"devicechange\", update);\n      update();\n    }\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a, _b;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = Boolean((_b = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _b.getDisplayMedia);\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    if (!isSupported || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility({ document = defaultDocument } = {}) {\n  if (!document)\n    return ref(\"visible\");\n  const visibility = ref(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  });\n  return visibility;\n}\n\nvar __defProp$d = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$f = Object.getOwnPropertySymbols;\nvar __hasOwnProp$f = Object.prototype.hasOwnProperty;\nvar __propIsEnum$f = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$d = (obj, key, value) => key in obj ? __defProp$d(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$d = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$f.call(b, prop))\n      __defNormalProp$d(a, prop, b[prop]);\n  if (__getOwnPropSymbols$f)\n    for (var prop of __getOwnPropSymbols$f(b)) {\n      if (__propIsEnum$f.call(b, prop))\n        __defNormalProp$d(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nfunction useDraggable(target, options = {}) {\n  var _a, _b;\n  const draggingElement = (_a = options.draggingElement) != null ? _a : defaultWindow;\n  const position = ref((_b = options.initialValue) != null ? _b : { x: 0, y: 0 });\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (options.pointerTypes)\n      return options.pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (unref(options.preventDefault))\n      e.preventDefault();\n    if (unref(options.stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (unref(options.exact) && e.target !== unref(target))\n      return;\n    const rect = unref(target).getBoundingClientRect();\n    const pos = {\n      x: e.pageX - rect.left,\n      y: e.pageY - rect.top\n    };\n    if (((_a2 = options.onStart) == null ? void 0 : _a2.call(options, pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    position.value = {\n      x: e.pageX - pressedDelta.value.x,\n      y: e.pageY - pressedDelta.value.y\n    };\n    (_a2 = options.onMove) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    (_a2 = options.onEnd) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    useEventListener(target, \"pointerdown\", start, true);\n    useEventListener(draggingElement, \"pointermove\", move, true);\n    useEventListener(draggingElement, \"pointerup\", end, true);\n  }\n  return __spreadProps$4(__spreadValues$d({}, toRefs(position)), {\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(() => `left:${position.value.x}px;top:${position.value.y}px;`)\n  });\n}\n\nfunction useDropZone(target, onDrop) {\n  const isOverDropZone = ref(false);\n  let counter = 0;\n  if (isClient) {\n    useEventListener(target, \"dragenter\", (event) => {\n      event.preventDefault();\n      counter += 1;\n      isOverDropZone.value = true;\n    });\n    useEventListener(target, \"dragover\", (event) => {\n      event.preventDefault();\n    });\n    useEventListener(target, \"dragleave\", (event) => {\n      event.preventDefault();\n      counter -= 1;\n      if (counter === 0)\n        isOverDropZone.value = false;\n    });\n    useEventListener(target, \"drop\", (event) => {\n      var _a, _b;\n      event.preventDefault();\n      counter = 0;\n      isOverDropZone.value = false;\n      const files = Array.from((_b = (_a = event.dataTransfer) == null ? void 0 : _a.files) != null ? _b : []);\n      if (files.length === 0) {\n        onDrop(null);\n        return;\n      }\n      onDrop(files);\n    });\n  }\n  return {\n    isOverDropZone\n  };\n}\n\nvar __getOwnPropSymbols$e = Object.getOwnPropertySymbols;\nvar __hasOwnProp$e = Object.prototype.hasOwnProperty;\nvar __propIsEnum$e = Object.prototype.propertyIsEnumerable;\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$e.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$e)\n    for (var prop of __getOwnPropSymbols$e(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$e.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useResizeObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, observerOptions = __objRest$2(_a, [\"window\"]);\n  let observer;\n  const isSupported = window && \"ResizeObserver\" in window;\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported && window && el) {\n      observer = new ResizeObserver(callback);\n      observer.observe(el, observerOptions);\n    }\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true\n  } = options;\n  const height = ref(0);\n  const bottom = ref(0);\n  const left = ref(0);\n  const right = ref(0);\n  const top = ref(0);\n  const width = ref(0);\n  const x = ref(0);\n  const y = ref(0);\n  function update() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    window = defaultWindow\n  } = options;\n  const isActive = ref(false);\n  let rafId = null;\n  function loop() {\n    if (!isActive.value || !window)\n      return;\n    fn();\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      loop();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$c = Object.defineProperty;\nvar __getOwnPropSymbols$d = Object.getOwnPropertySymbols;\nvar __hasOwnProp$d = Object.prototype.hasOwnProperty;\nvar __propIsEnum$d = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$c = (obj, key, value) => key in obj ? __defProp$c(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$c = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$d.call(b, prop))\n      __defNormalProp$c(a, prop, b[prop]);\n  if (__getOwnPropSymbols$d)\n    for (var prop of __getOwnPropSymbols$d(b)) {\n      if (__propIsEnum$d.call(b, prop))\n        __defNormalProp$c(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useElementByPoint(options) {\n  const element = ref(null);\n  const { x, y } = options;\n  const controls = useRafFn(() => {\n    element.value = document.elementFromPoint(unref(x), unref(y));\n  });\n  return __spreadValues$c({\n    element\n  }, controls);\n}\n\nfunction useElementHover(el) {\n  const isHovered = ref(false);\n  useEventListener(el, \"mouseenter\", () => isHovered.value = true);\n  useEventListener(el, \"mouseleave\", () => isHovered.value = false);\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const width = ref(initialSize.width);\n  const height = ref(initialSize.height);\n  useResizeObserver(target, ([entry]) => {\n    width.value = entry.contentRect.width;\n    height.value = entry.contentRect.height;\n  }, options);\n  watch(() => unrefElement(target), (ele) => {\n    width.value = ele ? initialSize.width : 0;\n    height.value = ele ? initialSize.height : 0;\n  });\n  return {\n    width,\n    height\n  };\n}\n\nfunction useElementVisibility(element, { window = defaultWindow, scrollTarget } = {}) {\n  const elementIsVisible = ref(false);\n  const testBounding = () => {\n    if (!window)\n      return;\n    const document = window.document;\n    if (!unref(element)) {\n      elementIsVisible.value = false;\n    } else {\n      const rect = unref(element).getBoundingClientRect();\n      elementIsVisible.value = rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth) && rect.bottom >= 0 && rect.right >= 0;\n    }\n  };\n  tryOnMounted(testBounding);\n  if (window)\n    tryOnMounted(() => useEventListener(unref(scrollTarget) || window, \"scroll\", testBounding, { capture: false, passive: true }));\n  return elementIsVisible;\n}\n\nconst events = new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || [];\n    listeners.push(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    const index = listeners.indexOf(listener);\n    if (index > -1)\n      listeners.splice(index, 1);\n    if (!listeners.length)\n      events.delete(key);\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction useEventSource(url, events = [], options = {}) {\n  const event = ref(null);\n  const data = ref(null);\n  const status = ref(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = ref(null);\n  const {\n    withCredentials = false\n  } = options;\n  const close = () => {\n    if (eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n    }\n  };\n  const es = new EventSource(url, { withCredentials });\n  eventSource.value = es;\n  es.onopen = () => {\n    status.value = \"OPEN\";\n    error.value = null;\n  };\n  es.onerror = (e) => {\n    status.value = \"CLOSED\";\n    error.value = e;\n  };\n  es.onmessage = (e) => {\n    event.value = null;\n    data.value = e.data;\n  };\n  for (const event_name of events) {\n    useEventListener(es, event_name, (e) => {\n      event.value = event_name;\n      data.value = e.data || null;\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    close\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = Boolean(typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = ref(initialValue);\n  async function open(openOptions) {\n    if (!isSupported)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = isRef(newIcon) ? newIcon : ref(newIcon);\n  const applyIcon = (icon) => {\n    document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`).forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(favicon, (i, o) => {\n    if (isString(i) && i !== o)\n      applyIcon(i);\n  }, { immediate: true });\n  return favicon;\n}\n\nvar __defProp$b = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$c = Object.getOwnPropertySymbols;\nvar __hasOwnProp$c = Object.prototype.hasOwnProperty;\nvar __propIsEnum$c = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$b = (obj, key, value) => key in obj ? __defProp$b(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$b = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$c.call(b, prop))\n      __defNormalProp$b(a, prop, b[prop]);\n  if (__getOwnPropSymbols$c)\n    for (var prop of __getOwnPropSymbols$c(b)) {\n      if (__propIsEnum$c.call(b, prop))\n        __defNormalProp$b(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\",\n  formData: \"multipart/form-data\"\n};\nfunction isFetchOptions(obj) {\n  return containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\");\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries([...headers.entries()]);\n  return headers;\n}\nfunction createFetch(config = {}) {\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => config.baseUrl ? joinPaths(unref(config.baseUrl), unref(url)) : unref(url));\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = __spreadValues$b(__spreadValues$b({}, options), args[0]);\n      } else {\n        fetchOptions = __spreadProps$3(__spreadValues$b(__spreadValues$b({}, fetchOptions), args[0]), {\n          headers: __spreadValues$b(__spreadValues$b({}, headersToObject(fetchOptions.headers) || {}), headersToObject(args[0].headers) || {})\n        });\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1]))\n      options = __spreadValues$b(__spreadValues$b({}, options), args[1]);\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = { immediate: true, refetch: false, timeout: 0 };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = __spreadValues$b(__spreadValues$b({}, options), args[0]);\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = __spreadValues$b(__spreadValues$b({}, options), args[1]);\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = ref(false);\n  const isFetching = ref(false);\n  const aborted = ref(false);\n  const statusCode = ref(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort && controller)\n      controller.abort();\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  const execute = async (throwOnFailed = false) => {\n    var _a2;\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    controller = void 0;\n    if (supportsAbort) {\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = __spreadProps$3(__spreadValues$b({}, fetchOptions), {\n        signal: controller.signal\n      });\n    }\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    if (config.payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(unref(config.payload)) : unref(config.payload);\n    }\n    let isCanceled = false;\n    const context = { url: unref(url), options: __spreadValues$b(__spreadValues$b({}, defaultFetchOptions), fetchOptions), cancel: () => {\n      isCanceled = true;\n    } };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return new Promise((resolve, reject) => {\n      var _a3;\n      fetch(context.url, __spreadProps$3(__spreadValues$b(__spreadValues$b({}, defaultFetchOptions), context.options), {\n        headers: __spreadValues$b(__spreadValues$b({}, headersToObject(defaultFetchOptions.headers)), headersToObject((_a3 = context.options) == null ? void 0 : _a3.headers))\n      })).then(async (fetchResponse) => {\n        response.value = fetchResponse;\n        statusCode.value = fetchResponse.status;\n        responseData = await fetchResponse[config.type]();\n        if (options.afterFetch && statusCode.value >= 200 && statusCode.value < 300)\n          ({ data: responseData } = await options.afterFetch({ data: responseData, response: fetchResponse }));\n        data.value = responseData;\n        if (!fetchResponse.ok)\n          throw new Error(fetchResponse.statusText);\n        responseEvent.trigger(fetchResponse);\n        return resolve(fetchResponse);\n      }).catch(async (fetchError) => {\n        let errorData = fetchError.message || fetchError.name;\n        if (options.onFetchError)\n          ({ data: responseData, error: errorData } = await options.onFetchError({ data: responseData, error: fetchError, response: response.value }));\n        data.value = responseData;\n        error.value = errorData;\n        errorEvent.trigger(fetchError);\n        if (throwOnFailed)\n          return reject(fetchError);\n        return resolve(null);\n      }).finally(() => {\n        loading(false);\n        if (timer)\n          timer.stop();\n        finallyEvent.trigger(null);\n      });\n    });\n  };\n  watch(() => [\n    unref(url),\n    unref(options.refetch)\n  ], () => unref(options.refetch) && execute(), { deep: true });\n  const shell = {\n    isFinished,\n    statusCode,\n    response,\n    error,\n    data,\n    isFetching,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch(() => [\n            unref(config.payload),\n            unref(options.refetch)\n          ], () => unref(options.refetch) && execute(), { deep: true });\n        }\n        if (!payloadType && unref(payload) && Object.getPrototypeOf(unref(payload)) === Object.prototype)\n          config.payloadType = \"json\";\n        return __spreadProps$3(__spreadValues$b({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch((error2) => reject(error2));\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return __spreadProps$3(__spreadValues$b({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    setTimeout(execute, 0);\n  return __spreadProps$3(__spreadValues$b({}, shell), {\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  });\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\"))\n    return `${start}/${end}`;\n  return `${start}${end}`;\n}\n\nvar __defProp$a = Object.defineProperty;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$a = (obj, key, value) => key in obj ? __defProp$a(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$a = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$a(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$a(a, prop, b[prop]);\n    }\n  return a;\n};\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\"\n};\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(null);\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n    };\n  }\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = __spreadValues$a(__spreadValues$a(__spreadValues$a({}, DEFAULT_OPTIONS), options), localOptions);\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    input.capture = _options.capture;\n    input.click();\n  };\n  const reset = () => {\n    files.value = null;\n    if (input)\n      input.value = \"\";\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset\n  };\n}\n\nvar __defProp$9 = Object.defineProperty;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = unref(options);\n  const window = _window;\n  const isSupported = Boolean(window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = ref();\n  const data = ref();\n  const file = ref();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported)\n      return;\n    const [handle] = await window.showOpenFilePicker(__spreadValues$9(__spreadValues$9({}, unref(options)), _options));\n    fileHandle.value = handle;\n    await updateFile();\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$9(__spreadValues$9({}, unref(options)), _options));\n    data.value = void 0;\n    await updateFile();\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$9(__spreadValues$9({}, unref(options)), _options));\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    if (unref(dataType) === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    if (unref(dataType) === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    if (unref(dataType) === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => unref(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false } = options;\n  const activeElement = useActiveElement(options);\n  const targetElement = computed(() => unrefElement(target));\n  const focused = computed({\n    get() {\n      return isDef(activeElement.value) && isDef(targetElement.value) && activeElement.value === targetElement.value;\n    },\n    set(value) {\n      var _a, _b;\n      if (!value && focused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      if (value && !focused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus();\n    }\n  });\n  watch(targetElement, () => {\n    focused.value = initialValue;\n  }, { immediate: true, flush: \"post\" });\n  return { focused };\n}\n\nfunction useFocusWithin(target, options = {}) {\n  const activeElement = useActiveElement(options);\n  const targetElement = computed(() => unrefElement(target));\n  const focused = computed(() => targetElement.value && activeElement.value ? targetElement.value.contains(activeElement.value) : false);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = ref(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst functionsMap = [\n  [\n    \"requestFullscreen\",\n    \"exitFullscreen\",\n    \"fullscreenElement\",\n    \"fullscreenEnabled\",\n    \"fullscreenchange\",\n    \"fullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullscreen\",\n    \"webkitExitFullscreen\",\n    \"webkitFullscreenElement\",\n    \"webkitFullscreenEnabled\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullScreen\",\n    \"webkitCancelFullScreen\",\n    \"webkitCurrentFullScreenElement\",\n    \"webkitCancelFullScreen\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"mozRequestFullScreen\",\n    \"mozCancelFullScreen\",\n    \"mozFullScreenElement\",\n    \"mozFullScreenEnabled\",\n    \"mozfullscreenchange\",\n    \"mozfullscreenerror\"\n  ],\n  [\n    \"msRequestFullscreen\",\n    \"msExitFullscreen\",\n    \"msFullscreenElement\",\n    \"msFullscreenEnabled\",\n    \"MSFullscreenChange\",\n    \"MSFullscreenError\"\n  ]\n];\nfunction useFullscreen(target, options = {}) {\n  const { document = defaultDocument, autoExit = false } = options;\n  const targetRef = target || (document == null ? void 0 : document.querySelector(\"html\"));\n  const isFullscreen = ref(false);\n  let isSupported = false;\n  let map = functionsMap[0];\n  if (!document) {\n    isSupported = false;\n  } else {\n    for (const m of functionsMap) {\n      if (m[1] in document) {\n        map = m;\n        isSupported = true;\n        break;\n      }\n    }\n  }\n  const [REQUEST, EXIT, ELEMENT, , EVENT] = map;\n  async function exit() {\n    if (!isSupported)\n      return;\n    if (document == null ? void 0 : document[ELEMENT])\n      await document[EXIT]();\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported)\n      return;\n    await exit();\n    const target2 = unrefElement(targetRef);\n    if (target2) {\n      await target2[REQUEST]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    if (isFullscreen.value)\n      await exit();\n    else\n      await enter();\n  }\n  if (document) {\n    useEventListener(document, EVENT, () => {\n      isFullscreen.value = !!(document == null ? void 0 : document[ELEMENT]);\n    }, false);\n  }\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = navigator && \"getGamepads\" in navigator;\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      hapticActuators,\n      index: gamepad.index,\n      mapping: gamepad.mapping,\n      connected: gamepad.connected,\n      timestamp: gamepad.timestamp,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (let i = 0; i < _gamepads.length; ++i) {\n      const gamepad = _gamepads[i];\n      if (gamepad) {\n        const index = gamepads.value.findIndex(({ index: index2 }) => index2 === gamepad.index);\n        if (index > -1)\n          gamepads.value[index] = stateFromGamepad(gamepad);\n      }\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad));\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad));\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    if (_gamepads) {\n      for (let i = 0; i < _gamepads.length; ++i) {\n        const gamepad = _gamepads[i];\n        if (gamepad)\n          onGamepadConnected(gamepad);\n      }\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = navigator && \"geolocation\" in navigator;\n  const locatedAt = ref(null);\n  const error = ref(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Infinity,\n    longitude: Infinity,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  if (isSupported) {\n    watcher = navigator.geolocation.watchPosition(updatePosition, (err) => error.value = err, {\n      enableHighAccuracy,\n      maximumAge,\n      timeout\n    });\n  }\n  tryOnScopeDispose(() => {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = ref(initialState);\n  const lastActive = ref(timestamp());\n  let timer;\n  const onEvent = createFilterWrapper(eventFilter, () => {\n    idle.value = false;\n    lastActive.value = timestamp();\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  });\n  if (window) {\n    const document = window.document;\n    for (const event of events)\n      useEventListener(window, event, onEvent, { passive: true });\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      });\n    }\n  }\n  timer = setTimeout(() => idle.value = true, timeout);\n  return { idle, lastActive };\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes } = options;\n    img.src = src;\n    if (srcset)\n      img.srcset = srcset;\n    if (sizes)\n      img.sizes = sizes;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nconst useImage = (options, asyncStateOptions = {}) => {\n  const state = useAsyncState(() => loadImage(unref(options)), void 0, __spreadValues$8({\n    resetOnExecute: true\n  }, asyncStateOptions));\n  watch(() => unref(options), () => state.execute(asyncStateOptions.delay), { deep: true });\n  return state;\n};\n\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    }\n  } = options;\n  const x = ref(0);\n  const y = ref(0);\n  const isScrolling = ref(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  if (element) {\n    const onScrollEnd = useDebounceFn((e) => {\n      isScrolling.value = false;\n      directions.left = false;\n      directions.right = false;\n      directions.top = false;\n      directions.bottom = false;\n      onStop(e);\n    }, throttle + idle);\n    const onScrollHandler = (e) => {\n      const eventTarget = e.target === document ? e.target.documentElement : e.target;\n      const scrollLeft = eventTarget.scrollLeft;\n      directions.left = scrollLeft < x.value;\n      directions.right = scrollLeft > x.value;\n      arrivedState.left = scrollLeft <= 0 + (offset.left || 0);\n      arrivedState.right = scrollLeft + eventTarget.clientWidth >= eventTarget.scrollWidth - (offset.right || 0);\n      x.value = scrollLeft;\n      let scrollTop = eventTarget.scrollTop;\n      if (e.target === document && !scrollTop)\n        scrollTop = document.body.scrollTop;\n      directions.top = scrollTop < y.value;\n      directions.bottom = scrollTop > y.value;\n      arrivedState.top = scrollTop <= 0 + (offset.top || 0);\n      arrivedState.bottom = scrollTop + eventTarget.clientHeight >= eventTarget.scrollHeight - (offset.bottom || 0);\n      y.value = scrollTop;\n      isScrolling.value = true;\n      onScrollEnd(e);\n      onScroll(e);\n    };\n    useEventListener(element, \"scroll\", throttle ? useThrottleFn(onScrollHandler, throttle) : onScrollHandler, eventListenerOptions);\n  }\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions\n  };\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$8.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(b)) {\n      if (__propIsEnum$8.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a, _b;\n  const direction = (_a = options.direction) != null ? _a : \"bottom\";\n  const state = reactive(useScroll(element, __spreadProps$2(__spreadValues$7({}, options), {\n    offset: __spreadValues$7({\n      [direction]: (_b = options.distance) != null ? _b : 0\n    }, options.offset)\n  })));\n  watch(() => state.arrivedState[direction], async (v) => {\n    var _a2, _b2;\n    if (v) {\n      const elem = unref(element);\n      const previous = {\n        height: (_a2 = elem == null ? void 0 : elem.scrollHeight) != null ? _a2 : 0,\n        width: (_b2 = elem == null ? void 0 : elem.scrollWidth) != null ? _b2 : 0\n      };\n      await onLoadMore(state);\n      if (options.preserveScrollPosition && elem) {\n        nextTick(() => {\n          elem.scrollTo({\n            top: elem.scrollHeight - previous.height,\n            left: elem.scrollWidth - previous.width\n          });\n        });\n      }\n    }\n  });\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0.1,\n    window = defaultWindow\n  } = options;\n  const isSupported = window && \"IntersectionObserver\" in window;\n  let cleanup = noop;\n  const stopWatch = isSupported ? watch(() => ({\n    el: unrefElement(target),\n    root: unrefElement(root)\n  }), ({ el, root: root2 }) => {\n    cleanup();\n    if (!el)\n      return;\n    const observer = new IntersectionObserver(callback, {\n      root: root2,\n      rootMargin,\n      threshold\n    });\n    observer.observe(el);\n    cleanup = () => {\n      observer.disconnect();\n      cleanup = noop;\n    };\n  }, { immediate: true, flush: \"post\" }) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = ref(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(new Set());\n  const obj = { toJSON() {\n    return {};\n  }, current };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = new Set();\n  const usedKeys = new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (code) {\n      if (value)\n        current.add(e.code);\n      else\n        current.delete(e.code);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  if (target) {\n    useEventListener(target, \"keydown\", (e) => {\n      updateRefs(e, true);\n      return onEventFired(e);\n    }, { passive });\n    useEventListener(target, \"keyup\", (e) => {\n      updateRefs(e, false);\n      return onEventFired(e);\n    }, { passive });\n    useEventListener(\"blur\", reset, { passive: true });\n    useEventListener(\"focus\", reset, { passive: true });\n  }\n  const proxy = new Proxy(refs, {\n    get(target2, prop, rec) {\n      if (typeof prop !== \"string\")\n        return Reflect.get(target2, prop, rec);\n      prop = prop.toLowerCase();\n      if (prop in aliasMap)\n        prop = aliasMap[prop];\n      if (!(prop in refs)) {\n        if (/[+_-]/.test(prop)) {\n          const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n          refs[prop] = computed(() => keys.every((key) => unref(proxy[key])));\n        } else {\n          refs[prop] = ref(false);\n        }\n      }\n      const r = Reflect.get(target2, prop, rec);\n      return useReactive ? unref(r) : r;\n    }\n  });\n  return proxy;\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction usingElRef(source, cb) {\n  if (unref(source))\n    cb(unref(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  options = __spreadValues$6(__spreadValues$6({}, defaultOptions), options);\n  const {\n    document = defaultDocument\n  } = options;\n  const currentTime = ref(0);\n  const duration = ref(0);\n  const seeking = ref(false);\n  const volume = ref(1);\n  const waiting = ref(false);\n  const ended = ref(false);\n  const playing = ref(false);\n  const rate = ref(1);\n  const stalled = ref(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = ref(-1);\n  const isPictureInPicture = ref(false);\n  const muted = ref(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = isNumber(track) ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = isNumber(track) ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = unref(target);\n    if (!el)\n      return;\n    const src = unref(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (isString(src))\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.removeEventListener(\"error\", sourceErrorEvent.trigger);\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.addEventListener(\"error\", sourceErrorEvent.trigger);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  tryOnScopeDispose(() => {\n    const el = unref(target);\n    if (!el)\n      return;\n    el.querySelectorAll(\"source\").forEach((e) => e.removeEventListener(\"error\", sourceErrorEvent.trigger));\n  });\n  watch(volume, (vol) => {\n    const el = unref(target);\n    if (!el)\n      return;\n    el.volume = vol;\n  });\n  watch(muted, (mute) => {\n    const el = unref(target);\n    if (!el)\n      return;\n    el.muted = mute;\n  });\n  watch(rate, (rate2) => {\n    const el = unref(target);\n    if (!el)\n      return;\n    el.playbackRate = rate2;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = unref(options.tracks);\n    const el = unref(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = unref(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = unref(target);\n    if (!el)\n      return;\n    isPlaying ? el.play() : el.pause();\n  });\n  useEventListener(target, \"timeupdate\", () => ignoreCurrentTimeUpdates(() => currentTime.value = unref(target).currentTime));\n  useEventListener(target, \"durationchange\", () => duration.value = unref(target).duration);\n  useEventListener(target, \"progress\", () => buffered.value = timeRangeToArray(unref(target).buffered));\n  useEventListener(target, \"seeking\", () => seeking.value = true);\n  useEventListener(target, \"seeked\", () => seeking.value = false);\n  useEventListener(target, \"waiting\", () => waiting.value = true);\n  useEventListener(target, \"playing\", () => waiting.value = false);\n  useEventListener(target, \"ratechange\", () => rate.value = unref(target).playbackRate);\n  useEventListener(target, \"stalled\", () => stalled.value = true);\n  useEventListener(target, \"ended\", () => ended.value = true);\n  useEventListener(target, \"pause\", () => ignorePlayingUpdates(() => playing.value = false));\n  useEventListener(target, \"play\", () => ignorePlayingUpdates(() => playing.value = true));\n  useEventListener(target, \"enterpictureinpicture\", () => isPictureInPicture.value = true);\n  useEventListener(target, \"leavepictureinpicture\", () => isPictureInPicture.value = false);\n  useEventListener(target, \"volumechange\", () => {\n    const el = unref(target);\n    if (!el)\n      return;\n    volume.value = el.volume;\n    muted.value = el.muted;\n  });\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = unref(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks));\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    volume,\n    muted,\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    onSourceError: sourceErrorEvent.on\n  };\n}\n\nconst getMapVue2Compat = () => {\n  const data = reactive({});\n  return {\n    get: (key) => data[key],\n    set: (key, value) => set(data, key, value),\n    has: (key) => Object.prototype.hasOwnProperty.call(data, key),\n    delete: (key) => del(data, key),\n    clear: () => {\n      Object.keys(data).forEach((key) => {\n        del(data, key);\n      });\n    }\n  };\n};\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return reactive(options.cache);\n    if (isVue2)\n      return getMapVue2Compat();\n    return reactive(new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = typeof performance !== \"undefined\" && \"memory\" in performance;\n  if (isSupported) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nfunction useMounted() {\n  const isMounted = ref(false);\n  onMounted(() => {\n    isMounted.value = true;\n  });\n  return isMounted;\n}\n\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    eventFilter\n  } = options;\n  const x = ref(initialValue.x);\n  const y = ref(initialValue.y);\n  const sourceType = ref(null);\n  const mouseHandler = (event) => {\n    if (type === \"page\") {\n      x.value = event.pageX;\n      y.value = event.pageY;\n    } else if (type === \"client\") {\n      x.value = event.clientX;\n      y.value = event.clientY;\n    }\n    sourceType.value = \"mouse\";\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const touch2 = event.touches[0];\n      if (type === \"page\") {\n        x.value = touch2.pageX;\n        y.value = touch2.pageY;\n      } else if (type === \"client\") {\n        x.value = touch2.clientX;\n        y.value = touch2.clientY;\n      }\n      sourceType.value = \"touch\";\n    }\n  };\n  const mouseHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? mouseHandler(event) : eventFilter(() => mouseHandler(event), {});\n  };\n  const touchHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? touchHandler(event) : eventFilter(() => touchHandler(event), {});\n  };\n  if (window) {\n    useEventListener(window, \"mousemove\", mouseHandlerWrapper, { passive: true });\n    useEventListener(window, \"dragover\", mouseHandlerWrapper, { passive: true });\n    if (touch) {\n      useEventListener(window, \"touchstart\", touchHandlerWrapper, { passive: true });\n      useEventListener(window, \"touchmove\", touchHandlerWrapper, { passive: true });\n      if (resetOnTouchEnds)\n        useEventListener(window, \"touchend\", reset, { passive: true });\n    }\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = ref(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = ref(0);\n  const elementY = ref(0);\n  const elementPositionX = ref(0);\n  const elementPositionY = ref(0);\n  const elementHeight = ref(0);\n  const elementWidth = ref(0);\n  const isOutside = ref(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch([targetRef, x, y], () => {\n      const el = unrefElement(targetRef);\n      if (!el)\n        return;\n      const {\n        left,\n        top,\n        width,\n        height\n      } = el.getBoundingClientRect();\n      elementPositionX.value = left + window.pageXOffset;\n      elementPositionY.value = top + window.pageYOffset;\n      elementHeight.value = height;\n      elementWidth.value = width;\n      const elX = x.value - elementPositionX.value;\n      const elY = y.value - elementPositionY.value;\n      isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n      if (handleOutside || !isOutside.value) {\n        elementX.value = elX;\n        elementY.value = elY;\n      }\n    }, { immediate: true });\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = ref(initialValue);\n  const sourceType = ref(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => () => {\n    pressed.value = true;\n    sourceType.value = srcType;\n  };\n  const onReleased = () => {\n    pressed.value = false;\n    sourceType.value = null;\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), { passive: true });\n  useEventListener(window, \"mouseleave\", onReleased, { passive: true });\n  useEventListener(window, \"mouseup\", onReleased, { passive: true });\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), { passive: true });\n    useEventListener(window, \"drop\", onReleased, { passive: true });\n    useEventListener(window, \"dragend\", onReleased, { passive: true });\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), { passive: true });\n    useEventListener(window, \"touchend\", onReleased, { passive: true });\n    useEventListener(window, \"touchcancel\", onReleased, { passive: true });\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$6.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$6.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useMutationObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, mutationOptions = __objRest$1(_a, [\"window\"]);\n  let observer;\n  const isSupported = window && \"MutationObserver\" in window;\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported && window && el) {\n      observer = new MutationObserver(callback);\n      observer.observe(el, mutationOptions);\n    }\n  }, { immediate: true });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst useNavigatorLanguage = (options = {}) => {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = Boolean(navigator && \"language\" in navigator);\n  const language = ref(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  });\n  return {\n    isSupported,\n    language\n  };\n};\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = Boolean(navigator && \"connection\" in navigator);\n  const isOnline = ref(true);\n  const saveData = ref(false);\n  const offlineAt = ref(void 0);\n  const onlineAt = ref(void 0);\n  const downlink = ref(void 0);\n  const downlinkMax = ref(void 0);\n  const rtt = ref(void 0);\n  const effectiveType = ref(void 0);\n  const type = ref(\"unknown\");\n  const connection = isSupported && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    });\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    });\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, false);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline,\n    saveData,\n    offlineAt,\n    onlineAt,\n    downlink,\n    downlinkMax,\n    effectiveType,\n    rtt,\n    type\n  };\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$5.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(b)) {\n      if (__propIsEnum$5.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(new Date());\n  const update = () => now.value = new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return __spreadValues$5({\n      now\n    }, controls);\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = ref();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(() => unref(object), (newObject) => {\n    release();\n    if (newObject)\n      url.value = URL.createObjectURL(newObject);\n  }, { immediate: true });\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Infinity,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Infinity);\n  const pageCount = computed(() => Math.ceil(unref(total) / unref(currentPageSize)));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page))\n    syncRef(page, currentPage);\n  if (isRef(pageSize))\n    syncRef(pageSize, currentPageSize);\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = ref(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    useEventListener(window, \"mouseout\", handler, { passive: true });\n    useEventListener(window.document, \"mouseleave\", handler, { passive: true });\n    useEventListener(window.document, \"mouseenter\", handler, { passive: true });\n  }\n  return isLeft;\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0))\n      return \"deviceOrientation\";\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = -orientation.beta / 90;\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = orientation.gamma / 90;\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = ref(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    useEventListener(target, \"pointerdown\", handler, { passive: true });\n    useEventListener(target, \"pointermove\", handler, { passive: true });\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, { passive: true });\n  }\n  return __spreadProps$1(__spreadValues$4({}, toRefs(state)), {\n    isInside\n  });\n}\n\nvar SwipeDirection;\n(function(SwipeDirection2) {\n  SwipeDirection2[\"UP\"] = \"UP\";\n  SwipeDirection2[\"RIGHT\"] = \"RIGHT\";\n  SwipeDirection2[\"DOWN\"] = \"DOWN\";\n  SwipeDirection2[\"LEFT\"] = \"LEFT\";\n  SwipeDirection2[\"NONE\"] = \"NONE\";\n})(SwipeDirection || (SwipeDirection = {}));\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true,\n    window = defaultWindow\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return diffY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  let listenerOptions;\n  const isPassiveEventSupported = checkPassiveEventSupport(window == null ? void 0 : window.document);\n  if (!passive)\n    listenerOptions = isPassiveEventSupported ? { passive: false, capture: true } : { capture: true };\n  else\n    listenerOptions = isPassiveEventSupported ? { passive: true } : { capture: false };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (listenerOptions.capture && !listenerOptions.passive)\n        e.preventDefault();\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchend\", onTouchEnd, listenerOptions),\n    useEventListener(target, \"touchcancel\", onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isPassiveEventSupported,\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop\n  };\n}\nfunction checkPassiveEventSupport(document) {\n  if (!document)\n    return false;\n  let supportsPassive = false;\n  const optionsBlock = {\n    get passive() {\n      supportsPassive = true;\n      return false;\n    }\n  };\n  document.addEventListener(\"x\", noop, optionsBlock);\n  document.removeEventListener(\"x\", noop);\n  return supportsPassive;\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = ref(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const isPointerDown = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return distanceY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const filterEvent = (e) => {\n    if (options.pointerTypes)\n      return options.pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      var _a, _b;\n      if (!filterEvent(e))\n        return;\n      isPointerDown.value = true;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!filterEvent(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }),\n    useEventListener(target, \"pointerup\", (e) => {\n      var _a, _b;\n      if (!filterEvent(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"initial\");\n    })\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  });\n  return value;\n}\n\nconst useScreenOrientation = (options = {}) => {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = !!(window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = ref(screenOrientation.angle || 0);\n  if (isSupported) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    });\n  }\n  const lockOrientation = (type) => {\n    if (!isSupported)\n      return Promise.reject(new Error(\"Not supported\"));\n    return screenOrientation.lock(type);\n  };\n  const unlockOrientation = () => {\n    if (isSupported)\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n};\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = ref(\"\");\n  const right = ref(\"\");\n  const bottom = ref(\"\");\n  const left = ref(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update));\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = ref(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${src}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = unref(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    el.addEventListener(\"error\", (event) => reject(event));\n    el.addEventListener(\"abort\", (event) => reject(event));\n    el.addEventListener(\"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    });\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${src}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = ref(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow;\n  watch(() => unref(element), (el) => {\n    if (el) {\n      const ele = el;\n      initialOverflow = ele.style.overflow;\n      if (isLocked.value)\n        ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const ele = unref(element);\n    if (!ele || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(ele, \"touchmove\", preventDefault, { passive: false });\n    }\n    ele.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const ele = unref(element);\n    if (!ele || !isLocked.value)\n      return;\n    isIOS && (stopTouchMoveListener == null ? void 0 : stopTouchMoveListener());\n    ele.style.overflow = initialOverflow;\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else\n        unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = _navigator && \"canShare\" in _navigator;\n  const share = async (overrideOptions = {}) => {\n    if (isSupported) {\n      const data = __spreadValues$3(__spreadValues$3({}, unref(shareOptions)), unref(overrideOptions));\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    window = defaultWindow\n  } = options;\n  const lang = ref(options.lang || \"en-US\");\n  const isListening = ref(false);\n  const isFinal = ref(false);\n  const result = ref(\"\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isListening.value) => {\n    isListening.value = value;\n  };\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = Boolean(SpeechRecognition);\n  let recognition;\n  if (isSupported) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = unref(lang);\n    recognition.onstart = () => {\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const transcript = Array.from(event.results).map((result2) => {\n        isFinal.value = result2.isFinal;\n        return result2[0];\n      }).map((result2) => result2.transcript).join(\"\");\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = unref(lang);\n    };\n    watch(isListening, () => {\n      if (isListening.value)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isListening.value = false;\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  var _a, _b;\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = Boolean(synth);\n  const isPlaying = ref(false);\n  const status = ref(\"init\");\n  const voiceInfo = {\n    lang: ((_a = options.voice) == null ? void 0 : _a.lang) || \"default\",\n    name: ((_b = options.voice) == null ? void 0 : _b.name) || \"\"\n  };\n  const spokenText = ref(text || \"\");\n  const lang = ref(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = unref(lang);\n    options.voice && (utterance2.voice = options.voice);\n    utterance2.pitch = pitch;\n    utterance2.rate = rate;\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      utterance2.lang = unref(lang);\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    utterance && synth.speak(utterance.value);\n  };\n  if (isSupported) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    voiceInfo,\n    utterance,\n    error,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = unref(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(initialValue);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => setTimeout(() => read(e), 0));\n  if (storage) {\n    watchWithFilter(data, async () => {\n      try {\n        if (data.value == null)\n          await storage.removeItem(key);\n        else\n          await storage.setItem(key, await serializer.write(data.value));\n      } catch (e) {\n        onError(e);\n      }\n    }, {\n      flush,\n      deep,\n      eventFilter\n    });\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = ref(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = ref(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    el.type = \"text/css\";\n    el.id = id;\n    if (options.media)\n      el.media = options.media;\n    document.head.appendChild(el);\n    if (isLoaded.value)\n      return;\n    stop = watch(cssRef, (value) => {\n      el.innerText = value;\n    }, { immediate: true });\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  const ranges = new Array(rangeCount);\n  for (let i = 0; i < rangeCount; i++) {\n    const range = selection.getRangeAt(i);\n    ranges[i] = range;\n  }\n  return ranges;\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange);\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction useTextareaAutosize(options) {\n  const textarea = ref(options == null ? void 0 : options.element);\n  const input = ref(options == null ? void 0 : options.input);\n  function triggerResize() {\n    var _a, _b;\n    if (!textarea.value)\n      return;\n    textarea.value.style.height = \"1px\";\n    textarea.value.style.height = `${(_a = textarea.value) == null ? void 0 : _a.scrollHeight}px`;\n    (_b = options == null ? void 0 : options.onResize) == null ? void 0 : _b.call(options);\n  }\n  watch([input, textarea], triggerResize, { immediate: true });\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, __spreadProps(__spreadValues$2({}, options), { eventFilter: filter }));\n  return __spreadValues$2({}, history);\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$1.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$1.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Infinity, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`\n};\nconst DEFAULT_FORMATTER = (date) => date.toISOString().slice(0, 10);\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    max,\n    updateInterval = 3e4,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER\n  } = options;\n  const { abs, round } = Math;\n  const _a = useNow({ interval: updateInterval, controls: true }), { now } = _a, controls = __objRest(_a, [\"now\"]);\n  function getTimeago(from, now2) {\n    var _a2;\n    const diff = +now2 - +from;\n    const absDiff = abs(diff);\n    if (absDiff < 6e4)\n      return messages.justNow;\n    if (typeof max === \"number\" && absDiff > max)\n      return fullDateFormatter(new Date(from));\n    if (typeof max === \"string\") {\n      const unitMax = (_a2 = UNITS.find((i) => i.name === max)) == null ? void 0 : _a2.max;\n      if (unitMax && absDiff > unitMax)\n        return fullDateFormatter(new Date(from));\n    }\n    for (const unit of UNITS) {\n      if (absDiff < unit.max)\n        return format(diff, unit);\n    }\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  function format(diff, unit) {\n    const val = round(abs(diff) / unit.value);\n    const past = diff > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  const timeAgo = computed(() => getTimeago(new Date(unref(time)), unref(now.value)));\n  if (exposeControls) {\n    return __spreadValues$1({\n      timeAgo\n    }, controls);\n  } else {\n    return timeAgo;\n  }\n}\n\nfunction useTimeoutPoll(fn, interval, timeoutPollOptions) {\n  const { start } = useTimeoutFn(loop, interval);\n  const isActive = ref(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      loop();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (timeoutPollOptions == null ? void 0 : timeoutPollOptions.immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const ts = ref(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate }) : useIntervalFn(update, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues({\n      timestamp: ts\n    }, controls);\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b;\n  const {\n    document = defaultDocument,\n    observe = false,\n    titleTemplate = \"%s\"\n  } = options;\n  const title = ref((_a = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _a : null);\n  watch(title, (t, o) => {\n    if (isString(t) && t !== o && document)\n      document.title = titleTemplate.replace(\"%s\", t);\n  }, { immediate: true });\n  if (observe && document) {\n    useMutationObserver((_b = document.head) == null ? void 0 : _b.querySelector(\"title\"), () => {\n      if (document && document.title !== title.value)\n        title.value = titleTemplate.replace(\"%s\", document.title);\n    }, { childList: true });\n  }\n  return title;\n}\n\nconst TransitionPresets = {\n  linear: identity,\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction useTransition(source, options = {}) {\n  const {\n    delay = 0,\n    disabled = false,\n    duration = 1e3,\n    onFinished = noop,\n    onStarted = noop,\n    transition = identity\n  } = options;\n  const currentTransition = computed(() => {\n    const t = unref(transition);\n    return isFunction(t) ? t : createEasingFunction(t);\n  });\n  const sourceValue = computed(() => {\n    const s = unref(source);\n    return isNumber(s) ? s : s.map(unref);\n  });\n  const sourceVector = computed(() => isNumber(sourceValue.value) ? [sourceValue.value] : sourceValue.value);\n  const outputVector = ref(sourceVector.value.slice(0));\n  let currentDuration;\n  let diffVector;\n  let endAt;\n  let startAt;\n  let startVector;\n  const { resume, pause } = useRafFn(() => {\n    const now = Date.now();\n    const progress = clamp(1 - (endAt - now) / currentDuration, 0, 1);\n    outputVector.value = startVector.map((val, i) => {\n      var _a;\n      return val + ((_a = diffVector[i]) != null ? _a : 0) * currentTransition.value(progress);\n    });\n    if (progress >= 1) {\n      pause();\n      onFinished();\n    }\n  }, { immediate: false });\n  const start = () => {\n    pause();\n    currentDuration = unref(duration);\n    diffVector = outputVector.value.map((n, i) => {\n      var _a, _b;\n      return ((_a = sourceVector.value[i]) != null ? _a : 0) - ((_b = outputVector.value[i]) != null ? _b : 0);\n    });\n    startVector = outputVector.value.slice(0);\n    startAt = Date.now();\n    endAt = startAt + currentDuration;\n    resume();\n    onStarted();\n  };\n  const timeout = useTimeoutFn(start, delay, { immediate: false });\n  watch(sourceVector, () => {\n    if (unref(disabled)) {\n      outputVector.value = sourceVector.value.slice(0);\n    } else {\n      if (unref(delay) <= 0)\n        start();\n      else\n        timeout.start();\n    }\n  }, { deep: true });\n  return computed(() => {\n    const targetVector = unref(disabled) ? sourceVector : outputVector;\n    return isNumber(sourceValue.value) ? targetVector.value[0] : targetVector.value;\n  });\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(state, () => {\n    const params = new URLSearchParams(\"\");\n    Object.keys(state).forEach((key) => {\n      const mapEntry = state[key];\n      if (Array.isArray(mapEntry))\n        mapEntry.forEach((value) => params.append(key, value));\n      else if (removeNullishValues && mapEntry == null)\n        params.delete(key);\n      else if (removeFalsyValues && !mapEntry)\n        params.delete(key);\n      else\n        params.set(key, mapEntry);\n    });\n    write(params);\n  }, { deep: true });\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    window.history.replaceState(window.history.state, window.document.title, window.location.pathname + constructQuery(params));\n    resume();\n  }\n  function onChanged() {\n    write(read(), true);\n  }\n  useEventListener(window, \"popstate\", onChanged, false);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, false);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b, _c;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = ref((_b = options.autoSwitch) != null ? _b : true);\n  const videoDeviceId = ref(options.videoDeviceId);\n  const audioDeviceId = ref(options.audioDeviceId);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = Boolean((_c = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _c.getUserMedia);\n  const stream = shallowRef();\n  function getDeviceOptions(device) {\n    if (device.value === \"none\" || device.value === false)\n      return false;\n    if (device.value == null)\n      return true;\n    return {\n      deviceId: device.value\n    };\n  }\n  async function _start() {\n    if (!isSupported || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(videoDeviceId),\n      audio: getDeviceOptions(audioDeviceId)\n    });\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  watch([videoDeviceId, audioDeviceId], () => {\n    if (autoSwitch.value && stream.value)\n      restart();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    videoDeviceId,\n    audioDeviceId,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c, _d, _e;\n  const {\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    if (isVue2) {\n      const modelOptions = (_e = (_d = vm == null ? void 0 : vm.proxy) == null ? void 0 : _d.$options) == null ? void 0 : _e.model;\n      key = (modelOptions == null ? void 0 : modelOptions.value) || \"value\";\n      if (!eventName)\n        event = (modelOptions == null ? void 0 : modelOptions.event) || \"input\";\n    } else {\n      key = \"modelValue\";\n    }\n  }\n  event = eventName || event || `update:${key.toString()}`;\n  const getValue = () => isDef(props[key]) ? props[key] : defaultValue;\n  if (passive) {\n    const proxy = ref(getValue());\n    watch(() => props[key], (v) => proxy.value = v);\n    watch(proxy, (v) => {\n      if (v !== props[key] || deep)\n        _emit(event, v);\n    }, {\n      deep\n    });\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        _emit(event, value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props)\n    ret[key] = useVModel(props, key, emit, options);\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = typeof navigator !== \"undefined\" && \"vibrate\" in navigator;\n  const patternRef = ref(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(vibrate, interval, {\n      immediate: false,\n      immediateCallback: false\n    });\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const containerRef = ref();\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = (containerHeight) => {\n    if (typeof itemHeight === \"number\")\n      return Math.ceil(containerHeight / itemHeight);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const height = itemHeight(i);\n      sum += height;\n      if (sum >= containerHeight) {\n        capacity = i;\n        break;\n      }\n    }\n    return capacity - start;\n  };\n  const getOffset = (scrollTop) => {\n    if (typeof itemHeight === \"number\")\n      return Math.floor(scrollTop / itemHeight) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const height = itemHeight(i);\n      sum += height;\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n  const calculateRange = () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(element.scrollTop);\n      const viewCapacity = getViewCapacity(element.clientHeight);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n  watch([size.width, size.height, list], () => {\n    calculateRange();\n  });\n  const totalHeight = computed(() => {\n    if (typeof itemHeight === \"number\")\n      return source.value.length * itemHeight;\n    return source.value.reduce((sum, _, index) => sum + itemHeight(index), 0);\n  });\n  const getDistanceTop = (index) => {\n    if (typeof itemHeight === \"number\") {\n      const height2 = index * itemHeight;\n      return height2;\n    }\n    const height = source.value.slice(0, index).reduce((sum, _, i) => sum + itemHeight(i), 0);\n    return height;\n  };\n  const scrollTo = (index) => {\n    if (containerRef.value) {\n      containerRef.value.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  const containerStyle = { overflowY: \"auto\" };\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\n\nconst useWakeLock = (options = {}) => {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  let wakeLock;\n  const isSupported = navigator && \"wakeLock\" in navigator;\n  const isActive = ref(false);\n  async function onVisibilityChange() {\n    if (!isSupported || !wakeLock)\n      return;\n    if (document && document.visibilityState === \"visible\")\n      wakeLock = await navigator.wakeLock.request(\"screen\");\n    isActive.value = !wakeLock.released;\n  }\n  if (document)\n    useEventListener(document, \"visibilitychange\", onVisibilityChange, { passive: true });\n  async function request(type) {\n    if (!isSupported)\n      return;\n    wakeLock = await navigator.wakeLock.request(type);\n    isActive.value = !wakeLock.released;\n  }\n  async function release() {\n    if (!isSupported || !wakeLock)\n      return;\n    await wakeLock.release();\n    isActive.value = !wakeLock.released;\n    wakeLock = null;\n  }\n  return {\n    isSupported,\n    isActive,\n    request,\n    release\n  };\n};\n\nconst useWebNotification = (defaultOptions = {}) => {\n  const {\n    window = defaultWindow\n  } = defaultOptions;\n  const isSupported = !!window && \"Notification\" in window;\n  const notification = ref(null);\n  const requestPermission = async () => {\n    if (!isSupported)\n      return;\n    if (\"permission\" in Notification && Notification.permission !== \"denied\")\n      await Notification.requestPermission();\n  };\n  const onClick = createEventHook();\n  const onShow = createEventHook();\n  const onError = createEventHook();\n  const onClose = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported)\n      return;\n    await requestPermission();\n    const options = Object.assign({}, defaultOptions, overrides);\n    notification.value = new Notification(options.title || \"\", options);\n    notification.value.onclick = (event) => onClick.trigger(event);\n    notification.value.onshow = (event) => onShow.trigger(event);\n    notification.value.onerror = (event) => onError.trigger(event);\n    notification.value.onclose = (event) => onClose.trigger(event);\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  tryOnMounted(async () => {\n    if (isSupported)\n      await requestPermission();\n  });\n  tryOnScopeDispose(close);\n  if (isSupported && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n};\n\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = ref(\"CONNECTING\");\n  const wsRef = ref();\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  const close = (code = 1e3, reason) => {\n    if (!wsRef.value)\n      return;\n    explicitlyClosed = true;\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n  };\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    const ws = new WebSocket(url, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    explicitlyClosed = false;\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      wsRef.value = void 0;\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = \"ping\",\n      interval = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(() => send(message, false), interval, { immediate: false });\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (immediate)\n    _init();\n  if (autoClose) {\n    useEventListener(window, \"beforeunload\", () => close());\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    close();\n    retried = 0;\n    _init();\n  };\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(url, workerOptions, options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = function post2(val) {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(val);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    worker.value = new Worker(url, workerOptions);\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nconst jobRunner = (userFunc) => (e) => {\n  const userFuncArgs = e.data[0];\n  return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n    postMessage([\"SUCCESS\", result]);\n  }).catch((error) => {\n    postMessage([\"ERROR\", error]);\n  });\n};\n\nconst depsParser = (deps) => {\n  if (deps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  return `importScripts(${depsString})`;\n};\n\nconst createWorkerBlobUrl = (fn, deps) => {\n  const blobCode = `${depsParser(deps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n};\n\nconst useWebWorkerFn = (fn, options = {}) => {\n  const {\n    dependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = ref(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = ref();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(() => workerTerminate(\"TIMEOUT_EXPIRED\"), timeout);\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    promise.value = {\n      resolve,\n      reject\n    };\n    worker.value && worker.value.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\"[useWebWorkerFn] You can only run one instance of the worker at a time.\");\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n};\n\nfunction useWindowFocus({ window = defaultWindow } = {}) {\n  if (!window)\n    return ref(false);\n  const focused = ref(window.document.hasFocus());\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  });\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  });\n  return focused;\n}\n\nfunction useWindowScroll({ window = defaultWindow } = {}) {\n  if (!window) {\n    return {\n      x: ref(0),\n      y: ref(0)\n    };\n  }\n  const x = ref(window.pageXOffset);\n  const y = ref(window.pageYOffset);\n  useEventListener(\"scroll\", () => {\n    x.value = window.pageXOffset;\n    y.value = window.pageYOffset;\n  }, {\n    capture: false,\n    passive: true\n  });\n  return { x, y };\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Infinity,\n    initialHeight = Infinity,\n    listenOrientation = true\n  } = options;\n  const width = ref(initialWidth);\n  const height = ref(initialHeight);\n  const update = () => {\n    if (window) {\n      width.value = window.innerWidth;\n      height.value = window.innerHeight;\n    }\n  };\n  update();\n  tryOnMounted(update);\n  useEventListener(\"resize\", update, { passive: true });\n  if (listenOrientation)\n    useEventListener(\"orientationchange\", update, { passive: true });\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, SwipeDirection, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, computedAsync, computedInject, createFetch, createUnrefFn, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, setSSRHandler, templateRef, unrefElement, useActiveElement, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClamp, useClipboard, useColorMode, useConfirmDialog, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, usePermission, usePointer, usePointerSwipe, usePreferredColorScheme, usePreferredDark, usePreferredLanguages, useRafFn, useRefHistory, useResizeObserver, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSwipe, useTemplateRefsList, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n", "<template>\r\n  <div class=\"ai-home-lazy-img__waterfall\" ref=\"wrappRef\" @scroll=\"handleScrollChange\">\r\n    <div class=\"img__waterfall__container\" ref=\"imgWaterfallRef\" style=\"overflow: hidden\">\r\n      <div class=\"img__ls__box\" v-for=\"(v, index) in imgWaterfallList\" :key=\"index\">\r\n        <div class=\"img__box\" v-for=\"child in v.children\" :key=\"child._id\">\r\n          <template-card\r\n            :templateInfo=\"{\r\n              key: child._id,\r\n              src: child.url,\r\n              name: child.name,\r\n              size: { w: child.width, h: child.height },\r\n              type: 'imgWaterfall',\r\n              userId: child.userId\r\n            }\"\r\n            :params=\"child\"\r\n            @preview=\"handlePreview(child)\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <el-empty v-if=\"imgWaterfallList.length === 0 && isReady\" description=\"没有数据\"></el-empty>\r\n      <div v-else>\r\n        <div v-if=\"imgWaterfallLoading\" class=\"design-list-no-more\">加载中...</div>\r\n        <div v-if=\"listNoMore\" class=\"design-list-no-more\">没有更多了</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <ImageViewer :material=\"material\" :showViewer=\"showViewer\" @close=\"showViewer = false\" />\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { materialList } from \"@/api/common\";\r\nimport { materialStore } from \"@/store\";\r\nimport { ElNotification } from \"element-plus\";\r\nimport { defineComponent, onMounted, ref, watch } from \"vue\";\r\nimport TemplateCard from \"./templateCard.vue\";\r\nimport ImageViewer from \"../../components/image-view.vue\";\r\nimport { debounce } from \"lodash\";\r\n\r\nexport default defineComponent({\r\n  components: {\r\n    TemplateCard,\r\n    ImageViewer\r\n  },\r\n  setup() {\r\n    let imgWaterfallList = ref<any>([]);\r\n    let imgWaterfallRef = ref<any>(null);\r\n    let wrappRef = ref<any>(null);\r\n    let pageCount = ref<number>(1);\r\n    let listNoMore = ref<boolean>(false);\r\n    let imgWaterfallLoading = ref<boolean>(false);\r\n    let showViewer = ref<boolean>(false);\r\n    let material = ref<Record<string, any>>({});\r\n    let isReady = ref<boolean>(false);\r\n    let width = ref(0);\r\n    let store = materialStore(); // 素材Store\r\n    const total = ref<number>(0);\r\n\r\n    const setMenuId = () => {\r\n      pageCount.value = 1;\r\n      listNoMore.value = false;\r\n      getImgWaterfall();\r\n    };\r\n    const handlePreview = (info) => {\r\n      material.value = info;\r\n      showViewer.value = true;\r\n    };\r\n    const getImgWaterfall = async () => {\r\n      if (listNoMore.value) {\r\n        return false;\r\n      }\r\n      imgWaterfallLoading.value = true;\r\n      const data = await materialList({\r\n        appId: +store.appId !== 1 ? store.appId : \"\",\r\n        lverId: store.versionId.join(\",\") || \"\",\r\n        moduleId: store.tagsId.join(\",\") || \"\",\r\n        categoryId: store.categoryId,\r\n        page: pageCount.value,\r\n        pageSize: 20\r\n      });\r\n      if (data.data.code === 0) {\r\n        // 数据分成5组-加载就可以，然后懒加载，懒加载判断从上次加到的组开始加数组\r\n        isReady.value = true;\r\n        total.value = +data.data.data.total;\r\n        data.data.data.list.length < 10 && (listNoMore.value = true);\r\n        pageCount.value === 1 && (imgWaterfallList.value = []);\r\n        data.data.data.list.forEach((e: any, index: number) => {\r\n          if (index < 5 && pageCount.value === 1) {\r\n            let _h = e.height * (width.value / e.width);\r\n            imgWaterfallList.value[index] = {\r\n              height: (_h > 400 ? 400 : _h) + 36,\r\n              children: []\r\n            };\r\n            imgWaterfallList.value[index].children.push(e);\r\n          } else {\r\n            // 计算高度比较小的添加\r\n            let heigthArr: Array<number> = [];\r\n            for (let v in imgWaterfallList.value) {\r\n              heigthArr.push(imgWaterfallList.value[v].height);\r\n            }\r\n            for (let v in imgWaterfallList.value) {\r\n              let _h = e.height * (width.value / e.width);\r\n              if (imgWaterfallList.value[v].height === heigthArr.sort((a: number, b: number) => a - b)[0]) {\r\n                imgWaterfallList.value[v].height += (_h > 400 ? 400 : _h) + 36;\r\n                imgWaterfallList.value[v].children.push(e);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        ElNotification({\r\n          message: data.data.msg,\r\n          type: \"error\",\r\n          duration: 1500\r\n        });\r\n      }\r\n      imgWaterfallLoading.value = false;\r\n    };\r\n\r\n    watch(\r\n      () => store.getMaterialData(),\r\n      () => {\r\n        pageCount.value = 1;\r\n        imgWaterfallList.value = [];\r\n        listNoMore.value = false;\r\n        getImgWaterfall();\r\n      },\r\n      { immediate: true, deep: true }\r\n    );\r\n\r\n    onMounted(() => {\r\n      width.value = imgWaterfallRef.value.offsetWidth / 5 - 20;\r\n    });\r\n\r\n    // scroll\r\n    const handleScrollChange = debounce((event: any) => {\r\n      if (listNoMore.value) {\r\n        return false;\r\n      }\r\n      const { scrollHeight, scrollTop, clientHeight } = event.target;\r\n      const sh = parseInt(scrollHeight);\r\n      const st = parseInt(scrollTop);\r\n      const ch = parseInt(clientHeight);\r\n      if (sh - (st + ch) === 0 && pageCount.value !== total.value) {\r\n        pageCount.value++;\r\n        getImgWaterfall();\r\n      }\r\n    }, 300);\r\n\r\n    return {\r\n      setMenuId,\r\n      getImgWaterfall,\r\n      handleScrollChange,\r\n      imgWaterfallList,\r\n      imgWaterfallRef,\r\n      wrappRef,\r\n      isReady,\r\n      imgWaterfallLoading,\r\n      listNoMore,\r\n      showViewer,\r\n      material,\r\n      handlePreview\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.ai-home-lazy-img__waterfall {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n  min-height: calc(100vh - 134px);\r\n  position: relative;\r\n  padding-top: 20px;\r\n  padding-bottom: 102px;\r\n  box-sizing: border-box;\r\n  &::-webkit-scrollbar {\r\n    width: 0 !important;\r\n  }\r\n  .design-list-no-more {\r\n    clear: both;\r\n    text-align: center;\r\n    color: #aaa;\r\n    padding: 20px;\r\n    font-size: 14px;\r\n  }\r\n  .img__ls__box {\r\n    width: 20%;\r\n    float: left;\r\n    padding-right: 20px;\r\n    box-sizing: border-box;\r\n    &:nth-child(5n) {\r\n      padding-right: 0;\r\n    }\r\n  }\r\n  .img__box {\r\n    margin-bottom: 16px;\r\n    width: 100%;\r\n    border-radius: 4px;\r\n    vertical-align: middle;\r\n  }\r\n  .item {\r\n    position: absolute;\r\n    top: 5px;\r\n    left: 5px;\r\n    right: 5px;\r\n    bottom: 5px;\r\n    font-size: 1.2em;\r\n    color: rgb(0, 158, 107);\r\n  }\r\n  .item:after {\r\n    content: attr(index);\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    -webkit-transform: translate(-50%, -50%);\r\n    -ms-transform: translate(-50%, -50%);\r\n  }\r\n  .wf-transition {\r\n    transition: opacity 0.3s ease;\r\n    -webkit-transition: opacity 0.3s ease;\r\n  }\r\n  .wf-enter {\r\n    opacity: 0;\r\n  }\r\n  .item-move {\r\n    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);\r\n    -webkit-transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);\r\n  }\r\n}\r\n</style>\r\n", "<template>\n  <teleport to=\"body\">\n    <transition name=\"viewer-fade\" appear>\n      <div ref=\"wrapper\" v-if=\"showViewer\" class=\"image-view-wrapper\">\n        <div class=\"image-view-mask\" />\n        <div class=\"image-view-content\">\n          <!-- BAR -->\n          <div class=\"image-view-bar\">\n            <div class=\"image-view-title\"></div>\n            <div class=\"image-view-btn\">\n              <el-icon @click=\"handleActions('zoomOut')\">\n                <ZoomOut />\n              </el-icon>\n              <el-icon @click=\"handleActions('zoomIn')\">\n                <ZoomIn />\n              </el-icon>\n              <el-icon @click=\"handleActions('anticlockwise')\">\n                <RefreshLeft />\n              </el-icon>\n              <el-icon @click=\"handleActions('clockwise')\">\n                <RefreshRight />\n              </el-icon>\n            </div>\n          </div>\n          <!-- CANVAS -->\n          <div class=\"image-view-canvas\" @click=\"handleClose\">\n            <img loading=\"lazy\" :key=\"material.url\" @mousedown=\"handleMouseDown\" :src=\"material.url && material.url.indexOf('?') < 0 ? `${material.url}?imageView2/0/format/webp` : material.url\" :style=\"imgStyle\" class=\"image-view-img\" />\n          </div>\n        </div>\n        <div class=\"image-view-actions\">\n          <div class=\"image-view-actions-content\">\n            <div class=\"image-view-actions-content__title\">\n              <span>基础信息</span>\n            </div>\n            <div class=\"image-view-actions-content__item\">\n              <label>文件大小</label>\n              <span>{{ (material?.size / 1000).toFixed(2) }}mb</span>\n            </div>\n            <div class=\"image-view-actions-content__item\">\n              <label>尺寸</label>\n              <span>{{ material.width }} * {{ material.height }}</span>\n            </div>\n            <div class=\"image-view-actions-content__item\">\n              <label>文件格式</label>\n              <span>{{ material?.format }}</span>\n            </div>\n            <div class=\"image-view-actions-content__item\">\n              <label>颜色类型</label>\n              <span>{{ material?.colorModel }}</span>\n            </div>\n            <div class=\"image-view-actions-content__item\">\n              <label>添加日期</label>\n              <span>{{ new Date(parseInt(material?.createTime)).toLocaleString().replace(/:\\d{1,2}$/, \" \") }}</span>\n            </div>\n            <div class=\"image-view-actions-content__item origin__path\">\n              <label>来源路径</label>\n              <div class=\"origin-path__content\">\n                <div class=\"origin-path__item\">\n                  <div class=\"tips\">\n                    <img loading=\"lazy\" :src=\"material?.app.icon\" alt=\"\" />\n                    <span>{{ material?.app?.name }} V{{ material.lver?.name }}</span>\n                  </div>\n                </div>\n                <div class=\"origin-path__item\">\n                  <div class=\"tips\">\n                    <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png\" alt=\"\" />\n                    <span>{{ material?.module.name }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"image-view-actions-content__btns\">\n                <el-button class=\"el-button-code\" type=\"primary\" @click=\"handleClickAICode\">AI原型Code</el-button>\n                <el-button type=\"primary\" @click=\"handleClickAI\">AI灵感升级</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { aiGenerated } from \"@/api/upload\";\nimport { RefreshLeft, RefreshRight, ZoomIn, ZoomOut } from \"@element-plus/icons-vue\";\nimport { useEventListener } from \"@vueuse/core\";\nimport { ElLoading, ElMessageBox } from \"element-plus\";\nimport { throttle } from \"lodash-unified\";\nimport type { CSSProperties } from \"vue\";\nimport { defineEmits, defineProps, effectScope, onMounted, ref, computed } from \"vue\";\nimport { useRouter } from \"vue-router\";\ntype ImageViewerAction = \"zoomIn\" | \"zoomOut\" | \"clockwise\" | \"anticlockwise\";\nconst EVENT_CODE = {\n  esc: \"Escape\",\n  space: \"Space\",\n  left: \"ArrowLeft\",\n  up: \"ArrowUp\",\n  right: \"ArrowRight\",\n  down: \"ArrowDown\"\n};\nconst scopeEventListener = effectScope();\nconst props = withDefaults(\n  defineProps<{\n    material: Record<string, any>;\n    showViewer: boolean;\n    minScale?: number;\n    maxScale?: number;\n    zoomRate?: number;\n    closeOnPressEscape: boolean;\n  }>(),\n  {\n    minScale: 0.2,\n    maxScale: 6,\n    zoomRate: 1.2,\n    closeOnPressEscape: false\n  }\n);\nconst emits = defineEmits({\n  close: () => true\n});\nconst router = useRouter();\nonMounted(() => {\n  registerEventListener();\n});\nfunction registerEventListener() {\n  const keydownHandler = throttle((e: KeyboardEvent) => {\n    switch (e.code) {\n      // ESC\n      case EVENT_CODE.esc:\n        props.closeOnPressEscape && handleClose();\n        break;\n      // SPACE\n      case EVENT_CODE.space:\n        break;\n      // UP_ARROW\n      case EVENT_CODE.up:\n        handleActions(\"zoomIn\");\n        break;\n      // DOWN_ARROW\n      case EVENT_CODE.down:\n        handleActions(\"zoomOut\");\n        break;\n    }\n  });\n  const mousewheelHandler = throttle((e: WheelEvent) => {\n    const delta = e.deltaY || e.deltaX;\n    handleActions(delta < 0 ? \"zoomIn\" : \"zoomOut\", {\n      zoomRate: props.zoomRate,\n      enableTransition: false\n    });\n  });\n\n  scopeEventListener.run(() => {\n    useEventListener(document, \"keydown\", keydownHandler);\n    useEventListener(document, \"wheel\", mousewheelHandler);\n  });\n}\n\nconst transform = ref({\n  scale: 1,\n  deg: 0,\n  offsetX: 0,\n  offsetY: 0,\n  enableTransition: false\n});\n\nconst imgStyle = computed(() => {\n  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value;\n  let translateX = offsetX / scale;\n  let translateY = offsetY / scale;\n\n  switch (deg % 360) {\n    case 90:\n    case -270:\n      [translateX, translateY] = [translateY, -translateX];\n      break;\n    case 180:\n    case -180:\n      [translateX, translateY] = [-translateX, -translateY];\n      break;\n    case 270:\n    case -90:\n      [translateX, translateY] = [-translateY, translateX];\n      break;\n  }\n\n  const style: CSSProperties = {\n    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n    transition: enableTransition ? \"transform .3s\" : \"\"\n  };\n  style.maxWidth = style.maxHeight = \"95%\";\n  return style;\n});\n\nfunction handleActions(action: ImageViewerAction, options = {}) {\n  const { minScale, maxScale, showViewer } = props;\n  const { zoomRate, rotateDeg, enableTransition } = {\n    zoomRate: props.zoomRate,\n    rotateDeg: 90,\n    enableTransition: true,\n    ...options\n  };\n  if (!showViewer) {\n    return;\n  }\n  switch (action) {\n    case \"zoomOut\":\n      if (transform.value.scale > minScale) {\n        transform.value.scale = Number.parseFloat((transform.value.scale / zoomRate).toFixed(3));\n      }\n      break;\n    case \"zoomIn\":\n      if (transform.value.scale < maxScale) {\n        transform.value.scale = Number.parseFloat((transform.value.scale * zoomRate).toFixed(3));\n      }\n      break;\n    case \"clockwise\":\n      transform.value.deg += rotateDeg;\n      break;\n    case \"anticlockwise\":\n      transform.value.deg -= rotateDeg;\n      break;\n  }\n  transform.value.enableTransition = enableTransition;\n}\nfunction handleMouseDown(e: MouseEvent) {\n  transform.value.enableTransition = false;\n\n  const { offsetX, offsetY } = transform.value;\n  const startX = e.pageX;\n  const startY = e.pageY;\n\n  const dragHandler = throttle((ev: MouseEvent) => {\n    transform.value = {\n      ...transform.value,\n      offsetX: offsetX + ev.pageX - startX,\n      offsetY: offsetY + ev.pageY - startY\n    };\n  });\n  const removeMousemove = useEventListener(document, \"mousemove\", dragHandler);\n  useEventListener(document, \"mouseup\", () => {\n    removeMousemove();\n  });\n\n  e.preventDefault();\n}\nconst handleClickAI = async () => {\n  ElMessageBox.confirm(\"确认以此图喂给AI生成新图?时间有点长耐心等待啊\", \"提示\", {\n    confirmButtonText: \"确定\",\n    cancelButtonText: \"取消\",\n    type: \"warning\"\n  })\n    .then(async () => {\n      const loading = ElLoading.service({\n        lock: true,\n        text: \"Loading\",\n        background: \"rgba(0, 0, 0, 0.7)\"\n      });\n\n      const res = await aiGenerated({\n        name: props.material?.module.name || \"首页\"\n      });\n\n      if (res?.data?.data) {\n        loading.close();\n        window.open(res?.data?.data);\n      }\n    })\n    .catch(() => {\n      // window.open(res?.data?.data);\n    });\n};\nconst handleClickAICode = () => {\n  const { href } = router.resolve({\n    path: \"/screenCode\",\n    query: {\n      id: props.material._id\n    }\n  });\n\n  window.open(href, \"_blank\");\n};\n\nconst handleClose = () => {\n  transform.value = {\n    scale: 1,\n    deg: 0,\n    offsetX: 0,\n    offsetY: 0,\n    enableTransition: false\n  };\n  emits(\"close\");\n};\n</script>\n<style lang=\"less\" scoped>\n.image-view-wrapper {\n  position: fixed;\n  top: 64px;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  display: flex;\n  z-index: 999;\n  .image-view-mask {\n    width: calc(100% - 300px);\n    height: 100%;\n    position: absolute;\n    top: 0;\n    left: 0;\n    background: rgba(0, 0, 0, 0.75);\n  }\n  .image-view-content {\n    flex: 0 0 calc(100% - 300px);\n    height: 100%;\n    position: relative;\n  }\n  .image-view-bar {\n    height: 60px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n  .image-view-btn {\n    display: inline-flex;\n    .el-icon {\n      font-size: 18px;\n      color: #fff;\n      margin: 0 10px;\n      cursor: pointer;\n    }\n  }\n  .image-view-canvas {\n    height: calc(100% - 124px);\n    overflow: overlay;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .image-view-actions {\n    background: #000;\n    height: 100%;\n    width: 300px;\n    &-content {\n      width: 100%;\n      padding: 30px;\n      display: flex;\n      flex-direction: column;\n      &__title {\n        width: 100%;\n        height: 37px;\n        font-family: PingFangSC-Regular;\n        font-size: 14px;\n        color: #ffffff;\n        letter-spacing: 0;\n        font-weight: 400;\n        span {\n          font-family: PingFangSC-Medium;\n          font-size: 14px;\n          color: #ffffff;\n          letter-spacing: 0;\n          font-weight: 500;\n        }\n      }\n      &__item {\n        width: 100%;\n        margin-bottom: 15px;\n\n        label,\n        span {\n          font-family: PingFangSC-Regular;\n          font-size: 14px;\n          color: #ffffff;\n          letter-spacing: 0;\n          font-weight: 400;\n        }\n\n        label {\n          width: 101px;\n          text-align: left;\n          display: inline-block;\n        }\n\n        &.origin__path {\n          display: flex;\n          flex-direction: column;\n          margin-top: 25px;\n          .origin-path__content {\n            width: 100%;\n            flex-direction: column;\n            margin-top: 16px;\n\n            .origin-path__item {\n              display: flex;\n              align-items: center;\n              justify-content: flex-start;\n              .tips {\n                padding: 5px 10px;\n                margin-bottom: 15px;\n                background: rgba(255, 255, 255, 0.2);\n                border-radius: 14px;\n                font-size: 14px;\n                display: inline-block;\n              }\n              img {\n                width: 18px;\n                height: 18px;\n                border-radius: 4.5px;\n                margin-right: 10px;\n                vertical-align: text-top;\n              }\n            }\n          }\n        }\n      }\n      &__btns {\n        display: flex;\n        flex-direction: column;\n        .el-button {\n          width: max-content;\n          margin: 5px 0;\n        }\n        .el-button-code {\n          background-color: transparent;\n          border: none;\n          opacity: 0.8;\n          background-image: linear-gradient(90deg, #978de5 4%, #8664d2 51%, #713eff 96%);\n          &:hover {\n            opacity: 1;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <div class=\"home-body\">\n    <!-- 左侧导航部分内容 start -->\n    <div class=\"home-body__left\">\n      <!-- 分类导航list start -->\n      <div\n        :class=\"{\n          'category-nav-selector': true,\n          'category-nav-selector__active': store.themeShow\n        }\"\n      >\n        <div class=\"category-nav-selector__btn\" v-click-outside=\"handleOutsideClick\" @click=\"handleToggle\">\n          <span>全部{{ currentDropdownMenuName }}</span>\n          <i class=\"iconfont icon-jiantoushouqi\" :style=\"{ transform: isReverse ? 'rotate(180deg)' : 'rotate(0deg)', color: isReverse ? '#5C54F0' : '' }\"></i>\n        </div>\n        <!-- 分类loop list start -->\n        <div\n          ref=\"dropdownRef\"\n          :class=\"{\n            'category-nav-selector__dropdown': true,\n            'category-nav-selector-dropdown-reverse__false': !isReverse,\n            'category-nav-selector-dropdown-reverse__true': isReverse\n          }\"\n        >\n          <div class=\"category-nav-selector-dropdown__item\" v-for=\"item in categoryList\" :key=\"item._id\" @click=\"handleSelectedMenuItem(item._id)\">\n            <span>全部{{ item.name }}</span>\n            <i v-if=\"item._id === material.categoryId\" class=\"iconfont icon-duigou icon-duigou__active\"></i>\n            <div v-if=\"item._id === material.categoryId\" class=\"category-nav-selector-dropdown-item__bg\"></div>\n          </div>\n        </div>\n        <!-- <div v-if=\"routerListData.length === 0\" class=\"category-nav-selector-no__data\">暂无数据</div> -->\n        <!-- 分类loop list end -->\n      </div>\n      <!-- 分类导航list end -->\n\n      <!-- 灵感集导航 start -->\n      <sp-nav @tabs-change=\"tabsChange\" :data=\"routerListData\" :default-id=\"material.appId\"></sp-nav>\n      <!-- 灵感集导航 end -->\n    </div>\n    <!-- 左侧导航部分内容 end -->\n\n    <!-- 右侧内容 start -->\n    <div class=\"home-body__right\">\n      <home-body-header :app-or-design=\"appOrDesign\"></home-body-header>\n      <home-body-container></home-body-container>\n    </div>\n    <!-- 右侧内容 start -->\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport SpNav from \"@/views/layouts/components/spNav.vue\";\nimport { materialStore, themeStore } from \"@/store\";\nimport HomeBodyHeader from \"@/views/layouts/home/<USER>/homeBodyHeader.vue\";\nimport HomeBodyContainer from \"@/views/layouts/home/<USER>/homeBodyContainer.vue\";\nimport { appsList } from \"@/api/common\";\nimport { onMounted, ref, computed } from \"vue\";\nimport { GetCategoryList } from \"@/api/admin\";\nimport { ElMessage } from \"element-plus\";\n\nconst material = materialStore(); // 素材Store\nconst store = themeStore(); // 主题store\n\nconst appOrDesign = ref<number>(1); // 1: 'app' or 'it'\n\nconst routerListData = ref<any>([]);\n\nconst tabsChange = (item: any) => {\n  appOrDesign.value = +item._id;\n  material.updateMaterialInfo(item);\n};\n\nconst initData = async () => {\n  await getCategoryList();\n};\n\n// 分类下拉\nconst dropdownRef = ref<any>(null);\nconst categoryList = ref<any>([]);\nconst currentDropdownMenuName = computed(() => {\n  return categoryList.value.find((item: any) => item._id === material.categoryId)?.name;\n});\nconst isReverse = ref<boolean>(false);\n\nconst handleOutsideClick = () => {\n  isReverse.value = false;\n};\n\nconst handleToggle = () => {\n  isReverse.value = true;\n};\n\n// 选中某一个分类\nconst handleSelectedMenuItem = (_id: string) => {\n  material.updateCategoryId(_id);\n  getRouterListData();\n  isReverse.value = false;\n};\n\nconst getCategoryList = async () => {\n  try {\n    const res = await GetCategoryList({});\n    if (res.code !== 0) {\n      throw new Error(res.msg);\n    }\n    if (res.data.length === 0) {\n      ElMessage.error(\"暂无数据\");\n      return;\n    }\n    categoryList.value = [...categoryList.value, ...res.data];\n\n    handleSelectedMenuItem(res.data[0]._id);\n    await getRouterListData();\n  } catch (error: any) {\n    ElMessage.error(error.message);\n  }\n};\n\nconst getRouterListData = async () => {\n  const data = await appsList({\n    categoryId: material.categoryId\n  });\n  routerListData.value = data.data.data.list;\n};\n\nonMounted(() => {\n  initData();\n});\n</script>\n<style lang=\"less\" scoped>\n.home-body {\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  .home-body__left {\n    width: 250px;\n    display: flex;\n    flex-direction: column;\n    .category-nav-selector {\n      position: relative;\n      width: 100%;\n      padding: 10px 20px 0;\n      box-sizing: border-box;\n      background: #ffffff;\n      .category-nav-selector__btn {\n        position: relative;\n        width: 100%;\n        height: 44px;\n        background: #f4f4f4;\n        color: #171717;\n        border-radius: 6px;\n        margin-bottom: 15px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n        cursor: pointer;\n        i {\n          position: absolute;\n          right: 20px;\n          font-size: 10px;\n          color: #c0c4cc;\n          transition: transform 0.3s;\n          transform: rotate(0);\n        }\n      }\n      .category-nav-selector__dropdown {\n        width: 210px;\n        position: absolute;\n        top: 50px;\n        background: hsla(0, 0%, 100%, 0.98);\n        box-shadow: 0 2px 25px 0 rgba(0, 0, 0, 0.21);\n        z-index: 10;\n        border: none;\n        border-radius: 1px;\n        box-sizing: border-box;\n        overflow-x: hidden;\n        overflow-y: auto;\n        margin-top: 5px;\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        .category-nav-selector-dropdown__item {\n          position: relative;\n          height: 40px;\n          font-size: 14px;\n          color: #171717;\n          font-weight: 400;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          padding: 0 20px;\n          box-sizing: border-box;\n          cursor: pointer;\n          span {\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n          &:first-child {\n            margin-top: 10px;\n          }\n          i {\n            font-size: 12px;\n            &.icon-duigou__active {\n              color: var(--el-color-primary);\n            }\n          }\n          .category-nav-selector-dropdown-item__bg {\n            position: absolute;\n            top: 0;\n            left: 0;\n            background: var(--el-color-primary);\n            transition: all 0.2s;\n            width: 100%;\n            height: 40px;\n            opacity: 0.05;\n          }\n        }\n        &.category-nav-selector-dropdown-reverse__false {\n          max-height: 0;\n          transition: max-height 0.3s ease-in;\n        }\n        &.category-nav-selector-dropdown-reverse__true {\n          transition: max-height 0.3s ease-out;\n          transform-origin: 50% 0;\n          animation: slide-down 0.3s ease-out;\n          -webkit-animation: slide-down 0.3s ease-out;\n          max-height: 316px;\n          padding: 0 0 10px;\n        }\n      }\n      .category-nav-selector-no__data {\n        width: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n      }\n      &.category-nav-selector__active {\n        background: #26282b;\n        .category-nav-selector__btn {\n          background: rgba(255, 255, 255, 0.05);\n          span {\n            color: #ffffff;\n          }\n        }\n        .category-nav-selector__dropdown {\n          background: rgba(51, 51, 51);\n          .category-nav-selector-dropdown__item {\n            span {\n              color: #ffffff;\n            }\n          }\n        }\n      }\n    }\n  }\n  .home-body__right {\n    width: 100%;\n    position: relative;\n    height: calc(100vh - 64px);\n    overflow: hidden;\n    display: flex;\n    flex-direction: column;\n  }\n}\n@-webkit-keyframes slide-down {\n  0% {\n    height: 0;\n  }\n\n  100% {\n    height: 316px;\n  }\n}\n\n@-webkit-keyframes slide-up {\n  0% {\n    height: 316px;\n  }\n\n  100% {\n    height: 0;\n  }\n}\n</style>\n", "import axios from \"@/api\";\nimport qs from \"qs\";\n// 上传应用\nexport const appsAdd = async (args) => {\n  return await axios.post(\"/apps/add\", qs.stringify(args));\n};\n\n// 上传应用的版本\nexport const appsAddVersion = async (args) => {\n  return await axios.get(\"/apps/addversion\", {\n    params: args\n  });\n};\n\n// 上传模块\nexport const appsAddModule = async (args) => {\n  return await axios.post(\"/apps/addModule\", qs.stringify(args));\n};\n\n// 获取分类列表\nexport const GetCategoryList = async (args) => {\n  const res = await axios.get(\"/category/list\", {\n    params: args\n  });\n  return res.data;\n};\n\n// 添加分类\nexport const AddCategory = async (args) => {\n  const res = await axios.get(\"/category/add\", {\n    params: args\n  });\n  return res.data;\n};\n\n// 编辑分类\nexport const EditCategory = async (args) => {\n  const res = await axios.get(\"/category/update\", {\n    params: args\n  });\n  return res.data;\n};\nexport const GetAppList = async (args) => {\n  const res = await axios.get(\"/apps/list\", {\n    params: args\n  });\n  return res.data;\n};\n\n// 添加应用\nexport const AddApp = async (args) => {\n  const res = await axios.get(\"/apps/add\", {\n    params: args\n  });\n  return res.data;\n};\n\n// 编辑应用\nexport const EditApp = async (args) => {\n  const res = await axios.get(\"/apps/update\", {\n    params: args\n  });\n  return res.data;\n};\n\n//\nexport const GetLverList = async (args) => {\n  const res = await axios.get(\"/apps/lverList\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const AddLver = async (args) => {\n  const res = await axios.get(\"/apps/addlver\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const GetModuleList = async (args) => {\n  const res = await axios.get(\"/apps/moduleList\", {\n    params: args\n  });\n  return res.data;\n};\n\nexport const AddModule = async (args) => {\n  const res = await axios.post(\"/apps/addModule\", qs.stringify(args));\n  return res.data;\n};\n\nexport const UpdateModule = async (args) => {\n  const res = await axios.post(\"/apps/updateModule\", qs.stringify(args));\n  return res.data;\n};\n\n/**\n * 更新material\n */\nexport const UpdateMaterial = async (args) => {\n  const res = await axios.post(\"/material/update\", qs.stringify(args));\n  return res.data;\n};\n\n/**\n * 删除material\n */\nexport const delMaterial = async (args) => {\n  const res = await axios.get(\"/material/delete\", {\n    params: args\n  });\n  return res.data;\n};\n"], "names": ["throttle", "func", "wait", "options", "leading", "trailing", "TypeError", "isObject", "debounce", "max<PERSON><PERSON>", "store", "themeStore", "material", "materialStore", "emits", "__emit", "props", "__props", "selectedId", "ref", "scroll", "event", "target", "scrollLeft", "watch", "ids", "n", "value", "item", "includes", "_id", "filter", "e", "push", "console", "log", "materialType", "updateVersionInfo", "updateTagsInfo", "materialStoreData", "isToggle", "container", "clientWidth", "scrollWidth", "listItemW", "scrollListItemW", "underWayScrollWidth", "appTabsData", "versionTabsData", "preChange", "header", "document", "getElementById", "nextChange", "design", "updateScrollWidth", "distance", "getVersionList", "async", "data", "versionList", "appId", "code", "initData", "moduleList", "length", "nextTick", "cur", "querySelectorAll", "offsetWidth", "newId", "onMounted", "userInfo", "userInfoStore", "emit", "templateCard", "isCollection", "width", "bgArr", "background", "Math", "floor", "random", "window", "addEventListener", "handleClick", "collect", "userId", "id", "key", "isCollect", "ElNotification", "type", "message", "duration", "msg", "downlondImage", "_a", "isClient", "noop", "navigator", "userAgent", "test", "defaultWindow", "useEventListener", "args", "listener", "cleanup", "stopWatch", "elRef", "plain", "unref", "$el", "unrefElement", "el", "removeEventListener", "immediate", "flush", "stop", "fn", "getCurrentScope", "onScopeDispose", "_global", "globalThis", "global", "self", "globalKey", "SwipeDirection", "SwipeDirection2", "_sfc_main$2", "defineComponent", "components", "TemplateCard", "ImageViewer", "EVENT_CODE", "scopeEventListener", "effectScope", "router", "useRouter", "keydownHandler", "closeOnPressEscape", "handleClose", "handleActions", "mousewheelHandler", "deltaY", "deltaX", "zoomRate", "enableTransition", "run", "transform", "scale", "deg", "offsetX", "offsetY", "imgStyle", "computed", "translateX", "translateY", "style", "transition", "max<PERSON><PERSON><PERSON>", "maxHeight", "action", "minScale", "maxScale", "showViewer", "rotateDeg", "Number", "parseFloat", "toFixed", "handleMouseDown", "startX", "pageX", "startY", "pageY", "<PERSON><PERSON><PERSON><PERSON>", "ev", "removeMousemove", "preventDefault", "handleClickAI", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "loading", "ElLoading", "service", "lock", "text", "res", "aiGenerated", "name", "module", "close", "open", "catch", "handleClickAICode", "href", "resolve", "path", "query", "setup", "imgWaterfallList", "imgWaterfallRef", "wrappRef", "pageCount", "listNoMore", "imgWaterfallLoading", "isReady", "total", "getImgWaterfall", "materialList", "lverId", "versionId", "join", "moduleId", "tagsId", "categoryId", "page", "pageSize", "list", "for<PERSON>ach", "index", "_h", "height", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "sort", "a", "b", "getMaterialData", "deep", "handleScrollChange", "scrollHeight", "scrollTop", "clientHeight", "parseInt", "setMenuId", "handlePreview", "info", "class", "overflow", "_hoisted_2", "_component_ImageViewer", "_resolveComponent", "_openBlock", "_createElementBlock", "_Fragment", "_createElementVNode", "onScroll", "_cache", "_ctx", "_hoisted_1", "_renderList", "child", "_createVNode", "_component_template_card", "templateInfo", "src", "url", "size", "w", "h", "_createBlock", "_component_el_empty", "description", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "appOrDesign", "routerListData", "tabsChange", "updateMaterialInfo", "dropdownRef", "categoryList", "currentDropdownMenuName", "find", "isReverse", "handleOutsideClick", "handleToggle", "handleSelectedMenuItem", "updateCategoryId", "getCategoryList", "axios", "get", "params", "GetCategoryList", "Error", "ElMessage", "error", "getRouterListData", "appsList"], "mappings": "q7BAkDA,SAASA,GAASC,EAAMC,EAAMC,GACxB,IAAAC,GAAU,EACVC,GAAW,EAEX,GAAe,mBAARJ,EACH,MAAA,IAAIK,UAnDQ,uBAyDb,OAJHC,EAASJ,KACXC,EAAU,YAAaD,IAAYA,EAAQC,QAAUA,EACrDC,EAAW,aAAcF,IAAYA,EAAQE,SAAWA,GAEnDG,GAASP,EAAMC,EAAM,CAC1BE,QAAWA,EACXK,QAAWP,EACXG,SAAYA,GAEhB,2NCxCA,MAAAK,EAAAC,IACAC,EAAAC,IAEA,IAAAC,EAAAC,EAEAC,EAAAC,EAeA,MAAAC,EAAAC,EAAA,IAgBAC,EAAAC,IACEP,EAAA,oBAAAO,EAAAC,OAAAC,WAAA,SAGFC,GAAA,IAAAZ,EAAAa,MACiBC,IAEbR,EAAAS,MAAAD,CAAA,4VArBJ,CAAAE,IACEV,EAAAS,MAAAE,SAAAD,EAAAE,KACEZ,EAAAS,MAAAT,EAAAS,MAAAI,QAAAC,GAAAA,IAAAJ,EAAAE,MAEAZ,EAAAS,MAAAM,KAAAL,EAAAE,KAEFI,QAAAC,IAAAjB,EAAAS,MAAA,YACA,YAAAX,EAAAoB,aACExB,EAAAyB,kBAAAnB,EAAAS,OAEAf,EAAA0B,eAAApB,EAAAS,MAAwC,qyBCN5C,MAAAjB,EAAAC,IACA4B,EAAA1B,IAEA2B,EAAArB,GAAA,GAEAsB,EAAAtB,EAAA,GACAuB,EAAAvB,EAAA,GACAwB,EAAAxB,EAAA,GACAyB,EAAAzB,EAAA,GACA0B,EAAA1B,EAAA,GACA2B,EAAA3B,EAAA,GACA4B,EAAA5B,EAAA,IACA6B,EAAA7B,EAAA,IAKA8B,EAAA,KACE,MAAAC,EAAAC,SAAAC,eAAA,kBACAF,EAAA3B,WAAA,IACEuB,EAAAnB,OAAA,IACAuB,EAAA3B,WAAAuB,EAAAnB,MAAwC,EAO5C0B,EAAA,KACE,MAAAC,EAAAH,SAAAC,eAAA,kBACAE,EAAA/B,YAAAoB,EAAAhB,QAEEmB,EAAAnB,OAAA,IAEA2B,EAAA/B,WAAAuB,EAAAnB,MAAwC,EAQ5C4B,EAAAC,IACEV,EAAAnB,MAAA6B,CAAA,EA8BFC,EAAAC,MAAA5B,IACE,MAAA6B,QAAAC,EAAA,CAAAC,MAAA/B,IACA,IAAA6B,EAAAA,KAAAG,KACEd,EAAArB,MAAAgC,EAAAA,KAAAA,KAEAX,EAAArB,MAAA,EAAyB,EAO7BoC,EAAA,KApCAL,WACE,MAAAC,QAAAK,EAAA,CAAA,GACA,GAAA,IAAAL,EAAAA,KAAAG,MAAAH,EAAAA,KAAAA,KAAAM,OAAA,EAAA,CACElB,EAAApB,MAAAgC,EAAAA,KAAAA,KACAlB,EAAAd,MAAAwB,SAAAC,eAAA,cAAAV,YACAA,EAAAf,MAAAwB,SAAAC,eAAA,kBAAAV,kBACAwB,IACA,IAAAC,EAAAhB,SAAAiB,iBAAA,yBACAzB,EAAAhB,MAAAwC,EAAA,GAAAE,YAAAV,EAAAA,KAAAA,KAAAM,OAAA,GAAAN,EAAAA,KAAAA,KAAAM,OACArB,EAAAjB,MAAAwB,SAAAC,eAAA,kBAAAV,YACAG,EAAAlB,MAAAwB,SAAAC,eAAA,kBAAAT,YACAF,EAAAd,MAAA,IAAAgB,EAAAhB,QAAAe,EAAAf,QAAAgB,EAAAhB,MACEa,EAAAb,OAAA,EAEAa,EAAAb,OAAA,CACF,MAEAoB,EAAApB,MAAA,EAAqB,KAqBvB8B,EAAA,GAAA,SAGFjC,GAAA,IAAAe,EAAAsB,QAC0BS,IAEtBb,EAAAa,EAAA,IAIJC,GAAA,m+CC9HA,MAAAC,EAAAC,IAyCAC,EAAA3D,EACA4D,EAAAxD,EAAA,MAGAyD,EAAAzD,GAAA,GA4BA,IAAA0D,EAAA1D,EAAA,GACA,MAAA2D,EAAA3D,EAAA,CAAA,UAAA,UAAA,UAAA,UAAA,YACA,IAAA4D,EAAA5D,EAAA,IAEAoD,GAAA,KACEM,EAAAlD,MAAAgD,EAAAhD,MAAA0C,YACAU,EAAApD,MAAAmD,EAAAnD,MAAAqD,KAAAC,MAAA,EAAAD,KAAAE,WACAC,OAAAC,iBAAA,UAAA,KACET,EAAAhD,QAAAkD,EAAAlD,MAAAgD,EAAAhD,MAAA0C,YAAA,GAAwD,IAI5D,MAAAgB,EAAA,KACEX,EAAA,UAAA,+eAxCFhB,OAAA9B,IACE,MAAA+B,QAAA2B,EAAA,CAA2BC,OAAA3D,EAAA2D,OACZC,GAAA5D,EAAA6D,IACJC,UAAA,IAGX,IAAA/B,EAAAA,KAAAG,MACEc,EAAAjD,OAAA,EACAgE,GAAA,CAAeC,KAAA,UACPC,QAAA,OACGC,SAAA,OAIXH,GAAA,CAAeC,KAAA,QACPC,QAAAlC,EAAAA,KAAAoC,IACaD,SAAA,KAEpB,qTAKLlE,iBACEM,QAAAC,IAAAP,EAAA,WACAoE,EAAApE,GAFF,IAAAA,yQC5DA,IAAIqE,GACJ,MAAMC,GAA6B,oBAAXf,OAgBlBgB,GAAO,OAOCD,KAAkE,OAApDD,GAAe,MAAVd,YAAiB,EAASA,OAAOiB,gBAAqB,EAASH,GAAGI,YAAc,iBAAiBC,KAAKnB,OAAOiB,UAAUC,WCiCxJ,MAAME,GAAgBL,GAAWf,YAAS,EAK1C,SAASqB,MAAoBC,GACvB,IAAAnF,EACAD,EACAqF,EACAvG,EAOJ,GD9DuC,iBCwD1BsG,EAAK,KACfpF,EAAOqF,EAAUvG,GAAWsG,EACpBnF,EAAAiF,KAERjF,EAAQD,EAAOqF,EAAUvG,GAAWsG,GAElCnF,EACI,OAAA6E,GACT,IAAIQ,EAAUR,GACd,MAAMS,EAAYpF,GAAM,IAzB1B,SAAsBqF,GAChBZ,IAAAA,EACE,MAAAa,EAAQC,EAAMF,GACpB,OAAoD,OAA5CZ,EAAc,MAATa,OAAgB,EAASA,EAAME,KAAef,EAAKa,CAClE,CAqBgCG,CAAa3F,KAAU4F,QAE9CA,IAEFA,EAAA9B,iBAAiB/D,EAAOqF,EAAUvG,GACrCwG,EAAU,KACLO,EAAAC,oBAAoB9F,EAAOqF,EAAUvG,GAC9BwG,EAAAR,EAAA,EAChB,GACK,CAAEiB,WAAW,EAAMC,MAAO,SACvBC,EAAO,cD8Kf,IAA2BC,ECzKlB,ODyKkBA,EC1KPD,ED2KdE,KACFC,EAAeF,GC3KVD,CACT,CAqvBA,MAAMI,GAAgC,oBAAfC,WAA6BA,WAA+B,oBAAXxC,OAAyBA,OAA2B,oBAAXyC,OAAyBA,OAAyB,oBAATC,KAAuBA,KAAO,GAClLC,GAAY,0BA4hGlB,IAAIC,GACMC,GA5hGVN,GAAQI,IAAaJ,GAAQI,KAAc,CAAA,GA4hGjCE,GAMPD,KAAmBA,GAAiB,CAAA,IALjB,GAAI,KACxBC,GAAuB,MAAI,QAC3BA,GAAsB,KAAI,OAC1BA,GAAsB,KAAI,OAC1BA,GAAsB,KAAI,uyCC92H5BC,GAAAC,EAAA,CAA+BC,WAAA,CACjBC,gBACVC,yOCoDJ,MAAAC,EAAmB,SAAnBA,EACO,QADPA,EAGQ,UAHRA,EAKS,YAGTC,EAAAC,IACAxH,EAAAC,EAgBAH,EAAAC,EAGA0H,EAAAC,IACAnE,GAAA,MAGA,WACE,MAAAoE,EAAA3I,IAAAgC,IACE,OAAAA,EAAA8B,MAAgB,KAAAwE,EAGZtH,EAAA4H,oBAAAC,IACA,MAAA,KAAAP,EAGA,MAAA,KAAAA,EAGAQ,EAAA,UACA,MAAA,KAAAR,EAGAQ,EAAA,WACA,IAGNC,EAAA/I,IAAAgC,IAEE8G,GADA9G,EAAAgH,QAAAhH,EAAAiH,QACA,EAAA,SAAA,UAAA,CAAgDC,SAAAlI,EAAAkI,SAC9BC,kBAAA,GACE,IAItBZ,EAAAa,KAAA,KACE5C,GAAArD,SAAA,UAAAwF,GACAnC,GAAArD,SAAA,QAAA4F,EAAA,GACD,OAGH,MAAAM,EAAAlI,EAAA,CAAsBmI,MAAA,EACbC,IAAA,EACFC,QAAA,EACIC,QAAA,EACAN,kBAAA,IAIXO,EAAAC,GAAA,KACE,MAAAL,MAAAA,EAAAC,IAAAA,EAAAC,QAAAA,UAAAC,EAAAN,iBAAAA,GAAAE,EAAA1H,MACA,IAAAiI,EAAAJ,EAAAF,EACAO,EAAAJ,EAAAH,EAEA,OAAAC,EAAA,KAAmB,KAAA,GACZ,KAAA,KAEHK,EAAAC,GAAA,CAAAA,GAAAD,GACA,MAAA,KAAA,IACG,KAAA,KAEHA,EAAAC,GAAA,EAAAD,GAAAC,GACA,MAAA,KAAA,IACG,KAAA,IAEHD,EAAAC,GAAA,EAAAA,EAAAD,GAIJ,MAAAE,EAAA,CAA6BT,UAAA,SAAAC,aAAAC,mBAAAK,QAAAC,OAC0DE,WAAAZ,EAAA,gBAAA,IAIvF,OADAW,EAAAE,SAAAF,EAAAG,UAAA,MACAH,CAAA,IAGF,SAAAhB,EAAAoB,EAAA/J,EAAA,IACE,MAAAgK,SAAAA,EAAAC,SAAAA,EAAAC,WAAAA,GAAArJ,GACAkI,SAAAA,EAAAoB,UAAAA,EAAAnB,iBAAAA,GAAA,CAAkDD,SAAAlI,EAAAkI,SAChCoB,UAAA,GACLnB,kBAAA,KACOhJ,GAGpB,GAAAkK,EAAA,CAGA,OAAAH,GAAgB,IAAA,UAEZb,EAAA1H,MAAA2H,MAAAa,IACEd,EAAA1H,MAAA2H,MAAAiB,OAAAC,YAAAnB,EAAA1H,MAAA2H,MAAAJ,GAAAuB,QAAA,KAEF,MAAA,IAAA,SAEApB,EAAA1H,MAAA2H,MAAAc,IACEf,EAAA1H,MAAA2H,MAAAiB,OAAAC,YAAAnB,EAAA1H,MAAA2H,MAAAJ,GAAAuB,QAAA,KAEF,MAAA,IAAA,YAEApB,EAAA1H,MAAA4H,KAAAe,EACA,MAAA,IAAA,gBAEAjB,EAAA1H,MAAA4H,KAAAe,EAGJjB,EAAA1H,MAAAwH,iBAAAA,CApBE,CAoBiC,CAErC,SAAAuB,EAAA1I,GACEqH,EAAA1H,MAAAwH,kBAAA,EAEA,MAAAK,QAAAA,EAAAC,QAAAA,GAAAJ,EAAA1H,MACAgJ,EAAA3I,EAAA4I,MACAC,EAAA7I,EAAA8I,MAEAC,EAAA/K,IAAAgL,IACE3B,EAAA1H,MAAA,IAAkB0H,EAAA1H,MACH6H,QAAAA,EAAAwB,EAAAJ,MAAAD,EACiBlB,QAAAA,EAAAuB,EAAAF,MAAAD,EACA,IAGlCI,EAAAzE,GAAArD,SAAA,YAAA4H,GACAvE,GAAArD,SAAA,WAAA,YAIAnB,EAAAkJ,gBAAiB,CAEnB,MAAAC,EAAAzH,UACE0H,GAAAC,QAAA,2BAAA,KAAA,CAAuDC,kBAAA,KAClCC,iBAAA,KACD3F,KAAA,YACZ4F,MAAA9H,UAGJ,MAAA+H,EAAAC,GAAAC,QAAA,CAAkCC,MAAA,EAC1BC,KAAA,UACA9G,WAAA,uBAIR+G,QAAAC,EAAA,CAA8BC,KAAAhL,EAAAJ,UAAAqL,OAAAD,MAAA,OAI9BF,GAAAnI,MAAAA,OACE8H,EAAAS,QACA/G,OAAAgH,KAAAL,GAAAnI,MAAAA,MAA2B,IAC7ByI,OAAA,QAEW,EAIjBC,EAAA,KACE,MAAAC,KAAAA,GAAA7D,EAAA8D,QAAA,CAAgCC,KAAA,cACxBC,MAAA,CACCjH,GAAAxE,EAAAJ,SAAAkB,OAKTqD,OAAAgH,KAAAG,EAAA,SAAA,EAGFzD,EAAA,KACEQ,EAAA1H,MAAA,CAAkB2H,MAAA,EACTC,IAAA,EACFC,QAAA,EACIC,QAAA,EACAN,kBAAA,GAGXrI,EAAA,QAAA,0rDDzPA,KAAA4L,GAEE,IAAAC,EAAAxL,EAAA,IACAyL,EAAAzL,EAAA,MACA0L,EAAA1L,EAAA,MACA2L,EAAA3L,EAAA,GACA4L,EAAA5L,GAAA,GACA6L,EAAA7L,GAAA,GACAkJ,EAAAlJ,GAAA,GACAP,EAAAO,EAAA,CAAA,GACA8L,EAAA9L,GAAA,GACA0D,EAAA1D,EAAA,GACAT,EAAAG,IACA,MAAAqM,EAAA/L,EAAA,GAWAgM,EAAAzJ,UACE,GAAAqJ,EAAApL,MACE,OAAA,EAEFqL,EAAArL,OAAA,EACA,MAAAgC,QAAAyJ,EAAA,CAAgCvJ,MAAA,IAAAnD,EAAAmD,MAAAnD,EAAAmD,MAAA,GACYwJ,OAAA3M,EAAA4M,UAAAC,KAAA,MAAA,GACLC,SAAA9M,EAAA+M,OAAAF,KAAA,MAAA,GACDG,WAAAhN,EAAAgN,WAClBC,KAAAb,EAAAnL,MACFiM,SAAA,KAGlB,IAAAjK,EAAAA,KAAAG,MAEEmJ,EAAAtL,OAAA,EACAuL,EAAAvL,OAAAgC,EAAAA,KAAAA,KAAAuJ,MACAvJ,EAAAA,KAAAA,KAAAkK,KAAA5J,OAAA,KAAA8I,EAAApL,OAAA,GACA,IAAAmL,EAAAnL,QAAAgL,EAAAhL,MAAA,IACAgC,EAAAA,KAAAA,KAAAkK,KAAAC,SAAA,CAAA9L,EAAA+L,KACE,GAAAA,EAAA,GAAA,IAAAjB,EAAAnL,MAAA,CACE,IAAAqM,EAAAhM,EAAAiM,QAAApJ,EAAAlD,MAAAK,EAAA6C,OACA8H,EAAAhL,MAAAoM,GAAA,CAAgCE,QAAAD,EAAA,IAAA,IAAAA,GAAA,GACEE,SAAA,IAGlCvB,EAAAhL,MAAAoM,GAAAG,SAAAjM,KAAAD,EAA6C,KAAA,CAG7C,IAAAmM,EAAA,GACA,IAAA,IAAAC,KAAAzB,EAAAhL,MACEwM,EAAAlM,KAAA0K,EAAAhL,MAAAyM,GAAAH,QAEF,IAAA,IAAAG,KAAAzB,EAAAhL,MAAA,CACE,IAAAqM,EAAAhM,EAAAiM,QAAApJ,EAAAlD,MAAAK,EAAA6C,OACA,GAAA8H,EAAAhL,MAAAyM,GAAAH,SAAAE,EAAAE,MAAA,CAAAC,EAAAC,IAAAD,EAAAC,IAAA,GAGE,OAFA5B,EAAAhL,MAAAyM,GAAAH,SAAAD,EAAA,IAAA,IAAAA,GAAA,QACArB,EAAAhL,MAAAyM,GAAAF,SAAAjM,KAAAD,EAEF,CACF,MAIJ2D,GAAA,CAAeE,QAAAlC,EAAAA,KAAAoC,IACMH,KAAA,QACbE,SAAA,OAIVkH,EAAArL,OAAA,CAAA,EAGFH,GAAA,IAAAd,EAAA8N,oBAC8B,KAE1B1B,EAAAnL,MAAA,EACAgL,EAAAhL,MAAA,GACAoL,EAAApL,OAAA,QAEF,CAAAyF,WAAA,EAAAqH,MAAA,IAIFlK,GAAA,KACEM,EAAAlD,MAAAiL,EAAAjL,MAAA0C,YAAA,EAAA,EAAA,IAIF,MAAAqK,EAAAlO,aAAAa,IACE,GAAA0L,EAAApL,MACE,OAAA,EAEF,MAAAgN,aAAAA,EAAAC,UAAAA,EAAAC,aAAAA,GAAAxN,EAAAC,OACAwN,SAAAH,IACAG,SAAAF,GACAE,SAAAD,KACA,GAAA/B,EAAAnL,QAAAuL,EAAAvL,QACEmL,EAAAnL,YACgB,GAClB,KAGF,MAAA,CAAOoN,UA5FP,KACEjC,EAAAnL,MAAA,EACAoL,EAAApL,OAAA,OA2FAwL,kBACAuB,qBACA/B,mBACAC,kBACAC,WACAI,UACAD,sBACAD,aACA1C,aACAzJ,WACAoO,cAlGFC,IACErO,EAAAe,MAAAsN,EACA5E,EAAA1I,OAAA,CAAA,EAkGF,QAhK2CuN,MAAA,4BAAkB/N,IAAA,kBAFjE2I,MAAA,CAAAqF,SAAA,WAAAC,GAAA,CAAA3J,IAAA,OAqB8CA,IAAA,mCACTA,IAAA,6GAtBrC4J,EAAAC,EAAA,eACO,OAAAC,IAAAC,EAAAC,EAAA,KAAA,CAAmCC,EAAA,MAAA,CAAeR,MAAA,8BAAE/N,IAAA,WACvDwO,SAAAC,EAAA,KAAAA,EAAA,GAAA,IAAAnJ,IAAAoJ,EAAAnB,oBAAAmB,EAAAnB,sBAAAjI,KAsBM,CArBJiJ,EAAA,MAAAI,GAAA,EAAAP,GAAA,GAAAC,EAAAC,EAAA,KAAAM,EAAAF,EAAAlD,kBAAA,CAAAyB,EAAAL,kBAA4EmB,MAAA,sEACTA,MAAA,WAC/DzJ,IAAAuK,EAAAlO,KAAA,CACgDmO,EAAAC,EAAA,CAAAC,aAAA,CAA+B1K,IAAAuK,EAAAlO,IAA4BsO,IAAAJ,EAAAK,IAAiCrE,KAAAgE,EAAAhE,KAA4FsE,KAAA,CAAAC,EAAAP,EAAAnL,MAAA2L,EAAAR,EAAA/B,QAAYrI,KAAA,wIAaxO+G,MAnBtB,IAAAkD,EAAAlD,iBAAA1I,QAAA4L,EAAA5C,SAAAsC,IAAAkB,EAAAC,EAAA,CAmBkFjL,IAAA,EAC5EkL,YAAA,WAAApB,IAAAC,EAAA,MAAAJ,GAAA,CACES,EAAA7C,qBAAAuC,IAAAC,EAAA,MAAAoB,GAAA,WAAAC,EAAA,IAAA,GACAhB,EAAA9C,YAAAwC,IAAAC,EAAA,MAAAsB,GAAA,UAAAD,EAAA,IAAA,OAIN,MAAA,KAAcZ,EAAAZ,EAAA,CAA2CzO,SAAAiP,EAAAjP,SAAQyJ,WAAAwF,EAAAxF,gjBEiCnE,MAAAzJ,EAAAC,IACAH,EAAAC,IAEAoQ,EAAA5P,EAAA,GAEA6P,EAAA7P,EAAA,IAEA8P,EAAArP,IACEmP,EAAApP,OAAAC,EAAAE,IACAlB,EAAAsQ,mBAAAtP,EAAA,EAQFuP,EAAAhQ,EAAA,MACAiQ,EAAAjQ,EAAA,IACAkQ,EAAA1H,GAAA,IACEyH,EAAAzP,MAAA2P,MAAA1P,GAAAA,EAAAE,MAAAlB,EAAA8M,cAAA1B,OAEFuF,EAAApQ,GAAA,GAEAqQ,EAAA,KACED,EAAA5P,OAAA,CAAA,EAGF8P,EAAA,KACEF,EAAA5P,OAAA,CAAA,EAIF+P,EAAA5P,IACElB,EAAA+Q,iBAAA7P,OAEAyP,EAAA5P,OAAA,CAAA,EAGFiQ,EAAAlO,UACE,IACE,MAAAoI,OChF2BpI,OAAO+C,UAClBoL,EAAMC,IAAI,iBAAkB,CAC5CC,OAAQtL,KAEC9C,KD4ETqO,CAAA,CAAA,GACA,GAAA,IAAAlG,EAAAhI,KACE,MAAA,IAAAmO,MAAAnG,EAAA/F,KAEF,GAAA,IAAA+F,EAAAnI,KAAAM,OAEE,YADAiO,EAAAC,MAAA,QAGFf,EAAAzP,MAAA,IAAAyP,EAAAzP,SAAAmK,EAAAnI,MAEA+N,EAAA5F,EAAAnI,KAAA,GAAA7B,WACAsQ,GAAwB,OAAAD,GAExBD,EAAAC,MAAAA,EAAAtM,QAA6B,GAIjCuM,EAAA1O,UACE,MAAAC,QAAA0O,EAAA,CAA4B3E,WAAA9M,EAAA8M,aAG5BsD,EAAArP,MAAAgC,EAAAA,KAAAA,KAAAkK,IAAA,SAGFtJ,GAAA,KArDAb,iBACEkO,GAAA", "x_google_ignoreList": [0, 4, 5]}