{"version": 3, "file": "chunk.275523d0.js", "sources": ["../src/views/layouts/components/spNav.vue", "../src/utils/downloadImage.ts"], "sourcesContent": ["<template>\r\n  <div\r\n    :class=\"{\r\n      sp__nav: true,\r\n      'sp-nav-theme__active': store.themeShow\r\n    }\"\r\n  >\r\n    <div class=\"sp-nav__content\">\r\n      <div\r\n        v-for=\"item in data\"\r\n        :key=\"item._id\"\r\n        :class=\"{\r\n          'sp-nav-content__item': true,\r\n          'sp-nav-content-item__active': item._id === currentId\r\n        }\"\r\n        @click=\"navChange(item)\"\r\n      >\r\n        <img loading=\"lazy\" v-if=\"item.icon === '' || item.icon === undefined\" :src=\"store.themeShow || item._id === currentId ? 'https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png' : 'https://static.soyoung.com/sy-pre/ty5osyh2u1oh-1697609400738.png'\" alt=\"\" />\r\n        <img loading=\"lazy\" v-else :src=\"item.icon\" alt=\"\" />\r\n        <span class=\"sp-nav-content-item__span\">{{ item.name }}</span>\r\n\r\n        <el-popover v-if=\"item.name !== '收藏夹' && identify === 1 && item._id === currentId\" placement=\"bottom\" :width=\"103\" :popper-class=\"'popover-operation__style'\" trigger=\"hover\" :effect=\"store.themeShow ? 'light' : 'dark'\">\r\n          <template #reference>\r\n            <!-- 操作按钮 start -->\r\n            <div class=\"operation-span__container\">\r\n              <img loading=\"lazy\" class=\"operation__btn\" src=\"https://static.soyoung.com/sy-pre/1spmepi29mrwu-1698304200710.png\" alt=\"\" />\r\n            </div>\r\n            <!-- 操作按钮 end-->\r\n          </template>\r\n          <div class=\"operation-body\">\r\n            <div class=\"operation-body__item\" @click=\"handleRename(item)\">\r\n              <span>重命名</span>\r\n            </div>\r\n            <div class=\"operation-body__item\" @click=\"handleDelete(item)\">\r\n              <span>删除</span>\r\n            </div>\r\n          </div>\r\n        </el-popover>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { themeStore } from \"@/store\";\r\nimport { PropType, ref, watch } from \"vue\";\r\nlet emits = defineEmits([\"tabsChange\", \"handleRename\", \"handleDelete\"]);\r\nconst store = themeStore();\r\n\r\ntype RouterListData = {\r\n  _id: string;\r\n  name: string;\r\n  icon?: string;\r\n};\r\nlet props = defineProps({\r\n  data: {\r\n    type: Array as PropType<RouterListData[]>,\r\n    default: () => []\r\n  },\r\n  defaultId: {\r\n    type: String,\r\n    default: \"\"\r\n  },\r\n  identify: {\r\n    // 用来确定是否有右侧操作按钮 0: 没有  1: 有\r\n    type: Number,\r\n    default: 0\r\n  }\r\n});\r\n\r\nconst currentId = ref<string>(\"\");\r\nconst navChange = (item: any) => {\r\n  currentId.value = item._id;\r\n  emits(\"tabsChange\", item);\r\n};\r\n\r\n// 重命名\r\nconst handleRename = (item: any) => {\r\n  emits(\"handleRename\", item);\r\n};\r\n\r\n// 删除\r\nconst handleDelete = (item: any) => {\r\n  emits(\"handleDelete\", item);\r\n};\r\n\r\nwatch(\r\n  () => props.defaultId,\r\n  (n: string) => {\r\n    currentId.value = n;\r\n  },\r\n  { immediate: true }\r\n);\r\n\r\nwatch(\r\n  () => props.data,\r\n  (n: { _id: string; name: string }[]) => {\r\n    if (n.length === 0 || n.length === 1) {\r\n      emits(\"tabsChange\", { _id: \"\", name: \"\" });\r\n    }\r\n  },\r\n  { immediate: true, deep: true }\r\n);\r\n</script>\r\n<style lang=\"less\">\r\n.sp__nav {\r\n  width: 250px;\r\n  height: calc(100vh - 160px);\r\n  background: #ffffff;\r\n  padding: 0 20px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n  &::before {\r\n    content: \" \";\r\n    position: absolute;\r\n    right: 0;\r\n    top: 0;\r\n    width: 1px;\r\n    height: 100%;\r\n    background-image: linear-gradient(to bottom, #ededed 50%, #ededed 50%);\r\n  }\r\n  .sp-nav__content {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    overflow-y: auto;\r\n    overflow-x: hidden;\r\n    scroll-behavior: smooth;\r\n    scroll-snap-type: x proximity;\r\n    &::-webkit-scrollbar {\r\n      display: none;\r\n    }\r\n    .sp-nav-content__item {\r\n      width: 100%;\r\n      height: 44px;\r\n      line-height: 44px;\r\n      text-align: left;\r\n      font-size: 14px;\r\n      color: #303233;\r\n      padding: 0 11px;\r\n      box-sizing: border-box;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      cursor: pointer;\r\n      .sp-nav-content-item__span {\r\n        flex: 1;\r\n      }\r\n      img {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 10px;\r\n        border-radius: 50%;\r\n        &.operation__btn {\r\n          width: 3px;\r\n        }\r\n      }\r\n      &.sp-nav-content-item__active {\r\n        border-radius: 6px;\r\n        background-color: #5c54f0;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  &.sp-nav-theme__active {\r\n    background-color: #26282b;\r\n    &::before {\r\n      background-image: linear-gradient(to bottom, #000000 50%, #000000 50%);\r\n    }\r\n    .sp-nav__content {\r\n      .sp-nav-content__item {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  .operation-span__container {\r\n    width: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    height: 100%;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.popover-operation__style {\r\n  min-width: 103px !important;\r\n}\r\n.operation-body {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .operation-body__item {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n    font-family: PingFangSC-Regular;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    letter-spacing: 0;\r\n    font-weight: 400;\r\n    margin-bottom: 15px;\r\n    cursor: pointer;\r\n    &:hover {\r\n      color: #5c54f0;\r\n    }\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "export const downlondImage = (item: any) => {\n  const canvas = document.createElement(\"canvas\");\n  const ctx: any = canvas.getContext(\"2d\");\n  const ratio = window.devicePixelRatio || 1;\n  ctx.scale(ratio, ratio);\n  const image = new Image();\n  image.setAttribute(\"crossOrigin\", \"anonymous\");\n  image.src = item.src;\n  image.onload = () => {\n    const imgScale = image.width / image.height;\n    canvas.style.width = image.width + \"px\";\n    canvas.style.height = image.height + \"px\";\n    canvas.width = image.width;\n    canvas.height = image.height;\n    ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height);\n\n    const url = canvas.toDataURL(\"image/png\");\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = `${item.name}.png`;\n    a.click();\n  };\n};\n"], "names": ["emits", "__emit", "store", "themeStore", "props", "__props", "currentId", "ref", "watch", "defaultId", "n", "value", "immediate", "data", "length", "_id", "name", "deep", "item", "downlondImage", "canvas", "document", "createElement", "ctx", "getContext", "ratio", "window", "devicePixelRatio", "scale", "image", "Image", "setAttribute", "src", "onload", "width", "height", "style", "drawImage", "url", "toDataURL", "a", "href", "download", "click"], "mappings": "4zBA6CA,IAAAA,EAAAC,EACA,MAAAC,EAAAC,IAOA,IAAAC,EAAAC,EAgBA,MAAAC,EAAAC,EAAA,WAgBAC,GAAA,IAAAJ,EAAAK,YACcC,IAEVJ,EAAAK,MAAAD,CAAA,GACF,CAAAE,WAAA,IAIFJ,GAAA,IAAAJ,EAAAS,OACcH,IAEV,IAAAA,EAAAI,QAAA,IAAAJ,EAAAI,QACEd,EAAA,aAAA,CAAAe,IAAA,GAAAC,KAAA,IAAyC,GAE7C,CAAAJ,WAAA,EAAAK,MAAA,sQA7BF,CAAAC,IACEZ,EAAAK,MAAAO,EAAAH,IACAf,EAAA,aAAAkB,EAAA,goBAIF,CAAAA,IACElB,EAAA,eAAAkB,EAAA,+DAIF,CAAAA,IACElB,EAAA,eAAAkB,EAAA,6EClFWC,EAAiBD,IACtB,MAAAE,EAASC,SAASC,cAAc,UAChCC,EAAWH,EAAOI,WAAW,MAC7BC,EAAQC,OAAOC,kBAAoB,EACrCJ,EAAAK,MAAMH,EAAOA,GACX,MAAAI,EAAQ,IAAIC,MACZD,EAAAE,aAAa,cAAe,aAClCF,EAAMG,IAAMd,EAAKc,IACjBH,EAAMI,OAAS,KACIJ,EAAMK,MAAQL,EAAMM,OAC9Bf,EAAAgB,MAAMF,MAAQL,EAAMK,MAAQ,KAC5Bd,EAAAgB,MAAMD,OAASN,EAAMM,OAAS,KACrCf,EAAOc,MAAQL,EAAMK,MACrBd,EAAOe,OAASN,EAAMM,OACtBZ,EAAIc,UAAUR,EAAO,EAAG,EAAGA,EAAMK,MAAOL,EAAMM,OAAQ,EAAG,EAAGf,EAAOc,MAAOd,EAAOe,QAE3E,MAAAG,EAAMlB,EAAOmB,UAAU,aACvBC,EAAInB,SAASC,cAAc,KACjCkB,EAAEC,KAAOH,EACPE,EAAAE,SAAW,GAAGxB,EAAKF,WACrBwB,EAAEG,OAAM,CAAA"}