{"version": 3, "file": "chunk.e117c129.js", "sources": ["../node_modules/element-plus/es/components/loading/index.mjs"], "sourcesContent": ["import { Loading } from './src/service.mjs';\nexport { Loading as ElLoadingService } from './src/service.mjs';\nimport { vLoading } from './src/directive.mjs';\nexport { vLoading as ElLoadingDirective, vLoading } from './src/directive.mjs';\nimport './src/types.mjs';\n\nconst ElLoading = {\n  install(app) {\n    app.directive(\"loading\", vLoading);\n    app.config.globalProperties.$loading = Loading;\n  },\n  directive: vLoading,\n  service: Loading\n};\n\nexport { ElLoading, ElLoading as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["ElLoading", "install", "app", "directive", "vLoading", "config", "globalProperties", "$loading", "Loading", "service"], "mappings": "+CAMK,MAACA,EAAY,CAChB,OAAAC,CAAQC,GACFA,EAAAC,UAAU,UAAWC,GACrBF,EAAAG,OAAOC,iBAAiBC,SAAWC,CACxC,EACDL,UAAWC,EACXK,QAASD", "x_google_ignoreList": [0]}