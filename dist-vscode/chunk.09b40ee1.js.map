{"version": 3, "file": "chunk.09b40ee1.js", "sources": ["../node_modules/viewerjs/dist/viewer.esm.js", "../src/views/layouts/viewImage/components/viewLargerImage.vue", "../src/views/layouts/viewImage/index.vue"], "sourcesContent": ["/*!\n * Viewer.js v1.11.6\n * https://fengyuanchen.github.io/viewerjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2023-09-17T03:16:38.052Z\n */\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar DEFAULTS = {\n  /**\n   * Enable a modal backdrop, specify `static` for a backdrop\n   * which doesn't close the modal on click.\n   * @type {boolean}\n   */\n  backdrop: true,\n  /**\n   * Show the button on the top-right of the viewer.\n   * @type {boolean}\n   */\n  button: true,\n  /**\n   * Show the navbar.\n   * @type {boolean | number}\n   */\n  navbar: true,\n  /**\n   * Specify the visibility and the content of the title.\n   * @type {boolean | number | Function | Array}\n   */\n  title: true,\n  /**\n   * Show the toolbar.\n   * @type {boolean | number | Object}\n   */\n  toolbar: true,\n  /**\n   * Custom class name(s) to add to the viewer's root element.\n   * @type {string}\n   */\n  className: '',\n  /**\n   * Define where to put the viewer in modal mode.\n   * @type {string | Element}\n   */\n  container: 'body',\n  /**\n   * Filter the images for viewing. Return true if the image is viewable.\n   * @type {Function}\n   */\n  filter: null,\n  /**\n   * Enable to request fullscreen when play.\n   * {@link https://developer.mozilla.org/en-US/docs/Web/API/FullscreenOptions}\n   * @type {boolean|FullscreenOptions}\n   */\n  fullscreen: true,\n  /**\n   * Define the extra attributes to inherit from the original image.\n   * @type {Array}\n   */\n  inheritedAttributes: ['crossOrigin', 'decoding', 'isMap', 'loading', 'referrerPolicy', 'sizes', 'srcset', 'useMap'],\n  /**\n   * Define the initial coverage of the viewing image.\n   * @type {number}\n   */\n  initialCoverage: 0.9,\n  /**\n   * Define the initial index of the image for viewing.\n   * @type {number}\n   */\n  initialViewIndex: 0,\n  /**\n   * Enable inline mode.\n   * @type {boolean}\n   */\n  inline: false,\n  /**\n   * The amount of time to delay between automatically cycling an image when playing.\n   * @type {number}\n   */\n  interval: 5000,\n  /**\n   * Enable keyboard support.\n   * @type {boolean}\n   */\n  keyboard: true,\n  /**\n   * Focus the viewer when initialized.\n   * @type {boolean}\n   */\n  focus: true,\n  /**\n   * Indicate if show a loading spinner when load image or not.\n   * @type {boolean}\n   */\n  loading: true,\n  /**\n   * Indicate if enable loop viewing or not.\n   * @type {boolean}\n   */\n  loop: true,\n  /**\n   * Min width of the viewer in inline mode.\n   * @type {number}\n   */\n  minWidth: 200,\n  /**\n   * Min height of the viewer in inline mode.\n   * @type {number}\n   */\n  minHeight: 100,\n  /**\n   * Enable to move the image.\n   * @type {boolean}\n   */\n  movable: true,\n  /**\n   * Enable to rotate the image.\n   * @type {boolean}\n   */\n  rotatable: true,\n  /**\n   * Enable to scale the image.\n   * @type {boolean}\n   */\n  scalable: true,\n  /**\n   * Enable to zoom the image.\n   * @type {boolean}\n   */\n  zoomable: true,\n  /**\n   * Enable to zoom the current image by dragging on the touch screen.\n   * @type {boolean}\n   */\n  zoomOnTouch: true,\n  /**\n   * Enable to zoom the image by wheeling mouse.\n   * @type {boolean}\n   */\n  zoomOnWheel: true,\n  /**\n   * Enable to slide to the next or previous image by swiping on the touch screen.\n   * @type {boolean}\n   */\n  slideOnTouch: true,\n  /**\n   * Indicate if toggle the image size between its natural size\n   * and initial size when double click on the image or not.\n   * @type {boolean}\n   */\n  toggleOnDblclick: true,\n  /**\n   * Show the tooltip with image ratio (percentage) when zoom in or zoom out.\n   * @type {boolean}\n   */\n  tooltip: true,\n  /**\n   * Enable CSS3 Transition for some special elements.\n   * @type {boolean}\n   */\n  transition: true,\n  /**\n   * Define the CSS `z-index` value of viewer in modal mode.\n   * @type {number}\n   */\n  zIndex: 2015,\n  /**\n   * Define the CSS `z-index` value of viewer in inline mode.\n   * @type {number}\n   */\n  zIndexInline: 0,\n  /**\n   * Define the ratio when zoom the image by wheeling mouse.\n   * @type {number}\n   */\n  zoomRatio: 0.1,\n  /**\n   * Define the min ratio of the image when zoom out.\n   * @type {number}\n   */\n  minZoomRatio: 0.01,\n  /**\n   * Define the max ratio of the image when zoom in.\n   * @type {number}\n   */\n  maxZoomRatio: 100,\n  /**\n   * Define where to get the original image URL for viewing.\n   * @type {string | Function}\n   */\n  url: 'src',\n  /**\n   * Event shortcuts.\n   * @type {Function}\n   */\n  ready: null,\n  show: null,\n  shown: null,\n  hide: null,\n  hidden: null,\n  view: null,\n  viewed: null,\n  move: null,\n  moved: null,\n  rotate: null,\n  rotated: null,\n  scale: null,\n  scaled: null,\n  zoom: null,\n  zoomed: null,\n  play: null,\n  stop: null\n};\n\nvar TEMPLATE = '<div class=\"viewer-container\" tabindex=\"-1\" touch-action=\"none\">' + '<div class=\"viewer-canvas\"></div>' + '<div class=\"viewer-footer\">' + '<div class=\"viewer-title\"></div>' + '<div class=\"viewer-toolbar\"></div>' + '<div class=\"viewer-navbar\">' + '<ul class=\"viewer-list\" role=\"navigation\"></ul>' + '</div>' + '</div>' + '<div class=\"viewer-tooltip\" role=\"alert\" aria-hidden=\"true\"></div>' + '<div class=\"viewer-button\" data-viewer-action=\"mix\" role=\"button\"></div>' + '<div class=\"viewer-player\"></div>' + '</div>';\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'viewer';\n\n// Actions\nvar ACTION_MOVE = 'move';\nvar ACTION_SWITCH = 'switch';\nvar ACTION_ZOOM = 'zoom';\n\n// Classes\nvar CLASS_ACTIVE = \"\".concat(NAMESPACE, \"-active\");\nvar CLASS_CLOSE = \"\".concat(NAMESPACE, \"-close\");\nvar CLASS_FADE = \"\".concat(NAMESPACE, \"-fade\");\nvar CLASS_FIXED = \"\".concat(NAMESPACE, \"-fixed\");\nvar CLASS_FULLSCREEN = \"\".concat(NAMESPACE, \"-fullscreen\");\nvar CLASS_FULLSCREEN_EXIT = \"\".concat(NAMESPACE, \"-fullscreen-exit\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_HIDE_MD_DOWN = \"\".concat(NAMESPACE, \"-hide-md-down\");\nvar CLASS_HIDE_SM_DOWN = \"\".concat(NAMESPACE, \"-hide-sm-down\");\nvar CLASS_HIDE_XS_DOWN = \"\".concat(NAMESPACE, \"-hide-xs-down\");\nvar CLASS_IN = \"\".concat(NAMESPACE, \"-in\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_LOADING = \"\".concat(NAMESPACE, \"-loading\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\nvar CLASS_OPEN = \"\".concat(NAMESPACE, \"-open\");\nvar CLASS_SHOW = \"\".concat(NAMESPACE, \"-show\");\nvar CLASS_TRANSITION = \"\".concat(NAMESPACE, \"-transition\");\n\n// Native events\nvar EVENT_CLICK = 'click';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_DRAG_START = 'dragstart';\nvar EVENT_FOCUSIN = 'focusin';\nvar EVENT_KEY_DOWN = 'keydown';\nvar EVENT_LOAD = 'load';\nvar EVENT_ERROR = 'error';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_RESIZE = 'resize';\nvar EVENT_TRANSITION_END = 'transitionend';\nvar EVENT_WHEEL = 'wheel';\n\n// Custom events\nvar EVENT_READY = 'ready';\nvar EVENT_SHOW = 'show';\nvar EVENT_SHOWN = 'shown';\nvar EVENT_HIDE = 'hide';\nvar EVENT_HIDDEN = 'hidden';\nvar EVENT_VIEW = 'view';\nvar EVENT_VIEWED = 'viewed';\nvar EVENT_MOVE = 'move';\nvar EVENT_MOVED = 'moved';\nvar EVENT_ROTATE = 'rotate';\nvar EVENT_ROTATED = 'rotated';\nvar EVENT_SCALE = 'scale';\nvar EVENT_SCALED = 'scaled';\nvar EVENT_ZOOM = 'zoom';\nvar EVENT_ZOOMED = 'zoomed';\nvar EVENT_PLAY = 'play';\nvar EVENT_STOP = 'stop';\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\n\n// RegExps\nvar REGEXP_SPACES = /\\s\\s*/;\n\n// Misc\nvar BUTTONS = ['zoom-in', 'zoom-out', 'one-to-one', 'reset', 'prev', 'play', 'next', 'rotate-left', 'rotate-right', 'flip-horizontal', 'flip-vertical'];\n\n/**\n * Check if the given value is a string.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a string, else `false`.\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      var length = data.length;\n      var i;\n      for (i = 0; i < length; i += 1) {\n        if (callback.call(data, data[i], i, data) === false) {\n          break;\n        }\n      }\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} obj - The object to be extended.\n * @param {*} args - The rest objects which will be merged to the first object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(obj) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(obj) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          obj[key] = arg[key];\n        });\n      }\n    });\n  }\n  return obj;\n};\nvar REGEXP_SUFFIX = /^(?:width|height|left|top|marginLeft|marginTop)$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value += 'px';\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Escape a string for using in HTML.\n * @param {String} value - The string to escape.\n * @returns {String} Returns the escaped string.\n */\nfunction escapeHTMLEntities(value) {\n  return isString(value) ? value.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;') : value;\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  if (!element || !value) {\n    return false;\n  }\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!element || !value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!element || !value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_HYPHENATE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction hyphenate(value) {\n  return value.replace(REGEXP_HYPHENATE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(hyphenate(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(hyphenate(name)), data);\n  }\n}\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @param {Object} options - The additional event options.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data, options) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, _objectSpread2({\n      bubbles: true,\n      cancelable: true,\n      detail: data\n    }, options));\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get an image name from an image url.\n * @param {string} url - The target url.\n * @example\n * // picture.jpg\n * getImageNameFromURL('https://domain.com/path/to/picture.jpg?size=1280×960')\n * @returns {string} A string contains the image name.\n */\nfunction getImageNameFromURL(url) {\n  return isString(url) ? decodeURIComponent(url.replace(/^.*\\//, '').replace(/[?&#].*$/, '')) : '';\n}\nvar IS_SAFARI = WINDOW.navigator && /Version\\/\\d+(\\.\\d+)+?\\s+Safari/i.test(WINDOW.navigator.userAgent);\n\n/**\n * Get an image's natural sizes.\n * @param {string} image - The target image.\n * @param {Object} options - The viewer options.\n * @param {Function} callback - The callback function.\n * @returns {HTMLImageElement} The new image.\n */\nfunction getImageNaturalSizes(image, options, callback) {\n  var newImage = document.createElement('img');\n\n  // Modern browsers (except Safari)\n  if (image.naturalWidth && !IS_SAFARI) {\n    callback(image.naturalWidth, image.naturalHeight);\n    return newImage;\n  }\n  var body = document.body || document.documentElement;\n  newImage.onload = function () {\n    callback(newImage.width, newImage.height);\n    if (!IS_SAFARI) {\n      body.removeChild(newImage);\n    }\n  };\n  forEach(options.inheritedAttributes, function (name) {\n    var value = image.getAttribute(name);\n    if (value !== null) {\n      newImage.setAttribute(name, value);\n    }\n  });\n  newImage.src = image.src;\n\n  // iOS Safari will convert the image automatically\n  // with its orientation once append it into DOM\n  if (!IS_SAFARI) {\n    newImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n    body.appendChild(newImage);\n  }\n  return newImage;\n}\n\n/**\n * Get the related class name of a responsive type number.\n * @param {string} type - The responsive type.\n * @returns {string} The related class name.\n */\nfunction getResponsiveClass(type) {\n  switch (type) {\n    case 2:\n      return CLASS_HIDE_XS_DOWN;\n    case 3:\n      return CLASS_HIDE_SM_DOWN;\n    case 4:\n      return CLASS_HIDE_MD_DOWN;\n    default:\n      return '';\n  }\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var ratios = [];\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      ratios.push(ratio);\n    });\n  });\n  ratios.sort(function (a, b) {\n    return Math.abs(a) < Math.abs(b);\n  });\n  return ratios[0];\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    timeStamp: Date.now(),\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initViewer();\n    this.initList();\n    this.renderViewer();\n  },\n  initBody: function initBody() {\n    var ownerDocument = this.element.ownerDocument;\n    var body = ownerDocument.body || ownerDocument.documentElement;\n    this.body = body;\n    this.scrollbarWidth = window.innerWidth - ownerDocument.documentElement.clientWidth;\n    this.initialBodyPaddingRight = body.style.paddingRight;\n    this.initialBodyComputedPaddingRight = window.getComputedStyle(body).paddingRight;\n  },\n  initContainer: function initContainer() {\n    this.containerData = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  },\n  initViewer: function initViewer() {\n    var options = this.options,\n      parent = this.parent;\n    var viewerData;\n    if (options.inline) {\n      viewerData = {\n        width: Math.max(parent.offsetWidth, options.minWidth),\n        height: Math.max(parent.offsetHeight, options.minHeight)\n      };\n      this.parentData = viewerData;\n    }\n    if (this.fulled || !viewerData) {\n      viewerData = this.containerData;\n    }\n    this.viewerData = assign({}, viewerData);\n  },\n  renderViewer: function renderViewer() {\n    if (this.options.inline && !this.fulled) {\n      setStyle(this.viewer, this.viewerData);\n    }\n  },\n  initList: function initList() {\n    var _this = this;\n    var element = this.element,\n      options = this.options,\n      list = this.list;\n    var items = [];\n\n    // initList may be called in this.update, so should keep idempotent\n    list.innerHTML = '';\n    forEach(this.images, function (image, index) {\n      var src = image.src;\n      var alt = image.alt || getImageNameFromURL(src);\n      var url = _this.getImageURL(image);\n      if (src || url) {\n        var item = document.createElement('li');\n        var img = document.createElement('img');\n        forEach(options.inheritedAttributes, function (name) {\n          var value = image.getAttribute(name);\n          if (value !== null) {\n            img.setAttribute(name, value);\n          }\n        });\n        if (options.navbar) {\n          img.src = src || url;\n        }\n        img.alt = alt;\n        img.setAttribute('data-original-url', url || src);\n        item.setAttribute('data-index', index);\n        item.setAttribute('data-viewer-action', 'view');\n        item.setAttribute('role', 'button');\n        if (options.keyboard) {\n          item.setAttribute('tabindex', 0);\n        }\n        item.appendChild(img);\n        list.appendChild(item);\n        items.push(item);\n      }\n    });\n    this.items = items;\n    forEach(items, function (item) {\n      var image = item.firstElementChild;\n      var onLoad;\n      var onError;\n      setData(image, 'filled', true);\n      if (options.loading) {\n        addClass(item, CLASS_LOADING);\n      }\n      addListener(image, EVENT_LOAD, onLoad = function onLoad(event) {\n        removeListener(image, EVENT_ERROR, onError);\n        if (options.loading) {\n          removeClass(item, CLASS_LOADING);\n        }\n        _this.loadImage(event);\n      }, {\n        once: true\n      });\n      addListener(image, EVENT_ERROR, onError = function onError() {\n        removeListener(image, EVENT_LOAD, onLoad);\n        if (options.loading) {\n          removeClass(item, CLASS_LOADING);\n        }\n      }, {\n        once: true\n      });\n    });\n    if (options.transition) {\n      addListener(element, EVENT_VIEWED, function () {\n        addClass(list, CLASS_TRANSITION);\n      }, {\n        once: true\n      });\n    }\n  },\n  renderList: function renderList() {\n    var index = this.index;\n    var item = this.items[index];\n    if (!item) {\n      return;\n    }\n    var next = item.nextElementSibling;\n    var gutter = parseInt(window.getComputedStyle(next || item).marginLeft, 10);\n    var offsetWidth = item.offsetWidth;\n    var outerWidth = offsetWidth + gutter;\n\n    // Place the active item in the center of the screen\n    setStyle(this.list, assign({\n      width: outerWidth * this.length - gutter\n    }, getTransforms({\n      translateX: (this.viewerData.width - offsetWidth) / 2 - outerWidth * index\n    })));\n  },\n  resetList: function resetList() {\n    var list = this.list;\n    list.innerHTML = '';\n    removeClass(list, CLASS_TRANSITION);\n    setStyle(list, getTransforms({\n      translateX: 0\n    }));\n  },\n  initImage: function initImage(done) {\n    var _this2 = this;\n    var options = this.options,\n      image = this.image,\n      viewerData = this.viewerData;\n    var footerHeight = this.footer.offsetHeight;\n    var viewerWidth = viewerData.width;\n    var viewerHeight = Math.max(viewerData.height - footerHeight, footerHeight);\n    var oldImageData = this.imageData || {};\n    var sizingImage;\n    this.imageInitializing = {\n      abort: function abort() {\n        sizingImage.onload = null;\n      }\n    };\n    sizingImage = getImageNaturalSizes(image, options, function (naturalWidth, naturalHeight) {\n      var aspectRatio = naturalWidth / naturalHeight;\n      var initialCoverage = Math.max(0, Math.min(1, options.initialCoverage));\n      var width = viewerWidth;\n      var height = viewerHeight;\n      _this2.imageInitializing = false;\n      if (viewerHeight * aspectRatio > viewerWidth) {\n        height = viewerWidth / aspectRatio;\n      } else {\n        width = viewerHeight * aspectRatio;\n      }\n      initialCoverage = isNumber(initialCoverage) ? initialCoverage : 0.9;\n      width = Math.min(width * initialCoverage, naturalWidth);\n      height = Math.min(height * initialCoverage, naturalHeight);\n      var left = (viewerWidth - width) / 2;\n      var top = (viewerHeight - height) / 2;\n      var imageData = {\n        left: left,\n        top: top,\n        x: left,\n        y: top,\n        width: width,\n        height: height,\n        oldRatio: 1,\n        ratio: width / naturalWidth,\n        aspectRatio: aspectRatio,\n        naturalWidth: naturalWidth,\n        naturalHeight: naturalHeight\n      };\n      var initialImageData = assign({}, imageData);\n      if (options.rotatable) {\n        imageData.rotate = oldImageData.rotate || 0;\n        initialImageData.rotate = 0;\n      }\n      if (options.scalable) {\n        imageData.scaleX = oldImageData.scaleX || 1;\n        imageData.scaleY = oldImageData.scaleY || 1;\n        initialImageData.scaleX = 1;\n        initialImageData.scaleY = 1;\n      }\n      _this2.imageData = imageData;\n      _this2.initialImageData = initialImageData;\n      if (done) {\n        done();\n      }\n    });\n  },\n  renderImage: function renderImage(done) {\n    var _this3 = this;\n    var image = this.image,\n      imageData = this.imageData;\n    setStyle(image, assign({\n      width: imageData.width,\n      height: imageData.height,\n      // XXX: Not to use translateX/Y to avoid image shaking when zooming\n      marginLeft: imageData.x,\n      marginTop: imageData.y\n    }, getTransforms(imageData)));\n    if (done) {\n      if ((this.viewing || this.moving || this.rotating || this.scaling || this.zooming) && this.options.transition && hasClass(image, CLASS_TRANSITION)) {\n        var onTransitionEnd = function onTransitionEnd() {\n          _this3.imageRendering = false;\n          done();\n        };\n        this.imageRendering = {\n          abort: function abort() {\n            removeListener(image, EVENT_TRANSITION_END, onTransitionEnd);\n          }\n        };\n        addListener(image, EVENT_TRANSITION_END, onTransitionEnd, {\n          once: true\n        });\n      } else {\n        done();\n      }\n    }\n  },\n  resetImage: function resetImage() {\n    var image = this.image;\n    if (image) {\n      if (this.viewing) {\n        this.viewing.abort();\n      }\n      image.parentNode.removeChild(image);\n      this.image = null;\n      this.title.innerHTML = '';\n    }\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var options = this.options,\n      viewer = this.viewer,\n      canvas = this.canvas;\n    var document = this.element.ownerDocument;\n    addListener(viewer, EVENT_CLICK, this.onClick = this.click.bind(this));\n    addListener(viewer, EVENT_DRAG_START, this.onDragStart = this.dragstart.bind(this));\n    addListener(canvas, EVENT_POINTER_DOWN, this.onPointerDown = this.pointerdown.bind(this));\n    addListener(document, EVENT_POINTER_MOVE, this.onPointerMove = this.pointermove.bind(this));\n    addListener(document, EVENT_POINTER_UP, this.onPointerUp = this.pointerup.bind(this));\n    addListener(document, EVENT_KEY_DOWN, this.onKeyDown = this.keydown.bind(this));\n    addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(viewer, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleOnDblclick) {\n      addListener(canvas, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var options = this.options,\n      viewer = this.viewer,\n      canvas = this.canvas;\n    var document = this.element.ownerDocument;\n    removeListener(viewer, EVENT_CLICK, this.onClick);\n    removeListener(viewer, EVENT_DRAG_START, this.onDragStart);\n    removeListener(canvas, EVENT_POINTER_DOWN, this.onPointerDown);\n    removeListener(document, EVENT_POINTER_MOVE, this.onPointerMove);\n    removeListener(document, EVENT_POINTER_UP, this.onPointerUp);\n    removeListener(document, EVENT_KEY_DOWN, this.onKeyDown);\n    removeListener(window, EVENT_RESIZE, this.onResize);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(viewer, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleOnDblclick) {\n      removeListener(canvas, EVENT_DBLCLICK, this.onDblclick);\n    }\n  }\n};\n\nvar handlers = {\n  click: function click(event) {\n    var options = this.options,\n      imageData = this.imageData;\n    var target = event.target;\n    var action = getData(target, DATA_ACTION);\n    if (!action && target.localName === 'img' && target.parentElement.localName === 'li') {\n      target = target.parentElement;\n      action = getData(target, DATA_ACTION);\n    }\n\n    // Cancel the emulated click when the native click event was triggered.\n    if (IS_TOUCH_DEVICE && event.isTrusted && target === this.canvas) {\n      clearTimeout(this.clickCanvasTimeout);\n    }\n    switch (action) {\n      case 'mix':\n        if (this.played) {\n          this.stop();\n        } else if (options.inline) {\n          if (this.fulled) {\n            this.exit();\n          } else {\n            this.full();\n          }\n        } else {\n          this.hide();\n        }\n        break;\n      case 'hide':\n        if (!this.pointerMoved) {\n          this.hide();\n        }\n        break;\n      case 'view':\n        this.view(getData(target, 'index'));\n        break;\n      case 'zoom-in':\n        this.zoom(0.1, true);\n        break;\n      case 'zoom-out':\n        this.zoom(-0.1, true);\n        break;\n      case 'one-to-one':\n        this.toggle();\n        break;\n      case 'reset':\n        this.reset();\n        break;\n      case 'prev':\n        this.prev(options.loop);\n        break;\n      case 'play':\n        this.play(options.fullscreen);\n        break;\n      case 'next':\n        this.next(options.loop);\n        break;\n      case 'rotate-left':\n        this.rotate(-90);\n        break;\n      case 'rotate-right':\n        this.rotate(90);\n        break;\n      case 'flip-horizontal':\n        this.scaleX(-imageData.scaleX || -1);\n        break;\n      case 'flip-vertical':\n        this.scaleY(-imageData.scaleY || -1);\n        break;\n      default:\n        if (this.played) {\n          this.stop();\n        }\n    }\n  },\n  dblclick: function dblclick(event) {\n    event.preventDefault();\n    if (this.viewed && event.target === this.image) {\n      // Cancel the emulated double click when the native dblclick event was triggered.\n      if (IS_TOUCH_DEVICE && event.isTrusted) {\n        clearTimeout(this.doubleClickImageTimeout);\n      }\n\n      // XXX: No pageX/Y properties in custom event, fallback to the original event.\n      this.toggle(event.isTrusted ? event : event.detail && event.detail.originalEvent);\n    }\n  },\n  load: function load() {\n    var _this = this;\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n      this.timeout = false;\n    }\n    var element = this.element,\n      options = this.options,\n      image = this.image,\n      index = this.index,\n      viewerData = this.viewerData;\n    removeClass(image, CLASS_INVISIBLE);\n    if (options.loading) {\n      removeClass(this.canvas, CLASS_LOADING);\n    }\n    image.style.cssText = 'height:0;' + \"margin-left:\".concat(viewerData.width / 2, \"px;\") + \"margin-top:\".concat(viewerData.height / 2, \"px;\") + 'max-width:none!important;' + 'position:relative;' + 'width:0;';\n    this.initImage(function () {\n      toggleClass(image, CLASS_MOVE, options.movable);\n      toggleClass(image, CLASS_TRANSITION, options.transition);\n      _this.renderImage(function () {\n        _this.viewed = true;\n        _this.viewing = false;\n        if (isFunction(options.viewed)) {\n          addListener(element, EVENT_VIEWED, options.viewed, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_VIEWED, {\n          originalImage: _this.images[index],\n          index: index,\n          image: image\n        }, {\n          cancelable: false\n        });\n      });\n    });\n  },\n  loadImage: function loadImage(event) {\n    var image = event.target;\n    var parent = image.parentNode;\n    var parentWidth = parent.offsetWidth || 30;\n    var parentHeight = parent.offsetHeight || 50;\n    var filled = !!getData(image, 'filled');\n    getImageNaturalSizes(image, this.options, function (naturalWidth, naturalHeight) {\n      var aspectRatio = naturalWidth / naturalHeight;\n      var width = parentWidth;\n      var height = parentHeight;\n      if (parentHeight * aspectRatio > parentWidth) {\n        if (filled) {\n          width = parentHeight * aspectRatio;\n        } else {\n          height = parentWidth / aspectRatio;\n        }\n      } else if (filled) {\n        height = parentWidth / aspectRatio;\n      } else {\n        width = parentHeight * aspectRatio;\n      }\n      setStyle(image, assign({\n        width: width,\n        height: height\n      }, getTransforms({\n        translateX: (parentWidth - width) / 2,\n        translateY: (parentHeight - height) / 2\n      })));\n    });\n  },\n  keydown: function keydown(event) {\n    var options = this.options;\n    if (!options.keyboard) {\n      return;\n    }\n    var keyCode = event.keyCode || event.which || event.charCode;\n    switch (keyCode) {\n      // Enter\n      case 13:\n        if (this.viewer.contains(event.target)) {\n          this.click(event);\n        }\n        break;\n    }\n    if (!this.fulled) {\n      return;\n    }\n    switch (keyCode) {\n      // Escape\n      case 27:\n        if (this.played) {\n          this.stop();\n        } else if (options.inline) {\n          if (this.fulled) {\n            this.exit();\n          }\n        } else {\n          this.hide();\n        }\n        break;\n\n      // Space\n      case 32:\n        if (this.played) {\n          this.stop();\n        }\n        break;\n\n      // ArrowLeft\n      case 37:\n        if (this.played && this.playing) {\n          this.playing.prev();\n        } else {\n          this.prev(options.loop);\n        }\n        break;\n\n      // ArrowUp\n      case 38:\n        // Prevent scroll on Firefox\n        event.preventDefault();\n\n        // Zoom in\n        this.zoom(options.zoomRatio, true);\n        break;\n\n      // ArrowRight\n      case 39:\n        if (this.played && this.playing) {\n          this.playing.next();\n        } else {\n          this.next(options.loop);\n        }\n        break;\n\n      // ArrowDown\n      case 40:\n        // Prevent scroll on Firefox\n        event.preventDefault();\n\n        // Zoom out\n        this.zoom(-options.zoomRatio, true);\n        break;\n\n      // Ctrl + 0\n      case 48:\n      // Fall through\n\n      // Ctrl + 1\n      // eslint-disable-next-line no-fallthrough\n      case 49:\n        if (event.ctrlKey) {\n          event.preventDefault();\n          this.toggle();\n        }\n        break;\n    }\n  },\n  dragstart: function dragstart(event) {\n    if (event.target.localName === 'img') {\n      event.preventDefault();\n    }\n  },\n  pointerdown: function pointerdown(event) {\n    var options = this.options,\n      pointers = this.pointers;\n    var buttons = event.buttons,\n      button = event.button;\n    this.pointerMoved = false;\n    if (!this.viewed || this.showing || this.viewing || this.hiding\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n\n    // Prevent default behaviours as page zooming in touch devices.\n    event.preventDefault();\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    var action = options.movable ? ACTION_MOVE : false;\n    if (options.zoomOnTouch && options.zoomable && Object.keys(pointers).length > 1) {\n      action = ACTION_ZOOM;\n    } else if (options.slideOnTouch && (event.pointerType === 'touch' || event.type === 'touchstart') && this.isSwitchable()) {\n      action = ACTION_SWITCH;\n    }\n    if (options.transition && (action === ACTION_MOVE || action === ACTION_ZOOM)) {\n      removeClass(this.image, CLASS_TRANSITION);\n    }\n    this.action = action;\n  },\n  pointermove: function pointermove(event) {\n    var pointers = this.pointers,\n      action = this.action;\n    if (!this.viewed || !action) {\n      return;\n    }\n    event.preventDefault();\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  pointerup: function pointerup(event) {\n    var _this2 = this;\n    var options = this.options,\n      action = this.action,\n      pointers = this.pointers;\n    var pointer;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        pointer = pointers[touch.identifier];\n        delete pointers[touch.identifier];\n      });\n    } else {\n      pointer = pointers[event.pointerId || 0];\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (options.transition && (action === ACTION_MOVE || action === ACTION_ZOOM)) {\n      addClass(this.image, CLASS_TRANSITION);\n    }\n    this.action = false;\n\n    // Emulate click and double click in touch devices to support backdrop and image zooming (#210).\n    if (IS_TOUCH_DEVICE && action !== ACTION_ZOOM && pointer && Date.now() - pointer.timeStamp < 500) {\n      clearTimeout(this.clickCanvasTimeout);\n      clearTimeout(this.doubleClickImageTimeout);\n      if (options.toggleOnDblclick && this.viewed && event.target === this.image) {\n        if (this.imageClicked) {\n          this.imageClicked = false;\n\n          // This timeout will be cleared later when a native dblclick event is triggering\n          this.doubleClickImageTimeout = setTimeout(function () {\n            dispatchEvent(_this2.image, EVENT_DBLCLICK, {\n              originalEvent: event\n            });\n          }, 50);\n        } else {\n          this.imageClicked = true;\n\n          // The default timing of a double click in Windows is 500 ms\n          this.doubleClickImageTimeout = setTimeout(function () {\n            _this2.imageClicked = false;\n          }, 500);\n        }\n      } else {\n        this.imageClicked = false;\n        if (options.backdrop && options.backdrop !== 'static' && event.target === this.canvas) {\n          // This timeout will be cleared later when a native click event is triggering\n          this.clickCanvasTimeout = setTimeout(function () {\n            dispatchEvent(_this2.canvas, EVENT_CLICK, {\n              originalEvent: event\n            });\n          }, 50);\n        }\n      }\n    }\n  },\n  resize: function resize() {\n    var _this3 = this;\n    if (!this.isShown || this.hiding) {\n      return;\n    }\n    if (this.fulled) {\n      this.close();\n      this.initBody();\n      this.open();\n    }\n    this.initContainer();\n    this.initViewer();\n    this.renderViewer();\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this3.renderImage();\n      });\n    }\n    if (this.played) {\n      if (this.options.fullscreen && this.fulled && !(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n        this.stop();\n        return;\n      }\n      forEach(this.player.getElementsByTagName('img'), function (image) {\n        addListener(image, EVENT_LOAD, _this3.loadImage.bind(_this3), {\n          once: true\n        });\n        dispatchEvent(image, EVENT_LOAD);\n      });\n    }\n  },\n  wheel: function wheel(event) {\n    var _this4 = this;\n    if (!this.viewed) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this4.wheeling = false;\n    }, 50);\n    var ratio = Number(this.options.zoomRatio) || 0.1;\n    var delta = 1;\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, true, null, event);\n  }\n};\n\nvar methods = {\n  /** Show the viewer (only available in modal mode)\n   * @param {boolean} [immediate=false] - Indicates if show the viewer immediately or not.\n   * @returns {Viewer} this\n   */\n  show: function show() {\n    var immediate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var element = this.element,\n      options = this.options;\n    if (options.inline || this.showing || this.isShown || this.showing) {\n      return this;\n    }\n    if (!this.ready) {\n      this.build();\n      if (this.ready) {\n        this.show(immediate);\n      }\n      return this;\n    }\n    if (isFunction(options.show)) {\n      addListener(element, EVENT_SHOW, options.show, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_SHOW) === false || !this.ready) {\n      return this;\n    }\n    if (this.hiding) {\n      this.transitioning.abort();\n    }\n    this.showing = true;\n    this.open();\n    var viewer = this.viewer;\n    removeClass(viewer, CLASS_HIDE);\n    viewer.setAttribute('role', 'dialog');\n    viewer.setAttribute('aria-labelledby', this.title.id);\n    viewer.setAttribute('aria-modal', true);\n    viewer.removeAttribute('aria-hidden');\n    if (options.transition && !immediate) {\n      var shown = this.shown.bind(this);\n      this.transitioning = {\n        abort: function abort() {\n          removeListener(viewer, EVENT_TRANSITION_END, shown);\n          removeClass(viewer, CLASS_IN);\n        }\n      };\n      addClass(viewer, CLASS_TRANSITION);\n\n      // Force reflow to enable CSS3 transition\n      viewer.initialOffsetWidth = viewer.offsetWidth;\n      addListener(viewer, EVENT_TRANSITION_END, shown, {\n        once: true\n      });\n      addClass(viewer, CLASS_IN);\n    } else {\n      addClass(viewer, CLASS_IN);\n      this.shown();\n    }\n    return this;\n  },\n  /**\n   * Hide the viewer (only available in modal mode)\n   * @param {boolean} [immediate=false] - Indicates if hide the viewer immediately or not.\n   * @returns {Viewer} this\n   */\n  hide: function hide() {\n    var _this = this;\n    var immediate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var element = this.element,\n      options = this.options;\n    if (options.inline || this.hiding || !(this.isShown || this.showing)) {\n      return this;\n    }\n    if (isFunction(options.hide)) {\n      addListener(element, EVENT_HIDE, options.hide, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_HIDE) === false) {\n      return this;\n    }\n    if (this.showing) {\n      this.transitioning.abort();\n    }\n    this.hiding = true;\n    if (this.played) {\n      this.stop();\n    } else if (this.viewing) {\n      this.viewing.abort();\n    }\n    var viewer = this.viewer,\n      image = this.image;\n    var hideImmediately = function hideImmediately() {\n      removeClass(viewer, CLASS_IN);\n      _this.hidden();\n    };\n    if (options.transition && !immediate) {\n      var onViewerTransitionEnd = function onViewerTransitionEnd(event) {\n        // Ignore all propagating `transitionend` events (#275).\n        if (event && event.target === viewer) {\n          removeListener(viewer, EVENT_TRANSITION_END, onViewerTransitionEnd);\n          _this.hidden();\n        }\n      };\n      var onImageTransitionEnd = function onImageTransitionEnd() {\n        // In case of show the viewer by `viewer.show(true)` previously (#407).\n        if (hasClass(viewer, CLASS_TRANSITION)) {\n          addListener(viewer, EVENT_TRANSITION_END, onViewerTransitionEnd);\n          removeClass(viewer, CLASS_IN);\n        } else {\n          hideImmediately();\n        }\n      };\n      this.transitioning = {\n        abort: function abort() {\n          if (_this.viewed && hasClass(image, CLASS_TRANSITION)) {\n            removeListener(image, EVENT_TRANSITION_END, onImageTransitionEnd);\n          } else if (hasClass(viewer, CLASS_TRANSITION)) {\n            removeListener(viewer, EVENT_TRANSITION_END, onViewerTransitionEnd);\n          }\n        }\n      };\n\n      // In case of hiding the viewer when holding on the image (#255),\n      // note that the `CLASS_TRANSITION` class will be removed on pointer down.\n      if (this.viewed && hasClass(image, CLASS_TRANSITION)) {\n        addListener(image, EVENT_TRANSITION_END, onImageTransitionEnd, {\n          once: true\n        });\n        this.zoomTo(0, false, null, null, true);\n      } else {\n        onImageTransitionEnd();\n      }\n    } else {\n      hideImmediately();\n    }\n    return this;\n  },\n  /**\n   * View one of the images with image's index\n   * @param {number} index - The index of the image to view.\n   * @returns {Viewer} this\n   */\n  view: function view() {\n    var _this2 = this;\n    var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.initialViewIndex;\n    index = Number(index) || 0;\n    if (this.hiding || this.played || index < 0 || index >= this.length || this.viewed && index === this.index) {\n      return this;\n    }\n    if (!this.isShown) {\n      this.index = index;\n      return this.show();\n    }\n    if (this.viewing) {\n      this.viewing.abort();\n    }\n    var element = this.element,\n      options = this.options,\n      title = this.title,\n      canvas = this.canvas;\n    var item = this.items[index];\n    var img = item.querySelector('img');\n    var url = getData(img, 'originalUrl');\n    var alt = img.getAttribute('alt');\n    var image = document.createElement('img');\n    forEach(options.inheritedAttributes, function (name) {\n      var value = img.getAttribute(name);\n      if (value !== null) {\n        image.setAttribute(name, value);\n      }\n    });\n    image.src = url;\n    image.alt = alt;\n    if (isFunction(options.view)) {\n      addListener(element, EVENT_VIEW, options.view, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_VIEW, {\n      originalImage: this.images[index],\n      index: index,\n      image: image\n    }) === false || !this.isShown || this.hiding || this.played) {\n      return this;\n    }\n    var activeItem = this.items[this.index];\n    if (activeItem) {\n      removeClass(activeItem, CLASS_ACTIVE);\n      activeItem.removeAttribute('aria-selected');\n    }\n    addClass(item, CLASS_ACTIVE);\n    item.setAttribute('aria-selected', true);\n    if (options.focus) {\n      item.focus();\n    }\n    this.image = image;\n    this.viewed = false;\n    this.index = index;\n    this.imageData = {};\n    addClass(image, CLASS_INVISIBLE);\n    if (options.loading) {\n      addClass(canvas, CLASS_LOADING);\n    }\n    canvas.innerHTML = '';\n    canvas.appendChild(image);\n\n    // Center current item\n    this.renderList();\n\n    // Clear title\n    title.innerHTML = '';\n\n    // Generate title after viewed\n    var onViewed = function onViewed() {\n      var imageData = _this2.imageData;\n      var render = Array.isArray(options.title) ? options.title[1] : options.title;\n      title.innerHTML = escapeHTMLEntities(isFunction(render) ? render.call(_this2, image, imageData) : \"\".concat(alt, \" (\").concat(imageData.naturalWidth, \" \\xD7 \").concat(imageData.naturalHeight, \")\"));\n    };\n    var onLoad;\n    var onError;\n    addListener(element, EVENT_VIEWED, onViewed, {\n      once: true\n    });\n    this.viewing = {\n      abort: function abort() {\n        removeListener(element, EVENT_VIEWED, onViewed);\n        if (image.complete) {\n          if (_this2.imageRendering) {\n            _this2.imageRendering.abort();\n          } else if (_this2.imageInitializing) {\n            _this2.imageInitializing.abort();\n          }\n        } else {\n          // Cancel download to save bandwidth.\n          image.src = '';\n          removeListener(image, EVENT_LOAD, onLoad);\n          if (_this2.timeout) {\n            clearTimeout(_this2.timeout);\n          }\n        }\n      }\n    };\n    if (image.complete) {\n      this.load();\n    } else {\n      addListener(image, EVENT_LOAD, onLoad = function onLoad() {\n        removeListener(image, EVENT_ERROR, onError);\n        _this2.load();\n      }, {\n        once: true\n      });\n      addListener(image, EVENT_ERROR, onError = function onError() {\n        removeListener(image, EVENT_LOAD, onLoad);\n        if (_this2.timeout) {\n          clearTimeout(_this2.timeout);\n          _this2.timeout = false;\n        }\n        removeClass(image, CLASS_INVISIBLE);\n        if (options.loading) {\n          removeClass(_this2.canvas, CLASS_LOADING);\n        }\n      }, {\n        once: true\n      });\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n      }\n\n      // Make the image visible if it fails to load within 1s\n      this.timeout = setTimeout(function () {\n        removeClass(image, CLASS_INVISIBLE);\n        _this2.timeout = false;\n      }, 1000);\n    }\n    return this;\n  },\n  /**\n   * View the previous image\n   * @param {boolean} [loop=false] - Indicate if view the last one\n   * when it is the first one at present.\n   * @returns {Viewer} this\n   */\n  prev: function prev() {\n    var loop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var index = this.index - 1;\n    if (index < 0) {\n      index = loop ? this.length - 1 : 0;\n    }\n    this.view(index);\n    return this;\n  },\n  /**\n   * View the next image\n   * @param {boolean} [loop=false] - Indicate if view the first one\n   * when it is the last one at present.\n   * @returns {Viewer} this\n   */\n  next: function next() {\n    var loop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var maxIndex = this.length - 1;\n    var index = this.index + 1;\n    if (index > maxIndex) {\n      index = loop ? 0 : maxIndex;\n    }\n    this.view(index);\n    return this;\n  },\n  /**\n   * Move the image with relative offsets.\n   * @param {number} x - The moving distance in the horizontal direction.\n   * @param {number} [y=x] The moving distance in the vertical direction.\n   * @returns {Viewer} this\n   */\n  move: function move(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var imageData = this.imageData;\n    this.moveTo(isUndefined(x) ? x : imageData.x + Number(x), isUndefined(y) ? y : imageData.y + Number(y));\n    return this;\n  },\n  /**\n   * Move the image to an absolute point.\n   * @param {number} x - The new position in the horizontal direction.\n   * @param {number} [y=x] - The new position in the vertical direction.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  moveTo: function moveTo(x) {\n    var _this3 = this;\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var _originalEvent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    x = Number(x);\n    y = Number(y);\n    if (this.viewed && !this.played && options.movable) {\n      var oldX = imageData.x;\n      var oldY = imageData.y;\n      var changed = false;\n      if (isNumber(x)) {\n        changed = true;\n      } else {\n        x = oldX;\n      }\n      if (isNumber(y)) {\n        changed = true;\n      } else {\n        y = oldY;\n      }\n      if (changed) {\n        if (isFunction(options.move)) {\n          addListener(element, EVENT_MOVE, options.move, {\n            once: true\n          });\n        }\n        if (dispatchEvent(element, EVENT_MOVE, {\n          x: x,\n          y: y,\n          oldX: oldX,\n          oldY: oldY,\n          originalEvent: _originalEvent\n        }) === false) {\n          return this;\n        }\n        imageData.x = x;\n        imageData.y = y;\n        imageData.left = x;\n        imageData.top = y;\n        this.moving = true;\n        this.renderImage(function () {\n          _this3.moving = false;\n          if (isFunction(options.moved)) {\n            addListener(element, EVENT_MOVED, options.moved, {\n              once: true\n            });\n          }\n          dispatchEvent(element, EVENT_MOVED, {\n            x: x,\n            y: y,\n            oldX: oldX,\n            oldY: oldY,\n            originalEvent: _originalEvent\n          }, {\n            cancelable: false\n          });\n        });\n      }\n    }\n    return this;\n  },\n  /**\n   * Rotate the image with a relative degree.\n   * @param {number} degree - The rotate degree.\n   * @returns {Viewer} this\n   */\n  rotate: function rotate(degree) {\n    this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n    return this;\n  },\n  /**\n   * Rotate the image to an absolute degree.\n   * @param {number} degree - The rotate degree.\n   * @returns {Viewer} this\n   */\n  rotateTo: function rotateTo(degree) {\n    var _this4 = this;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    degree = Number(degree);\n    if (isNumber(degree) && this.viewed && !this.played && options.rotatable) {\n      var oldDegree = imageData.rotate;\n      if (isFunction(options.rotate)) {\n        addListener(element, EVENT_ROTATE, options.rotate, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_ROTATE, {\n        degree: degree,\n        oldDegree: oldDegree\n      }) === false) {\n        return this;\n      }\n      imageData.rotate = degree;\n      this.rotating = true;\n      this.renderImage(function () {\n        _this4.rotating = false;\n        if (isFunction(options.rotated)) {\n          addListener(element, EVENT_ROTATED, options.rotated, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_ROTATED, {\n          degree: degree,\n          oldDegree: oldDegree\n        }, {\n          cancelable: false\n        });\n      });\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Viewer} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    this.scale(_scaleX, this.imageData.scaleY);\n    return this;\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Viewer} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    this.scale(this.imageData.scaleX, _scaleY);\n    return this;\n  },\n  /**\n   * Scale the image.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Viewer} this\n   */\n  scale: function scale(scaleX) {\n    var _this5 = this;\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.viewed && !this.played && options.scalable) {\n      var oldScaleX = imageData.scaleX;\n      var oldScaleY = imageData.scaleY;\n      var changed = false;\n      if (isNumber(scaleX)) {\n        changed = true;\n      } else {\n        scaleX = oldScaleX;\n      }\n      if (isNumber(scaleY)) {\n        changed = true;\n      } else {\n        scaleY = oldScaleY;\n      }\n      if (changed) {\n        if (isFunction(options.scale)) {\n          addListener(element, EVENT_SCALE, options.scale, {\n            once: true\n          });\n        }\n        if (dispatchEvent(element, EVENT_SCALE, {\n          scaleX: scaleX,\n          scaleY: scaleY,\n          oldScaleX: oldScaleX,\n          oldScaleY: oldScaleY\n        }) === false) {\n          return this;\n        }\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n        this.scaling = true;\n        this.renderImage(function () {\n          _this5.scaling = false;\n          if (isFunction(options.scaled)) {\n            addListener(element, EVENT_SCALED, options.scaled, {\n              once: true\n            });\n          }\n          dispatchEvent(element, EVENT_SCALED, {\n            scaleX: scaleX,\n            scaleY: scaleY,\n            oldScaleX: oldScaleX,\n            oldScaleY: oldScaleY\n          }, {\n            cancelable: false\n          });\n        });\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the image with a relative ratio.\n   * @param {number} ratio - The target ratio.\n   * @param {boolean} [showTooltip=false] - Indicates whether to show the tooltip.\n   * @param {Object} [pivot] - The pivot point coordinate for zooming.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  zoom: function zoom(ratio) {\n    var showTooltip = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var pivot = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var _originalEvent = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    var imageData = this.imageData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    this.zoomTo(imageData.width * ratio / imageData.naturalWidth, showTooltip, pivot, _originalEvent);\n    return this;\n  },\n  /**\n   * Zoom the image to an absolute ratio.\n   * @param {number} ratio - The target ratio.\n   * @param {boolean} [showTooltip] - Indicates whether to show the tooltip.\n   * @param {Object} [pivot] - The pivot point coordinate for zooming.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @param {Event} [_zoomable=false] - Indicates if the current zoom is available or not.\n   * @returns {Viewer} this\n   */\n  zoomTo: function zoomTo(ratio) {\n    var _this6 = this;\n    var showTooltip = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var pivot = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var _originalEvent = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    var _zoomable = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    var element = this.element,\n      options = this.options,\n      pointers = this.pointers,\n      imageData = this.imageData;\n    var x = imageData.x,\n      y = imageData.y,\n      width = imageData.width,\n      height = imageData.height,\n      naturalWidth = imageData.naturalWidth,\n      naturalHeight = imageData.naturalHeight;\n    ratio = Math.max(0, ratio);\n    if (isNumber(ratio) && this.viewed && !this.played && (_zoomable || options.zoomable)) {\n      if (!_zoomable) {\n        var minZoomRatio = Math.max(0.01, options.minZoomRatio);\n        var maxZoomRatio = Math.min(100, options.maxZoomRatio);\n        ratio = Math.min(Math.max(ratio, minZoomRatio), maxZoomRatio);\n      }\n      if (_originalEvent) {\n        switch (_originalEvent.type) {\n          case 'wheel':\n            if (options.zoomRatio >= 0.055 && ratio > 0.95 && ratio < 1.05) {\n              ratio = 1;\n            }\n            break;\n          case 'pointermove':\n          case 'touchmove':\n          case 'mousemove':\n            if (ratio > 0.99 && ratio < 1.01) {\n              ratio = 1;\n            }\n            break;\n        }\n      }\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      var offsetWidth = newWidth - width;\n      var offsetHeight = newHeight - height;\n      var oldRatio = imageData.ratio;\n      if (isFunction(options.zoom)) {\n        addListener(element, EVENT_ZOOM, options.zoom, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: oldRatio,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      this.zooming = true;\n      if (_originalEvent) {\n        var offset = getOffset(this.viewer);\n        var center = pointers && Object.keys(pointers).length > 0 ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        imageData.x -= offsetWidth * ((center.pageX - offset.left - x) / width);\n        imageData.y -= offsetHeight * ((center.pageY - offset.top - y) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        imageData.x -= offsetWidth * ((pivot.x - x) / width);\n        imageData.y -= offsetHeight * ((pivot.y - y) / height);\n      } else {\n        // Zoom from the center of the image\n        imageData.x -= offsetWidth / 2;\n        imageData.y -= offsetHeight / 2;\n      }\n      imageData.left = imageData.x;\n      imageData.top = imageData.y;\n      imageData.width = newWidth;\n      imageData.height = newHeight;\n      imageData.oldRatio = oldRatio;\n      imageData.ratio = ratio;\n      this.renderImage(function () {\n        _this6.zooming = false;\n        if (isFunction(options.zoomed)) {\n          addListener(element, EVENT_ZOOMED, options.zoomed, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_ZOOMED, {\n          ratio: ratio,\n          oldRatio: oldRatio,\n          originalEvent: _originalEvent\n        }, {\n          cancelable: false\n        });\n      });\n      if (showTooltip) {\n        this.tooltip();\n      }\n    }\n    return this;\n  },\n  /**\n   * Play the images\n   * @param {boolean|FullscreenOptions} [fullscreen=false] - Indicate if request fullscreen or not.\n   * @returns {Viewer} this\n   */\n  play: function play() {\n    var _this7 = this;\n    var fullscreen = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!this.isShown || this.played) {\n      return this;\n    }\n    var element = this.element,\n      options = this.options;\n    if (isFunction(options.play)) {\n      addListener(element, EVENT_PLAY, options.play, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_PLAY) === false) {\n      return this;\n    }\n    var player = this.player;\n    var onLoad = this.loadImage.bind(this);\n    var list = [];\n    var total = 0;\n    var index = 0;\n    this.played = true;\n    this.onLoadWhenPlay = onLoad;\n    if (fullscreen) {\n      this.requestFullscreen(fullscreen);\n    }\n    addClass(player, CLASS_SHOW);\n    forEach(this.items, function (item, i) {\n      var img = item.querySelector('img');\n      var image = document.createElement('img');\n      image.src = getData(img, 'originalUrl');\n      image.alt = img.getAttribute('alt');\n      image.referrerPolicy = img.referrerPolicy;\n      total += 1;\n      addClass(image, CLASS_FADE);\n      toggleClass(image, CLASS_TRANSITION, options.transition);\n      if (hasClass(item, CLASS_ACTIVE)) {\n        addClass(image, CLASS_IN);\n        index = i;\n      }\n      list.push(image);\n      addListener(image, EVENT_LOAD, onLoad, {\n        once: true\n      });\n      player.appendChild(image);\n    });\n    if (isNumber(options.interval) && options.interval > 0) {\n      var prev = function prev() {\n        clearTimeout(_this7.playing.timeout);\n        removeClass(list[index], CLASS_IN);\n        index -= 1;\n        index = index >= 0 ? index : total - 1;\n        addClass(list[index], CLASS_IN);\n        _this7.playing.timeout = setTimeout(prev, options.interval);\n      };\n      var next = function next() {\n        clearTimeout(_this7.playing.timeout);\n        removeClass(list[index], CLASS_IN);\n        index += 1;\n        index = index < total ? index : 0;\n        addClass(list[index], CLASS_IN);\n        _this7.playing.timeout = setTimeout(next, options.interval);\n      };\n      if (total > 1) {\n        this.playing = {\n          prev: prev,\n          next: next,\n          timeout: setTimeout(next, options.interval)\n        };\n      }\n    }\n    return this;\n  },\n  // Stop play\n  stop: function stop() {\n    var _this8 = this;\n    if (!this.played) {\n      return this;\n    }\n    var element = this.element,\n      options = this.options;\n    if (isFunction(options.stop)) {\n      addListener(element, EVENT_STOP, options.stop, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_STOP) === false) {\n      return this;\n    }\n    var player = this.player;\n    clearTimeout(this.playing.timeout);\n    this.playing = false;\n    this.played = false;\n    forEach(player.getElementsByTagName('img'), function (image) {\n      removeListener(image, EVENT_LOAD, _this8.onLoadWhenPlay);\n    });\n    removeClass(player, CLASS_SHOW);\n    player.innerHTML = '';\n    this.exitFullscreen();\n    return this;\n  },\n  // Enter modal mode (only available in inline mode)\n  full: function full() {\n    var _this9 = this;\n    var options = this.options,\n      viewer = this.viewer,\n      image = this.image,\n      list = this.list;\n    if (!this.isShown || this.played || this.fulled || !options.inline) {\n      return this;\n    }\n    this.fulled = true;\n    this.open();\n    addClass(this.button, CLASS_FULLSCREEN_EXIT);\n    if (options.transition) {\n      removeClass(list, CLASS_TRANSITION);\n      if (this.viewed) {\n        removeClass(image, CLASS_TRANSITION);\n      }\n    }\n    addClass(viewer, CLASS_FIXED);\n    viewer.setAttribute('role', 'dialog');\n    viewer.setAttribute('aria-labelledby', this.title.id);\n    viewer.setAttribute('aria-modal', true);\n    viewer.removeAttribute('style');\n    setStyle(viewer, {\n      zIndex: options.zIndex\n    });\n    if (options.focus) {\n      this.enforceFocus();\n    }\n    this.initContainer();\n    this.viewerData = assign({}, this.containerData);\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this9.renderImage(function () {\n          if (options.transition) {\n            setTimeout(function () {\n              addClass(image, CLASS_TRANSITION);\n              addClass(list, CLASS_TRANSITION);\n            }, 0);\n          }\n        });\n      });\n    }\n    return this;\n  },\n  // Exit modal mode (only available in inline mode)\n  exit: function exit() {\n    var _this10 = this;\n    var options = this.options,\n      viewer = this.viewer,\n      image = this.image,\n      list = this.list;\n    if (!this.isShown || this.played || !this.fulled || !options.inline) {\n      return this;\n    }\n    this.fulled = false;\n    this.close();\n    removeClass(this.button, CLASS_FULLSCREEN_EXIT);\n    if (options.transition) {\n      removeClass(list, CLASS_TRANSITION);\n      if (this.viewed) {\n        removeClass(image, CLASS_TRANSITION);\n      }\n    }\n    if (options.focus) {\n      this.clearEnforceFocus();\n    }\n    viewer.removeAttribute('role');\n    viewer.removeAttribute('aria-labelledby');\n    viewer.removeAttribute('aria-modal');\n    removeClass(viewer, CLASS_FIXED);\n    setStyle(viewer, {\n      zIndex: options.zIndexInline\n    });\n    this.viewerData = assign({}, this.parentData);\n    this.renderViewer();\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this10.renderImage(function () {\n          if (options.transition) {\n            setTimeout(function () {\n              addClass(image, CLASS_TRANSITION);\n              addClass(list, CLASS_TRANSITION);\n            }, 0);\n          }\n        });\n      });\n    }\n    return this;\n  },\n  // Show the current ratio of the image with percentage\n  tooltip: function tooltip() {\n    var _this11 = this;\n    var options = this.options,\n      tooltipBox = this.tooltipBox,\n      imageData = this.imageData;\n    if (!this.viewed || this.played || !options.tooltip) {\n      return this;\n    }\n    tooltipBox.textContent = \"\".concat(Math.round(imageData.ratio * 100), \"%\");\n    if (!this.tooltipping) {\n      if (options.transition) {\n        if (this.fading) {\n          dispatchEvent(tooltipBox, EVENT_TRANSITION_END);\n        }\n        addClass(tooltipBox, CLASS_SHOW);\n        addClass(tooltipBox, CLASS_FADE);\n        addClass(tooltipBox, CLASS_TRANSITION);\n        tooltipBox.removeAttribute('aria-hidden');\n\n        // Force reflow to enable CSS3 transition\n        tooltipBox.initialOffsetWidth = tooltipBox.offsetWidth;\n        addClass(tooltipBox, CLASS_IN);\n      } else {\n        addClass(tooltipBox, CLASS_SHOW);\n        tooltipBox.removeAttribute('aria-hidden');\n      }\n    } else {\n      clearTimeout(this.tooltipping);\n    }\n    this.tooltipping = setTimeout(function () {\n      if (options.transition) {\n        addListener(tooltipBox, EVENT_TRANSITION_END, function () {\n          removeClass(tooltipBox, CLASS_SHOW);\n          removeClass(tooltipBox, CLASS_FADE);\n          removeClass(tooltipBox, CLASS_TRANSITION);\n          tooltipBox.setAttribute('aria-hidden', true);\n          _this11.fading = false;\n        }, {\n          once: true\n        });\n        removeClass(tooltipBox, CLASS_IN);\n        _this11.fading = true;\n      } else {\n        removeClass(tooltipBox, CLASS_SHOW);\n        tooltipBox.setAttribute('aria-hidden', true);\n      }\n      _this11.tooltipping = false;\n    }, 1000);\n    return this;\n  },\n  /**\n   * Toggle the image size between its current size and natural size\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  toggle: function toggle() {\n    var _originalEvent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    if (this.imageData.ratio === 1) {\n      this.zoomTo(this.imageData.oldRatio, true, null, _originalEvent);\n    } else {\n      this.zoomTo(1, true, null, _originalEvent);\n    }\n    return this;\n  },\n  // Reset the image to its initial state\n  reset: function reset() {\n    if (this.viewed && !this.played) {\n      this.imageData = assign({}, this.initialImageData);\n      this.renderImage();\n    }\n    return this;\n  },\n  // Update viewer when images changed\n  update: function update() {\n    var _this12 = this;\n    var element = this.element,\n      options = this.options,\n      isImg = this.isImg;\n\n    // Destroy viewer if the target image was deleted\n    if (isImg && !element.parentNode) {\n      return this.destroy();\n    }\n    var images = [];\n    forEach(isImg ? [element] : element.querySelectorAll('img'), function (image) {\n      if (isFunction(options.filter)) {\n        if (options.filter.call(_this12, image)) {\n          images.push(image);\n        }\n      } else if (_this12.getImageURL(image)) {\n        images.push(image);\n      }\n    });\n    if (!images.length) {\n      return this;\n    }\n    this.images = images;\n    this.length = images.length;\n    if (this.ready) {\n      var changedIndexes = [];\n      forEach(this.items, function (item, i) {\n        var img = item.querySelector('img');\n        var image = images[i];\n        if (image && img) {\n          if (image.src !== img.src\n\n          // Title changed (#408)\n          || image.alt !== img.alt) {\n            changedIndexes.push(i);\n          }\n        } else {\n          changedIndexes.push(i);\n        }\n      });\n      setStyle(this.list, {\n        width: 'auto'\n      });\n      this.initList();\n      if (this.isShown) {\n        if (this.length) {\n          if (this.viewed) {\n            var changedIndex = changedIndexes.indexOf(this.index);\n            if (changedIndex >= 0) {\n              this.viewed = false;\n              this.view(Math.max(Math.min(this.index - changedIndex, this.length - 1), 0));\n            } else {\n              var activeItem = this.items[this.index];\n\n              // Reactivate the current viewing item after reset the list.\n              addClass(activeItem, CLASS_ACTIVE);\n              activeItem.setAttribute('aria-selected', true);\n            }\n          }\n        } else {\n          this.image = null;\n          this.viewed = false;\n          this.index = 0;\n          this.imageData = {};\n          this.canvas.innerHTML = '';\n          this.title.innerHTML = '';\n        }\n      }\n    } else {\n      this.build();\n    }\n    return this;\n  },\n  // Destroy the viewer\n  destroy: function destroy() {\n    var element = this.element,\n      options = this.options;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    this.destroyed = true;\n    if (this.ready) {\n      if (this.played) {\n        this.stop();\n      }\n      if (options.inline) {\n        if (this.fulled) {\n          this.exit();\n        }\n        this.unbind();\n      } else if (this.isShown) {\n        if (this.viewing) {\n          if (this.imageRendering) {\n            this.imageRendering.abort();\n          } else if (this.imageInitializing) {\n            this.imageInitializing.abort();\n          }\n        }\n        if (this.hiding) {\n          this.transitioning.abort();\n        }\n        this.hidden();\n      } else if (this.showing) {\n        this.transitioning.abort();\n        this.hidden();\n      }\n      this.ready = false;\n      this.viewer.parentNode.removeChild(this.viewer);\n    } else if (options.inline) {\n      if (this.delaying) {\n        this.delaying.abort();\n      } else if (this.initializing) {\n        this.initializing.abort();\n      }\n    }\n    if (!options.inline) {\n      removeListener(element, EVENT_CLICK, this.onStart);\n    }\n    element[NAMESPACE] = undefined;\n    return this;\n  }\n};\n\nvar others = {\n  getImageURL: function getImageURL(image) {\n    var url = this.options.url;\n    if (isString(url)) {\n      url = image.getAttribute(url);\n    } else if (isFunction(url)) {\n      url = url.call(this, image);\n    } else {\n      url = '';\n    }\n    return url;\n  },\n  enforceFocus: function enforceFocus() {\n    var _this = this;\n    this.clearEnforceFocus();\n    addListener(document, EVENT_FOCUSIN, this.onFocusin = function (event) {\n      var viewer = _this.viewer;\n      var target = event.target;\n      if (target === document || target === viewer || viewer.contains(target)) {\n        return;\n      }\n      while (target) {\n        // Avoid conflicts with other modals (#474, #540)\n        if (target.getAttribute('tabindex') !== null || target.getAttribute('aria-modal') === 'true') {\n          return;\n        }\n        target = target.parentElement;\n      }\n      viewer.focus();\n    });\n  },\n  clearEnforceFocus: function clearEnforceFocus() {\n    if (this.onFocusin) {\n      removeListener(document, EVENT_FOCUSIN, this.onFocusin);\n      this.onFocusin = null;\n    }\n  },\n  open: function open() {\n    var body = this.body;\n    addClass(body, CLASS_OPEN);\n    if (this.scrollbarWidth > 0) {\n      body.style.paddingRight = \"\".concat(this.scrollbarWidth + (parseFloat(this.initialBodyComputedPaddingRight) || 0), \"px\");\n    }\n  },\n  close: function close() {\n    var body = this.body;\n    removeClass(body, CLASS_OPEN);\n    if (this.scrollbarWidth > 0) {\n      body.style.paddingRight = this.initialBodyPaddingRight;\n    }\n  },\n  shown: function shown() {\n    var element = this.element,\n      options = this.options,\n      viewer = this.viewer;\n    this.fulled = true;\n    this.isShown = true;\n    this.render();\n    this.bind();\n    this.showing = false;\n    if (options.focus) {\n      viewer.focus();\n      this.enforceFocus();\n    }\n    if (isFunction(options.shown)) {\n      addListener(element, EVENT_SHOWN, options.shown, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_SHOWN) === false) {\n      return;\n    }\n    if (this.ready && this.isShown && !this.hiding) {\n      this.view(this.index);\n    }\n  },\n  hidden: function hidden() {\n    var element = this.element,\n      options = this.options,\n      viewer = this.viewer;\n    if (options.fucus) {\n      this.clearEnforceFocus();\n    }\n    this.close();\n    this.unbind();\n    addClass(viewer, CLASS_HIDE);\n    viewer.removeAttribute('role');\n    viewer.removeAttribute('aria-labelledby');\n    viewer.removeAttribute('aria-modal');\n    viewer.setAttribute('aria-hidden', true);\n    this.resetList();\n    this.resetImage();\n    this.fulled = false;\n    this.viewed = false;\n    this.isShown = false;\n    this.hiding = false;\n    if (!this.destroyed) {\n      if (isFunction(options.hidden)) {\n        addListener(element, EVENT_HIDDEN, options.hidden, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_HIDDEN, null, {\n        cancelable: false\n      });\n    }\n  },\n  requestFullscreen: function requestFullscreen(options) {\n    var document = this.element.ownerDocument;\n    if (this.fulled && !(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n      var documentElement = document.documentElement;\n\n      // Element.requestFullscreen()\n      if (documentElement.requestFullscreen) {\n        // Avoid TypeError when convert `options` to dictionary\n        if (isPlainObject(options)) {\n          documentElement.requestFullscreen(options);\n        } else {\n          documentElement.requestFullscreen();\n        }\n      } else if (documentElement.webkitRequestFullscreen) {\n        documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);\n      } else if (documentElement.mozRequestFullScreen) {\n        documentElement.mozRequestFullScreen();\n      } else if (documentElement.msRequestFullscreen) {\n        documentElement.msRequestFullscreen();\n      }\n    }\n  },\n  exitFullscreen: function exitFullscreen() {\n    var document = this.element.ownerDocument;\n    if (this.fulled && (document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n      // Document.exitFullscreen()\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen();\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen();\n      }\n    }\n  },\n  change: function change(event) {\n    var options = this.options,\n      pointers = this.pointers;\n    var pointer = pointers[Object.keys(pointers)[0]];\n\n    // In the case of the `pointers` object is empty (#421)\n    if (!pointer) {\n      return;\n    }\n    var offsetX = pointer.endX - pointer.startX;\n    var offsetY = pointer.endY - pointer.startY;\n    switch (this.action) {\n      // Move the current image\n      case ACTION_MOVE:\n        if (offsetX !== 0 || offsetY !== 0) {\n          this.pointerMoved = true;\n          this.move(offsetX, offsetY, event);\n        }\n        break;\n\n      // Zoom the current image\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), false, null, event);\n        break;\n      case ACTION_SWITCH:\n        {\n          this.action = 'switched';\n          var absoluteOffsetX = Math.abs(offsetX);\n          if (absoluteOffsetX > 1 && absoluteOffsetX > Math.abs(offsetY)) {\n            // Empty `pointers` as `touchend` event will not be fired after swiped in iOS browsers.\n            this.pointers = {};\n            if (offsetX > 1) {\n              this.prev(options.loop);\n            } else if (offsetX < -1) {\n              this.next(options.loop);\n            }\n          }\n          break;\n        }\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  },\n  isSwitchable: function isSwitchable() {\n    var imageData = this.imageData,\n      viewerData = this.viewerData;\n    return this.length > 1 && imageData.x >= 0 && imageData.y >= 0 && imageData.width <= viewerData.width && imageData.height <= viewerData.height;\n  }\n};\n\nvar AnotherViewer = WINDOW.Viewer;\nvar getUniqueID = function (id) {\n  return function () {\n    id += 1;\n    return id;\n  };\n}(-1);\nvar Viewer = /*#__PURE__*/function () {\n  /**\n   * Create a new Viewer.\n   * @param {Element} element - The target element for viewing.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Viewer(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Viewer);\n    if (!element || element.nodeType !== 1) {\n      throw new Error('The first argument is required and must be an element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.action = false;\n    this.fading = false;\n    this.fulled = false;\n    this.hiding = false;\n    this.imageClicked = false;\n    this.imageData = {};\n    this.index = this.options.initialViewIndex;\n    this.isImg = false;\n    this.isShown = false;\n    this.length = 0;\n    this.moving = false;\n    this.played = false;\n    this.playing = false;\n    this.pointers = {};\n    this.ready = false;\n    this.rotating = false;\n    this.scaling = false;\n    this.showing = false;\n    this.timeout = false;\n    this.tooltipping = false;\n    this.viewed = false;\n    this.viewing = false;\n    this.wheeling = false;\n    this.zooming = false;\n    this.pointerMoved = false;\n    this.id = getUniqueID();\n    this.init();\n  }\n  _createClass(Viewer, [{\n    key: \"init\",\n    value: function init() {\n      var _this = this;\n      var element = this.element,\n        options = this.options;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n\n      // The `focus` option requires the `keyboard` option set to `true`.\n      if (options.focus && !options.keyboard) {\n        options.focus = false;\n      }\n      var isImg = element.localName === 'img';\n      var images = [];\n      forEach(isImg ? [element] : element.querySelectorAll('img'), function (image) {\n        if (isFunction(options.filter)) {\n          if (options.filter.call(_this, image)) {\n            images.push(image);\n          }\n        } else if (_this.getImageURL(image)) {\n          images.push(image);\n        }\n      });\n      this.isImg = isImg;\n      this.length = images.length;\n      this.images = images;\n      this.initBody();\n\n      // Override `transition` option if it is not supported\n      if (isUndefined(document.createElement(NAMESPACE).style.transition)) {\n        options.transition = false;\n      }\n      if (options.inline) {\n        var count = 0;\n        var progress = function progress() {\n          count += 1;\n          if (count === _this.length) {\n            var timeout;\n            _this.initializing = false;\n            _this.delaying = {\n              abort: function abort() {\n                clearTimeout(timeout);\n              }\n            };\n\n            // build asynchronously to keep `this.viewer` is accessible in `ready` event handler.\n            timeout = setTimeout(function () {\n              _this.delaying = false;\n              _this.build();\n            }, 0);\n          }\n        };\n        this.initializing = {\n          abort: function abort() {\n            forEach(images, function (image) {\n              if (!image.complete) {\n                removeListener(image, EVENT_LOAD, progress);\n                removeListener(image, EVENT_ERROR, progress);\n              }\n            });\n          }\n        };\n        forEach(images, function (image) {\n          if (image.complete) {\n            progress();\n          } else {\n            var onLoad;\n            var onError;\n            addListener(image, EVENT_LOAD, onLoad = function onLoad() {\n              removeListener(image, EVENT_ERROR, onError);\n              progress();\n            }, {\n              once: true\n            });\n            addListener(image, EVENT_ERROR, onError = function onError() {\n              removeListener(image, EVENT_LOAD, onLoad);\n              progress();\n            }, {\n              once: true\n            });\n          }\n        });\n      } else {\n        addListener(element, EVENT_CLICK, this.onStart = function (_ref) {\n          var target = _ref.target;\n          if (target.localName === 'img' && (!isFunction(options.filter) || options.filter.call(_this, target))) {\n            _this.view(_this.images.indexOf(target));\n          }\n        });\n      }\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options;\n      var parent = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var viewer = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var title = viewer.querySelector(\".\".concat(NAMESPACE, \"-title\"));\n      var toolbar = viewer.querySelector(\".\".concat(NAMESPACE, \"-toolbar\"));\n      var navbar = viewer.querySelector(\".\".concat(NAMESPACE, \"-navbar\"));\n      var button = viewer.querySelector(\".\".concat(NAMESPACE, \"-button\"));\n      var canvas = viewer.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      this.parent = parent;\n      this.viewer = viewer;\n      this.title = title;\n      this.toolbar = toolbar;\n      this.navbar = navbar;\n      this.button = button;\n      this.canvas = canvas;\n      this.footer = viewer.querySelector(\".\".concat(NAMESPACE, \"-footer\"));\n      this.tooltipBox = viewer.querySelector(\".\".concat(NAMESPACE, \"-tooltip\"));\n      this.player = viewer.querySelector(\".\".concat(NAMESPACE, \"-player\"));\n      this.list = viewer.querySelector(\".\".concat(NAMESPACE, \"-list\"));\n      viewer.id = \"\".concat(NAMESPACE).concat(this.id);\n      title.id = \"\".concat(NAMESPACE, \"Title\").concat(this.id);\n      addClass(title, !options.title ? CLASS_HIDE : getResponsiveClass(Array.isArray(options.title) ? options.title[0] : options.title));\n      addClass(navbar, !options.navbar ? CLASS_HIDE : getResponsiveClass(options.navbar));\n      toggleClass(button, CLASS_HIDE, !options.button);\n      if (options.keyboard) {\n        button.setAttribute('tabindex', 0);\n      }\n      if (options.backdrop) {\n        addClass(viewer, \"\".concat(NAMESPACE, \"-backdrop\"));\n        if (!options.inline && options.backdrop !== 'static') {\n          setData(canvas, DATA_ACTION, 'hide');\n        }\n      }\n      if (isString(options.className) && options.className) {\n        // In case there are multiple class names\n        options.className.split(REGEXP_SPACES).forEach(function (className) {\n          addClass(viewer, className);\n        });\n      }\n      if (options.toolbar) {\n        var list = document.createElement('ul');\n        var custom = isPlainObject(options.toolbar);\n        var zoomButtons = BUTTONS.slice(0, 3);\n        var rotateButtons = BUTTONS.slice(7, 9);\n        var scaleButtons = BUTTONS.slice(9);\n        if (!custom) {\n          addClass(toolbar, getResponsiveClass(options.toolbar));\n        }\n        forEach(custom ? options.toolbar : BUTTONS, function (value, index) {\n          var deep = custom && isPlainObject(value);\n          var name = custom ? hyphenate(index) : value;\n          var show = deep && !isUndefined(value.show) ? value.show : value;\n          if (!show || !options.zoomable && zoomButtons.indexOf(name) !== -1 || !options.rotatable && rotateButtons.indexOf(name) !== -1 || !options.scalable && scaleButtons.indexOf(name) !== -1) {\n            return;\n          }\n          var size = deep && !isUndefined(value.size) ? value.size : value;\n          var click = deep && !isUndefined(value.click) ? value.click : value;\n          var item = document.createElement('li');\n          if (options.keyboard) {\n            item.setAttribute('tabindex', 0);\n          }\n          item.setAttribute('role', 'button');\n          addClass(item, \"\".concat(NAMESPACE, \"-\").concat(name));\n          if (!isFunction(click)) {\n            setData(item, DATA_ACTION, name);\n          }\n          if (isNumber(show)) {\n            addClass(item, getResponsiveClass(show));\n          }\n          if (['small', 'large'].indexOf(size) !== -1) {\n            addClass(item, \"\".concat(NAMESPACE, \"-\").concat(size));\n          } else if (name === 'play') {\n            addClass(item, \"\".concat(NAMESPACE, \"-large\"));\n          }\n          if (isFunction(click)) {\n            addListener(item, EVENT_CLICK, click);\n          }\n          list.appendChild(item);\n        });\n        toolbar.appendChild(list);\n      } else {\n        addClass(toolbar, CLASS_HIDE);\n      }\n      if (!options.rotatable) {\n        var rotates = toolbar.querySelectorAll('li[class*=\"rotate\"]');\n        addClass(rotates, CLASS_INVISIBLE);\n        forEach(rotates, function (rotate) {\n          toolbar.appendChild(rotate);\n        });\n      }\n      if (options.inline) {\n        addClass(button, CLASS_FULLSCREEN);\n        setStyle(viewer, {\n          zIndex: options.zIndexInline\n        });\n        if (window.getComputedStyle(parent).position === 'static') {\n          setStyle(parent, {\n            position: 'relative'\n          });\n        }\n        parent.insertBefore(viewer, element.nextSibling);\n      } else {\n        addClass(button, CLASS_CLOSE);\n        addClass(viewer, CLASS_FIXED);\n        addClass(viewer, CLASS_FADE);\n        addClass(viewer, CLASS_HIDE);\n        setStyle(viewer, {\n          zIndex: options.zIndex\n        });\n        var container = options.container;\n        if (isString(container)) {\n          container = element.ownerDocument.querySelector(container);\n        }\n        if (!container) {\n          container = this.body;\n        }\n        container.appendChild(viewer);\n      }\n      if (options.inline) {\n        this.render();\n        this.bind();\n        this.isShown = true;\n      }\n      this.ready = true;\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_READY) === false) {\n        this.ready = false;\n        return;\n      }\n      if (this.ready && options.inline) {\n        this.view(this.index);\n      }\n    }\n\n    /**\n     * Get the no conflict viewer class.\n     * @returns {Viewer} The viewer class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Viewer = AnotherViewer;\n      return Viewer;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n  return Viewer;\n}();\nassign(Viewer.prototype, render, events, handlers, methods, others);\n\nexport { Viewer as default };\n", "<template>\r\n  <div class=\"view-larger-image\">\r\n    <div class=\"view-larger-image__left\">\r\n      <div class=\"view-larger-image-left__header\">\r\n        <!--        <div class=\"view-larger-image-header__left\">-->\r\n        <!--          <img src=\"https://static.soyoung.com/sy-pre/3re6h6fgwsmoq-1697807400719.png\" alt=\"\" @click=\"handleClickClose\">-->\r\n        <!--        </div>-->\r\n        <div class=\"view-larger-image-header__right\">\r\n          <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/1rvzbhc4prwgs-1697808769379.png\" alt=\"\" @click=\"handleClickZoom(1)\" />\r\n          <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/3g0qyyheb6e5d-1697808769379.png\" alt=\"\" @click=\"handleClickZoom(0.5)\" />\r\n          <!--          <img src=\"https://static.soyoung.com/sy-pre/1nubvuocuun6k-1697808769379.png\" alt=\"\">-->\r\n          <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697808769379.png\" alt=\"\" @click=\"handleClickDownload\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"view-large-image-left__body\">\r\n        <div class=\"view-large-image-left-body__cover\">\r\n          <img loading=\"lazy\" id=\"imgTooles\" :data-original=\"`http://*************:7777${info?.url}`\" :src=\"`http://*************:7777${info?.url}`\" object-fit=\"contain\" alt=\"\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"view-larger-image__right\">\r\n      <div class=\"view-larger-image-right__content\">\r\n        <div class=\"view-larger-image-right-content__title\">\r\n          <span>基础信息</span>\r\n        </div>\r\n        <div class=\"view-larger-image-right-content__item\">\r\n          <label>文件大小</label>\r\n          <span>{{ (info?.size / 1000).toFixed(2) }}mb</span>\r\n        </div>\r\n        <div class=\"view-larger-image-right-content__item\">\r\n          <label>尺寸</label>\r\n          <span>{{ info?.width }} * {{ info?.height }}</span>\r\n        </div>\r\n        <div class=\"view-larger-image-right-content__item\">\r\n          <label>文件格式</label>\r\n          <span>{{ info?.url?.split(\".\")[1] }}</span>\r\n        </div>\r\n        <div class=\"view-larger-image-right-content__item\">\r\n          <label>添加日期</label>\r\n          <span>{{ new Date(parseInt(info?.createTime)).toLocaleString().replace(/:\\d{1,2}$/, \" \") }}</span>\r\n        </div>\r\n        <div class=\"view-larger-image-right-content__item origin__path\">\r\n          <label>来源路径</label>\r\n          <div class=\"origin-path__content\">\r\n            <div class=\"origin-path__item\">\r\n              <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/pic_1684138594288-1697631000719.png\" alt=\"\" />\r\n              <span>{{ info?.appName }} V{{ info.appVersion }}</span>\r\n            </div>\r\n            <div class=\"origin-path__item\">\r\n              <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png\" alt=\"\" />\r\n              <span>{{ info?.appTags }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"view-larger-image-right__content\">\r\n            <div class=\"view-larger-image-right-content__title\">\r\n              <!--              <span style=\"display:inline-block; margin-bottom: 10px;\">灵感升级</span>-->\r\n              <div>\r\n                <el-button type=\"primary\" @click=\"handleClickAI\">AI灵感升级</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { aiGenerated, moduleList } from \"@/api/upload\";\r\nimport { appsList } from \"@/api/common\";\r\nimport { ElMessageBox, ElLoading } from \"element-plus\";\r\nimport { onMounted, ref } from \"vue\";\r\nimport { useRouter, useRoute } from \"vue-router\";\r\nimport Viewer from \"viewerjs\";\r\nimport \"viewerjs/dist/viewer.css\";\r\nimport { versionList } from \"@/api/common\";\r\n\r\ntype InfoTypeData = {\r\n  url: string;\r\n  size: number;\r\n  width: number;\r\n  height: number;\r\n  createTime: string;\r\n  appName: string;\r\n  appVersion: string;\r\n  appTags: string[];\r\n};\r\n\r\nconst route = useRoute();\r\n\r\nlet info = ref<InfoTypeData>({\r\n  url: \"\",\r\n  size: 0,\r\n  width: 0,\r\n  height: 0,\r\n  createTime: \"\",\r\n  appName: \"\",\r\n  appTags: [],\r\n  appVersion: \"\"\r\n});\r\nlet viewer = ref<any>({});\r\nconst router = useRouter();\r\n\r\nconst handleClickAI = async () => {\r\n  ElMessageBox.confirm(\"确认以此图喂给AI生成新图?时间有点长耐心等待啊\", \"提示\", {\r\n    confirmButtonText: \"确定\",\r\n    cancelButtonText: \"取消\",\r\n    type: \"warning\"\r\n  })\r\n    .then(async () => {\r\n      const loading = ElLoading.service({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\"\r\n      });\r\n\r\n      const res = await aiGenerated({\r\n        name: info.value.appTags || \"首页\"\r\n      });\r\n\r\n      if (res?.data?.data) {\r\n        loading.close();\r\n        window.open(res?.data?.data);\r\n      }\r\n    })\r\n    .catch(() => {\r\n      // window.open(res?.data?.data);\r\n    });\r\n};\r\n\r\nconst appData = ref<any>([]);\r\nconst getAppData = async () => {\r\n  const data = await appsList({});\r\n  console.log(data, \"数据\");\r\n  if (data.data.code === 0) {\r\n    appData.value = data.data.data;\r\n  }\r\n};\r\n\r\nconst tagsData = ref<any>([]); // TAGS Data\r\nconst getModuleList = async () => {\r\n  const data = await moduleList({});\r\n  if (data.data.code === 0) {\r\n    tagsData.value = data.data.data;\r\n  }\r\n};\r\nconst versionData = ref<any>([]); // TAGS Data\r\nconst getVersionData = async () => {\r\n  const data = await versionList({});\r\n  if (data.data.code === 0) {\r\n    versionData.value = data.data.data;\r\n  }\r\n};\r\n\r\nconst filterName = (type: string, id: string) => {\r\n  console.log(type, id, \"数据\");\r\n  if (type === \"app\") {\r\n    return appData.value.filter((e: any) => e._id === id)[0].name;\r\n  } else if (type === \"version\") {\r\n    return versionData.value.filter((e: any) => e._id === id)[0].name;\r\n  } else {\r\n    const data = tagsData.value.filter((e: any) => e._id === id);\r\n    console.log(data, \"标签\");\r\n    return data.map((e: any) => e.name).join(\",\");\r\n  }\r\n};\r\n\r\nonMounted(async () => {\r\n  await getAppData();\r\n  await getModuleList();\r\n  await getVersionData();\r\n  const { query } = route;\r\n  info.value = {\r\n    ...query,\r\n    appName: filterName(\"app\", <string>query.appId),\r\n    appVersion: filterName(\"version\", <string>query.version),\r\n    appTags: filterName(\"tags\", <string>query.tags)\r\n  } as any;\r\n\r\n  const ViewerDom = document.getElementById(\"imgTooles\");\r\n  //@ts-ignore\r\n  viewer.value = new Viewer(ViewerDom, {\r\n    url: \"data-original\",\r\n    show: function () {\r\n      //@ts-ignore\r\n      viewer.update();\r\n    }\r\n  });\r\n});\r\n\r\nconst handleClickClose = () => {\r\n  router.go(-1);\r\n};\r\n\r\nconst handleClickDownload = () => {\r\n  let url = \"http://*************:7777\";\r\n\r\n  window.open((url += info.value.url));\r\n};\r\n\r\nconst handleClickZoom = (zoom) => {\r\n  viewer.value.zoomTo(parseInt(zoom));\r\n  viewer.value.show();\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.view-larger-image {\r\n  width: 100%;\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: row;\r\n  background: #1b1a24;\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n\r\n  .view-larger-image__left {\r\n    flex: 1;\r\n\r\n    .view-larger-image-left__header {\r\n      width: 100%;\r\n      height: 64px;\r\n      background: #26252e;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n\r\n      .view-larger-image-header__left {\r\n        width: 28px;\r\n        height: 28px;\r\n        margin-left: 22px;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n\r\n      .view-larger-image-header__right {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n\r\n        img {\r\n          width: 22px;\r\n          height: 22px;\r\n          margin-right: 20px;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n\r\n    .view-large-image-left__body {\r\n      width: 100%;\r\n      height: calc(100% - 64px);\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .view-large-image-left-body__cover {\r\n        width: 100%;\r\n        height: 100%;\r\n        max-width: 300px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        overflow-x: hidden;\r\n        overflow-y: auto;\r\n\r\n        img {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .view-larger-image__right {\r\n    width: 300px;\r\n    border-left: 1px solid #ffffff;\r\n    padding: 27px 30px;\r\n    box-sizing: border-box;\r\n\r\n    .view-larger-image-right__content {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .view-larger-image-right-content__title {\r\n        width: 100%;\r\n        height: 37px;\r\n\r\n        span {\r\n          font-family: PingFangSC-Medium;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          letter-spacing: 0;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      .view-larger-image-right-content__item {\r\n        width: 100%;\r\n        margin-bottom: 15px;\r\n\r\n        label,\r\n        span {\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          font-weight: 400;\r\n        }\r\n\r\n        label {\r\n          width: 101px;\r\n          text-align: left;\r\n          display: inline-block;\r\n        }\r\n\r\n        &.origin__path {\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          .origin-path__content {\r\n            width: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            margin-top: 16px;\r\n\r\n            .origin-path__item {\r\n              padding: 5px 10px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: flex-start;\r\n              margin-bottom: 15px;\r\n              background: rgba(255, 255, 255, 0.2);\r\n              border-radius: 14px;\r\n              font-size: 14px;\r\n\r\n              img {\r\n                width: 18px;\r\n                height: 18px;\r\n                border-radius: 4.5px;\r\n                margin-right: 10px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "<template>\n  <div\n    :class=\"{\n      home: true,\n      'home-theme': store.themeShow\n    }\"\n  >\n    <sp-header :headBgColor=\"store.themeShow ? '#26282B' : '#FFFFFF'\">\n      <sp-login></sp-login>\n    </sp-header>\n\n    <div class=\"home__line\" :style=\"{ background: store.themeShow ? '#000000' : '#F5F5F5' }\"></div>\n\n    <div class=\"home-body\">\n      <!--      &lt;!&ndash; 左侧导航部分内容 start &ndash;&gt;-->\n      <!--      <div class=\"home-body__left\">-->\n      <!--        &lt;!&ndash; 灵感集&设计协作tabs start &ndash;&gt;-->\n      <!--        <sp-tabs></sp-tabs>-->\n      <!--        &lt;!&ndash; 灵感集&设计协作tabs end &ndash;&gt;-->\n\n      <!--        &lt;!&ndash; 灵感集导航 start &ndash;&gt;-->\n      <!--        <sp-nav @tabs-change=\"tabsChange\" :data=\"routerListData\"></sp-nav>-->\n      <!--        &lt;!&ndash; 灵感集导航 end &ndash;&gt;-->\n      <!--      </div>-->\n      <!-- 左侧导航部分内容 end -->\n\n      <!-- 右侧内容 start -->\n      <div class=\"home-body__right\">\n        <!-- 图片放大 start -->\n        <view-larger-image></view-larger-image>\n        <!-- 图片放大 end -->\n      </div>\n      <!-- 右侧内容 start -->\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport SpHeader from \"@/views/layouts/components/spHeader.vue\";\nimport SpLogin from \"@/views/layouts/components/spLogin.vue\";\nimport SpNav from \"@/views/layouts/components/spNav.vue\";\nimport SpTabs from \"@/views/layouts/components/tabs.vue\";\nimport { themeStore, materialStore } from \"@/store\";\nimport HomeBodyHeader from \"@/views/layouts/home/<USER>/homeBodyHeader.vue\";\nimport HomeBodyContainer from \"@/views/layouts/home/<USER>/homeBodyContainer.vue\";\nimport ViewLargerImage from \"@/views/layouts/viewImage/components/viewLargerImage.vue\";\nimport { appsList } from \"@/api/common\";\nimport { onMounted } from \"vue\";\n\nconst store = themeStore(); // 黑白主题切换Store\nconst material = materialStore(); // 素材Store\n\nconst appOrDesign = ref<number>(1); // 1: 'app' or 'it'\n\nconst routerListData = ref<any>([{ _id: 1, name: \"全部应用\" }]);\nconst tabsChange = (item: any) => {\n  console.log(item, \"Item\");\n  appOrDesign.value = +item._id;\n  material.updateMaterialInfo(item);\n};\n\nconst initData = async () => {\n  const data = await appsList({});\n  console.log(data, \"应用列表\");\n  if (data.data.code === 0) {\n    routerListData.value = [...routerListData.value, ...data.data.data];\n  }\n};\n\nonMounted(() => {\n  initData();\n});\n</script>\n<style lang=\"less\">\n.home {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  //height: calc(100vh - 1px);\n  &::-webkit-scrollbar {\n    display: none;\n  }\n\n  .home__line {\n    width: 100%;\n    height: 1px;\n    background: #f5f5f5;\n  }\n\n  .home-body {\n    width: 100%;\n    display: flex;\n    flex-direction: row;\n\n    .home-body__left {\n      width: 250px;\n      display: flex;\n      flex-direction: column;\n    }\n\n    .home-body__right {\n      width: 100%;\n      position: relative;\n      height: calc(100vh - 64px);\n      overflow: hidden;\n      display: flex;\n      flex-direction: column;\n    }\n  }\n\n  &.home-theme {\n    .home-body {\n      .home-body__right {\n        .home-body-right__header {\n          background: #26282b;\n\n          .swiper-list-tool__bg {\n            background: #000000;\n            opacity: 0.4;\n          }\n\n          .previous-icon {\n            color: #5c54f0;\n            z-index: 1;\n          }\n\n          .home-body-right-header__more {\n            .more-list__item {\n              label {\n                color: #ffffff;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_typeof", "Symbol", "iterator", "constructor", "prototype", "_defineProperties", "target", "props", "i", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "obj", "value", "arg", "input", "hint", "prim", "toPrimitive", "res", "call", "TypeError", "String", "Number", "_toPrimitive", "DEFAULTS", "backdrop", "button", "navbar", "title", "toolbar", "className", "container", "fullscreen", "inheritedAttributes", "initialCoverage", "initialViewIndex", "inline", "interval", "keyboard", "focus", "loading", "loop", "min<PERSON><PERSON><PERSON>", "minHeight", "movable", "rotatable", "scalable", "zoomable", "zoomOnTouch", "zoomOnWheel", "slideOnTouch", "toggleOnDblclick", "tooltip", "transition", "zIndex", "zIndexInline", "zoomRatio", "minZoomRatio", "maxZoomRatio", "url", "ready", "show", "shown", "hide", "hidden", "view", "viewed", "move", "moved", "rotate", "rotated", "scale", "scaled", "zoom", "zoomed", "play", "stop", "IS_BROWSER", "window", "document", "WINDOW", "IS_TOUCH_DEVICE", "documentElement", "HAS_POINTER_EVENT", "NAMESPACE", "ACTION_MOVE", "ACTION_SWITCH", "ACTION_ZOOM", "CLASS_ACTIVE", "concat", "CLASS_CLOSE", "CLASS_FADE", "CLASS_FIXED", "CLASS_FULLSCREEN", "CLASS_FULLSCREEN_EXIT", "CLASS_HIDE", "CLASS_HIDE_MD_DOWN", "CLASS_HIDE_SM_DOWN", "CLASS_HIDE_XS_DOWN", "CLASS_IN", "CLASS_INVISIBLE", "CLASS_LOADING", "CLASS_MOVE", "CLASS_OPEN", "CLASS_SHOW", "CLASS_TRANSITION", "EVENT_CLICK", "EVENT_DBLCLICK", "EVENT_DRAG_START", "EVENT_FOCUSIN", "EVENT_KEY_DOWN", "EVENT_LOAD", "EVENT_ERROR", "EVENT_POINTER_DOWN", "EVENT_POINTER_MOVE", "EVENT_POINTER_UP", "EVENT_RESIZE", "EVENT_TRANSITION_END", "EVENT_WHEEL", "EVENT_READY", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_VIEW", "EVENT_VIEWED", "EVENT_MOVE", "EVENT_MOVED", "EVENT_ROTATE", "EVENT_ROTATED", "EVENT_SCALE", "EVENT_SCALED", "EVENT_ZOOM", "EVENT_ZOOMED", "EVENT_PLAY", "EVENT_STOP", "DATA_ACTION", "REGEXP_SPACES", "BUTTONS", "isString", "isNaN", "isNumber", "isUndefined", "isObject", "hasOwnProperty", "isPlainObject", "_constructor", "error", "isFunction", "data", "callback", "Array", "isArray", "assign", "_len", "args", "_key", "REGEXP_SUFFIX", "setStyle", "element", "styles", "style", "property", "test", "hasClass", "classList", "contains", "indexOf", "addClass", "elem", "add", "trim", "removeClass", "remove", "replace", "toggleClass", "added", "REGEXP_HYPHENATE", "hyphenate", "toLowerCase", "getData", "name", "dataset", "getAttribute", "setData", "setAttribute", "onceSupported", "supported", "once", "listener", "options", "get", "set", "addEventListener", "removeEventListener", "removeListener", "type", "handler", "split", "event", "listeners", "addListener", "_handler", "_element$listeners", "_len2", "_key2", "dispatchEvent", "Event", "CustomEvent", "bubbles", "cancelable", "detail", "createEvent", "initCustomEvent", "getTransforms", "_ref", "scaleX", "scaleY", "translateX", "translateY", "values", "transform", "join", "WebkitTransform", "msTransform", "IS_SAFARI", "navigator", "userAgent", "getImageNaturalSizes", "image", "newImage", "createElement", "naturalWidth", "naturalHeight", "body", "onload", "width", "height", "<PERSON><PERSON><PERSON><PERSON>", "src", "cssText", "append<PERSON><PERSON><PERSON>", "getResponsiveClass", "getPointer", "_ref2", "endOnly", "pageX", "pageY", "end", "endX", "endY", "timeStamp", "Date", "now", "startX", "startY", "id", "render", "this", "initContainer", "initViewer", "initList", "<PERSON><PERSON><PERSON><PERSON>", "initBody", "ownerDocument", "scrollbarWidth", "innerWidth", "clientWidth", "initialBodyPaddingRight", "paddingRight", "initialBodyComputedPaddingRight", "getComputedStyle", "containerData", "innerHeight", "viewerData", "parent", "Math", "max", "offsetWidth", "offsetHeight", "parentData", "fulled", "viewer", "_this", "list", "items", "innerHTML", "images", "index", "alt", "decodeURIComponent", "getImageNameFromURL", "getImageURL", "item", "img", "onLoad", "onError", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadImage", "renderList", "next", "nextElement<PERSON><PERSON>ling", "gutter", "parseInt", "marginLeft", "outerWidth", "resetList", "initImage", "done", "sizingImage", "_this2", "footerHeight", "footer", "viewerWidth", "viewerHeight", "oldImageData", "imageData", "imageInitializing", "abort", "aspectRatio", "min", "left", "top", "x", "y", "oldRatio", "ratio", "initialImageData", "renderImage", "_this3", "marginTop", "viewing", "moving", "rotating", "scaling", "zooming", "onTransitionEnd", "imageRendering", "resetImage", "parentNode", "events", "bind", "canvas", "onClick", "click", "onDragStart", "dragstart", "onPointerDown", "pointerdown", "onPointerMove", "pointermove", "onPointerUp", "pointerup", "onKeyDown", "keydown", "onResize", "resize", "onWheel", "wheel", "passive", "capture", "onDblclick", "dblclick", "unbind", "handlers", "action", "localName", "parentElement", "isTrusted", "clearTimeout", "clickCanvasTimeout", "played", "exit", "full", "pointerMoved", "toggle", "reset", "prev", "preventDefault", "doubleClickImageTimeout", "originalEvent", "load", "timeout", "originalImage", "parentWidth", "parentHeight", "filled", "keyCode", "which", "charCode", "playing", "ctrl<PERSON>ey", "pointers", "buttons", "showing", "hiding", "pointerType", "changedTouches", "touch", "identifier", "pointerId", "isSwitchable", "change", "pointer", "imageClicked", "setTimeout", "isShown", "close", "open", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "player", "getElementsByTagName", "_this4", "wheeling", "delta", "deltaY", "wheelDelta", "methods", "immediate", "build", "transitioning", "removeAttribute", "initialOffsetWidth", "hideImmediately", "onViewerTransitionEnd", "onImageTransitionEnd", "zoomTo", "querySelector", "activeItem", "onViewed", "complete", "maxIndex", "moveTo", "_originalEvent", "oldX", "oldY", "changed", "degree", "rotateTo", "oldDegree", "_scaleX", "_scaleY", "_this5", "oldScaleX", "oldScaleY", "showTooltip", "pivot", "_this6", "_zoomable", "newWidth", "newHeight", "offset", "box", "getBoundingClientRect", "pageXOffset", "clientLeft", "pageYOffset", "clientTop", "getOffset", "center", "count", "_ref3", "getPointersCenter", "_this7", "total", "onLoadWhenPlay", "requestFullscreen", "referrerPolicy", "_this8", "exitFullscreen", "_this9", "enforceFocus", "_this10", "clearEnforceFocus", "_this11", "tooltipBox", "textContent", "round", "tooltipping", "fading", "update", "_this12", "isImg", "destroy", "querySelectorAll", "changedIndexes", "changedIndex", "destroyed", "delaying", "initializing", "onStart", "others", "onFocusin", "parseFloat", "fucus", "webkitRequestFullscreen", "Element", "ALLOW_KEYBOARD_INPUT", "mozRequestFullScreen", "msRequestFullscreen", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "offsetX", "offsetY", "pointers2", "ratios", "pointer2", "x1", "abs", "y1", "x2", "y2", "z1", "sqrt", "sort", "a", "b", "getMaxZoomRatio", "absoluteOffsetX", "p", "<PERSON><PERSON><PERSON><PERSON>", "Viewer", "getUniqueID", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "nodeType", "Error", "init", "protoProps", "staticProps", "progress", "template", "custom", "zoomButtons", "slice", "rotateButtons", "scaleButtons", "deep", "size", "rotates", "position", "insertBefore", "nextS<PERSON>ling", "route", "useRoute", "info", "ref", "createTime", "appName", "appTags", "appVersion", "useRouter", "handleClickAI", "async", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "ElLoading", "service", "lock", "text", "background", "aiGenerated", "catch", "appData", "tagsData", "versionData", "filterName", "console", "log", "_id", "map", "onMounted", "appsList", "code", "getAppData", "moduleList", "getModuleList", "versionList", "getVersionData", "query", "appId", "version", "tags", "ViewerDom", "getElementById", "handleClickDownload", "handleClickZoom", "store", "themeStore", "materialStore", "routerListData"], "mappings": ";;;;;;;;;GAUA,SAASA,EAAQC,EAAGC,GACd,IAAAC,EAAIC,OAAOC,KAAKJ,GACpB,GAAIG,OAAOE,sBAAuB,CAC5B,IAAAC,EAAIH,OAAOE,sBAAsBL,GACrCC,IAAMK,EAAIA,EAAEC,QAAO,SAAUN,GAC3B,OAAOE,OAAOK,yBAAyBR,EAAGC,GAAGQ,UACnD,KAASP,EAAEQ,KAAKC,MAAMT,EAAGI,EACtB,CACM,OAAAJ,CACT,CACA,SAASU,EAAeZ,GACtB,IAAA,IAASC,EAAI,EAAGA,EAAIY,UAAUC,OAAQb,IAAK,CACrC,IAAAC,EAAI,MAAQW,UAAUZ,GAAKY,UAAUZ,GAAK,GAC1CA,EAAA,EAAIF,EAAQI,OAAOD,IAAI,GAAIa,SAAQ,SAAUd,GAC/Ce,EAAgBhB,EAAGC,EAAGC,EAAED,GAC9B,IAASE,OAAOc,0BAA4Bd,OAAOe,iBAAiBlB,EAAGG,OAAOc,0BAA0Bf,IAAMH,EAAQI,OAAOD,IAAIa,SAAQ,SAAUd,GAC7IE,OAAOgB,eAAenB,EAAGC,EAAGE,OAAOK,yBAAyBN,EAAGD,GACrE,GACG,CACM,OAAAD,CACT,CACA,SAASoB,EAAQd,GAGR,OAAAc,EAAU,mBAAqBC,QAAU,iBAAmBA,OAAOC,SAAW,SAAUhB,GAC7F,cAAcA,CACf,EAAG,SAAUA,GACLA,OAAAA,GAAK,mBAAqBe,QAAUf,EAAEiB,cAAgBF,QAAUf,IAAMe,OAAOG,UAAY,gBAAkBlB,CACtH,GAAaA,EACb,CAMA,SAASmB,EAAkBC,EAAQC,GACjC,IAAA,IAASC,EAAI,EAAGA,EAAID,EAAMb,OAAQc,IAAK,CACjC,IAAAC,EAAaF,EAAMC,GACZC,EAAApB,WAAaoB,EAAWpB,aAAc,EACjDoB,EAAWC,cAAe,EACtB,UAAWD,IAAYA,EAAWE,UAAW,GACjD5B,OAAOgB,eAAeO,EAAQM,EAAeH,EAAWI,KAAMJ,EAC/D,CACH,CASA,SAASb,EAAgBkB,EAAKD,EAAKE,GAY1B,OAXPF,EAAMD,EAAeC,MACVC,EACF/B,OAAAgB,eAAee,EAAKD,EAAK,CAC9BE,QACA1B,YAAY,EACZqB,cAAc,EACdC,UAAU,IAGZG,EAAID,GAAOE,EAEND,CACT,CAWA,SAASF,EAAeI,GAClB,IAAAH,EAXN,SAAsBI,EAAOC,GACvB,GAAiB,iBAAVD,GAAgC,OAAVA,EAAuB,OAAAA,EACpD,IAAAE,EAAOF,EAAMhB,OAAOmB,aACxB,QAAa,IAATD,EAAoB,CACtB,IAAIE,EAAMF,EAAKG,KAAKL,EAAOC,GAAQ,WACnC,GAAmB,iBAARG,EAAyB,OAAAA,EAC9B,MAAA,IAAIE,UAAU,+CACrB,CACD,OAAiB,WAATL,EAAoBM,OAASC,QAAQR,EAC/C,CAEYS,CAAaV,EAAK,UAC5B,MAAsB,iBAARH,EAAmBA,EAAMW,OAAOX,EAChD,CAEA,IAAIc,EAAW,CAMbC,UAAU,EAKVC,QAAQ,EAKRC,QAAQ,EAKRC,OAAO,EAKPC,SAAS,EAKTC,UAAW,GAKXC,UAAW,OAKX/C,OAAQ,KAMRgD,YAAY,EAKZC,oBAAqB,CAAC,cAAe,WAAY,QAAS,UAAW,iBAAkB,QAAS,SAAU,UAK1GC,gBAAiB,GAKjBC,iBAAkB,EAKlBC,QAAQ,EAKRC,SAAU,IAKVC,UAAU,EAKVC,OAAO,EAKPC,SAAS,EAKTC,MAAM,EAKNC,SAAU,IAKVC,UAAW,IAKXC,SAAS,EAKTC,WAAW,EAKXC,UAAU,EAKVC,UAAU,EAKVC,aAAa,EAKbC,aAAa,EAKbC,cAAc,EAMdC,kBAAkB,EAKlBC,SAAS,EAKTC,YAAY,EAKZC,OAAQ,KAKRC,aAAc,EAKdC,UAAW,GAKXC,aAAc,IAKdC,aAAc,IAKdC,IAAK,MAKLC,MAAO,KACPC,KAAM,KACNC,MAAO,KACPC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,MAAO,KACPC,OAAQ,KACRC,QAAS,KACTC,MAAO,KACPC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,KAAM,MAKJC,EAA+B,oBAAXC,aAAqD,IAApBA,OAAOC,SAC5DC,EAASH,EAAaC,OAAS,GAC/BG,KAAkBJ,IAAcG,EAAOD,SAASG,kBAAkB,iBAAkBF,EAAOD,SAASG,gBACpGC,IAAoBN,GAAa,iBAAkBG,EACnDI,EAAY,SAGZC,EAAc,OACdC,EAAgB,SAChBC,EAAc,OAGdC,EAAe,GAAGC,OAAOL,EAAW,WACpCM,EAAc,GAAGD,OAAOL,EAAW,UACnCO,EAAa,GAAGF,OAAOL,EAAW,SAClCQ,EAAc,GAAGH,OAAOL,EAAW,UACnCS,EAAmB,GAAGJ,OAAOL,EAAW,eACxCU,EAAwB,GAAGL,OAAOL,EAAW,oBAC7CW,EAAa,GAAGN,OAAOL,EAAW,SAClCY,EAAqB,GAAGP,OAAOL,EAAW,iBAC1Ca,EAAqB,GAAGR,OAAOL,EAAW,iBAC1Cc,EAAqB,GAAGT,OAAOL,EAAW,iBAC1Ce,EAAW,GAAGV,OAAOL,EAAW,OAChCgB,EAAkB,GAAGX,OAAOL,EAAW,cACvCiB,EAAgB,GAAGZ,OAAOL,EAAW,YACrCkB,GAAa,GAAGb,OAAOL,EAAW,SAClCmB,GAAa,GAAGd,OAAOL,EAAW,SAClCoB,GAAa,GAAGf,OAAOL,EAAW,SAClCqB,GAAmB,GAAGhB,OAAOL,EAAW,eAGxCsB,GAAc,QACdC,GAAiB,WACjBC,GAAmB,YACnBC,GAAgB,UAChBC,GAAiB,UACjBC,GAAa,OACbC,GAAc,QAIdC,GAAqB9B,EAAoB,cADrBF,EAAkB,aAAe,YAErDiC,GAAqB/B,EAAoB,cAHtBF,EAAkB,YAAc,YAInDkC,GAAmBhC,EAAoB,0BALrBF,EAAkB,uBAAyB,UAM7DmC,GAAe,SACfC,GAAuB,gBACvBC,GAAc,QAGdC,GAAc,QACdC,GAAa,OACbC,GAAc,QACdC,GAAa,OACbC,GAAe,SACfC,GAAa,OACbC,GAAe,SACfC,GAAa,OACbC,GAAc,QACdC,GAAe,SACfC,GAAgB,UAChBC,GAAc,QACdC,GAAe,SACfC,GAAa,OACbC,GAAe,SACfC,GAAa,OACbC,GAAa,OAGbC,GAAc,GAAG/C,OAAOL,EAAW,UAGnCqD,GAAgB,QAGhBC,GAAU,CAAC,UAAW,WAAY,aAAc,QAAS,OAAQ,OAAQ,OAAQ,cAAe,eAAgB,kBAAmB,iBAOvI,SAASC,GAAS/H,GAChB,MAAwB,iBAAVA,CAChB,CAKA,IAAIgI,GAAQtH,OAAOsH,OAAS5D,EAAO4D,MAOnC,SAASC,GAASjI,GAChB,MAAwB,iBAAVA,IAAuBgI,GAAMhI,EAC7C,CAOA,SAASkI,GAAYlI,GACnB,YAAwB,IAAVA,CAChB,CAOA,SAASmI,GAASnI,GAChB,MAA0B,WAAnBf,EAAQe,IAAiC,OAAVA,CACxC,CACA,IAAIoI,GAAiBpK,OAAOqB,UAAU+I,eAOtC,SAASC,GAAcrI,GACjB,IAACmI,GAASnI,GACL,OAAA,EAEL,IACF,IAAIsI,EAAetI,EAAMZ,YACrBC,EAAYiJ,EAAajJ,UAC7B,OAAOiJ,GAAgBjJ,GAAa+I,GAAe7H,KAAKlB,EAAW,gBACpE,OAAQkJ,GACA,OAAA,CACR,CACH,CAOA,SAASC,GAAWxI,GAClB,MAAwB,mBAAVA,CAChB,CAQA,SAASpB,GAAQ6J,EAAMC,GACjB,GAAAD,GAAQD,GAAWE,GACrB,GAAIC,MAAMC,QAAQH,IAASR,GAASQ,EAAK9J,QAA0B,CACjE,IACIc,EADAd,EAAS8J,EAAK9J,OAElB,IAAKc,EAAI,EAAGA,EAAId,IACgC,IAA1C+J,EAASnI,KAAKkI,EAAMA,EAAKhJ,GAAIA,EAAGgJ,GADdhJ,GAAK,GAKnC,MAAe0I,GAASM,IAClBzK,OAAOC,KAAKwK,GAAM7J,SAAQ,SAAUkB,GAClC4I,EAASnI,KAAKkI,EAAMA,EAAK3I,GAAMA,EAAK2I,EAC5C,IAGS,OAAAA,CACT,CAQA,IAAII,GAAS7K,OAAO6K,QAAU,SAAgB9I,GAC5C,IAAA,IAAS+I,EAAOpK,UAAUC,OAAQoK,EAAO,IAAIJ,MAAMG,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IAClGD,EAAKC,EAAO,GAAKtK,UAAUsK,GAWtB,OATHb,GAASpI,IAAQgJ,EAAKpK,OAAS,GAC5BoK,EAAAnK,SAAQ,SAAUqB,GACjBkI,GAASlI,IACXjC,OAAOC,KAAKgC,GAAKrB,SAAQ,SAAUkB,GAC7BC,EAAAD,GAAOG,EAAIH,EACzB,GAEA,IAESC,CACT,EACIkJ,GAAgB,mDAOpB,SAASC,GAASC,EAASC,GACzB,IAAIC,EAAQF,EAAQE,MACZzK,GAAAwK,GAAQ,SAAUpJ,EAAOsJ,GAC3BL,GAAcM,KAAKD,IAAarB,GAASjI,KAClCA,GAAA,MAEXqJ,EAAMC,GAAYtJ,CACtB,GACA,CAiBA,SAASwJ,GAASL,EAASnJ,GACrB,SAACmJ,IAAYnJ,KAGVmJ,EAAQM,UAAYN,EAAQM,UAAUC,SAAS1J,GAASmJ,EAAQjI,UAAUyI,QAAQ3J,IAAS,EACpG,CAOA,SAAS4J,GAAST,EAASnJ,GACrB,GAACmJ,GAAYnJ,EAGb,GAAAiI,GAASkB,EAAQxK,QACXC,GAAAuK,GAAS,SAAUU,GACzBD,GAASC,EAAM7J,EACrB,SAGE,GAAImJ,EAAQM,UACFN,EAAAM,UAAUK,IAAI9J,OADxB,CAII,IAAAkB,EAAYiI,EAAQjI,UAAU6I,OAC7B7I,EAEMA,EAAUyI,QAAQ3J,GAAS,IACpCmJ,EAAQjI,UAAY,GAAG2D,OAAO3D,EAAW,KAAK2D,OAAO7E,IAFrDmJ,EAAQjI,UAAYlB,CAHrB,CAOH,CAOA,SAASgK,GAAYb,EAASnJ,GACvBmJ,GAAYnJ,IAGbiI,GAASkB,EAAQxK,QACXC,GAAAuK,GAAS,SAAUU,GACzBG,GAAYH,EAAM7J,EACxB,IAGMmJ,EAAQM,UACFN,EAAAM,UAAUQ,OAAOjK,GAGvBmJ,EAAQjI,UAAUyI,QAAQ3J,IAAU,IACtCmJ,EAAQjI,UAAYiI,EAAQjI,UAAUgJ,QAAQlK,EAAO,KAEzD,CAQA,SAASmK,GAAYhB,EAASnJ,EAAOoK,GAC9BpK,IAGDiI,GAASkB,EAAQxK,QACXC,GAAAuK,GAAS,SAAUU,GACbM,GAAAN,EAAM7J,EAAOoK,EAC/B,IAKMA,EACFR,GAAST,EAASnJ,GAElBgK,GAAYb,EAASnJ,GAEzB,CACA,IAAIqK,GAAmB,oBAOvB,SAASC,GAAUtK,GACjB,OAAOA,EAAMkK,QAAQG,GAAkB,SAASE,aAClD,CAQA,SAASC,GAAQrB,EAASsB,GACxB,OAAItC,GAASgB,EAAQsB,IACZtB,EAAQsB,GAEbtB,EAAQuB,QACHvB,EAAQuB,QAAQD,GAElBtB,EAAQwB,aAAa,QAAQ9F,OAAOyF,GAAUG,IACvD,CAQA,SAASG,GAAQzB,EAASsB,EAAMhC,GAC1BN,GAASM,GACXU,EAAQsB,GAAQhC,EACPU,EAAQuB,QACTvB,EAAAuB,QAAQD,GAAQhC,EAExBU,EAAQ0B,aAAa,QAAQhG,OAAOyF,GAAUG,IAAQhC,EAE1D,CACA,IAAIqC,GAAgB,WAClB,IAAIC,GAAY,EAChB,GAAI9G,EAAY,CACd,IAAI+G,GAAO,EACPC,EAAW,aACXC,EAAUlN,OAAOgB,eAAe,CAAA,EAAI,OAAQ,CAC9CmM,IAAK,WAEI,OADKJ,GAAA,EACLC,CACR,EAMDI,IAAK,SAAapL,GACTgL,EAAAhL,CACR,IAEIoE,EAAAiH,iBAAiB,OAAQJ,EAAUC,GACnC9G,EAAAkH,oBAAoB,OAAQL,EAAUC,EAC9C,CACM,OAAAH,CACT,CAvBoB,GAgCpB,SAASQ,GAAepC,EAASqC,EAAMP,GACjC,IAAAC,EAAUxM,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAC9E+M,EAAUR,EACdO,EAAKzB,OAAO2B,MAAM7D,IAAejJ,SAAQ,SAAU+M,GACjD,IAAKb,GAAe,CAClB,IAAIc,EAAYzC,EAAQyC,UACpBA,GAAaA,EAAUD,IAAUC,EAAUD,GAAOV,KAC1CQ,EAAAG,EAAUD,GAAOV,UACpBW,EAAUD,GAAOV,GACqB,IAAzCjN,OAAOC,KAAK2N,EAAUD,IAAQhN,eACzBiN,EAAUD,GAEmB,IAAlC3N,OAAOC,KAAK2N,GAAWjN,eAClBwK,EAAQyC,UAGpB,CACOzC,EAAAmC,oBAAoBK,EAAOF,EAASP,EAChD,GACA,CASA,SAASW,GAAY1C,EAASqC,EAAMP,GAC9B,IAAAC,EAAUxM,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAC9EoN,EAAWb,EACfO,EAAKzB,OAAO2B,MAAM7D,IAAejJ,SAAQ,SAAU+M,GAC7C,GAAAT,EAAQF,OAASF,GAAe,CAClC,IAAIiB,EAAqB5C,EAAQyC,UAC/BA,OAAmC,IAAvBG,EAAgC,CAAA,EAAKA,EACnDD,EAAW,kBACFF,EAAUD,GAAOV,GAChB9B,EAAAmC,oBAAoBK,EAAOG,EAAUZ,GAC7C,IAAA,IAASc,EAAQtN,UAAUC,OAAQoK,EAAO,IAAIJ,MAAMqD,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/ElD,EAAAkD,GAASvN,UAAUuN,GAEjBhB,EAAAzM,MAAM2K,EAASJ,EAChC,EACW6C,EAAUD,KACHC,EAAAD,GAAS,IAEjBC,EAAUD,GAAOV,IACnB9B,EAAQmC,oBAAoBK,EAAOC,EAAUD,GAAOV,GAAWC,GAEvDU,EAAAD,GAAOV,GAAYa,EAC7B3C,EAAQyC,UAAYA,CACrB,CACOzC,EAAAkC,iBAAiBM,EAAOG,EAAUZ,EAC9C,GACA,CAUA,SAASgB,GAAc/C,EAASqC,EAAM/C,EAAMyC,GACtC,IAAAS,EAaG,OAVHnD,GAAW2D,QAAU3D,GAAW4D,aAC1BT,EAAA,IAAIS,YAAYZ,EAAM/M,EAAe,CAC3C4N,SAAS,EACTC,YAAY,EACZC,OAAQ9D,GACPyC,KAEKS,EAAAxH,SAASqI,YAAY,gBACvBC,gBAAgBjB,GAAM,GAAM,EAAM/C,GAEnCU,EAAQ+C,cAAcP,EAC/B,CAoBA,SAASe,GAAcC,GACrB,IAAIlJ,EAASkJ,EAAKlJ,OAChBmJ,EAASD,EAAKC,OACdC,EAASF,EAAKE,OACdC,EAAaH,EAAKG,WAClBC,EAAaJ,EAAKI,WAChBC,EAAS,GACT/E,GAAS6E,IAA8B,IAAfA,GAC1BE,EAAOzO,KAAK,cAAcsG,OAAOiI,EAAY,QAE3C7E,GAAS8E,IAA8B,IAAfA,GAC1BC,EAAOzO,KAAK,cAAcsG,OAAOkI,EAAY,QAI3C9E,GAASxE,IAAsB,IAAXA,GACtBuJ,EAAOzO,KAAK,UAAUsG,OAAOpB,EAAQ,SAEnCwE,GAAS2E,IAAsB,IAAXA,GACtBI,EAAOzO,KAAK,UAAUsG,OAAO+H,EAAQ,MAEnC3E,GAAS4E,IAAsB,IAAXA,GACtBG,EAAOzO,KAAK,UAAUsG,OAAOgI,EAAQ,MAEvC,IAAII,EAAYD,EAAOrO,OAASqO,EAAOE,KAAK,KAAO,OAC5C,MAAA,CACLC,gBAAiBF,EACjBG,YAAaH,EACbA,YAEJ,CAaA,IAAII,GAAYjJ,EAAOkJ,WAAa,kCAAkC/D,KAAKnF,EAAOkJ,UAAUC,WAS5F,SAASC,GAAqBC,EAAOvC,EAASxC,GACxC,IAAAgF,EAAWvJ,SAASwJ,cAAc,OAGlC,GAAAF,EAAMG,eAAiBP,GAElB,OADE3E,EAAA+E,EAAMG,aAAcH,EAAMI,eAC5BH,EAEL,IAAAI,EAAO3J,SAAS2J,MAAQ3J,SAASG,gBAqB9B,OApBPoJ,EAASK,OAAS,WACPrF,EAAAgF,EAASM,MAAON,EAASO,QAC7BZ,IACHS,EAAKI,YAAYR,EAEvB,EACU9O,GAAAsM,EAAQ7J,qBAAqB,SAAUoJ,GACzC,IAAAzK,EAAQyN,EAAM9C,aAAaF,GACjB,OAAVzK,GACO0N,EAAA7C,aAAaJ,EAAMzK,EAElC,IACE0N,EAASS,IAAMV,EAAMU,IAIhBd,KACHK,EAASrE,MAAM+E,QAAU,uJACzBN,EAAKO,YAAYX,IAEZA,CACT,CAOA,SAASY,GAAmB9C,GAC1B,OAAQA,GACN,KAAK,EACI,OAAAlG,EACT,KAAK,EACI,OAAAD,EACT,KAAK,EACI,OAAAD,EACT,QACS,MAAA,GAEb,CAmCA,SAASmJ,GAAWC,EAAOC,GACzB,IAAIC,EAAQF,EAAME,MAChBC,EAAQH,EAAMG,MACZC,EAAM,CACRC,KAAMH,EACNI,KAAMH,GAED,OAAAF,EAAUG,EAAMnQ,EAAe,CACpCsQ,UAAWC,KAAKC,MAChBC,OAAQR,EACRS,OAAQR,GACPC,EACL,CA0BA,IAi7D4BQ,GAj7DxBC,GAAS,CACXA,OAAQ,WACNC,KAAKC,gBACLD,KAAKE,aACLF,KAAKG,WACLH,KAAKI,cACN,EACDC,SAAU,WACJ,IAAAC,EAAgBN,KAAKnG,QAAQyG,cAC7B9B,EAAO8B,EAAc9B,MAAQ8B,EAActL,gBAC/CgL,KAAKxB,KAAOA,EACZwB,KAAKO,eAAiB3L,OAAO4L,WAAaF,EAActL,gBAAgByL,YACnET,KAAAU,wBAA0BlC,EAAKzE,MAAM4G,aAC1CX,KAAKY,gCAAkChM,OAAOiM,iBAAiBrC,GAAMmC,YACtE,EACDV,cAAe,WACbD,KAAKc,cAAgB,CACnBpC,MAAO9J,OAAO4L,WACd7B,OAAQ/J,OAAOmM,YAElB,EACDb,WAAY,WACV,IAEIc,EAFApF,EAAUoE,KAAKpE,QACjBqF,EAASjB,KAAKiB,OAEZrF,EAAQ1J,SACG8O,EAAA,CACXtC,MAAOwC,KAAKC,IAAIF,EAAOG,YAAaxF,EAAQpJ,UAC5CmM,OAAQuC,KAAKC,IAAIF,EAAOI,aAAczF,EAAQnJ,YAEhDuN,KAAKsB,WAAaN,IAEhBhB,KAAKuB,QAAWP,IAClBA,EAAahB,KAAKc,eAEpBd,KAAKgB,WAAazH,GAAO,CAAE,EAAEyH,EAC9B,EACDZ,aAAc,WACRJ,KAAKpE,QAAQ1J,SAAW8N,KAAKuB,QACtB3H,GAAAoG,KAAKwB,OAAQxB,KAAKgB,WAE9B,EACDb,SAAU,WACR,IAAIsB,EAAQzB,KACRnG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf8F,EAAO1B,KAAK0B,KACVC,EAAQ,GAGZD,EAAKE,UAAY,GACjBtS,GAAQ0Q,KAAK6B,QAAQ,SAAU1D,EAAO2D,GACpC,IAAIjD,EAAMV,EAAMU,IACZkD,EAAM5D,EAAM4D,KA1LtB,SAA6BtO,GAC3B,OAAOgF,GAAShF,GAAOuO,mBAAmBvO,EAAImH,QAAQ,QAAS,IAAIA,QAAQ,WAAY,KAAO,EAChG,CAwL6BqH,CAAoBpD,GACvCpL,EAAMgO,EAAMS,YAAY/D,GAC5B,GAAIU,GAAOpL,EAAK,CACV,IAAA0O,EAAOtN,SAASwJ,cAAc,MAC9B+D,EAAMvN,SAASwJ,cAAc,OACzB/O,GAAAsM,EAAQ7J,qBAAqB,SAAUoJ,GACzC,IAAAzK,EAAQyN,EAAM9C,aAAaF,GACjB,OAAVzK,GACE0R,EAAA7G,aAAaJ,EAAMzK,EAEnC,IACYkL,EAAQnK,SACV2Q,EAAIvD,IAAMA,GAAOpL,GAEnB2O,EAAIL,IAAMA,EACNK,EAAA7G,aAAa,oBAAqB9H,GAAOoL,GACxCsD,EAAA5G,aAAa,aAAcuG,GAC3BK,EAAA5G,aAAa,qBAAsB,QACnC4G,EAAA5G,aAAa,OAAQ,UACtBK,EAAQxJ,UACL+P,EAAA5G,aAAa,WAAY,GAEhC4G,EAAKpD,YAAYqD,GACjBV,EAAK3C,YAAYoD,GACjBR,EAAM1S,KAAKkT,EACZ,CACP,IACInC,KAAK2B,MAAQA,EACLrS,GAAAqS,GAAO,SAAUQ,GACvB,IACIE,EACAC,EAFAnE,EAAQgE,EAAKI,kBAGTjH,GAAA6C,EAAO,UAAU,GACrBvC,EAAQtJ,SACVgI,GAAS6H,EAAMhM,GAEjBoG,GAAY4B,EAAOtH,GAAYwL,EAAS,SAAgBhG,GACvCJ,GAAAkC,EAAOrH,GAAawL,GAC/B1G,EAAQtJ,SACVoI,GAAYyH,EAAMhM,GAEpBsL,EAAMe,UAAUnG,EACxB,EAAS,CACDX,MAAM,IAERa,GAAY4B,EAAOrH,GAAawL,EAAU,WACzBrG,GAAAkC,EAAOtH,GAAYwL,GAC9BzG,EAAQtJ,SACVoI,GAAYyH,EAAMhM,EAE5B,EAAS,CACDuF,MAAM,GAEd,IACQE,EAAQzI,YACEoJ,GAAA1C,EAASlC,IAAc,WACjC2C,GAASoH,EAAMnL,GACvB,GAAS,CACDmF,MAAM,GAGX,EACD+G,WAAY,WACV,IAAIX,EAAQ9B,KAAK8B,MACbK,EAAOnC,KAAK2B,MAAMG,GACtB,GAAKK,EAAL,CAGA,IAAIO,EAAOP,EAAKQ,mBACZC,EAASC,SAASjO,OAAOiM,iBAAiB6B,GAAQP,GAAMW,WAAY,IACpE1B,EAAce,EAAKf,YACnB2B,EAAa3B,EAAcwB,EAGtBhJ,GAAAoG,KAAK0B,KAAMnI,GAAO,CACzBmF,MAAOqE,EAAa/C,KAAK3Q,OAASuT,GACjCxF,GAAc,CACfI,YAAawC,KAAKgB,WAAWtC,MAAQ0C,GAAe,EAAI2B,EAAajB,KAVtE,CAYF,EACDkB,UAAW,WACT,IAAItB,EAAO1B,KAAK0B,KAChBA,EAAKE,UAAY,GACjBlH,GAAYgH,EAAMnL,IAClBqD,GAAS8H,EAAMtE,GAAc,CAC3BI,WAAY,IAEf,EACDyF,UAAW,SAAmBC,GAC5B,IAQIC,EARAC,EAASpD,KACTpE,EAAUoE,KAAKpE,QACjBuC,EAAQ6B,KAAK7B,MACb6C,EAAahB,KAAKgB,WAChBqC,EAAerD,KAAKsD,OAAOjC,aAC3BkC,EAAcvC,EAAWtC,MACzB8E,EAAetC,KAAKC,IAAIH,EAAWrC,OAAS0E,EAAcA,GAC1DI,EAAezD,KAAK0D,WAAa,GAErC1D,KAAK2D,kBAAoB,CACvBC,MAAO,WACLT,EAAY1E,OAAS,IACtB,GAEH0E,EAAcjF,GAAqBC,EAAOvC,GAAS,SAAU0C,EAAcC,GACzE,IAAIsF,EAAcvF,EAAeC,EAC7BvM,EAAkBkP,KAAKC,IAAI,EAAGD,KAAK4C,IAAI,EAAGlI,EAAQ5J,kBAClD0M,EAAQ6E,EACR5E,EAAS6E,EACbJ,EAAOO,mBAAoB,EACvBH,EAAeK,EAAcN,EAC/B5E,EAAS4E,EAAcM,EAEvBnF,EAAQ8E,EAAeK,EAEP7R,EAAA2G,GAAS3G,GAAmBA,EAAkB,GAChE0M,EAAQwC,KAAK4C,IAAIpF,EAAQ1M,EAAiBsM,GAC1CK,EAASuC,KAAK4C,IAAInF,EAAS3M,EAAiBuM,GACxC,IAAAwF,GAAQR,EAAc7E,GAAS,EAC/BsF,GAAOR,EAAe7E,GAAU,EAChC+E,EAAY,CACdK,OACAC,MACAC,EAAGF,EACHG,EAAGF,EACHtF,QACAC,SACAwF,SAAU,EACVC,MAAO1F,EAAQJ,EACfuF,cACAvF,eACAC,iBAEE8F,EAAmB9K,GAAO,CAAE,EAAEmK,GAC9B9H,EAAQjJ,YACA+Q,EAAAvP,OAASsP,EAAatP,QAAU,EAC1CkQ,EAAiBlQ,OAAS,GAExByH,EAAQhJ,WACA8Q,EAAApG,OAASmG,EAAanG,QAAU,EAChCoG,EAAAnG,OAASkG,EAAalG,QAAU,EAC1C8G,EAAiB/G,OAAS,EAC1B+G,EAAiB9G,OAAS,GAE5B6F,EAAOM,UAAYA,EACnBN,EAAOiB,iBAAmBA,EACtBnB,MAGV,GACG,EACDoB,YAAa,SAAqBpB,GAChC,IAAIqB,EAASvE,KACT7B,EAAQ6B,KAAK7B,MACfuF,EAAY1D,KAAK0D,UAQnB,GAPA9J,GAASuE,EAAO5E,GAAO,CACrBmF,MAAOgF,EAAUhF,MACjBC,OAAQ+E,EAAU/E,OAElBmE,WAAYY,EAAUO,EACtBO,UAAWd,EAAUQ,GACpB9G,GAAcsG,KACbR,EACF,IAAKlD,KAAKyE,SAAWzE,KAAK0E,QAAU1E,KAAK2E,UAAY3E,KAAK4E,SAAW5E,KAAK6E,UAAY7E,KAAKpE,QAAQzI,YAAc+G,GAASiE,EAAO5H,IAAmB,CAC9I,IAAAuO,EAAkB,WACpBP,EAAOQ,gBAAiB,KAElC,EACQ/E,KAAK+E,eAAiB,CACpBnB,MAAO,WACU3H,GAAAkC,EAAOhH,GAAsB2N,EAC7C,GAESvI,GAAA4B,EAAOhH,GAAsB2N,EAAiB,CACxDpJ,MAAM,GAEhB,SAIG,EACDsJ,WAAY,WACV,IAAI7G,EAAQ6B,KAAK7B,MACbA,IACE6B,KAAKyE,SACPzE,KAAKyE,QAAQb,QAETzF,EAAA8G,WAAWrG,YAAYT,GAC7B6B,KAAK7B,MAAQ,KACb6B,KAAKtO,MAAMkQ,UAAY,GAE1B,GAGCsD,GAAS,CACXC,KAAM,WACJ,IAAIvJ,EAAUoE,KAAKpE,QACjB4F,EAASxB,KAAKwB,OACd4D,EAASpF,KAAKoF,OACZvQ,EAAWmL,KAAKnG,QAAQyG,cAChBkB,GAAAA,EAAQhL,GAAawJ,KAAKqF,QAAUrF,KAAKsF,MAAMH,KAAKnF,OACpDwB,GAAAA,EAAQ9K,GAAkBsJ,KAAKuF,YAAcvF,KAAKwF,UAAUL,KAAKnF,OACjEzD,GAAA6I,EAAQrO,GAAoBiJ,KAAKyF,cAAgBzF,KAAK0F,YAAYP,KAAKnF,OACvEnL,GAAAA,EAAUmC,GAAoBgJ,KAAK2F,cAAgB3F,KAAK4F,YAAYT,KAAKnF,OACzEnL,GAAAA,EAAUoC,GAAkB+I,KAAK6F,YAAc7F,KAAK8F,UAAUX,KAAKnF,OACnEnL,GAAAA,EAAU+B,GAAgBoJ,KAAK+F,UAAY/F,KAAKgG,QAAQb,KAAKnF,OAC7DzD,GAAA3H,OAAQsC,GAAc8I,KAAKiG,SAAWjG,KAAKkG,OAAOf,KAAKnF,OAC/DpE,EAAQ/I,UAAY+I,EAAQ7I,aAClByO,GAAAA,EAAQpK,GAAa4I,KAAKmG,QAAUnG,KAAKoG,MAAMjB,KAAKnF,MAAO,CACrEqG,SAAS,EACTC,SAAS,IAGT1K,EAAQ3I,kBACEsJ,GAAA6I,EAAQ3O,GAAgBuJ,KAAKuG,WAAavG,KAAKwG,SAASrB,KAAKnF,MAE5E,EACDyG,OAAQ,WACN,IAAI7K,EAAUoE,KAAKpE,QACjB4F,EAASxB,KAAKwB,OACd4D,EAASpF,KAAKoF,OACZvQ,EAAWmL,KAAKnG,QAAQyG,cACbkB,GAAAA,EAAQhL,GAAawJ,KAAKqF,SAC1B7D,GAAAA,EAAQ9K,GAAkBsJ,KAAKuF,aAC/BtJ,GAAAmJ,EAAQrO,GAAoBiJ,KAAKyF,eACjC5Q,GAAAA,EAAUmC,GAAoBgJ,KAAK2F,eACnC9Q,GAAAA,EAAUoC,GAAkB+I,KAAK6F,aACjChR,GAAAA,EAAU+B,GAAgBoJ,KAAK+F,WAC/B9J,GAAArH,OAAQsC,GAAc8I,KAAKiG,UACtCrK,EAAQ/I,UAAY+I,EAAQ7I,aACfyO,GAAAA,EAAQpK,GAAa4I,KAAKmG,QAAS,CAChDE,SAAS,EACTC,SAAS,IAGT1K,EAAQ3I,kBACKgJ,GAAAmJ,EAAQ3O,GAAgBuJ,KAAKuG,WAE/C,GAGCG,GAAW,CACbpB,MAAO,SAAejJ,GACpB,IAAIT,EAAUoE,KAAKpE,QACjB8H,EAAY1D,KAAK0D,UACfzT,EAASoM,EAAMpM,OACf0W,EAASzL,GAAQjL,EAAQqI,IAU7B,OATKqO,GAA+B,QAArB1W,EAAO2W,WAA0D,OAAnC3W,EAAO4W,cAAcD,YAEvDD,EAAAzL,GADTjL,EAASA,EAAO4W,cACSvO,KAIvBvD,GAAmBsH,EAAMyK,WAAa7W,IAAW+P,KAAKoF,QACxD2B,aAAa/G,KAAKgH,oBAEZL,GACN,IAAK,MACC3G,KAAKiH,OACPjH,KAAKtL,OACIkH,EAAQ1J,OACb8N,KAAKuB,OACPvB,KAAKkH,OAELlH,KAAKmH,OAGPnH,KAAKnM,OAEP,MACF,IAAK,OACEmM,KAAKoH,cACRpH,KAAKnM,OAEP,MACF,IAAK,OACHmM,KAAKjM,KAAKmH,GAAQjL,EAAQ,UAC1B,MACF,IAAK,UACE+P,KAAAzL,KAAK,IAAK,GACf,MACF,IAAK,WACEyL,KAAAzL,UAAW,GAChB,MACF,IAAK,aACHyL,KAAKqH,SACL,MACF,IAAK,QACHrH,KAAKsH,QACL,MACF,IAAK,OACEtH,KAAAuH,KAAK3L,EAAQrJ,MAClB,MACF,IAAK,OACEyN,KAAAvL,KAAKmH,EAAQ9J,YAClB,MACF,IAAK,OACEkO,KAAA0C,KAAK9G,EAAQrJ,MAClB,MACF,IAAK,cACHyN,KAAK7L,QAAU,IACf,MACF,IAAK,eACH6L,KAAK7L,OAAO,IACZ,MACF,IAAK,kBACH6L,KAAK1C,QAAQoG,EAAUpG,SAAY,GACnC,MACF,IAAK,gBACH0C,KAAKzC,QAAQmG,EAAUnG,SAAY,GACnC,MACF,QACMyC,KAAKiH,QACPjH,KAAKtL,OAGZ,EACD8R,SAAU,SAAkBnK,GAC1BA,EAAMmL,iBACFxH,KAAKhM,QAAUqI,EAAMpM,SAAW+P,KAAK7B,QAEnCpJ,GAAmBsH,EAAMyK,WAC3BC,aAAa/G,KAAKyH,yBAIfzH,KAAAqH,OAAOhL,EAAMyK,UAAYzK,EAAQA,EAAMY,QAAUZ,EAAMY,OAAOyK,eAEtE,EACDC,KAAM,WACJ,IAAIlG,EAAQzB,KACRA,KAAK4H,UACPb,aAAa/G,KAAK4H,SAClB5H,KAAK4H,SAAU,GAEjB,IAAI/N,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACfuC,EAAQ6B,KAAK7B,MACb2D,EAAQ9B,KAAK8B,MACbd,EAAahB,KAAKgB,WACpBtG,GAAYyD,EAAOjI,GACf0F,EAAQtJ,SACEoI,GAAAsF,KAAKoF,OAAQjP,GAE3BgI,EAAMpE,MAAM+E,QAAU,YAAc,eAAevJ,OAAOyL,EAAWtC,MAAQ,EAAG,OAAS,cAAcnJ,OAAOyL,EAAWrC,OAAS,EAAG,OAAS,sDAC9IqB,KAAKiD,WAAU,WACDpI,GAAAsD,EAAO/H,GAAYwF,EAAQlJ,SAC3BmI,GAAAsD,EAAO5H,GAAkBqF,EAAQzI,YAC7CsO,EAAM6C,aAAY,WAChB7C,EAAMzN,QAAS,EACfyN,EAAMgD,SAAU,EACZvL,GAAW0C,EAAQ5H,SACTuI,GAAA1C,EAASlC,GAAciE,EAAQ5H,OAAQ,CACjD0H,MAAM,IAGVkB,GAAc/C,EAASlC,GAAc,CACnCkQ,cAAepG,EAAMI,OAAOC,GAC5BA,QACA3D,SACC,CACDnB,YAAY,GAEtB,GACA,GACG,EACDwF,UAAW,SAAmBnG,GAC5B,IAAI8B,EAAQ9B,EAAMpM,OACdgR,EAAS9C,EAAM8G,WACf6C,EAAc7G,EAAOG,aAAe,GACpC2G,EAAe9G,EAAOI,cAAgB,GACtC2G,IAAW9M,GAAQiD,EAAO,UAC9BD,GAAqBC,EAAO6B,KAAKpE,SAAS,SAAU0C,EAAcC,GAChE,IAAIsF,EAAcvF,EAAeC,EAC7BG,EAAQoJ,EACRnJ,EAASoJ,EACTA,EAAelE,EAAciE,EAC3BE,EACFtJ,EAAQqJ,EAAelE,EAEvBlF,EAASmJ,EAAcjE,EAEhBmE,EACTrJ,EAASmJ,EAAcjE,EAEvBnF,EAAQqJ,EAAelE,EAEzBjK,GAASuE,EAAO5E,GAAO,CACrBmF,QACAC,UACCvB,GAAc,CACfI,YAAasK,EAAcpJ,GAAS,EACpCjB,YAAasK,EAAepJ,GAAU,KAE9C,GACG,EACDqH,QAAS,SAAiB3J,GACxB,IAAIT,EAAUoE,KAAKpE,QACf,GAACA,EAAQxJ,SAAT,CAGJ,IAAI6V,EAAU5L,EAAM4L,SAAW5L,EAAM6L,OAAS7L,EAAM8L,SACpD,GAEO,KAFCF,EAGAjI,KAAKwB,OAAOpH,SAASiC,EAAMpM,SAC7B+P,KAAKsF,MAAMjJ,GAIb,GAAC2D,KAAKuB,OAGV,OAAQ0G,GAEN,KAAK,GACCjI,KAAKiH,OACPjH,KAAKtL,OACIkH,EAAQ1J,OACb8N,KAAKuB,QACPvB,KAAKkH,OAGPlH,KAAKnM,OAEP,MAGF,KAAK,GACCmM,KAAKiH,QACPjH,KAAKtL,OAEP,MAGF,KAAK,GACCsL,KAAKiH,QAAUjH,KAAKoI,QACtBpI,KAAKoI,QAAQb,OAERvH,KAAAuH,KAAK3L,EAAQrJ,MAEpB,MAGF,KAAK,GAEH8J,EAAMmL,iBAGDxH,KAAAzL,KAAKqH,EAAQtI,WAAW,GAC7B,MAGF,KAAK,GACC0M,KAAKiH,QAAUjH,KAAKoI,QACtBpI,KAAKoI,QAAQ1F,OAER1C,KAAA0C,KAAK9G,EAAQrJ,MAEpB,MAGF,KAAK,GAEH8J,EAAMmL,iBAGNxH,KAAKzL,MAAMqH,EAAQtI,WAAW,GAC9B,MAGF,KAAK,GAKL,KAAK,GACC+I,EAAMgM,UACRhM,EAAMmL,iBACNxH,KAAKqH,UA/EV,CAmFF,EACD7B,UAAW,SAAmBnJ,GACG,QAA3BA,EAAMpM,OAAO2W,WACfvK,EAAMmL,gBAET,EACD9B,YAAa,SAAqBrJ,GAChC,IAAIT,EAAUoE,KAAKpE,QACjB0M,EAAWtI,KAAKsI,SACdC,EAAUlM,EAAMkM,QAClB/W,EAAS6K,EAAM7K,OAEjB,GADAwO,KAAKoH,cAAe,KACfpH,KAAKhM,QAAUgM,KAAKwI,SAAWxI,KAAKyE,SAAWzE,KAAKyI,SAGtC,cAAfpM,EAAMH,MAAuC,gBAAfG,EAAMH,MAAgD,UAAtBG,EAAMqM,eAExE/P,GAAS4P,IAAwB,IAAZA,GAAiB5P,GAASnH,IAAsB,IAAXA,GAGvD6K,EAAMgM,UART,CAaAhM,EAAMmL,iBACFnL,EAAMsM,eACArZ,GAAA+M,EAAMsM,gBAAgB,SAAUC,GACtCN,EAASM,EAAMC,YAAc5J,GAAW2J,EAChD,IAEMN,EAASjM,EAAMyM,WAAa,GAAK7J,GAAW5C,GAE1C,IAAAsK,IAAS/K,EAAQlJ,SAAUyC,EAC3ByG,EAAQ9I,aAAe8I,EAAQ/I,UAAYnE,OAAOC,KAAK2Z,GAAUjZ,OAAS,EACnEsX,EAAAtR,EACAuG,EAAQ5I,eAAuC,UAAtBqJ,EAAMqM,aAA0C,eAAfrM,EAAMH,OAA0B8D,KAAK+I,iBAC/FpC,EAAAvR,IAEPwG,EAAQzI,YAAewT,IAAWxR,GAAewR,IAAWtR,GAClDqF,GAAAsF,KAAK7B,MAAO5H,IAE1ByJ,KAAK2G,OAASA,CApBb,CAqBF,EACDf,YAAa,SAAqBvJ,GAChC,IAAIiM,EAAWtI,KAAKsI,SAClB3B,EAAS3G,KAAK2G,OACX3G,KAAKhM,QAAW2S,IAGrBtK,EAAMmL,iBACFnL,EAAMsM,eACArZ,GAAA+M,EAAMsM,gBAAgB,SAAUC,GAC/BrP,GAAA+O,EAASM,EAAMC,aAAe,CAAE,EAAE5J,GAAW2J,GAAO,GACnE,IAEarP,GAAA+O,EAASjM,EAAMyM,WAAa,IAAM,CAAE,EAAE7J,GAAW5C,GAAO,IAEjE2D,KAAKgJ,OAAO3M,GACb,EACDyJ,UAAW,SAAmBzJ,GAC5B,IAII4M,EAJA7F,EAASpD,KACTpE,EAAUoE,KAAKpE,QACjB+K,EAAS3G,KAAK2G,OACd2B,EAAWtI,KAAKsI,SAEdjM,EAAMsM,eACArZ,GAAA+M,EAAMsM,gBAAgB,SAAUC,GAC5BK,EAAAX,EAASM,EAAMC,mBAClBP,EAASM,EAAMC,WAC9B,KAEgBI,EAAAX,EAASjM,EAAMyM,WAAa,UAC/BR,EAASjM,EAAMyM,WAAa,IAEhCnC,IAGLtK,EAAMmL,kBACF5L,EAAQzI,YAAewT,IAAWxR,GAAewR,IAAWtR,GACrDiF,GAAA0F,KAAK7B,MAAO5H,IAEvByJ,KAAK2G,QAAS,EAGV5R,GAAmB4R,IAAWtR,GAAe4T,GAAWvJ,KAAKC,MAAQsJ,EAAQxJ,UAAY,MAC3FsH,aAAa/G,KAAKgH,oBAClBD,aAAa/G,KAAKyH,yBACd7L,EAAQ3I,kBAAoB+M,KAAKhM,QAAUqI,EAAMpM,SAAW+P,KAAK7B,MAC/D6B,KAAKkJ,cACPlJ,KAAKkJ,cAAe,EAGflJ,KAAAyH,wBAA0B0B,YAAW,WAC1BvM,GAAAwG,EAAOjF,MAAO1H,GAAgB,CAC1CiR,cAAerL,GAElB,GAAE,MAEH2D,KAAKkJ,cAAe,EAGflJ,KAAAyH,wBAA0B0B,YAAW,WACxC/F,EAAO8F,cAAe,CACvB,GAAE,OAGLlJ,KAAKkJ,cAAe,EAChBtN,EAAQrK,UAAiC,WAArBqK,EAAQrK,UAAyB8K,EAAMpM,SAAW+P,KAAKoF,SAExEpF,KAAAgH,mBAAqBmC,YAAW,WACrBvM,GAAAwG,EAAOgC,OAAQ5O,GAAa,CACxCkR,cAAerL,GAElB,GAAE,OAIV,EACD6J,OAAQ,WACN,IAAI3B,EAASvE,KACb,GAAKA,KAAKoJ,UAAWpJ,KAAKyI,SAGtBzI,KAAKuB,SACPvB,KAAKqJ,QACLrJ,KAAKK,WACLL,KAAKsJ,QAEPtJ,KAAKC,gBACLD,KAAKE,aACLF,KAAKI,eACLJ,KAAKyC,aACDzC,KAAKhM,QACPgM,KAAKiD,WAAU,WACbsB,EAAOD,aACf,IAEQtE,KAAKiH,QAAQ,CACf,GAAIjH,KAAKpE,QAAQ9J,YAAckO,KAAKuB,UAAY1M,SAAS0U,mBAAqB1U,SAAS2U,yBAA2B3U,SAAS4U,sBAAwB5U,SAAS6U,qBAE1J,YADA1J,KAAKtL,OAGPpF,GAAQ0Q,KAAK2J,OAAOC,qBAAqB,QAAQ,SAAUzL,GACzD5B,GAAY4B,EAAOtH,GAAY0N,EAAO/B,UAAU2C,KAAKZ,GAAS,CAC5D7I,MAAM,IAERkB,GAAcuB,EAAOtH,GAC7B,GACK,CACF,EACDuP,MAAO,SAAe/J,GACpB,IAAIwN,EAAS7J,KACT,GAACA,KAAKhM,SAGVqI,EAAMmL,kBAGFxH,KAAK8J,UAAT,CAGA9J,KAAK8J,UAAW,EAChBX,YAAW,WACTU,EAAOC,UAAW,CACnB,GAAE,IACH,IAAI1F,EAAQhT,OAAO4O,KAAKpE,QAAQtI,YAAc,GAC1CyW,EAAQ,EACR1N,EAAM2N,OACAD,EAAA1N,EAAM2N,OAAS,EAAI,GAAI,EACtB3N,EAAM4N,WACPF,GAAC1N,EAAM4N,WAAa,IACnB5N,EAAMY,SACP8M,EAAA1N,EAAMY,OAAS,EAAI,GAAI,GAEjC+C,KAAKzL,MAAMwV,EAAQ3F,GAAO,EAAM,KAAM/H,EAdrC,CAeF,GAGC6N,GAAU,CAKZvW,KAAM,WACA,IAAAwW,EAAY/a,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAC3EyK,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACjB,GAAIA,EAAQ1J,QAAU8N,KAAKwI,SAAWxI,KAAKoJ,SAAWpJ,KAAKwI,QAClD,OAAAxI,KAEL,IAACA,KAAKtM,MAKD,OAJPsM,KAAKoK,QACDpK,KAAKtM,OACPsM,KAAKrM,KAAKwW,GAELnK,KAOT,GALI9G,GAAW0C,EAAQjI,OACT4I,GAAA1C,EAASvC,GAAYsE,EAAQjI,KAAM,CAC7C+H,MAAM,KAGiC,IAAvCkB,GAAc/C,EAASvC,MAA0B0I,KAAKtM,MACjD,OAAAsM,KAELA,KAAKyI,QACPzI,KAAKqK,cAAczG,QAErB5D,KAAKwI,SAAU,EACfxI,KAAKsJ,OACL,IAAI9H,EAASxB,KAAKwB,OAMd,GALJ9G,GAAY8G,EAAQ3L,GACpB2L,EAAOjG,aAAa,OAAQ,UAC5BiG,EAAOjG,aAAa,kBAAmByE,KAAKtO,MAAMoO,IAClD0B,EAAOjG,aAAa,cAAc,GAClCiG,EAAO8I,gBAAgB,eACnB1O,EAAQzI,aAAegX,EAAW,CACpC,IAAIvW,EAAQoM,KAAKpM,MAAMuR,KAAKnF,MAC5BA,KAAKqK,cAAgB,CACnBzG,MAAO,WACUpC,GAAAA,EAAQrK,GAAsBvD,GAC7C8G,GAAY8G,EAAQvL,EACrB,GAEHqE,GAASkH,EAAQjL,IAGjBiL,EAAO+I,mBAAqB/I,EAAOJ,YACvBI,GAAAA,EAAQrK,GAAsBvD,EAAO,CAC/C8H,MAAM,IAERpB,GAASkH,EAAQvL,EACvB,MACMqE,GAASkH,EAAQvL,GACjB+J,KAAKpM,QAEA,OAAAoM,IACR,EAMDnM,KAAM,WACJ,IAAI4N,EAAQzB,KACRmK,EAAY/a,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAC3EyK,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACb,GAAAA,EAAQ1J,QAAU8N,KAAKyI,SAAYzI,KAAKoJ,UAAWpJ,KAAKwI,QACnD,OAAAxI,KAOT,GALI9G,GAAW0C,EAAQ/H,OACT0I,GAAA1C,EAASrC,GAAYoE,EAAQ/H,KAAM,CAC7C6H,MAAM,KAGiC,IAAvCkB,GAAc/C,EAASrC,IAClB,OAAAwI,KAELA,KAAKwI,SACPxI,KAAKqK,cAAczG,QAErB5D,KAAKyI,QAAS,EACVzI,KAAKiH,OACPjH,KAAKtL,OACIsL,KAAKyE,SACdzE,KAAKyE,QAAQb,QAEf,IAAIpC,EAASxB,KAAKwB,OAChBrD,EAAQ6B,KAAK7B,MACXqM,EAAkB,WACpB9P,GAAY8G,EAAQvL,GACpBwL,EAAM3N,QACZ,EACQ,GAAA8H,EAAQzI,aAAegX,EAAW,CAChC,IAAAM,EAAwB,SAASA,EAAsBpO,GAErDA,GAASA,EAAMpM,SAAWuR,IACbA,GAAAA,EAAQrK,GAAsBsT,GAC7ChJ,EAAM3N,SAEhB,EACU4W,EAAuB,WAErBxQ,GAASsH,EAAQjL,KACPiL,GAAAA,EAAQrK,GAAsBsT,GAC1C/P,GAAY8G,EAAQvL,OAI9B,EACM+J,KAAKqK,cAAgB,CACnBzG,MAAO,WACDnC,EAAMzN,QAAUkG,GAASiE,EAAO5H,IACnB0F,GAAAkC,EAAOhH,GAAsBuT,GACnCxQ,GAASsH,EAAQjL,KACXiL,GAAAA,EAAQrK,GAAsBsT,EAEhD,GAKCzK,KAAKhM,QAAUkG,GAASiE,EAAO5H,KACrBgG,GAAA4B,EAAOhH,GAAsBuT,EAAsB,CAC7DhP,MAAM,IAERsE,KAAK2K,OAAO,GAAG,EAAO,KAAM,MAAM,OAI1C,UAGW,OAAA3K,IACR,EAMDjM,KAAM,WACJ,IAAIqP,EAASpD,KACT8B,EAAQ1S,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK4Q,KAAKpE,QAAQ3J,iBAE7F,GADQ6P,EAAA1Q,OAAO0Q,IAAU,EACrB9B,KAAKyI,QAAUzI,KAAKiH,QAAUnF,EAAQ,GAAKA,GAAS9B,KAAK3Q,QAAU2Q,KAAKhM,QAAU8N,IAAU9B,KAAK8B,MAC5F,OAAA9B,KAEL,IAACA,KAAKoJ,QAER,OADApJ,KAAK8B,MAAQA,EACN9B,KAAKrM,OAEVqM,KAAKyE,SACPzE,KAAKyE,QAAQb,QAEX,IAAA/J,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACflK,EAAQsO,KAAKtO,MACb0T,EAASpF,KAAKoF,OACZjD,EAAOnC,KAAK2B,MAAMG,GAClBM,EAAMD,EAAKyI,cAAc,OACzBnX,EAAMyH,GAAQkH,EAAK,eACnBL,EAAMK,EAAI/G,aAAa,OACvB8C,EAAQtJ,SAASwJ,cAAc,OAc/B,GAbI/O,GAAAsM,EAAQ7J,qBAAqB,SAAUoJ,GACzC,IAAAzK,EAAQ0R,EAAI/G,aAAaF,GACf,OAAVzK,GACIyN,EAAA5C,aAAaJ,EAAMzK,EAEjC,IACIyN,EAAMU,IAAMpL,EACZ0K,EAAM4D,IAAMA,EACR7I,GAAW0C,EAAQ7H,OACTwI,GAAA1C,EAASnC,GAAYkE,EAAQ7H,KAAM,CAC7C2H,MAAM,KAOH,IAJHkB,GAAc/C,EAASnC,GAAY,CACrCmQ,cAAe7H,KAAK6B,OAAOC,GAC3BA,QACA3D,YACe6B,KAAKoJ,SAAWpJ,KAAKyI,QAAUzI,KAAKiH,OAC5C,OAAAjH,KAET,IAAI6K,EAAa7K,KAAK2B,MAAM3B,KAAK8B,OAC7B+I,IACFnQ,GAAYmQ,EAAYvV,GACxBuV,EAAWP,gBAAgB,kBAE7BhQ,GAAS6H,EAAM7M,GACV6M,EAAA5G,aAAa,iBAAiB,GAC/BK,EAAQvJ,OACV8P,EAAK9P,QAEP2N,KAAK7B,MAAQA,EACb6B,KAAKhM,QAAS,EACdgM,KAAK8B,MAAQA,EACb9B,KAAK0D,UAAY,GACjBpJ,GAAS6D,EAAOjI,GACZ0F,EAAQtJ,SACVgI,GAAS8K,EAAQjP,GAEnBiP,EAAOxD,UAAY,GACnBwD,EAAOrG,YAAYZ,GAGnB6B,KAAKyC,aAGL/Q,EAAMkQ,UAAY,GAGd,IAKAS,EACAC,EANAwI,EAAW,WACb,IAv1CsBpa,EAu1ClBgT,EAAYN,EAAOM,UACnB3D,EAAS1G,MAAMC,QAAQsC,EAAQlK,OAASkK,EAAQlK,MAAM,GAAKkK,EAAQlK,MACjEA,EAAAkQ,UAx1CHnJ,GADmB/H,EAy1CewI,GAAW6G,GAAUA,EAAO9O,KAAKmS,EAAQjF,EAAOuF,GAAa,GAAGnO,OAAOwM,EAAK,MAAMxM,OAAOmO,EAAUpF,aAAc,OAAU/I,OAAOmO,EAAUnF,cAAe,MAx1C3K7N,EAAMkK,QAAQ,gCAAiC,SAASA,QAAQ,KAAM,UAAUA,QAAQ,KAAM,SAASA,QAAQ,KAAM,QAAQA,QAAQ,KAAM,QAAUlK,CAy1ChL,EAyDW,OAtDK6L,GAAA1C,EAASlC,GAAcmT,EAAU,CAC3CpP,MAAM,IAERsE,KAAKyE,QAAU,CACbb,MAAO,WACU3H,GAAApC,EAASlC,GAAcmT,GAClC3M,EAAM4M,SACJ3H,EAAO2B,eACT3B,EAAO2B,eAAenB,QACbR,EAAOO,mBAChBP,EAAOO,kBAAkBC,SAI3BzF,EAAMU,IAAM,GACG5C,GAAAkC,EAAOtH,GAAYwL,GAC9Be,EAAOwE,SACTb,aAAa3D,EAAOwE,SAGzB,GAECzJ,EAAM4M,SACR/K,KAAK2H,QAELpL,GAAY4B,EAAOtH,GAAYwL,EAAS,WACvBpG,GAAAkC,EAAOrH,GAAawL,GACnCc,EAAOuE,MACf,EAAS,CACDjM,MAAM,IAERa,GAAY4B,EAAOrH,GAAawL,EAAU,WACzBrG,GAAAkC,EAAOtH,GAAYwL,GAC9Be,EAAOwE,UACTb,aAAa3D,EAAOwE,SACpBxE,EAAOwE,SAAU,GAEnBlN,GAAYyD,EAAOjI,GACf0F,EAAQtJ,SACEoI,GAAA0I,EAAOgC,OAAQjP,EAErC,EAAS,CACDuF,MAAM,IAEJsE,KAAK4H,SACPb,aAAa/G,KAAK4H,SAIf5H,KAAA4H,QAAUuB,YAAW,WACxBzO,GAAYyD,EAAOjI,GACnBkN,EAAOwE,SAAU,CAClB,GAAE,MAEE5H,IACR,EAODuH,KAAM,WACA,IAAAhV,EAAOnD,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GACtE0S,EAAQ9B,KAAK8B,MAAQ,EAKlB,OAJHA,EAAQ,IACFA,EAAAvP,EAAOyN,KAAK3Q,OAAS,EAAI,GAEnC2Q,KAAKjM,KAAK+N,GACH9B,IACR,EAOD0C,KAAM,WACA,IAAAnQ,EAAOnD,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GACtE4b,EAAWhL,KAAK3Q,OAAS,EACzByS,EAAQ9B,KAAK8B,MAAQ,EAKlB,OAJHA,EAAQkJ,IACVlJ,EAAQvP,EAAO,EAAIyY,GAErBhL,KAAKjM,KAAK+N,GACH9B,IACR,EAOD/L,KAAM,SAAcgQ,GACd,IAAAC,EAAI9U,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK6U,EACxEP,EAAY1D,KAAK0D,UAEd,OADP1D,KAAKiL,OAAOrS,GAAYqL,GAAKA,EAAIP,EAAUO,EAAI7S,OAAO6S,GAAIrL,GAAYsL,GAAKA,EAAIR,EAAUQ,EAAI9S,OAAO8S,IAC7FlE,IACR,EAQDiL,OAAQ,SAAgBhH,GACtB,IAAIM,EAASvE,KACTkE,EAAI9U,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK6U,EACxEiH,EAAiB9b,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KACrFyK,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf8H,EAAY1D,KAAK0D,UAGnB,GAFAO,EAAI7S,OAAO6S,GACXC,EAAI9S,OAAO8S,GACPlE,KAAKhM,SAAWgM,KAAKiH,QAAUrL,EAAQlJ,QAAS,CAClD,IAAIyY,EAAOzH,EAAUO,EACjBmH,EAAO1H,EAAUQ,EACjBmH,GAAU,EAWd,GAVI1S,GAASsL,GACDoH,GAAA,EAENpH,EAAAkH,EAEFxS,GAASuL,GACDmH,GAAA,EAENnH,EAAAkH,EAEFC,EAAS,CAMP,GALAnS,GAAW0C,EAAQ3H,OACTsI,GAAA1C,EAASjC,GAAYgE,EAAQ3H,KAAM,CAC7CyH,MAAM,KASH,IANHkB,GAAc/C,EAASjC,GAAY,CACrCqM,IACAC,IACAiH,OACAC,OACA1D,cAAewD,IAER,OAAAlL,KAET0D,EAAUO,EAAIA,EACdP,EAAUQ,EAAIA,EACdR,EAAUK,KAAOE,EACjBP,EAAUM,IAAME,EAChBlE,KAAK0E,QAAS,EACd1E,KAAKsE,aAAY,WACfC,EAAOG,QAAS,EACZxL,GAAW0C,EAAQ1H,QACTqI,GAAA1C,EAAShC,GAAa+D,EAAQ1H,MAAO,CAC/CwH,MAAM,IAGVkB,GAAc/C,EAAShC,GAAa,CAClCoM,IACAC,IACAiH,OACAC,OACA1D,cAAewD,GACd,CACDlO,YAAY,GAExB,GACO,CACF,CACM,OAAAgD,IACR,EAMD7L,OAAQ,SAAgBmX,GAEf,OADPtL,KAAKuL,UAAUvL,KAAK0D,UAAUvP,QAAU,GAAK/C,OAAOka,IAC7CtL,IACR,EAMDuL,SAAU,SAAkBD,GAC1B,IAAIzB,EAAS7J,KACTnG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf8H,EAAY1D,KAAK0D,UAEf,GAAA/K,GADJ2S,EAASla,OAAOka,KACQtL,KAAKhM,SAAWgM,KAAKiH,QAAUrL,EAAQjJ,UAAW,CACxE,IAAI6Y,EAAY9H,EAAUvP,OAMtB,GALA+E,GAAW0C,EAAQzH,SACToI,GAAA1C,EAAS/B,GAAc8D,EAAQzH,OAAQ,CACjDuH,MAAM,KAMH,IAHHkB,GAAc/C,EAAS/B,GAAc,CACvCwT,SACAE,cAEO,OAAAxL,KAET0D,EAAUvP,OAASmX,EACnBtL,KAAK2E,UAAW,EAChB3E,KAAKsE,aAAY,WACfuF,EAAOlF,UAAW,EACdzL,GAAW0C,EAAQxH,UACTmI,GAAA1C,EAAS9B,GAAe6D,EAAQxH,QAAS,CACnDsH,MAAM,IAGVkB,GAAc/C,EAAS9B,GAAe,CACpCuT,SACAE,aACC,CACDxO,YAAY,GAEtB,GACK,CACM,OAAAgD,IACR,EAMD1C,OAAQ,SAAgBmO,GAEf,OADPzL,KAAK3L,MAAMoX,EAASzL,KAAK0D,UAAUnG,QAC5ByC,IACR,EAMDzC,OAAQ,SAAgBmO,GAEf,OADP1L,KAAK3L,MAAM2L,KAAK0D,UAAUpG,OAAQoO,GAC3B1L,IACR,EAOD3L,MAAO,SAAeiJ,GACpB,IAAIqO,EAAS3L,KACTzC,EAASnO,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAKkO,EAC7EzD,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf8H,EAAY1D,KAAK0D,UAGnB,GAFApG,EAASlM,OAAOkM,GAChBC,EAASnM,OAAOmM,GACZyC,KAAKhM,SAAWgM,KAAKiH,QAAUrL,EAAQhJ,SAAU,CACnD,IAAIgZ,EAAYlI,EAAUpG,OACtBuO,EAAYnI,EAAUnG,OACtB8N,GAAU,EAWd,GAVI1S,GAAS2E,GACD+N,GAAA,EAEV/N,EAASsO,EAEPjT,GAAS4E,GACD8N,GAAA,EAEV9N,EAASsO,EAEPR,EAAS,CAMP,GALAnS,GAAW0C,EAAQvH,QACTkI,GAAA1C,EAAS7B,GAAa4D,EAAQvH,MAAO,CAC/CqH,MAAM,KAQH,IALHkB,GAAc/C,EAAS7B,GAAa,CACtCsF,OAAQA,EACRC,OAAQA,EACRqO,YACAC,cAEO,OAAA7L,KAET0D,EAAUpG,OAASA,EACnBoG,EAAUnG,OAASA,EACnByC,KAAK4E,SAAU,EACf5E,KAAKsE,aAAY,WACfqH,EAAO/G,SAAU,EACb1L,GAAW0C,EAAQtH,SACTiI,GAAA1C,EAAS5B,GAAc2D,EAAQtH,OAAQ,CACjDoH,MAAM,IAGVkB,GAAc/C,EAAS5B,GAAc,CACnCqF,OAAQA,EACRC,OAAQA,EACRqO,YACAC,aACC,CACD7O,YAAY,GAExB,GACO,CACF,CACM,OAAAgD,IACR,EASDzL,KAAM,SAAc6P,GACd,IAAA0H,EAAc1c,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAC7E2c,EAAQ3c,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAC5E8b,EAAiB9b,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KACrFsU,EAAY1D,KAAK0D,UAQd,OALLU,GAFFA,EAAQhT,OAAOgT,IACH,EACF,GAAK,EAAIA,GAET,EAAIA,EAETpE,KAAA2K,OAAOjH,EAAUhF,MAAQ0F,EAAQV,EAAUpF,aAAcwN,EAAaC,EAAOb,GAC3ElL,IACR,EAUD2K,OAAQ,SAAgBvG,GACtB,IAAI4H,EAAShM,KACT8L,EAAc1c,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAC7E2c,EAAQ3c,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAC5E8b,EAAiB9b,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KACrF6c,EAAY7c,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAC3EyK,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf0M,EAAWtI,KAAKsI,SAChB5E,EAAY1D,KAAK0D,UACfO,EAAIP,EAAUO,EAChBC,EAAIR,EAAUQ,EACdxF,EAAQgF,EAAUhF,MAClBC,EAAS+E,EAAU/E,OACnBL,EAAeoF,EAAUpF,aACzBC,EAAgBmF,EAAUnF,cAExB,GAAA5F,GADIyL,EAAAlD,KAAKC,IAAI,EAAGiD,KACGpE,KAAKhM,SAAWgM,KAAKiH,SAAWgF,GAAarQ,EAAQ/I,UAAW,CACrF,IAAKoZ,EAAW,CACd,IAAI1Y,EAAe2N,KAAKC,IAAI,IAAMvF,EAAQrI,cACtCC,EAAe0N,KAAK4C,IAAI,IAAKlI,EAAQpI,cACzC4Q,EAAQlD,KAAK4C,IAAI5C,KAAKC,IAAIiD,EAAO7Q,GAAeC,EACjD,CACD,GAAI0X,EACF,OAAQA,EAAehP,MACrB,IAAK,QACCN,EAAQtI,WAAa,MAAS8Q,EAAQ,KAAQA,EAAQ,OAChDA,EAAA,GAEV,MACF,IAAK,cACL,IAAK,YACL,IAAK,YACCA,EAAQ,KAAQA,EAAQ,OAClBA,EAAA,GAKhB,IAAI8H,EAAW5N,EAAe8F,EAC1B+H,EAAY5N,EAAgB6F,EAC5BhD,EAAc8K,EAAWxN,EACzB2C,EAAe8K,EAAYxN,EAC3BwF,EAAWT,EAAUU,MAMrB,GALAlL,GAAW0C,EAAQrH,OACTgI,GAAA1C,EAAS3B,GAAY0D,EAAQrH,KAAM,CAC7CmH,MAAM,KAOH,IAJHkB,GAAc/C,EAAS3B,GAAY,CACrCkM,QACAD,WACAuD,cAAewD,IAER,OAAAlL,KAGT,GADAA,KAAK6E,SAAU,EACXqG,EAAgB,CACd,IAAAkB,EA3+CZ,SAAmBvS,GACb,IAAAwS,EAAMxS,EAAQyS,wBACX,MAAA,CACLvI,KAAMsI,EAAItI,MAAQnP,OAAO2X,YAAc1X,SAASG,gBAAgBwX,YAChExI,IAAKqI,EAAIrI,KAAOpP,OAAO6X,YAAc5X,SAASG,gBAAgB0X,WAElE,CAq+CqBC,CAAU3M,KAAKwB,QACxBoL,EAAStE,GAAY5Z,OAAOC,KAAK2Z,GAAUjZ,OAAS,EAr0ChE,SAA2BiZ,GACzB,IAAIlJ,EAAQ,EACRC,EAAQ,EACRwN,EAAQ,EAUL,OATCvd,GAAAgZ,GAAU,SAAUwE,GAC1B,IAAIlN,EAASkN,EAAMlN,OACjBC,EAASiN,EAAMjN,OACRT,GAAAQ,EACAP,GAAAQ,EACAgN,GAAA,CACb,IAGS,CACLzN,MAHOA,GAAAyN,EAIPxN,MAHOA,GAAAwN,EAKX,CAozCoEE,CAAkBzE,GAAY,CACxFlJ,MAAO8L,EAAe9L,MACtBC,MAAO6L,EAAe7L,OAIxBqE,EAAUO,GAAK7C,IAAgBwL,EAAOxN,MAAQgN,EAAOrI,KAAOE,GAAKvF,GACjEgF,EAAUQ,GAAK7C,IAAiBuL,EAAOvN,MAAQ+M,EAAOpI,IAAME,GAAKvF,EAClE,MAAU5F,GAAcgT,IAAUpT,GAASoT,EAAM9H,IAAMtL,GAASoT,EAAM7H,IACrER,EAAUO,GAAK7C,IAAgB2K,EAAM9H,EAAIA,GAAKvF,GAC9CgF,EAAUQ,GAAK7C,IAAiB0K,EAAM7H,EAAIA,GAAKvF,KAG/C+E,EAAUO,GAAK7C,EAAc,EAC7BsC,EAAUQ,GAAK7C,EAAe,GAEhCqC,EAAUK,KAAOL,EAAUO,EAC3BP,EAAUM,IAAMN,EAAUQ,EAC1BR,EAAUhF,MAAQwN,EAClBxI,EAAU/E,OAASwN,EACnBzI,EAAUS,SAAWA,EACrBT,EAAUU,MAAQA,EAClBpE,KAAKsE,aAAY,WACf0H,EAAOnH,SAAU,EACb3L,GAAW0C,EAAQpH,SACT+H,GAAA1C,EAAS1B,GAAcyD,EAAQpH,OAAQ,CACjDkH,MAAM,IAGVkB,GAAc/C,EAAS1B,GAAc,CACnCiM,QACAD,WACAuD,cAAewD,GACd,CACDlO,YAAY,GAEtB,IACU8O,GACF9L,KAAK9M,SAER,CACM,OAAA8M,IACR,EAMDvL,KAAM,WACJ,IAAIuY,EAAShN,KACTlO,EAAa1C,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,IAAmBA,UAAU,GAChF,IAAK4Q,KAAKoJ,SAAWpJ,KAAKiH,OACjB,OAAAjH,KAET,IAAInG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QAMjB,GALI1C,GAAW0C,EAAQnH,OACT8H,GAAA1C,EAASzB,GAAYwD,EAAQnH,KAAM,CAC7CiH,MAAM,KAGiC,IAAvCkB,GAAc/C,EAASzB,IAClB,OAAA4H,KAET,IAAI2J,EAAS3J,KAAK2J,OACdtH,EAASrC,KAAKwC,UAAU2C,KAAKnF,MAC7B0B,EAAO,GACPuL,EAAQ,EACRnL,EAAQ,EA0BZ,GAzBA9B,KAAKiH,QAAS,EACdjH,KAAKkN,eAAiB7K,EAClBvQ,GACFkO,KAAKmN,kBAAkBrb,GAEzBwI,GAASqP,EAAQrT,IACjBhH,GAAQ0Q,KAAK2B,OAAO,SAAUQ,EAAMhS,GAC9B,IAAAiS,EAAMD,EAAKyI,cAAc,OACzBzM,EAAQtJ,SAASwJ,cAAc,OAC7BF,EAAAU,IAAM3D,GAAQkH,EAAK,eACnBjE,EAAA4D,IAAMK,EAAI/G,aAAa,OAC7B8C,EAAMiP,eAAiBhL,EAAIgL,eAClBH,GAAA,EACT3S,GAAS6D,EAAO1I,GACJoF,GAAAsD,EAAO5H,GAAkBqF,EAAQzI,YACzC+G,GAASiI,EAAM7M,KACjBgF,GAAS6D,EAAOlI,GACR6L,EAAA3R,GAEVuR,EAAKzS,KAAKkP,GACE5B,GAAA4B,EAAOtH,GAAYwL,EAAQ,CACrC3G,MAAM,IAERiO,EAAO5K,YAAYZ,EACzB,IACQxF,GAASiD,EAAQzJ,WAAayJ,EAAQzJ,SAAW,EAAG,CAClDoV,IAQA7E,EAAO,SAASA,IACLqE,aAAAiG,EAAO5E,QAAQR,SAChBlN,GAAAgH,EAAKI,GAAQ7L,GAGhBqE,GAAAoH,EADDI,GADCA,GAAA,GACOmL,EAAQnL,EAAQ,GACV7L,GACtB+W,EAAO5E,QAAQR,QAAUuB,WAAWzG,EAAM9G,EAAQzJ,SAC1D,EACU8a,EAAQ,IACVjN,KAAKoI,QAAU,CACbb,KAlBO,SAASA,IACLR,aAAAiG,EAAO5E,QAAQR,SAChBlN,GAAAgH,EAAKI,GAAQ7L,GAGhBqE,GAAAoH,EADDI,GADCA,GAAA,IACQ,EAAIA,EAAQmL,EAAQ,GACfhX,GACtB+W,EAAO5E,QAAQR,QAAUuB,WAAW5B,EAAM3L,EAAQzJ,SAC1D,EAYUuQ,KAAMA,EACNkF,QAASuB,WAAWzG,EAAM9G,EAAQzJ,WAGvC,CACM,OAAA6N,IACR,EAEDtL,KAAM,WACJ,IAAI2Y,EAASrN,KACT,IAACA,KAAKiH,OACD,OAAAjH,KAET,IAAInG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QAMjB,GALI1C,GAAW0C,EAAQlH,OACT6H,GAAA1C,EAASxB,GAAYuD,EAAQlH,KAAM,CAC7CgH,MAAM,KAGiC,IAAvCkB,GAAc/C,EAASxB,IAClB,OAAA2H,KAET,IAAI2J,EAAS3J,KAAK2J,OAUX,OATM5C,aAAA/G,KAAKoI,QAAQR,SAC1B5H,KAAKoI,SAAU,EACfpI,KAAKiH,QAAS,EACd3X,GAAQqa,EAAOC,qBAAqB,QAAQ,SAAUzL,GACrClC,GAAAkC,EAAOtH,GAAYwW,EAAOH,eAC/C,IACIxS,GAAYiP,EAAQrT,IACpBqT,EAAO/H,UAAY,GACnB5B,KAAKsN,iBACEtN,IACR,EAEDmH,KAAM,WACJ,IAAIoG,EAASvN,KACTpE,EAAUoE,KAAKpE,QACjB4F,EAASxB,KAAKwB,OACdrD,EAAQ6B,KAAK7B,MACbuD,EAAO1B,KAAK0B,KACV,OAAC1B,KAAKoJ,SAAWpJ,KAAKiH,QAAUjH,KAAKuB,SAAW3F,EAAQ1J,SAG5D8N,KAAKuB,QAAS,EACdvB,KAAKsJ,OACIhP,GAAA0F,KAAKxO,OAAQoE,GAClBgG,EAAQzI,aACVuH,GAAYgH,EAAMnL,IACdyJ,KAAKhM,QACP0G,GAAYyD,EAAO5H,KAGvB+D,GAASkH,EAAQ9L,GACjB8L,EAAOjG,aAAa,OAAQ,UAC5BiG,EAAOjG,aAAa,kBAAmByE,KAAKtO,MAAMoO,IAClD0B,EAAOjG,aAAa,cAAc,GAClCiG,EAAO8I,gBAAgB,SACvB1Q,GAAS4H,EAAQ,CACfpO,OAAQwI,EAAQxI,SAEdwI,EAAQvJ,OACV2N,KAAKwN,eAEPxN,KAAKC,gBACLD,KAAKgB,WAAazH,GAAO,CAAE,EAAEyG,KAAKc,eAClCd,KAAKyC,aACDzC,KAAKhM,QACPgM,KAAKiD,WAAU,WACbsK,EAAOjJ,aAAY,WACb1I,EAAQzI,YACVgW,YAAW,WACT7O,GAAS6D,EAAO5H,IAChB+D,GAASoH,EAAMnL,GAChB,GAAE,EAEf,GACA,KAnCayJ,IAsCV,EAEDkH,KAAM,WACJ,IAAIuG,EAAUzN,KACVpE,EAAUoE,KAAKpE,QACjB4F,EAASxB,KAAKwB,OACdrD,EAAQ6B,KAAK7B,MACbuD,EAAO1B,KAAK0B,KACV,OAAC1B,KAAKoJ,UAAWpJ,KAAKiH,QAAWjH,KAAKuB,QAAW3F,EAAQ1J,QAG7D8N,KAAKuB,QAAS,EACdvB,KAAKqJ,QACO3O,GAAAsF,KAAKxO,OAAQoE,GACrBgG,EAAQzI,aACVuH,GAAYgH,EAAMnL,IACdyJ,KAAKhM,QACP0G,GAAYyD,EAAO5H,KAGnBqF,EAAQvJ,OACV2N,KAAK0N,oBAEPlM,EAAO8I,gBAAgB,QACvB9I,EAAO8I,gBAAgB,mBACvB9I,EAAO8I,gBAAgB,cACvB5P,GAAY8G,EAAQ9L,GACpBkE,GAAS4H,EAAQ,CACfpO,OAAQwI,EAAQvI,eAElB2M,KAAKgB,WAAazH,GAAO,CAAE,EAAEyG,KAAKsB,YAClCtB,KAAKI,eACLJ,KAAKyC,aACDzC,KAAKhM,QACPgM,KAAKiD,WAAU,WACbwK,EAAQnJ,aAAY,WACd1I,EAAQzI,YACVgW,YAAW,WACT7O,GAAS6D,EAAO5H,IAChB+D,GAASoH,EAAMnL,GAChB,GAAE,EAEf,GACA,IAEWyJ,MApCEA,IAqCV,EAED9M,QAAS,WACP,IAAIya,EAAU3N,KACVpE,EAAUoE,KAAKpE,QACjBgS,EAAa5N,KAAK4N,WAClBlK,EAAY1D,KAAK0D,UACnB,OAAK1D,KAAKhM,SAAUgM,KAAKiH,QAAWrL,EAAQ1I,SAGjC0a,EAAAC,YAAc,GAAGtY,OAAO2L,KAAK4M,MAAwB,IAAlBpK,EAAUU,OAAc,KACjEpE,KAAK+N,YAkBRhH,aAAa/G,KAAK+N,aAjBdnS,EAAQzI,YACN6M,KAAKgO,QACPpR,GAAcgR,EAAYzW,IAE5BmD,GAASsT,EAAYtX,IACrBgE,GAASsT,EAAYnY,GACrB6E,GAASsT,EAAYrX,IACrBqX,EAAWtD,gBAAgB,eAG3BsD,EAAWrD,mBAAqBqD,EAAWxM,YAC3C9G,GAASsT,EAAY3X,KAErBqE,GAASsT,EAAYtX,IACrBsX,EAAWtD,gBAAgB,gBAK1BtK,KAAA+N,YAAc5E,YAAW,WACxBvN,EAAQzI,YACEoJ,GAAAqR,EAAYzW,IAAsB,WAC5CuD,GAAYkT,EAAYtX,IACxBoE,GAAYkT,EAAYnY,GACxBiF,GAAYkT,EAAYrX,IACbqX,EAAArS,aAAa,eAAe,GACvCoS,EAAQK,QAAS,CAC3B,GAAW,CACDtS,MAAM,IAERhB,GAAYkT,EAAY3X,GACxB0X,EAAQK,QAAS,IAEjBtT,GAAYkT,EAAYtX,IACbsX,EAAArS,aAAa,eAAe,IAEzCoS,EAAQI,aAAc,CACvB,GAAE,KACI/N,MA1CEA,IA2CV,EAMDqH,OAAQ,WACF,IAAA6D,EAAiB9b,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAMlF,OALsB,IAAzB4Q,KAAK0D,UAAUU,MACjBpE,KAAK2K,OAAO3K,KAAK0D,UAAUS,UAAU,EAAM,KAAM+G,GAEjDlL,KAAK2K,OAAO,GAAG,EAAM,KAAMO,GAEtBlL,IACR,EAEDsH,MAAO,WAKE,OAJHtH,KAAKhM,SAAWgM,KAAKiH,SACvBjH,KAAK0D,UAAYnK,GAAO,CAAE,EAAEyG,KAAKqE,kBACjCrE,KAAKsE,eAEAtE,IACR,EAEDiO,OAAQ,WACN,IAAIC,EAAUlO,KACVnG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACfuS,EAAQnO,KAAKmO,MAGX,GAAAA,IAAUtU,EAAQoL,WACpB,OAAOjF,KAAKoO,UAEd,IAAIvM,EAAS,GAUT,GATIvS,GAAA6e,EAAQ,CAACtU,GAAWA,EAAQwU,iBAAiB,QAAQ,SAAUlQ,GACjEjF,GAAW0C,EAAQ9M,QACjB8M,EAAQ9M,OAAOmC,KAAKid,EAAS/P,IAC/B0D,EAAO5S,KAAKkP,GAEL+P,EAAQhM,YAAY/D,IAC7B0D,EAAO5S,KAAKkP,EAEpB,KACS0D,EAAOxS,OACH,OAAA2Q,KAIT,GAFAA,KAAK6B,OAASA,EACd7B,KAAK3Q,OAASwS,EAAOxS,OACjB2Q,KAAKtM,MAAO,CACd,IAAI4a,EAAiB,GAmBrB,GAlBAhf,GAAQ0Q,KAAK2B,OAAO,SAAUQ,EAAMhS,GAC9B,IAAAiS,EAAMD,EAAKyI,cAAc,OACzBzM,EAAQ0D,EAAO1R,GACfgO,GAASiE,GACPjE,EAAMU,MAAQuD,EAAIvD,KAGnBV,EAAM4D,MAAQK,EAAIL,KAIrBuM,EAAerf,KAAKkB,EAE9B,IACMyJ,GAASoG,KAAK0B,KAAM,CAClBhD,MAAO,SAETsB,KAAKG,WACDH,KAAKoJ,QACP,GAAIpJ,KAAK3Q,QACP,GAAI2Q,KAAKhM,OAAQ,CACf,IAAIua,EAAeD,EAAejU,QAAQ2F,KAAK8B,OAC/C,GAAIyM,GAAgB,EAClBvO,KAAKhM,QAAS,EACdgM,KAAKjM,KAAKmN,KAAKC,IAAID,KAAK4C,IAAI9D,KAAK8B,MAAQyM,EAAcvO,KAAK3Q,OAAS,GAAI,QACpE,CACL,IAAIwb,EAAa7K,KAAK2B,MAAM3B,KAAK8B,OAGjCxH,GAASuQ,EAAYvV,GACVuV,EAAAtP,aAAa,iBAAiB,EAC1C,CACF,OAEDyE,KAAK7B,MAAQ,KACb6B,KAAKhM,QAAS,EACdgM,KAAK8B,MAAQ,EACb9B,KAAK0D,UAAY,GACjB1D,KAAKoF,OAAOxD,UAAY,GACxB5B,KAAKtO,MAAMkQ,UAAY,EAGjC,MACM5B,KAAKoK,QAEA,OAAApK,IACR,EAEDoO,QAAS,WACP,IAAIvU,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACb,OAAC/B,EAAQ3E,IAGb8K,KAAKwO,WAAY,EACbxO,KAAKtM,OACHsM,KAAKiH,QACPjH,KAAKtL,OAEHkH,EAAQ1J,QACN8N,KAAKuB,QACPvB,KAAKkH,OAEPlH,KAAKyG,UACIzG,KAAKoJ,SACVpJ,KAAKyE,UACHzE,KAAK+E,eACP/E,KAAK+E,eAAenB,QACX5D,KAAK2D,mBACd3D,KAAK2D,kBAAkBC,SAGvB5D,KAAKyI,QACPzI,KAAKqK,cAAczG,QAErB5D,KAAKlM,UACIkM,KAAKwI,UACdxI,KAAKqK,cAAczG,QACnB5D,KAAKlM,UAEPkM,KAAKtM,OAAQ,EACbsM,KAAKwB,OAAOyD,WAAWrG,YAAYoB,KAAKwB,SAC/B5F,EAAQ1J,SACb8N,KAAKyO,SACPzO,KAAKyO,SAAS7K,QACL5D,KAAK0O,cACd1O,KAAK0O,aAAa9K,SAGjBhI,EAAQ1J,QACI+J,GAAApC,EAASrD,GAAawJ,KAAK2O,SAE5C9U,EAAQ3E,QAAa,EACd8K,MAzCEA,IA0CV,GAGC4O,GAAS,CACX1M,YAAa,SAAqB/D,GAC5B,IAAA1K,EAAMuM,KAAKpE,QAAQnI,IAQhB,OANCA,EADJgF,GAAShF,GACL0K,EAAM9C,aAAa5H,GAChByF,GAAWzF,GACdA,EAAIxC,KAAK+O,KAAM7B,GAEf,EAGT,EACDqP,aAAc,WACZ,IAAI/L,EAAQzB,KACZA,KAAK0N,oBACLnR,GAAY1H,SAAU8B,GAAeqJ,KAAK6O,UAAY,SAAUxS,GAC9D,IAAImF,EAASC,EAAMD,OACfvR,EAASoM,EAAMpM,OACnB,GAAIA,IAAW4E,UAAY5E,IAAWuR,IAAUA,EAAOpH,SAASnK,GAAhE,CAGA,KAAOA,GAAQ,CAET,GAAoC,OAApCA,EAAOoL,aAAa,aAA8D,SAAtCpL,EAAOoL,aAAa,cAClE,OAEFpL,EAASA,EAAO4W,aACjB,CACDrF,EAAOnP,OARN,CASP,EACG,EACDqb,kBAAmB,WACb1N,KAAK6O,YACQ5S,GAAApH,SAAU8B,GAAeqJ,KAAK6O,WAC7C7O,KAAK6O,UAAY,KAEpB,EACDvF,KAAM,WACJ,IAAI9K,EAAOwB,KAAKxB,KAChBlE,GAASkE,EAAMnI,IACX2J,KAAKO,eAAiB,IACnB/B,EAAAzE,MAAM4G,aAAe,GAAGpL,OAAOyK,KAAKO,gBAAkBuO,WAAW9O,KAAKY,kCAAoC,GAAI,MAEtH,EACDyI,MAAO,WACL,IAAI7K,EAAOwB,KAAKxB,KAChB9D,GAAY8D,EAAMnI,IACd2J,KAAKO,eAAiB,IACnB/B,EAAAzE,MAAM4G,aAAeX,KAAKU,wBAElC,EACD9M,MAAO,WACL,IAAIiG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf4F,EAASxB,KAAKwB,OAChBxB,KAAKuB,QAAS,EACdvB,KAAKoJ,SAAU,EACfpJ,KAAKD,SACLC,KAAKmF,OACLnF,KAAKwI,SAAU,EACX5M,EAAQvJ,QACVmP,EAAOnP,QACP2N,KAAKwN,gBAEHtU,GAAW0C,EAAQhI,QACT2I,GAAA1C,EAAStC,GAAaqE,EAAQhI,MAAO,CAC/C8H,MAAM,KAGkC,IAAxCkB,GAAc/C,EAAStC,KAGvByI,KAAKtM,OAASsM,KAAKoJ,UAAYpJ,KAAKyI,QACjCzI,KAAAjM,KAAKiM,KAAK8B,MAElB,EACDhO,OAAQ,WACN,IAAI+F,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACf4F,EAASxB,KAAKwB,OACZ5F,EAAQmT,OACV/O,KAAK0N,oBAEP1N,KAAKqJ,QACLrJ,KAAKyG,SACLnM,GAASkH,EAAQ3L,GACjB2L,EAAO8I,gBAAgB,QACvB9I,EAAO8I,gBAAgB,mBACvB9I,EAAO8I,gBAAgB,cACvB9I,EAAOjG,aAAa,eAAe,GACnCyE,KAAKgD,YACLhD,KAAKgF,aACLhF,KAAKuB,QAAS,EACdvB,KAAKhM,QAAS,EACdgM,KAAKoJ,SAAU,EACfpJ,KAAKyI,QAAS,EACTzI,KAAKwO,YACJtV,GAAW0C,EAAQ9H,SACTyI,GAAA1C,EAASpC,GAAcmE,EAAQ9H,OAAQ,CACjD4H,MAAM,IAGIkB,GAAA/C,EAASpC,GAAc,KAAM,CACzCuF,YAAY,IAGjB,EACDmQ,kBAAmB,SAA2BvR,GACxC/G,IAAAA,EAAWmL,KAAKnG,QAAQyG,cACxB,GAAAN,KAAKuB,UAAY1M,EAAS0U,mBAAqB1U,EAAS2U,yBAA2B3U,EAAS4U,sBAAwB5U,EAAS6U,qBAAsB,CACrJ,IAAI1U,EAAkBH,EAASG,gBAG3BA,EAAgBmY,kBAEdpU,GAAc6C,GAChB5G,EAAgBmY,kBAAkBvR,GAElC5G,EAAgBmY,oBAETnY,EAAgBga,wBACTha,EAAAga,wBAAwBC,QAAQC,sBACvCla,EAAgBma,qBACzBna,EAAgBma,uBACPna,EAAgBoa,qBACzBpa,EAAgBoa,qBAEnB,CACF,EACD9B,eAAgB,WACVzY,IAAAA,EAAWmL,KAAKnG,QAAQyG,cACxBN,KAAKuB,SAAW1M,EAAS0U,mBAAqB1U,EAAS2U,yBAA2B3U,EAAS4U,sBAAwB5U,EAAS6U,uBAE1H7U,EAASyY,eACXzY,EAASyY,iBACAzY,EAASwa,qBAClBxa,EAASwa,uBACAxa,EAASya,oBAClBza,EAASya,sBACAza,EAAS0a,kBAClB1a,EAAS0a,mBAGd,EACDvG,OAAQ,SAAgB3M,GACtB,IAAIT,EAAUoE,KAAKpE,QACjB0M,EAAWtI,KAAKsI,SACdW,EAAUX,EAAS5Z,OAAOC,KAAK2Z,GAAU,IAG7C,GAAKW,EAAL,CAGI,IAAAuG,EAAUvG,EAAQ1J,KAAO0J,EAAQrJ,OACjC6P,EAAUxG,EAAQzJ,KAAOyJ,EAAQpJ,OACrC,OAAQG,KAAK2G,QAEX,KAAKxR,EACa,IAAZqa,GAA6B,IAAZC,IACnBzP,KAAKoH,cAAe,EACfpH,KAAA/L,KAAKub,EAASC,EAASpT,IAE9B,MAGF,KAAKhH,EACH2K,KAAKzL,KAl9Db,SAAyB+T,GACvB,IAAIoH,EAAYvgB,EAAe,CAAE,EAAEmZ,GAC/BqH,EAAS,GAiBb,OAhBQrgB,GAAAgZ,GAAU,SAAUW,EAASH,UAC5B4G,EAAU5G,GACTxZ,GAAAogB,GAAW,SAAUE,GAC3B,IAAIC,EAAK3O,KAAK4O,IAAI7G,EAAQrJ,OAASgQ,EAAShQ,QACxCmQ,EAAK7O,KAAK4O,IAAI7G,EAAQpJ,OAAS+P,EAAS/P,QACxCmQ,EAAK9O,KAAK4O,IAAI7G,EAAQ1J,KAAOqQ,EAASrQ,MACtC0Q,EAAK/O,KAAK4O,IAAI7G,EAAQzJ,KAAOoQ,EAASpQ,MACtC0Q,EAAKhP,KAAKiP,KAAKN,EAAKA,EAAKE,EAAKA,GAE9B3L,GADKlD,KAAKiP,KAAKH,EAAKA,EAAKC,EAAKA,GAChBC,GAAMA,EACxBP,EAAO1gB,KAAKmV,EAClB,GACA,IACSuL,EAAAS,MAAK,SAAUC,EAAGC,GACvB,OAAOpP,KAAK4O,IAAIO,GAAKnP,KAAK4O,IAAIQ,EAClC,IACSX,EAAO,EAChB,CA87DkBY,CAAgBjI,IAAW,EAAO,KAAMjM,GAClD,MACF,KAAKjH,EAED4K,KAAK2G,OAAS,WACV,IAAA6J,EAAkBtP,KAAK4O,IAAIN,GAC3BgB,EAAkB,GAAKA,EAAkBtP,KAAK4O,IAAIL,KAEpDzP,KAAKsI,SAAW,GACZkH,EAAU,EACPxP,KAAAuH,KAAK3L,EAAQrJ,MACTid,GAAc,GAClBxP,KAAA0C,KAAK9G,EAAQrJ,OAQpBjD,GAAAgZ,GAAU,SAAUmI,GAC1BA,EAAE7Q,OAAS6Q,EAAElR,KACbkR,EAAE5Q,OAAS4Q,EAAEjR,IACnB,GArCK,CAsCF,EACDuJ,aAAc,WACZ,IAAIrF,EAAY1D,KAAK0D,UACnB1C,EAAahB,KAAKgB,WACpB,OAAOhB,KAAK3Q,OAAS,GAAKqU,EAAUO,GAAK,GAAKP,EAAUQ,GAAK,GAAKR,EAAUhF,OAASsC,EAAWtC,OAASgF,EAAU/E,QAAUqC,EAAWrC,MACzI,GAGC+R,GAAgB5b,EAAO6b,OACvBC,IAAwB9Q,IAKxB,EAJK,WAEE,OADDA,IAAA,CAEV,GAEI6Q,GAAkC,WAMpC,SAASA,EAAO9W,GACV,IAAA+B,EAAUxM,UAAUC,OAAS,QAAsB,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAElF,GA/0FJ,SAAyByhB,EAAUC,GAC7B,KAAED,aAAoBC,GAClB,MAAA,IAAI5f,UAAU,oCAExB,CA00FI6f,CAAgB/Q,KAAM2Q,IACjB9W,GAAgC,IAArBA,EAAQmX,SAChB,MAAA,IAAIC,MAAM,0DAElBjR,KAAKnG,QAAUA,EACVmG,KAAApE,QAAUrC,GAAO,CAAE,EAAEjI,EAAUyH,GAAc6C,IAAYA,GAC9DoE,KAAK2G,QAAS,EACd3G,KAAKgO,QAAS,EACdhO,KAAKuB,QAAS,EACdvB,KAAKyI,QAAS,EACdzI,KAAKkJ,cAAe,EACpBlJ,KAAK0D,UAAY,GACZ1D,KAAA8B,MAAQ9B,KAAKpE,QAAQ3J,iBAC1B+N,KAAKmO,OAAQ,EACbnO,KAAKoJ,SAAU,EACfpJ,KAAK3Q,OAAS,EACd2Q,KAAK0E,QAAS,EACd1E,KAAKiH,QAAS,EACdjH,KAAKoI,SAAU,EACfpI,KAAKsI,SAAW,GAChBtI,KAAKtM,OAAQ,EACbsM,KAAK2E,UAAW,EAChB3E,KAAK4E,SAAU,EACf5E,KAAKwI,SAAU,EACfxI,KAAK4H,SAAU,EACf5H,KAAK+N,aAAc,EACnB/N,KAAKhM,QAAS,EACdgM,KAAKyE,SAAU,EACfzE,KAAK8J,UAAW,EAChB9J,KAAK6E,SAAU,EACf7E,KAAKoH,cAAe,EACpBpH,KAAKF,GAAK8Q,KACV5Q,KAAKkR,MACN,CAj2FH,IAAsBJ,EAAaK,EAAYC,EAwmGtCT,OAxmGaG,EAk2FPH,EAl2FgCS,EAulGzC,CAAC,CACH5gB,IAAK,aACLE,MAAO,WAEEigB,OADP/b,OAAO+b,OAASD,GACTC,CACR,GAMA,CACDngB,IAAK,cACLE,MAAO,SAAqBkL,GAC1BrC,GAAOjI,EAAUyH,GAAc6C,IAAYA,EAC5C,KAtmG8BuV,EAk2FZ,CAAC,CACpB3gB,IAAK,OACLE,MAAO,WACL,IAAI+Q,EAAQzB,KACRnG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACb,IAAA/B,EAAQ3E,GAAR,CAGJ2E,EAAQ3E,GAAa8K,KAGjBpE,EAAQvJ,QAAUuJ,EAAQxJ,WAC5BwJ,EAAQvJ,OAAQ,GAEd,IAAA8b,EAA8B,QAAtBtU,EAAQ+M,UAChB/E,EAAS,GAmBb,GAlBQvS,GAAA6e,EAAQ,CAACtU,GAAWA,EAAQwU,iBAAiB,QAAQ,SAAUlQ,GACjEjF,GAAW0C,EAAQ9M,QACjB8M,EAAQ9M,OAAOmC,KAAKwQ,EAAOtD,IAC7B0D,EAAO5S,KAAKkP,GAELsD,EAAMS,YAAY/D,IAC3B0D,EAAO5S,KAAKkP,EAEtB,IACM6B,KAAKmO,MAAQA,EACbnO,KAAK3Q,OAASwS,EAAOxS,OACrB2Q,KAAK6B,OAASA,EACd7B,KAAKK,WAGDzH,GAAY/D,SAASwJ,cAAcnJ,GAAW6E,MAAM5G,cACtDyI,EAAQzI,YAAa,GAEnByI,EAAQ1J,OAAQ,CAClB,IAAI2a,EAAQ,EACRwE,EAAW,WAGP,IAAAzJ,GAFGiF,GAAA,KACKpL,EAAMpS,SAElBoS,EAAMiN,cAAe,EACrBjN,EAAMgN,SAAW,CACf7K,MAAO,WACLmD,aAAaa,EACd,GAIHA,EAAUuB,YAAW,WACnB1H,EAAMgN,UAAW,EACjBhN,EAAM2I,OACP,GAAE,GAEf,EACQpK,KAAK0O,aAAe,CAClB9K,MAAO,WACGtU,GAAAuS,GAAQ,SAAU1D,GACnBA,EAAM4M,WACM9O,GAAAkC,EAAOtH,GAAYwa,GACnBpV,GAAAkC,EAAOrH,GAAaua,GAEnD,GACW,GAEK/hB,GAAAuS,GAAQ,SAAU1D,GAIlB,IAAAkE,EACAC,EAJFnE,EAAM4M,cAKRxO,GAAY4B,EAAOtH,GAAYwL,EAAS,WACvBpG,GAAAkC,EAAOrH,GAAawL,MAEjD,EAAe,CACD5G,MAAM,IAERa,GAAY4B,EAAOrH,GAAawL,EAAU,WACzBrG,GAAAkC,EAAOtH,GAAYwL,MAEhD,EAAe,CACD3G,MAAM,IAGpB,GACA,MACQa,GAAY1C,EAASrD,GAAawJ,KAAK2O,QAAU,SAAUtR,GACzD,IAAIpN,EAASoN,EAAKpN,OACO,QAArBA,EAAO2W,WAAyB1N,GAAW0C,EAAQ9M,UAAW8M,EAAQ9M,OAAOmC,KAAKwQ,EAAOxR,IAC3FwR,EAAM1N,KAAK0N,EAAMI,OAAOxH,QAAQpK,GAE5C,EAnFO,CAqFF,GACA,CACDO,IAAK,QACLE,MAAO,WACL,IAAIsP,KAAKtM,MAAT,CAGA,IAAImG,EAAUmG,KAAKnG,QACjB+B,EAAUoE,KAAKpE,QACbqF,EAASpH,EAAQoL,WACjBqM,EAAWzc,SAASwJ,cAAc,OACtCiT,EAAS1P,UAttFA,wcAutFT,IAAIJ,EAAS8P,EAAS1G,cAAc,IAAIrV,OAAOL,EAAW,eACtDxD,EAAQ8P,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,WACnDvD,EAAU6P,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,aACrDzD,EAAS+P,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,YACpD1D,EAASgQ,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,YACpDkQ,EAAS5D,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,YAgCxD,GA/BA8K,KAAKiB,OAASA,EACdjB,KAAKwB,OAASA,EACdxB,KAAKtO,MAAQA,EACbsO,KAAKrO,QAAUA,EACfqO,KAAKvO,OAASA,EACduO,KAAKxO,OAASA,EACdwO,KAAKoF,OAASA,EACdpF,KAAKsD,OAAS9B,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,YACzD8K,KAAK4N,WAAapM,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,aAC7D8K,KAAK2J,OAASnI,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,YACzD8K,KAAK0B,KAAOF,EAAOoJ,cAAc,IAAIrV,OAAOL,EAAW,UACvDsM,EAAO1B,GAAK,GAAGvK,OAAOL,GAAWK,OAAOyK,KAAKF,IACvCpO,EAAAoO,GAAK,GAAGvK,OAAOL,EAAW,SAASK,OAAOyK,KAAKF,IACrDxF,GAAS5I,EAAQkK,EAAQlK,MAAqBsN,GAAmB3F,MAAMC,QAAQsC,EAAQlK,OAASkK,EAAQlK,MAAM,GAAKkK,EAAQlK,OAA1FmE,GACxByE,GAAA7I,EAASmK,EAAQnK,OAAsBuN,GAAmBpD,EAAQnK,QAAxCoE,GACnCgF,GAAYrJ,EAAQqE,GAAa+F,EAAQpK,QACrCoK,EAAQxJ,UACHZ,EAAA+J,aAAa,WAAY,GAE9BK,EAAQrK,WACV+I,GAASkH,EAAQ,GAAGjM,OAAOL,EAAW,cACjC0G,EAAQ1J,QAA+B,WAArB0J,EAAQrK,UACrB+J,GAAA8J,EAAQ9M,GAAa,SAG7BG,GAASmD,EAAQhK,YAAcgK,EAAQhK,WAEzCgK,EAAQhK,UAAUwK,MAAM7D,IAAejJ,SAAQ,SAAUsC,GACvD0I,GAASkH,EAAQ5P,EAC3B,IAEUgK,EAAQjK,QAAS,CACf,IAAA+P,EAAO7M,SAASwJ,cAAc,MAC9BkT,EAASxY,GAAc6C,EAAQjK,SAC/B6f,EAAchZ,GAAQiZ,MAAM,EAAG,GAC/BC,EAAgBlZ,GAAQiZ,MAAM,EAAG,GACjCE,EAAenZ,GAAQiZ,MAAM,GAC5BF,GACHjX,GAAS3I,EAASqN,GAAmBpD,EAAQjK,UAE/CrC,GAAQiiB,EAAS3V,EAAQjK,QAAU6G,IAAS,SAAU9H,EAAOoR,GACvD,IAAA8P,EAAOL,GAAUxY,GAAcrI,GAC/ByK,EAAOoW,EAASvW,GAAU8G,GAASpR,EACnCiD,EAAOie,IAAShZ,GAAYlI,EAAMiD,MAAQjD,EAAMiD,KAAOjD,EACvD,GAACiD,IAASiI,EAAQ/I,WAA0C,IAA9B2e,EAAYnX,QAAQc,MAAiBS,EAAQjJ,YAAmD,IAAtC+e,EAAcrX,QAAQc,MAAiBS,EAAQhJ,WAA+C,IAAnC+e,EAAatX,QAAQc,IAAxK,CAGA,IAAA0W,EAAOD,IAAShZ,GAAYlI,EAAMmhB,MAAQnhB,EAAMmhB,KAAOnhB,EACvD4U,EAAQsM,IAAShZ,GAAYlI,EAAM4U,OAAS5U,EAAM4U,MAAQ5U,EAC1DyR,EAAOtN,SAASwJ,cAAc,MAC9BzC,EAAQxJ,UACL+P,EAAA5G,aAAa,WAAY,GAE3B4G,EAAA5G,aAAa,OAAQ,UACjBjB,GAAA6H,EAAM,GAAG5M,OAAOL,EAAW,KAAKK,OAAO4F,IAC3CjC,GAAWoM,IACNhK,GAAA6G,EAAM7J,GAAa6C,GAEzBxC,GAAShF,IACF2G,GAAA6H,EAAMnD,GAAmBrL,KAES,IAAzC,CAAC,QAAS,SAAS0G,QAAQwX,GACpBvX,GAAA6H,EAAM,GAAG5M,OAAOL,EAAW,KAAKK,OAAOsc,IAC9B,SAAT1W,GACTb,GAAS6H,EAAM,GAAG5M,OAAOL,EAAW,WAElCgE,GAAWoM,IACD/I,GAAA4F,EAAM3L,GAAa8O,GAEjC5D,EAAK3C,YAAYoD,EAvBhB,CAwBX,IACQxQ,EAAQoN,YAAY2C,EAC5B,MACQpH,GAAS3I,EAASkE,GAEhB,IAAC+F,EAAQjJ,UAAW,CAClB,IAAAmf,EAAUngB,EAAQ0c,iBAAiB,uBACvC/T,GAASwX,EAAS5b,GACV5G,GAAAwiB,GAAS,SAAU3d,GACzBxC,EAAQoN,YAAY5K,EAC9B,GACO,CACD,GAAIyH,EAAQ1J,OACVoI,GAAS9I,EAAQmE,GACjBiE,GAAS4H,EAAQ,CACfpO,OAAQwI,EAAQvI,eAE+B,WAA7CuB,OAAOiM,iBAAiBI,GAAQ8Q,UAClCnY,GAASqH,EAAQ,CACf8Q,SAAU,aAGP9Q,EAAA+Q,aAAaxQ,EAAQ3H,EAAQoY,iBAC/B,CACL3X,GAAS9I,EAAQgE,GACjB8E,GAASkH,EAAQ9L,GACjB4E,GAASkH,EAAQ/L,GACjB6E,GAASkH,EAAQ3L,GACjB+D,GAAS4H,EAAQ,CACfpO,OAAQwI,EAAQxI,SAElB,IAAIvB,EAAY+J,EAAQ/J,UACpB4G,GAAS5G,KACCA,EAAAgI,EAAQyG,cAAcsK,cAAc/Y,IAE7CA,IACHA,EAAYmO,KAAKxB,MAEnB3M,EAAUkN,YAAYyC,EACvB,CACG5F,EAAQ1J,SACV8N,KAAKD,SACLC,KAAKmF,OACLnF,KAAKoJ,SAAU,GAEjBpJ,KAAKtM,OAAQ,EACTwF,GAAW0C,EAAQlI,QACT6I,GAAA1C,EAASxC,GAAauE,EAAQlI,MAAO,CAC/CgI,MAAM,KAGkC,IAAxCkB,GAAc/C,EAASxC,IAIvB2I,KAAKtM,OAASkI,EAAQ1J,QACnB8N,KAAAjM,KAAKiM,KAAK8B,OAJf9B,KAAKtM,OAAQ,CAtId,CA4IF,MAhlG+B1D,EAAA8gB,EAAY/gB,UAAWohB,GACrDC,GAAaphB,EAAkB8gB,EAAaM,GACzC1iB,OAAAgB,eAAeohB,EAAa,YAAa,CAC9CxgB,UAAU,IAomGLqgB,CACT,CAjTsC,GAkTtCpX,GAAOoX,GAAO5gB,UAAWgQ,GAAQmF,GAAQwB,GAAUwD,GAAS0E,uzCCzkG5D,MAAAsD,EAAAC,IAEA,IAAAC,EAAAC,EAAA,CAA6B5e,IAAA,GACtBoe,KAAA,EACCnT,MAAA,EACCC,OAAA,EACC2T,WAAA,GACIC,QAAA,GACHC,QAAA,GACCC,WAAA,KAGZjR,EAAA6Q,EAAA,CAAA,GACAK,IAEA,MAAAC,EAAAC,UACEC,EAAAC,QAAA,2BAAA,KAAA,CAAuDC,kBAAA,KAClCC,iBAAA,KACD9W,KAAA,YACZ+W,MAAAL,UAGJ,MAAAtgB,EAAA4gB,EAAAC,QAAA,CAAkCC,MAAA,EAC1BC,KAAA,UACAC,WAAA,uBAIRtiB,QAAAuiB,EAAA,CAA8BpY,KAAAiX,EAAA1hB,MAAA8hB,SAAA,OAI9BxhB,GAAAmI,MAAAA,OACE7G,EAAA+W,QACAzU,OAAA0U,KAAAtY,GAAAmI,MAAAA,MAA2B,IAC7Bqa,OAAA,QAEW,EAKjBC,EAAApB,EAAA,IASAqB,EAAArB,EAAA,IAOAsB,EAAAtB,EAAA,IAQAuB,EAAA,CAAA1X,EAAA4D,KAEE,GADA+T,QAAAC,IAAA5X,EAAA4D,EAAA,MACA,QAAA5D,EACE,OAAAuX,EAAA/iB,MAAA5B,QAAAP,GAAAA,EAAAwlB,MAAAjU,IAAA,GAAA3E,KAAyD,GAAA,YAAAe,EAEzD,OAAAyX,EAAAjjB,MAAA5B,QAAAP,GAAAA,EAAAwlB,MAAAjU,IAAA,GAAA3E,KAA6D,CAE7D,MAAAhC,EAAAua,EAAAhjB,MAAA5B,QAAAP,GAAAA,EAAAwlB,MAAAjU,IAEA,OADA+T,QAAAC,IAAA3a,EAAA,MACAA,EAAA6a,KAAAzlB,GAAAA,EAAA4M,OAAAyC,KAAA,IAA4C,GAIhDqW,GAAArB,eApCAA,WACE,MAAAzZ,QAAA+a,EAAA,CAAA,GACAL,QAAAC,IAAA3a,EAAA,MACA,IAAAA,EAAAA,KAAAgb,OACEV,EAAA/iB,MAAAyI,EAAAA,KAAAA,KAA0B,EAiC5Bib,QA5BFxB,WACE,MAAAzZ,QAAAkb,EAAA,CAAA,GACA,IAAAlb,EAAAA,KAAAgb,OACET,EAAAhjB,MAAAyI,EAAAA,KAAAA,KAA2B,EA0B7Bmb,QAtBF1B,WACE,MAAAzZ,QAAAob,EAAA,CAAA,GACA,IAAApb,EAAAA,KAAAgb,OACER,EAAAjjB,MAAAyI,EAAAA,KAAAA,KAA8B,EAoBhCqb,GACA,MAAAC,MAAAA,GAAAvC,EACAE,EAAA1hB,MAAA,IAAa+jB,EACRlC,QAAAqB,EAAA,MAAAa,EAAAC,OAC2CjC,WAAAmB,EAAA,UAAAa,EAAAE,SACSnC,QAAAoB,EAAA,OAAAa,EAAAG,OAIzD,MAAAC,EAAAhgB,SAAAigB,eAAA,aAEAtT,EAAA9Q,MAAA,IAAAigB,GAAAkE,EAAA,CAAqCphB,IAAA,gBAC9BE,KAAA,WAGH6N,EAAAyM,QAAc,GAChB,IAQJ,MAAA8G,EAAA,KACE,IAAAthB,EAAA,4BAEAmB,OAAA0U,KAAA7V,GAAA2e,EAAA1hB,MAAA+C,IAAA,EAGFuhB,EAAAzgB,IACEiN,EAAA9Q,MAAAia,OAAA9H,SAAAtO,IACAiN,EAAA9Q,MAAAiD,u3CCzJF,MAAAshB,EAAAC,IACAC,IAEA9C,EAAA,GAEA,MAAA+C,EAAA/C,EAAA,CAAA,CAAA0B,IAAA,EAAA5Y,KAAA,iBAeA8Y,GAAA,KARArB,WACE,MAAAzZ,QAAA+a,EAAA,CAAA,GACAL,QAAAC,IAAA3a,EAAA,QACA,IAAAA,EAAAA,KAAAgb,OACEiB,EAAA1kB,MAAA,IAAA0kB,EAAA1kB,SAAAyI,EAAAA,KAAAA,MAAkE", "x_google_ignoreList": [0]}