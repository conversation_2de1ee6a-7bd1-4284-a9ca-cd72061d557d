{"version": 3, "file": "chunk.f8c4f303.js", "sources": ["../src/views/layouts/screenToCode/components/preview.vue", "../src/views/layouts/screenToCode/components/codePreview.vue", "../node_modules/codemirror/mode/xml/xml.js", "../node_modules/codemirror/mode/javascript/javascript.js", "../node_modules/codemirror/mode/htmlmixed/htmlmixed.js", "../src/views/layouts/screenToCode/components/codeMirror.vue", "../src/views/layouts/screenToCode/index.ts", "../src/views/layouts/screenToCode/index.vue"], "sourcesContent": ["<template>\n  <iframe title=\"预览\" :srcDoc=\"throttledCode\"></iframe>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, watch } from \"vue\";\nimport { throttle } from \"lodash\";\ninterface Props {\n  code: string;\n  appState: \"INITIAL\" | \"CODING\" | \"CODE_READY\";\n}\nconst props = defineProps<Props>();\nconst throttledCode = ref(\"\");\n\nconst throttled = throttle(\n  () => {\n    console.log(props.code);\n    throttledCode.value = props.code;\n  },\n  200,\n  { trailing: false }\n);\nwatch(\n  () => props.code,\n  (code) => {\n    throttled();\n  }\n);\n</script>\n<style lang=\"less\" scoped>\niframe {\n  width: 100%;\n  height: 100%;\n  border: none;\n}\n</style>\n", "<template>\n  <div ref=\"scrollRef\" class=\"code-review\">{{ code }}</div>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, watch } from \"vue\";\nimport { throttle } from \"lodash\";\ninterface Props {\n  code: string;\n}\nconst props = defineProps<Props>();\nconst scrollRef = ref();\n\nwatch(\n  () => props.code,\n  (code) => {\n    if (scrollRef.value) {\n      scrollRef.value.scrollTop = scrollRef.value.scrollHeight;\n    }\n  }\n);\n</script>\n<style lang=\"less\" scoped>\n.code-review {\n  width: 100%;\n  overflow-x: hidden;\n  background: #000;\n  color: #5fff5f;\n  font-size: 12px;\n  padding: 5px;\n  max-height: 200px;\n  overflow-y: overlay;\n  position: relative;\n  z-index: 2;\n}\n</style>\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nvar htmlConfig = {\n  autoSelfClosers: {'area': true, 'base': true, 'br': true, 'col': true, 'command': true,\n                    'embed': true, 'frame': true, 'hr': true, 'img': true, 'input': true,\n                    'keygen': true, 'link': true, 'meta': true, 'param': true, 'source': true,\n                    'track': true, 'wbr': true, 'menuitem': true},\n  implicitlyClosed: {'dd': true, 'li': true, 'optgroup': true, 'option': true, 'p': true,\n                     'rp': true, 'rt': true, 'tbody': true, 'td': true, 'tfoot': true,\n                     'th': true, 'tr': true},\n  contextGrabbers: {\n    'dd': {'dd': true, 'dt': true},\n    'dt': {'dd': true, 'dt': true},\n    'li': {'li': true},\n    'option': {'option': true, 'optgroup': true},\n    'optgroup': {'optgroup': true},\n    'p': {'address': true, 'article': true, 'aside': true, 'blockquote': true, 'dir': true,\n          'div': true, 'dl': true, 'fieldset': true, 'footer': true, 'form': true,\n          'h1': true, 'h2': true, 'h3': true, 'h4': true, 'h5': true, 'h6': true,\n          'header': true, 'hgroup': true, 'hr': true, 'menu': true, 'nav': true, 'ol': true,\n          'p': true, 'pre': true, 'section': true, 'table': true, 'ul': true},\n    'rp': {'rp': true, 'rt': true},\n    'rt': {'rp': true, 'rt': true},\n    'tbody': {'tbody': true, 'tfoot': true},\n    'td': {'td': true, 'th': true},\n    'tfoot': {'tbody': true},\n    'th': {'td': true, 'th': true},\n    'thead': {'tbody': true, 'tfoot': true},\n    'tr': {'tr': true}\n  },\n  doNotIndent: {\"pre\": true},\n  allowUnquoted: true,\n  allowMissing: true,\n  caseFold: true\n}\n\nvar xmlConfig = {\n  autoSelfClosers: {},\n  implicitlyClosed: {},\n  contextGrabbers: {},\n  doNotIndent: {},\n  allowUnquoted: false,\n  allowMissing: false,\n  allowMissingTagName: false,\n  caseFold: false\n}\n\nCodeMirror.defineMode(\"xml\", function(editorConf, config_) {\n  var indentUnit = editorConf.indentUnit\n  var config = {}\n  var defaults = config_.htmlMode ? htmlConfig : xmlConfig\n  for (var prop in defaults) config[prop] = defaults[prop]\n  for (var prop in config_) config[prop] = config_[prop]\n\n  // Return variables for tokenizers\n  var type, setStyle;\n\n  function inText(stream, state) {\n    function chain(parser) {\n      state.tokenize = parser;\n      return parser(stream, state);\n    }\n\n    var ch = stream.next();\n    if (ch == \"<\") {\n      if (stream.eat(\"!\")) {\n        if (stream.eat(\"[\")) {\n          if (stream.match(\"CDATA[\")) return chain(inBlock(\"atom\", \"]]>\"));\n          else return null;\n        } else if (stream.match(\"--\")) {\n          return chain(inBlock(\"comment\", \"-->\"));\n        } else if (stream.match(\"DOCTYPE\", true, true)) {\n          stream.eatWhile(/[\\w\\._\\-]/);\n          return chain(doctype(1));\n        } else {\n          return null;\n        }\n      } else if (stream.eat(\"?\")) {\n        stream.eatWhile(/[\\w\\._\\-]/);\n        state.tokenize = inBlock(\"meta\", \"?>\");\n        return \"meta\";\n      } else {\n        type = stream.eat(\"/\") ? \"closeTag\" : \"openTag\";\n        state.tokenize = inTag;\n        return \"tag bracket\";\n      }\n    } else if (ch == \"&\") {\n      var ok;\n      if (stream.eat(\"#\")) {\n        if (stream.eat(\"x\")) {\n          ok = stream.eatWhile(/[a-fA-F\\d]/) && stream.eat(\";\");\n        } else {\n          ok = stream.eatWhile(/[\\d]/) && stream.eat(\";\");\n        }\n      } else {\n        ok = stream.eatWhile(/[\\w\\.\\-:]/) && stream.eat(\";\");\n      }\n      return ok ? \"atom\" : \"error\";\n    } else {\n      stream.eatWhile(/[^&<]/);\n      return null;\n    }\n  }\n  inText.isInText = true;\n\n  function inTag(stream, state) {\n    var ch = stream.next();\n    if (ch == \">\" || (ch == \"/\" && stream.eat(\">\"))) {\n      state.tokenize = inText;\n      type = ch == \">\" ? \"endTag\" : \"selfcloseTag\";\n      return \"tag bracket\";\n    } else if (ch == \"=\") {\n      type = \"equals\";\n      return null;\n    } else if (ch == \"<\") {\n      state.tokenize = inText;\n      state.state = baseState;\n      state.tagName = state.tagStart = null;\n      var next = state.tokenize(stream, state);\n      return next ? next + \" tag error\" : \"tag error\";\n    } else if (/[\\'\\\"]/.test(ch)) {\n      state.tokenize = inAttribute(ch);\n      state.stringStartCol = stream.column();\n      return state.tokenize(stream, state);\n    } else {\n      stream.match(/^[^\\s\\u00a0=<>\\\"\\']*[^\\s\\u00a0=<>\\\"\\'\\/]/);\n      return \"word\";\n    }\n  }\n\n  function inAttribute(quote) {\n    var closure = function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.next() == quote) {\n          state.tokenize = inTag;\n          break;\n        }\n      }\n      return \"string\";\n    };\n    closure.isInAttribute = true;\n    return closure;\n  }\n\n  function inBlock(style, terminator) {\n    return function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.match(terminator)) {\n          state.tokenize = inText;\n          break;\n        }\n        stream.next();\n      }\n      return style;\n    }\n  }\n\n  function doctype(depth) {\n    return function(stream, state) {\n      var ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == \"<\") {\n          state.tokenize = doctype(depth + 1);\n          return state.tokenize(stream, state);\n        } else if (ch == \">\") {\n          if (depth == 1) {\n            state.tokenize = inText;\n            break;\n          } else {\n            state.tokenize = doctype(depth - 1);\n            return state.tokenize(stream, state);\n          }\n        }\n      }\n      return \"meta\";\n    };\n  }\n\n  function lower(tagName) {\n    return tagName && tagName.toLowerCase();\n  }\n\n  function Context(state, tagName, startOfLine) {\n    this.prev = state.context;\n    this.tagName = tagName || \"\";\n    this.indent = state.indented;\n    this.startOfLine = startOfLine;\n    if (config.doNotIndent.hasOwnProperty(tagName) || (state.context && state.context.noIndent))\n      this.noIndent = true;\n  }\n  function popContext(state) {\n    if (state.context) state.context = state.context.prev;\n  }\n  function maybePopContext(state, nextTagName) {\n    var parentTagName;\n    while (true) {\n      if (!state.context) {\n        return;\n      }\n      parentTagName = state.context.tagName;\n      if (!config.contextGrabbers.hasOwnProperty(lower(parentTagName)) ||\n          !config.contextGrabbers[lower(parentTagName)].hasOwnProperty(lower(nextTagName))) {\n        return;\n      }\n      popContext(state);\n    }\n  }\n\n  function baseState(type, stream, state) {\n    if (type == \"openTag\") {\n      state.tagStart = stream.column();\n      return tagNameState;\n    } else if (type == \"closeTag\") {\n      return closeTagNameState;\n    } else {\n      return baseState;\n    }\n  }\n  function tagNameState(type, stream, state) {\n    if (type == \"word\") {\n      state.tagName = stream.current();\n      setStyle = \"tag\";\n      return attrState;\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"tag bracket\";\n      return attrState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return tagNameState;\n    }\n  }\n  function closeTagNameState(type, stream, state) {\n    if (type == \"word\") {\n      var tagName = stream.current();\n      if (state.context && state.context.tagName != tagName &&\n          config.implicitlyClosed.hasOwnProperty(lower(state.context.tagName)))\n        popContext(state);\n      if ((state.context && state.context.tagName == tagName) || config.matchClosing === false) {\n        setStyle = \"tag\";\n        return closeState;\n      } else {\n        setStyle = \"tag error\";\n        return closeStateErr;\n      }\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"tag bracket\";\n      return closeState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return closeStateErr;\n    }\n  }\n\n  function closeState(type, _stream, state) {\n    if (type != \"endTag\") {\n      setStyle = \"error\";\n      return closeState;\n    }\n    popContext(state);\n    return baseState;\n  }\n  function closeStateErr(type, stream, state) {\n    setStyle = \"error\";\n    return closeState(type, stream, state);\n  }\n\n  function attrState(type, _stream, state) {\n    if (type == \"word\") {\n      setStyle = \"attribute\";\n      return attrEqState;\n    } else if (type == \"endTag\" || type == \"selfcloseTag\") {\n      var tagName = state.tagName, tagStart = state.tagStart;\n      state.tagName = state.tagStart = null;\n      if (type == \"selfcloseTag\" ||\n          config.autoSelfClosers.hasOwnProperty(lower(tagName))) {\n        maybePopContext(state, tagName);\n      } else {\n        maybePopContext(state, tagName);\n        state.context = new Context(state, tagName, tagStart == state.indented);\n      }\n      return baseState;\n    }\n    setStyle = \"error\";\n    return attrState;\n  }\n  function attrEqState(type, stream, state) {\n    if (type == \"equals\") return attrValueState;\n    if (!config.allowMissing) setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrValueState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    if (type == \"word\" && config.allowUnquoted) {setStyle = \"string\"; return attrState;}\n    setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrContinuedState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    return attrState(type, stream, state);\n  }\n\n  return {\n    startState: function(baseIndent) {\n      var state = {tokenize: inText,\n                   state: baseState,\n                   indented: baseIndent || 0,\n                   tagName: null, tagStart: null,\n                   context: null}\n      if (baseIndent != null) state.baseIndent = baseIndent\n      return state\n    },\n\n    token: function(stream, state) {\n      if (!state.tagName && stream.sol())\n        state.indented = stream.indentation();\n\n      if (stream.eatSpace()) return null;\n      type = null;\n      var style = state.tokenize(stream, state);\n      if ((style || type) && style != \"comment\") {\n        setStyle = null;\n        state.state = state.state(type || style, stream, state);\n        if (setStyle)\n          style = setStyle == \"error\" ? style + \" error\" : setStyle;\n      }\n      return style;\n    },\n\n    indent: function(state, textAfter, fullLine) {\n      var context = state.context;\n      // Indent multi-line strings (e.g. css).\n      if (state.tokenize.isInAttribute) {\n        if (state.tagStart == state.indented)\n          return state.stringStartCol + 1;\n        else\n          return state.indented + indentUnit;\n      }\n      if (context && context.noIndent) return CodeMirror.Pass;\n      if (state.tokenize != inTag && state.tokenize != inText)\n        return fullLine ? fullLine.match(/^(\\s*)/)[0].length : 0;\n      // Indent the starts of attribute names.\n      if (state.tagName) {\n        if (config.multilineTagIndentPastTag !== false)\n          return state.tagStart + state.tagName.length + 2;\n        else\n          return state.tagStart + indentUnit * (config.multilineTagIndentFactor || 1);\n      }\n      if (config.alignCDATA && /<!\\[CDATA\\[/.test(textAfter)) return 0;\n      var tagAfter = textAfter && /^<(\\/)?([\\w_:\\.-]*)/.exec(textAfter);\n      if (tagAfter && tagAfter[1]) { // Closing tag spotted\n        while (context) {\n          if (context.tagName == tagAfter[2]) {\n            context = context.prev;\n            break;\n          } else if (config.implicitlyClosed.hasOwnProperty(lower(context.tagName))) {\n            context = context.prev;\n          } else {\n            break;\n          }\n        }\n      } else if (tagAfter) { // Opening tag spotted\n        while (context) {\n          var grabbers = config.contextGrabbers[lower(context.tagName)];\n          if (grabbers && grabbers.hasOwnProperty(lower(tagAfter[2])))\n            context = context.prev;\n          else\n            break;\n        }\n      }\n      while (context && context.prev && !context.startOfLine)\n        context = context.prev;\n      if (context) return context.indent + indentUnit;\n      else return state.baseIndent || 0;\n    },\n\n    electricInput: /<\\/[\\s\\w:]+>$/,\n    blockCommentStart: \"<!--\",\n    blockCommentEnd: \"-->\",\n\n    configuration: config.htmlMode ? \"html\" : \"xml\",\n    helperType: config.htmlMode ? \"html\" : \"xml\",\n\n    skipAttribute: function(state) {\n      if (state.state == attrValueState)\n        state.state = attrState\n    },\n\n    xmlCurrentTag: function(state) {\n      return state.tagName ? {name: state.tagName, close: state.type == \"closeTag\"} : null\n    },\n\n    xmlCurrentContext: function(state) {\n      var context = []\n      for (var cx = state.context; cx; cx = cx.prev)\n        context.push(cx.tagName)\n      return context.reverse()\n    }\n  };\n});\n\nCodeMirror.defineMIME(\"text/xml\", \"xml\");\nCodeMirror.defineMIME(\"application/xml\", \"xml\");\nif (!CodeMirror.mimeModes.hasOwnProperty(\"text/html\"))\n  CodeMirror.defineMIME(\"text/html\", {name: \"xml\", htmlMode: true});\n\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode(\"javascript\", function(config, parserConfig) {\n  var indentUnit = config.indentUnit;\n  var statementIndent = parserConfig.statementIndent;\n  var jsonldMode = parserConfig.jsonld;\n  var jsonMode = parserConfig.json || jsonldMode;\n  var trackScope = parserConfig.trackScope !== false\n  var isTS = parserConfig.typescript;\n  var wordRE = parserConfig.wordCharacters || /[\\w$\\xa1-\\uffff]/;\n\n  // Tokenizer\n\n  var keywords = function(){\n    function kw(type) {return {type: type, style: \"keyword\"};}\n    var A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\"), D = kw(\"keyword d\");\n    var operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"};\n\n    return {\n      \"if\": kw(\"if\"), \"while\": A, \"with\": A, \"else\": B, \"do\": B, \"try\": B, \"finally\": B,\n      \"return\": D, \"break\": D, \"continue\": D, \"new\": kw(\"new\"), \"delete\": C, \"void\": C, \"throw\": C,\n      \"debugger\": kw(\"debugger\"), \"var\": kw(\"var\"), \"const\": kw(\"var\"), \"let\": kw(\"var\"),\n      \"function\": kw(\"function\"), \"catch\": kw(\"catch\"),\n      \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n      \"in\": operator, \"typeof\": operator, \"instanceof\": operator,\n      \"true\": atom, \"false\": atom, \"null\": atom, \"undefined\": atom, \"NaN\": atom, \"Infinity\": atom,\n      \"this\": kw(\"this\"), \"class\": kw(\"class\"), \"super\": kw(\"atom\"),\n      \"yield\": C, \"export\": kw(\"export\"), \"import\": kw(\"import\"), \"extends\": C,\n      \"await\": C\n    };\n  }();\n\n  var isOperatorChar = /[+\\-*&%=<>!?|~^@]/;\n  var isJsonldKeyword = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)\"/;\n\n  function readRegexp(stream) {\n    var escaped = false, next, inSet = false;\n    while ((next = stream.next()) != null) {\n      if (!escaped) {\n        if (next == \"/\" && !inSet) return;\n        if (next == \"[\") inSet = true;\n        else if (inSet && next == \"]\") inSet = false;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n  }\n\n  // Used as scratch variables to communicate multiple values without\n  // consing up tons of objects.\n  var type, content;\n  function ret(tp, style, cont) {\n    type = tp; content = cont;\n    return style;\n  }\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\" && stream.match(/^\\d[\\d_]*(?:[eE][+\\-]?[\\d_]+)?/)) {\n      return ret(\"number\", \"number\");\n    } else if (ch == \".\" && stream.match(\"..\")) {\n      return ret(\"spread\", \"meta\");\n    } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n      return ret(ch);\n    } else if (ch == \"=\" && stream.eat(\">\")) {\n      return ret(\"=>\", \"operator\");\n    } else if (ch == \"0\" && stream.match(/^(?:x[\\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/)) {\n      return ret(\"number\", \"number\");\n    } else if (/\\d/.test(ch)) {\n      stream.match(/^[\\d_]*(?:n|(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)?/);\n      return ret(\"number\", \"number\");\n    } else if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      } else if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return ret(\"comment\", \"comment\");\n      } else if (expressionAllowed(stream, state, 1)) {\n        readRegexp(stream);\n        stream.match(/^\\b(([gimyus])(?![gimyus]*\\2))+\\b/);\n        return ret(\"regexp\", \"string-2\");\n      } else {\n        stream.eat(\"=\");\n        return ret(\"operator\", \"operator\", stream.current());\n      }\n    } else if (ch == \"`\") {\n      state.tokenize = tokenQuasi;\n      return tokenQuasi(stream, state);\n    } else if (ch == \"#\" && stream.peek() == \"!\") {\n      stream.skipToEnd();\n      return ret(\"meta\", \"meta\");\n    } else if (ch == \"#\" && stream.eatWhile(wordRE)) {\n      return ret(\"variable\", \"property\")\n    } else if (ch == \"<\" && stream.match(\"!--\") ||\n               (ch == \"-\" && stream.match(\"->\") && !/\\S/.test(stream.string.slice(0, stream.start)))) {\n      stream.skipToEnd()\n      return ret(\"comment\", \"comment\")\n    } else if (isOperatorChar.test(ch)) {\n      if (ch != \">\" || !state.lexical || state.lexical.type != \">\") {\n        if (stream.eat(\"=\")) {\n          if (ch == \"!\" || ch == \"=\") stream.eat(\"=\")\n        } else if (/[<>*+\\-|&?]/.test(ch)) {\n          stream.eat(ch)\n          if (ch == \">\") stream.eat(ch)\n        }\n      }\n      if (ch == \"?\" && stream.eat(\".\")) return ret(\".\")\n      return ret(\"operator\", \"operator\", stream.current());\n    } else if (wordRE.test(ch)) {\n      stream.eatWhile(wordRE);\n      var word = stream.current()\n      if (state.lastType != \".\") {\n        if (keywords.propertyIsEnumerable(word)) {\n          var kw = keywords[word]\n          return ret(kw.type, kw.style, word)\n        }\n        if (word == \"async\" && stream.match(/^(\\s|\\/\\*([^*]|\\*(?!\\/))*?\\*\\/)*[\\[\\(\\w]/, false))\n          return ret(\"async\", \"keyword\", word)\n      }\n      return ret(\"variable\", \"variable\", word)\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next;\n      if (jsonldMode && stream.peek() == \"@\" && stream.match(isJsonldKeyword)){\n        state.tokenize = tokenBase;\n        return ret(\"jsonld-keyword\", \"meta\");\n      }\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) break;\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (!escaped) state.tokenize = tokenBase;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return ret(\"comment\", \"comment\");\n  }\n\n  function tokenQuasi(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (!escaped && (next == \"`\" || next == \"$\" && stream.eat(\"{\"))) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    return ret(\"quasi\", \"string-2\", stream.current());\n  }\n\n  var brackets = \"([{}])\";\n  // This is a crude lookahead trick to try and notice that we're\n  // parsing the argument patterns for a fat-arrow function before we\n  // actually hit the arrow token. It only works if the arrow is on\n  // the same line as the arguments and there's no strange noise\n  // (comments) in between. Fallback is to only notice when we hit the\n  // arrow, and not declare the arguments as locals for the arrow\n  // body.\n  function findFatArrow(stream, state) {\n    if (state.fatArrowAt) state.fatArrowAt = null;\n    var arrow = stream.string.indexOf(\"=>\", stream.start);\n    if (arrow < 0) return;\n\n    if (isTS) { // Try to skip TypeScript return type declarations after the arguments\n      var m = /:\\s*(?:\\w+(?:<[^>]*>|\\[\\])?|\\{[^}]*\\})\\s*$/.exec(stream.string.slice(stream.start, arrow))\n      if (m) arrow = m.index\n    }\n\n    var depth = 0, sawSomething = false;\n    for (var pos = arrow - 1; pos >= 0; --pos) {\n      var ch = stream.string.charAt(pos);\n      var bracket = brackets.indexOf(ch);\n      if (bracket >= 0 && bracket < 3) {\n        if (!depth) { ++pos; break; }\n        if (--depth == 0) { if (ch == \"(\") sawSomething = true; break; }\n      } else if (bracket >= 3 && bracket < 6) {\n        ++depth;\n      } else if (wordRE.test(ch)) {\n        sawSomething = true;\n      } else if (/[\"'\\/`]/.test(ch)) {\n        for (;; --pos) {\n          if (pos == 0) return\n          var next = stream.string.charAt(pos - 1)\n          if (next == ch && stream.string.charAt(pos - 2) != \"\\\\\") { pos--; break }\n        }\n      } else if (sawSomething && !depth) {\n        ++pos;\n        break;\n      }\n    }\n    if (sawSomething && !depth) state.fatArrowAt = pos;\n  }\n\n  // Parser\n\n  var atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true,\n                     \"regexp\": true, \"this\": true, \"import\": true, \"jsonld-keyword\": true};\n\n  function JSLexical(indented, column, type, align, prev, info) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.prev = prev;\n    this.info = info;\n    if (align != null) this.align = align;\n  }\n\n  function inScope(state, varname) {\n    if (!trackScope) return false\n    for (var v = state.localVars; v; v = v.next)\n      if (v.name == varname) return true;\n    for (var cx = state.context; cx; cx = cx.prev) {\n      for (var v = cx.vars; v; v = v.next)\n        if (v.name == varname) return true;\n    }\n  }\n\n  function parseJS(state, style, type, content, stream) {\n    var cc = state.cc;\n    // Communicate our context to the combinators.\n    // (Less wasteful than consing up a hundred closures on every call.)\n    cx.state = state; cx.stream = stream; cx.marked = null, cx.cc = cc; cx.style = style;\n\n    if (!state.lexical.hasOwnProperty(\"align\"))\n      state.lexical.align = true;\n\n    while(true) {\n      var combinator = cc.length ? cc.pop() : jsonMode ? expression : statement;\n      if (combinator(type, content)) {\n        while(cc.length && cc[cc.length - 1].lex)\n          cc.pop()();\n        if (cx.marked) return cx.marked;\n        if (type == \"variable\" && inScope(state, content)) return \"variable-2\";\n        return style;\n      }\n    }\n  }\n\n  // Combinator utils\n\n  var cx = {state: null, column: null, marked: null, cc: null};\n  function pass() {\n    for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n  }\n  function cont() {\n    pass.apply(null, arguments);\n    return true;\n  }\n  function inList(name, list) {\n    for (var v = list; v; v = v.next) if (v.name == name) return true\n    return false;\n  }\n  function register(varname) {\n    var state = cx.state;\n    cx.marked = \"def\";\n    if (!trackScope) return\n    if (state.context) {\n      if (state.lexical.info == \"var\" && state.context && state.context.block) {\n        // FIXME function decls are also not block scoped\n        var newContext = registerVarScoped(varname, state.context)\n        if (newContext != null) {\n          state.context = newContext\n          return\n        }\n      } else if (!inList(varname, state.localVars)) {\n        state.localVars = new Var(varname, state.localVars)\n        return\n      }\n    }\n    // Fall through means this is global\n    if (parserConfig.globalVars && !inList(varname, state.globalVars))\n      state.globalVars = new Var(varname, state.globalVars)\n  }\n  function registerVarScoped(varname, context) {\n    if (!context) {\n      return null\n    } else if (context.block) {\n      var inner = registerVarScoped(varname, context.prev)\n      if (!inner) return null\n      if (inner == context.prev) return context\n      return new Context(inner, context.vars, true)\n    } else if (inList(varname, context.vars)) {\n      return context\n    } else {\n      return new Context(context.prev, new Var(varname, context.vars), false)\n    }\n  }\n\n  function isModifier(name) {\n    return name == \"public\" || name == \"private\" || name == \"protected\" || name == \"abstract\" || name == \"readonly\"\n  }\n\n  // Combinators\n\n  function Context(prev, vars, block) { this.prev = prev; this.vars = vars; this.block = block }\n  function Var(name, next) { this.name = name; this.next = next }\n\n  var defaultVars = new Var(\"this\", new Var(\"arguments\", null))\n  function pushcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, false)\n    cx.state.localVars = defaultVars\n  }\n  function pushblockcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, true)\n    cx.state.localVars = null\n  }\n  pushcontext.lex = pushblockcontext.lex = true\n  function popcontext() {\n    cx.state.localVars = cx.state.context.vars\n    cx.state.context = cx.state.context.prev\n  }\n  popcontext.lex = true\n  function pushlex(type, info) {\n    var result = function() {\n      var state = cx.state, indent = state.indented;\n      if (state.lexical.type == \"stat\") indent = state.lexical.indented;\n      else for (var outer = state.lexical; outer && outer.type == \")\" && outer.align; outer = outer.prev)\n        indent = outer.indented;\n      state.lexical = new JSLexical(indent, cx.stream.column(), type, null, state.lexical, info);\n    };\n    result.lex = true;\n    return result;\n  }\n  function poplex() {\n    var state = cx.state;\n    if (state.lexical.prev) {\n      if (state.lexical.type == \")\")\n        state.indented = state.lexical.indented;\n      state.lexical = state.lexical.prev;\n    }\n  }\n  poplex.lex = true;\n\n  function expect(wanted) {\n    function exp(type) {\n      if (type == wanted) return cont();\n      else if (wanted == \";\" || type == \"}\" || type == \")\" || type == \"]\") return pass();\n      else return cont(exp);\n    };\n    return exp;\n  }\n\n  function statement(type, value) {\n    if (type == \"var\") return cont(pushlex(\"vardef\", value), vardef, expect(\";\"), poplex);\n    if (type == \"keyword a\") return cont(pushlex(\"form\"), parenExpr, statement, poplex);\n    if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n    if (type == \"keyword d\") return cx.stream.match(/^\\s*$/, false) ? cont() : cont(pushlex(\"stat\"), maybeexpression, expect(\";\"), poplex);\n    if (type == \"debugger\") return cont(expect(\";\"));\n    if (type == \"{\") return cont(pushlex(\"}\"), pushblockcontext, block, poplex, popcontext);\n    if (type == \";\") return cont();\n    if (type == \"if\") {\n      if (cx.state.lexical.info == \"else\" && cx.state.cc[cx.state.cc.length - 1] == poplex)\n        cx.state.cc.pop()();\n      return cont(pushlex(\"form\"), parenExpr, statement, poplex, maybeelse);\n    }\n    if (type == \"function\") return cont(functiondef);\n    if (type == \"for\") return cont(pushlex(\"form\"), pushblockcontext, forspec, statement, popcontext, poplex);\n    if (type == \"class\" || (isTS && value == \"interface\")) {\n      cx.marked = \"keyword\"\n      return cont(pushlex(\"form\", type == \"class\" ? type : value), className, poplex)\n    }\n    if (type == \"variable\") {\n      if (isTS && value == \"declare\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else if (isTS && (value == \"module\" || value == \"enum\" || value == \"type\") && cx.stream.match(/^\\s*\\w/, false)) {\n        cx.marked = \"keyword\"\n        if (value == \"enum\") return cont(enumdef);\n        else if (value == \"type\") return cont(typename, expect(\"operator\"), typeexpr, expect(\";\"));\n        else return cont(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), block, poplex, poplex)\n      } else if (isTS && value == \"namespace\") {\n        cx.marked = \"keyword\"\n        return cont(pushlex(\"form\"), expression, statement, poplex)\n      } else if (isTS && value == \"abstract\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else {\n        return cont(pushlex(\"stat\"), maybelabel);\n      }\n    }\n    if (type == \"switch\") return cont(pushlex(\"form\"), parenExpr, expect(\"{\"), pushlex(\"}\", \"switch\"), pushblockcontext,\n                                      block, poplex, poplex, popcontext);\n    if (type == \"case\") return cont(expression, expect(\":\"));\n    if (type == \"default\") return cont(expect(\":\"));\n    if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, maybeCatchBinding, statement, poplex, popcontext);\n    if (type == \"export\") return cont(pushlex(\"stat\"), afterExport, poplex);\n    if (type == \"import\") return cont(pushlex(\"stat\"), afterImport, poplex);\n    if (type == \"async\") return cont(statement)\n    if (value == \"@\") return cont(expression, statement)\n    return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n  }\n  function maybeCatchBinding(type) {\n    if (type == \"(\") return cont(funarg, expect(\")\"))\n  }\n  function expression(type, value) {\n    return expressionInner(type, value, false);\n  }\n  function expressionNoComma(type, value) {\n    return expressionInner(type, value, true);\n  }\n  function parenExpr(type) {\n    if (type != \"(\") return pass()\n    return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex)\n  }\n  function expressionInner(type, value, noComma) {\n    if (cx.state.fatArrowAt == cx.stream.start) {\n      var body = noComma ? arrowBodyNoComma : arrowBody;\n      if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, expect(\"=>\"), body, popcontext);\n      else if (type == \"variable\") return pass(pushcontext, pattern, expect(\"=>\"), body, popcontext);\n    }\n\n    var maybeop = noComma ? maybeoperatorNoComma : maybeoperatorComma;\n    if (atomicTypes.hasOwnProperty(type)) return cont(maybeop);\n    if (type == \"function\") return cont(functiondef, maybeop);\n    if (type == \"class\" || (isTS && value == \"interface\")) { cx.marked = \"keyword\"; return cont(pushlex(\"form\"), classExpression, poplex); }\n    if (type == \"keyword c\" || type == \"async\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeop);\n    if (type == \"operator\" || type == \"spread\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"[\") return cont(pushlex(\"]\"), arrayLiteral, poplex, maybeop);\n    if (type == \"{\") return contCommasep(objprop, \"}\", null, maybeop);\n    if (type == \"quasi\") return pass(quasi, maybeop);\n    if (type == \"new\") return cont(maybeTarget(noComma));\n    return cont();\n  }\n  function maybeexpression(type) {\n    if (type.match(/[;\\}\\)\\],]/)) return pass();\n    return pass(expression);\n  }\n\n  function maybeoperatorComma(type, value) {\n    if (type == \",\") return cont(maybeexpression);\n    return maybeoperatorNoComma(type, value, false);\n  }\n  function maybeoperatorNoComma(type, value, noComma) {\n    var me = noComma == false ? maybeoperatorComma : maybeoperatorNoComma;\n    var expr = noComma == false ? expression : expressionNoComma;\n    if (type == \"=>\") return cont(pushcontext, noComma ? arrowBodyNoComma : arrowBody, popcontext);\n    if (type == \"operator\") {\n      if (/\\+\\+|--/.test(value) || isTS && value == \"!\") return cont(me);\n      if (isTS && value == \"<\" && cx.stream.match(/^([^<>]|<[^<>]*>)*>\\s*\\(/, false))\n        return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, me);\n      if (value == \"?\") return cont(expression, expect(\":\"), expr);\n      return cont(expr);\n    }\n    if (type == \"quasi\") { return pass(quasi, me); }\n    if (type == \";\") return;\n    if (type == \"(\") return contCommasep(expressionNoComma, \")\", \"call\", me);\n    if (type == \".\") return cont(property, me);\n    if (type == \"[\") return cont(pushlex(\"]\"), maybeexpression, expect(\"]\"), poplex, me);\n    if (isTS && value == \"as\") { cx.marked = \"keyword\"; return cont(typeexpr, me) }\n    if (type == \"regexp\") {\n      cx.state.lastType = cx.marked = \"operator\"\n      cx.stream.backUp(cx.stream.pos - cx.stream.start - 1)\n      return cont(expr)\n    }\n  }\n  function quasi(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasi);\n    return cont(maybeexpression, continueQuasi);\n  }\n  function continueQuasi(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasi);\n    }\n  }\n  function arrowBody(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expression);\n  }\n  function arrowBodyNoComma(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expressionNoComma);\n  }\n  function maybeTarget(noComma) {\n    return function(type) {\n      if (type == \".\") return cont(noComma ? targetNoComma : target);\n      else if (type == \"variable\" && isTS) return cont(maybeTypeArgs, noComma ? maybeoperatorNoComma : maybeoperatorComma)\n      else return pass(noComma ? expressionNoComma : expression);\n    };\n  }\n  function target(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorComma); }\n  }\n  function targetNoComma(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorNoComma); }\n  }\n  function maybelabel(type) {\n    if (type == \":\") return cont(poplex, statement);\n    return pass(maybeoperatorComma, expect(\";\"), poplex);\n  }\n  function property(type) {\n    if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n  }\n  function objprop(type, value) {\n    if (type == \"async\") {\n      cx.marked = \"property\";\n      return cont(objprop);\n    } else if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      if (value == \"get\" || value == \"set\") return cont(getterSetter);\n      var m // Work around fat-arrow-detection complication for detecting typescript typed arrow params\n      if (isTS && cx.state.fatArrowAt == cx.stream.start && (m = cx.stream.match(/^\\s*:\\s*/, false)))\n        cx.state.fatArrowAt = cx.stream.pos + m[0].length\n      return cont(afterprop);\n    } else if (type == \"number\" || type == \"string\") {\n      cx.marked = jsonldMode ? \"property\" : (cx.style + \" property\");\n      return cont(afterprop);\n    } else if (type == \"jsonld-keyword\") {\n      return cont(afterprop);\n    } else if (isTS && isModifier(value)) {\n      cx.marked = \"keyword\"\n      return cont(objprop)\n    } else if (type == \"[\") {\n      return cont(expression, maybetype, expect(\"]\"), afterprop);\n    } else if (type == \"spread\") {\n      return cont(expressionNoComma, afterprop);\n    } else if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(objprop);\n    } else if (type == \":\") {\n      return pass(afterprop)\n    }\n  }\n  function getterSetter(type) {\n    if (type != \"variable\") return pass(afterprop);\n    cx.marked = \"property\";\n    return cont(functiondef);\n  }\n  function afterprop(type) {\n    if (type == \":\") return cont(expressionNoComma);\n    if (type == \"(\") return pass(functiondef);\n  }\n  function commasep(what, end, sep) {\n    function proceed(type, value) {\n      if (sep ? sep.indexOf(type) > -1 : type == \",\") {\n        var lex = cx.state.lexical;\n        if (lex.info == \"call\") lex.pos = (lex.pos || 0) + 1;\n        return cont(function(type, value) {\n          if (type == end || value == end) return pass()\n          return pass(what)\n        }, proceed);\n      }\n      if (type == end || value == end) return cont();\n      if (sep && sep.indexOf(\";\") > -1) return pass(what)\n      return cont(expect(end));\n    }\n    return function(type, value) {\n      if (type == end || value == end) return cont();\n      return pass(what, proceed);\n    };\n  }\n  function contCommasep(what, end, info) {\n    for (var i = 3; i < arguments.length; i++)\n      cx.cc.push(arguments[i]);\n    return cont(pushlex(end, info), commasep(what, end), poplex);\n  }\n  function block(type) {\n    if (type == \"}\") return cont();\n    return pass(statement, block);\n  }\n  function maybetype(type, value) {\n    if (isTS) {\n      if (type == \":\") return cont(typeexpr);\n      if (value == \"?\") return cont(maybetype);\n    }\n  }\n  function maybetypeOrIn(type, value) {\n    if (isTS && (type == \":\" || value == \"in\")) return cont(typeexpr)\n  }\n  function mayberettype(type) {\n    if (isTS && type == \":\") {\n      if (cx.stream.match(/^\\s*\\w+\\s+is\\b/, false)) return cont(expression, isKW, typeexpr)\n      else return cont(typeexpr)\n    }\n  }\n  function isKW(_, value) {\n    if (value == \"is\") {\n      cx.marked = \"keyword\"\n      return cont()\n    }\n  }\n  function typeexpr(type, value) {\n    if (value == \"keyof\" || value == \"typeof\" || value == \"infer\" || value == \"readonly\") {\n      cx.marked = \"keyword\"\n      return cont(value == \"typeof\" ? expressionNoComma : typeexpr)\n    }\n    if (type == \"variable\" || value == \"void\") {\n      cx.marked = \"type\"\n      return cont(afterType)\n    }\n    if (value == \"|\" || value == \"&\") return cont(typeexpr)\n    if (type == \"string\" || type == \"number\" || type == \"atom\") return cont(afterType);\n    if (type == \"[\") return cont(pushlex(\"]\"), commasep(typeexpr, \"]\", \",\"), poplex, afterType)\n    if (type == \"{\") return cont(pushlex(\"}\"), typeprops, poplex, afterType)\n    if (type == \"(\") return cont(commasep(typearg, \")\"), maybeReturnType, afterType)\n    if (type == \"<\") return cont(commasep(typeexpr, \">\"), typeexpr)\n    if (type == \"quasi\") { return pass(quasiType, afterType); }\n  }\n  function maybeReturnType(type) {\n    if (type == \"=>\") return cont(typeexpr)\n  }\n  function typeprops(type) {\n    if (type.match(/[\\}\\)\\]]/)) return cont()\n    if (type == \",\" || type == \";\") return cont(typeprops)\n    return pass(typeprop, typeprops)\n  }\n  function typeprop(type, value) {\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\"\n      return cont(typeprop)\n    } else if (value == \"?\" || type == \"number\" || type == \"string\") {\n      return cont(typeprop)\n    } else if (type == \":\") {\n      return cont(typeexpr)\n    } else if (type == \"[\") {\n      return cont(expect(\"variable\"), maybetypeOrIn, expect(\"]\"), typeprop)\n    } else if (type == \"(\") {\n      return pass(functiondecl, typeprop)\n    } else if (!type.match(/[;\\}\\)\\],]/)) {\n      return cont()\n    }\n  }\n  function quasiType(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasiType);\n    return cont(typeexpr, continueQuasiType);\n  }\n  function continueQuasiType(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasiType);\n    }\n  }\n  function typearg(type, value) {\n    if (type == \"variable\" && cx.stream.match(/^\\s*[?:]/, false) || value == \"?\") return cont(typearg)\n    if (type == \":\") return cont(typeexpr)\n    if (type == \"spread\") return cont(typearg)\n    return pass(typeexpr)\n  }\n  function afterType(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n    if (value == \"|\" || type == \".\" || value == \"&\") return cont(typeexpr)\n    if (type == \"[\") return cont(typeexpr, expect(\"]\"), afterType)\n    if (value == \"extends\" || value == \"implements\") { cx.marked = \"keyword\"; return cont(typeexpr) }\n    if (value == \"?\") return cont(typeexpr, expect(\":\"), typeexpr)\n  }\n  function maybeTypeArgs(_, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n  }\n  function typeparam() {\n    return pass(typeexpr, maybeTypeDefault)\n  }\n  function maybeTypeDefault(_, value) {\n    if (value == \"=\") return cont(typeexpr)\n  }\n  function vardef(_, value) {\n    if (value == \"enum\") {cx.marked = \"keyword\"; return cont(enumdef)}\n    return pass(pattern, maybetype, maybeAssign, vardefCont);\n  }\n  function pattern(type, value) {\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(pattern) }\n    if (type == \"variable\") { register(value); return cont(); }\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"[\") return contCommasep(eltpattern, \"]\");\n    if (type == \"{\") return contCommasep(proppattern, \"}\");\n  }\n  function proppattern(type, value) {\n    if (type == \"variable\" && !cx.stream.match(/^\\s*:/, false)) {\n      register(value);\n      return cont(maybeAssign);\n    }\n    if (type == \"variable\") cx.marked = \"property\";\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"}\") return pass();\n    if (type == \"[\") return cont(expression, expect(']'), expect(':'), proppattern);\n    return cont(expect(\":\"), pattern, maybeAssign);\n  }\n  function eltpattern() {\n    return pass(pattern, maybeAssign)\n  }\n  function maybeAssign(_type, value) {\n    if (value == \"=\") return cont(expressionNoComma);\n  }\n  function vardefCont(type) {\n    if (type == \",\") return cont(vardef);\n  }\n  function maybeelse(type, value) {\n    if (type == \"keyword b\" && value == \"else\") return cont(pushlex(\"form\", \"else\"), statement, poplex);\n  }\n  function forspec(type, value) {\n    if (value == \"await\") return cont(forspec);\n    if (type == \"(\") return cont(pushlex(\")\"), forspec1, poplex);\n  }\n  function forspec1(type) {\n    if (type == \"var\") return cont(vardef, forspec2);\n    if (type == \"variable\") return cont(forspec2);\n    return pass(forspec2)\n  }\n  function forspec2(type, value) {\n    if (type == \")\") return cont()\n    if (type == \";\") return cont(forspec2)\n    if (value == \"in\" || value == \"of\") { cx.marked = \"keyword\"; return cont(expression, forspec2) }\n    return pass(expression, forspec2)\n  }\n  function functiondef(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondef);}\n    if (type == \"variable\") {register(value); return cont(functiondef);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, statement, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondef)\n  }\n  function functiondecl(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondecl);}\n    if (type == \"variable\") {register(value); return cont(functiondecl);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondecl)\n  }\n  function typename(type, value) {\n    if (type == \"keyword\" || type == \"variable\") {\n      cx.marked = \"type\"\n      return cont(typename)\n    } else if (value == \"<\") {\n      return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex)\n    }\n  }\n  function funarg(type, value) {\n    if (value == \"@\") cont(expression, funarg)\n    if (type == \"spread\") return cont(funarg);\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(funarg); }\n    if (isTS && type == \"this\") return cont(maybetype, maybeAssign)\n    return pass(pattern, maybetype, maybeAssign);\n  }\n  function classExpression(type, value) {\n    // Class expressions may have an optional name.\n    if (type == \"variable\") return className(type, value);\n    return classNameAfter(type, value);\n  }\n  function className(type, value) {\n    if (type == \"variable\") {register(value); return cont(classNameAfter);}\n  }\n  function classNameAfter(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, classNameAfter)\n    if (value == \"extends\" || value == \"implements\" || (isTS && type == \",\")) {\n      if (value == \"implements\") cx.marked = \"keyword\";\n      return cont(isTS ? typeexpr : expression, classNameAfter);\n    }\n    if (type == \"{\") return cont(pushlex(\"}\"), classBody, poplex);\n  }\n  function classBody(type, value) {\n    if (type == \"async\" ||\n        (type == \"variable\" &&\n         (value == \"static\" || value == \"get\" || value == \"set\" || (isTS && isModifier(value))) &&\n         cx.stream.match(/^\\s+#?[\\w$\\xa1-\\uffff]/, false))) {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      return cont(classfield, classBody);\n    }\n    if (type == \"number\" || type == \"string\") return cont(classfield, classBody);\n    if (type == \"[\")\n      return cont(expression, maybetype, expect(\"]\"), classfield, classBody)\n    if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (isTS && type == \"(\") return pass(functiondecl, classBody)\n    if (type == \";\" || type == \",\") return cont(classBody);\n    if (type == \"}\") return cont();\n    if (value == \"@\") return cont(expression, classBody)\n  }\n  function classfield(type, value) {\n    if (value == \"!\") return cont(classfield)\n    if (value == \"?\") return cont(classfield)\n    if (type == \":\") return cont(typeexpr, maybeAssign)\n    if (value == \"=\") return cont(expressionNoComma)\n    var context = cx.state.lexical.prev, isInterface = context && context.info == \"interface\"\n    return pass(isInterface ? functiondecl : functiondef)\n  }\n  function afterExport(type, value) {\n    if (value == \"*\") { cx.marked = \"keyword\"; return cont(maybeFrom, expect(\";\")); }\n    if (value == \"default\") { cx.marked = \"keyword\"; return cont(expression, expect(\";\")); }\n    if (type == \"{\") return cont(commasep(exportField, \"}\"), maybeFrom, expect(\";\"));\n    return pass(statement);\n  }\n  function exportField(type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(expect(\"variable\")); }\n    if (type == \"variable\") return pass(expressionNoComma, exportField);\n  }\n  function afterImport(type) {\n    if (type == \"string\") return cont();\n    if (type == \"(\") return pass(expression);\n    if (type == \".\") return pass(maybeoperatorComma);\n    return pass(importSpec, maybeMoreImports, maybeFrom);\n  }\n  function importSpec(type, value) {\n    if (type == \"{\") return contCommasep(importSpec, \"}\");\n    if (type == \"variable\") register(value);\n    if (value == \"*\") cx.marked = \"keyword\";\n    return cont(maybeAs);\n  }\n  function maybeMoreImports(type) {\n    if (type == \",\") return cont(importSpec, maybeMoreImports)\n  }\n  function maybeAs(_type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(importSpec); }\n  }\n  function maybeFrom(_type, value) {\n    if (value == \"from\") { cx.marked = \"keyword\"; return cont(expression); }\n  }\n  function arrayLiteral(type) {\n    if (type == \"]\") return cont();\n    return pass(commasep(expressionNoComma, \"]\"));\n  }\n  function enumdef() {\n    return pass(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), commasep(enummember, \"}\"), poplex, poplex)\n  }\n  function enummember() {\n    return pass(pattern, maybeAssign);\n  }\n\n  function isContinuedStatement(state, textAfter) {\n    return state.lastType == \"operator\" || state.lastType == \",\" ||\n      isOperatorChar.test(textAfter.charAt(0)) ||\n      /[,.]/.test(textAfter.charAt(0));\n  }\n\n  function expressionAllowed(stream, state, backUp) {\n    return state.tokenize == tokenBase &&\n      /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\\[{}\\(,;:]|=>)$/.test(state.lastType) ||\n      (state.lastType == \"quasi\" && /\\{\\s*$/.test(stream.string.slice(0, stream.pos - (backUp || 0))))\n  }\n\n  // Interface\n\n  return {\n    startState: function(basecolumn) {\n      var state = {\n        tokenize: tokenBase,\n        lastType: \"sof\",\n        cc: [],\n        lexical: new JSLexical((basecolumn || 0) - indentUnit, 0, \"block\", false),\n        localVars: parserConfig.localVars,\n        context: parserConfig.localVars && new Context(null, null, false),\n        indented: basecolumn || 0\n      };\n      if (parserConfig.globalVars && typeof parserConfig.globalVars == \"object\")\n        state.globalVars = parserConfig.globalVars;\n      return state;\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (!state.lexical.hasOwnProperty(\"align\"))\n          state.lexical.align = false;\n        state.indented = stream.indentation();\n        findFatArrow(stream, state);\n      }\n      if (state.tokenize != tokenComment && stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (type == \"comment\") return style;\n      state.lastType = type == \"operator\" && (content == \"++\" || content == \"--\") ? \"incdec\" : type;\n      return parseJS(state, style, type, content, stream);\n    },\n\n    indent: function(state, textAfter) {\n      if (state.tokenize == tokenComment || state.tokenize == tokenQuasi) return CodeMirror.Pass;\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical, top\n      // Kludge to prevent 'maybelse' from blocking lexical scope pops\n      if (!/^\\s*else\\b/.test(textAfter)) for (var i = state.cc.length - 1; i >= 0; --i) {\n        var c = state.cc[i];\n        if (c == poplex) lexical = lexical.prev;\n        else if (c != maybeelse && c != popcontext) break;\n      }\n      while ((lexical.type == \"stat\" || lexical.type == \"form\") &&\n             (firstChar == \"}\" || ((top = state.cc[state.cc.length - 1]) &&\n                                   (top == maybeoperatorComma || top == maybeoperatorNoComma) &&\n                                   !/^[,\\.=+\\-*:?[\\(]/.test(textAfter))))\n        lexical = lexical.prev;\n      if (statementIndent && lexical.type == \")\" && lexical.prev.type == \"stat\")\n        lexical = lexical.prev;\n      var type = lexical.type, closing = firstChar == type;\n\n      if (type == \"vardef\") return lexical.indented + (state.lastType == \"operator\" || state.lastType == \",\" ? lexical.info.length + 1 : 0);\n      else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n      else if (type == \"form\") return lexical.indented + indentUnit;\n      else if (type == \"stat\")\n        return lexical.indented + (isContinuedStatement(state, textAfter) ? statementIndent || indentUnit : 0);\n      else if (lexical.info == \"switch\" && !closing && parserConfig.doubleIndentSwitch != false)\n        return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? indentUnit : 2 * indentUnit);\n      else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n      else return lexical.indented + (closing ? 0 : indentUnit);\n    },\n\n    electricInput: /^\\s*(?:case .*?:|default:|\\{|\\})$/,\n    blockCommentStart: jsonMode ? null : \"/*\",\n    blockCommentEnd: jsonMode ? null : \"*/\",\n    blockCommentContinue: jsonMode ? null : \" * \",\n    lineComment: jsonMode ? null : \"//\",\n    fold: \"brace\",\n    closeBrackets: \"()[]{}''\\\"\\\"``\",\n\n    helperType: jsonMode ? \"json\" : \"javascript\",\n    jsonldMode: jsonldMode,\n    jsonMode: jsonMode,\n\n    expressionAllowed: expressionAllowed,\n\n    skipExpression: function(state) {\n      parseJS(state, \"atom\", \"atom\", \"true\", new CodeMirror.StringStream(\"\", 2, null))\n    }\n  };\n});\n\nCodeMirror.registerHelper(\"wordChars\", \"javascript\", /[\\w$]/);\n\nCodeMirror.defineMIME(\"text/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"text/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/x-javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/x-json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/manifest+json\", { name: \"javascript\", json: true })\nCodeMirror.defineMIME(\"application/ld+json\", { name: \"javascript\", jsonld: true });\nCodeMirror.defineMIME(\"text/typescript\", { name: \"javascript\", typescript: true });\nCodeMirror.defineMIME(\"application/typescript\", { name: \"javascript\", typescript: true });\n\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../xml/xml\"), require(\"../javascript/javascript\"), require(\"../css/css\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../xml/xml\", \"../javascript/javascript\", \"../css/css\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var defaultTags = {\n    script: [\n      [\"lang\", /(javascript|babel)/i, \"javascript\"],\n      [\"type\", /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i, \"javascript\"],\n      [\"type\", /./, \"text/plain\"],\n      [null, null, \"javascript\"]\n    ],\n    style:  [\n      [\"lang\", /^css$/i, \"css\"],\n      [\"type\", /^(text\\/)?(x-)?(stylesheet|css)$/i, \"css\"],\n      [\"type\", /./, \"text/plain\"],\n      [null, null, \"css\"]\n    ]\n  };\n\n  function maybeBackup(stream, pat, style) {\n    var cur = stream.current(), close = cur.search(pat);\n    if (close > -1) {\n      stream.backUp(cur.length - close);\n    } else if (cur.match(/<\\/?$/)) {\n      stream.backUp(cur.length);\n      if (!stream.match(pat, false)) stream.match(cur);\n    }\n    return style;\n  }\n\n  var attrRegexpCache = {};\n  function getAttrRegexp(attr) {\n    var regexp = attrRegexpCache[attr];\n    if (regexp) return regexp;\n    return attrRegexpCache[attr] = new RegExp(\"\\\\s+\" + attr + \"\\\\s*=\\\\s*('|\\\")?([^'\\\"]+)('|\\\")?\\\\s*\");\n  }\n\n  function getAttrValue(text, attr) {\n    var match = text.match(getAttrRegexp(attr))\n    return match ? /^\\s*(.*?)\\s*$/.exec(match[2])[1] : \"\"\n  }\n\n  function getTagRegexp(tagName, anchored) {\n    return new RegExp((anchored ? \"^\" : \"\") + \"<\\/\\\\s*\" + tagName + \"\\\\s*>\", \"i\");\n  }\n\n  function addTags(from, to) {\n    for (var tag in from) {\n      var dest = to[tag] || (to[tag] = []);\n      var source = from[tag];\n      for (var i = source.length - 1; i >= 0; i--)\n        dest.unshift(source[i])\n    }\n  }\n\n  function findMatchingMode(tagInfo, tagText) {\n    for (var i = 0; i < tagInfo.length; i++) {\n      var spec = tagInfo[i];\n      if (!spec[0] || spec[1].test(getAttrValue(tagText, spec[0]))) return spec[2];\n    }\n  }\n\n  CodeMirror.defineMode(\"htmlmixed\", function (config, parserConfig) {\n    var htmlMode = CodeMirror.getMode(config, {\n      name: \"xml\",\n      htmlMode: true,\n      multilineTagIndentFactor: parserConfig.multilineTagIndentFactor,\n      multilineTagIndentPastTag: parserConfig.multilineTagIndentPastTag,\n      allowMissingTagName: parserConfig.allowMissingTagName,\n    });\n\n    var tags = {};\n    var configTags = parserConfig && parserConfig.tags, configScript = parserConfig && parserConfig.scriptTypes;\n    addTags(defaultTags, tags);\n    if (configTags) addTags(configTags, tags);\n    if (configScript) for (var i = configScript.length - 1; i >= 0; i--)\n      tags.script.unshift([\"type\", configScript[i].matches, configScript[i].mode])\n\n    function html(stream, state) {\n      var style = htmlMode.token(stream, state.htmlState), tag = /\\btag\\b/.test(style), tagName\n      if (tag && !/[<>\\s\\/]/.test(stream.current()) &&\n          (tagName = state.htmlState.tagName && state.htmlState.tagName.toLowerCase()) &&\n          tags.hasOwnProperty(tagName)) {\n        state.inTag = tagName + \" \"\n      } else if (state.inTag && tag && />$/.test(stream.current())) {\n        var inTag = /^([\\S]+) (.*)/.exec(state.inTag)\n        state.inTag = null\n        var modeSpec = stream.current() == \">\" && findMatchingMode(tags[inTag[1]], inTag[2])\n        var mode = CodeMirror.getMode(config, modeSpec)\n        var endTagA = getTagRegexp(inTag[1], true), endTag = getTagRegexp(inTag[1], false);\n        state.token = function (stream, state) {\n          if (stream.match(endTagA, false)) {\n            state.token = html;\n            state.localState = state.localMode = null;\n            return null;\n          }\n          return maybeBackup(stream, endTag, state.localMode.token(stream, state.localState));\n        };\n        state.localMode = mode;\n        state.localState = CodeMirror.startState(mode, htmlMode.indent(state.htmlState, \"\", \"\"));\n      } else if (state.inTag) {\n        state.inTag += stream.current()\n        if (stream.eol()) state.inTag += \" \"\n      }\n      return style;\n    };\n\n    return {\n      startState: function () {\n        var state = CodeMirror.startState(htmlMode);\n        return {token: html, inTag: null, localMode: null, localState: null, htmlState: state};\n      },\n\n      copyState: function (state) {\n        var local;\n        if (state.localState) {\n          local = CodeMirror.copyState(state.localMode, state.localState);\n        }\n        return {token: state.token, inTag: state.inTag,\n                localMode: state.localMode, localState: local,\n                htmlState: CodeMirror.copyState(htmlMode, state.htmlState)};\n      },\n\n      token: function (stream, state) {\n        return state.token(stream, state);\n      },\n\n      indent: function (state, textAfter, line) {\n        if (!state.localMode || /^\\s*<\\//.test(textAfter))\n          return htmlMode.indent(state.htmlState, textAfter, line);\n        else if (state.localMode.indent)\n          return state.localMode.indent(state.localState, textAfter, line);\n        else\n          return CodeMirror.Pass;\n      },\n\n      innerMode: function (state) {\n        return {state: state.localState || state.htmlState, mode: state.localMode || htmlMode};\n      }\n    };\n  }, \"xml\", \"javascript\", \"css\");\n\n  CodeMirror.defineMIME(\"text/html\", \"htmlmixed\");\n});\n", "<template>\n  <Codemirror v-model:value=\"generateCode\" :options=\"cmOptions\" border ref=\"cmRef\" @change=\"onChange\" @ready=\"onReady\"> </Codemirror>\n</template>\n<script lang=\"ts\" setup>\nimport { ref, reactive, onMounted, onUnmounted, defineProps } from \"vue\";\nimport Codemirror from \"codemirror-editor-vue3\";\nimport \"codemirror/theme/monokai.css\";\n// language\nimport \"codemirror/mode/htmlmixed/htmlmixed.js\";\ninterface Props {\n  code: string;\n}\nconst props = defineProps<Props>();\nconst generateCode = ref<string>(\"\");\nconst cmRef = ref();\nconst cmOptions = reactive({\n  mode: \"text/html\",\n  theme: \"monokai\" // Theme\n});\nwatch(\n  () => props.code,\n  (code) => {\n    generateCode.value = code;\n  },\n  {\n    immediate: true\n  }\n);\nonMounted(() => {\n  setTimeout(() => {\n    cmRef.value?.refresh();\n  }, 1000);\n});\n\nonUnmounted(() => {\n  cmRef.value?.destroy();\n});\nconst onChange = (val, cm) => {\n  console.log(val);\n  console.log(cm.getValue());\n};\nconst onReady = (cm) => {\n  console.log(cm.focus());\n};\n</script>\n<style lang=\"less\" scoped></style>\n", "import { ElMessage } from \"element-plus\";\n\nconst WS_BACKEND_URL =\n  import.meta.env.VITE_WS_BACKEND_URL || \"wss://design-ws.sy.soyoung.com/generate-code\";\nconst ERROR_MESSAGE = \"生成代码错误。\";\n\nexport interface CodeGenerationParams {\n  generationType: \"create\" | \"update\";\n  image: string;\n  history?: string[];\n  // isImageGenerationEnabled: boolean; // TODO: Merge with Settings type in types.ts\n}\n\nexport function generateCode(\n  params: CodeGenerationParams,\n  onChange: (chunk: string) => void,\n  onSetCode: (code: string) => void,\n  onStatusUpdate: (status: string) => void,\n  onComplete: () => void\n) {\n  const wsUrl = `${WS_BACKEND_URL}`;\n\n  const ws = new WebSocket(wsUrl);\n\n  ws.addEventListener(\"open\", () => {\n    ws.send(\n      JSON.stringify({\n        event: \"message\",\n        data: params\n      })\n    );\n  });\n\n  ws.addEventListener(\"message\", async (event: MessageEvent) => {\n    const response = JSON.parse(event.data);\n    if (response.type === \"chunk\") {\n      onChange(response.value);\n    } else if (response.type === \"status\") {\n      onStatusUpdate(response.value);\n    } else if (response.type === \"setCode\") {\n      onSetCode(response.value);\n    } else if (response.type === \"error\") {\n      console.error(\"Error generating code\", response.value);\n      ElMessage({\n        message: response.value,\n        type: \"error\"\n      });\n    }\n  });\n\n  ws.addEventListener(\"close\", (event) => {\n    console.log(\"Connection closed\", event.code, event.reason);\n    if (event.code != 1000) {\n      console.error(\"WebSocket error code\", event);\n      ElMessage({\n        message: ERROR_MESSAGE,\n        type: \"error\"\n      });\n    } else {\n      onComplete();\n    }\n  });\n\n  ws.addEventListener(\"error\", (error) => {\n    console.error(\"WebSocket error\", error);\n    ElMessage({\n      message: ERROR_MESSAGE,\n      type: \"error\"\n    });\n  });\n}\n", "<template>\r\n  <div\r\n    :class=\"{\r\n      home: true,\r\n      'home-theme': store.themeShow\r\n    }\"\r\n  >\r\n    <SpHeader>\r\n      <SpLogin></SpLogin>\r\n    </SpHeader>\r\n    <div class=\"screen-body\">\r\n      <div class=\"screen-body__left\">\r\n        <CodePreview v-if=\"appState === 'CODING'\" :code=\"generatedCode\" />\r\n        <div\r\n          :class=\"{\r\n            scan: appState === 'CODING',\r\n            'screen-body__left-image': true\r\n          }\"\r\n        >\r\n          <img loading=\"lazy\" style=\"width: 100%\" :src=\"material.url\" alt=\"Reference\" />\r\n        </div>\r\n        <el-button :disabled=\"loading\" @click=\"doCreate\">开始生成</el-button>\r\n      </div>\r\n      <div class=\"screen-body__content\">\r\n        <div\r\n          :class=\"{\r\n            'preview-desktop': deviceType === 'desktop',\r\n            'preview-mobile': deviceType === 'mobile'\r\n          }\"\r\n        >\r\n          <Preview v-if=\"appState === 'CODING' || appState === 'CODE_READY'\" :appState=\"appState\" :code=\"generatedCode\" :device=\"deviceType\" />\r\n        </div>\r\n      </div>\r\n      <div v-if=\"appState === 'CODE_READY'\" class=\"screen-body__right\">\r\n        <CodeMirror :code=\"generatedCode\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted, watch } from \"vue\";\r\nimport { themeStore } from \"@/store\";\r\nimport { useRoute } from \"vue-router\";\r\nimport { ErrorCode } from \"@/model\";\r\nimport SpHeader from \"@/views/layouts/components/spHeader.vue\";\r\nimport SpLogin from \"@/views/layouts/components/spLogin.vue\";\r\nimport Preview from \"./components/preview.vue\";\r\nimport CodePreview from \"./components/codePreview.vue\";\r\nimport CodeMirror from \"./components/codeMirror.vue\";\r\nimport { materialDetail } from \"@/api/common\";\r\nimport { generateCode } from \".\";\r\ninterface Settings {\r\n  isImageGenerationEnabled: boolean;\r\n  editorTheme: string;\r\n}\r\nconst store = themeStore(); // 黑白主题切换Store\r\nconst appState = ref<\"INITIAL\" | \"CODING\" | \"CODE_READY\">(\"INITIAL\");\r\nconst generatedCode = ref<string>(\"\");\r\nconst referenceImage = ref<string>(\"\");\r\nconst executionConsole = ref<string[]>([]);\r\nconst history = ref<string[]>([]);\r\nconst updateInstruction = ref<string>(\"\");\r\nconst loading = ref<boolean>(false);\r\nconst deviceType = ref<\"desktop\" | \"mobile\">(\"desktop\");\r\nconst material = ref<Record<string, any>>({});\r\nconst route = useRoute();\r\nconst settings = ref<Settings>({\r\n  isImageGenerationEnabled: true,\r\n  editorTheme: \"cobalt\"\r\n});\r\nfunction detectDeviceType(width: number, height: number) {\r\n  const aspectRatio = width / height;\r\n\r\n  // 定义移动端和 PC 端的宽高比范围\r\n  const mobileAspectRatioThreshold = 1.5; // 举例：移动端宽高比的阈值\r\n  const pcAspectRatioThreshold = 1.7; // 举例：PC 端宽高比的阈值\r\n  // 判断宽高比来确定设备类型\r\n  if (aspectRatio <= mobileAspectRatioThreshold) {\r\n    deviceType.value = \"mobile\";\r\n  } else {\r\n    deviceType.value = \"desktop\";\r\n  }\r\n}\r\n\r\nconst findMaterial = async (id) => {\r\n  try {\r\n    loading.value = true;\r\n    const { code, data, message } = await materialDetail({ id });\r\n    if (code !== ErrorCode.OK) {\r\n      throw new Error(message);\r\n    }\r\n    material.value = data;\r\n    detectDeviceType(data.width, data.height);\r\n    referenceImage.value = await loadImage();\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n  loading.value = false;\r\n};\r\nconst doCreate = () => {\r\n  doGenerateCode({\r\n    generationType: \"create\",\r\n    image: referenceImage.value\r\n  });\r\n};\r\nconst doGenerateCode = (params) => {\r\n  executionConsole.value = [];\r\n  appState.value = \"CODING\";\r\n  const updatedParams = { ...params, ...settings.value };\r\n  generateCode(\r\n    updatedParams,\r\n    (token) => {\r\n      generatedCode.value += token;\r\n    },\r\n    (code) => {\r\n      generatedCode.value = code;\r\n    },\r\n    (line) => {\r\n      executionConsole.value = [...executionConsole.value, line];\r\n    },\r\n    () => {\r\n      appState.value = \"CODE_READY\";\r\n    }\r\n  );\r\n};\r\n\r\nconst reset = () => {\r\n  appState.value = \"INITIAL\";\r\n  generatedCode.value = \"\";\r\n  referenceImage.value = \"\";\r\n  executionConsole.value = [];\r\n  history.value = [];\r\n};\r\n\r\nconst loadImage = (): Promise<string> => {\r\n  return new Promise((next, error) => {\r\n    const image = new Image();\r\n    image.onload = function () {\r\n      next(getBase64Image(image));\r\n    };\r\n    image.crossOrigin = \"*\";\r\n    image.onerror = error;\r\n    image.src = material.value.url;\r\n  });\r\n};\r\n\r\nfunction getBase64Image(img) {\r\n  let canvas = document.createElement(\"canvas\");\r\n  canvas.width = img.width;\r\n  canvas.height = img.height;\r\n  let ctx = canvas.getContext(\"2d\") as any;\r\n  ctx.drawImage(img, 0, 0);\r\n  let dataURL = canvas.toDataURL(\"image/png\");\r\n  return dataURL.replace(/^data:image\\/?[A-z]*;base64,/, \"\");\r\n}\r\nwatch(\r\n  () => route.query,\r\n  (query) => {\r\n    console.log(query);\r\n    if (query.id) {\r\n      findMaterial(query.id);\r\n    }\r\n  },\r\n  {\r\n    immediate: true\r\n  }\r\n);\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.screen-body {\r\n  height: calc(100vh - 64px);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  &__left {\r\n    height: 100%;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n    border-right: 1px solid #ccc;\r\n    background: #fff;\r\n    max-width: 300px;\r\n    overflow: auto;\r\n    &-image {\r\n      border: 1px solid transparent;\r\n      padding: 5px;\r\n    }\r\n  }\r\n  &__content {\r\n    height: 100%;\r\n    display: flex;\r\n    padding: 20px;\r\n    flex: 1;\r\n    min-width: 400px;\r\n    justify-content: center;\r\n  }\r\n  &__right {\r\n    width: 400px;\r\n    padding: 5px;\r\n    box-sizing: border-box;\r\n    overflow: hidden;\r\n  }\r\n}\r\n.preview {\r\n  &-desktop {\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: 5px;\r\n  }\r\n  &-mobile {\r\n    width: 420px;\r\n    height: 820px;\r\n    box-sizing: border-box;\r\n    padding: 105px 20px;\r\n    z-index: 0;\r\n    background: url(https://static.soyoung.com/sy-pre/780-1701159000690.jpeg) 0 0 no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .screen-body__left {\r\n    display: none;\r\n  }\r\n}\r\n.scan {\r\n  background: url(https://static.soyoung.com/sy-pre/2-1701310200687.png) center center no-repeat;\r\n  border: 1px solid #b0f9e4;\r\n  background-size: 100% 100%;\r\n  position: relative;\r\n  &::after {\r\n    content: \"\";\r\n    background: url(https://static.soyoung.com/sy-pre/4-1701310200687.png) center center no-repeat;\r\n    position: absolute;\r\n    background-size: 100% 100%;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin: 0 auto;\r\n    overflow: hidden;\r\n    animation: move 1.5s ease-in-out infinite;\r\n    top: 0;\r\n    left: 0;\r\n  }\r\n}\r\n@keyframes move {\r\n  from {\r\n    top: -100%;\r\n  }\r\n  to {\r\n    top: 0;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "throttledCode", "ref", "throttled", "throttle", "console", "log", "code", "value", "trailing", "watch", "scrollRef", "scrollTop", "scrollHeight", "CodeMirror", "require$$0", "htmlConfig", "autoSelfClosers", "area", "base", "br", "col", "command", "embed", "frame", "hr", "img", "input", "keygen", "link", "meta", "param", "source", "track", "wbr", "menuitem", "implicitlyClosed", "dd", "li", "optgroup", "option", "p", "rp", "rt", "tbody", "td", "tfoot", "th", "tr", "contextGrabbers", "dt", "address", "article", "aside", "blockquote", "dir", "div", "dl", "fieldset", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "header", "hgroup", "menu", "nav", "ol", "pre", "section", "table", "ul", "thead", "doNotIndent", "allowUnquoted", "allowMissing", "caseFold", "xmlConfig", "allowMissingTagName", "defineMode", "editor<PERSON><PERSON><PERSON>", "config_", "type", "setStyle", "indentUnit", "config", "defaults", "htmlMode", "prop", "inText", "stream", "state", "chain", "parser", "tokenize", "ch", "next", "eat", "match", "inBlock", "eat<PERSON>hile", "doctype", "inTag", "baseState", "tagName", "tagStart", "test", "inAttribute", "stringStartCol", "column", "quote", "closure", "eol", "isInAttribute", "style", "terminator", "depth", "lower", "toLowerCase", "Context", "startOfLine", "this", "prev", "context", "indent", "indented", "hasOwnProperty", "noIndent", "popContext", "maybePopContext", "nextTagName", "parentTagName", "tagNameState", "closeTagNameState", "current", "attrState", "matchClosing", "closeState", "closeStateErr", "_stream", "attrEqState", "attrValueState", "attrContinuedState", "isInText", "startState", "baseIndent", "token", "sol", "indentation", "eatSpace", "textAfter", "fullLine", "Pass", "length", "multilineTagIndentPastTag", "multilineTagIndentFactor", "alignCDATA", "tagAfter", "exec", "grabbers", "electricInput", "blockCommentStart", "blockCommentEnd", "configuration", "helperType", "skipAttribute", "xmlCurrentTag", "name", "close", "xmlCurrentContext", "cx", "push", "reverse", "defineMIME", "mimeModes", "parserConfig", "content", "statementIndent", "jsonldMode", "j<PERSON>ld", "jsonMode", "json", "trackScope", "isTS", "typescript", "wordRE", "wordCharacters", "keywords", "kw", "A", "B", "C", "D", "operator", "atom", "if", "while", "with", "else", "do", "try", "finally", "return", "break", "continue", "new", "delete", "void", "throw", "debugger", "var", "const", "let", "function", "catch", "for", "switch", "case", "default", "in", "typeof", "instanceof", "true", "false", "null", "undefined", "NaN", "Infinity", "class", "super", "yield", "export", "import", "extends", "await", "isOperatorChar", "isJsonldKeyword", "readRegexp", "escaped", "inSet", "ret", "tp", "cont", "tokenBase", "tokenString", "tokenComment", "skipToEnd", "expressionAllowed", "tokenQuasi", "peek", "string", "slice", "start", "lexical", "word", "lastType", "propertyIsEnumerable", "maybeEnd", "brackets", "findFatArrow", "fatArrowAt", "arrow", "indexOf", "m", "index", "sawSomething", "pos", "char<PERSON>t", "bracket", "atomicTypes", "number", "variable", "regexp", "JSLexical", "align", "info", "inScope", "varname", "v", "localVars", "vars", "parseJS", "cc", "marked", "pop", "expression", "statement", "lex", "pass", "i", "arguments", "apply", "inList", "list", "register", "block", "newContext", "registerVarScoped", "Var", "globalVars", "inner", "isModifier", "defaultVars", "pushcontext", "pushblockcontext", "popcontext", "pushlex", "result", "outer", "poplex", "expect", "wanted", "exp", "var<PERSON><PERSON>", "parenExpr", "maybeexpression", "<PERSON><PERSON><PERSON>", "functiondef", "forspec", "className", "enum<PERSON>f", "typename", "typeexpr", "pattern", "maybelabel", "maybeCatchBinding", "afterExport", "afterImport", "funarg", "expressionInner", "expressionNoComma", "noComma", "body", "arrowBodyNoComma", "arrowBody", "commasep", "maybeop", "maybeoperatorNoComma", "maybeoperatorComma", "classExpression", "arrayLiteral", "contCommasep", "objprop", "quasi", "<PERSON><PERSON><PERSON><PERSON>", "me", "expr", "property", "backUp", "continueQuasi", "targetNoComma", "target", "maybeTypeArgs", "_", "getterSetter", "afterprop", "maybetype", "what", "end", "sep", "proceed", "maybetypeOrIn", "mayberettype", "isKW", "afterType", "typeprops", "typearg", "maybeReturnType", "quasiType", "typeprop", "functiondecl", "continueQuasiType", "typeparam", "maybeTypeDefault", "maybeAssign", "vardefCont", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proppa<PERSON>n", "_type", "forspec1", "forspec2", "classNameAfter", "classBody", "classfield", "maybeFrom", "exportField", "importSpec", "maybeMoreImports", "maybeAs", "enummember", "isContinuedStatement", "basecolumn", "top", "firstChar", "c", "closing", "doubleIndentSwitch", "blockCommentContinue", "lineComment", "fold", "closeBrackets", "skipExpression", "StringStream", "registerHelper", "defaultTags", "script", "maybeBackup", "pat", "cur", "search", "attrRegexpCache", "getAttrRegexp", "attr", "RegExp", "getAttrValue", "text", "getTagRegexp", "anchored", "addTags", "from", "to", "tag", "dest", "unshift", "findMatchingMode", "tagInfo", "tagText", "spec", "getMode", "tags", "configTags", "configScript", "scriptTypes", "matches", "mode", "html", "htmlState", "modeSpec", "endTagA", "endTag", "localState", "localMode", "copyState", "local", "line", "innerMode", "mod", "require$$1", "require$$2", "require$$3", "generateCode", "cmRef", "cmOptions", "reactive", "theme", "immediate", "onMounted", "setTimeout", "refresh", "onUnmounted", "destroy", "onChange", "val", "cm", "getValue", "onReady", "focus", "WS_BACKEND_URL", "VITE_WS_BACKEND_URL", "ERROR_MESSAGE", "store", "themeStore", "appState", "generatedCode", "referenceImage", "executionConsole", "loading", "deviceType", "material", "route", "useRoute", "settings", "isImageGenerationEnabled", "editor<PERSON><PERSON><PERSON>", "findMaterial", "async", "id", "data", "message", "materialDetail", "ErrorCode", "OK", "Error", "width", "height", "aspectRatio", "detectDeviceType", "loadImage", "error", "doCreate", "doGenerateCode", "generationType", "image", "params", "onSetCode", "onStatusUpdate", "onComplete", "ws", "WebSocket", "addEventListener", "send", "JSON", "stringify", "event", "response", "parse", "ElMessage", "reason", "Promise", "Image", "onload", "canvas", "document", "createElement", "getContext", "drawImage", "toDataURL", "replace", "getBase64Image", "crossOrigin", "onerror", "src", "url", "query"], "mappings": "+lBAUA,MAAAA,EAAAC,EACAC,EAAAC,EAAA,IAEAC,EAAAC,EAAAA,UAAkB,KAEdC,QAAAC,IAAAP,EAAAQ,MACAN,EAAAO,MAAAT,EAAAQ,IAAA,GACF,IACA,CAAAE,UAAA,WAGFC,GAAA,IAAAX,EAAAQ,OACcA,iKCbd,MAAAR,EAAAC,EACAW,EAAAT,WAEAQ,GAAA,IAAAX,EAAAQ,OACcA,IAEVI,EAAAH,QACEG,EAAAH,MAAAI,UAAAD,EAAAH,MAAAK,aAA4C,qKCNtCC,EALJC,IAQJC,EAAa,CACfC,gBAAiB,CAACC,MAAQ,EAAMC,MAAQ,EAAMC,IAAM,EAAMC,KAAO,EAAMC,SAAW,EAChEC,OAAS,EAAMC,OAAS,EAAMC,IAAM,EAAMC,KAAO,EAAMC,OAAS,EAChEC,QAAU,EAAMC,MAAQ,EAAMC,MAAQ,EAAMC,OAAS,EAAMC,QAAU,EACrEC,OAAS,EAAMC,KAAO,EAAMC,UAAY,GAC1DC,iBAAkB,CAACC,IAAM,EAAMC,IAAM,EAAMC,UAAY,EAAMC,QAAU,EAAMC,GAAK,EAC/DC,IAAM,EAAMC,IAAM,EAAMC,OAAS,EAAMC,IAAM,EAAMC,OAAS,EAC5DC,IAAM,EAAMC,IAAM,GACrCC,gBAAiB,CACfZ,GAAM,CAACA,IAAM,EAAMa,IAAM,GACzBA,GAAM,CAACb,IAAM,EAAMa,IAAM,GACzBZ,GAAM,CAACA,IAAM,GACbE,OAAU,CAACA,QAAU,EAAMD,UAAY,GACvCA,SAAY,CAACA,UAAY,GACzBE,EAAK,CAACU,SAAW,EAAMC,SAAW,EAAMC,OAAS,EAAMC,YAAc,EAAMC,KAAO,EAC5EC,KAAO,EAAMC,IAAM,EAAMC,UAAY,EAAMC,QAAU,EAAMC,MAAQ,EACnEC,IAAM,EAAMC,IAAM,EAAMC,IAAM,EAAMC,IAAM,EAAMC,IAAM,EAAMC,IAAM,EAClEC,QAAU,EAAMC,QAAU,EAAM3C,IAAM,EAAM4C,MAAQ,EAAMC,KAAO,EAAMC,IAAM,EAC7E9B,GAAK,EAAM+B,KAAO,EAAMC,SAAW,EAAMC,OAAS,EAAMC,IAAM,GACpEjC,GAAM,CAACA,IAAM,EAAMC,IAAM,GACzBA,GAAM,CAACD,IAAM,EAAMC,IAAM,GACzBC,MAAS,CAACA,OAAS,EAAME,OAAS,GAClCD,GAAM,CAACA,IAAM,EAAME,IAAM,GACzBD,MAAS,CAACF,OAAS,GACnBG,GAAM,CAACF,IAAM,EAAME,IAAM,GACzB6B,MAAS,CAAChC,OAAS,EAAME,OAAS,GAClCE,GAAM,CAACA,IAAM,IAEf6B,YAAa,CAACL,KAAO,GACrBM,eAAe,EACfC,cAAc,EACdC,UAAU,GAGRC,EAAY,CACdhE,gBAAiB,CAAE,EACnBmB,iBAAkB,CAAE,EACpBa,gBAAiB,CAAE,EACnB4B,YAAa,CAAE,EACfC,eAAe,EACfC,cAAc,EACdG,qBAAqB,EACrBF,UAAU,GAGZlE,EAAWqE,WAAW,OAAO,SAASC,EAAYC,GAChD,IAOIC,EAAMC,EAPNC,EAAaJ,EAAWI,WACxBC,EAAS,CAAE,EACXC,EAAWL,EAAQM,SAAW3E,EAAaiE,EAC/C,IAAA,IAASW,KAAQF,EAAiBD,EAAAG,GAAQF,EAASE,GACnD,IAAA,IAASA,KAAQP,EAAgBI,EAAAG,GAAQP,EAAQO,GAKxC,SAAAC,EAAOC,EAAQC,GACtB,SAASC,EAAMC,GAEN,OADPF,EAAMG,SAAWD,EACVA,EAAOH,EAAQC,EACvB,CAEG,IAAAI,EAAKL,EAAOM,OAChB,MAAU,KAAND,EACEL,EAAOO,IAAI,KACTP,EAAOO,IAAI,KACTP,EAAOQ,MAAM,UAAkBN,EAAMO,EAAQ,OAAQ,QAC7C,KACHT,EAAOQ,MAAM,MACfN,EAAMO,EAAQ,UAAW,WACvBT,EAAOQ,MAAM,WAAW,GAAM,IACvCR,EAAOU,SAAS,aACTR,EAAMS,EAAQ,KAEd,KAEAX,EAAOO,IAAI,MACpBP,EAAOU,SAAS,aACVT,EAAAG,SAAWK,EAAQ,OAAQ,MAC1B,SAEPjB,EAAOQ,EAAOO,IAAI,KAAO,WAAa,UACtCN,EAAMG,SAAWQ,EACV,eAEM,KAANP,GAELL,EAAOO,IAAI,KACTP,EAAOO,IAAI,KACRP,EAAOU,SAAS,eAAiBV,EAAOO,IAAI,KAE5CP,EAAOU,SAAS,SAAWV,EAAOO,IAAI,KAGxCP,EAAOU,SAAS,cAAgBV,EAAOO,IAAI,MAEtC,OAAS,SAErBP,EAAOU,SAAS,SACT,KAEV,CAGQ,SAAAE,EAAMZ,EAAQC,GACjB,IAAAI,EAAKL,EAAOM,OAChB,GAAU,KAAND,GAAoB,KAANA,GAAaL,EAAOO,IAAI,KAGjC,OAFPN,EAAMG,SAAWL,EACVP,EAAM,KAANa,EAAY,SAAW,eACvB,cACb,GAAqB,KAANA,EAEF,OADAb,EAAA,SACA,KACb,GAAqB,KAANa,EAAW,CACpBJ,EAAMG,SAAWL,EACjBE,EAAMA,MAAQY,EACRZ,EAAAa,QAAUb,EAAMc,SAAW,KACjC,IAAIT,EAAOL,EAAMG,SAASJ,EAAQC,GAC3B,OAAAK,EAAOA,EAAO,aAAe,WACrC,CAAU,MAAA,SAASU,KAAKX,IACjBJ,EAAAG,SAAWa,EAAYZ,GACvBJ,EAAAiB,eAAiBlB,EAAOmB,SACvBlB,EAAMG,SAASJ,EAAQC,KAE9BD,EAAOQ,MAAM,4CACN,OAEV,CAED,SAASS,EAAYG,GACf,IAAAC,EAAU,SAASrB,EAAQC,GACtB,MAACD,EAAOsB,OACT,GAAAtB,EAAOM,QAAUc,EAAO,CAC1BnB,EAAMG,SAAWQ,EACjB,KACD,CAEI,MAAA,QACb,EAEW,OADPS,EAAQE,eAAgB,EACjBF,CACR,CAEQ,SAAAZ,EAAQe,EAAOC,GACf,OAAA,SAASzB,EAAQC,GACf,MAACD,EAAOsB,OAAO,CAChB,GAAAtB,EAAOQ,MAAMiB,GAAa,CAC5BxB,EAAMG,SAAWL,EACjB,KACD,CACDC,EAAOM,MACR,CACM,OAAAkB,CACR,CACF,CAED,SAASb,EAAQe,GACR,OAAA,SAAS1B,EAAQC,GAEtB,IADI,IAAAI,EAC2B,OAAvBA,EAAKL,EAAOM,SAAiB,CACnC,GAAU,KAAND,EAEK,OADDJ,EAAAG,SAAWO,EAAQe,EAAQ,GAC1BzB,EAAMG,SAASJ,EAAQC,GACxC,GAAyB,KAANI,EAAW,CACpB,GAAa,GAATqB,EAAY,CACdzB,EAAMG,SAAWL,EACjB,KACZ,CAEmB,OADDE,EAAAG,SAAWO,EAAQe,EAAQ,GAC1BzB,EAAMG,SAASJ,EAAQC,EAEjC,CACF,CACM,MAAA,MACb,CACG,CAED,SAAS0B,EAAMb,GACN,OAAAA,GAAWA,EAAQc,aAC3B,CAEQ,SAAAC,EAAQ5B,EAAOa,EAASgB,GAC/BC,KAAKC,KAAO/B,EAAMgC,QAClBF,KAAKjB,QAAUA,GAAW,GAC1BiB,KAAKG,OAASjC,EAAMkC,SACpBJ,KAAKD,YAAcA,GACfnC,EAAOZ,YAAYqD,eAAetB,IAAab,EAAMgC,SAAWhC,EAAMgC,QAAQI,YAChFN,KAAKM,UAAW,EACnB,CACD,SAASC,EAAWrC,GACdA,EAAMgC,UAAehC,EAAAgC,QAAUhC,EAAMgC,QAAQD,KAClD,CACQ,SAAAO,EAAgBtC,EAAOuC,GAE9B,IADI,IAAAC,IACS,CACP,IAACxC,EAAMgC,QACT,OAGF,GADAQ,EAAgBxC,EAAMgC,QAAQnB,SACzBnB,EAAOxC,gBAAgBiF,eAAeT,EAAMc,MAC5C9C,EAAOxC,gBAAgBwE,EAAMc,IAAgBL,eAAeT,EAAMa,IACrE,OAEFF,EAAWrC,EACZ,CACF,CAEQ,SAAAY,EAAUrB,EAAMQ,EAAQC,GAC/B,MAAY,WAART,GACIS,EAAAc,SAAWf,EAAOmB,SACjBuB,GACU,YAARlD,EACFmD,EAEA9B,CAEV,CACQ,SAAA6B,EAAalD,EAAMQ,EAAQC,GAClC,MAAY,QAART,GACIS,EAAAa,QAAUd,EAAO4C,UACZnD,EAAA,MACJoD,GACElD,EAAOP,qBAA+B,UAARI,GAC5BC,EAAA,cACJoD,EAAUrD,EAAMQ,EAAQC,KAEpBR,EAAA,QACJiD,EAEV,CACQ,SAAAC,EAAkBnD,EAAMQ,EAAQC,GACvC,GAAY,QAART,EAAgB,CACd,IAAAsB,EAAUd,EAAO4C,UAIhB,OAHD3C,EAAMgC,SAAWhC,EAAMgC,QAAQnB,SAAWA,GAC1CnB,EAAOrD,iBAAiB8F,eAAeT,EAAM1B,EAAMgC,QAAQnB,WAC7DwB,EAAWrC,GACRA,EAAMgC,SAAWhC,EAAMgC,QAAQnB,SAAWA,IAAoC,IAAxBnB,EAAOmD,cACrDrD,EAAA,MACJsD,IAEItD,EAAA,YACJuD,EAEV,CAAU,OAAArD,EAAOP,qBAA+B,UAARI,GAC5BC,EAAA,cACJsD,EAAWvD,EAAMQ,EAAQC,KAErBR,EAAA,QACJuD,EAEV,CAEQ,SAAAD,EAAWvD,EAAMyD,EAAShD,GACjC,MAAY,UAART,GACSC,EAAA,QACJsD,IAETT,EAAWrC,GACJY,EACR,CACQ,SAAAmC,EAAcxD,EAAMQ,EAAQC,GAE5B,OADIR,EAAA,QACJsD,EAAWvD,EAAMQ,EAAQC,EACjC,CAEQ,SAAA4C,EAAUrD,EAAMyD,EAAShD,GAChC,GAAY,QAART,EAEK,OADIC,EAAA,YACJyD,EACE1D,GAAQ,UAARA,GAA4B,gBAARA,EAAwB,CACrD,IAAIsB,EAAUb,EAAMa,QAASC,EAAWd,EAAMc,SASvC,OARDd,EAAAa,QAAUb,EAAMc,SAAW,KACrB,gBAARvB,GACAG,EAAOxE,gBAAgBiH,eAAeT,EAAMb,IAC9CyB,EAAgBtC,EAAOa,IAEvByB,EAAgBtC,EAAOa,GACvBb,EAAMgC,QAAU,IAAIJ,EAAQ5B,EAAOa,EAASC,GAAYd,EAAMkC,WAEzDtB,CACR,CAEM,OADIpB,EAAA,QACJoD,CACR,CACQ,SAAAK,EAAY1D,EAAMQ,EAAQC,GACjC,MAAY,UAART,EAAyB2D,GACxBxD,EAAOV,eAAyBQ,EAAA,SAC9BoD,EAAUrD,EAAMQ,EAAQC,GAChC,CACQ,SAAAkD,EAAe3D,EAAMQ,EAAQC,GACpC,MAAY,UAART,EAAyB4D,EACjB,QAAR5D,GAAkBG,EAAOX,eAA2BS,EAAA,SAAiBoD,IAC9DpD,EAAA,QACJoD,EAAUrD,EAAMQ,EAAQC,GAChC,CACQ,SAAAmD,EAAmB5D,EAAMQ,EAAQC,GACxC,MAAY,UAART,EAAyB4D,EACtBP,EAAUrD,EAAMQ,EAAQC,EAChC,CAEM,OAtMPF,EAAOsD,UAAW,EAsMX,CACLC,WAAY,SAASC,GACnB,IAAItD,EAAQ,CAACG,SAAUL,EACVE,MAAOY,EACPsB,SAAUoB,GAAc,EACxBzC,QAAS,KAAMC,SAAU,KACzBkB,QAAS,MAEf,OADW,MAAdsB,IAAoBtD,EAAMsD,WAAaA,GACpCtD,CACR,EAEDuD,MAAO,SAASxD,EAAQC,GAItB,IAHKA,EAAMa,SAAWd,EAAOyD,QACrBxD,EAAAkC,SAAWnC,EAAO0D,eAEtB1D,EAAO2D,WAAmB,OAAA,KACvBnE,EAAA,KACP,IAAIgC,EAAQvB,EAAMG,SAASJ,EAAQC,GAO5B,OANFuB,GAAShC,IAAkB,WAATgC,IACV/B,EAAA,KACXQ,EAAMA,MAAQA,EAAMA,MAAMT,GAAQgC,EAAOxB,EAAQC,GAC7CR,IACM+B,EAAY,SAAZ/B,EAAsB+B,EAAQ,SAAW/B,IAE9C+B,CACR,EAEDU,OAAQ,SAASjC,EAAO2D,EAAWC,GACjC,IAAI5B,EAAUhC,EAAMgC,QAEhB,GAAAhC,EAAMG,SAASmB,cACb,OAAAtB,EAAMc,UAAYd,EAAMkC,SACnBlC,EAAMiB,eAAiB,EAEvBjB,EAAMkC,SAAWzC,EAE5B,GAAIuC,GAAWA,EAAQI,SAAU,OAAOrH,EAAW8I,KACnD,GAAI7D,EAAMG,UAAYQ,GAASX,EAAMG,UAAYL,EAC/C,OAAO8D,EAAWA,EAASrD,MAAM,UAAU,GAAGuD,OAAS,EAEzD,GAAI9D,EAAMa,QACR,OAAyC,IAArCnB,EAAOqE,0BACF/D,EAAMc,SAAWd,EAAMa,QAAQiD,OAAS,EAExC9D,EAAMc,SAAWrB,GAAcC,EAAOsE,0BAA4B,GAE7E,GAAItE,EAAOuE,YAAc,cAAclD,KAAK4C,GAAmB,OAAA,EAC/D,IAAIO,EAAWP,GAAa,sBAAsBQ,KAAKR,GACnD,GAAAO,GAAYA,EAAS,GACvB,KAAOlC,GAAS,CACd,GAAIA,EAAQnB,SAAWqD,EAAS,GAAI,CAClClC,EAAUA,EAAQD,KAClB,KACZ,CAAA,IAAqBrC,EAAOrD,iBAAiB8F,eAAeT,EAAMM,EAAQnB,UAG9D,MAFAmB,EAAUA,EAAQD,IAIrB,SACQmC,EACT,KAAOlC,GAAS,CACd,IAAIoC,EAAW1E,EAAOxC,gBAAgBwE,EAAMM,EAAQnB,UACpD,IAAIuD,IAAYA,EAASjC,eAAeT,EAAMwC,EAAS,KAGrD,MAFAlC,EAAUA,EAAQD,IAGrB,CAEH,KAAOC,GAAWA,EAAQD,OAASC,EAAQH,aACzCG,EAAUA,EAAQD,KAChB,OAAAC,EAAgBA,EAAQC,OAASxC,EACzBO,EAAMsD,YAAc,CACjC,EAEDe,cAAe,gBACfC,kBAAmB,UACnBC,gBAAiB,SAEjBC,cAAe9E,EAAOE,SAAW,OAAS,MAC1C6E,WAAY/E,EAAOE,SAAW,OAAS,MAEvC8E,cAAe,SAAS1E,GAClBA,EAAMA,OAASkD,IACjBlD,EAAMA,MAAQ4C,EACjB,EAED+B,cAAe,SAAS3E,GACf,OAAAA,EAAMa,QAAU,CAAC+D,KAAM5E,EAAMa,QAASgE,MAAqB,YAAd7E,EAAMT,MAAsB,IACjF,EAEDuF,kBAAmB,SAAS9E,GAE1B,IADA,IAAIgC,EAAU,GACL+C,EAAK/E,EAAMgC,QAAS+C,EAAIA,EAAKA,EAAGhD,KAC/BC,EAAAgD,KAAKD,EAAGlE,SAClB,OAAOmB,EAAQiD,SAChB,EAEL,IAEWlK,EAAAmK,WAAW,WAAY,OACvBnK,EAAAmK,WAAW,kBAAmB,OACpCnK,EAAWoK,UAAUhD,eAAe,cACvCpH,EAAWmK,WAAW,YAAa,CAACN,KAAM,MAAOhF,UAAU,WApZjD7E,EAGRE,EAkCAiE,0CCrCQnE,EALJC,KAQGoE,WAAW,cAAc,SAASM,EAAQ0F,GACnD,IA8CI7F,EAAM8F,EA9CN5F,EAAaC,EAAOD,WACpB6F,EAAkBF,EAAaE,gBAC/BC,EAAaH,EAAaI,OAC1BC,EAAWL,EAAaM,MAAQH,EAChCI,GAAyC,IAA5BP,EAAaO,WAC1BC,EAAOR,EAAaS,WACpBC,EAASV,EAAaW,gBAAkB,mBAIxCC,EAAW,WACb,SAASC,EAAG1G,GAAO,MAAO,CAACA,KAAMA,EAAMgC,MAAO,UAAW,CACzD,IAAI2E,EAAID,EAAG,aAAcE,EAAIF,EAAG,aAAcG,EAAIH,EAAG,aAAcI,EAAIJ,EAAG,aACtEK,EAAWL,EAAG,YAAaM,EAAO,CAAChH,KAAM,OAAQgC,MAAO,QAErD,MAAA,CACLiF,GAAMP,EAAG,MAAOQ,MAASP,EAAGQ,KAAQR,EAAGS,KAAQR,EAAGS,GAAMT,EAAGU,IAAOV,EAAGW,QAAWX,EAChFY,OAAUV,EAAGW,MAASX,EAAGY,SAAYZ,EAAGa,IAAOjB,EAAG,OAAQkB,OAAUf,EAAGgB,KAAQhB,EAAGiB,MAASjB,EAC3FkB,SAAYrB,EAAG,YAAasB,IAAOtB,EAAG,OAAQuB,MAASvB,EAAG,OAAQwB,IAAOxB,EAAG,OAC5EyB,SAAYzB,EAAG,YAAa0B,MAAS1B,EAAG,SACxC2B,IAAO3B,EAAG,OAAQ4B,OAAU5B,EAAG,UAAW6B,KAAQ7B,EAAG,QAAS8B,QAAW9B,EAAG,WAC5E+B,GAAM1B,EAAU2B,OAAU3B,EAAU4B,WAAc5B,EAClD6B,KAAQ5B,EAAM6B,MAAS7B,EAAM8B,KAAQ9B,EAAM+B,UAAa/B,EAAMgC,IAAOhC,EAAMiC,SAAYjC,EACvFzE,KAAQmE,EAAG,QAASwC,MAASxC,EAAG,SAAUyC,MAASzC,EAAG,QACtD0C,MAASvC,EAAGwC,OAAU3C,EAAG,UAAW4C,OAAU5C,EAAG,UAAW6C,QAAW1C,EACvE2C,MAAS3C,EAEf,CAjBiB,GAmBX4C,EAAiB,oBACjBC,EAAkB,wFAEtB,SAASC,EAAWnJ,GAElB,IADI,IAAiBM,EAAjB8I,GAAU,EAAaC,GAAQ,EACF,OAAzB/I,EAAON,EAAOM,SAAiB,CACrC,IAAK8I,EAAS,CACR,GAAQ,KAAR9I,IAAgB+I,EAAO,OACf,KAAR/I,EAAqB+I,GAAA,EAChBA,GAAiB,KAAR/I,IAAqB+I,GAAA,EACxC,CACSD,GAACA,GAAmB,MAAR9I,CACvB,CACF,CAKQ,SAAAgJ,EAAIC,EAAI/H,EAAOgI,GAEf,OADAhK,EAAA+J,EAAcC,EAAAA,EACdhI,CACR,CACQ,SAAAiI,EAAUzJ,EAAQC,GACrB,IAAAI,EAAKL,EAAOM,OACZ,GAAM,KAAND,GAAmB,KAANA,EAER,OADDJ,EAAAG,SAAWsJ,EAAYrJ,GACtBJ,EAAMG,SAASJ,EAAQC,MACf,KAANI,GAAaL,EAAOQ,MAAM,kCAC5B,OAAA8I,EAAI,SAAU,aACN,KAANjJ,GAAaL,EAAOQ,MAAM,MAC5B,OAAA8I,EAAI,SAAU,QACZ,GAAA,qBAAqBtI,KAAKX,GACnC,OAAOiJ,EAAIjJ,MACI,KAANA,GAAaL,EAAOO,IAAI,KAC1B,OAAA+I,EAAI,KAAM,eACF,KAANjJ,GAAaL,EAAOQ,MAAM,yCAC5B,OAAA8I,EAAI,SAAU,UACZ,GAAA,KAAKtI,KAAKX,GAEZ,OADPL,EAAOQ,MAAM,oDACN8I,EAAI,SAAU,UAC3B,GAAqB,KAANjJ,EACL,OAAAL,EAAOO,IAAI,MACbN,EAAMG,SAAWuJ,EACVA,EAAa3J,EAAQC,IACnBD,EAAOO,IAAI,MACpBP,EAAO4J,YACAN,EAAI,UAAW,YACbO,GAAkB7J,EAAQC,EAAO,IAC1CkJ,EAAWnJ,GACXA,EAAOQ,MAAM,qCACN8I,EAAI,SAAU,cAErBtJ,EAAOO,IAAI,KACJ+I,EAAI,WAAY,WAAYtJ,EAAO4C,YAElD,GAAqB,KAANvC,EAEF,OADPJ,EAAMG,SAAW0J,EACVA,EAAW9J,EAAQC,MACX,KAANI,GAA8B,KAAjBL,EAAO+J,OAEtB,OADP/J,EAAO4J,YACAN,EAAI,OAAQ,WACJ,KAANjJ,GAAaL,EAAOU,SAASqF,GAC/B,OAAAuD,EAAI,WAAY,YACxB,GAAgB,KAANjJ,GAAaL,EAAOQ,MAAM,QACnB,KAANH,GAAaL,EAAOQ,MAAM,QAAU,KAAKQ,KAAKhB,EAAOgK,OAAOC,MAAM,EAAGjK,EAAOkK,QAE/E,OADPlK,EAAO4J,YACAN,EAAI,UAAW,WACb,GAAAL,EAAejI,KAAKX,GAS7B,MARU,KAANA,GAAcJ,EAAMkK,SAAiC,KAAtBlK,EAAMkK,QAAQ3K,OAC3CQ,EAAOO,IAAI,KACH,KAANF,GAAmB,KAANA,GAAWL,EAAOO,IAAI,KAC9B,cAAcS,KAAKX,KAC5BL,EAAOO,IAAIF,GACD,KAANA,GAAWL,EAAOO,IAAIF,KAGpB,KAANA,GAAaL,EAAOO,IAAI,KAAa+I,EAAI,KACtCA,EAAI,WAAY,WAAYtJ,EAAO4C,WACjC,GAAAmD,EAAO/E,KAAKX,GAAK,CAC1BL,EAAOU,SAASqF,GACZ,IAAAqE,EAAOpK,EAAO4C,UACd,GAAkB,KAAlB3C,EAAMoK,SAAiB,CACrB,GAAApE,EAASqE,qBAAqBF,GAAO,CACnC,IAAAlE,EAAKD,EAASmE,GAClB,OAAOd,EAAIpD,EAAG1G,KAAM0G,EAAG1E,MAAO4I,EAC/B,CACD,GAAY,SAARA,GAAmBpK,EAAOQ,MAAM,4CAA4C,GACvE,OAAA8I,EAAI,QAAS,UAAWc,EAClC,CACM,OAAAd,EAAI,WAAY,WAAYc,EACpC,CACF,CAED,SAASV,EAAYtI,GACZ,OAAA,SAASpB,EAAQC,GACtB,IAAqBK,EAAjB8I,GAAU,EACV,GAAA5D,GAA+B,KAAjBxF,EAAO+J,QAAiB/J,EAAOQ,MAAM0I,GAE9C,OADPjJ,EAAMG,SAAWqJ,EACVH,EAAI,iBAAkB,QAE/B,KAAiC,OAAzBhJ,EAAON,EAAOM,UAChBA,GAAQc,GAAUgI,IACZA,GAACA,GAAmB,MAAR9I,EAGjB,OADF8I,IAASnJ,EAAMG,SAAWqJ,GACxBH,EAAI,SAAU,SAC3B,CACG,CAEQ,SAAAK,EAAa3J,EAAQC,GAErB,IADP,IAAsBI,EAAlBkK,GAAW,EACRlK,EAAKL,EAAOM,QAAQ,CACrB,GAAM,KAAND,GAAakK,EAAU,CACzBtK,EAAMG,SAAWqJ,EACjB,KACD,CACDc,EAAkB,KAANlK,CACb,CACM,OAAAiJ,EAAI,UAAW,UACvB,CAEQ,SAAAQ,EAAW9J,EAAQC,GAE1B,IADA,IAAqBK,EAAjB8I,GAAU,EACmB,OAAzB9I,EAAON,EAAOM,SAAiB,CACjC,IAAC8I,IAAoB,KAAR9I,GAAuB,KAARA,GAAeN,EAAOO,IAAI,MAAO,CAC/DN,EAAMG,SAAWqJ,EACjB,KACD,CACSL,GAACA,GAAmB,MAAR9I,CACvB,CACD,OAAOgJ,EAAI,QAAS,WAAYtJ,EAAO4C,UACxC,CAED,IAAI4H,EAAW,SAQN,SAAAC,EAAazK,EAAQC,GACxBA,EAAMyK,aAAYzK,EAAMyK,WAAa,MACzC,IAAIC,EAAQ3K,EAAOgK,OAAOY,QAAQ,KAAM5K,EAAOkK,OAC/C,KAAIS,EAAQ,GAAZ,CAEA,GAAI9E,EAAM,CACJ,IAAAgF,EAAI,6CAA6CzG,KAAKpE,EAAOgK,OAAOC,MAAMjK,EAAOkK,MAAOS,IACxFE,IAAGF,EAAQE,EAAEC,MAClB,CAGD,IADI,IAAApJ,EAAQ,EAAGqJ,GAAe,EACrBC,EAAML,EAAQ,EAAGK,GAAO,IAAKA,EAAK,CACzC,IAAI3K,EAAKL,EAAOgK,OAAOiB,OAAOD,GAC1BE,EAAUV,EAASI,QAAQvK,GAC3B,GAAA6K,GAAW,GAAKA,EAAU,EAAG,CAC/B,IAAKxJ,EAAO,GAAIsJ,EAAK,KAAQ,CACzB,GAAW,KAATtJ,EAAY,CAAY,KAANrB,IAA0B0K,GAAA,GAAM,KAAQ,CACjE,MAAU,GAAAG,GAAW,GAAKA,EAAU,IACjCxJ,OACO,GAAAqE,EAAO/E,KAAKX,GACN0K,GAAA,OACN,GAAA,UAAU/J,KAAKX,GACxB,QAAU2K,EAAK,CACb,GAAW,GAAPA,EAAU,OAEV,GADOhL,EAAOgK,OAAOiB,OAAOD,EAAM,IAC1B3K,GAAuC,MAAjCL,EAAOgK,OAAOiB,OAAOD,EAAM,GAAY,CAAEA,IAAO,KAAO,CAC1E,MACT,GAAiBD,IAAiBrJ,EAAO,GAC/BsJ,EACF,KACD,CACF,CACGD,IAAiBrJ,IAAOzB,EAAMyK,WAAaM,EA7BhC,CA8BhB,CAID,IAAIG,EAAc,CAAC3E,MAAQ,EAAM4E,QAAU,EAAMC,UAAY,EAAMrB,QAAU,EAC1DsB,QAAU,EAAMvJ,MAAQ,EAAM+G,QAAU,EAAM,kBAAkB,GAEnF,SAASyC,EAAUpJ,EAAUhB,EAAQ3B,EAAMgM,EAAOxJ,EAAMyJ,GACtD1J,KAAKI,SAAWA,EAChBJ,KAAKZ,OAASA,EACdY,KAAKvC,KAAOA,EACZuC,KAAKC,KAAOA,EACZD,KAAK0J,KAAOA,EACC,MAATD,IAAezJ,KAAKyJ,MAAQA,EACjC,CAEQ,SAAAE,EAAQzL,EAAO0L,GACtB,IAAK/F,EAAmB,OAAA,EACxB,IAAA,IAASgG,EAAI3L,EAAM4L,UAAWD,EAAGA,EAAIA,EAAEtL,KACrC,GAAIsL,EAAE/G,MAAQ8G,EAAgB,OAAA,EAChC,IAAA,IAAS3G,EAAK/E,EAAMgC,QAAS+C,EAAIA,EAAKA,EAAGhD,KACvC,IAAS4J,EAAI5G,EAAG8G,KAAMF,EAAGA,EAAIA,EAAEtL,KAC7B,GAAIsL,EAAE/G,MAAQ8G,EAAgB,OAAA,CAEnC,CAED,SAASI,EAAQ9L,EAAOuB,EAAOhC,EAAM8F,EAAStF,GAC5C,IAAIgM,EAAK/L,EAAM+L,GAQf,IALAhH,EAAG/E,MAAQA,EAAO+E,EAAGhF,OAASA,EAAWgF,EAAAiH,OAAS,KAAMjH,EAAGgH,GAAKA,EAAIhH,EAAGxD,MAAQA,EAE1EvB,EAAMkK,QAAQ/H,eAAe,WAChCnC,EAAMkK,QAAQqB,OAAQ,KAIlB,IADaQ,EAAGjI,OAASiI,EAAGE,MAAQxG,EAAWyG,EAAaC,GACjD5M,EAAM8F,GAAU,CAC7B,KAAM0G,EAAGjI,QAAUiI,EAAGA,EAAGjI,OAAS,GAAGsI,KACnCL,EAAGE,KAAHF,GACF,OAAIhH,EAAGiH,OAAejH,EAAGiH,OACb,YAARzM,GAAsBkM,EAAQzL,EAAOqF,GAAiB,aACnD9D,CACR,CAEJ,CAIG,IAAAwD,EAAK,CAAC/E,MAAO,KAAMkB,OAAQ,KAAM8K,OAAQ,KAAMD,GAAI,MACvD,SAASM,IACP,IAAA,IAASC,EAAIC,UAAUzI,OAAS,EAAGwI,GAAK,EAAGA,IAAKvH,EAAGgH,GAAG/G,KAAKuH,UAAUD,GACtE,CACD,SAAS/C,IAEA,OADF8C,EAAAG,MAAM,KAAMD,YACV,CACR,CACQ,SAAAE,EAAO7H,EAAM8H,GACpB,IAAA,IAASf,EAAIe,EAAMf,EAAGA,EAAIA,EAAEtL,KAAM,GAAIsL,EAAE/G,MAAQA,EAAa,OAAA,EACtD,OAAA,CACR,CACD,SAAS+H,EAASjB,GAChB,IAAI1L,EAAQ+E,EAAG/E,MAEf,GADA+E,EAAGiH,OAAS,MACPrG,EAAL,CACA,GAAI3F,EAAMgC,QACJ,GAAsB,OAAtBhC,EAAMkK,QAAQsB,MAAiBxL,EAAMgC,SAAWhC,EAAMgC,QAAQ4K,MAAO,CAEvE,IAAIC,EAAaC,EAAkBpB,EAAS1L,EAAMgC,SAClD,GAAkB,MAAd6K,EAEF,YADA7M,EAAMgC,QAAU6K,EAGnB,UAAWJ,EAAOf,EAAS1L,EAAM4L,WAEhC,YADA5L,EAAM4L,UAAY,IAAImB,EAAIrB,EAAS1L,EAAM4L,YAKzCxG,EAAa4H,aAAeP,EAAOf,EAAS1L,EAAMgN,cACpDhN,EAAMgN,WAAa,IAAID,EAAIrB,EAAS1L,EAAMgN,YAhB3B,CAiBlB,CACQ,SAAAF,EAAkBpB,EAAS1J,GAClC,GAAKA,EAET,IAAeA,EAAQ4K,MAAO,CACxB,IAAIK,EAAQH,EAAkBpB,EAAS1J,EAAQD,MAC/C,OAAKkL,EACDA,GAASjL,EAAQD,KAAaC,EAC3B,IAAIJ,EAAQqL,EAAOjL,EAAQ6J,MAAM,GAFrB,IAGpB,CAAU,OAAAY,EAAOf,EAAS1J,EAAQ6J,MAC1B7J,EAEA,IAAIJ,EAAQI,EAAQD,KAAM,IAAIgL,EAAIrB,EAAS1J,EAAQ6J,OAAO,EAClE,CAVQ,OAAA,IAWV,CAED,SAASqB,EAAWtI,GACX,MAAQ,UAARA,GAA4B,WAARA,GAA6B,aAARA,GAA+B,YAARA,GAA8B,YAARA,CAC9F,CAIQ,SAAAhD,EAAQG,EAAM8J,EAAMe,GAAS9K,KAAKC,KAAOA,EAAMD,KAAK+J,KAAOA,EAAM/J,KAAK8K,MAAQA,CAAO,CACrF,SAAAG,EAAInI,EAAMvE,GAAQyB,KAAK8C,KAAOA,EAAM9C,KAAKzB,KAAOA,CAAM,CAE3D,IAAA8M,EAAc,IAAIJ,EAAI,OAAQ,IAAIA,EAAI,YAAa,OACvD,SAASK,IACJrI,EAAA/E,MAAMgC,QAAU,IAAIJ,EAAQmD,EAAG/E,MAAMgC,QAAS+C,EAAG/E,MAAM4L,WAAW,GACrE7G,EAAG/E,MAAM4L,UAAYuB,CACtB,CACD,SAASE,IACJtI,EAAA/E,MAAMgC,QAAU,IAAIJ,EAAQmD,EAAG/E,MAAMgC,QAAS+C,EAAG/E,MAAM4L,WAAW,GACrE7G,EAAG/E,MAAM4L,UAAY,IACtB,CAED,SAAS0B,IACPvI,EAAG/E,MAAM4L,UAAY7G,EAAG/E,MAAMgC,QAAQ6J,KACtC9G,EAAG/E,MAAMgC,QAAU+C,EAAG/E,MAAMgC,QAAQD,IACrC,CAEQ,SAAAwL,EAAQhO,EAAMiM,GACrB,IAAIgC,EAAS,WACX,IAAIxN,EAAQ+E,EAAG/E,MAAOiC,EAASjC,EAAMkC,SACjC,GAAsB,QAAtBlC,EAAMkK,QAAQ3K,KAAgB0C,EAASjC,EAAMkK,QAAQhI,cAC3C,IAAA,IAAAuL,EAAQzN,EAAMkK,QAASuD,GAAuB,KAAdA,EAAMlO,MAAekO,EAAMlC,MAAOkC,EAAQA,EAAM1L,KAC5FE,EAASwL,EAAMvL,SACjBlC,EAAMkK,QAAU,IAAIoB,EAAUrJ,EAAQ8C,EAAGhF,OAAOmB,SAAU3B,EAAM,KAAMS,EAAMkK,QAASsB,EAC3F,EAEW,OADPgC,EAAOpB,KAAM,EACNoB,CACR,CACD,SAASE,IACP,IAAI1N,EAAQ+E,EAAG/E,MACXA,EAAMkK,QAAQnI,OACU,KAAtB/B,EAAMkK,QAAQ3K,OACVS,EAAAkC,SAAWlC,EAAMkK,QAAQhI,UAC3BlC,EAAAkK,QAAUlK,EAAMkK,QAAQnI,KAEjC,CAGD,SAAS4L,EAAOC,GACd,SAASC,EAAItO,GACX,OAAIA,GAAQqO,EAAerE,IACR,KAAVqE,GAAyB,KAARrO,GAAuB,KAARA,GAAuB,KAARA,EAAoB8M,IAChE9C,EAAKsE,GAEZ,OAAAA,CACR,CAEQ,SAAA1B,EAAU5M,EAAM9E,GACvB,MAAY,OAAR8E,EAAsBgK,EAAKgE,EAAQ,SAAU9S,GAAQqT,GAAQH,EAAO,KAAMD,GAClE,aAARnO,EAA4BgK,EAAKgE,EAAQ,QAASQ,EAAW5B,EAAWuB,GAChE,aAARnO,EAA4BgK,EAAKgE,EAAQ,QAASpB,EAAWuB,GACrD,aAARnO,EAA4BwF,EAAGhF,OAAOQ,MAAM,SAAS,GAASgJ,IAASA,EAAKgE,EAAQ,QAASS,EAAiBL,EAAO,KAAMD,GACnH,YAARnO,EAA2BgK,EAAKoE,EAAO,MAC/B,KAARpO,EAAoBgK,EAAKgE,EAAQ,KAAMF,EAAkBT,GAAOc,EAAQJ,GAChE,KAAR/N,EAAoBgK,IACZ,MAARhK,GAC2B,QAAzBwF,EAAG/E,MAAMkK,QAAQsB,MAAkBzG,EAAG/E,MAAM+L,GAAGhH,EAAG/E,MAAM+L,GAAGjI,OAAS,IAAM4J,GACzE3I,EAAA/E,MAAM+L,GAAGE,KAATlH,GACEwE,EAAKgE,EAAQ,QAASQ,EAAW5B,EAAWuB,EAAQO,KAEjD,YAAR1O,EAA2BgK,EAAK2E,IACxB,OAAR3O,EAAsBgK,EAAKgE,EAAQ,QAASF,EAAkBc,GAAShC,EAAWmB,EAAYI,GACtF,SAARnO,GAAoBqG,GAAiB,aAATnL,GAC9BsK,EAAGiH,OAAS,UACLzC,EAAKgE,EAAQ,OAAgB,SAARhO,EAAkBA,EAAO9E,GAAQ2T,GAAWV,IAE9D,YAARnO,EACEqG,GAAiB,WAATnL,GACVsK,EAAGiH,OAAS,UACLzC,EAAK4C,IACHvG,IAAkB,UAATnL,GAA8B,QAATA,GAA4B,QAATA,IAAoBsK,EAAGhF,OAAOQ,MAAM,UAAU,IACxGwE,EAAGiH,OAAS,UACC,QAATvR,EAAwB8O,EAAK8E,IACf,QAAT5T,EAAwB8O,EAAK+E,GAAUX,EAAO,YAAaY,GAAUZ,EAAO,MACzEpE,EAAKgE,EAAQ,QAASiB,GAASb,EAAO,KAAMJ,EAAQ,KAAMX,GAAOc,EAAQA,IAC5E9H,GAAiB,aAATnL,GACjBsK,EAAGiH,OAAS,UACLzC,EAAKgE,EAAQ,QAASrB,EAAYC,EAAWuB,IAC3C9H,GAAiB,YAATnL,GACjBsK,EAAGiH,OAAS,UACLzC,EAAK4C,IAEL5C,EAAKgE,EAAQ,QAASkB,IAGrB,UAARlP,EAAyBgK,EAAKgE,EAAQ,QAASQ,EAAWJ,EAAO,KAAMJ,EAAQ,IAAK,UAAWF,EACjET,GAAOc,EAAQA,EAAQJ,GAC7C,QAAR/N,EAAuBgK,EAAK2C,EAAYyB,EAAO,MACvC,WAARpO,EAA0BgK,EAAKoE,EAAO,MAC9B,SAARpO,EAAwBgK,EAAKgE,EAAQ,QAASH,EAAasB,EAAmBvC,EAAWuB,EAAQJ,GACzF,UAAR/N,EAAyBgK,EAAKgE,EAAQ,QAASoB,GAAajB,GACpD,UAARnO,EAAyBgK,EAAKgE,EAAQ,QAASqB,GAAalB,GACpD,SAARnO,EAAwBgK,EAAK4C,GACpB,KAAT1R,EAAqB8O,EAAK2C,EAAYC,GACnCE,EAAKkB,EAAQ,QAASrB,EAAYyB,EAAO,KAAMD,EACvD,CACD,SAASgB,EAAkBnP,GACzB,GAAY,KAARA,EAAa,OAAOgK,EAAKsF,GAAQlB,EAAO,KAC7C,CACQ,SAAAzB,EAAW3M,EAAM9E,GACjB,OAAAqU,EAAgBvP,EAAM9E,GAAO,EACrC,CACQ,SAAAsU,EAAkBxP,EAAM9E,GACxB,OAAAqU,EAAgBvP,EAAM9E,GAAO,EACrC,CACD,SAASsT,EAAUxO,GACjB,MAAY,KAARA,EAAoB8M,IACjB9C,EAAKgE,EAAQ,KAAMS,EAAiBL,EAAO,KAAMD,EACzD,CACQ,SAAAoB,EAAgBvP,EAAM9E,EAAOuU,GACpC,GAAIjK,EAAG/E,MAAMyK,YAAc1F,EAAGhF,OAAOkK,MAAO,CACtC,IAAAgF,EAAOD,EAAUE,GAAmBC,GACxC,GAAY,KAAR5P,EAAa,OAAOgK,EAAK6D,EAAaG,EAAQ,KAAM6B,GAASP,GAAQ,KAAMnB,EAAQC,EAAO,MAAOsB,EAAM3B,GAAU,GACpG,YAAR/N,EAAoB,OAAO8M,EAAKe,EAAaoB,GAASb,EAAO,MAAOsB,EAAM3B,EACpF,CAEG,IAAA+B,EAAUL,EAAUM,EAAuBC,EAC3C,OAAArE,EAAY/I,eAAe5C,GAAcgK,EAAK8F,GACtC,YAAR9P,EAA2BgK,EAAK2E,GAAamB,GACrC,SAAR9P,GAAoBqG,GAAiB,aAATnL,GAAyBsK,EAAGiH,OAAS,UAAkBzC,EAAKgE,EAAQ,QAASiC,GAAiB9B,IAClH,aAARnO,GAA+B,SAARA,EAAwBgK,EAAKyF,EAAUD,EAAoB7C,GAC1E,KAAR3M,EAAoBgK,EAAKgE,EAAQ,KAAMS,EAAiBL,EAAO,KAAMD,EAAQ2B,GACrE,YAAR9P,GAA8B,UAARA,EAAyBgK,EAAKyF,EAAUD,EAAoB7C,GAC1E,KAAR3M,EAAoBgK,EAAKgE,EAAQ,KAAMkC,GAAc/B,EAAQ2B,GACrD,KAAR9P,EAAoBmQ,GAAaC,GAAS,IAAK,KAAMN,GAC7C,SAAR9P,EAAwB8M,EAAKuD,EAAOP,GAC5B,OAAR9P,EAAsBgK,EAAKsG,GAAYb,IACpCzF,GACR,CACD,SAASyE,EAAgBzO,GACnBA,OAAAA,EAAKgB,MAAM,cAAsB8L,IAC9BA,EAAKH,EACb,CAEQ,SAAAqD,EAAmBhQ,EAAM9E,GAChC,MAAY,KAAR8E,EAAoBgK,EAAKyE,GACtBsB,EAAqB/P,EAAM9E,GAAO,EAC1C,CACQ,SAAA6U,EAAqB/P,EAAM9E,EAAOuU,GACrC,IAAAc,EAAgB,GAAXd,EAAmBO,EAAqBD,EAC7CS,EAAkB,GAAXf,EAAmB9C,EAAa6C,EAC3C,MAAY,MAARxP,EAAqBgK,EAAK6D,EAAa4B,EAAUE,GAAmBC,GAAW7B,GACvE,YAAR/N,EACE,UAAUwB,KAAKtG,IAAUmL,GAAiB,KAATnL,EAAqB8O,EAAKuG,GAC3DlK,GAAiB,KAATnL,GAAgBsK,EAAGhF,OAAOQ,MAAM,4BAA4B,GAC/DgJ,EAAKgE,EAAQ,KAAM6B,GAASb,GAAU,KAAMb,EAAQoC,GAChD,KAATrV,EAAqB8O,EAAK2C,EAAYyB,EAAO,KAAMoC,GAChDxG,EAAKwG,GAEF,SAARxQ,EAA0B8M,EAAKuD,EAAOE,GAC9B,KAARvQ,EACQ,KAARA,EAAoBmQ,GAAaX,EAAmB,IAAK,OAAQe,GACzD,KAARvQ,EAAoBgK,EAAKyG,GAAUF,GAC3B,KAARvQ,EAAoBgK,EAAKgE,EAAQ,KAAMS,EAAiBL,EAAO,KAAMD,EAAQoC,GAC7ElK,GAAiB,MAATnL,GAAiBsK,EAAGiH,OAAS,UAAkBzC,EAAKgF,GAAUuB,IAC9D,UAARvQ,GACCwF,EAAA/E,MAAMoK,SAAWrF,EAAGiH,OAAS,WAC7BjH,EAAAhF,OAAOkQ,OAAOlL,EAAGhF,OAAOgL,IAAMhG,EAAGhF,OAAOkK,MAAQ,GAC5CV,EAAKwG,SAHd,OALA,CAUD,CACQ,SAAAH,EAAMrQ,EAAM9E,GACnB,MAAY,SAAR8E,EAAwB8M,IACS,MAAjC5R,EAAMuP,MAAMvP,EAAMqJ,OAAS,GAAmByF,EAAKqG,GAChDrG,EAAKyE,EAAiBkC,EAC9B,CACD,SAASA,EAAc3Q,GACrB,GAAY,KAARA,EAGF,OAFAwF,EAAGiH,OAAS,WACZjH,EAAG/E,MAAMG,SAAW0J,EACbN,EAAKqG,EAEf,CACD,SAAST,GAAU5P,GAEjB,OADaiL,EAAAzF,EAAGhF,OAAQgF,EAAG/E,OACpBqM,EAAa,KAAR9M,EAAc4M,EAAYD,EACvC,CACD,SAASgD,GAAiB3P,GAExB,OADaiL,EAAAzF,EAAGhF,OAAQgF,EAAG/E,OACpBqM,EAAa,KAAR9M,EAAc4M,EAAY4C,EACvC,CACD,SAASc,GAAYb,GACnB,OAAO,SAASzP,GACd,MAAY,KAARA,EAAoBgK,EAAKyF,EAAUmB,GAAgBC,IACtC,YAAR7Q,GAAsBqG,EAAa2D,EAAK8G,GAAerB,EAAUM,EAAuBC,GACrFlD,EAAK2C,EAAUD,EAAoB7C,EACrD,CACG,CACQ,SAAAkE,GAAOE,EAAG7V,GACjB,GAAa,UAATA,EAA4C,OAAvBsK,EAAGiH,OAAS,UAAkBzC,EAAKgG,EAC7D,CACQ,SAAAY,GAAcG,EAAG7V,GACxB,GAAa,UAATA,EAA4C,OAAvBsK,EAAGiH,OAAS,UAAkBzC,EAAK+F,EAC7D,CACD,SAASb,GAAWlP,GAClB,MAAY,KAARA,EAAoBgK,EAAKmE,EAAQvB,GAC9BE,EAAKkD,EAAoB5B,EAAO,KAAMD,EAC9C,CACD,SAASsC,GAASzQ,GAChB,GAAY,YAARA,EAA6C,OAAxBwF,EAAGiH,OAAS,WAAmBzC,GACzD,CACQ,SAAAoG,GAAQpQ,EAAM9E,GACrB,MAAY,SAAR8E,GACFwF,EAAGiH,OAAS,WACLzC,EAAKoG,KACK,YAARpQ,GAAkC,WAAZwF,EAAGxD,OAClCwD,EAAGiH,OAAS,WACC,OAATvR,GAA2B,OAATA,EAAuB8O,EAAKgH,KAE9C3K,GAAQb,EAAG/E,MAAMyK,YAAc1F,EAAGhF,OAAOkK,QAAUW,EAAI7F,EAAGhF,OAAOQ,MAAM,YAAY,MACrFwE,EAAG/E,MAAMyK,WAAa1F,EAAGhF,OAAOgL,IAAMH,EAAE,GAAG9G,QACtCyF,EAAKiH,MACK,UAARjR,GAA4B,UAARA,GAC7BwF,EAAGiH,OAASzG,EAAa,WAAcR,EAAGxD,MAAQ,YAC3CgI,EAAKiH,KACK,kBAARjR,EACFgK,EAAKiH,IACH5K,GAAQsH,EAAWzS,IAC5BsK,EAAGiH,OAAS,UACLzC,EAAKoG,KACK,KAARpQ,EACFgK,EAAK2C,EAAYuE,GAAW9C,EAAO,KAAM6C,IAC/B,UAARjR,EACFgK,EAAKwF,EAAmByB,IACb,KAAT/V,GACTsK,EAAGiH,OAAS,UACLzC,EAAKoG,KACK,KAARpQ,EACF8M,EAAKmE,SADlB,EAnBU,IAAA5F,CAsBP,CACD,SAAS2F,GAAahR,GACpB,MAAY,YAARA,EAA2B8M,EAAKmE,KACpCzL,EAAGiH,OAAS,WACLzC,EAAK2E,IACb,CACD,SAASsC,GAAUjR,GACjB,MAAY,KAARA,EAAoBgK,EAAKwF,GACjB,KAARxP,EAAoB8M,EAAK6B,SAA7B,CACD,CACQ,SAAAkB,GAASsB,EAAMC,EAAKC,GAClB,SAAAC,EAAQtR,EAAM9E,GACrB,GAAImW,EAAMA,EAAIjG,QAAQpL,IAAQ,EAAa,KAARA,EAAa,CAC1C,IAAA6M,EAAMrH,EAAG/E,MAAMkK,QAEZ,MADS,QAAZkC,EAAIZ,OAAoBY,EAAArB,KAAOqB,EAAIrB,KAAO,GAAK,GAC5CxB,GAAK,SAAShK,EAAM9E,GACrB8E,OAAAA,GAAQoR,GAAOlW,GAASkW,EAAYtE,IACjCA,EAAKqE,EACb,GAAEG,EACJ,CACGtR,OAAAA,GAAQoR,GAAOlW,GAASkW,EAAYpH,IACpCqH,GAAOA,EAAIjG,QAAQ,MAAO,EAAW0B,EAAKqE,GACvCnH,EAAKoE,EAAOgD,GACpB,CACM,OAAA,SAASpR,EAAM9E,GAChB8E,OAAAA,GAAQoR,GAAOlW,GAASkW,EAAYpH,IACjC8C,EAAKqE,EAAMG,EACxB,CACG,CACQ,SAAAnB,GAAagB,EAAMC,EAAKnF,GAC/B,IAAA,IAASc,EAAI,EAAGA,EAAIC,UAAUzI,OAAQwI,IACpCvH,EAAGgH,GAAG/G,KAAKuH,UAAUD,IAChB,OAAA/C,EAAKgE,EAAQoD,EAAKnF,GAAO4D,GAASsB,EAAMC,GAAMjD,EACtD,CACD,SAASd,GAAMrN,GACb,MAAY,KAARA,EAAoBgK,IACjB8C,EAAKF,EAAWS,GACxB,CACQ,SAAA6D,GAAUlR,EAAM9E,GACvB,GAAImL,EAAM,CACR,GAAY,KAARrG,EAAa,OAAOgK,EAAKgF,IAC7B,GAAa,KAAT9T,EAAc,OAAO8O,EAAKkH,GAC/B,CACF,CACQ,SAAAK,GAAcvR,EAAM9E,GACvB,GAAAmL,IAAiB,KAARrG,GAAwB,MAAT9E,GAAgB,OAAO8O,EAAKgF,GACzD,CACD,SAASwC,GAAaxR,GAChB,GAAAqG,GAAgB,KAARrG,EACV,OAAIwF,EAAGhF,OAAOQ,MAAM,kBAAkB,GAAegJ,EAAK2C,EAAY8E,GAAMzC,IAChEhF,EAAKgF,GAEpB,CACQ,SAAAyC,GAAKV,EAAG7V,GACf,GAAa,MAATA,EAEF,OADAsK,EAAGiH,OAAS,UACLzC,GAEV,CACQ,SAAAgF,GAAShP,EAAM9E,GACtB,MAAa,SAATA,GAA6B,UAATA,GAA8B,SAATA,GAA6B,YAATA,GAC/DsK,EAAGiH,OAAS,UACLzC,EAAc,UAAT9O,EAAoBsU,EAAoBR,KAE1C,YAARhP,GAA+B,QAAT9E,GACxBsK,EAAGiH,OAAS,OACLzC,EAAK0H,KAED,KAATxW,GAAyB,KAATA,EAAqB8O,EAAKgF,IAClC,UAARhP,GAA4B,UAARA,GAA4B,QAARA,EAAuBgK,EAAK0H,IAC5D,KAAR1R,EAAoBgK,EAAKgE,EAAQ,KAAM6B,GAASb,GAAU,IAAK,KAAMb,EAAQuD,IACrE,KAAR1R,EAAoBgK,EAAKgE,EAAQ,KAAM2D,GAAWxD,EAAQuD,IAClD,KAAR1R,EAAoBgK,EAAK6F,GAAS+B,GAAS,KAAMC,GAAiBH,IAC1D,KAAR1R,EAAoBgK,EAAK6F,GAASb,GAAU,KAAMA,IAC1C,SAARhP,EAA0B8M,EAAKgF,GAAWJ,SAA9C,CACD,CACD,SAASG,GAAgB7R,GACvB,GAAY,MAARA,EAAc,OAAOgK,EAAKgF,GAC/B,CACD,SAAS2C,GAAU3R,GACbA,OAAAA,EAAKgB,MAAM,YAAoBgJ,IACvB,KAARhK,GAAuB,KAARA,EAAoBgK,EAAK2H,IACrC7E,EAAKiF,GAAUJ,GACvB,CACQ,SAAAI,GAAS/R,EAAM9E,GACtB,MAAY,YAAR8E,GAAkC,WAAZwF,EAAGxD,OAC3BwD,EAAGiH,OAAS,WACLzC,EAAK+H,KACM,KAAT7W,GAAwB,UAAR8E,GAA4B,UAARA,EACtCgK,EAAK+H,IACK,KAAR/R,EACFgK,EAAKgF,IACK,KAARhP,EACFgK,EAAKoE,EAAO,YAAamD,GAAenD,EAAO,KAAM2D,IAC3C,KAAR/R,EACF8M,EAAKkF,GAAcD,IAChB/R,EAAKgB,MAAM,mBAAZ,EACFgJ,GAEV,CACQ,SAAA8H,GAAU9R,EAAM9E,GACvB,MAAY,SAAR8E,EAAwB8M,IACS,MAAjC5R,EAAMuP,MAAMvP,EAAMqJ,OAAS,GAAmByF,EAAK8H,IAChD9H,EAAKgF,GAAUiD,GACvB,CACD,SAASA,GAAkBjS,GACzB,GAAY,KAARA,EAGF,OAFAwF,EAAGiH,OAAS,WACZjH,EAAG/E,MAAMG,SAAW0J,EACbN,EAAK8H,GAEf,CACQ,SAAAF,GAAQ5R,EAAM9E,GACjB8E,MAAQ,YAARA,GAAsBwF,EAAGhF,OAAOQ,MAAM,YAAY,IAAmB,KAAT9F,EAAqB8O,EAAK4H,IAC9E,KAAR5R,EAAoBgK,EAAKgF,IACjB,UAARhP,EAAyBgK,EAAK4H,IAC3B9E,EAAKkC,GACb,CACQ,SAAA0C,GAAU1R,EAAM9E,GACvB,MAAa,KAATA,EAAqB8O,EAAKgE,EAAQ,KAAM6B,GAASb,GAAU,KAAMb,EAAQuD,IAChE,KAATxW,GAAwB,KAAR8E,GAAwB,KAAT9E,EAAqB8O,EAAKgF,IACjD,KAARhP,EAAoBgK,EAAKgF,GAAUZ,EAAO,KAAMsD,IACvC,WAATxW,GAA+B,cAATA,GAAyBsK,EAAGiH,OAAS,UAAkBzC,EAAKgF,KACzE,KAAT9T,EAAqB8O,EAAKgF,GAAUZ,EAAO,KAAMY,SAArD,CACD,CACQ,SAAA8B,GAAcC,EAAG7V,GACxB,GAAa,KAATA,EAAqB,OAAA8O,EAAKgE,EAAQ,KAAM6B,GAASb,GAAU,KAAMb,EAAQuD,GAC9E,CACD,SAASQ,KACA,OAAApF,EAAKkC,GAAUmD,GACvB,CACQ,SAAAA,GAAiBpB,EAAG7V,GAC3B,GAAa,KAATA,EAAc,OAAO8O,EAAKgF,GAC/B,CACQ,SAAAT,GAAOwC,EAAG7V,GACjB,MAAa,QAATA,GAAkBsK,EAAGiH,OAAS,UAAkBzC,EAAK8E,KAClDhC,EAAKmC,GAASiC,GAAWkB,GAAaC,GAC9C,CACQ,SAAApD,GAAQjP,EAAM9E,GACjB,OAAAmL,GAAQsH,EAAWzS,IAAUsK,EAAGiH,OAAS,UAAkBzC,EAAKiF,KACxD,YAARjP,GAAsBoN,EAASlS,GAAe8O,KACtC,UAARhK,EAAyBgK,EAAKiF,IACtB,KAARjP,EAAoBmQ,GAAamC,GAAY,KACrC,KAARtS,EAAoBmQ,GAAaoC,GAAa,UAAlD,CACD,CACQ,SAAAA,GAAYvS,EAAM9E,GACrB8E,MAAQ,YAARA,GAAuBwF,EAAGhF,OAAOQ,MAAM,SAAS,IAIxC,YAARhB,IAAoBwF,EAAGiH,OAAS,YACxB,UAARzM,EAAyBgK,EAAKiF,IACtB,KAARjP,EAAoB8M,IACZ,KAAR9M,EAAoBgK,EAAK2C,EAAYyB,EAAO,KAAMA,EAAO,KAAMmE,IAC5DvI,EAAKoE,EAAO,KAAMa,GAASmD,MAPhChF,EAASlS,GACF8O,EAAKoI,IAOf,CACD,SAASE,KACA,OAAAxF,EAAKmC,GAASmD,GACtB,CACQ,SAAAA,GAAYI,EAAOtX,GAC1B,GAAa,KAATA,EAAc,OAAO8O,EAAKwF,EAC/B,CACD,SAAS6C,GAAWrS,GAClB,GAAY,KAARA,EAAa,OAAOgK,EAAKuE,GAC9B,CACQ,SAAAG,GAAU1O,EAAM9E,GACnB8E,GAAQ,aAARA,GAAgC,QAAT9E,EAAiB,OAAO8O,EAAKgE,EAAQ,OAAQ,QAASpB,EAAWuB,EAC7F,CACQ,SAAAS,GAAQ5O,EAAM9E,GACrB,MAAa,SAATA,EAAyB8O,EAAK4E,IACtB,KAAR5O,EAAoBgK,EAAKgE,EAAQ,KAAMyE,GAAUtE,QAArD,CACD,CACD,SAASsE,GAASzS,GAChB,MAAY,OAARA,EAAsBgK,EAAKuE,GAAQmE,IAC3B,YAAR1S,EAA2BgK,EAAK0I,IAC7B5F,EAAK4F,GACb,CACQ,SAAAA,GAAS1S,EAAM9E,GACtB,MAAY,KAAR8E,EAAoBgK,IACZ,KAARhK,EAAoBgK,EAAK0I,IAChB,MAATxX,GAA0B,MAATA,GAAiBsK,EAAGiH,OAAS,UAAkBzC,EAAK2C,EAAY+F,KAC9E5F,EAAKH,EAAY+F,GACzB,CACQ,SAAA/D,GAAY3O,EAAM9E,GACzB,MAAa,KAATA,GAAesK,EAAGiH,OAAS,UAAkBzC,EAAK2E,KAC1C,YAAR3O,GAAqBoN,EAASlS,GAAe8O,EAAK2E,KAC1C,KAAR3O,EAAoBgK,EAAK6D,EAAaG,EAAQ,KAAM6B,GAASP,GAAQ,KAAMnB,EAAQqD,GAAc5E,EAAWmB,GAC5G1H,GAAiB,KAATnL,EAAqB8O,EAAKgE,EAAQ,KAAM6B,GAASqC,GAAW,KAAM/D,EAAQQ,SAAtF,CACD,CACQ,SAAAqD,GAAahS,EAAM9E,GAC1B,MAAa,KAATA,GAAesK,EAAGiH,OAAS,UAAkBzC,EAAKgI,KAC1C,YAARhS,GAAqBoN,EAASlS,GAAe8O,EAAKgI,KAC1C,KAARhS,EAAoBgK,EAAK6D,EAAaG,EAAQ,KAAM6B,GAASP,GAAQ,KAAMnB,EAAQqD,GAAczD,GACjG1H,GAAiB,KAATnL,EAAqB8O,EAAKgE,EAAQ,KAAM6B,GAASqC,GAAW,KAAM/D,EAAQ6D,SAAtF,CACD,CACQ,SAAAjD,GAAS/O,EAAM9E,GAClB8E,MAAQ,WAARA,GAA6B,YAARA,GACvBwF,EAAGiH,OAAS,OACLzC,EAAK+E,KACM,KAAT7T,EACF8O,EAAKgE,EAAQ,KAAM6B,GAASqC,GAAW,KAAM/D,QAD1D,CAGG,CACQ,SAAAmB,GAAOtP,EAAM9E,GAEpB,MADa,KAATA,GAAc8O,EAAK2C,EAAY2C,IACvB,UAARtP,EAAyBgK,EAAKsF,IAC9BjJ,GAAQsH,EAAWzS,IAAUsK,EAAGiH,OAAS,UAAkBzC,EAAKsF,KAChEjJ,GAAgB,QAARrG,EAAuBgK,EAAKkH,GAAWkB,IAC5CtF,EAAKmC,GAASiC,GAAWkB,GACjC,CACQ,SAAAnC,GAAgBjQ,EAAM9E,GAE7B,MAAY,YAAR8E,EAA2B6O,GAAU7O,EAAM9E,GACxCyX,GAAe3S,EAAM9E,EAC7B,CACQ,SAAA2T,GAAU7O,EAAM9E,GACvB,GAAY,YAAR8E,EAAsC,OAAjBoN,EAASlS,GAAe8O,EAAK2I,GACvD,CACQ,SAAAA,GAAe3S,EAAM9E,GAC5B,MAAa,KAATA,EAAqB8O,EAAKgE,EAAQ,KAAM6B,GAASqC,GAAW,KAAM/D,EAAQwE,IACjE,WAATzX,GAA+B,cAATA,GAA0BmL,GAAgB,KAARrG,GAC7C,cAAT9E,IAAuBsK,EAAGiH,OAAS,WAChCzC,EAAK3D,EAAO2I,GAAWrC,EAAYgG,KAEhC,KAAR3S,EAAoBgK,EAAKgE,EAAQ,KAAM4E,GAAWzE,QAAtD,CACD,CACQ,SAAAyE,GAAU5S,EAAM9E,GACvB,MAAY,SAAR8E,GACS,YAARA,IACU,UAAT9E,GAA8B,OAATA,GAA2B,OAATA,GAAmBmL,GAAQsH,EAAWzS,KAC9EsK,EAAGhF,OAAOQ,MAAM,0BAA0B,IAC7CwE,EAAGiH,OAAS,UACLzC,EAAK4I,KAEF,YAAR5S,GAAkC,WAAZwF,EAAGxD,OAC3BwD,EAAGiH,OAAS,WACLzC,EAAK6I,GAAYD,KAEd,UAAR5S,GAA4B,UAARA,EAAyBgK,EAAK6I,GAAYD,IACtD,KAAR5S,EACKgK,EAAK2C,EAAYuE,GAAW9C,EAAO,KAAMyE,GAAYD,IACjD,KAAT1X,GACFsK,EAAGiH,OAAS,UACLzC,EAAK4I,KAEVvM,GAAgB,KAARrG,EAAoB8M,EAAKkF,GAAcY,IACvC,KAAR5S,GAAuB,KAARA,EAAoBgK,EAAK4I,IAChC,KAAR5S,EAAoBgK,IACX,KAAT9O,EAAqB8O,EAAK2C,EAAYiG,SAA1C,CACD,CACQ,SAAAC,GAAW7S,EAAM9E,GACxB,GAAa,KAATA,EAAc,OAAO8O,EAAK6I,IAC9B,GAAa,KAAT3X,EAAc,OAAO8O,EAAK6I,IAC9B,GAAY,KAAR7S,EAAoB,OAAAgK,EAAKgF,GAAUoD,IACvC,GAAa,KAATlX,EAAc,OAAO8O,EAAKwF,GAC1B,IAAA/M,EAAU+C,EAAG/E,MAAMkK,QAAQnI,KACxB,OAAAsK,EAD4CrK,GAA2B,aAAhBA,EAAQwJ,KAC5C+F,GAAerD,GAC1C,CACQ,SAAAS,GAAYpP,EAAM9E,GACzB,MAAa,KAATA,GAAgBsK,EAAGiH,OAAS,UAAkBzC,EAAK8I,GAAW1E,EAAO,OAC5D,WAATlT,GAAsBsK,EAAGiH,OAAS,UAAkBzC,EAAK2C,EAAYyB,EAAO,OACpE,KAARpO,EAAoBgK,EAAK6F,GAASkD,GAAa,KAAMD,GAAW1E,EAAO,MACpEtB,EAAKF,EACb,CACQ,SAAAmG,GAAY/S,EAAM9E,GACzB,MAAa,MAATA,GAAiBsK,EAAGiH,OAAS,UAAkBzC,EAAKoE,EAAO,cACnD,YAARpO,EAA2B8M,EAAK0C,EAAmBuD,SAAvD,CACD,CACD,SAAS1D,GAAYrP,GACnB,MAAY,UAARA,EAAyBgK,IACjB,KAARhK,EAAoB8M,EAAKH,GACjB,KAAR3M,EAAoB8M,EAAKkD,GACtBlD,EAAKkG,GAAYC,GAAkBH,GAC3C,CACQ,SAAAE,GAAWhT,EAAM9E,GACxB,MAAY,KAAR8E,EAAoBmQ,GAAa6C,GAAY,MACrC,YAARhT,GAAoBoN,EAASlS,GACpB,KAATA,IAAcsK,EAAGiH,OAAS,WACvBzC,EAAKkJ,IACb,CACD,SAASD,GAAiBjT,GACxB,GAAY,KAARA,EAAoB,OAAAgK,EAAKgJ,GAAYC,GAC1C,CACQ,SAAAC,GAAQV,EAAOtX,GACtB,GAAa,MAATA,EAAwC,OAAvBsK,EAAGiH,OAAS,UAAkBzC,EAAKgJ,GACzD,CACQ,SAAAF,GAAUN,EAAOtX,GACxB,GAAa,QAATA,EAA0C,OAAvBsK,EAAGiH,OAAS,UAAkBzC,EAAK2C,EAC3D,CACD,SAASuD,GAAalQ,GACpB,MAAY,KAARA,EAAoBgK,IACjB8C,EAAK+C,GAASL,EAAmB,KACzC,CACD,SAASV,KACP,OAAOhC,EAAKkB,EAAQ,QAASiB,GAASb,EAAO,KAAMJ,EAAQ,KAAM6B,GAASsD,GAAY,KAAMhF,EAAQA,EACrG,CACD,SAASgF,KACA,OAAArG,EAAKmC,GAASmD,GACtB,CAEQ,SAAAgB,GAAqB3S,EAAO2D,GACnC,MAAyB,YAAlB3D,EAAMoK,UAA4C,KAAlBpK,EAAMoK,UAC3CpB,EAAejI,KAAK4C,EAAUqH,OAAO,KACrC,OAAOjK,KAAK4C,EAAUqH,OAAO,GAChC,CAEQ,SAAApB,GAAkB7J,EAAQC,EAAOiQ,GACjC,OAAAjQ,EAAMG,UAAYqJ,GACvB,iFAAiFzI,KAAKf,EAAMoK,WACzE,SAAlBpK,EAAMoK,UAAuB,SAASrJ,KAAKhB,EAAOgK,OAAOC,MAAM,EAAGjK,EAAOgL,KAAOkF,GAAU,IAC9F,CAIM,OArhBK7C,EAAAhB,IAAMiB,EAAiBjB,KAAM,EAKzCkB,EAAWlB,KAAM,EAoBjBsB,EAAOtB,KAAM,EA4fN,CACL/I,WAAY,SAASuP,GACnB,IAAI5S,EAAQ,CACVG,SAAUqJ,EACVY,SAAU,MACV2B,GAAI,GACJ7B,QAAS,IAAIoB,GAAWsH,GAAc,GAAKnT,EAAY,EAAG,SAAS,GACnEmM,UAAWxG,EAAawG,UACxB5J,QAASoD,EAAawG,WAAa,IAAIhK,EAAQ,KAAM,MAAM,GAC3DM,SAAU0Q,GAAc,GAInB,OAFHxN,EAAa4H,YAAgD,iBAA3B5H,EAAa4H,aACjDhN,EAAMgN,WAAa5H,EAAa4H,YAC3BhN,CACR,EAEDuD,MAAO,SAASxD,EAAQC,GAOtB,GANID,EAAOyD,QACJxD,EAAMkK,QAAQ/H,eAAe,WAChCnC,EAAMkK,QAAQqB,OAAQ,GAClBvL,EAAAkC,SAAWnC,EAAO0D,cACxB+G,EAAazK,EAAQC,IAEnBA,EAAMG,UAAYuJ,GAAgB3J,EAAO2D,WAAmB,OAAA,KAChE,IAAInC,EAAQvB,EAAMG,SAASJ,EAAQC,GACnC,MAAY,WAART,EAA0BgC,GAC9BvB,EAAMoK,SAAmB,YAAR7K,GAAkC,MAAX8F,GAA8B,MAAXA,EAA8B9F,EAAX,SACvEuM,EAAQ9L,EAAOuB,EAAOhC,EAAM8F,EAAStF,GAC7C,EAEDkC,OAAQ,SAASjC,EAAO2D,GACtB,GAAI3D,EAAMG,UAAYuJ,GAAgB1J,EAAMG,UAAY0J,EAAY,OAAO9O,EAAW8I,KACtF,GAAI7D,EAAMG,UAAYqJ,EAAkB,OAAA,EACpC,IAAuEqJ,EAAvEC,EAAYnP,GAAaA,EAAUqH,OAAO,GAAId,EAAUlK,EAAMkK,QAE9D,IAAC,aAAanJ,KAAK4C,GAAqB,IAAA,IAAA2I,EAAItM,EAAM+L,GAAGjI,OAAS,EAAGwI,GAAK,IAAKA,EAAG,CAC5E,IAAAyG,EAAI/S,EAAM+L,GAAGO,GACjB,GAAIyG,GAAKrF,EAAQxD,EAAUA,EAAQnI,UAC1B,GAAAgR,GAAK9E,IAAa8E,GAAKzF,EAAY,KAC7C,CACO,MAAgB,QAAhBpD,EAAQ3K,MAAkC,QAAhB2K,EAAQ3K,QACrB,KAAbuT,IAAsBD,EAAM7S,EAAM+L,GAAG/L,EAAM+L,GAAGjI,OAAS,MACjC+O,GAAOtD,GAAsBsD,GAAOvD,KACpC,mBAAmBvO,KAAK4C,KACpDuG,EAAUA,EAAQnI,KAChBuD,GAAmC,KAAhB4E,EAAQ3K,MAAoC,QAArB2K,EAAQnI,KAAKxC,OACzD2K,EAAUA,EAAQnI,MACpB,IAAIxC,EAAO2K,EAAQ3K,KAAMyT,EAAUF,GAAavT,EAEhD,MAAY,UAARA,EAAyB2K,EAAQhI,UAA8B,YAAlBlC,EAAMoK,UAA4C,KAAlBpK,EAAMoK,SAAkBF,EAAQsB,KAAK1H,OAAS,EAAI,GAClH,QAARvE,GAA+B,KAAbuT,EAAyB5I,EAAQhI,SAC3C,QAAR3C,EAAuB2K,EAAQhI,SAAWzC,EAClC,QAARF,EACA2K,EAAQhI,UAAYyQ,GAAqB3S,EAAO2D,GAAa2B,GAAmB7F,EAAa,GAC7E,UAAhByK,EAAQsB,MAAqBwH,GAA8C,GAAnC5N,EAAa6N,mBAErD/I,EAAQqB,MAAcrB,EAAQhJ,QAAU8R,EAAU,EAAI,GACnD9I,EAAQhI,UAAY8Q,EAAU,EAAIvT,GAFrCyK,EAAQhI,UAAY,sBAAsBnB,KAAK4C,GAAalE,EAAa,EAAIA,EAGvF,EAED4E,cAAe,oCACfC,kBAAmBmB,EAAW,KAAO,KACrClB,gBAAiBkB,EAAW,KAAO,KACnCyN,qBAAsBzN,EAAW,KAAO,MACxC0N,YAAa1N,EAAW,KAAO,KAC/B2N,KAAM,QACNC,cAAe,iBAEf5O,WAAYgB,EAAW,OAAS,aAChCF,aACAE,WAEAmE,qBAEA0J,eAAgB,SAAStT,GACf8L,EAAA9L,EAAO,OAAQ,OAAQ,OAAQ,IAAIjF,EAAWwY,aAAa,GAAI,EAAG,MAC3E,EAEL,IAEWxY,EAAAyY,eAAe,YAAa,aAAc,SAE1CzY,EAAAmK,WAAW,kBAAmB,cAC9BnK,EAAAmK,WAAW,kBAAmB,cAC9BnK,EAAAmK,WAAW,yBAA0B,cACrCnK,EAAAmK,WAAW,2BAA4B,cACvCnK,EAAAmK,WAAW,yBAA0B,cAChDnK,EAAWmK,WAAW,mBAAoB,CAAEN,KAAM,aAAcc,MAAM,IACtE3K,EAAWmK,WAAW,qBAAsB,CAAEN,KAAM,aAAcc,MAAM,IACxE3K,EAAWmK,WAAW,4BAA6B,CAAEN,KAAM,aAAcc,MAAM,IAC/E3K,EAAWmK,WAAW,sBAAuB,CAAEN,KAAM,aAAcY,QAAQ,IAC3EzK,EAAWmK,WAAW,kBAAmB,CAAEN,KAAM,aAAciB,YAAY,IAC3E9K,EAAWmK,WAAW,yBAA0B,CAAEN,KAAM,aAAciB,YAAY,WAn7BtE9K,GCAT,SAASA,GAGV,IAAI0Y,EAAc,CAChBC,OAAQ,CACN,CAAC,OAAQ,sBAAuB,cAChC,CAAC,OAAQ,kEAAmE,cAC5E,CAAC,OAAQ,IAAK,cACd,CAAC,KAAM,KAAM,eAEfnS,MAAQ,CACN,CAAC,OAAQ,SAAU,OACnB,CAAC,OAAQ,oCAAqC,OAC9C,CAAC,OAAQ,IAAK,cACd,CAAC,KAAM,KAAM,SAIR,SAAAoS,EAAY5T,EAAQ6T,EAAKrS,GAChC,IAAIsS,EAAM9T,EAAO4C,UAAWkC,EAAQgP,EAAIC,OAAOF,GAOxC,OANH/O,GAAY,EACP9E,EAAAkQ,OAAO4D,EAAI/P,OAASe,GAClBgP,EAAItT,MAAM,WACZR,EAAAkQ,OAAO4D,EAAI/P,QACb/D,EAAOQ,MAAMqT,GAAK,IAAQ7T,EAAOQ,MAAMsT,IAEvCtS,CACR,CAED,IAAIwS,EAAkB,CAAA,EACtB,SAASC,EAAcC,GACjB,IAAA5I,EAAS0I,EAAgBE,GACzB,OAAA5I,IACG0I,EAAgBE,GAAQ,IAAIC,OAAO,OAASD,EAAO,wCAC3D,CAEQ,SAAAE,EAAaC,EAAMH,GAC1B,IAAI1T,EAAQ6T,EAAK7T,MAAMyT,EAAcC,IAC9B,OAAA1T,EAAQ,gBAAgB4D,KAAK5D,EAAM,IAAI,GAAK,EACpD,CAEQ,SAAA8T,EAAaxT,EAASyT,GACtB,OAAA,IAAIJ,QAAQI,EAAW,IAAM,IAAM,SAAYzT,EAAU,QAAS,IAC1E,CAEQ,SAAA0T,EAAQC,EAAMC,GACrB,IAAA,IAASC,KAAOF,EAGd,IAFA,IAAIG,EAAOF,EAAGC,KAASD,EAAGC,GAAO,IAC7BzY,EAASuY,EAAKE,GACTpI,EAAIrQ,EAAO6H,OAAS,EAAGwI,GAAK,EAAGA,IACjCqI,EAAAC,QAAQ3Y,EAAOqQ,GAEzB,CAEQ,SAAAuI,EAAiBC,EAASC,GACjC,IAAA,IAASzI,EAAI,EAAGA,EAAIwI,EAAQhR,OAAQwI,IAAK,CACnC,IAAA0I,EAAOF,EAAQxI,GACnB,IAAK0I,EAAK,IAAMA,EAAK,GAAGjU,KAAKoT,EAAaY,EAASC,EAAK,KAAM,OAAOA,EAAK,EAC3E,CACF,CAEDja,EAAWqE,WAAW,aAAa,SAAUM,EAAQ0F,GAC/C,IAAAxF,EAAW7E,EAAWka,QAAQvV,EAAQ,CACxCkF,KAAM,MACNhF,UAAU,EACVoE,yBAA0BoB,EAAapB,yBACvCD,0BAA2BqB,EAAarB,0BACxC5E,oBAAqBiG,EAAajG,sBAGhC+V,EAAO,CAAA,EACPC,EAAa/P,GAAgBA,EAAa8P,KAAME,EAAehQ,GAAgBA,EAAaiQ,YAG5F,GAFJd,EAAQd,EAAayB,GACjBC,GAAYZ,EAAQY,EAAYD,GAChCE,EAAc,IAAA,IAAS9I,EAAI8I,EAAatR,OAAS,EAAGwI,GAAK,EAAGA,IAC9D4I,EAAKxB,OAAOkB,QAAQ,CAAC,OAAQQ,EAAa9I,GAAGgJ,QAASF,EAAa9I,GAAGiJ,OAE/D,SAAAC,EAAKzV,EAAQC,GAChB,IAA8Ea,EAA9EU,EAAQ3B,EAAS2D,MAAMxD,EAAQC,EAAMyV,WAAYf,EAAM,UAAU3T,KAAKQ,GACtE,GAAAmT,IAAQ,WAAW3T,KAAKhB,EAAO4C,aAC9B9B,EAAUb,EAAMyV,UAAU5U,SAAWb,EAAMyV,UAAU5U,QAAQc,gBAC9DuT,EAAK/S,eAAetB,GACtBb,EAAMW,MAAQE,EAAU,SAChC,GAAiBb,EAAMW,OAAS+T,GAAO,KAAK3T,KAAKhB,EAAO4C,WAAY,CAC5D,IAAIhC,EAAQ,gBAAgBwD,KAAKnE,EAAMW,OACvCX,EAAMW,MAAQ,KACd,IAAI+U,EAA+B,KAApB3V,EAAO4C,WAAoBkS,EAAiBK,EAAKvU,EAAM,IAAKA,EAAM,IAC7E4U,EAAOxa,EAAWka,QAAQvV,EAAQgW,GAClCC,EAAUtB,EAAa1T,EAAM,IAAI,GAAOiV,EAASvB,EAAa1T,EAAM,IAAI,GACtEX,EAAAuD,MAAQ,SAAUxD,EAAQC,GAC9B,OAAID,EAAOQ,MAAMoV,GAAS,IACxB3V,EAAMuD,MAAQiS,EACdxV,EAAM6V,WAAa7V,EAAM8V,UAAY,KAC9B,MAEFnC,EAAY5T,EAAQ6V,EAAQ5V,EAAM8V,UAAUvS,MAAMxD,EAAQC,EAAM6V,YACjF,EACQ7V,EAAM8V,UAAYP,EACZvV,EAAA6V,WAAa9a,EAAWsI,WAAWkS,EAAM3V,EAASqC,OAAOjC,EAAMyV,UAAW,GAAI,IAC5F,MAAiBzV,EAAMW,QACTX,EAAAW,OAASZ,EAAO4C,UAClB5C,EAAOsB,QAAOrB,EAAMW,OAAS,MAE5B,OAAAY,CAEb,CACW,MAAA,CACL8B,WAAY,WAEH,MAAA,CAACE,MAAOiS,EAAM7U,MAAO,KAAMmV,UAAW,KAAMD,WAAY,KAAMJ,UADzD1a,EAAWsI,WAAWzD,GAEnC,EAEDmW,UAAW,SAAU/V,GACf,IAAAgW,EAIG,OAHHhW,EAAM6V,aACRG,EAAQjb,EAAWgb,UAAU/V,EAAM8V,UAAW9V,EAAM6V,aAE/C,CAACtS,MAAOvD,EAAMuD,MAAO5C,MAAOX,EAAMW,MACjCmV,UAAW9V,EAAM8V,UAAWD,WAAYG,EACxCP,UAAW1a,EAAWgb,UAAUnW,EAAUI,EAAMyV,WACzD,EAEDlS,MAAO,SAAUxD,EAAQC,GAChB,OAAAA,EAAMuD,MAAMxD,EAAQC,EAC5B,EAEDiC,OAAQ,SAAUjC,EAAO2D,EAAWsS,GAClC,OAAKjW,EAAM8V,WAAa,UAAU/U,KAAK4C,GAC9B/D,EAASqC,OAAOjC,EAAMyV,UAAW9R,EAAWsS,GAC5CjW,EAAM8V,UAAU7T,OAChBjC,EAAM8V,UAAU7T,OAAOjC,EAAM6V,WAAYlS,EAAWsS,GAEpDlb,EAAW8I,IACrB,EAEDqS,UAAW,SAAUlW,GACZ,MAAA,CAACA,MAAOA,EAAM6V,YAAc7V,EAAMyV,UAAWF,KAAMvV,EAAM8V,WAAalW,EAC9E,EAEP,GAAK,MAAO,aAAc,OAEb7E,EAAAmK,WAAW,YAAa,YACrC,CAnJIiR,CAAInb,IAAiCob,IAAuBC,IAAqCC,6DCOrG,MAAAtc,EAAAC,EACAsc,EAAApc,EAAA,IACAqc,EAAArc,IACAsc,EAAAC,EAAA,CAA2BnB,KAAA,YACnBoB,MAAA,YAGRhc,GAAA,IAAAX,EAAAQ,OACcA,IAEV+b,EAAA9b,MAAAD,CAAA,GACF,CACAoc,WAAA,IAIFC,GAAA,KACEC,YAAA,KACEN,EAAA/b,OAAAsc,YAAqB,IAAA,IAIzBC,GAAA,KACER,EAAA/b,OAAAwc,aAEF,MAAAC,EAAA,CAAAC,EAAAC,KACE9c,QAAAC,IAAA4c,GACA7c,QAAAC,IAAA6c,EAAAC,WAAA,EAEFC,EAAAF,IACE9c,QAAAC,IAAA6c,EAAAG,QAAA,qLCxCIC,EACJ,CAAA,EAAgBC,qBAAuB,+CACnCC,EAAgB,qLCmDtB,MAAAC,EAAAC,IACAC,EAAA1d,EAAA,WACA2d,EAAA3d,EAAA,IACA4d,EAAA5d,EAAA,IACA6d,EAAA7d,EAAA,IACAA,EAAA,IACAA,EAAA,IACA,MAAA8d,EAAA9d,GAAA,GACA+d,EAAA/d,EAAA,WACAge,EAAAhe,EAAA,CAAA,GACAie,EAAAC,IACAC,EAAAne,EAAA,CAA+Boe,0BAAA,EACHC,YAAA,WAiB5B,MAAAC,EAAAC,MAAAC,IACE,IACEV,EAAAxd,OAAA,EACA,MAAAD,KAAAA,OAAAoe,EAAAC,QAAAA,SAAAC,EAAA,CAAAH,OACA,GAAAne,IAAAue,EAAAC,GACE,MAAA,IAAAC,MAAAJ,GAEFV,EAAA1d,MAAAme,EArBJ,SAAAM,EAAAC,GACE,MAAAC,EAAAF,EAAAC,EAOEjB,EAAAzd,MADF2e,GAHA,IAIE,SAEA,SACF,CAWEC,CAAAT,EAAAM,MAAAN,EAAAO,QACApB,EAAAtd,YAAA6e,GAAuC,OAAAC,GAEvCjf,QAAAC,IAAAgf,EAAiB,CAEnBtB,EAAAxd,OAAA,CAAA,EAEF+e,EAAA,KACEC,EAAA,CAAeC,eAAA,SACGC,MAAA5B,EAAAtd,OACM,EAG1Bgf,EAAAG,IACE5B,EAAAvd,MAAA,GACAod,EAAApd,MAAA,UD9FK,SACLmf,EACA1C,EACA2C,EACAC,EACAC,GAEM,MAEAC,EAAK,IAAIC,UAFD,GAAGzC,KAIdwC,EAAAE,iBAAiB,QAAQ,KACvBF,EAAAG,KACDC,KAAKC,UAAU,CACbC,MAAO,UACP1B,KAAMgB,IACP,IAIFI,EAAAE,iBAAiB,WAAWxB,MAAO4B,IACpC,MAAMC,EAAWH,KAAKI,MAAMF,EAAM1B,MACZ,UAAlB2B,EAAShb,KACX2X,EAASqD,EAAS9f,OACS,WAAlB8f,EAAShb,KAClBua,EAAeS,EAAS9f,OACG,YAAlB8f,EAAShb,KAClBsa,EAAUU,EAAS9f,OACQ,UAAlB8f,EAAShb,OACVjF,QAAAif,MAAM,wBAAyBgB,EAAS9f,OACtCggB,EAAA,CACR5B,QAAS0B,EAAS9f,MAClB8E,KAAM,UAEV,IAGCya,EAAAE,iBAAiB,SAAUI,IAC5BhgB,QAAQC,IAAI,oBAAqB+f,EAAM9f,KAAM8f,EAAMI,QACjC,KAAdJ,EAAM9f,MACAF,QAAAif,MAAM,uBAAwBe,GAC5BG,EAAA,CACR5B,QAASnB,EACTnY,KAAM,cAIV,IAGCya,EAAAE,iBAAiB,SAAUX,IACpBjf,QAAAif,MAAM,kBAAmBA,GACvBkB,EAAA,CACR5B,QAASnB,EACTnY,KAAM,SACP,GAEL,CCuCEgX,CADA,IAAAqD,KAAAtB,EAAA7d,QAEE8I,IAEEuU,EAAArd,OAAA8I,CAAA,IACF/I,IAEEsd,EAAArd,MAAAD,CAAA,IACFyb,IAEE+B,EAAAvd,MAAA,IAAAud,EAAAvd,MAAAwb,EAAA,IACF,KAEE4B,EAAApd,MAAA,YAAA,GACF,EAYJ6e,EAAA,IACE,IAAAqB,SAAA,CAAAta,EAAAkZ,KACE,MAAAI,EAAA,IAAAiB,MACAjB,EAAAkB,OAAA,WACExa,EAQN,SAAA1E,GACE,IAAAmf,EAAAC,SAAAC,cAAA,UAMA,OALAF,EAAA5B,MAAAvd,EAAAud,MACA4B,EAAA3B,OAAAxd,EAAAwd,OACA2B,EAAAG,WAAA,MACAC,UAAAvf,EAAA,EAAA,GACAmf,EAAAK,UAAA,aACAC,QAAA,+BAAA,GAAyD,CAfrDC,CAAA1B,GAA0B,EAE5BA,EAAA2B,YAAA,IACA3B,EAAA4B,QAAAhC,EACAI,EAAA6B,IAAArD,EAAA1d,MAAAghB,GAAA,WAaJ9gB,GAAA,IAAAyd,EAAAsD,QACcA,IAEVphB,QAAAC,IAAAmhB,GACAA,EAAA/C,IACEF,EAAAiD,EAAA/C,GAAqB,GAEzB,CACA/B,WAAA", "x_google_ignoreList": [2, 3, 4]}