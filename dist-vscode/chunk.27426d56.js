import{h as e,Z as a,B as l,l as t,u as s,F as o,m as n,o as d,s as r,C as i,D as c,x as u,y as p,k as m,ar as v,as as f,a9 as h,at as b,an as k,E as y,au as g,av as w,a1 as _,c as x,_ as I,aw as j,ax as V,d as C,ay as $,Q as E,az as B,aA as S,aB as N}from"./chunk.25a51fc3.js";import{E as U}from"./chunk.fd6abe75.js";/* empty css              */import{E as R}from"./chunk.5705a63a.js";import{E as D,a as M,b as z}from"./chunk.884698ee.js";import{d as F}from"./chunk.c5fb43ac.js";import"./chunk.615a7c87.js";/* empty css              */import{K as A,r as G,Q as O,z as P,d as T,o as W,c as L,b as q,I as K,aM as J,u as Q,a1 as Z,n as H,q as X,O as Y,C as ee,e as ae,k as le,f as te,g as se,L as oe,M as ne,N as de,w as re,P as ie,h as ce,A as ue,B as pe,V as me,i as ve,F as fe,a8 as he,ah as be,p as ke,j as ye,av as ge,aN as we,l as _e,aO as xe,aP as Ie,a as je,aQ as Ve,aR as Ce,a9 as $e,aS as Ee,aT as Be}from"./index.7c7944d0.js";import{u as Se}from"./chunk.d8776116.js";/* empty css              */import{a as Ne,E as Ue}from"./chunk.48f0fbfa.js";import{E as Re}from"./chunk.8dc6cbd8.js";import{U as De,C as Me,E as ze}from"./chunk.a37e6231.js";import{v as Fe}from"./chunk.d5d38f7a.js";import{P as Ae}from"./chunk.6d705473.js";import{c as Ge}from"./chunk.b2d62236.js";const Oe=e({size:a,disabled:Boolean,label:{type:[String,Number,Boolean],default:""}}),Pe=e({...Oe,modelValue:{type:[String,Number,Boolean],default:""},name:{type:String,default:""},border:Boolean}),Te={[De]:e=>A(e)||l(e)||t(e),[Me]:e=>A(e)||l(e)||t(e)},We=Symbol("radioGroupKey"),Le=(e,a)=>{const l=G(),t=O(We,void 0),n=P((()=>!!t)),d=P({get:()=>n.value?t.modelValue:e.modelValue,set(s){n.value?t.changeEvent(s):a&&a(De,s),l.value.checked=e.modelValue===e.label}}),r=s(P((()=>null==t?void 0:t.size))),i=o(P((()=>null==t?void 0:t.disabled))),c=G(!1),u=P((()=>i.value||n.value&&d.value!==e.label?-1:0));return{radioRef:l,isGroup:n,radioGroup:t,focus:c,size:r,disabled:i,tabIndex:u,modelValue:d}},qe=["value","name","disabled"],Ke=T({name:"ElRadio"});var Je=d(T({...Ke,props:Pe,emits:Te,setup(e,{emit:a}){const l=e,t=n("radio"),{radioRef:s,radioGroup:o,focus:d,size:r,disabled:i,modelValue:c}=Le(l,a);function u(){le((()=>a("change",c.value)))}return(e,a)=>{var l;return W(),L("label",{class:H([Q(t).b(),Q(t).is("disabled",Q(i)),Q(t).is("focus",Q(d)),Q(t).is("bordered",e.border),Q(t).is("checked",Q(c)===e.label),Q(t).m(Q(r))])},[q("span",{class:H([Q(t).e("input"),Q(t).is("disabled",Q(i)),Q(t).is("checked",Q(c)===e.label)])},[K(q("input",{ref_key:"radioRef",ref:s,"onUpdate:modelValue":a[0]||(a[0]=e=>Z(c)?c.value=e:null),class:H(Q(t).e("original")),value:e.label,name:e.name||(null==(l=Q(o))?void 0:l.name),disabled:Q(i),type:"radio",onFocus:a[1]||(a[1]=e=>d.value=!0),onBlur:a[2]||(a[2]=e=>d.value=!1),onChange:u,onClick:a[3]||(a[3]=X((()=>{}),["stop"]))},null,42,qe),[[J,Q(c)]]),q("span",{class:H(Q(t).e("inner"))},null,2)],2),q("span",{class:H(Q(t).e("label")),onKeydown:a[4]||(a[4]=X((()=>{}),["stop"]))},[Y(e.$slots,"default",{},(()=>[ee(ae(e.label),1)]))],34)],2)}}}),[["__file","radio.vue"]]);const Qe=e({...Oe,name:{type:String,default:""}}),Ze=["value","name","disabled"],He=T({name:"ElRadioButton"});var Xe=d(T({...He,props:Qe,setup(e){const a=e,l=n("radio"),{radioRef:t,focus:s,size:o,disabled:d,modelValue:r,radioGroup:i}=Le(a),c=P((()=>({backgroundColor:(null==i?void 0:i.fill)||"",borderColor:(null==i?void 0:i.fill)||"",boxShadow:(null==i?void 0:i.fill)?`-1px 0 0 0 ${i.fill}`:"",color:(null==i?void 0:i.textColor)||""})));return(e,a)=>{var n;return W(),L("label",{class:H([Q(l).b("button"),Q(l).is("active",Q(r)===e.label),Q(l).is("disabled",Q(d)),Q(l).is("focus",Q(s)),Q(l).bm("button",Q(o))])},[K(q("input",{ref_key:"radioRef",ref:t,"onUpdate:modelValue":a[0]||(a[0]=e=>Z(r)?r.value=e:null),class:H(Q(l).be("button","original-radio")),value:e.label,type:"radio",name:e.name||(null==(n=Q(i))?void 0:n.name),disabled:Q(d),onFocus:a[1]||(a[1]=e=>s.value=!0),onBlur:a[2]||(a[2]=e=>s.value=!1),onClick:a[3]||(a[3]=X((()=>{}),["stop"]))},null,42,Ze),[[J,Q(r)]]),q("span",{class:H(Q(l).be("button","inner")),style:te(Q(r)===e.label?Q(c):{}),onKeydown:a[4]||(a[4]=X((()=>{}),["stop"]))},[Y(e.$slots,"default",{},(()=>[ee(ae(e.label),1)]))],38)],2)}}}),[["__file","radio-button.vue"]]);const Ye=e({id:{type:String,default:void 0},size:a,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:""},fill:{type:String,default:""},label:{type:String,default:void 0},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0}}),ea=Te,aa=["id","aria-label","aria-labelledby"],la=T({name:"ElRadioGroup"});var ta=d(T({...la,props:Ye,emits:ea,setup(e,{emit:a}){const l=e,t=n("radio"),s=r(),o=G(),{formItem:d}=i(),{inputId:u,isLabeledByFormItem:p}=c(l,{formItemContext:d});se((()=>{const e=o.value.querySelectorAll("[type=radio]"),a=e[0];!Array.from(e).some((e=>e.checked))&&a&&(a.tabIndex=0)}));const m=P((()=>l.name||s.value));return oe(We,ne({...de(l),changeEvent:e=>{a(De,e),le((()=>a("change",e)))},name:m})),re((()=>l.modelValue),(()=>{l.validateEvent&&(null==d||d.validate("change").catch((e=>F())))})),(e,a)=>(W(),L("div",{id:Q(u),ref_key:"radioGroupRef",ref:o,class:H(Q(t).b("group")),role:"radiogroup","aria-label":Q(p)?void 0:e.label||"radio-group","aria-labelledby":Q(p)?Q(d).labelId:void 0},[Y(e.$slots,"default")],10,aa))}}),[["__file","radio-group.vue"]]);const sa=u(Je,{RadioButton:Xe,RadioGroup:ta}),oa=p(ta);p(Xe);const na=e({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:m(String),default:"round"},textInside:{type:Boolean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:m([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:m(Function),default:e=>`${e}%`}}),da=["aria-valuenow"],ra={viewBox:"0 0 100 100"},ia=["d","stroke","stroke-linecap","stroke-width"],ca=["d","stroke","opacity","stroke-linecap","stroke-width"],ua={key:0},pa=T({name:"ElProgress"});const ma=u(d(T({...pa,props:na,setup(e){const a=e,l={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},t=n("progress"),s=P((()=>({width:`${a.percentage}%`,animationDuration:`${a.duration}s`,backgroundColor:I(a.percentage)}))),o=P((()=>(a.strokeWidth/a.width*100).toFixed(1))),d=P((()=>["circle","dashboard"].includes(a.type)?Number.parseInt(""+(50-Number.parseFloat(o.value)/2),10):0)),r=P((()=>{const e=d.value,l="dashboard"===a.type;return`\n          M 50 50\n          m 0 ${l?"":"-"}${e}\n          a ${e} ${e} 0 1 1 0 ${l?"-":""}${2*e}\n          a ${e} ${e} 0 1 1 0 ${l?"":"-"}${2*e}\n          `})),i=P((()=>2*Math.PI*d.value)),c=P((()=>"dashboard"===a.type?.75:1)),u=P((()=>`${-1*i.value*(1-c.value)/2}px`)),p=P((()=>({strokeDasharray:`${i.value*c.value}px, ${i.value}px`,strokeDashoffset:u.value}))),m=P((()=>({strokeDasharray:`${i.value*c.value*(a.percentage/100)}px, ${i.value}px`,strokeDashoffset:u.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"}))),g=P((()=>{let e;return e=a.color?I(a.percentage):l[a.status]||l.default,e})),w=P((()=>"warning"===a.status?v:"line"===a.type?"success"===a.status?f:h:"success"===a.status?b:k)),_=P((()=>"line"===a.type?12+.4*a.strokeWidth:.111111*a.width+2)),x=P((()=>a.format(a.percentage)));const I=e=>{var l;const{color:t}=a;if(ie(t))return t(e);if(A(t))return t;{const a=function(e){const a=100/e.length;return e.map(((e,l)=>A(e)?{color:e,percentage:(l+1)*a}:e)).sort(((e,a)=>e.percentage-a.percentage))}(t);for(const l of a)if(l.percentage>e)return l.color;return null==(l=a[a.length-1])?void 0:l.color}};return(e,a)=>(W(),L("div",{class:H([Q(t).b(),Q(t).m(e.type),Q(t).is(e.status),{[Q(t).m("without-text")]:!e.showText,[Q(t).m("text-inside")]:e.textInside}]),role:"progressbar","aria-valuenow":e.percentage,"aria-valuemin":"0","aria-valuemax":"100"},["line"===e.type?(W(),L("div",{key:0,class:H(Q(t).b("bar"))},[q("div",{class:H(Q(t).be("bar","outer")),style:te({height:`${e.strokeWidth}px`})},[q("div",{class:H([Q(t).be("bar","inner"),{[Q(t).bem("bar","inner","indeterminate")]:e.indeterminate},{[Q(t).bem("bar","inner","striped")]:e.striped},{[Q(t).bem("bar","inner","striped-flow")]:e.stripedFlow}]),style:te(Q(s))},[(e.showText||e.$slots.default)&&e.textInside?(W(),L("div",{key:0,class:H(Q(t).be("bar","innerText"))},[Y(e.$slots,"default",{percentage:e.percentage},(()=>[q("span",null,ae(Q(x)),1)]))],2)):ce("v-if",!0)],6)],6)],2)):(W(),L("div",{key:1,class:H(Q(t).b("circle")),style:te({height:`${e.width}px`,width:`${e.width}px`})},[(W(),L("svg",ra,[q("path",{class:H(Q(t).be("circle","track")),d:Q(r),stroke:`var(${Q(t).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":e.strokeLinecap,"stroke-width":Q(o),fill:"none",style:te(Q(p))},null,14,ia),q("path",{class:H(Q(t).be("circle","path")),d:Q(r),stroke:Q(g),fill:"none",opacity:e.percentage?1:0,"stroke-linecap":e.strokeLinecap,"stroke-width":Q(o),style:te(Q(m))},null,14,ca)]))],6)),!e.showText&&!e.$slots.default||e.textInside?ce("v-if",!0):(W(),L("div",{key:2,class:H(Q(t).e("text")),style:te({fontSize:`${Q(_)}px`})},[Y(e.$slots,"default",{percentage:e.percentage},(()=>[e.status?(W(),ue(Q(y),{key:1},{default:pe((()=>[(W(),ue(me(Q(w))))])),_:1})):(W(),L("span",ua,ae(Q(x)),1))]))],6))],10,da))}}),[["__file","progress.vue"]])),va=e=>(ke("data-v-d345d4d3"),e=e(),ye(),e),fa={class:"sketch-drawer-header"},ha={key:0,class:"group-tree-node"},ba={key:0,class:"node-content"},ka=va((()=>q("img",{class:"node-icon",src:"https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png",alt:""},null,-1))),ya={class:"node-name"},ga={key:1,class:"node-content"},wa=va((()=>q("img",{class:"node-icon",src:"https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png",alt:""},null,-1))),_a={class:"node-name"},xa={key:2,class:"node-right-bar"},Ia={class:"node-right-bar-handle"},ja={class:"sketch-add-item"},Va={class:"sketch-add-item-btn"},Ca={class:"sketch-add-footer"},$a=I(T({__name:"project",props:{visible:{type:Boolean},id:{},teamId:{}},emits:["change","close"],setup(e,{emit:a}){const l=e,t=a,s=G(!1),o=G(""),n=G([]),d=G(!1);re((()=>l.visible),(e=>{e?p():o.value=""}));const r=ne({visible:!1,name:""}),i=()=>{r.visible=!1,r.folderId=null,r.name=""},c=async()=>{if(!r.name)return;const e=await he({teamId:l.teamId,name:r.name,folderId:r.folderId||null});if(0===e.code){if(i(),d.value)return void t("change",e.data);p()}},u=e=>{r.visible=!0,r.folderId=e._id},p=async()=>{s.value=!0;const e=await be({teamId:l.teamId});n.value=e.data,s.value=!1},m=e=>{"project"===e.type&&t("change",e)};return(e,a)=>{const l=y,p=x,v=ze,f=Ne,h=Re,b=Ue,k=U,I=Fe;return W(),L(fe,null,[ve(h,{class:"sketch-drawer","model-value":e.visible,size:"100%","with-header":!1},{default:pe((()=>[q("div",fa,[q("div",{class:"sketch-drawer-header-back",onClick:a[0]||(a[0]=e=>t("close"))},[ve(l,null,{default:pe((()=>[ve(Q(g))])),_:1})]),ve(v,{class:"sketch-drawer-header-filter",modelValue:o.value,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value=e),placeholder:"搜索文件夹 / 项目"},{prepend:pe((()=>[ve(p,{type:"text",icon:Q(w)},null,8,["icon"])])),append:pe((()=>[ve(p,{onClick:u,type:"text",icon:Q(_)},null,8,["icon"])])),_:1},8,["modelValue"])]),K((W(),ue(f,{class:"group-tree",onNodeClick:m,"expand-on-click-node":!1,data:n.value,"node-key":"_id","default-expand-all":""},{default:pe((({data:e})=>["sketch"!==e.type?(W(),L("div",ha,["project"==e.type?(W(),L("div",ba,[ka,q("span",ya,ae(e.name),1)])):(W(),L("div",ga,[wa,q("span",_a,ae(e.name),1)])),"project"!==e.type?(W(),L("div",xa,[q("div",Ia,[ve(p,{onClick:[a[2]||(a[2]=X((()=>{}),["stop"])),a=>u(e)],type:"text"},{default:pe((()=>[ee(" 新建项目 ")])),_:2},1032,["onClick"])])])):ce("",!0)])):ce("",!0)])),_:1},8,["data"])),[[I,s.value]])])),_:1},8,["model-value"]),ve(k,{class:"sketch-add",modelValue:r.visible,"onUpdate:modelValue":a[5]||(a[5]=e=>r.visible=e),title:"新建项目",width:"70%"},{footer:pe((()=>[q("span",Ca,[ve(p,{onClick:i},{default:pe((()=>[ee("取消")])),_:1}),ve(p,{type:"primary",onClick:c},{default:pe((()=>[ee(" 确定 ")])),_:1})])])),default:pe((()=>[q("div",ja,[ve(v,{placeholder:"请输入项目名称",modelValue:r.name,"onUpdate:modelValue":a[3]||(a[3]=e=>r.name=e)},null,8,["modelValue"])]),q("div",Va,[ve(b,{modelValue:d.value,"onUpdate:modelValue":a[4]||(a[4]=e=>d.value=e),style:{"margin-right":"10px"}},null,8,["modelValue"]),ee(" 创建后自动选择")])])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-d345d4d3"]]),Ea={class:"sketch-drawer-header"},Ba={class:"sketch-drawer-body"},Sa={class:"sketch-tree-node"},Na={key:0,class:"sketch-add-item"},Ua={class:"sketch-add-item"},Ra={class:"sketch-add-footer"},Da=I(T({__name:"group",props:{visible:{type:Boolean},id:{},projectId:{}},emits:["change","close"],setup(e,{emit:a}){const l=G(),t=e,s={label:"name",children:"children"},o=a,n=G(!1),d=G(""),r=G([]);re(d,(e=>{l.value.filter(e)})),re((()=>t.visible),(e=>{e?v():d.value=""})),re((()=>t.projectId),(e=>{e&&i()}));const i=async()=>{await v(),r.value.length&&f(r.value.find((e=>"未分组"===e.name))||r.value[0]),console.log(r.value)},c=ne({visible:!1,name:"",parent:null}),u=()=>{c.visible=!1,c.parent=null,c.name=""},p=async()=>{if(!c.name)return;0===(await ge({projectId:t.projectId,parentId:c.parent?._id,name:c.name})).code&&(v(),u())},m=e=>{c.visible=!0,c.parent=e},v=async()=>{n.value=!0;const e=await we({projectId:t.projectId});r.value=e.data,n.value=!1},f=e=>{o("change",e)},h=(e,a)=>!e||a.label.includes(e);return(e,a)=>{const t=y,i=x,v=ze,b=R,k=Re,I=U,C=Fe;return W(),L(fe,null,[ve(k,{class:"sketch-drawer","model-value":e.visible,size:"100%","with-header":!1},{default:pe((()=>[q("div",Ea,[q("div",{class:"sketch-drawer-header-back",onClick:a[0]||(a[0]=e=>o("close"))},[ve(t,null,{default:pe((()=>[ve(Q(g))])),_:1})]),ve(v,{class:"sketch-drawer-header-filter",modelValue:d.value,"onUpdate:modelValue":a[2]||(a[2]=e=>d.value=e),placeholder:"搜索分组"},{prepend:pe((()=>[ve(i,{type:"text",icon:Q(w)},null,8,["icon"])])),append:pe((()=>[ve(i,{onClick:a[1]||(a[1]=e=>m()),type:"text",icon:Q(_)},null,8,["icon"])])),_:1},8,["modelValue"])]),K((W(),L("div",Ba,[ve(Q(Ne),{"auto-expand-parent":!1,"node-key":"_id","highlight-current":"","expand-on-click-node":!1,ref_key:"treeRef",ref:l,"current-node-key":e.id,class:"filter-tree",data:r.value,props:s,onNodeClick:f,"default-expand-all":"","filter-node-method":h},{default:pe((({node:e,data:a})=>[q("div",Sa,[q("div",null,[ve(t,null,{default:pe((()=>[ve(Q(j))])),_:1}),q("span",null,ae(e.label),1)]),ve(i,{onClick:X((e=>m(a)),["stop"]),type:"text",icon:Q(V)},null,8,["onClick","icon"])])])),empty:pe((()=>[ve(b,{description:"暂无项目"})])),_:1},8,["current-node-key","data"])])),[[C,n.value]])])),_:1},8,["model-value"]),ve(I,{class:"sketch-add",modelValue:c.visible,"onUpdate:modelValue":a[4]||(a[4]=e=>c.visible=e),title:"新建分组",width:"70%"},{footer:pe((()=>[q("span",Ra,[ve(i,{onClick:u},{default:pe((()=>[ee("取消")])),_:1}),ve(i,{type:"primary",onClick:p},{default:pe((()=>[ee(" 确定 ")])),_:1})])])),default:pe((()=>[c.parent?(W(),L("div",Na,ae(c.parent?.fullPath.map((({name:e})=>e)).join(" / "))+" /",1)):ce("",!0),q("div",Ua,[ve(v,{placeholder:"请输入分组名称",modelValue:c.name,"onUpdate:modelValue":a[3]||(a[3]=e=>c.name=e)},null,8,["modelValue"])])])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-1ea05f43"]]),Ma=e=>(ke("data-v-1f951185"),e=e(),ye(),e),za={key:0,class:"sketch-progress"},Fa={key:0,class:"sketch-progress-content"},Aa={class:"percentage-value"},Ga={class:"percentage-label"},Oa={class:"sketch-progress-done"},Pa=Ma((()=>q("div",{class:"sketch-progress-done-text"},"上传成功",-1))),Ta={class:"sketch-progress-done-desc"},Wa=Ma((()=>q("br",null,null,-1))),La={class:"sketch-progress-btn"},qa={key:0,class:"sketch"},Ka={class:"sketch-logo"},Ja=Ma((()=>q("img",{loading:"lazy",src:"https://static.soyoung.com/sy-design/2gv1oqwpo580u1716895637825.png",alt:"新氧画廊LOGO"},null,-1))),Qa={class:"sketch-bar"},Za={class:"sketch-dropdown-link"},Ha={key:0},Xa={key:0,class:"sketch__userInfo"},Ya=["src"],el={class:"sketch-selected"},al={key:0,class:"sketch-selected-placeholder"},ll={key:1,class:"sketch-selected-list"},tl={class:"sketch-radio"},sl={key:0},ol={key:0},nl=Ma((()=>q("div",{class:"sketch-info-label"},"项目",-1))),dl={class:"sketch-info-content"},rl={class:"sketch-info-content__text"},il=Ma((()=>q("div",{class:"sketch-info-label"},"分组",-1))),cl={class:"sketch-info-content"},ul={class:"sketch-info-content__text"},pl={class:"sketch-submit"},ml={class:"sketch-setting"},vl=Ma((()=>q("div",{class:"setting-menu-item"},[q("div",null,"版本更新"),q("span",null,"v1.0")],-1))),fl={key:1,class:"sketch-login"},hl=Ma((()=>q("img",{src:"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png",alt:""},null,-1))),bl={class:"sketch-add-item"},kl={class:"sketch-add-footer"},yl=I(T({__name:"folderChoose",setup(e){const a=G("2"),l=_e(),t=[{color:"#f56c6c",percentage:20},{color:"#e6a23c",percentage:40},{color:"#5cb87a",percentage:60},{color:"#1989fa",percentage:80},{color:"#6f7ad3",percentage:100}],s=ne({list:[],current:null}),o=ne({visible:!1,name:""}),n=G({}),d=G({}),r=G(""),i=G([]),c=G(null),u=G(null),p=G(null);re((()=>a.value),(()=>{window.postMessage("getSelectedBoard",a.value)}));const m=()=>{d.value?.current?(p.value={process:0},window.postMessage("submit",a.value)):C.error("请选择分组")};window.sketchError=e=>{console.log("sketchError",e)},window.sketchResult=e=>{try{V(e)}catch(a){console.log(a)}},window.uploadInfo=e=>{"slice"===e.type?v(e.export,e.index):f(e.export,e.index)},window.sketchProcess=e=>{p.value=e},window.sketchArtBoard=e=>{i.value=e};const v=async(e,a)=>{const l={exportable:[],index:a,type:"slice"};for(let t=0;t<e.length;t++){const a=e[t],s=await h(a);l.exportable.push({path:s,format:a.format,name:a.path})}window.postMessage("uploadResult",l)},f=async(e,a)=>{const l={exportable:{format:e.format,name:e.name},index:a,type:"artboard"};l.exportable.path=await h(e),window.postMessage("uploadResult",l)},h=async e=>{const a=new FormData,l=new Int8Array(e.buffer.data),t=new Blob([l],{type:e.format}),s=new File([t],e.name+"."+e.format,{type:e.format});a.append("file",s);const{data:o}=await Se(a);return o.data.url},b=async e=>{"create"!==e?void 0!==e&&(s.current=s.list[e],window.localStorage.setItem("teamId",s.current._id),n.value={},d.value={}):o.visible=!0},k=e=>{n.value={current:e,visible:!1},d.value={}},g=()=>{n.value.visible=!1},w=e=>{d.value={current:e,visible:!1}},I=()=>{d.value.visible=!1},j=async()=>{const e=await Ve({});if(s.list=e.data,e.data?.length){const a=window.localStorage.getItem("teamId");let l;a&&(l=e.data.findIndex((e=>e._id===a))),b(l||e.data.findIndex((e=>e.permission===Ae.OWNER||e.permission===Ae.MANAGE)))}};const V=async e=>{const a=function(e,a){let l={};return e.forEach((e=>{a.forEach((a=>{a.layers.forEach((t=>{t.objectID===e.objectID&&(l[a.objectID]||(l[a.objectID]=[]),l[a.objectID].push(e))}))}))})),l}(e.slices,e.artboards);p.value={process:0,name:"上传json"};const l=await Promise.all(e.artboards.map((async(l,t)=>{l.layers.forEach((e=>{e.exportable&&e.exportable.forEach((e=>{delete e.buffer}))}));return(await xe({artboard:JSON.stringify(l),name:l.name,slices:JSON.stringify(a[l.objectID]||[]),unit:e.unit,artId:l.objectID,pageId:l.pageObjectID,thumb:l.imagePath,resolution:e.resolution,colorFormat:e.colorFormat,colors:JSON.stringify(e.colors),groupId:d.value.current?._id,projectId:n.value.current?._id,index:t})).data}))),{add:t,update:s}=l.reduce(((e,a)=>(e[a.updated?"update":"add"].push(a.name),e)),{add:[],update:[]});p.value={process:100,done:!0,add:t,update:s,name:"上传成功"},C.success("上传成功"),Ie({projectId:n.value.current?._id,groupId:d.value.current?._id,sketchs:l}),setTimeout((()=>{}),2e3)},F=()=>{n.value.visible=!0},A=()=>{n.value.current&&(d.value.visible=!0)};se((()=>{l.ssoId&&Y()}));const O=async()=>{const e=await Ee({token:r.value});if(0!==e.code)return C.error(e.msg),r.value="",void u.value?.stop();1===e?.data&&(r.value="",u.value?.stop(),C.success("登录成功"),T())},P=async()=>{r.value=function(){let e=[],a="0123456789abcdef";for(let l=0;l<32;l++)e[l]=a.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=a.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23],e.join("")}(),window.postMessage("openExternalUrl",`${window.location.origin}/#/item/project/check?from=gallery-sketch&token=${r.value}`),u.value=Ge(O,2e3,30),u.value.start()},T=async()=>{const e=await Be({});if(200==e.status&&0==e.data.code){const a=e.data.data;l.updateInfo({syUserName:a.syUserName,syUid:a.syUid,ssoId:a.ssoId,type:a.type,syData:a.syData,url:a.url,name:a.name}),Y()}},K=()=>{o.visible=!1,o.name=""},J=async()=>{if(!o.name)return;0===(await Ce({name:o.name})).code&&(C.success("添加成功"),j(),K())},Z=()=>{window.postMessage("closed")},H=()=>{window.postMessage("openExternalUrl",`${window.location.origin}/#/item/project/stage?teamId=${s.current._id}&projectId=${n.value.current?._id}`)},X=e=>{switch(e){case"logout":$e();break;case"jumpTo":window.postMessage("openExternalUrl",`${window.location.origin}/#/item/project/index?teamId=${s.current._id}`)}},Y=()=>{j(),c.value=setInterval((()=>{window.postMessage("getSelectedBoard",a.value)}),1e3)};return(e,c)=>{const u=ma,v=y,f=x,h=D,j=M,V=z,C=R,G=sa,O=oa,T=ze,Y=U;return W(),L(fe,null,[null!==p.value?(W(),L("div",za,[p.value.done?(W(),L(fe,{key:1},[q("div",Oa,[ve(v,{class:"sketch-progress-done-icon"},{default:pe((()=>[ve(Q($))])),_:1}),Pa,q("div",Ta,[ee(" 新增画板： "+ae(p.value.add.length)+" ",1),Wa,ee(" 更新画板： "+ae(p.value.update.length),1)])]),q("div",La,[ve(f,{type:"primary",onClick:H,round:""},{default:pe((()=>[ee("查看设计")])),_:1}),ve(f,{onClick:c[0]||(c[0]=e=>p.value=null),round:""},{default:pe((()=>[ee("继续上传")])),_:1}),ve(f,{onClick:Z,round:""},{default:pe((()=>[ee("关 闭")])),_:1})])],64)):(W(),L("div",Fa,[ve(u,{color:t,"stroke-width":10,indeterminate:"",width:200,type:"dashboard",percentage:+p.value.process},{default:pe((({percentage:e})=>[q("span",Aa,ae(e)+"%",1),q("span",Ga,ae(p.value.name||""),1)])),_:1},8,["percentage"])]))])):(W(),L(fe,{key:1},[Q(l).ssoId?(W(),L("div",qa,[q("div",Ka,[Ja,q("div",Qa,[ve(V,{onCommand:b,trigger:"click"},{dropdown:pe((()=>[ve(j,null,{default:pe((()=>[(W(!0),L(fe,null,je(s.list,((e,a)=>(W(),ue(h,{disabled:e.permission!==Q(Ae).OWNER&&e.permission!==Q(Ae).MANAGE,command:a,key:e._id},{default:pe((()=>[q("div",null,[ee(ae(e.name)+" ",1),e.permission!==Q(Ae).OWNER&&e.permission!==Q(Ae).MANAGE?(W(),L("div",Ha,"(无上传权限)")):ce("",!0)])])),_:2},1032,["disabled","command"])))),128)),ve(h,{command:"create"},{default:pe((()=>[ve(v,null,{default:pe((()=>[ve(Q(_))])),_:1}),ee(" 新增团队 ")])),_:1})])),_:1})])),default:pe((()=>[q("span",Za,[q("div",null,ae(s.current?.name||"-"),1),ve(v,{class:"el-icon--right"},{default:pe((()=>[ve(Q(E))])),_:1})])])),_:1}),Q(l).ssoId?(W(),L("div",Xa,[q("img",{loading:"lazy",class:"sketch__avatar",src:Q(l).url||"https://static.soyoung.com/sy-pre/<EMAIL>",alt:""},null,8,Ya)])):ce("",!0)])]),q("div",el,[i.value.length?(W(),L("ul",ll,[(W(!0),L(fe,null,je(i.value,((e,a)=>(W(),L("li",{class:"sketch-selected-li",key:a},[ve(v,null,{default:pe((()=>[ve(Q(B))])),_:1}),ee(" "+ae(e),1)])))),128))])):(W(),L("div",al,[ve(C,{description:"未选中画板"})]))]),q("div",tl,[ve(O,{modelValue:a.value,"onUpdate:modelValue":c[1]||(c[1]=e=>a.value=e)},{default:pe((()=>[ve(G,{label:"1"},{default:pe((()=>[ee("该页面全部画板"),"1"==a.value?(W(),L("span",sl,"("+ae(i.value.length)+")",1)):ce("",!0)])),_:1}),ve(G,{label:"2"},{default:pe((()=>[ee("选中画板 "),"2"==a.value?(W(),L("span",ol,"("+ae(i.value.length)+")",1)):ce("",!0)])),_:1})])),_:1},8,["modelValue"])]),q("div",{class:"sketch-info",onClick:F},[nl,q("div",dl,[q("div",rl,ae(n.value.current?.name||"未选择"),1),ve(v,null,{default:pe((()=>[ve(Q(S))])),_:1})])]),q("div",{class:"sketch-info",onClick:A},[il,q("div",cl,[q("div",ul,ae(d.value?.current?d.value.current.fullPath.map((({name:e})=>e)).join("/"):"未选择"),1),ve(v,null,{default:pe((()=>[ve(Q(S))])),_:1})])]),q("div",pl,[ve(f,{class:"submit-button",onClick:m,type:"primary",round:""},{default:pe((()=>[ee("上 传")])),_:1}),ve(V,{onCommand:X,trigger:"click"},{dropdown:pe((()=>[ve(j,{class:"setting-menu"},{default:pe((()=>[ve(h,{disabled:""},{default:pe((()=>[vl])),_:1}),ve(h,{command:"jumpTo"},{default:pe((()=>[ee(" 前往画廊 ")])),_:1}),ve(h,{divided:"",command:"logout"},{default:pe((()=>[ee(" 退出登录 ")])),_:1})])),_:1})])),default:pe((()=>[q("span",ml,[ve(v,null,{default:pe((()=>[ve(Q(N))])),_:1}),ee(" 设置 ")])])),_:1})])])):(W(),L("div",fl,[hl,ve(f,{color:"#626aef",size:"large",disabled:!!r.value,onClick:P,dark:""},{default:pe((()=>[ee("登 录")])),_:1},8,["disabled"])]))],64)),ve(Y,{class:"sketch-add",modelValue:o.visible,"onUpdate:modelValue":c[3]||(c[3]=e=>o.visible=e),title:"新建团队",width:"70%"},{footer:pe((()=>[q("span",kl,[ve(f,{onClick:K},{default:pe((()=>[ee("取消")])),_:1}),ve(f,{type:"primary",onClick:J},{default:pe((()=>[ee(" 确定 ")])),_:1})])])),default:pe((()=>[q("div",bl,[ve(T,{placeholder:"请输入团队名称",modelValue:o.name,"onUpdate:modelValue":c[2]||(c[2]=e=>o.name=e)},null,8,["modelValue"])])])),_:1},8,["modelValue"]),ve($a,{visible:n.value.visible,id:n.value.current?._id,teamId:s.current?._id,onClose:g,onChange:k},null,8,["visible","id","teamId"]),ve(Da,{visible:d.value.visible,id:d.value.current?._id,projectId:n.value.current?._id,onClose:I,onChange:w},null,8,["visible","id","projectId"])],64)}}}),[["__scopeId","data-v-1f951185"]]);export{yl as default};
//# sourceMappingURL=chunk.27426d56.js.map
