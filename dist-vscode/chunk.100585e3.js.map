{"version": 3, "file": "chunk.100585e3.js", "sources": ["../src/api/upload/index.ts"], "sourcesContent": ["import axios from \"@/api\";\nimport qs from \"qs\";\n\nexport const folderAdd = async (args) => {\n  return await axios.get(\"/folder/add\", {\n    params: args\n  });\n};\n\nexport const folderNename = async (args) => {\n  return await axios.get(\"/folder/rename\", {\n    params: args\n  });\n};\n\nexport const moduleList = async (args) => {\n  return await axios.get(\"/apps/moduleList\", {\n    params: args\n  });\n};\n\nexport const aiGenerated = async (args) => {\n  return await axios.get(\"/generated/g\", {\n    params: args\n  });\n};\n\nexport const materialAdd = async (args) => {\n  return await axios.post(\"/material/add\", qs.stringify(args));\n};\n\nexport const materialAddAll = async (args) => {\n  return await axios.post(\"/material/addAll\", qs.stringify(args));\n};\n"], "names": ["folderAdd", "async", "args", "axios", "get", "params", "<PERSON><PERSON><PERSON><PERSON>", "moduleList", "aiGenerated", "materialAddAll", "post", "qs", "stringify"], "mappings": "gDAGa,MAAAA,EAAYC,MAAOC,SACjBC,EAAMC,IAAI,cAAe,CACpCC,OAAQH,IAICI,EAAeL,MAAOC,SACpBC,EAAMC,IAAI,iBAAkB,CACvCC,OAAQH,IAICK,EAAaN,MAAOC,SAClBC,EAAMC,IAAI,mBAAoB,CACzCC,OAAQH,IAICM,EAAcP,MAAOC,SACnBC,EAAMC,IAAI,eAAgB,CACrCC,OAAQH,IAQCO,EAAiBR,MAAOC,SACtBC,EAAMO,KAAK,mBAAoBC,EAAGC,UAAUV"}