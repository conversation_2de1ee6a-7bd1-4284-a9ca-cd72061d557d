import{d as e,_ as a}from"./chunk.49d958ff.js";import{d as t,t as l,m as s,r as o,w as i,o as n,c as r,F as c,a as d,n as u,b as m,e as v,u as p,f as g,g as f,h as _,i as h,p as y,j as w,k as b,l as k,q as I,s as C,v as z,x as E,y as T,z as j,A as x,B as W,C as S,T as F,D as B,E as L,G as O,H as R,I as D}from"./index.05904f40.js";import{v as V,c as N,m as A,a as M}from"./chunk.984ebab6.js";import{m as P,a as H}from"./chunk.805f473d.js";import{i as X,_ as Y,z as $,a as q,r as U,b as G,E as J,c as K,d as Q}from"./chunk.8df321e8.js";import{E as Z}from"./chunk.58688d79.js";import{E as ee}from"./chunk.9a591dc8.js";/* empty css              */import{d as ae}from"./chunk.78434e43.js";import{E as te}from"./chunk.0db0b66d.js";import{E as le}from"./chunk.e117c129.js";import{l as se}from"./chunk.4ae50552.js";import"./chunk.db8898e3.js";import"./chunk.1fa2ec58.js";import"./chunk.505381c5.js";import"./chunk.6eac9d60.js";import"./chunk.c83b4915.js";function oe(e,a,t){var l=!0,s=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return X(t)&&(l="leading"in t?!!t.leading:l,s="trailing"in t?!!t.trailing:s),ae(e,a,{leading:l,maxWait:a,trailing:s})}const ie=["onClick"],ne=Y(t({__name:"spScreenTabs",props:{isToggle:{type:Boolean,default:!1},data:{type:Array,default:()=>[]},materialType:{type:String,default:"version"}},emits:["updateScrollWidth"],setup(e,{emit:a}){const t=l(),f=s();let _=a,h=e;const y=o([]),w=e=>{_("updateScrollWidth",e.target.scrollLeft)};return i((()=>f.ids),(e=>{y.value=e})),(a,l)=>(n(),r("div",{class:u({"sp-screen-tabs":!0,"sp-screen-tabs-theme__active":p(t).themeShow}),id:"tabsContainer",style:g({justifyContent:e.isToggle?"flex-start":"center"}),onScroll:w},[(n(!0),r(c,null,d(e.data,(e=>(n(),r("div",{key:e._id,class:u({"sp-screen-tabs__item":!0,"sp-screen-tabs-item__active":y.value.includes(e._id)}),onClick:a=>(e=>{y.value.includes(e._id)?y.value=y.value.filter((a=>a!==e._id)):y.value.push(e._id),console.log(y.value,"valueIDS"),"version"===h.materialType?f.updateVersionInfo(y.value):f.updateTagsInfo(y.value)})(e)},[m("span",null,v(e.name),1)],10,ie)))),128))],38))}}),[["__scopeId","data-v-adcf940a"]]),re=e=>(y("data-v-e8bb36d1"),e=e(),w(),e),ce={key:0,class:"home-body-right-header__one"},de=[re((()=>m("div",{class:"swiper-list-tool__bg"},null,-1))),re((()=>m("span",{class:"previous-icon"},[m("i",{class:"iconfont icon-zuojiantou"})],-1)))],ue={class:"home-body-right-scroll__tabs"},me=[re((()=>m("div",{class:"swiper-list-tool__bg"},null,-1))),re((()=>m("span",{class:"previous-icon"},[m("i",{class:"iconfont icon-zuojiantou"})],-1)))],ve={key:1,class:"home-body-right-header__more"},pe={class:"more-list__item"},ge=re((()=>m("label",null,"版本筛选：",-1))),fe={class:"more-list__item"},_e=re((()=>m("label",null,"功能筛选：",-1))),he=Y(t({__name:"homeBodyHeader",props:{appOrDesign:{type:Number,default:1}},setup(e){const a=l(),t=s(),c=o(!1),d=o(0),v=o(0),g=o(0),y=o(0),w=o(0),k=o(0),I=o([]),C=o([]),z=()=>{const e=document.getElementById("tabsContainer");+e.scrollLeft>0&&(k.value-=400,e.scrollLeft=k.value)},E=()=>{const e=document.getElementById("tabsContainer");+e.scrollLeft<+g.value&&(k.value+=400,e.scrollLeft=k.value)},T=e=>{k.value=e},j=async e=>{const a=await V({appId:e});0===a.data.code?C.value=a.data.data:C.value=[]},x=()=>{(async()=>{const e=await P({});if(0===e.data.code&&e.data.data.length>0){I.value=e.data.data,d.value=document.getElementById("container")?.clientWidth,v.value=document.getElementById("tabsContainer")?.clientWidth,await b();let a=document.querySelectorAll(".sp-screen-tabs__item");g.value=a[0].offsetWidth*e.data.data.length+10*e.data.data.length,y.value=document.getElementById("tabsContainer")?.clientWidth,w.value=document.getElementById("tabsContainer")?.scrollWidth,d.value-20<+g.value&&+v.value<=+g.value?c.value=!0:c.value=!1}else I.value=[]})(),j("")};return i((()=>t.appId),(e=>{j(e)})),f((()=>{x()})),(t,l)=>(n(),r("div",{class:u({"home-body-right__header":!0,"home-body-right-header__active":p(a).themeShow}),id:"container"},[1===e.appOrDesign||0===e.appOrDesign?(n(),r("div",ce,[c.value?(n(),r("div",{key:0,class:"swiper-list__tool swiper-list__tool--previous",onClick:z},de)):_("",!0),m("div",ue,[h(ne,{"is-toggle":c.value,data:I.value,onUpdateScrollWidth:T,"material-type":"module"},null,8,["is-toggle","data"])]),c.value?(n(),r("div",{key:1,class:"swiper-list__tool swiper-list__tool--next",onClick:E},me)):_("",!0)])):(n(),r("div",ve,[m("div",pe,[ge,h(ne,{"is-toggle":!0,data:C.value,"material-type":"version"},null,8,["data"])]),m("div",fe,[_e,h(ne,{"is-toggle":!0,data:I.value,"material-type":"module"},null,8,["data"])])]))],2))}}),[["__scopeId","data-v-e8bb36d1"]]),ye={class:"group__btn_start"},we=["src"],be=[(e=>(y("data-v-92230e2d"),e=e(),w(),e))((()=>m("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/1l4j3t32i00st-1697804341094.png",alt:""},null,-1)))],ke=["src"],Ie=Y(t({__name:"templateCard",props:{templateInfo:{type:Object,default:()=>({key:"1111111",src:"https://static.soyoung.com/sy-pre/8d15af2b60d957d86961c0fbaa5b0cb7-1637511000713.jpeg",name:"name",collection:!1,designCategory:{_id:"",name:""},size:{w:"",h:""},type:"",styleType:"girl"})},params:{type:Object,default:()=>({})},noBorder:{type:Boolean,default:!1},needCenter:{type:Boolean,default:!1},designType:{type:String,default:""},maxHeight:{type:Number,default:0}},emits:["preview"],setup(a,{emit:t}){const l=k(),s=t,i=o(null),c=o(!1);let d=o(0);const v=o(["#F0F7FE","#FEF0F0","#FEFBF0","#EDF6E7","#F0EAF7"]);let h=o("");f((()=>{d.value=i.value.offsetWidth,h.value=v.value[Math.floor(5*Math.random())],window.addEventListener("resize",(()=>{i.value&&(d.value=i.value.offsetWidth)}))}));const y=()=>{s("preview")};return(t,s)=>(n(),r("div",{class:"ai-home-template-card",ref_key:"templateCard",ref:i,onClick:y},[m("div",{class:u("template-card-img-wrap "+(a.needCenter?"need-center":"")),style:g(""+("imgWaterfall"===a.templateInfo.type?"height:"+(a.templateInfo.size.h*p(d)/a.templateInfo.size.w?a.templateInfo.size.h*p(d)/a.templateInfo.size.w:200)+"px;background:"+p(h)+";":""))},[m("div",ye,[p(l).syUid!==a.templateInfo.userId?(n(),r("div",{key:0,class:"group-btn-start__item",onClick:s[0]||(s[0]=I((e=>(async e=>{const a=await N({userId:e.userId,id:e.key,isCollect:1});0===a.data.code?(c.value=!0,ee({type:"success",message:"收藏成功",duration:3e3})):ee({type:"error",message:a.data.msg,duration:3e3})})(a.templateInfo)),["stop"]))},[m("img",{loading:"lazy",src:c.value?"https://static.soyoung.com/sy-pre/23ust432yoq4s-1697804341094.png":"https://static.soyoung.com/sy-pre/1nubvuocuun6k-1697804341094.png",alt:""},null,8,we)])):_("",!0),m("div",{class:"group-btn-start__item",onClick:s[1]||(s[1]=I((t=>{return l=a.templateInfo,console.log(l,"下载"),void e(l);var l}),["stop"]))},be)]),m("img",{loading:"lazy",class:"template-card-cover-img",src:a.templateInfo.src&&a.templateInfo.src.indexOf("?")<0?`${a.templateInfo.src}?imageView2/0/format/webp`:a.templateInfo.src},null,8,ke)],6)],512))}}),[["__scopeId","data-v-92230e2d"]]);var Ce;const ze="undefined"!=typeof window,Ee=()=>{};ze&&(null==(Ce=null==window?void 0:window.navigator)?void 0:Ce.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);const Te=ze?window:void 0;function je(...e){let a,t,l,s;if("string"==typeof e[0]?([t,l,s]=e,a=Te):[a,t,l,s]=e,!a)return Ee;let o=Ee;const n=i((()=>function(e){var a;const t=p(e);return null!=(a=null==t?void 0:t.$el)?a:t}(a)),(e=>{o(),e&&(e.addEventListener(t,l,s),o=()=>{e.removeEventListener(t,l,s),o=Ee})}),{immediate:!0,flush:"post"}),r=()=>{n(),o()};var c;return c=r,C()&&z(c),r}const xe="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},We="__vueuse_ssr_handlers__";var Se,Fe;xe[We]=xe[We]||{},(Fe=Se||(Se={})).UP="UP",Fe.RIGHT="RIGHT",Fe.DOWN="DOWN",Fe.LEFT="LEFT",Fe.NONE="NONE";const Be=e=>(y("data-v-20c9dd25"),e=e(),w(),e),Le={key:0,ref:"wrapper",class:"image-view-wrapper"},Oe=Be((()=>m("div",{class:"image-view-mask"},null,-1))),Re={class:"image-view-content"},De={class:"image-view-bar"},Ve=Be((()=>m("div",{class:"image-view-title"},null,-1))),Ne={class:"image-view-btn"},Ae=["src"],Me={class:"image-view-actions"},Pe={class:"image-view-actions-content"},He=Be((()=>m("div",{class:"image-view-actions-content__title"},[m("span",null,"基础信息")],-1))),Xe={class:"image-view-actions-content__item"},Ye=Be((()=>m("label",null,"文件大小",-1))),$e={class:"image-view-actions-content__item"},qe=Be((()=>m("label",null,"尺寸",-1))),Ue={class:"image-view-actions-content__item"},Ge=Be((()=>m("label",null,"文件格式",-1))),Je={class:"image-view-actions-content__item"},Ke=Be((()=>m("label",null,"颜色类型",-1))),Qe={class:"image-view-actions-content__item"},Ze=Be((()=>m("label",null,"添加日期",-1))),ea={class:"image-view-actions-content__item origin__path"},aa=Be((()=>m("label",null,"来源路径",-1))),ta={class:"origin-path__content"},la={class:"origin-path__item"},sa={class:"tips"},oa=["src"],ia={class:"origin-path__item"},na={class:"tips"},ra=Be((()=>m("img",{loading:"lazy",src:"https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png",alt:""},null,-1))),ca={class:"image-view-actions-content__btns"},da=t({components:{TemplateCard:Ie,ImageViewer:Y(t({__name:"image-view",props:{material:{},showViewer:{type:Boolean},minScale:{default:.2},maxScale:{default:6},zoomRate:{default:1.2},closeOnPressEscape:{type:Boolean,default:!1}},emits:{close:()=>!0},setup(e,{emit:a}){const t="Escape",l="Space",s="ArrowUp",i="ArrowDown",c=E(),d=e,u=a,y=T();f((()=>{!function(){const e=oe((e=>{switch(e.code){case t:d.closeOnPressEscape&&L();break;case l:break;case s:k("zoomIn");break;case i:k("zoomOut")}})),a=oe((e=>{k((e.deltaY||e.deltaX)<0?"zoomIn":"zoomOut",{zoomRate:d.zoomRate,enableTransition:!1})}));c.run((()=>{je(document,"keydown",e),je(document,"wheel",a)}))}()}));const w=o({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),b=j((()=>{const{scale:e,deg:a,offsetX:t,offsetY:l,enableTransition:s}=w.value;let o=t/e,i=l/e;switch(a%360){case 90:case-270:[o,i]=[i,-o];break;case 180:case-180:[o,i]=[-o,-i];break;case 270:case-90:[o,i]=[-i,o]}const n={transform:`scale(${e}) rotate(${a}deg) translate(${o}px, ${i}px)`,transition:s?"transform .3s":""};return n.maxWidth=n.maxHeight="95%",n}));function k(e,a={}){const{minScale:t,maxScale:l,showViewer:s}=d,{zoomRate:o,rotateDeg:i,enableTransition:n}={zoomRate:d.zoomRate,rotateDeg:90,enableTransition:!0,...a};if(s){switch(e){case"zoomOut":w.value.scale>t&&(w.value.scale=Number.parseFloat((w.value.scale/o).toFixed(3)));break;case"zoomIn":w.value.scale<l&&(w.value.scale=Number.parseFloat((w.value.scale*o).toFixed(3)));break;case"clockwise":w.value.deg+=i;break;case"anticlockwise":w.value.deg-=i}w.value.enableTransition=n}}function I(e){w.value.enableTransition=!1;const{offsetX:a,offsetY:t}=w.value,l=e.pageX,s=e.pageY,o=oe((e=>{w.value={...w.value,offsetX:a+e.pageX-l,offsetY:t+e.pageY-s}})),i=je(document,"mousemove",o);je(document,"mouseup",(()=>{i()})),e.preventDefault()}const C=async()=>{te.confirm("确认以此图喂给AI生成新图?时间有点长耐心等待啊","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=le.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"}),a=await H({name:d.material?.module.name||"首页"});a?.data?.data&&(e.close(),window.open(a?.data?.data))})).catch((()=>{}))},z=()=>{const{href:e}=y.resolve({path:"/screenCode",query:{id:d.material._id}});window.open(e,"_blank")},L=()=>{w.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1},u("close")};return(e,a)=>{const t=J,l=K;return n(),x(B,{to:"body"},[h(F,{name:"viewer-fade",appear:""},{default:W((()=>[e.showViewer?(n(),r("div",Le,[Oe,m("div",Re,[m("div",De,[Ve,m("div",Ne,[h(t,{onClick:a[0]||(a[0]=e=>k("zoomOut"))},{default:W((()=>[h(p($))])),_:1}),h(t,{onClick:a[1]||(a[1]=e=>k("zoomIn"))},{default:W((()=>[h(p(q))])),_:1}),h(t,{onClick:a[2]||(a[2]=e=>k("anticlockwise"))},{default:W((()=>[h(p(U))])),_:1}),h(t,{onClick:a[3]||(a[3]=e=>k("clockwise"))},{default:W((()=>[h(p(G))])),_:1})])]),m("div",{class:"image-view-canvas",onClick:L},[(n(),r("img",{loading:"lazy",key:e.material.url,onMousedown:I,src:e.material.url&&e.material.url.indexOf("?")<0?`${e.material.url}?imageView2/0/format/webp`:e.material.url,style:g(b.value),class:"image-view-img"},null,44,Ae))])]),m("div",Me,[m("div",Pe,[He,m("div",Xe,[Ye,m("span",null,v((e.material?.size/1e3).toFixed(2))+"mb",1)]),m("div",$e,[qe,m("span",null,v(e.material.width)+" * "+v(e.material.height),1)]),m("div",Ue,[Ge,m("span",null,v(e.material?.format),1)]),m("div",Je,[Ke,m("span",null,v(e.material?.colorModel),1)]),m("div",Qe,[Ze,m("span",null,v(new Date(parseInt(e.material?.createTime)).toLocaleString().replace(/:\d{1,2}$/," ")),1)]),m("div",ea,[aa,m("div",ta,[m("div",la,[m("div",sa,[m("img",{loading:"lazy",src:e.material?.app.icon,alt:""},null,8,oa),m("span",null,v(e.material?.app?.name)+" V"+v(e.material.lver?.name),1)])]),m("div",ia,[m("div",na,[ra,m("span",null,v(e.material?.module.name),1)])])]),m("div",ca,[h(l,{class:"el-button-code",type:"primary",onClick:z},{default:W((()=>[S("AI原型Code")])),_:1}),h(l,{type:"primary",onClick:C},{default:W((()=>[S("AI灵感升级")])),_:1})])])])])],512)):_("",!0)])),_:1})])}}}),[["__scopeId","data-v-20c9dd25"]])},setup(){let e=o([]),a=o(null),t=o(null),l=o(1),n=o(!1),r=o(!1),c=o(!1),d=o({}),u=o(!1),m=o(0),v=s();const p=o(0),g=async()=>{if(n.value)return!1;r.value=!0;const a=await A({appId:1!=+v.appId?v.appId:"",lverId:v.versionId.join(",")||"",moduleId:v.tagsId.join(",")||"",categoryId:v.categoryId,page:l.value,pageSize:20});0===a.data.code?(u.value=!0,p.value=+a.data.data.total,a.data.data.list.length<10&&(n.value=!0),1===l.value&&(e.value=[]),a.data.data.list.forEach(((a,t)=>{if(t<5&&1===l.value){let l=a.height*(m.value/a.width);e.value[t]={height:(l>400?400:l)+36,children:[]},e.value[t].children.push(a)}else{let t=[];for(let a in e.value)t.push(e.value[a].height);for(let l in e.value){let s=a.height*(m.value/a.width);if(e.value[l].height===t.sort(((e,a)=>e-a))[0])return e.value[l].height+=(s>400?400:s)+36,void e.value[l].children.push(a)}}}))):ee({message:a.data.msg,type:"error",duration:1500}),r.value=!1};i((()=>v.getMaterialData()),(()=>{l.value=1,e.value=[],n.value=!1,g()}),{immediate:!0,deep:!0}),f((()=>{m.value=a.value.offsetWidth/5-20}));const _=se.debounce((e=>{if(n.value)return!1;const{scrollHeight:a,scrollTop:t,clientHeight:s}=e.target;parseInt(a)-(parseInt(t)+parseInt(s))==0&&l.value!==p.value&&(l.value++,g())}),300);return{setMenuId:()=>{l.value=1,n.value=!1,g()},getImgWaterfall:g,handleScrollChange:_,imgWaterfallList:e,imgWaterfallRef:a,wrappRef:t,isReady:u,imgWaterfallLoading:r,listNoMore:n,showViewer:c,material:d,handlePreview:e=>{d.value=e,c.value=!0}}}}),ua={class:"img__waterfall__container",ref:"imgWaterfallRef",style:{overflow:"hidden"}},ma={key:1},va={key:0,class:"design-list-no-more"},pa={key:1,class:"design-list-no-more"};const ga=Y(da,[["render",function(e,a,t,l,s,o){const i=L("template-card"),u=Z,v=L("ImageViewer");return n(),r(c,null,[m("div",{class:"ai-home-lazy-img__waterfall",ref:"wrappRef",onScroll:a[0]||(a[0]=(...a)=>e.handleScrollChange&&e.handleScrollChange(...a))},[m("div",ua,[(n(!0),r(c,null,d(e.imgWaterfallList,((a,t)=>(n(),r("div",{class:"img__ls__box",key:t},[(n(!0),r(c,null,d(a.children,(a=>(n(),r("div",{class:"img__box",key:a._id},[h(i,{templateInfo:{key:a._id,src:a.url,name:a.name,size:{w:a.width,h:a.height},type:"imgWaterfall",userId:a.userId},params:a,onPreview:t=>e.handlePreview(a)},null,8,["templateInfo","params","onPreview"])])))),128))])))),128)),0===e.imgWaterfallList.length&&e.isReady?(n(),x(u,{key:0,description:"没有数据"})):(n(),r("div",ma,[e.imgWaterfallLoading?(n(),r("div",va,"加载中...")):_("",!0),e.listNoMore?(n(),r("div",pa,"没有更多了")):_("",!0)]))],512)],544),h(v,{material:e.material,showViewer:e.showViewer,onClose:a[1]||(a[1]=a=>e.showViewer=!1)},null,8,["material","showViewer"])],64)}],["__scopeId","data-v-b81b118a"]]),fa={class:"home-body-right__container"},_a=Y(t({__name:"homeBodyContainer",setup:e=>(e,a)=>(n(),r("div",fa,[h(ga,{ref:"lazyWaterfall"},null,512)]))}),[["__scopeId","data-v-48dcb8c8"]]),ha={class:"home-body"},ya={class:"home-body__left"},wa=["onClick"],ba={key:0,class:"iconfont icon-duigou icon-duigou__active"},ka={key:1,class:"category-nav-selector-dropdown-item__bg"},Ia={class:"home-body__right"},Ca=Y(t({__name:"index",setup(e){const t=s(),i=l(),y=o(1),w=o([]),b=e=>{y.value=+e._id,t.updateMaterialInfo(e)},k=o(null),I=o([]),C=j((()=>I.value.find((e=>e._id===t.categoryId))?.name)),z=o(!1),E=()=>{z.value=!1},T=()=>{z.value=!0},x=e=>{t.updateCategoryId(e),S(),z.value=!1},W=async()=>{try{const e=await(async e=>(await O.get("/category/list",{params:e})).data)({});if(0!==e.code)throw new Error(e.msg);if(0===e.data.length)return void Q.error("暂无数据");I.value=[...I.value,...e.data],x(e.data[0]._id),await S()}catch(e){Q.error(e.message)}},S=async()=>{const e=await M({categoryId:t.categoryId});w.value=e.data.data.list};return f((()=>{(async()=>{await W()})()})),(e,l)=>{const s=R("click-outside");return n(),r("div",ha,[m("div",ya,[m("div",{class:u({"category-nav-selector":!0,"category-nav-selector__active":p(i).themeShow})},[D((n(),r("div",{class:"category-nav-selector__btn",onClick:T},[m("span",null,"全部"+v(C.value),1),m("i",{class:"iconfont icon-jiantoushouqi",style:g({transform:z.value?"rotate(180deg)":"rotate(0deg)",color:z.value?"#5C54F0":""})},null,4)])),[[s,E]]),m("div",{ref_key:"dropdownRef",ref:k,class:u({"category-nav-selector__dropdown":!0,"category-nav-selector-dropdown-reverse__false":!z.value,"category-nav-selector-dropdown-reverse__true":z.value})},[(n(!0),r(c,null,d(I.value,(e=>(n(),r("div",{class:"category-nav-selector-dropdown__item",key:e._id,onClick:a=>x(e._id)},[m("span",null,"全部"+v(e.name),1),e._id===p(t).categoryId?(n(),r("i",ba)):_("",!0),e._id===p(t).categoryId?(n(),r("div",ka)):_("",!0)],8,wa)))),128))],2)],2),h(a,{onTabsChange:b,data:w.value,"default-id":p(t).appId},null,8,["data","default-id"])]),m("div",Ia,[h(he,{"app-or-design":y.value},null,8,["app-or-design"]),h(_a)])])}}}),[["__scopeId","data-v-a8d04cb5"]]);export{Ca as default};
//# sourceMappingURL=chunk.45618571.js.map
