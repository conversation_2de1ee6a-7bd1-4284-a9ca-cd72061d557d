import{d as a,c as s,_ as e}from"./chunk.25a51fc3.js";/* empty css              */import{d as t,l as c,af as l,y as u,r as o,g as r,u as n,o as i,c as d,b as h,e as v,I as m,A as k,B as p,C as g,i as f,h as y,n as C,a9 as _,aK as j,p as w,j as I}from"./index.7c7944d0.js";import{v as b}from"./chunk.d5d38f7a.js";const x=a=>(w("data-v-750938d2"),a=a(),I(),a),z={key:0,class:C(["home"])},T=x((()=>h("div",{class:"home-header"},[h("img",{src:"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png",alt:""})],-1))),q={class:"authCheck"},S={class:"authCheck-content"},A=x((()=>h("div",{class:"authCheck-title"},"您正在登录 SoYoung 画廊 Sketch 插件",-1))),B={class:"authCheck-card"},K={class:"authCheck-card-img"},Y=["src"],D={class:"authCheck-card-name"},E={key:0,class:"authCheck-card-countdown"},F=e(t({__name:"authCheck",setup(e){const t=c(),C=l(),w=u(),I=o(!1),x=o(!1),F=o(null);r((()=>{const{token:a,from:s}=C.query;"gallery-sketch"===s&&a&&(I.value=!0)}));const G=async()=>{await _(),t.clearInfo(),a({type:"success",message:"退出成功"})},H=async()=>{const{token:s}=C.query;if(!s)return void a({type:"error",message:"授权失败"});x.value=!0;const e=await j({token:s});x.value=!1,0===e.code?(F.value=5,L()):a({type:"error",message:e.msg||"授权失败"})};let J;const L=()=>{J&&clearTimeout(J),J=setTimeout((()=>{if(F.value--,0===F.value)return clearTimeout(J),F.value=null,void w.push({path:"/"});L()}),1e3)};return(a,e)=>{const c=s,l=b;return n(t).ssoId&&I.value?(i(),d("div",z,[T,h("div",q,[h("div",S,[A,h("div",B,[h("div",K,[h("img",{src:n(t).url,alt:""},null,8,Y)]),h("div",D,v(n(t).name),1),m((i(),k(c,{onClick:H,disabled:null!==F.value,color:"#626aef",size:"large"},{default:p((()=>[g("登录")])),_:1},8,["disabled"])),[[l,x.value]]),f(c,{onClick:G,size:"large"},{default:p((()=>[g("切换账号")])),_:1}),null!==F.value?(i(),d("div",E,v(F.value)+"s后自动跳转",1)):y("",!0)])])])])):y("",!0)}}}),[["__scopeId","data-v-750938d2"]]);export{F as default};
//# sourceMappingURL=chunk.2711b925.js.map
