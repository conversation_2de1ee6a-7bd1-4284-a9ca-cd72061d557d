{"version": 3, "file": "chunk.6d705473.js", "sources": ["../src/model/index.ts"], "sourcesContent": ["export enum ErrorCode {\n  OK = 0, // 正常处理完成\n\n  ILLEGAL = 100000, // 全局错误10\n  SERVER_REPAIRING = 100001, // 服务器正在维护中\n  NOT_ALLOWED = 100002, // 无权限进行此操作\n  MOCK_LOGIN_NOT_PERMIT = 100003, // 免登陆不允许进行此操作\n\n  PARAMS = 100100, // 参数错误\n  NOT_FOUND = 100101, // 数据未找到\n  // 验证相关\n  AUTH = 100200, // 用户验证失败\n  LOGIN_VALIDATION = 100201, // 用户登录失败\n\n  // body相关\n  PAYLOAD_TOOLLARGE = 100400, // 负荷太大 （ body 超出 了最大限制 ）\n  UPLOAD_ERROR = 100401, // 上传失败\n\n  // 数据库相关\n  DB_DATA_NOT_FOUND = 100501 // 未找到对应数据\n}\n\nexport enum Permission {\n  OWNER = \"owner\", // 所有者， 这个数据是创建者，不会存在数据库中\n  EDIT = \"edit\", // 编辑模式\n  MANAGE = \"manage\", // 管理模式\n  PREVIEW = \"preview\" // 预览模式\n}\n\nexport const permissionName = {\n  [Permission.EDIT]: \"编辑\",\n  [Permission.MANAGE]: \"管理\",\n  [Permission.PREVIEW]: \"预览\"\n};\n// 邀请分类\nexport enum InviteCategory {\n  SMB = \"smb\"\n}\n\nexport enum NotifyCategory {\n  PROJECT = \"project\", // 项目\n  TEAM = \"team\" //  团队\n}\n\nexport enum NotifyAction {\n  ADD = \"add\", // 新增\n  UPDATE = \"update\", // 更新\n  DELETE = \"delete\", // 删除\n  INVITE = \"invite\" // 邀请\n}\n"], "names": ["ErrorCode", "Permission", "permissionName", "edit", "manage", "preview", "InviteCategory"], "mappings": "AAAY,IAAAA,GAAAA,IACVA,EAAAA,KAAK,GAAL,KAEAA,EAAAA,UAAU,KAAV,UACAA,EAAAA,mBAAmB,QAAnB,mBACAA,EAAAA,cAAc,QAAd,cACAA,EAAAA,wBAAwB,QAAxB,wBAEAA,EAAAA,SAAS,QAAT,SACAA,EAAAA,YAAY,QAAZ,YAEAA,EAAAA,OAAO,QAAP,OACAA,EAAAA,mBAAmB,QAAnB,mBAGAA,EAAAA,oBAAoB,QAApB,oBACAA,EAAAA,eAAe,QAAf,eAGAA,EAAAA,oBAAoB,QAApB,oBAnBUA,IAAAA,GAAA,CAAA,GAsBAC,GAAAA,IACVA,EAAQ,MAAA,QACRA,EAAO,KAAA,OACPA,EAAS,OAAA,SACTA,EAAU,QAAA,UAJAA,IAAAA,GAAA,CAAA,GAOL,MAAMC,EAAiB,CAC5BC,KAAmB,KACnBC,OAAqB,KACrBC,QAAsB,MAGZ,IAAAC,GAAAA,IACVA,EAAM,IAAA,MADIA,IAAAA,GAAA,CAAA"}