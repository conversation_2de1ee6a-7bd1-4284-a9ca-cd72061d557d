{"version": 3, "file": "chunk.4544d6f0.js", "sources": ["../src/store/modules/board.ts", "../src/views/designCooperate/components/board.vue", "../src/views/designCooperate/components/groupTree.vue", "../src/views/designCooperate/list.vue"], "sourcesContent": ["import { defineStore } from \"pinia\";\n\nexport const useBoardStore = defineStore(\"boardStore\", {\n  state: () => {\n    return {\n      hoverGroupInfo: {\n        id: null,\n        groupId: null,\n        source: null\n      }\n    };\n  },\n  actions: {\n    setHoverGroupInfo(group) {\n      this.hoverGroupInfo = group;\n    }\n  }\n});\n", "<template>\n  <div class=\"board\">\n    <div v-show=\"loading\" class=\"board-loading\" v-loading=\"loading\"></div>\n    <v-stage ref=\"stage\" :config=\"configKonva\" @dragend=\"onDragend($event)\" @wheel=\"handleWheel($event)\">\n      <v-layer ref=\"boardLayer\" :config=\"{}\">\n        <v-group v-for=\"[key, value] of imageDataMap\" :key=\"`group-${key}`\" :config=\"{\n          x: value.position_x,\n          y: value.position_y,\n          draggable: groupCanDrag\n        }\" @dblclick=\"handleGroupBdClick(value._id)\" @click=\"handleGroupClick(value._id, value.groupId)\" @mouseenter=\"\n          (e) =>\n            handleGroupMouseEnter({\n              id: value._id,\n              e: e\n            })\n        \" @mouseleave=\"\n          (e) =>\n            handleGroupMouseLeave({\n              id: value._id,\n              e: e\n            })\n        \" @dragmove=\"\n          (e) =>\n            handleGroupDragmove({\n              e,\n              id: value._id\n            })\n        \" @dragend=\"\n          (e) =>\n            handleGroupDragend({\n              e,\n              id: value._id\n            })\n        \" @mousedown=\"(e) => handleGroupMouseDown({ e, id: value._id })\">\n          <v-image :config=\"{\n            image: value.imageElement,\n            width: value.width || 100,\n            height: value.height || 100\n          }\" />\n          <v-rect :config=\"{\n            visible: value.borderVisible || false,\n            width: value.width,\n            height: value.height,\n            stroke: hoverColor,\n            strokeWidth: 4\n          }\" />\n          <v-text :config=\"{\n            x: 0,\n            y: -40,\n            text: value.name,\n            fontSize: 24,\n            fontFamily: 'Calibri',\n            fill: value.borderVisible ? hoverColor : '#555',\n            align: 'left'\n          }\"></v-text>\n        </v-group>\n        <v-line v-for=\"(line, index) in helperLines\" :key=\"`helperLine-${index}`\" :config=\"line\" />\n      </v-layer>\n    </v-stage>\n\n    <div class=\"board-toolbar\">\n      <div :class=\"{ 'bottom-toolbar__left': true, 'bottom-toolbar__left-disable': scaleNum < 10 }\"\n        @click=\"handleScaleChange(false)\">\n        <el-icon>\n          <Minus />\n        </el-icon>\n      </div>\n      <div class=\"bottom-toolbar__middle\">{{ scaleNum }}%</div>\n      <div :class=\"{ 'bottom-toolbar__right': true, 'bottom-toolbar__right-disable': scaleNum > 300 }\"\n        @click=\"handleScaleChange(true)\">\n        <el-icon>\n          <Plus />\n        </el-icon>\n      </div>\n    </div>\n\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { defineProps, getCurrentInstance, watch, ref, defineEmits, onMounted, onUnmounted, computed, nextTick } from \"vue\";\nimport { Minus, Plus } from \"@element-plus/icons-vue\";\nimport { cloneDeep, debounce } from \"lodash\";\nimport { ObjectAny } from \"@/types\";\nimport { useRouter } from \"vue-router\";\nimport { useBoardStore } from \"@/store/modules/board\";\nimport { setSketchPosition } from \"@/api/design\";\nimport { ElMessage } from \"element-plus\";\nimport { Permission } from \"@/model\";\nimport hotkeys from \"hotkeys-js\";\nconst boardStore = useBoardStore();\nconst router = useRouter();\n\nconst props = defineProps<{\n  renderData: any;\n  loading: boolean;\n  name?: string;\n  teamId?: string;\n  permission: Permission;\n}>();\n\nconst emit = defineEmits([\"update:loading\"]);\nconst ins = getCurrentInstance();\nconst insProxy = ins!.proxy;\nconst stage = ref<any>(null);\nconst imageDataMap = ref(new Map());\nconst helperLines = ref<ObjectAny[]>([]);\nconst scaleNum = ref<any>(100);\nconst hoverColor = \"#5c54f0\";\n\nconst groupCanDrag = computed(() => {\n  if (props.permission === Permission.PREVIEW) {\n    return false;\n  }\n  return configKonva.value.draggable !== true;\n});\n\nconst configKonva = ref<ObjectAny>({\n  width: 0,\n  height: 0,\n  x: 0,\n  y: 0,\n  draggable: false\n});\n\n\nonMounted(() => {\n\n  setHotKeys();\n  window.addEventListener(\"keydown\", spaceDown);\n  window.addEventListener(\"keyup\", spaceUp);\n  window.addEventListener(\"resize\", handleResize);\n});\n\nonUnmounted(() => {\n  window.removeEventListener(\"keydown\", spaceDown);\n  window.removeEventListener(\"keyup\", spaceUp);\n  window.removeEventListener(\"resize\", handleResize);\n});\n\nconst createPlaceholderImage = (sketch: any): Promise<HTMLImageElement> => {\n  const canvas = document.createElement('canvas');\n  canvas.width = sketch.width;\n  canvas.height = sketch.height;\n  const ctx = canvas.getContext('2d') as any;\n  // 填充浅灰色背景\n  ctx.fillStyle = '#F3F4F6';\n  ctx.fillRect(0, 0, sketch.width, sketch.height);\n\n  // 加载图片\n  const img = new Image();\n  img.crossOrigin = 'anonymous'; // 处理跨域问题\n  img.src = 'https://static.soyoung.com/sy-pre/image-1741831800627.png';\n\n  return new Promise((resolve) => {\n    img.onload = () => {\n      // 计算居中位置\n      const x = (sketch.width - sketch.width / 3) / 2;\n      const y = (sketch.height - sketch.width / 3) / 2;\n      // 绘制30x30图片\n      ctx.drawImage(img, x, y, sketch.width / 3, sketch.width / 3);\n      const src = canvas.toDataURL('image/png');\n      const image = new Image();\n      image.src = src;\n      // 转换为DataURL\n      resolve(image);\n    };\n  });\n}\nconst handleResize = () => {\n  configKonva.value.width = window.innerWidth;\n  configKonva.value.height = window.innerHeight;\n};\nconst setHotKeys = () => {\n  hotkeys(\"command-=\", { splitKey: \"-\" }, (event) => {\n    event.preventDefault();\n    handleScaleChange(true);\n  });\n  hotkeys(\"command+-\", (event) => {\n    event.preventDefault();\n    handleScaleChange(false);\n  });\n};\nconst spaceDown = (e) => {\n  if (e.keyCode !== 32) {\n    return;\n  }\n  configKonva.value.draggable = true;\n};\n\nconst spaceUp = () => {\n  configKonva.value.draggable = false;\n};\n\nconst onDragend = (e) => {\n  if (e.target.getClassName() !== \"Stage\") {\n    return;\n  }\n  configKonva.value.x = e.target.x();\n  configKonva.value.y = e.target.y();\n};\n\n// 增加鼠标滚轮滚动画布的拖拽效果\nconst handleWheel = (e) => {\n  e.evt.preventDefault();\n  let oldPos = {\n    x: configKonva.value.x,\n    y: configKonva.value.y\n  };\n  let newPos = {\n    x: oldPos.x - e.evt.deltaX,\n    y: oldPos.y - e.evt.deltaY\n  };\n  configKonva.value.x = newPos.x;\n  configKonva.value.y = newPos.y;\n};\nconst loadImage = (url: string, sketch: any) => {\n  return new Promise<void>((resolve) => {\n    const originalUrl = url;\n    let modifiedUrl = url + '?imageView2/0/format/webp';\n\n    const createImage = (src: string, isRetry = false) => {\n      const img = new Image();\n      img.src = src;\n\n      img.onload = () => {\n        sketch.imageElement = img;\n        resolve();\n      };\n      img.onerror = () => {\n        // 第一次重试时使用原始URL\n        if (!isRetry && modifiedUrl !== originalUrl) {\n          createImage(originalUrl, true);\n        }\n        // 加载默认图片\n        else {\n          console.log(\"load failed:\", url);\n          resolve();\n        }\n      };\n    };\n    createImage(modifiedUrl);\n  });\n};\nconst initKonva = async (sketchList: any[]) => {\n  console.log(\"initKonva\", sketchList);\n  configKonva.value.width = document.body.clientWidth;\n  configKonva.value.height = document.body.clientHeight;\n  imageDataMap.value = new Map();\n\n  if (!Array.isArray(sketchList) || sketchList.length == 0) {\n    emit(\"update:loading\", false);\n    setTimeout(() => {\n      ElMessage.error(\"没有发现可用数据\");\n    }, 200);\n    return;\n  }\n\n  return eachSketchInfo(sketchList);\n};\n\nconst eachSketchInfo = async (sketchList: any[]) => {\n  let rect = {\n    x1: 0,\n    x2: 0,\n    y1: 0,\n    y2: 0\n  };\n\n  // 最左上角的那个图\n  let rectLeftTopImage = sketchList[0];\n  const tasks: any[] = [];\n  await Promise.all(sketchList.map(async (sketch) => {\n    if (!sketch.position_x) {\n      sketch.position_x = sketch.x;\n    }\n    if (!sketch.position_y) {\n      sketch.position_y = sketch.y;\n    }\n    let { x1, x2, y1, y2 } = rect;\n    x1 = sketch.position_x > x1 ? x1 : sketch.position_x;\n    x2 = sketch.position_x < x2 ? x2 : sketch.position_x;\n    y1 = sketch.position_y > y1 ? y1 : sketch.position_y;\n    y2 = sketch.position_y < y2 ? y2 : sketch.position_y;\n    rect = {\n      x1,\n      x2,\n      y1,\n      y2\n    };\n\n    // 遍历所有的图，找到最左上角的图\n    if (sketch.position_x < rectLeftTopImage.position_x && sketch.position_y < rectLeftTopImage.position_y) {\n      rectLeftTopImage = sketch;\n    }\n\n    sketch.imageElement = await createPlaceholderImage(sketch);\n    sketch.borderVisible = false;\n\n    imageDataMap.value.set(sketch._id, sketch);\n    tasks.push(loadImage(sketch.imagePath, sketch))\n  }))\n\n  nextTick().then(async () => {\n    await Promise.all(tasks)\n    imageDataMap.value = new Map(imageDataMap.value);\n    // insProxy?.$forceUpdate();\n  });\n\n  // for (let [key, value] of imageDataMap.value) {\n  //   // const image = await loadImage(value.imagePath, value);\n  //   value.imageElement = (await loadImage(value.imagePath, value)) as any;\n  // }\n\n  const totalWidth = rect.x2 - rect.x1;\n  const totalHeight = rect.y2 - rect.y1;\n  const viewportWidth = document.body.clientWidth;\n  const viewportHeight = document.body.clientHeight;\n\n  const scaleX = viewportWidth / totalWidth;\n  const scaleY = viewportHeight / totalHeight;\n\n  let scale = Number(Math.min(scaleX, scaleY).toFixed(2));\n\n  // todo\n  // 初始化比例还需要优化，还要根据图大小来调整\n  if (scale < 0.3) {\n    scale = 0.3;\n  }\n\n  if (scale > 1) {\n    scale = 0.8;\n  }\n\n  scaleNum.value = Number((scale * 100).toFixed(2));\n  configKonva.value.scale = {\n    x: scale,\n    y: scale\n  };\n  console.log(\"左上角的图\", rectLeftTopImage);\n\n  const findItem = imageDataMap.value.get(boardStore.hoverGroupInfo.id);\n  rectLeftTopImage = findItem || rectLeftTopImage;\n\n  boardStore.setHoverGroupInfo({\n    id: rectLeftTopImage._id,\n    groupId: rectLeftTopImage.groupId,\n    source: \"konva\"\n  });\n\n  handleMovePositionXYToScreenCenter(rectLeftTopImage);\n\n  console.log(\"初始化konva\", configKonva.value);\n  emit(\"update:loading\", false);\n  // konvaRect.value = rect;\n};\n\nconst handleMovePositionXYToScreenCenter = (sketch) => {\n  const x = sketch.position_x || sketch.x;\n  const y = sketch.position_y || sketch.y;\n\n  console.log(\"handleMovePositionXYToScreenCenter\", x, y);\n\n  const viewportWidth = document.body.clientWidth;\n  const viewportHeight = document.body.clientHeight;\n  const scale = scaleNum.value / 100;\n  let actualX = x * scale;\n  let actualY = y * scale;\n\n  // 计算将图像移动到屏幕中心所需的x和y坐标\n  const centerX = viewportWidth / 2;\n  const centerY = viewportHeight / 2;\n  let _x = centerX - actualX;\n  let _y = centerY - actualY;\n\n  configKonva.value.x = _x;\n  configKonva.value.y = _y;\n\n  console.log(\"configKonva\", configKonva.value);\n};\n\nwatch(\n  () => props.renderData,\n  (newVal) => {\n    if (Array.isArray(newVal)) {\n      initKonva(cloneDeep(props.renderData));\n    }\n  },\n  {\n    deep: true\n  }\n);\n\nwatch(\n  () => props.loading,\n  (newVal) => {\n    console.log(\"newVal-props.loading\", newVal);\n  },\n  {\n    immediate: true\n  }\n);\n\nwatch(\n  () => boardStore.hoverGroupInfo,\n  (newVal) => {\n    if (newVal) {\n      const image = imageDataMap.value.get(newVal.id);\n\n      if (!image) {\n        console.log(\"没有找到元素\");\n        return;\n      }\n\n      setImageBorderVisible(image._id, true);\n      if (newVal.source === \"groupTree\") {\n        handleMovePositionXYToScreenCenter(image);\n      }\n    }\n  },\n  { deep: true }\n);\n\nconst handleScaleChange = (value: boolean) => {\n  const step = 5;\n  if (value) {\n    if (scaleNum.value >= 300) {\n      return;\n    }\n    scaleNum.value += step;\n  } else {\n    if (scaleNum.value <= 5) {\n      return;\n    }\n    scaleNum.value -= step;\n  }\n  configKonva.value.scale = {\n    x: scaleNum.value / 100,\n    y: scaleNum.value / 100\n  };\n};\n\nconst setImageBorderVisible = (id: string, visible: boolean) => {\n  const image = imageDataMap.value.get(id);\n  if (image) {\n    imageDataMap.value.forEach((_) => {\n      _.borderVisible = false;\n    });\n    image.borderVisible = visible;\n  }\n};\n\nconst handleGroupClick = (id: string, groupId: string) => {\n  const image = imageDataMap.value.get(id);\n  if (!image) {\n    return;\n  }\n\n  setImageBorderVisible(id, true);\n\n  boardStore.setHoverGroupInfo({\n    id: id,\n    groupId: groupId,\n    source: \"konva\"\n  });\n};\n\nconst handleGroupBdClick = (id: string) => {\n  router.push({\n    path: \"/item/project/detail\",\n    query: {\n      id: id,\n      teamId: props.teamId\n    }\n  });\n};\n\ninterface MouseEvent {\n  id: string;\n  e: any; // 这里你可以替换为具体的事件类型，例如MouseEvent\n}\n\nconst handleChangeSketchPosition = debounce(({ id, x, y }) => {\n  console.log(\"handleChangeSketchPosition\", x, y);\n  setSketchPosition({\n    id,\n    x,\n    y\n  });\n}, 100);\n\nconst handleGroupMouseEnter = ({ id, e }: MouseEvent) => {\n  // 检查id在不在imageDataMap的最后，如果不在就挪过去\n  // 这样konvajs渲染才能在再高zIndex级\n  if (imageDataMap.value.size > 0) {\n    const lastId = Array.from(imageDataMap.value.keys()).pop();\n    if (lastId !== id) {\n      const lastItem = imageDataMap.value.get(lastId);\n      const currentItem = imageDataMap.value.get(id);\n      if (lastItem && currentItem) {\n        imageDataMap.value.delete(id);\n        imageDataMap.value.set(id, currentItem);\n      }\n    }\n  }\n\n  // const img = imageDataMap.value.get(id);\n  // if (img) {\n  //   setImageBorderVisible(id, true);\n  // }\n  e.evt.target.style.cursor = \"pointer\";\n  helperLines.value = [];\n};\n\nconst handleGroupMouseLeave = ({ id, e }: MouseEvent) => {\n  // setImageBorderVisible(id, false);\n  e.evt.target.style.cursor = \"default\";\n  helperLines.value = [];\n};\n\nconst drawHelperLine = (x1: number, y1: number, x2: number, y2: number) => {\n  // console.log(\"drawHelperLine\", x1, y1, x2, y2);\n  helperLines.value.push({\n    points: [x1, y1, x2, y2],\n    stroke: \"red\",\n    strokeWidth: 2\n    // dash: [2, 2]\n  });\n};\n\nconst handleGroupDragmove = ({ id, e }: MouseEvent) => {\n  const movePosition = e.target.getStage().getPointerPosition();\n\n  if (!movePosition) {\n    return;\n  }\n\n  helperLines.value = [];\n\n  const movedX = e.target.attrs.x;\n  const movedY = e.target.attrs.y;\n\n  const currentGroup = imageDataMap.value.get(id);\n  if (!currentGroup) return;\n\n  currentGroup.position_x = movedX;\n  currentGroup.position_y = movedY;\n  // const item = { ...imageDataMap.value.get(id) };\n\n  handleChangeSketchPosition({\n    id,\n    x: movedX,\n    y: movedY\n  });\n  //\n  //\n  // currentGroupEl.zIndex(16)\n  // currentGroupEl.moveToTop()\n  // currentGroupEl.getLayer().draw()\n  // console.log('currentGroupEl',currentGroupEl.zIndex(16))\n\n  // todo 这里也涉及缩放，要不然画布缩放不一致的时候会有问题\n  const minThreshold = 10;\n  const collisionThreshold = 20;\n\n  // todo\n  // 遍历所有肯定不对。。。但是不知道咋空间换时间\n  imageDataMap.value.forEach((group) => {\n    // 碰撞检测\n    // https://learnopengl-cn.github.io/06%20In%20Practice/2D-Game/05%20Collisions/02%20Collision%20detection/\n\n    const isLeft = movedX < group.position_x;\n    const isTop = movedY < group.position_y;\n\n    const isCollisionX = isLeft ? movedX + currentGroup.width + collisionThreshold > group.position_x && movedX < group.position_x + group.width : movedX - collisionThreshold < group.position_x + group.width && movedX > group.position_x;\n    const isCollisionY = isTop ? movedY + currentGroup.height + collisionThreshold > group.position_y && movedY < group.position_y + group.height : movedY - collisionThreshold < group.position_y + group.height && movedY > group.position_y;\n\n    // 单独处理的定位坐标\n    // let position_x = movedX;\n    // let position_y = movedY;\n\n    if (group._id !== id) {\n      if (isCollisionX && isCollisionY) {\n        // console.log(\"isCollisionX && isCollisionY\");\n\n        // todo 判断方向和距离，如果距离小于一定值，就吸附\n        // 计算两个组的边界位置\n        const currentGroupLeft = movedX;\n        const currentGroupRight = movedX + currentGroup.width;\n        const currentGroupTop = movedY;\n        const currentGroupBottom = movedY + currentGroup.height;\n\n        const groupLeft = group.position_x;\n        const groupRight = group.position_x + group.width;\n        const groupTop = group.position_y;\n        const groupBottom = group.position_y + group.height;\n\n        // 计算每个方向的25%区域\n        const groupLeft25 = groupLeft + group.width * 0.75;\n        const groupRight25 = groupRight - group.width * 0.75;\n        const groupTop25 = groupTop + group.height * 0.75;\n        const groupBottom25 = groupBottom - group.height * 0.75;\n\n        const isLeft = movedX < group.position_x;\n        console.log(\"isLeft\", isLeft ? \"左边\" : \"右边\");\n\n        console.log(\"movedX\", movedX);\n        console.log(\"currentGroupLeft - collisionThreshold < groupRight\", currentGroupLeft - collisionThreshold < groupRight);\n\n        // 判断碰撞方向\n        const isCollisionLeft = currentGroupRight + collisionThreshold > groupLeft && currentGroupRight + collisionThreshold < groupLeft25;\n        const isCollisionRight = currentGroupLeft - collisionThreshold < groupRight && currentGroupLeft - collisionThreshold > groupRight25;\n        const isCollisionTop = currentGroupBottom + collisionThreshold > groupTop && currentGroupBottom + collisionThreshold < groupTop25;\n        const isCollisionBottom = currentGroupTop - collisionThreshold < groupBottom && currentGroupTop - collisionThreshold > groupBottom25;\n\n        console.log(\"isCollisionTop\", isCollisionTop);\n        console.log(\"isCollisionBottom\", isCollisionBottom);\n        console.log(\"isCollisionLeft\", isCollisionLeft);\n        console.log(\"isCollisionRight\", isCollisionRight);\n\n        let direction = \"\";\n        if (isCollisionRight) direction = \"right\";\n        else if (isCollisionLeft) direction = \"left\";\n        else if (isCollisionBottom) direction = \"bottom\";\n        else if (isCollisionTop) direction = \"top\";\n\n        switch (direction) {\n          case \"right\":\n            currentGroup.position_x = group.position_x + group.width;\n            e.target.attrs.x = currentGroup.position_x;\n            // position_x = currentGroup.position_x;\n            break;\n          case \"left\":\n            currentGroup.position_x = group.position_x - currentGroup.width;\n            e.target.attrs.x = currentGroup.position_x;\n            // position_x = currentGroup.position_x;\n            break;\n          case \"bottom\":\n            currentGroup.position_y = group.position_y + group.height;\n            e.target.attrs.y = currentGroup.position_y;\n            // position_y = currentGroup.position_y;\n            break;\n          case \"top\":\n            currentGroup.position_y = group.position_y - currentGroup.height;\n            e.target.attrs.y = currentGroup.position_y;\n            // position_y = currentGroup.position_y;\n            break;\n          default:\n            break;\n        }\n      }\n\n      // 画辅助线\n      const isLeftAligned = Math.abs(movedX - group.position_x) < minThreshold;\n      const isRightAligned = Math.abs(movedX + currentGroup.width - (group.position_x + group.width)) < minThreshold;\n      const isTopAligned = Math.abs(movedY - group.position_y) <= minThreshold;\n      const isBottomAligned = Math.abs(movedY + currentGroup.height - (group.position_y + group.height)) < minThreshold;\n\n      const isCurrentLeftAndGroupRightAligned = Math.abs(movedX - (group.position_x + group.width)) < minThreshold;\n      const isCurrentRightAndGroupLeftAligned = Math.abs(movedX + currentGroup.width - group.position_x) < minThreshold;\n      const isCurrentTopAndGroupBottomAligned = Math.abs(movedY - (group.position_y + group.height)) < minThreshold;\n      const isCurrentBottomAndGroupTopAligned = Math.abs(movedY + currentGroup.height - group.position_y) < minThreshold;\n\n      // 如果currentGroup的左边和group的右边在相同Y的时候，画辅助线\n      if (isCurrentLeftAndGroupRightAligned) {\n        const y1 = isTop ? movedY : group.position_y;\n        const y2 = isTop ? group.position_y + group.height : movedY;\n        drawHelperLine(movedX, y1, movedX, y2);\n      }\n\n      if (isCurrentRightAndGroupLeftAligned) {\n        const y1 = isTop ? movedY : group.position_y;\n        const y2 = isTop ? group.position_y + group.height : movedY;\n        drawHelperLine(movedX + currentGroup.width, y1, movedX + currentGroup.width, y2);\n      }\n\n      if (isCurrentTopAndGroupBottomAligned) {\n        const x1 = isLeft ? movedX : group.position_x;\n        const x2 = isLeft ? group.position_x + group.width : movedX;\n        drawHelperLine(x1, movedY, x2, movedY);\n      }\n\n      if (isCurrentBottomAndGroupTopAligned) {\n        const x1 = isLeft ? movedX : group.position_x;\n        const x2 = isLeft ? group.position_x + group.width : movedX;\n        drawHelperLine(x1, movedY + currentGroup.height, x2, movedY + currentGroup.height);\n      }\n\n      if (isTopAligned) {\n        // console.log(\"isTopAligned\");\n        // console.log(\"movedX\", movedX);\n        // console.log(\"group.position_x\", group.position_x);\n        // console.log(\"isLeft\", isLeft ? \"左边\" : \"右边\");\n        const x1 = isLeft ? movedX : group.position_x;\n        const x2 = isLeft ? group.position_x + group.width : movedX;\n\n        drawHelperLine(x1, movedY, x2, movedY);\n      }\n\n      if (isBottomAligned) {\n        // console.log(\"isBottomAligned\");\n        const x1 = isLeft ? movedX : group.position_x;\n        const x2 = isLeft ? group.position_x + group.width : movedX;\n        drawHelperLine(x1, movedY + currentGroup.height, x2, movedY + currentGroup.height);\n      }\n\n      if (isLeftAligned) {\n        // console.log(\"isLeftAligned\");\n        const isTop = movedY < group.position_y;\n        const y1 = isTop ? movedY : group.position_y;\n        const y2 = isTop ? group.position_y + group.height : movedY;\n        drawHelperLine(movedX, y1, movedX, y2);\n      }\n\n      if (isRightAligned) {\n        // console.log(\"isRightAligned\");\n        const y1 = isTop ? movedY : group.position_y;\n        const y2 = isTop ? group.position_y + group.height : movedY;\n        drawHelperLine(movedX + currentGroup.width, y1, movedX + currentGroup.width, y2);\n      }\n    }\n  });\n\n  // 更新当前组的坐标\n  // if (group._id == id && (group.position_x != position_x || group.position_y != position_y) && (!isCollisionX || !isCollisionY)) {\n  //   group.position_x = position_x;\n  //   group.position_y = position_y;\n  //   return;\n  // }\n};\nconst handleGroupDragend = ({ id, e }: MouseEvent) => {\n  console.log(id, e);\n  helperLines.value = [];\n};\n\nconst handleGroupMouseDown = ({ id, e }: MouseEvent) => {\n  console.log(e, id);\n};\n</script>\n\n<style scoped lang=\"less\">\n.board {\n  width: 100vw;\n  height: 100vh;\n  position: relative;\n  z-index: 300;\n\n  &-loading {\n    width: 100vw;\n    height: 100vh;\n  }\n\n  &-toolbar {\n    position: fixed;\n    width: 170px;\n    height: 40px;\n    background: #ffffff;\n    display: inline-flex;\n    padding: 22px;\n    box-shadow: 0 4px 8px 0 rgba(29, 41, 57, 0.1);\n    border-radius: 6px;\n    opacity: 1;\n    border: 1px solid #e4e4e5;\n    right: 24px;\n    bottom: 24px;\n    box-sizing: border-box;\n    align-items: center;\n    z-index: 12;\n\n    .bottom-toolbar__left,\n    .bottom-toolbar__right {\n      cursor: pointer;\n\n      &-disable {\n        cursor: not-allowed;\n        opacity: 0.5;\n      }\n    }\n\n    i {\n      font-size: 12px;\n      color: #909299;\n    }\n\n    .bottom-toolbar__middle {\n      font-size: 14px;\n      color: #303233;\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n    }\n  }\n}\n</style>\n", "<template>\n  <el-tree draggable :allow-drop=\"allowDrop\" class=\"group-tree\" @node-drop=\"handleDrop\" @node-click=\"handleNodeClick\" :expand-on-click-node=\"false\" :props=\"treeProps\" :data=\"treeList\" node-key=\"_id\" default-expand-all>\n    <template #default=\"{ data }\">\n      <div :id=\"`groupTree-${data._id}`\" style=\"display: flex; flex: 1; height: 100%;\">\n        <div v-if=\"data.type !== 'sketch'\" class=\"group-tree-node\">\n          <div>\n            <i class=\"iconfont icon-a-sucaiku3x\"></i> <span>{{ data.name }}</span>\n          </div>\n          <div v-if=\"permission !== Permission.PREVIEW\" class=\"node-right-bar\">\n            <span class=\"node-count\">{{ data.count || 0 }}</span>\n            <div class=\"node-right-bar-handle\" @click.stop>\n              <el-dropdown trigger=\"click\" placement=\"bottom-start\">\n                <el-button type=\"text\" style=\"margin-right: 10px; margin-top: 2px\">\n                  <span style=\"transform: rotate(90deg)\"><i class=\"iconfont icon-gengduo svg-icon\"></i></span>\n                </el-button>\n                <template #dropdown>\n                  <el-dropdown-menu>\n                    <el-dropdown-item @click.stop=\"renameGroup(data)\">重命名</el-dropdown-item>\n\n                    <el-dropdown-item @click.stop=\"deleteGroup(data)\">删除分组</el-dropdown-item>\n                    <!-- <el-dropdown-item>删除分组</el-dropdown-item> -->\n                  </el-dropdown-menu>\n                </template>\n              </el-dropdown>\n\n              <!-- <el-button @click=\"emit('add', data)\" type=\"text\">\n                <el-icon><Plus /></el-icon>\n              </el-button> -->\n            </div>\n          </div>\n        </div>\n        <div\n          v-else\n          class=\"sketch-tree-node\"\n          :class=\"{\n            'sketch-tree-node--hover': data._id === boardStore?.hoverGroupInfo?.id\n          }\"\n        >\n          <div class=\"node-label\">\n            <el-icon class=\"iconfont\"><Picture /></el-icon><span>{{ data.name }}</span>\n          </div>\n          <div v-if=\"permission !== Permission.PREVIEW\" class=\"node-right-bar\">\n            <div class=\"node-right-bar-handle\" @click.stop>\n              <el-dropdown trigger=\"click\" placement=\"bottom-start\">\n                <el-button type=\"text\">\n                  <span style=\"transform: rotate(90deg)\"><i class=\"iconfont icon-gengduo svg-icon\"></i></span>\n                </el-button>\n                <template #dropdown>\n                  <el-dropdown-menu>\n                    <el-dropdown-item @click.stop=\"renameSketch(data)\">重命名</el-dropdown-item>\n                    <el-dropdown-item @click.stop=\"deleteSketch(data)\">删除</el-dropdown-item>\n\n                    <!-- <el-dropdown-item>移动分组</el-dropdown-item> -->\n                  </el-dropdown-menu>\n                </template>\n              </el-dropdown>\n            </div>\n          </div>\n        </div>\n      </div>\n    </template>\n  </el-tree>\n</template>\n<script lang=\"ts\" setup>\nimport type Node from \"element-plus/es/components/tree/src/model/node\";\nimport type { NodeDropType } from \"element-plus/es/components/tree/src/tree.type\";\nimport { deleteSketchById, deleteGroupById, updateGroup, updateSketch } from \"@/api/design\";\n\nimport { Permission } from \"@/model\";\nimport { Picture } from \"@element-plus/icons-vue\";\nimport { defineProps } from \"vue\";\nimport { ErrorCode } from \"@/model\";\nimport { useBoardStore } from \"@/store/modules/board\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nconst boardStore = useBoardStore();\nimport { watch } from \"vue\";\nimport { ObjectAny } from \"@/types\";\ndefineProps<{\n  treeList?: any[];\n  permission: Permission;\n}>();\n\nconst emit = defineEmits([\"add\", \"move\", \"nodeClick\", \"refresh\"]);\nconst treeProps = {\n  children: \"subs\"\n};\n\nconst handleDrop = (draggingNode: Node, dropNode: Node, dropType: NodeDropType) => {\n  console.log(\"tree drop:\", dropNode.label, dropType);\n  const params = {\n    id: draggingNode.data._id,\n    parentId: dropNode.data._id,\n    type: draggingNode.data.type === \"sketch\" ? \"sketch\" : \"group\"\n  };\n  emit(\"move\", params);\n};\nconst allowDrop = (draggingNode: Node, dropNode: Node) => {\n  if (dropNode.data.type === \"sketch\") {\n    return false;\n  }\n  if (draggingNode.data.groupId == dropNode.data._id) {\n    return false;\n  }\n  return true;\n};\nconst handleNodeClick = (clickNode: any) => {\n  console.log(\"nodeClick:\", clickNode);\n\n  if ((!clickNode.type || clickNode.type !== \"sketch\") && (!Array.isArray(clickNode.subs) || clickNode.subs.length === 0)) {\n    ElMessage.warning(\"该分组下没有素材\");\n    return;\n  }\n  emit(\"nodeClick\", clickNode);\n};\n\nconst deleteGroup = async (info: ObjectAny) => {\n  if (info.subs.length) {\n    return ElMessage.warning(\"该分组下有子项目，请先删除子项目\");\n  }\n  await ElMessageBox.confirm(`确定删除“${info.name}”分组吗？`, \"注意\", {\n    confirmButtonText: \"确定\",\n    cancelButtonText: \"取消\",\n    type: \"warning\"\n  });\n  try {\n    const res = await deleteGroupById({\n      id: info._id\n    });\n    if (res.code !== ErrorCode.OK) {\n      throw res.msg;\n    }\n    ElMessage.success(\"删除成功\");\n    emit(\"refresh\");\n  } catch (error: any) {\n    ElMessage.error(error);\n  }\n  console.log(info.subs);\n};\nconst renameGroup = async (data: ObjectAny) => {\n  const input = await ElMessageBox.prompt(\"请输入分组名称\", \"重命名\", {\n    confirmButtonText: \"确认\",\n    cancelButtonText: \"取消\",\n    inputValue: data.name\n  });\n  if (!input.value) {\n    ElMessage({\n      type: \"error\",\n      message: \"名字不能为空\"\n    });\n    return;\n  }\n  const res = await updateGroup({\n    name: input.value,\n    id: data._id\n  });\n  if (res.code === 0) {\n    ElMessage({\n      type: \"success\",\n      message: \"重命名成功\"\n    });\n    emit(\"refresh\");\n  } else {\n    ElMessage({\n      type: \"error\",\n      message: \"重命名失败\"\n    });\n  }\n};\n\nconst deleteSketch = async (info: ObjectAny) => {\n  await ElMessageBox.confirm(`确定删除“${info.name}”素材吗？`, \"注意\", {\n    confirmButtonText: \"确定\",\n    cancelButtonText: \"取消\",\n    type: \"warning\"\n  });\n  try {\n    const res = await deleteSketchById({\n      id: info._id\n    });\n    if (res.code !== ErrorCode.OK) {\n      throw res.msg;\n    }\n    ElMessage.success(\"删除成功\");\n    emit(\"refresh\");\n  } catch (error: any) {\n    ElMessage.error(error);\n  }\n};\nconst renameSketch = async (data: ObjectAny) => {\n  const input = await ElMessageBox.prompt(\"请输入设计图名称\", \"重命名\", {\n    confirmButtonText: \"确认\",\n    cancelButtonText: \"取消\",\n    inputValue: data.name\n  });\n  if (!input.value) {\n    ElMessage({\n      type: \"error\",\n      message: \"名字不能为空\"\n    });\n    return;\n  }\n  const res = await updateSketch({\n    name: input.value,\n    id: data._id\n  });\n  if (res.code === 0) {\n    ElMessage({\n      type: \"success\",\n      message: \"重命名成功\"\n    });\n    emit(\"refresh\");\n  } else {\n    ElMessage({\n      type: \"error\",\n      message: \"重命名失败\"\n    });\n  }\n};\n\nwatch(\n  () => boardStore.hoverGroupInfo,\n  (newVal) => {\n    if (newVal) {\n      // console.log(newVal);\n      const ref = document.getElementById(`groupTree-${newVal.id}`);\n      if (ref) {\n        ref.scrollIntoView({\n          behavior: \"smooth\",\n          block: \"center\"\n        });\n      }\n    }\n  },\n  { deep: true }\n);\n</script>\n<style lang=\"less\" scoped>\n.group-tree {\n  padding: 0 12px;\n  ::v-deep {\n    .el-tree-node {\n      display: flex;\n\n      flex-direction: column;\n    }\n    .el-tree-node__content {\n      display: flex;\n\n      height: 40px;\n      flex-direction: row;\n      justify-content: flex-start;\n    }\n  }\n\n  .group-tree-node,\n  .sketch-tree-node {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    height: 100%;\n    .iconfont {\n      font-size: 14px;\n      margin-right: 5px;\n    }\n\n    &:hover {\n      .node-right-bar-handle {\n        opacity: 1;\n      }\n    }\n    .node-label {\n      flex: 0 0 160px;\n      align-items: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: #333333;\n      line-height: 20px;\n      height: 20px;\n      .el-icon {\n        margin-top: 3px;\n      }\n      span {\n        vertical-align: text-bottom;\n      }\n    }\n\n    .node-right-bar {\n      display: flex;\n      align-items: center;\n      .node-count {\n        font-size: 12px;\n        color: #8e8e8e;\n      }\n      &-handle {\n        opacity: 0;\n        transition: all 0.3s;\n        margin-left: 10px;\n      }\n    }\n    &--hover {\n      color: #5c54f0;\n      background-color: #f5f5f5;\n      ::v-deep {\n        .node-label {\n          color: #5c54f0;\n        }\n        .node-right-bar-handle {\n          opacity: 1;\n        }\n      }\n    }\n  }\n  .sketch-tree-node {\n    width: 200px;\n  }\n}\n</style>\n", "<template>\n  <div class=\"web-list\" @contextmenu=\"handleContextMenu\">\n    <div class=\"web-header-bar\">\n      <div class=\"web-header-logo\">\n        <img src=\"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png\" alt=\"logo\" @click=\"goBack\" />\n      </div>\n      <div class=\"bar-project-name-box\">\n        <template v-if=\"team\">\n          <div class=\"breadcrumb-button\" @click=\"goBack\">\n            {{ team.name }}\n            <!-- <i class=\"iconfont icon-zuojiantou\" @click=\"goBack\"></i> -->\n          </div>\n          <div class=\"breadcrumb-divider\">\n            <el-icon class=\"el-icon--right\">\n              <ArrowRight />\n            </el-icon>\n          </div>\n        </template>\n        <div class=\"project-menu-wrapper-box\">\n          <el-dropdown trigger=\"click\" @command=\"handleCommand\">\n            <span class=\"el-dropdown-link\">\n              {{ project?.name || \"\" }}\n              <el-icon class=\"el-icon--right\">\n                <ArrowDown />\n              </el-icon>\n            </span>\n            <template #dropdown>\n              <el-dropdown-menu class=\"project-dropdown-menu\">\n                <el-dropdown-item v-for=\"info in projectList\" :command=\"info._id\" :key=\"info._id\" :disabled=\"info._id === projectId\">{{ info.name }} </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n        </div>\n      </div>\n      <div class=\"bar-project-menu\">\n        <!--        <Notify />-->\n        <!-- <Share v-if=\"team\" :team=\"team\" :userInfo=\"userInfo\" :url=\"`/#/item/project/stage?projectId=${projectId}&`\" :project=\"project\" /> -->\n        <el-button v-if=\"project\" style=\"margin-right: 25px; width: 60px\" type=\"primary\" @click=\"share\">分享</el-button>\n        <Shortcut type=\"list\" />\n        <!-- <div class=\"bar-project-menu-item\">\n          <el-icon>\n            <BellFilled />\n          </el-icon>\n        </div>\n        <div class=\"bar-project-menu-item\">\n          <el-icon>\n            <MoreFilled />\n          </el-icon>\n        </div>\n        <div class=\"bar-project-menu-item\">\n          <el-button disabled size=\"small\" type=\"primary\">分享</el-button>\n        </div> -->\n      </div>\n    </div>\n\n    <div v-if=\"permission\" class=\"web-list-section-wrap\">\n      <!-- 折叠的全部样式 start -->\n      <div v-if=\"toggleShow\" class=\"section-total-wrap\" style=\"width: 142px; padding: 0 !important\">\n        <div class=\"hide-section\" style=\"background: white\" @click=\"handleToggleChange\">\n          <span class=\"total-name\">全部</span>\n          <div class=\"total-name__icon\">\n            <div class=\"show-exban-button\">\n              <i class=\"iconfont icon-jiantouzhankai\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 折叠的全部样式 end -->\n\n      <!-- 导航栏 start -->\n      <div class=\"nav-tree-content nav-tree-content-show\" v-else>\n        <div class=\"section-total-wrap\">\n          <div class=\"section-total\" @click=\"handleToggleChange\">\n            <span class=\"total-name\">全部</span>\n            <!-- <div class=\"moreSetButton\" style=\"float: right\">\n              <el-dropdown trigger=\"click\" placement=\"bottom-start\">\n                <div class=\"show-exban-button item-more\" @click.stop=\"handleMoreChange\">\n                  <span style=\"transform: rotate(90deg)\"><i class=\"iconfont icon-gengduo svg-icon\"></i></span>\n                </div>\n                <template #dropdown>\n                  <el-dropdown-menu>\n                    <el-dropdown-item>下载图片</el-dropdown-item>\n                    <el-dropdown-item>全选设计图</el-dropdown-item>\n                  </el-dropdown-menu>\n                </template>\n              </el-dropdown>\n            </div> -->\n            <div class=\"total-num\">{{ tree.total }}</div>\n            <div class=\"total-name__icon\">\n              <div class=\"show-exban-button\">\n                <i class=\"iconfont icon-jiantoushouqi\"></i>\n              </div>\n            </div>\n          </div>\n          <!-- 分割线 start -->\n          <div class=\"section-line\"></div>\n          <!-- 分割线 end -->\n\n          <!-- 新建分组 start -->\n          <!-- <div class=\"section-total create-group-content create-group-disable\" @click=\"openGroupDialog()\">\n            <i class=\"iconfont icon-jia\"></i>\n            <span class=\"create-group-content__sapn\"> 新建分组 </span>\n            <span class=\"key-icon\"> <span class=\"key-icon-ctrl\">Shift</span> + <span class=\"key-icon-code\">N</span> </span>\n          </div> -->\n          <!-- 新建分组 end -->\n        </div>\n        <div class=\"section-group-list\">\n          <GroupTree :permission=\"permission\" :tree-list=\"tree.list\" @add=\"openGroupDialog\" @nodeClick=\"nodeClick\" @move=\"handleMove\" @refresh=\"getTreeFn\" />\n        </div>\n      </div>\n      <!-- 导航栏 end -->\n    </div>\n\n    <!-- 设计图操作区 start -->\n    <board v-if=\"permission\" class=\"board\" :team-id=\"teamId\" :permission=\"permission\" :renderData=\"boardList\" :name=\"project?.name\" v-model:loading=\"boardLoading\" @handleBoardGroupClick=\"handleBoardGroupClick\" />\n    <div v-else class=\"board-placeholder\">\n      <el-empty description=\"暂无该项目权限，请联系项目管理员添加\" />\n    </div>\n    <!-- <template v-if=\"permission\">\n      <board v-if=\"boardList.length\" class=\"board\" :team-id=\"teamId\" :permission=\"permission\" :renderData=\"boardList\" :name=\"project?.name\" v-model:loading=\"boardLoading\" @handleBoardGroupClick=\"handleBoardGroupClick\" />\n      <div v-else class=\"board-placeholder\">\n        <el-empty description=\"暂无数据，请添加sketch文件\" />\n      </div>\n    </template>\n    <div v-else class=\"board-placeholder\">\n      <el-empty description=\"暂无该项目权限，请联系项目管理员添加\" />\n    </div> -->\n    <!-- 设计图操作区 end -->\n  </div>\n  <el-dialog class=\"sketch-add\" v-model=\"addGroupInfo.visible\" title=\"新建分组\" width=\"400px\">\n    <div v-if=\"addGroupInfo.parent\" class=\"sketch-add-item\">{{ getFullPath() }} /</div>\n    <div class=\"sketch-add-item\">\n      <el-input placeholder=\"请输入分组名称\" v-model=\"addGroupInfo.name\"></el-input>\n    </div>\n    <template #footer>\n      <span class=\"sketch-add-footer\">\n        <el-button @click=\"groupClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"groupSubmit\"> 确定 </el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n<script lang=\"ts\" setup>\nimport { ref, reactive, onMounted, computed, onUnmounted } from \"vue\";\nimport Shortcut from \"./components/shortcut.vue\";\nimport hotkeys from \"hotkeys-js\";\nimport board from \"./components/board.vue\";\nimport { ArrowDown, ArrowRight } from \"@element-plus/icons-vue\";\nimport { getProjectList, getTree, addGroup, sketchMove, groupMove } from \"@/api/design\";\nimport GroupTree from \"./components/groupTree.vue\";\nimport { ErrorCode, Permission } from \"@/model\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\n\nimport { Group, Project } from \"./model/index\";\nimport { useRouter, useRoute } from \"vue-router\";\nimport { ObjectAny } from \"@/types\";\nimport { useBoardStore } from \"@/store/modules/board\";\nimport Notify from \"./components/notify.vue\";\nimport { smbStore, userInfoStore } from \"@/store\";\nimport { invite, handleShare } from \"./utils\";\n\nconst router = useRouter();\nconst route = useRoute();\nconst toggleShow = ref<boolean>(false); // 是否展开\nconst projectId = ref<string>(\"\");\nconst teamId = ref<string>(\"\");\nconst smbInfo = smbStore();\nconst userInfo = userInfoStore();\n\nconst boardStore = useBoardStore();\n\nconst project = computed(() => {\n  if (projectId.value) {\n    return projectList.value.find((item) => {\n      return item._id === projectId.value;\n    });\n  }\n  return null;\n});\nconst projectList = ref<Project[]>([]);\nconst tree = ref<{\n  total?: number;\n  list?: Group[];\n}>({});\nconst boardLoading = ref<boolean>(true);\nconst boardList = ref<ObjectAny[]>([]);\nconst addGroupInfo = reactive<{ visible: boolean; name: string; parent: any }>({\n  visible: false,\n  name: \"\",\n  parent: null\n});\nconst share = () => {\n  const breadcrumb = [team.value!.name];\n  if (project.value) {\n    breadcrumb.push(project.value.name);\n  }\n  handleShare(team.value!, Permission.PREVIEW, `/#/item/project/stage?projectId=${projectId.value}&`, breadcrumb);\n};\nconst goBack = () => {\n  router.replace({\n    path: \"/item/project/index\",\n    query: {\n      teamId: teamId.value\n    }\n  });\n};\nconst handleContextMenu = () => {\n  // e.stopPropagation();\n  // e.preventDefault();\n};\nconst team = computed(() => {\n  return smbInfo.teamList.find((item) => item._id == teamId.value);\n});\n\nconst permission = computed<Permission | null>(() => {\n  if (!team.value) {\n    return null;\n  }\n  return team.value!.permission;\n});\nconst handleToggleChange = () => {\n  toggleShow.value = !toggleShow.value;\n};\n\n// 点击Icon收起&展开dom树\n// const expandChange = () => {};\n\nconst getProjectListFn = async () => {\n  try {\n    const res = await getProjectList({\n      teamId: route.query.teamId\n    });\n    if (res.code !== ErrorCode.OK) {\n      throw res.msg;\n    }\n    projectList.value = res.data.reverse();\n    if (res.data.length) {\n      let info;\n      if (route.query.projectId) {\n        info = res.data.find((item) => {\n          if (item._id === route.query.projectId) {\n            return true;\n          }\n        });\n      }\n      handleCommand(info ? info._id : res.data[0]._id);\n    }\n  } catch (error: any) {\n    ElMessage.error(error);\n  }\n};\n\nconst getTreeFn = async () => {\n  try {\n    boardLoading.value = true;\n    const res = await getTree({\n      projectId: projectId.value\n    });\n    if (res.code !== ErrorCode.OK) {\n      throw res.msg;\n    }\n\n    tree.value = res.data;\n\n    // 找到第一个type=sketch的分组\n\n    const firstList = res.data.list.find((v) => {\n      if (v.type === \"sketch\") {\n        return true;\n      }\n      if (Array.isArray(v.subs) && v.subs.length > 0) {\n        return v.subs.find((v1) => v1.type === \"sketch\");\n      }\n    });\n    if (firstList) {\n      boardList.value = getSketchList(firstList.subs);\n    } else {\n      boardList.value = [];\n    }\n  } catch (error: any) {\n    ElMessage.error(error);\n  }\n};\n\nconst init = async () => {\n  const query = route.query;\n  if (query.iv_id) {\n    delete query.teamId;\n    const id = await invite(query.iv_id as string);\n    if (!id) {\n      return;\n    }\n    teamId.value = id;\n    router.replace({\n      path: route.path,\n      query: {\n        projectId: query.projectId,\n        teamId: id\n      }\n    });\n  } else {\n    teamId.value = query.teamId as string;\n  }\n  await smbInfo.init();\n  await getProjectListFn();\n};\n\n// 移动分组和 sketch\nconst handleMove = async (info: { parentId: string; id: string; type: \"sketch\" | \"group\" }) => {\n  if (info.type === \"sketch\") {\n    await moveSketch({\n      id: info.id,\n      groupId: info.parentId\n    });\n  } else {\n    await moveGroup({\n      id: info.id,\n      parentId: info.parentId\n    });\n  }\n\n  await getTreeFn();\n};\n\nconst findNode = (list: any[], id: string) => {\n  for (const item of list) {\n    if (item._id === id) {\n      return item;\n    }\n    if (Array.isArray(item.subs) && item.subs.length > 0) {\n      const found = findNode(item.subs, id);\n      if (found) {\n        return found;\n      }\n    }\n  }\n  return null;\n};\n\nconst nodeClick = async (node: ObjectAny) => {\n  if (node.type === \"sketch\") {\n    if (boardStore.hoverGroupInfo.groupId !== node.groupId) {\n      if (tree.value.list) {\n        let list = findNode(tree.value.list, node.groupId);\n        if (list) {\n          await handleGroupNodeClick(list);\n        }\n      }\n    }\n\n    boardStore.setHoverGroupInfo({\n      id: node._id,\n      groupId: node.groupId,\n      source: \"groupTree\"\n    });\n\n    // handleSketchClick(node);\n  } else {\n    handleGroupNodeClick(node);\n  }\n};\n\nconst getSketchList = (arr: any[]) => {\n  const sketchList: any[] = [];\n  arr.forEach((v: any) => {\n    if (v.type == \"sketch\") {\n      sketchList.push(v);\n    } else if (v.subs && v.subs.length) {\n      sketchList.push(...getSketchList(v.subs));\n    }\n  });\n  return sketchList;\n};\n\nconst handleGroupNodeClick = async (node) => {\n  boardLoading.value = true;\n  boardList.value = getSketchList(node.subs);\n  boardLoading.value = false;\n};\n\n// 移动sketch\nconst moveSketch = async (params: { id: string; groupId: string }) => {\n  const res = await sketchMove(params);\n  if (res.code != ErrorCode.OK) {\n    ElMessage.error(res.msg);\n  }\n};\n// 移动分组\nconst moveGroup = async (params: { id: string; parentId: string }) => {\n  const res = await groupMove(params);\n  if (res.code != ErrorCode.OK) {\n    ElMessage.error(res.msg);\n  }\n};\n\nconst handleBoardGroupClick = (e) => {\n  console.log(\"list-handleBoardGroupClick\", e);\n};\n\n// 选择项目\nconst handleCommand = (command: string) => {\n  if (projectId.value === command) {\n    return;\n  }\n  router.replace({\n    query: {\n      ...route.query,\n      projectId: command\n    }\n  });\n  projectId.value = command;\n\n  getTreeFn();\n};\n// 新建分组 关闭\nconst groupClose = () => {\n  addGroupInfo.visible = false;\n  addGroupInfo.parent = null;\n  addGroupInfo.name = \"\";\n};\n// 分组的全路径\nconst getFullPath = () => {\n  return addGroupInfo.parent?.fullPath.map(({ name }) => name).join(\" / \");\n};\n// 新建分组\nconst groupSubmit = async () => {\n  if (!addGroupInfo.name) {\n    return;\n  }\n  const res = await addGroup({\n    projectId: projectId.value,\n    parentId: addGroupInfo.parent?._id,\n    name: addGroupInfo.name\n  });\n  if (res.code === 0) {\n    getTreeFn();\n    groupClose();\n  }\n};\nonMounted(() => {\n  init();\n  setHotKeys();\n});\n\nonUnmounted(() => {\n  removeHotKeys();\n});\nconst setHotKeys = () => {\n  hotkeys(\"shift+n\", () => openGroupDialog());\n};\nconst removeHotKeys = () => {\n  hotkeys.unbind(\"shift+n\", () => openGroupDialog());\n};\n\nconst openGroupDialog = (parent?: any) => {\n  addGroupInfo.visible = true;\n  addGroupInfo.parent = parent;\n};\n</script>\n<style lang=\"less\" scoped>\n.web-list {\n  width: 100%;\n  position: relative;\n  height: 100vh;\n\n  .web-header-bar {\n    display: flex;\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: 406;\n    height: 48px;\n    background: white;\n    width: 100%;\n    line-height: 48x;\n    font-size: 14px;\n    transition: all 0.2s ease;\n    transform: translateY(0);\n    border-bottom: 1px solid #eeeff1;\n    box-sizing: content-box;\n    .web-header-logo {\n      padding-left: 25px;\n      cursor: pointer;\n      padding-right: 10px;\n      display: flex;\n      align-items: center;\n      img {\n        width: 84px;\n        height: 18px;\n        display: inline-block;\n      }\n    }\n    .bar-project-name-box {\n      // width: 246px;\n      padding-left: 10px;\n      height: 48px;\n      position: relative;\n      display: flex;\n      align-items: center;\n\n      .breadcrumb-button {\n        // width: 24px;\n        // height: 24px;\n        // display: flex;\n        // align-items: center;\n        // color: rgba(0, 0, 0, 0.87);\n        // margin-left: 24px;\n        // border-radius: 4px;\n        cursor: pointer;\n        line-height: 48px;\n      }\n      .breadcrumb-divider {\n        padding: 0 10px;\n        font-size: 14px;\n      }\n      .project-menu-wrapper-box {\n        display: flex;\n        align-items: center;\n        // position: absolute;\n        cursor: pointer;\n        // margin-left: 71px;\n        height: 100%;\n        .el-dropdown {\n          height: 100%;\n        }\n        .project-name {\n          display: inline-block;\n          width: auto;\n          max-width: 160px;\n          cursor: pointer;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          -webkit-user-select: text;\n          -moz-user-select: text;\n          -ms-user-select: text;\n          user-select: text;\n          font-family: PingFang SC;\n          font-style: normal;\n          font-weight: 500;\n          font-size: 14px;\n          line-height: 24px;\n          color: #2f2e3f;\n\n          .project-info-desc {\n            max-width: 160px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            display: block;\n            pointer-events: none;\n          }\n        }\n\n        .el-dropdown-link {\n          display: flex;\n          align-items: center;\n        }\n\n        .project-info-button {\n          margin-left: 5px;\n          display: flex;\n        }\n      }\n    }\n\n    .bar-project-menu {\n      flex: 1;\n      display: flex;\n      flex-direction: row;\n      justify-content: flex-end;\n      align-items: center;\n      padding: 0 20px;\n      box-sizing: border-box;\n\n      .bar-project-menu-item {\n        margin-right: 32px;\n        display: flex;\n\n        i {\n          font-size: 18px;\n          cursor: pointer;\n        }\n      }\n    }\n  }\n\n  .web-list-section-wrap {\n    min-width: 264px;\n    position: absolute;\n    top: 64px;\n    left: 24px;\n    z-index: 405;\n    pointer-events: auto;\n    background: none;\n    padding-top: 8px;\n    box-sizing: border-box;\n    border-radius: 4px;\n\n    .hide-section {\n      display: flex;\n      align-items: center;\n      width: 123px;\n      height: 56px !important;\n      border-radius: 10px !important;\n      padding-left: 16px;\n      cursor: pointer;\n      box-shadow: 0 6px 18px 1px rgba(29, 41, 57, 0.14);\n\n      .total-name {\n        font-size: 14px;\n        color: #303233;\n        font-weight: 500;\n      }\n\n      .total-num {\n        font-size: 13px;\n        color: rgba(47, 46, 63, 0.3);\n        letter-spacing: 0.6px;\n        margin: 0 4px;\n        width: 24px;\n        height: 100%;\n        justify-content: center;\n        align-items: center;\n        display: flex;\n      }\n    }\n\n    .section-total-wrap {\n      padding-top: 10px !important;\n      padding: 0 8px;\n      -webkit-user-select: none;\n      -moz-user-select: none;\n      -ms-user-select: none;\n      user-select: none;\n      // border-bottom: 1px solid #eeeff1;\n    }\n\n    .section-group-list {\n      overflow: overlay;\n      flex: 1;\n\n      &::-webkit-scrollbar {\n        display: none;\n      }\n    }\n\n    .nav-tree-content {\n      background: white;\n      border-radius: 10px;\n      -webkit-user-select: none;\n      position: relative;\n      max-height: calc(100vh - 90px);\n      display: flex;\n      flex-direction: column;\n\n      .section-total {\n        height: 36px;\n        line-height: 36px;\n        font-weight: 400;\n        font-size: 14px;\n        padding: 0 8px;\n        border-radius: 4px;\n        cursor: pointer;\n\n        .total-name {\n          float: left;\n          font-style: normal;\n          font-size: 14px;\n          color: #303233;\n          font-weight: 500;\n        }\n\n        .moreSetButton {\n          float: right;\n          display: flex !important;\n          align-items: center;\n          justify-content: center;\n          height: 100%;\n\n          .item-more {\n            background: none;\n            width: 24px;\n            height: 24px;\n            display: flex !important;\n            align-items: center;\n            justify-content: center;\n            border-radius: 6px;\n            background-size: 10px 10px !important;\n          }\n        }\n\n        .total-num {\n          float: right;\n          margin: 0 4px;\n          padding: 0 0;\n          height: 24px;\n          width: 24px;\n          display: flex;\n          justify-content: center;\n          font-style: normal;\n          font-weight: normal;\n          font-size: 14px;\n          color: #c0c2cc;\n        }\n\n        .total-name__icon {\n          height: 100%;\n          display: flex;\n          align-items: center;\n        }\n      }\n\n      .create-group-content {\n        display: flex;\n        align-items: center;\n        font-size: 13px;\n        font-weight: normal;\n        color: #2f2e3f;\n\n        .icon-jia {\n          margin-right: 10px;\n          margin-left: 6px;\n          font-size: 12px;\n        }\n\n        &__span {\n          font-size: 14px;\n          color: #303233;\n        }\n\n        .key-icon {\n          position: absolute;\n          right: 16px;\n\n          .key-icon-ctrl {\n            margin-right: 3px;\n          }\n\n          .key-icon-code {\n            margin-left: 3px;\n          }\n\n          span {\n            font-size: 12px;\n            color: rgba(31, 33, 38, 0.7);\n            height: 18px;\n            line-height: 18px;\n            background: linear-gradient(360deg, #fafafa 0%, #ffffff 100%);\n            border-radius: 4px 4px 4px 4px;\n            border: 1px solid #d1d5db;\n            padding: 0 3px;\n          }\n        }\n      }\n\n      .create-group-disable {\n        cursor: pointer;\n\n        &:hover {\n          background: #f5f7fa;\n        }\n      }\n\n      .section-line {\n        height: 1px;\n        background: rgb(238, 239, 241);\n        margin: 8px;\n      }\n    }\n\n    .show-exban-button {\n      width: 24px;\n      height: 24px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 6px;\n      position: relative;\n      left: 4px;\n      top: 0;\n\n      .icon-gengduo {\n        color: #575b66;\n      }\n\n      .icon-jiantouzhankai,\n      .icon-jiantoushouqi {\n        font-size: 6px;\n        color: #909299;\n      }\n    }\n\n    .nav-tree-content-show {\n      padding-bottom: 8px;\n      box-shadow: 0 6px 18px 1px rgba(29, 41, 57, 0.14);\n    }\n\n    .show-exban-button:hover {\n      background: #e0e0e2;\n    }\n\n    .show-exban-button:active {\n      background: #d5d5d9;\n    }\n  }\n\n  .web-canvas-area::-webkit-scrollbar {\n    /* WebKit */\n    width: 0;\n    height: 0;\n  }\n\n  .web-canvas-area {\n    width: 100%;\n    height: 100%;\n    position: fixed;\n    overflow: auto;\n    box-sizing: border-box;\n    top: 0;\n    left: 0;\n    scrollbar-width: none;\n    -ms-overflow-style: none;\n\n    .canvas {\n      box-sizing: border-box;\n      // margin-left: -50%;\n      // margin-top: -50%;\n      border: 20px solid #e93030;\n      position: relative;\n    }\n\n    .vue-draggable-resizable-class {\n      box-sizing: border-box;\n\n      &:hover {\n        border: 1px solid #5c54f0;\n        cursor: move;\n      }\n\n      img {\n        width: 100%;\n      }\n    }\n\n    .vue-draggable-resizable-active-class {\n      border: 1px solid #5c54f0;\n      cursor: move;\n    }\n\n    .ref-line {\n      position: absolute;\n      background-color: rgb(255 0 204);\n      z-index: 9999;\n    }\n\n    .v-line {\n      width: 1px;\n    }\n\n    .h-line {\n      height: 1px;\n    }\n\n    .vdr {\n      touch-action: none;\n      position: absolute;\n      box-sizing: border-box;\n      border: 1px dashed #d6d6d6;\n    }\n\n    .handle {\n      box-sizing: border-box;\n      position: absolute;\n      width: 8px;\n      height: 8px;\n      background: #fff;\n      border: 1px solid #333;\n      box-shadow: 0 0 2px #bbb;\n    }\n\n    .handle-tl {\n      top: -5px;\n      left: -5px;\n      cursor: nw-resize;\n    }\n\n    .handle-tm {\n      top: -5px;\n      left: calc(50% - 4px);\n      cursor: n-resize;\n    }\n\n    .handle-tr {\n      top: -5px;\n      right: -5px;\n      cursor: ne-resize;\n    }\n\n    .handle-ml {\n      top: calc(50% - 4px);\n      left: -5px;\n      cursor: w-resize;\n    }\n\n    .handle-mr {\n      top: calc(50% - 4px);\n      right: -5px;\n      cursor: e-resize;\n    }\n\n    .handle-bl {\n      bottom: -5px;\n      left: -5px;\n      cursor: sw-resize;\n    }\n\n    .handle-bm {\n      bottom: -5px;\n      left: calc(50% - 4px);\n      cursor: s-resize;\n    }\n\n    .handle-br {\n      bottom: -5px;\n      right: -5px;\n      cursor: se-resize;\n    }\n  }\n\n  .pd_btm {\n    padding-bottom: 8px;\n  }\n}\n\n.el-popper.is-dark {\n  background: rgba(97, 97, 97, 0.9) !important;\n  color: #ffffff;\n  border-radius: 4px;\n  font-size: 12px;\n  line-height: 22px;\n  display: inline-block;\n  padding: 5px 16px;\n  position: absolute;\n  text-transform: initial;\n  width: auto;\n  opacity: 1;\n  pointer-events: none;\n}\n\n.el-popper__arrow {\n  display: none;\n}\n\n.board {\n  z-index: 300;\n  &-placeholder {\n    z-index: 300;\n    width: 100vw;\n    height: 100vh;\n    padding: 50px 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n}\n.project-dropdown-menu {\n  max-height: 400px;\n  overflow: overlay;\n}\n</style>\n"], "names": ["useBoardStore", "defineStore", "state", "hoverGroupInfo", "id", "groupId", "source", "actions", "setHoverGroupInfo", "group", "this", "hoverColor", "boardStore", "router", "useRouter", "props", "__props", "emit", "__emit", "getCurrentInstance", "proxy", "stage", "ref", "imageDataMap", "Map", "helperLines", "scaleNum", "groupCanDrag", "computed", "permission", "Permission", "PREVIEW", "config<PERSON><PERSON><PERSON>", "value", "draggable", "width", "height", "x", "y", "onMounted", "window", "addEventListener", "spaceDown", "spaceUp", "handleResize", "onUnmounted", "removeEventListener", "innerWidth", "innerHeight", "setHotKeys", "hotkeys", "splitKey", "event", "preventDefault", "handleScaleChange", "e", "keyCode", "eachSketchInfo", "async", "sketchList", "rect", "x1", "x2", "y1", "y2", "rectLeftTopImage", "tasks", "Promise", "all", "map", "sketch", "position_x", "position_y", "imageElement", "canvas", "document", "createElement", "ctx", "getContext", "fillStyle", "fillRect", "img", "Image", "crossOrigin", "src", "resolve", "onload", "drawImage", "toDataURL", "image", "createPlaceholderImage", "borderVisible", "set", "_id", "push", "url", "originalUrl", "modifiedUrl", "createImage", "isRetry", "onerror", "console", "log", "loadImage", "imagePath", "nextTick", "then", "totalWidth", "totalHeight", "scaleX", "body", "clientWidth", "scaleY", "clientHeight", "scale", "Number", "Math", "min", "toFixed", "findItem", "get", "handleMovePositionXYToScreenCenter", "viewportWidth", "viewportHeight", "_x", "_y", "watch", "renderData", "newVal", "Array", "isArray", "length", "setTimeout", "ElMessage", "error", "initKonva", "cloneDeep", "deep", "loading", "immediate", "setImageBorderVisible", "visible", "for<PERSON>ach", "_", "handleChangeSketchPosition", "debounce", "setSketchPosition", "drawHelperLine", "points", "stroke", "strokeWidth", "target", "getClassName", "evt", "oldPos", "newPos", "deltaX", "deltaY", "path", "query", "teamId", "size", "lastId", "from", "keys", "pop", "lastItem", "currentItem", "delete", "style", "cursor", "getStage", "getPointerPosition", "movedX", "attrs", "movedY", "currentGroup", "min<PERSON><PERSON><PERSON><PERSON>", "collisionThreshold", "isLeft", "isTop", "isCollisionX", "isCollisionY", "currentGroupLeft", "currentGroupRight", "currentGroupTop", "currentGroupBottom", "groupLeft", "groupRight", "groupTop", "groupBottom", "groupLeft25", "groupRight25", "groupTop25", "groupBottom25", "isLeft2", "isCollisionLeft", "isCollisionRight", "isCollisionTop", "isCollisionBottom", "direction", "isLeftAligned", "abs", "isRightAligned", "isTopAligned", "isBottomAligned", "isCurrentLeftAndGroupRightAligned", "isCurrentRightAndGroupLeftAligned", "isCurrentTopAndGroupBottomAligned", "isCurrentBottomAndGroupTopAligned", "isTop2", "treeProps", "children", "handleDrop", "draggingNode", "dropNode", "dropType", "label", "params", "data", "parentId", "type", "allowDrop", "handleNodeClick", "clickNode", "subs", "warning", "getElementById", "scrollIntoView", "behavior", "block", "input", "ElMessageBox", "prompt", "confirmButtonText", "cancelButtonText", "inputValue", "name", "message", "updateGroup", "code", "info", "confirm", "res", "deleteGroupById", "ErrorCode", "OK", "msg", "success", "updateSketch", "deleteSketchById", "route", "useRoute", "toggleShow", "projectId", "smbInfo", "smbStore", "userInfoStore", "project", "projectList", "find", "item", "tree", "boardLoading", "boardList", "addGroupInfo", "reactive", "parent", "share", "breadcrumb", "team", "handleShare", "goBack", "replace", "handleContextMenu", "teamList", "handleToggleChange", "getTreeFn", "getTree", "firstList", "list", "v", "v1", "getSketchList", "init", "iv_id", "invite", "getProjectList", "reverse", "handleCommand", "getProjectListFn", "handleMove", "moveSketch", "moveGroup", "findNode", "found", "nodeClick", "node", "handleGroupNodeClick", "arr", "sketchMove", "groupMove", "handleBoardGroupClick", "command", "groupClose", "groupSubmit", "addGroup", "openGroupDialog", "removeHotKeys", "unbind", "fullPath", "join"], "mappings": "6mCAEa,MAAAA,GAAgBC,EAAY,aAAc,CACrDC,MAAO,KACE,CACLC,eAAgB,CACdC,GAAI,KACJC,QAAS,KACTC,OAAQ,QAIdC,QAAS,CACP,iBAAAC,CAAkBC,GAChBC,KAAKP,eAAiBM,CACxB,kHC6FJE,GAAA,yJAlBA,MAAAC,EAAAZ,KACAa,EAAAC,IAEAC,EAAAC,EAQAC,EAAAC,EACAC,IACAC,MACA,MAAAC,EAAAC,EAAA,MACAC,EAAAD,EAAA,IAAAE,KACAC,EAAAH,EAAA,IACAI,EAAAJ,EAAA,KAGAK,EAAAC,GAAA,IACEb,EAAAc,aAAAC,GAAAC,UAGA,IAAAC,EAAAC,MAAAC,YAGFF,EAAAV,EAAA,CAAmCa,MAAA,EAC1BC,OAAA,EACCC,EAAA,EACLC,EAAA,EACAJ,WAAA,IAKLK,GAAA,SAGEC,OAAAC,iBAAA,UAAAC,GACAF,OAAAC,iBAAA,QAAAE,GACAH,OAAAC,iBAAA,SAAAG,EAAA,IAGFC,GAAA,KACEL,OAAAM,oBAAA,UAAAJ,GACAF,OAAAM,oBAAA,QAAAH,GACAH,OAAAM,oBAAA,SAAAF,EAAA,IAGF,MA6BAA,EAAA,KACEZ,EAAAC,MAAAE,MAAAK,OAAAO,WACAf,EAAAC,MAAAG,OAAAI,OAAAQ,WAAA,EAEFC,EAAA,KACEC,EAAA,YAAA,CAAAC,SAAA,MAAAC,IACEA,EAAAC,iBACAC,GAAA,EAAA,IAEFJ,EAAA,aAAAE,IACEA,EAAAC,iBACAC,GAAA,EAAA,GAAuB,EAG3BZ,EAAAa,IACE,KAAAA,EAAAC,UAGAxB,EAAAC,MAAAC,WAAA,EAAA,EAGFS,EAAA,KACEX,EAAAC,MAAAC,WAAA,CAAA,EAsEFuB,EAAAC,MAAAC,IACE,IAAAC,EAAA,CAAWC,GAAA,EACLC,GAAA,EACAC,GAAA,EACAC,GAAA,GAKNC,EAAAN,EAAA,GACA,MAAAO,EAAA,SACAC,QAAAC,IAAAT,EAAAU,KAAAX,MAAAY,IACEA,EAAAC,aACED,EAAAC,WAAAD,EAAAjC,GAEFiC,EAAAE,aACEF,EAAAE,WAAAF,EAAAhC,GAEF,IAAAuB,GAAAA,EAAAC,GAAAA,EAAAC,GAAAA,EAAAC,GAAAA,GAAAJ,EACAC,EAAAS,EAAAC,WAAAV,EAAAA,EAAAS,EAAAC,WACAT,EAAAQ,EAAAC,WAAAT,EAAAA,EAAAQ,EAAAC,WACAR,EAAAO,EAAAE,WAAAT,EAAAA,EAAAO,EAAAE,WACAR,EAAAM,EAAAE,WAAAR,EAAAA,EAAAM,EAAAE,WACAZ,EAAA,CAAOC,KACLC,KACAC,KACAC,MAKFM,EAAAC,WAAAN,EAAAM,YAAAD,EAAAE,WAAAP,EAAAO,aACEP,EAAAK,GAGFA,EAAAG,kBA5JJ,CAAAH,IACE,MAAAI,EAAAC,SAAAC,cAAA,UACAF,EAAAvC,MAAAmC,EAAAnC,MACAuC,EAAAtC,OAAAkC,EAAAlC,OACA,MAAAyC,EAAAH,EAAAI,WAAA,MAEAD,EAAAE,UAAA,UACAF,EAAAG,SAAA,EAAA,EAAAV,EAAAnC,MAAAmC,EAAAlC,QAGA,MAAA6C,EAAA,IAAAC,MAIA,OAHAD,EAAAE,YAAA,YACAF,EAAAG,IAAA,4DAEA,IAAAjB,SAAAkB,IACEJ,EAAAK,OAAA,KAEE,MAAAjD,GAAAiC,EAAAnC,MAAAmC,EAAAnC,MAAA,GAAA,EACAG,GAAAgC,EAAAlC,OAAAkC,EAAAnC,MAAA,GAAA,EAEA0C,EAAAU,UAAAN,EAAA5C,EAAAC,EAAAgC,EAAAnC,MAAA,EAAAmC,EAAAnC,MAAA,GACA,MAAAiD,EAAAV,EAAAc,UAAA,aACAC,EAAA,IAAAP,MACAO,EAAAL,IAAAA,EAEAC,EAAAI,EAAA,CAAa,GACf,EAkIAC,CAAApB,GACAA,EAAAqB,eAAA,EAEApE,EAAAU,MAAA2D,IAAAtB,EAAAuB,IAAAvB,GACAJ,EAAA4B,KApFJ,EAAAC,EAAAzB,IACE,IAAAH,SAAAkB,IACE,MAAAW,EAAAD,EACA,IAAAE,EAAAF,EAAA,4BAEA,MAAAG,EAAA,CAAAd,EAAAe,GAAA,KACE,MAAAlB,EAAA,IAAAC,MACAD,EAAAG,IAAAA,EAEAH,EAAAK,OAAA,KACEhB,EAAAG,aAAAQ,OAGFA,EAAAmB,QAAA,KAEED,GAAAF,IAAAD,GAKEK,QAAAC,IAAA,eAAAP,QAJAG,EAAAF,GAAA,EAKQ,CACV,EAGJE,EAAAD,EAAA,IA2DAM,CAAAjC,EAAAkC,UAAAlC,GAAA,KAGFmC,IAAAC,MAAAhD,gBACES,QAAAC,IAAAF,GACA3C,EAAAU,MAAA,IAAAT,IAAAD,EAAAU,MAAA,IASF,MAAA0E,EAAA/C,EAAAE,GAAAF,EAAAC,GACA+C,EAAAhD,EAAAI,GAAAJ,EAAAG,GAIA8C,EAHAlC,SAAAmC,KAAAC,YAGAJ,EACAK,EAHArC,SAAAmC,KAAAG,aAGAL,EAEA,IAAAM,EAAAC,OAAAC,KAAAC,IAAAR,EAAAG,GAAAM,QAAA,IAIAJ,EAAA,KACEA,EAAA,IAGFA,EAAA,IACEA,EAAA,IAGFxF,EAAAO,MAAAkF,QAAA,IAAAD,GAAAI,QAAA,IACAtF,EAAAC,MAAAiF,MAAA,CAA0B7E,EAAA6E,EACrB5E,EAAA4E,GAGLb,QAAAC,IAAA,QAAArC,GAEA,MAAAsD,EAAAhG,EAAAU,MAAAuF,IAAA5G,EAAAT,eAAAC,IACA6D,EAAAsD,GAAAtD,EAEArD,EAAAJ,kBAAA,CAA6BJ,GAAA6D,EAAA4B,IACNxF,QAAA4D,EAAA5D,QACKC,OAAA,UAI5BmH,EAAAxD,GAEAoC,QAAAC,IAAA,WAAAtE,EAAAC,OACAhB,EAAA,kBAAA,EAAA,EAIFwG,EAAAnD,IACE,MAAAjC,EAAAiC,EAAAC,YAAAD,EAAAjC,EACAC,EAAAgC,EAAAE,YAAAF,EAAAhC,EAEA+D,QAAAC,IAAA,qCAAAjE,EAAAC,GAEA,MAAAoF,EAAA/C,SAAAmC,KAAAC,YACAY,EAAAhD,SAAAmC,KAAAG,aACAC,EAAAxF,EAAAO,MAAA,IAOA,IAAA2F,EAFAF,EAAA,EAJArF,EAAA6E,EAOAW,EAFAF,EAAA,EAJArF,EAAA4E,EAQAlF,EAAAC,MAAAI,EAAAuF,EACA5F,EAAAC,MAAAK,EAAAuF,EAEAxB,QAAAC,IAAA,cAAAtE,EAAAC,MAAA,EAGF6F,GAAA,IAAA/G,EAAAgH,aACcC,IAEVC,MAAAC,QAAAF,IA5IJtE,OAAAC,IACE0C,QAAAC,IAAA,YAAA3C,GACA3B,EAAAC,MAAAE,MAAAwC,SAAAmC,KAAAC,YACA/E,EAAAC,MAAAG,OAAAuC,SAAAmC,KAAAG,aACA1F,EAAAU,UAAAT,IAEAyG,MAAAC,QAAAvE,IAAA,GAAAA,EAAAwE,OAQA1E,EAAAE,IAPE1C,EAAA,kBAAA,GACAmH,YAAA,KACEC,EAAAC,MAAA,WAAA,GAA0B,KAK9B,EA+HIC,CAAAC,GAAAA,UAAAzH,EAAAgH,YAAqC,GAEzC,CACAU,MAAA,IAKFX,GAAA,IAAA/G,EAAA2H,UACcV,IAEV3B,QAAAC,IAAA,uBAAA0B,EAAA,GACF,CACAW,WAAA,IAKFb,GAAA,IAAAlH,EAAAT,iBACmB6H,IAEf,GAAAA,EAAA,CACE,MAAAvC,EAAAlE,EAAAU,MAAAuF,IAAAQ,EAAA5H,IAEA,IAAAqF,EAEE,YADAY,QAAAC,IAAA,UAIFsC,EAAAnD,EAAAI,KAAA,GACA,cAAAmC,EAAA1H,QACEmH,EAAAhC,EACF,IAEJ,CAAAgD,MAAA,IAIF,MAAAnF,EAAArB,IAEE,GAAAA,EAAA,CACE,GAAAP,EAAAO,OAAA,IACE,OAEFP,EAAAO,OALF,CAKoB,KAAA,CAElB,GAAAP,EAAAO,OAAA,EACE,OAEFP,EAAAO,OAVF,CAUoB,CAEpBD,EAAAC,MAAAiF,MAAA,CAA0B7E,EAAAX,EAAAO,MAAA,IACJK,EAAAZ,EAAAO,MAAA,IACA,EAIxB2G,EAAA,CAAAxI,EAAAyI,KACE,MAAApD,EAAAlE,EAAAU,MAAAuF,IAAApH,GACAqF,IACElE,EAAAU,MAAA6G,SAAAC,IACEA,EAAApD,eAAA,CAAA,IAEFF,EAAAE,cAAAkD,EAAsB,EAkC1BG,EAAAC,GAAAA,UAAA,EAAA7I,KAAAiC,IAAAC,QACE+D,QAAAC,IAAA,6BAAAjE,EAAAC,GACA4G,EAAA,CAAkB9I,KAChBiC,IACAC,KACA,GACD,KAgCH6G,EAAA,CAAAtF,EAAAE,EAAAD,EAAAE,KAEEvC,EAAAQ,MAAA6D,KAAA,CAAuBsD,OAAA,CAAAvF,EAAAE,EAAAD,EAAAE,GACEqF,OAAA,MACfC,YAAA,GACK,sRA3UjB,IAAA/F,EACE,WADFA,KACEgG,OAAAC,iBAGAxH,EAAAC,MAAAI,EAAAkB,EAAAgG,OAAAlH,IACAL,EAAAC,MAAAK,EAAAiB,EAAAgG,OAAAjH,8BAIF,CAAAiB,IACEA,EAAAkG,IAAApG,iBACA,IAAAqG,EAAa1H,EAAAC,MAAAI,EAAbqH,EACuB1H,EAAAC,MAAAK,EAGvBqH,EAAA,CAAatH,EAAAqH,EAAAnG,EAAAkG,IAAAG,OACStH,EAAAoH,EAAAnG,EAAAkG,IAAAI,QAGtB7H,EAAAC,MAAAI,EAAAsH,EAAAtH,EACAL,EAAAC,MAAAK,EAAAqH,EAAArH,CAAA,yNA6PFlC,aACES,EAAAiF,KAAA,CAAYgE,KAAA,uBACJC,MAAA,CACC3J,KACL4J,OAAAjJ,EAAAiJ,UAJN,IAAA5J,sBAfAA,QAAAC,iBACEkB,EAAAU,MAAAuF,IAAApH,KAKAwI,EAAAxI,GAAA,GAEAQ,EAAAJ,kBAAA,CAA6BJ,KAC3BC,UACAC,OAAA,YAVJ,IAAAF,EAAAC,mBAuCA,GAAAD,KAAAmD,QAGE,GAAAhC,EAAAU,MAAAgI,KAAA,EAAA,CACE,MAAAC,EAAAjC,MAAAkC,KAAA5I,EAAAU,MAAAmI,QAAAC,MACA,GAAAH,IAAA9J,EAAA,CACE,MAAAkK,EAAA/I,EAAAU,MAAAuF,IAAA0C,GACAK,EAAAhJ,EAAAU,MAAAuF,IAAApH,GACAkK,GAAAC,IACEhJ,EAAAU,MAAAuI,OAAApK,GACAmB,EAAAU,MAAA2D,IAAAxF,EAAAmK,GACF,CACF,CAOFhH,EAAAkG,IAAAF,OAAAkB,MAAAC,OAAA,UACAjJ,EAAAQ,MAAA,qCAGF,GAAA7B,KAAAmD,QAEEA,EAAAkG,IAAAF,OAAAkB,MAAAC,OAAA,UACAjJ,EAAAQ,MAAA,mCAaF,GAAA7B,KAAAmD,QAGE,IAFAA,EAAAgG,OAAAoB,WAAAC,qBAGE,OAGFnJ,EAAAQ,MAAA,GAEA,MAAA4I,EAAAtH,EAAAgG,OAAAuB,MAAAzI,EACA0I,EAAAxH,EAAAgG,OAAAuB,MAAAxI,EAEA0I,EAAAzJ,EAAAU,MAAAuF,IAAApH,GACA,IAAA4K,EAAmB,OAEnBA,EAAAzG,WAAAsG,EACAG,EAAAxG,WAAAuG,EAGA/B,EAAA,CAA2B5I,KACzBiC,EAAAwI,EACGvI,EAAAyI,IAWL,MAAAE,EAAA,GACAC,EAAA,GAIA3J,EAAAU,MAAA6G,SAAArI,IAIE,MAAA0K,EAAAN,EAAApK,EAAA8D,WACA6G,EAAAL,EAAAtK,EAAA+D,WAEA6G,EAAAF,EAAAN,EAAAG,EAAA7I,MAAA+I,EAAAzK,EAAA8D,YAAAsG,EAAApK,EAAA8D,WAAA9D,EAAA0B,MAAA0I,EAAAK,EAAAzK,EAAA8D,WAAA9D,EAAA0B,OAAA0I,EAAApK,EAAA8D,WACA+G,EAAAF,EAAAL,EAAAC,EAAA5I,OAAA8I,EAAAzK,EAAA+D,YAAAuG,EAAAtK,EAAA+D,WAAA/D,EAAA2B,OAAA2I,EAAAG,EAAAzK,EAAA+D,WAAA/D,EAAA2B,QAAA2I,EAAAtK,EAAA+D,WAMA,GAAA/D,EAAAoF,MAAAzF,EAAA,CACE,GAAAiL,GAAAC,EAAA,CAKE,MAAAC,EAAAV,EACAW,EAAAX,EAAAG,EAAA7I,MACAsJ,EAAAV,EACAW,EAAAX,EAAAC,EAAA5I,OAEAuJ,EAAAlL,EAAA8D,WACAqH,EAAAnL,EAAA8D,WAAA9D,EAAA0B,MACA0J,EAAApL,EAAA+D,WACAsH,EAAArL,EAAA+D,WAAA/D,EAAA2B,OAGA2J,EAAAJ,EAAA,IAAAlL,EAAA0B,MACA6J,EAAAJ,EAAA,IAAAnL,EAAA0B,MACA8J,EAAAJ,EAAA,IAAApL,EAAA2B,OACA8J,EAAAJ,EAAA,IAAArL,EAAA2B,OAEA+J,EAAAtB,EAAApK,EAAA8D,WACA8B,QAAAC,IAAA,SAAA6F,EAAA,KAAA,MAEA9F,QAAAC,IAAA,SAAAuE,GACAxE,QAAAC,IAAA,qDAAAiF,EAAAL,EAAAU,GAGA,MAAAQ,EAAAZ,EAAAN,EAAAS,GAAAH,EAAAN,EAAAa,EACAM,EAAAd,EAAAL,EAAAU,GAAAL,EAAAL,EAAAc,EACAM,EAAAZ,EAAAR,EAAAW,GAAAH,EAAAR,EAAAe,EACAM,EAAAd,EAAAP,EAAAY,GAAAL,EAAAP,EAAAgB,EAEA7F,QAAAC,IAAA,iBAAAgG,GACAjG,QAAAC,IAAA,oBAAAiG,GACAlG,QAAAC,IAAA,kBAAA8F,GACA/F,QAAAC,IAAA,mBAAA+F,GAEA,IAAAG,EAAA,GAMA,OALAH,EAAsBG,EAAA,QAAYJ,EACRI,EAAA,OAAYD,EACVC,EAAA,SAAYF,IACfE,EAAA,OAEzBA,GAAmB,IAAA,QAEfxB,EAAAzG,WAAA9D,EAAA8D,WAAA9D,EAAA0B,MACAoB,EAAAgG,OAAAuB,MAAAzI,EAAA2I,EAAAzG,WAEA,MAAA,IAAA,OAEAyG,EAAAzG,WAAA9D,EAAA8D,WAAAyG,EAAA7I,MACAoB,EAAAgG,OAAAuB,MAAAzI,EAAA2I,EAAAzG,WAEA,MAAA,IAAA,SAEAyG,EAAAxG,WAAA/D,EAAA+D,WAAA/D,EAAA2B,OACAmB,EAAAgG,OAAAuB,MAAAxI,EAAA0I,EAAAxG,WAEA,MAAA,IAAA,MAEAwG,EAAAxG,WAAA/D,EAAA+D,WAAAwG,EAAA5I,OACAmB,EAAAgG,OAAAuB,MAAAxI,EAAA0I,EAAAxG,WAKJ,CAIF,MAAAiI,EAAArF,KAAAsF,IAAA7B,EAAApK,EAAA8D,YAAA0G,EACA0B,EAAAvF,KAAAsF,IAAA7B,EAAAG,EAAA7I,OAAA1B,EAAA8D,WAAA9D,EAAA0B,QAAA8I,EACA2B,EAAAxF,KAAAsF,IAAA3B,EAAAtK,EAAA+D,aAAAyG,EACA4B,EAAAzF,KAAAsF,IAAA3B,EAAAC,EAAA5I,QAAA3B,EAAA+D,WAAA/D,EAAA2B,SAAA6I,EAEA6B,EAAA1F,KAAAsF,IAAA7B,GAAApK,EAAA8D,WAAA9D,EAAA0B,QAAA8I,EACA8B,EAAA3F,KAAAsF,IAAA7B,EAAAG,EAAA7I,MAAA1B,EAAA8D,YAAA0G,EACA+B,EAAA5F,KAAAsF,IAAA3B,GAAAtK,EAAA+D,WAAA/D,EAAA2B,SAAA6I,EACAgC,EAAA7F,KAAAsF,IAAA3B,EAAAC,EAAA5I,OAAA3B,EAAA+D,YAAAyG,EAGA,GAAA6B,EAAA,CACE,MAAA/I,EAAAqH,EAAAL,EAAAtK,EAAA+D,WACAR,EAAAoH,EAAA3K,EAAA+D,WAAA/D,EAAA2B,OAAA2I,EACA5B,EAAA0B,EAAA9G,EAAA8G,EAAA7G,EAAqC,CAGvC,GAAA+I,EAAA,CACE,MAAAhJ,EAAAqH,EAAAL,EAAAtK,EAAA+D,WACAR,EAAAoH,EAAA3K,EAAA+D,WAAA/D,EAAA2B,OAAA2I,EACA5B,EAAA0B,EAAAG,EAAA7I,MAAA4B,EAAA8G,EAAAG,EAAA7I,MAAA6B,EAA+E,CAGjF,GAAAgJ,EAAA,CACE,MAAAnJ,EAAAsH,EAAAN,EAAApK,EAAA8D,WACAT,EAAAqH,EAAA1K,EAAA8D,WAAA9D,EAAA0B,MAAA0I,EACA1B,EAAAtF,EAAAkH,EAAAjH,EAAAiH,EAAqC,CAGvC,GAAAkC,EAAA,CACE,MAAApJ,EAAAsH,EAAAN,EAAApK,EAAA8D,WACAT,EAAAqH,EAAA1K,EAAA8D,WAAA9D,EAAA0B,MAAA0I,EACA1B,EAAAtF,EAAAkH,EAAAC,EAAA5I,OAAA0B,EAAAiH,EAAAC,EAAA5I,OAAiF,CAGnF,GAAAwK,EAAA,CAKE,MAAA/I,EAAAsH,EAAAN,EAAApK,EAAA8D,WACAT,EAAAqH,EAAA1K,EAAA8D,WAAA9D,EAAA0B,MAAA0I,EAEA1B,EAAAtF,EAAAkH,EAAAjH,EAAAiH,EAAqC,CAGvC,GAAA8B,EAAA,CAEE,MAAAhJ,EAAAsH,EAAAN,EAAApK,EAAA8D,WACAT,EAAAqH,EAAA1K,EAAA8D,WAAA9D,EAAA0B,MAAA0I,EACA1B,EAAAtF,EAAAkH,EAAAC,EAAA5I,OAAA0B,EAAAiH,EAAAC,EAAA5I,OAAiF,CAGnF,GAAAqK,EAAA,CAEE,MAAAS,EAAAnC,EAAAtK,EAAA+D,WACAT,EAAAmJ,EAAAnC,EAAAtK,EAAA+D,WACAR,EAAAkJ,EAAAzM,EAAA+D,WAAA/D,EAAA2B,OAAA2I,EACA5B,EAAA0B,EAAA9G,EAAA8G,EAAA7G,EAAqC,CAGvC,GAAA2I,EAAA,CAEE,MAAA5I,EAAAqH,EAAAL,EAAAtK,EAAA+D,WACAR,EAAAoH,EAAA3K,EAAA+D,WAAA/D,EAAA2B,OAAA2I,EACA5B,EAAA0B,EAAAG,EAAA7I,MAAA4B,EAAA8G,EAAAG,EAAA7I,MAAA6B,EAA+E,CACjF,IACF,gCAUJ,GAAA5D,KAAAmD,QACE8C,QAAAC,IAAAlG,EAAAmD,GACA9B,EAAAQ,MAAA,oCAGF,GAAA7B,KAAAmD,QACE8C,QAAAC,IAAA/C,EAAAnD,EAAA,+oDCtpBF,MAAAQ,EAAAZ,KAQAiB,EAAAC,EACAiM,EAAA,CAAkBC,SAAA,QAIlBC,EAAA,CAAAC,EAAAC,EAAAC,KACEnH,QAAAC,IAAA,aAAAiH,EAAAE,MAAAD,GACA,MAAAE,EAAA,CAAetN,GAAAkN,EAAAK,KAAA9H,IACS+H,SAAAL,EAAAI,KAAA9H,IACEgI,KAAA,WAAAP,EAAAK,KAAAE,KAAA,SAAA,SAG1B5M,EAAA,OAAAyM,EAAA,EAEFI,EAAA,CAAAR,EAAAC,IACE,WAAAA,EAAAI,KAAAE,MAGAP,EAAAK,KAAAtN,SAAAkN,EAAAI,KAAA9H,IAKFkI,EAAAC,IACE3H,QAAAC,IAAA,aAAA0H,GAEAA,EAAAH,MAAA,WAAAG,EAAAH,MAAA5F,MAAAC,QAAA8F,EAAAC,OAAA,IAAAD,EAAAC,KAAA9F,OAIAlH,EAAA,YAAA+M,GAHE3F,EAAA6F,QAAA,WAGF,SA2GFpG,GAAA,IAAAlH,EAAAT,iBACmB6H,IAEf,GAAAA,EAAA,CAEE,MAAA1G,EAAAqD,SAAAwJ,eAAA,aAAAnG,EAAA5H,MACAkB,GACEA,EAAA8M,eAAA,CAAmBC,SAAA,SACPC,MAAA,UAGd,IAEJ,CAAA7F,MAAA,4qBA9FF/E,OAAAiK,IACE,MAAAY,QAAAC,GAAAC,OAAA,UAAA,MAAA,CAA0DC,kBAAA,KACrCC,iBAAA,KACDC,WAAAjB,EAAAkB,OAGpB,IAAAN,EAAAtM,MAKE,YAJAoG,EAAA,CAAUwF,KAAA,QACFiB,QAAA,WASV,WAJAC,EAAA,CAA8BF,KAAAN,EAAAtM,MAChB7B,GAAAuN,EAAA9H,OAGdmJ,MACE3G,EAAA,CAAUwF,KAAA,UACFiB,QAAA,UAGR7N,EAAA,YAEAoH,EAAA,CAAUwF,KAAA,QACFiB,QAAA,SAEP,yFAlDLpL,OAAAuL,IACE,GAAAA,EAAAhB,KAAA9F,OACE,OAAAE,EAAA6F,QAAA,0BAEFM,GAAAU,QAAA,QAAAD,EAAAJ,YAAA,KAAA,CAA2DH,kBAAA,KACtCC,iBAAA,KACDd,KAAA,YAGpB,IACE,MAAAsB,QAAAC,EAAA,CAAkChP,GAAA6O,EAAApJ,MAGlC,GAAAsJ,EAAAH,OAAAK,GAAAC,GACE,MAAAH,EAAAI,IAEFlH,EAAAmH,QAAA,QACAvO,EAAA,UAAc,OAAAqH,GAEdD,EAAAC,MAAAA,EAAqB,CAEvBjC,QAAAC,IAAA2I,EAAAhB,KAAA,yqBAoDFvK,OAAAiK,IACE,MAAAY,QAAAC,GAAAC,OAAA,WAAA,MAAA,CAA2DC,kBAAA,KACtCC,iBAAA,KACDC,WAAAjB,EAAAkB,OAGpB,IAAAN,EAAAtM,MAKE,YAJAoG,EAAA,CAAUwF,KAAA,QACFiB,QAAA,WASV,WAJAW,EAAA,CAA+BZ,KAAAN,EAAAtM,MACjB7B,GAAAuN,EAAA9H,OAGdmJ,MACE3G,EAAA,CAAUwF,KAAA,UACFiB,QAAA,UAGR7N,EAAA,YAEAoH,EAAA,CAAUwF,KAAA,QACFiB,QAAA,SAEP,yFA9CLpL,OAAAuL,UACET,GAAAU,QAAA,QAAAD,EAAAJ,YAAA,KAAA,CAA2DH,kBAAA,KACtCC,iBAAA,KACDd,KAAA,YAGpB,IACE,MAAAsB,QAAAO,EAAA,CAAmCtP,GAAA6O,EAAApJ,MAGnC,GAAAsJ,EAAAH,OAAAK,GAAAC,GACE,MAAAH,EAAAI,IAEFlH,EAAAmH,QAAA,QACAvO,EAAA,UAAc,OAAAqH,GAEdD,EAAAC,MAAAA,EAAqB,m0CCxBzB,MAAAzH,EAAAC,IACA6O,EAAAC,IACAC,EAAAvO,GAAA,GACAwO,EAAAxO,EAAA,IACA0I,EAAA1I,EAAA,IACAyO,EAAAC,IACAC,IAEA,MAAArP,EAAAZ,KAEAkQ,EAAAtO,GAAA,IACEkO,EAAA7N,MACEkO,EAAAlO,MAAAmO,MAAAC,GACEA,EAAAxK,MAAAiK,EAAA7N,QAGJ,OAEFkO,EAAA7O,EAAA,IACAgP,EAAAhP,EAAA,CAAA,GAIAiP,EAAAjP,GAAA,GACAkP,EAAAlP,EAAA,IACAmP,EAAAC,EAAA,CAA+E7H,SAAA,EACpEgG,KAAA,GACH8B,OAAA,OAGRC,EAAA,KACE,MAAAC,EAAA,CAAAC,EAAA7O,MAAA4M,MACAqB,EAAAjO,OACE4O,EAAA/K,KAAAoK,EAAAjO,MAAA4M,MAEFkC,GAAAD,EAAA7O,MAAAH,GAAAC,QAAA,mCAAA+N,EAAA7N,SAAA4O,EAAA,EAEFG,EAAA,KACEnQ,EAAAoQ,QAAA,CAAenH,KAAA,sBACPC,MAAA,CACCC,OAAAA,EAAA/H,QAEP,EAGJiP,EAAA,OAIAJ,EAAAlP,GAAA,IACEmO,EAAAoB,SAAAf,MAAAC,GAAAA,EAAAxK,KAAAmE,EAAA/H,UAGFJ,EAAAD,GAAA,IACEkP,EAAA7O,MAGA6O,EAAA7O,MAAAJ,WAFE,OAIJuP,EAAA,KACEvB,EAAA5N,OAAA4N,EAAA5N,KAAA,EA+BFoP,EAAA3N,UACE,IACE6M,EAAAtO,OAAA,EACA,MAAAkN,QAAAmC,GAAA,CAA0BxB,UAAAA,EAAA7N,QAG1B,GAAAkN,EAAAH,OAAAK,GAAAC,GACE,MAAAH,EAAAI,IAGFe,EAAArO,MAAAkN,EAAAxB,KAIA,MAAA4D,EAAApC,EAAAxB,KAAA6D,KAAApB,MAAAqB,GACE,WAAAA,EAAA5D,OAGA5F,MAAAC,QAAAuJ,EAAAxD,OAAAwD,EAAAxD,KAAA9F,OAAA,EACEsJ,EAAAxD,KAAAmC,MAAAsB,GAAA,WAAAA,EAAA7D,YADF,KAKA2C,EAAAvO,MADFsP,EACEI,GAAAJ,EAAAtD,MAEA,EACF,OAAA3F,GAEAD,EAAAC,MAAAA,EAAqB,GAIzBsJ,GAAAlO,UACE,MAAAqG,EAAA4F,EAAA5F,MACA,GAAAA,EAAA8H,MAAA,QACE9H,EAAAC,OACA,MAAA5J,QAAA0R,GAAA/H,EAAA8H,OACA,IAAAzR,EACE,OAEF4J,EAAA/H,MAAA7B,EACAS,EAAAoQ,QAAA,CAAenH,KAAA6F,EAAA7F,KACDC,MAAA,CACL+F,UAAA/F,EAAA+F,UACY9F,OAAA5J,IAGpB,MAED4J,EAAA/H,MAAA8H,EAAAC,aAEF+F,EAAA6B,YA5EFlO,WACE,IACE,MAAAyL,QAAA4C,GAAA,CAAiC/H,OAAA2F,EAAA5F,MAAAC,SAGjC,GAAAmF,EAAAH,OAAAK,GAAAC,GACE,MAAAH,EAAAI,IAGF,GADAY,EAAAlO,MAAAkN,EAAAxB,KAAAqE,UACA7C,EAAAxB,KAAAxF,OAAA,CACE,IAAA8G,EACAU,EAAA5F,MAAA+F,YACEb,EAAAE,EAAAxB,KAAAyC,MAAAC,IACE,GAAAA,EAAAxK,MAAA8J,EAAA5F,MAAA+F,UACE,OAAA,CAAO,KAIbmC,GAAAhD,EAAAA,EAAApJ,IAAAsJ,EAAAxB,KAAA,GAAA9H,IAA+C,CACjD,OAAAyC,GAEAD,EAAAC,MAAAA,EAAqB,GAwDvB4J,EAAA,EAIFC,GAAAzO,MAAAuL,IACE,WAAAA,EAAApB,WACEuE,GAAA,CAAiBhS,GAAA6O,EAAA7O,GACNC,QAAA4O,EAAArB,iBAIXyE,GAAA,CAAgBjS,GAAA6O,EAAA7O,GACLwN,SAAAqB,EAAArB,iBAKbyD,GAAA,EAGFiB,GAAA,CAAAd,EAAApR,KACE,IAAA,MAAAiQ,KAAAmB,EAAA,CACE,GAAAnB,EAAAxK,MAAAzF,EACE,OAAAiQ,EAEF,GAAApI,MAAAC,QAAAmI,EAAApC,OAAAoC,EAAApC,KAAA9F,OAAA,EAAA,CACE,MAAAoK,EAAAD,GAAAjC,EAAApC,KAAA7N,GACA,GAAAmS,EACE,OAAAA,CACF,CACF,CAEF,OAAA,IAAA,EAGFC,GAAA9O,MAAA+O,IACE,GAAA,WAAAA,EAAA5E,KAAA,CACE,GAAAjN,EAAAT,eAAAE,UAAAoS,EAAApS,SACEiQ,EAAArO,MAAAuP,KAAA,CACE,IAAAA,EAAAc,GAAAhC,EAAArO,MAAAuP,KAAAiB,EAAApS,SACAmR,SACEkB,GAAAlB,EACF,CAIJ5Q,EAAAJ,kBAAA,CAA6BJ,GAAAqS,EAAA5M,IAClBxF,QAAAoS,EAAApS,QACKC,OAAA,aAEf,MAIDoS,GAAAD,EAAyB,EAI7Bd,GAAAgB,IACE,MAAAhP,EAAA,GAQA,OAPAgP,EAAA7J,SAAA2I,IACE,UAAAA,EAAA5D,KACElK,EAAAmC,KAAA2L,GAAiBA,EAAAxD,MAAAwD,EAAAxD,KAAA9F,QAEjBxE,EAAAmC,QAAA6L,GAAAF,EAAAxD,MAAwC,IAG5CtK,CAAA,EAGF+O,GAAAhP,MAAA+O,IACElC,EAAAtO,OAAA,EACAuO,EAAAvO,MAAA0P,GAAAc,EAAAxE,MACAsC,EAAAtO,OAAA,CAAA,EAIFmQ,GAAA1O,MAAAgK,IACE,MAAAyB,QAAAyD,GAAAlF,GACAyB,EAAAH,MAAAK,GAAAC,IACEjH,EAAAC,MAAA6G,EAAAI,IAAuB,EAI3B8C,GAAA3O,MAAAgK,IACE,MAAAyB,QAAA0D,GAAAnF,GACAyB,EAAAH,MAAAK,GAAAC,IACEjH,EAAAC,MAAA6G,EAAAI,IAAuB,EAI3BuD,GAAAvP,IACE8C,QAAAC,IAAA,6BAAA/C,EAAA,EAIF0O,GAAAc,IACEjD,EAAA7N,QAAA8Q,IAGAlS,EAAAoQ,QAAA,CAAelH,MAAA,IACN4F,EAAA5F,MACI+F,UAAAiD,KAIbjD,EAAA7N,MAAA8Q,QAKFC,GAAA,KACEvC,EAAA5H,SAAA,EACA4H,EAAAE,OAAA,KACAF,EAAA5B,KAAA,EAAA,EAOFoE,GAAAvP,UACE,IAAA+M,EAAA5B,KACE,OAOF,WALAqE,GAAA,CAA2BpD,UAAAA,EAAA7N,MACJ2L,SAAA6C,EAAAE,QAAA9K,IACUgJ,KAAA4B,EAAA5B,QAGjCG,gBAEa,EAGfzM,GAAA,kBAKAM,GAAA,aAGA,MAAAI,GAAA,KACEC,EAAA,WAAA,IAAAiQ,MAAA,EAEFC,GAAA,KACElQ,EAAAmQ,OAAA,WAAA,IAAAF,MAAA,EAGFA,GAAAxC,IACEF,EAAA5H,SAAA,EACA4H,EAAAE,OAAAA,CAAA,glEAlCAF,EAAAE,QAAA2C,SAAAjP,KAAA,EAAAwK,UAAAA,IAAA0E,KAAA"}