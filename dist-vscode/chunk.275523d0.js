import"./chunk.25a51fc3.js";import"./chunk.c5fb43ac.js";import{E as e}from"./chunk.0b77532e.js";import{d as t,t as a,r as n,w as s,o as i,c as o,b as l,F as c,a as d,n as p,u as r,e as h,A as m,B as g,h as u}from"./index.7c7944d0.js";const _={class:"sp-nav__content"},y=["onClick"],v=["src"],w=["src"],f={class:"sp-nav-content-item__span"},k=l("div",{class:"operation-span__container"},[l("img",{loading:"lazy",class:"operation__btn",src:"https://static.soyoung.com/sy-pre/1spmepi29mrwu-1698304200710.png",alt:""})],-1),b={class:"operation-body"},C=["onClick"],x=[l("span",null,"重命名",-1)],j=["onClick"],I=[l("span",null,"删除",-1)],R=t({__name:"spNav",props:{data:{type:Array,default:()=>[]},defaultId:{type:String,default:""},identify:{type:Number,default:0}},emits:["tabsChange","handleRename","handleDelete"],setup(t,{emit:R}){let S=R;const z=a();let A=t;const D=n("");return s((()=>A.defaultId),(e=>{D.value=e}),{immediate:!0}),s((()=>A.data),(e=>{0!==e.length&&1!==e.length||S("tabsChange",{_id:"",name:""})}),{immediate:!0,deep:!0}),(a,n)=>{const s=e;return i(),o("div",{class:p({sp__nav:!0,"sp-nav-theme__active":r(z).themeShow})},[l("div",_,[(i(!0),o(c,null,d(t.data,(e=>(i(),o("div",{key:e._id,class:p({"sp-nav-content__item":!0,"sp-nav-content-item__active":e._id===D.value}),onClick:t=>(e=>{D.value=e._id,S("tabsChange",e)})(e)},[""===e.icon||void 0===e.icon?(i(),o("img",{key:0,loading:"lazy",src:r(z).themeShow||e._id===D.value?"https://static.soyoung.com/sy-pre/23bl3x53rwck5-1697609400738.png":"https://static.soyoung.com/sy-pre/ty5osyh2u1oh-1697609400738.png",alt:""},null,8,v)):(i(),o("img",{key:1,loading:"lazy",src:e.icon,alt:""},null,8,w)),l("span",f,h(e.name),1),"收藏夹"!==e.name&&1===t.identify&&e._id===D.value?(i(),m(s,{key:2,placement:"bottom",width:103,"popper-class":"popover-operation__style",trigger:"hover",effect:r(z).themeShow?"light":"dark"},{reference:g((()=>[k])),default:g((()=>[l("div",b,[l("div",{class:"operation-body__item",onClick:t=>(e=>{S("handleRename",e)})(e)},x,8,C),l("div",{class:"operation-body__item",onClick:t=>(e=>{S("handleDelete",e)})(e)},I,8,j)])])),_:2},1032,["effect"])):u("",!0)],10,y)))),128))])],2)}}}),S=e=>{const t=document.createElement("canvas"),a=t.getContext("2d"),n=window.devicePixelRatio||1;a.scale(n,n);const s=new Image;s.setAttribute("crossOrigin","anonymous"),s.src=e.src,s.onload=()=>{s.width,s.height,t.style.width=s.width+"px",t.style.height=s.height+"px",t.width=s.width,t.height=s.height,a.drawImage(s,0,0,s.width,s.height,0,0,t.width,t.height);const n=t.toDataURL("image/png"),i=document.createElement("a");i.href=n,i.download=`${e.name}.png`,i.click()}};export{R as _,S as d};
//# sourceMappingURL=chunk.275523d0.js.map
