{"version": 3, "file": "chunk.d8776116.js", "sources": ["../src/api/common/index.ts"], "sourcesContent": ["import axios from \"@/api\";\nimport qs from \"qs\";\n// 获取素材列表\nexport const materialList = async (args) => {\n  return await axios.get(\"/material/list\", {\n    params: args\n  });\n};\n\nexport const materialDetail = async (args) => {\n  const res = await axios.get(\"/material/find\", {\n    params: args\n  });\n  return res.data;\n};\n// 获取应用版本\nexport const versionList = async (args) => {\n  return await axios.get(\"/apps/lverList\", {\n    params: args\n  });\n};\n\n// 文件上传\nexport const upload = async (args) => {\n  return await axios.post(\"/file/upload\", args, {\n    \"Content-Type\": \"form-data\"\n  } as any);\n};\n\n// 素材收藏\nexport const collect = async (args) => {\n  return await axios.get(\"/material/collect\", {\n    params: args\n  });\n};\n// 获取文件夹列表\nexport const getfolderList = async (args) => {\n  return await axios.get(\"/folder/list\", {\n    params: args\n  });\n};\n\n// 删除文件夹\nexport const folderDelete = async (args) => {\n  return await axios.get(\"/folder/delete\", {\n    params: args\n  });\n};\n\n// 获取应用列表\nexport const appsList = async (args) => {\n  return await axios.get(\"/apps/list\", {\n    params: args\n  });\n};\n\n// 获取模块功能列表\nexport const appsModuleList = async (args) => {\n  return await axios.get(\"/apps/moduleList\", {\n    params: args\n  });\n};\n\n// 分享素材\nexport const shareMaterial = async (args) => {\n  return await axios.get(\"/material/share\", {\n    params: args\n  });\n};\n"], "names": ["materialList", "async", "args", "axios", "get", "params", "materialDetail", "data", "versionList", "upload", "post", "collect", "getfolderList", "folderDelete", "appsList", "appsModuleList", "shareMaterial"], "mappings": "wCAGa,MAAAA,EAAeC,MAAOC,SACpBC,EAAMC,IAAI,iBAAkB,CACvCC,OAAQH,IAICI,EAAiBL,MAAOC,UACjBC,EAAMC,IAAI,iBAAkB,CAC5CC,OAAQH,KAECK,KAGAC,EAAcP,MAAOC,SACnBC,EAAMC,IAAI,iBAAkB,CACvCC,OAAQH,IAKCO,EAASR,MAAOC,SACdC,EAAMO,KAAK,eAAgBR,EAAM,CAC5C,eAAgB,cAKPS,EAAUV,MAAOC,SACfC,EAAMC,IAAI,oBAAqB,CAC1CC,OAAQH,IAICU,EAAgBX,MAAOC,SACrBC,EAAMC,IAAI,eAAgB,CACrCC,OAAQH,IAKCW,EAAeZ,MAAOC,SACpBC,EAAMC,IAAI,iBAAkB,CACvCC,OAAQH,IAKCY,EAAWb,MAAOC,SAChBC,EAAMC,IAAI,aAAc,CACnCC,OAAQH,IAKCa,EAAiBd,MAAOC,SACtBC,EAAMC,IAAI,mBAAoB,CACzCC,OAAQH,IAKCc,EAAgBf,MAAOC,SACrBC,EAAMC,IAAI,kBAAmB,CACxCC,OAAQH"}