import{b as e,c as t,u as o,E as a}from"./chunk.db8898e3.js";import{h as r,l as s,m as p,v as i,o as l,ap as n,x as f}from"./chunk.8df321e8.js";import{d,z as c,r as u,u as b,o as v,A as h,B as m,c as g,n as w,e as y,h as x,O as A,C as S,aG as C}from"./index.05904f40.js";const k=r({trigger:e.trigger,placement:t.placement,disabled:e.disabled,visible:o.visible,transition:o.transition,popperOptions:t.popperOptions,tabindex:t.tabindex,content:o.content,popperStyle:o.popperStyle,popperClass:o.popperClass,enterable:{...o.enterable,default:!0},effect:{...o.effect,default:"light"},teleported:o.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),B={"update:visible":e=>s(e),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},N=d({name:"ElPopover"}),R=d({...N,props:k,emits:B,setup(e,{expose:t,emit:o}){const r=e,s=c((()=>r["onUpdate:visible"])),l=p("popover"),n=u(),f=c((()=>{var e;return null==(e=b(n))?void 0:e.popperRef})),d=c((()=>[{width:i(r.width)},r.popperStyle])),k=c((()=>[l.b(),r.popperClass,{[l.m("plain")]:!!r.content}])),B=c((()=>r.transition===`${l.namespace.value}-fade-in-linear`)),N=()=>{o("before-enter")},R=()=>{o("before-leave")},$=()=>{o("after-enter")},O=()=>{o("update:visible",!1),o("after-leave")};return t({popperRef:f,hide:()=>{var e;null==(e=n.value)||e.hide()}}),(e,t)=>(v(),h(b(a),C({ref_key:"tooltipRef",ref:n},e.$attrs,{trigger:e.trigger,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":b(k),"popper-style":b(d),teleported:e.teleported,persistent:e.persistent,"gpu-acceleration":b(B),"onUpdate:visible":b(s),onBeforeShow:N,onBeforeHide:R,onShow:$,onHide:O}),{content:m((()=>[e.title?(v(),g("div",{key:0,class:w(b(l).e("title")),role:"title"},y(e.title),3)):x("v-if",!0),A(e.$slots,"default",{},(()=>[S(y(e.content),1)]))])),default:m((()=>[e.$slots.reference?A(e.$slots,"reference",{key:0}):x("v-if",!0)])),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});const $=(e,t)=>{const o=t.arg||t.value,a=null==o?void 0:o.popperRef;a&&(a.triggerRef=e)};const O=f(l(R,[["__file","popover.vue"]]),{directive:n({mounted(e,t){$(e,t)},updated(e,t){$(e,t)}},"popover")});export{O as E};
//# sourceMappingURL=chunk.1fa2ec58.js.map
