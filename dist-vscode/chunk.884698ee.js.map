{"version": 3, "file": "chunk.884698ee.js", "sources": ["../node_modules/element-plus/es/components/roving-focus-group/src/roving-focus-group.mjs", "../node_modules/element-plus/es/components/roving-focus-group/src/tokens.mjs", "../node_modules/element-plus/es/components/roving-focus-group/src/utils.mjs", "../node_modules/element-plus/es/components/roving-focus-group/src/roving-focus-group-impl.mjs", "../node_modules/element-plus/es/components/roving-focus-group/src/roving-focus-group2.mjs", "../node_modules/element-plus/es/components/roving-focus-group/src/roving-focus-item.mjs", "../node_modules/element-plus/es/components/dropdown/src/tokens.mjs", "../node_modules/element-plus/es/components/dropdown/src/dropdown2.mjs", "../node_modules/element-plus/es/components/dropdown/src/dropdown-item-impl.mjs", "../node_modules/element-plus/es/components/dropdown/src/useDropdown.mjs", "../node_modules/element-plus/es/components/dropdown/src/dropdown-item.mjs", "../node_modules/element-plus/es/components/dropdown/src/dropdown-menu.mjs", "../node_modules/element-plus/es/components/dropdown/index.mjs"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport '../../collection/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { createCollectionWithScope } from '../../collection/src/collection.mjs';\n\nconst rovingFocusGroupProps = buildProps({\n  style: { type: definePropType([String, Array, Object]) },\n  currentTabId: {\n    type: definePropType(String)\n  },\n  defaultCurrentTabId: String,\n  loop: Boolean,\n  dir: {\n    type: String,\n    values: [\"ltr\", \"rtl\"],\n    default: \"ltr\"\n  },\n  orientation: {\n    type: definePropType(String)\n  },\n  onBlur: Function,\n  onFocus: Function,\n  onMousedown: Function\n});\nconst {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY\n} = createCollectionWithScope(\"RovingFocusGroup\");\n\nexport { ElCollection, ElCollectionItem, COLLECTION_INJECTION_KEY as ROVING_FOCUS_COLLECTION_INJECTION_KEY, COLLECTION_ITEM_INJECTION_KEY as ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY, rovingFocusGroupProps };\n//# sourceMappingURL=roving-focus-group.mjs.map\n", "const ROVING_FOCUS_GROUP_INJECTION_KEY = Symbol(\"elRovingFocusGroup\");\nconst ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY = Symbol(\"elRovingFocusGroupItem\");\n\nexport { ROVING_FOCUS_GROUP_INJECTION_KEY, ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY };\n//# sourceMappingURL=tokens.mjs.map\n", "import '../../../constants/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\n\nconst MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nconst getDirectionAwareKey = (key, dir) => {\n  if (dir !== \"rtl\")\n    return key;\n  switch (key) {\n    case EVENT_CODE.right:\n      return EVENT_CODE.left;\n    case EVENT_CODE.left:\n      return EVENT_CODE.right;\n    default:\n      return key;\n  }\n};\nconst getFocusIntent = (event, orientation, dir) => {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === \"vertical\" && [EVENT_CODE.left, EVENT_CODE.right].includes(key))\n    return void 0;\n  if (orientation === \"horizontal\" && [EVENT_CODE.up, EVENT_CODE.down].includes(key))\n    return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n};\nconst reorderArray = (array, atIdx) => {\n  return array.map((_, idx) => array[(idx + atIdx) % array.length]);\n};\nconst focusFirst = (elements) => {\n  const { activeElement: prevActive } = document;\n  for (const element of elements) {\n    if (element === prevActive)\n      return;\n    element.focus();\n    if (prevActive !== document.activeElement)\n      return;\n  }\n};\n\nexport { focusFirst, getFocusIntent, reorderArray };\n//# sourceMappingURL=utils.mjs.map\n", "import { defineComponent, ref, inject, computed, unref, provide, readonly, toRef, watch, renderSlot } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport '../../../utils/index.mjs';\nimport { rovingFocusGroupProps, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens.mjs';\nimport { focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\n\nconst CURRENT_TAB_ID_CHANGE_EVT = \"currentTabIdChange\";\nconst ENTRY_FOCUS_EVT = \"rovingFocusGroup.entryFocus\";\nconst EVT_OPTS = { bubbles: false, cancelable: true };\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroupImpl\",\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, \"entryFocus\"],\n  setup(props, { emit }) {\n    var _a;\n    const currentTabbedId = ref((_a = props.currentTabId || props.defaultCurrentTabId) != null ? _a : null);\n    const isBackingOut = ref(false);\n    const isClickFocus = ref(false);\n    const rovingFocusGroupRef = ref(null);\n    const { getItems } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const rovingFocusGroupRootStyle = computed(() => {\n      return [\n        {\n          outline: \"none\"\n        },\n        props.style\n      ];\n    });\n    const onItemFocus = (tabbedId) => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId);\n    };\n    const onItemShiftTab = () => {\n      isBackingOut.value = true;\n    };\n    const onMousedown = composeEventHandlers((e) => {\n      var _a2;\n      (_a2 = props.onMousedown) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isClickFocus.value = true;\n    });\n    const onFocus = composeEventHandlers((e) => {\n      var _a2;\n      (_a2 = props.onFocus) == null ? void 0 : _a2.call(props, e);\n    }, (e) => {\n      const isKeyboardFocus = !unref(isClickFocus);\n      const { target, currentTarget } = e;\n      if (target === currentTarget && isKeyboardFocus && !unref(isBackingOut)) {\n        const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS);\n        currentTarget == null ? void 0 : currentTarget.dispatchEvent(entryFocusEvt);\n        if (!entryFocusEvt.defaultPrevented) {\n          const items = getItems().filter((item) => item.focusable);\n          const activeItem = items.find((item) => item.active);\n          const currentItem = items.find((item) => item.id === unref(currentTabbedId));\n          const candidates = [activeItem, currentItem, ...items].filter(Boolean);\n          const candidateNodes = candidates.map((item) => item.ref);\n          focusFirst(candidateNodes);\n        }\n      }\n      isClickFocus.value = false;\n    });\n    const onBlur = composeEventHandlers((e) => {\n      var _a2;\n      (_a2 = props.onBlur) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isBackingOut.value = false;\n    });\n    const handleEntryFocus = (...args) => {\n      emit(\"entryFocus\", ...args);\n    };\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, \"loop\"),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0;\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, \"orientation\"),\n      dir: toRef(props, \"dir\"),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown\n    });\n    watch(() => props.currentTabId, (val) => {\n      currentTabbedId.value = val != null ? val : null;\n    });\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus);\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar ElRovingFocusGroupImpl = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group-impl.vue\"]]);\n\nexport { ElRovingFocusGroupImpl as default };\n//# sourceMappingURL=roving-focus-group-impl.mjs.map\n", "import { defineComponent, resolveComponent, openBlock, createBlock, withCtx, createVNode, normalizeProps, guardReactiveProps, renderSlot } from 'vue';\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.mjs';\nimport { ElCollection } from './roving-focus-group.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\n\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroup\",\n  components: {\n    ElFocusGroupCollection: ElCollection,\n    ElRovingFocusGroupImpl\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_roving_focus_group_impl = resolveComponent(\"el-roving-focus-group-impl\");\n  const _component_el_focus_group_collection = resolveComponent(\"el-focus-group-collection\");\n  return openBlock(), createBlock(_component_el_focus_group_collection, null, {\n    default: withCtx(() => [\n      createVNode(_component_el_roving_focus_group_impl, normalizeProps(guardReactiveProps(_ctx.$attrs)), {\n        default: withCtx(() => [\n          renderSlot(_ctx.$slots, \"default\")\n        ]),\n        _: 3\n      }, 16)\n    ]),\n    _: 3\n  });\n}\nvar ElRovingFocusGroup = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group.vue\"]]);\n\nexport { ElRovingFocusGroup as default };\n//# sourceMappingURL=roving-focus-group2.mjs.map\n", "import { defineComponent, inject, ref, unref, nextTick, computed, provide, resolveComponent, openBlock, createBlock, withCtx, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport { ElCollectionItem, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY, ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY } from './tokens.mjs';\nimport { getFocusIntent, reorderArray, focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\n\nconst _sfc_main = defineComponent({\n  components: {\n    ElRovingFocusCollectionItem: ElCollectionItem\n  },\n  props: {\n    focusable: {\n      type: Boolean,\n      default: true\n    },\n    active: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\"mousedown\", \"focus\", \"keydown\"],\n  setup(props, { emit }) {\n    const { currentTabbedId, loop, onItemFocus, onItemShiftTab } = inject(ROVING_FOCUS_GROUP_INJECTION_KEY, void 0);\n    const { getItems } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const id = useId();\n    const rovingFocusGroupItemRef = ref(null);\n    const handleMousedown = composeEventHandlers((e) => {\n      emit(\"mousedown\", e);\n    }, (e) => {\n      if (!props.focusable) {\n        e.preventDefault();\n      } else {\n        onItemFocus(unref(id));\n      }\n    });\n    const handleFocus = composeEventHandlers((e) => {\n      emit(\"focus\", e);\n    }, () => {\n      onItemFocus(unref(id));\n    });\n    const handleKeydown = composeEventHandlers((e) => {\n      emit(\"keydown\", e);\n    }, (e) => {\n      const { key, shiftKey, target, currentTarget } = e;\n      if (key === EVENT_CODE.tab && shiftKey) {\n        onItemShiftTab();\n        return;\n      }\n      if (target !== currentTarget)\n        return;\n      const focusIntent = getFocusIntent(e);\n      if (focusIntent) {\n        e.preventDefault();\n        const items = getItems().filter((item) => item.focusable);\n        let elements = items.map((item) => item.ref);\n        switch (focusIntent) {\n          case \"last\": {\n            elements.reverse();\n            break;\n          }\n          case \"prev\":\n          case \"next\": {\n            if (focusIntent === \"prev\") {\n              elements.reverse();\n            }\n            const currentIdx = elements.indexOf(currentTarget);\n            elements = loop.value ? reorderArray(elements, currentIdx + 1) : elements.slice(currentIdx + 1);\n            break;\n          }\n          default: {\n            break;\n          }\n        }\n        nextTick(() => {\n          focusFirst(elements);\n        });\n      }\n    });\n    const isCurrentTab = computed(() => currentTabbedId.value === unref(id));\n    provide(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, {\n      rovingFocusGroupItemRef,\n      tabIndex: computed(() => unref(isCurrentTab) ? 0 : -1),\n      handleMousedown,\n      handleFocus,\n      handleKeydown\n    });\n    return {\n      id,\n      handleKeydown,\n      handleFocus,\n      handleMousedown\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_roving_focus_collection_item = resolveComponent(\"el-roving-focus-collection-item\");\n  return openBlock(), createBlock(_component_el_roving_focus_collection_item, {\n    id: _ctx.id,\n    focusable: _ctx.focusable,\n    active: _ctx.active\n  }, {\n    default: withCtx(() => [\n      renderSlot(_ctx.$slots, \"default\")\n    ]),\n    _: 3\n  }, 8, [\"id\", \"focusable\", \"active\"]);\n}\nvar ElRovingFocusItem = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-item.vue\"]]);\n\nexport { ElRovingFocusItem as default };\n//# sourceMappingURL=roving-focus-item.mjs.map\n", "const DROPDOWN_INJECTION_KEY = Symbol(\"elDropdown\");\n\nexport { DROPDOWN_INJECTION_KEY };\n//# sourceMappingURL=tokens.mjs.map\n", "import { defineComponent, getCurrentInstance, ref, computed, watch, onBeforeUnmount, unref, provide, toRef, resolveComponent, openBlock, createElementBlock, normalizeClass, createVNode, createSlots, withCtx, renderSlot, createBlock, mergeProps, createCommentVNode } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../roving-focus-group/index.mjs';\nimport '../../slot/index.mjs';\nimport '../../form/index.mjs';\nimport '../../../utils/index.mjs';\nimport { ArrowDown } from '@element-plus/icons-vue';\nimport '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { ElCollection, dropdownProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ElRovingFocusGroup from '../../roving-focus-group/src/roving-focus-group2.mjs';\nimport { OnlyChild } from '../../slot/src/only-child.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { castArray } from 'lodash-unified';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\n\nconst { ButtonGroup: ElButtonGroup } = ElButton;\nconst _sfc_main = defineComponent({\n  name: \"ElDropdown\",\n  components: {\n    ElButton,\n    ElButtonGroup,\n    ElScrollbar,\n    ElDropdownCollection: ElCollection,\n    ElTooltip,\n    ElRovingFocusGroup,\n    ElOnlyChild: OnlyChild,\n    ElIcon,\n    ArrowDown\n  },\n  props: dropdownProps,\n  emits: [\"visible-change\", \"click\", \"command\"],\n  setup(props, { emit }) {\n    const _instance = getCurrentInstance();\n    const ns = useNamespace(\"dropdown\");\n    const { t } = useLocale();\n    const triggeringElementRef = ref();\n    const referenceElementRef = ref();\n    const popperRef = ref(null);\n    const contentRef = ref(null);\n    const scrollbar = ref(null);\n    const currentTabId = ref(null);\n    const isUsingKeyboard = ref(false);\n    const triggerKeys = [EVENT_CODE.enter, EVENT_CODE.space, EVENT_CODE.down];\n    const wrapStyle = computed(() => ({\n      maxHeight: addUnit(props.maxHeight)\n    }));\n    const dropdownTriggerKls = computed(() => [ns.m(dropdownSize.value)]);\n    const trigger = computed(() => castArray(props.trigger));\n    const defaultTriggerId = useId().value;\n    const triggerId = computed(() => {\n      return props.id || defaultTriggerId;\n    });\n    watch([triggeringElementRef, trigger], ([triggeringElement, trigger2], [prevTriggeringElement]) => {\n      var _a, _b, _c;\n      if ((_a = prevTriggeringElement == null ? void 0 : prevTriggeringElement.$el) == null ? void 0 : _a.removeEventListener) {\n        prevTriggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if ((_b = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if (((_c = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _c.addEventListener) && trigger2.includes(\"hover\")) {\n        triggeringElement.$el.addEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    }, { immediate: true });\n    onBeforeUnmount(() => {\n      var _a, _b;\n      if ((_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElementRef.value.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    });\n    function handleClick() {\n      handleClose();\n    }\n    function handleClose() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onClose();\n    }\n    function handleOpen() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onOpen();\n    }\n    const dropdownSize = useFormSize();\n    function commandHandler(...args) {\n      emit(\"command\", ...args);\n    }\n    function onAutofocusTriggerEnter() {\n      var _a, _b;\n      (_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.focus();\n    }\n    function onItemEnter() {\n    }\n    function onItemLeave() {\n      const contentEl = unref(contentRef);\n      trigger.value.includes(\"hover\") && (contentEl == null ? void 0 : contentEl.focus());\n      currentTabId.value = null;\n    }\n    function handleCurrentTabIdChange(id) {\n      currentTabId.value = id;\n    }\n    function handleEntryFocus(e) {\n      if (!isUsingKeyboard.value) {\n        e.preventDefault();\n        e.stopImmediatePropagation();\n      }\n    }\n    function handleBeforeShowTooltip() {\n      emit(\"visible-change\", true);\n    }\n    function handleShowTooltip(event) {\n      if ((event == null ? void 0 : event.type) === \"keydown\") {\n        contentRef.value.focus();\n      }\n    }\n    function handleBeforeHideTooltip() {\n      emit(\"visible-change\", false);\n    }\n    provide(DROPDOWN_INJECTION_KEY, {\n      contentRef,\n      role: computed(() => props.role),\n      triggerId,\n      isUsingKeyboard,\n      onItemEnter,\n      onItemLeave\n    });\n    provide(\"elDropdown\", {\n      instance: _instance,\n      dropdownSize,\n      handleClick,\n      commandHandler,\n      trigger: toRef(props, \"trigger\"),\n      hideOnClick: toRef(props, \"hideOnClick\")\n    });\n    const onFocusAfterTrapped = (e) => {\n      var _a, _b;\n      e.preventDefault();\n      (_b = (_a = contentRef.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a, {\n        preventScroll: true\n      });\n    };\n    const handlerMainButtonClick = (event) => {\n      emit(\"click\", event);\n    };\n    return {\n      t,\n      ns,\n      scrollbar,\n      wrapStyle,\n      dropdownTriggerKls,\n      dropdownSize,\n      triggerId,\n      triggerKeys,\n      currentTabId,\n      handleCurrentTabIdChange,\n      handlerMainButtonClick,\n      handleEntryFocus,\n      handleClose,\n      handleOpen,\n      handleBeforeShowTooltip,\n      handleShowTooltip,\n      handleBeforeHideTooltip,\n      onFocusAfterTrapped,\n      popperRef,\n      contentRef,\n      triggeringElementRef,\n      referenceElementRef\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_collection = resolveComponent(\"el-dropdown-collection\");\n  const _component_el_roving_focus_group = resolveComponent(\"el-roving-focus-group\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _component_el_only_child = resolveComponent(\"el-only-child\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _component_el_button = resolveComponent(\"el-button\");\n  const _component_arrow_down = resolveComponent(\"arrow-down\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_button_group = resolveComponent(\"el-button-group\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"disabled\", _ctx.disabled)])\n  }, [\n    createVNode(_component_el_tooltip, {\n      ref: \"popperRef\",\n      role: _ctx.role,\n      effect: _ctx.effect,\n      \"fallback-placements\": [\"bottom\", \"top\"],\n      \"popper-options\": _ctx.popperOptions,\n      \"gpu-acceleration\": false,\n      \"hide-after\": _ctx.trigger === \"hover\" ? _ctx.hideTimeout : 0,\n      \"manual-mode\": true,\n      placement: _ctx.placement,\n      \"popper-class\": [_ctx.ns.e(\"popper\"), _ctx.popperClass],\n      \"reference-element\": (_a = _ctx.referenceElementRef) == null ? void 0 : _a.$el,\n      trigger: _ctx.trigger,\n      \"trigger-keys\": _ctx.triggerKeys,\n      \"trigger-target-el\": _ctx.contentRef,\n      \"show-after\": _ctx.trigger === \"hover\" ? _ctx.showTimeout : 0,\n      \"stop-popper-mouse-event\": false,\n      \"virtual-ref\": _ctx.triggeringElementRef,\n      \"virtual-triggering\": _ctx.splitButton,\n      disabled: _ctx.disabled,\n      transition: `${_ctx.ns.namespace.value}-zoom-in-top`,\n      teleported: _ctx.teleported,\n      pure: \"\",\n      persistent: \"\",\n      onBeforeShow: _ctx.handleBeforeShowTooltip,\n      onShow: _ctx.handleShowTooltip,\n      onBeforeHide: _ctx.handleBeforeHideTooltip\n    }, createSlots({\n      content: withCtx(() => [\n        createVNode(_component_el_scrollbar, {\n          ref: \"scrollbar\",\n          \"wrap-style\": _ctx.wrapStyle,\n          tag: \"div\",\n          \"view-class\": _ctx.ns.e(\"list\")\n        }, {\n          default: withCtx(() => [\n            createVNode(_component_el_roving_focus_group, {\n              loop: _ctx.loop,\n              \"current-tab-id\": _ctx.currentTabId,\n              orientation: \"horizontal\",\n              onCurrentTabIdChange: _ctx.handleCurrentTabIdChange,\n              onEntryFocus: _ctx.handleEntryFocus\n            }, {\n              default: withCtx(() => [\n                createVNode(_component_el_dropdown_collection, null, {\n                  default: withCtx(() => [\n                    renderSlot(_ctx.$slots, \"dropdown\")\n                  ]),\n                  _: 3\n                })\n              ]),\n              _: 3\n            }, 8, [\"loop\", \"current-tab-id\", \"onCurrentTabIdChange\", \"onEntryFocus\"])\n          ]),\n          _: 3\n        }, 8, [\"wrap-style\", \"view-class\"])\n      ]),\n      _: 2\n    }, [\n      !_ctx.splitButton ? {\n        name: \"default\",\n        fn: withCtx(() => [\n          createVNode(_component_el_only_child, {\n            id: _ctx.triggerId,\n            ref: \"triggeringElementRef\",\n            role: \"button\",\n            tabindex: _ctx.tabindex\n          }, {\n            default: withCtx(() => [\n              renderSlot(_ctx.$slots, \"default\")\n            ]),\n            _: 3\n          }, 8, [\"id\", \"tabindex\"])\n        ])\n      } : void 0\n    ]), 1032, [\"role\", \"effect\", \"popper-options\", \"hide-after\", \"placement\", \"popper-class\", \"reference-element\", \"trigger\", \"trigger-keys\", \"trigger-target-el\", \"show-after\", \"virtual-ref\", \"virtual-triggering\", \"disabled\", \"transition\", \"teleported\", \"onBeforeShow\", \"onShow\", \"onBeforeHide\"]),\n    _ctx.splitButton ? (openBlock(), createBlock(_component_el_button_group, { key: 0 }, {\n      default: withCtx(() => [\n        createVNode(_component_el_button, mergeProps({ ref: \"referenceElementRef\" }, _ctx.buttonProps, {\n          size: _ctx.dropdownSize,\n          type: _ctx.type,\n          disabled: _ctx.disabled,\n          tabindex: _ctx.tabindex,\n          onClick: _ctx.handlerMainButtonClick\n        }), {\n          default: withCtx(() => [\n            renderSlot(_ctx.$slots, \"default\")\n          ]),\n          _: 3\n        }, 16, [\"size\", \"type\", \"disabled\", \"tabindex\", \"onClick\"]),\n        createVNode(_component_el_button, mergeProps({\n          id: _ctx.triggerId,\n          ref: \"triggeringElementRef\"\n        }, _ctx.buttonProps, {\n          role: \"button\",\n          size: _ctx.dropdownSize,\n          type: _ctx.type,\n          class: _ctx.ns.e(\"caret-button\"),\n          disabled: _ctx.disabled,\n          tabindex: _ctx.tabindex,\n          \"aria-label\": _ctx.t(\"el.dropdown.toggleDropdown\")\n        }), {\n          default: withCtx(() => [\n            createVNode(_component_el_icon, {\n              class: normalizeClass(_ctx.ns.e(\"icon\"))\n            }, {\n              default: withCtx(() => [\n                createVNode(_component_arrow_down)\n              ]),\n              _: 1\n            }, 8, [\"class\"])\n          ]),\n          _: 1\n        }, 16, [\"id\", \"size\", \"type\", \"class\", \"disabled\", \"tabindex\", \"aria-label\"])\n      ]),\n      _: 3\n    })) : createCommentVNode(\"v-if\", true)\n  ], 2);\n}\nvar Dropdown = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown.vue\"]]);\n\nexport { Dropdown as default };\n//# sourceMappingURL=dropdown2.mjs.map\n", "import { defineComponent, inject, computed, resolveComponent, openBlock, createElementBlock, Fragment, mergeProps, createCommentVNode, createElementVNode, withModifiers, createBlock, withCtx, resolveDynamicComponent, renderSlot } from 'vue';\nimport '../../roving-focus-group/index.mjs';\nimport '../../collection/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport { dropdownItemProps, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY as COLLECTION_ITEM_INJECTION_KEY } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY as COLLECTION_ITEM_INJECTION_KEY$1 } from '../../roving-focus-group/src/roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY } from '../../roving-focus-group/src/tokens.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { COLLECTION_ITEM_SIGN } from '../../collection/src/collection.mjs';\n\nconst _sfc_main = defineComponent({\n  name: \"DropdownItemImpl\",\n  components: {\n    ElIcon\n  },\n  props: dropdownItemProps,\n  emits: [\"pointermove\", \"pointerleave\", \"click\", \"clickimpl\"],\n  setup(_, { emit }) {\n    const ns = useNamespace(\"dropdown\");\n    const { role: menuRole } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const { collectionItemRef: dropdownCollectionItemRef } = inject(COLLECTION_ITEM_INJECTION_KEY, void 0);\n    const { collectionItemRef: rovingFocusCollectionItemRef } = inject(COLLECTION_ITEM_INJECTION_KEY$1, void 0);\n    const {\n      rovingFocusGroupItemRef,\n      tabIndex,\n      handleFocus,\n      handleKeydown: handleItemKeydown,\n      handleMousedown\n    } = inject(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, void 0);\n    const itemRef = composeRefs(dropdownCollectionItemRef, rovingFocusCollectionItemRef, rovingFocusGroupItemRef);\n    const role = computed(() => {\n      if (menuRole.value === \"menu\") {\n        return \"menuitem\";\n      } else if (menuRole.value === \"navigation\") {\n        return \"link\";\n      }\n      return \"button\";\n    });\n    const handleKeydown = composeEventHandlers((e) => {\n      const { code } = e;\n      if (code === EVENT_CODE.enter || code === EVENT_CODE.space) {\n        e.preventDefault();\n        e.stopImmediatePropagation();\n        emit(\"clickimpl\", e);\n        return true;\n      }\n    }, handleItemKeydown);\n    return {\n      ns,\n      itemRef,\n      dataset: {\n        [COLLECTION_ITEM_SIGN]: \"\"\n      },\n      role,\n      tabIndex,\n      handleFocus,\n      handleKeydown,\n      handleMousedown\n    };\n  }\n});\nconst _hoisted_1 = [\"aria-disabled\", \"tabindex\", \"role\"];\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  return openBlock(), createElementBlock(Fragment, null, [\n    _ctx.divided ? (openBlock(), createElementBlock(\"li\", mergeProps({\n      key: 0,\n      role: \"separator\",\n      class: _ctx.ns.bem(\"menu\", \"item\", \"divided\")\n    }, _ctx.$attrs), null, 16)) : createCommentVNode(\"v-if\", true),\n    createElementVNode(\"li\", mergeProps({ ref: _ctx.itemRef }, { ..._ctx.dataset, ..._ctx.$attrs }, {\n      \"aria-disabled\": _ctx.disabled,\n      class: [_ctx.ns.be(\"menu\", \"item\"), _ctx.ns.is(\"disabled\", _ctx.disabled)],\n      tabindex: _ctx.tabIndex,\n      role: _ctx.role,\n      onClick: _cache[0] || (_cache[0] = (e) => _ctx.$emit(\"clickimpl\", e)),\n      onFocus: _cache[1] || (_cache[1] = (...args) => _ctx.handleFocus && _ctx.handleFocus(...args)),\n      onKeydown: _cache[2] || (_cache[2] = withModifiers((...args) => _ctx.handleKeydown && _ctx.handleKeydown(...args), [\"self\"])),\n      onMousedown: _cache[3] || (_cache[3] = (...args) => _ctx.handleMousedown && _ctx.handleMousedown(...args)),\n      onPointermove: _cache[4] || (_cache[4] = (e) => _ctx.$emit(\"pointermove\", e)),\n      onPointerleave: _cache[5] || (_cache[5] = (e) => _ctx.$emit(\"pointerleave\", e))\n    }), [\n      _ctx.icon ? (openBlock(), createBlock(_component_el_icon, { key: 0 }, {\n        default: withCtx(() => [\n          (openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))\n        ]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true),\n      renderSlot(_ctx.$slots, \"default\")\n    ], 16, _hoisted_1)\n  ], 64);\n}\nvar ElDropdownItemImpl = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-item-impl.vue\"]]);\n\nexport { ElDropdownItemImpl as default };\n//# sourceMappingURL=dropdown-item-impl.mjs.map\n", "import { inject, computed, ref } from 'vue';\nimport '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { addClass } from '../../../utils/dom/style.mjs';\n\nconst useDropdown = () => {\n  const elDropdown = inject(\"elDropdown\", {});\n  const _elDropdownSize = computed(() => elDropdown == null ? void 0 : elDropdown.dropdownSize);\n  return {\n    elDropdown,\n    _elDropdownSize\n  };\n};\nconst initDropdownDomEvent = (dropdownChildren, triggerElm, _instance) => {\n  const ns = useNamespace(\"dropdown\");\n  const menuItems = ref(null);\n  const menuItemsArray = ref(null);\n  const dropdownElm = ref(null);\n  const listId = useId();\n  dropdownElm.value = dropdownChildren == null ? void 0 : dropdownChildren.subTree.el;\n  function removeTabindex() {\n    var _a;\n    triggerElm.setAttribute(\"tabindex\", \"-1\");\n    (_a = menuItemsArray.value) == null ? void 0 : _a.forEach((item) => {\n      item.setAttribute(\"tabindex\", \"-1\");\n    });\n  }\n  function resetTabindex(ele) {\n    removeTabindex();\n    ele == null ? void 0 : ele.setAttribute(\"tabindex\", \"0\");\n  }\n  function handleTriggerKeyDown(ev) {\n    const code = ev.code;\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      removeTabindex();\n      resetTabindex(menuItems.value[0]);\n      menuItems.value[0].focus();\n      ev.preventDefault();\n      ev.stopPropagation();\n    } else if (code === EVENT_CODE.enter) {\n      _instance.handleClick();\n    } else if ([EVENT_CODE.tab, EVENT_CODE.esc].includes(code)) {\n      _instance.hide();\n    }\n  }\n  function handleItemKeyDown(ev) {\n    const code = ev.code;\n    const target = ev.target;\n    const currentIndex = menuItemsArray.value.indexOf(target);\n    const max = menuItemsArray.value.length - 1;\n    let nextIndex;\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      if (code === EVENT_CODE.up) {\n        nextIndex = currentIndex !== 0 ? currentIndex - 1 : 0;\n      } else {\n        nextIndex = currentIndex < max ? currentIndex + 1 : max;\n      }\n      removeTabindex();\n      resetTabindex(menuItems.value[nextIndex]);\n      menuItems.value[nextIndex].focus();\n      ev.preventDefault();\n      ev.stopPropagation();\n    } else if (code === EVENT_CODE.enter) {\n      triggerElmFocus();\n      target.click();\n      if (_instance.props.hideOnClick) {\n        _instance.hide();\n      }\n    } else if ([EVENT_CODE.tab, EVENT_CODE.esc].includes(code)) {\n      _instance.hide();\n      triggerElmFocus();\n    }\n  }\n  function initAria() {\n    dropdownElm.value.setAttribute(\"id\", listId.value);\n    triggerElm.setAttribute(\"aria-haspopup\", \"list\");\n    triggerElm.setAttribute(\"aria-controls\", listId.value);\n    if (!_instance.props.splitButton) {\n      triggerElm.setAttribute(\"role\", \"button\");\n      triggerElm.setAttribute(\"tabindex\", _instance.props.tabindex);\n      addClass(triggerElm, ns.b(\"selfdefine\"));\n    }\n  }\n  function initEvent() {\n    var _a;\n    triggerElm == null ? void 0 : triggerElm.addEventListener(\"keydown\", handleTriggerKeyDown);\n    (_a = dropdownElm.value) == null ? void 0 : _a.addEventListener(\"keydown\", handleItemKeyDown, true);\n  }\n  function initDomOperation() {\n    menuItems.value = dropdownElm.value.querySelectorAll(\"[tabindex='-1']\");\n    menuItemsArray.value = Array.from(menuItems.value);\n    initEvent();\n    initAria();\n  }\n  function triggerElmFocus() {\n    triggerElm.focus();\n  }\n  initDomOperation();\n};\n\nexport { initDropdownDomEvent, useDropdown };\n//# sourceMappingURL=useDropdown.mjs.map\n", "import { defineComponent, getCurrentInstance, ref, computed, unref, inject, resolveComponent, openBlock, createBlock, withCtx, createVNode, mergeProps, renderSlot } from 'vue';\nimport '../../roving-focus-group/index.mjs';\nimport '../../../utils/index.mjs';\nimport ElDropdownItemImpl from './dropdown-item-impl.mjs';\nimport { useDropdown } from './useDropdown.mjs';\nimport { ElCollectionItem, dropdownItemProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ElRovingFocusItem from '../../roving-focus-group/src/roving-focus-item.mjs';\nimport { composeEventHandlers, whenMouse } from '../../../utils/dom/event.mjs';\n\nconst _sfc_main = defineComponent({\n  name: \"ElDropdownItem\",\n  components: {\n    ElDropdownCollectionItem: ElCollectionItem,\n    ElRovingFocusItem,\n    ElDropdownItemImpl\n  },\n  inheritAttrs: false,\n  props: dropdownItemProps,\n  emits: [\"pointermove\", \"pointerleave\", \"click\"],\n  setup(props, { emit, attrs }) {\n    const { elDropdown } = useDropdown();\n    const _instance = getCurrentInstance();\n    const itemRef = ref(null);\n    const textContent = computed(() => {\n      var _a, _b;\n      return (_b = (_a = unref(itemRef)) == null ? void 0 : _a.textContent) != null ? _b : \"\";\n    });\n    const { onItemEnter, onItemLeave } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const handlePointerMove = composeEventHandlers((e) => {\n      emit(\"pointermove\", e);\n      return e.defaultPrevented;\n    }, whenMouse((e) => {\n      if (props.disabled) {\n        onItemLeave(e);\n        return;\n      }\n      const target = e.currentTarget;\n      if (target === document.activeElement || target.contains(document.activeElement)) {\n        return;\n      }\n      onItemEnter(e);\n      if (!e.defaultPrevented) {\n        target == null ? void 0 : target.focus();\n      }\n    }));\n    const handlePointerLeave = composeEventHandlers((e) => {\n      emit(\"pointerleave\", e);\n      return e.defaultPrevented;\n    }, whenMouse((e) => {\n      onItemLeave(e);\n    }));\n    const handleClick = composeEventHandlers((e) => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", e);\n      return e.type !== \"keydown\" && e.defaultPrevented;\n    }, (e) => {\n      var _a, _b, _c;\n      if (props.disabled) {\n        e.stopImmediatePropagation();\n        return;\n      }\n      if ((_a = elDropdown == null ? void 0 : elDropdown.hideOnClick) == null ? void 0 : _a.value) {\n        (_b = elDropdown.handleClick) == null ? void 0 : _b.call(elDropdown);\n      }\n      (_c = elDropdown.commandHandler) == null ? void 0 : _c.call(elDropdown, props.command, _instance, e);\n    });\n    const propsAndAttrs = computed(() => {\n      return { ...props, ...attrs };\n    });\n    return {\n      handleClick,\n      handlePointerMove,\n      handlePointerLeave,\n      textContent,\n      propsAndAttrs\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_item_impl = resolveComponent(\"el-dropdown-item-impl\");\n  const _component_el_roving_focus_item = resolveComponent(\"el-roving-focus-item\");\n  const _component_el_dropdown_collection_item = resolveComponent(\"el-dropdown-collection-item\");\n  return openBlock(), createBlock(_component_el_dropdown_collection_item, {\n    disabled: _ctx.disabled,\n    \"text-value\": (_a = _ctx.textValue) != null ? _a : _ctx.textContent\n  }, {\n    default: withCtx(() => [\n      createVNode(_component_el_roving_focus_item, {\n        focusable: !_ctx.disabled\n      }, {\n        default: withCtx(() => [\n          createVNode(_component_el_dropdown_item_impl, mergeProps(_ctx.propsAndAttrs, {\n            onPointerleave: _ctx.handlePointerLeave,\n            onPointermove: _ctx.handlePointerMove,\n            onClickimpl: _ctx.handleClick\n          }), {\n            default: withCtx(() => [\n              renderSlot(_ctx.$slots, \"default\")\n            ]),\n            _: 3\n          }, 16, [\"onPointerleave\", \"onPointermove\", \"onClickimpl\"])\n        ]),\n        _: 3\n      }, 8, [\"focusable\"])\n    ]),\n    _: 3\n  }, 8, [\"disabled\", \"text-value\"]);\n}\nvar DropdownItem = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-item.vue\"]]);\n\nexport { DropdownItem as default };\n//# sourceMappingURL=dropdown-item.mjs.map\n", "import { defineComponent, inject, computed, unref, openBlock, createElementBlock, normalizeClass, normalizeStyle, withModifiers, renderSlot } from 'vue';\nimport '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport '../../focus-trap/index.mjs';\nimport '../../roving-focus-group/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport { dropdownMenuProps, DROPDOWN_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY, FIRST_LAST_KEYS, LAST_KEYS } from './dropdown.mjs';\nimport { useDropdown } from './useDropdown.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { FOCUS_TRAP_INJECTION_KEY } from '../../focus-trap/src/tokens.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from '../../roving-focus-group/src/tokens.mjs';\nimport { ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY$1 } from '../../roving-focus-group/src/roving-focus-group.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { focusFirst } from '../../roving-focus-group/src/utils.mjs';\n\nconst _sfc_main = defineComponent({\n  name: \"ElDropdownMenu\",\n  props: dropdownMenuProps,\n  setup(props) {\n    const ns = useNamespace(\"dropdown\");\n    const { _elDropdownSize } = useDropdown();\n    const size = _elDropdownSize.value;\n    const { focusTrapRef, onKeydown } = inject(FOCUS_TRAP_INJECTION_KEY, void 0);\n    const { contentRef, role, triggerId } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const { collectionRef: dropdownCollectionRef, getItems } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const {\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      onBlur,\n      onFocus,\n      onMousedown\n    } = inject(ROVING_FOCUS_GROUP_INJECTION_KEY, void 0);\n    const { collectionRef: rovingFocusGroupCollectionRef } = inject(COLLECTION_INJECTION_KEY$1, void 0);\n    const dropdownKls = computed(() => {\n      return [ns.b(\"menu\"), ns.bm(\"menu\", size == null ? void 0 : size.value)];\n    });\n    const dropdownListWrapperRef = composeRefs(contentRef, dropdownCollectionRef, focusTrapRef, rovingFocusGroupRef, rovingFocusGroupCollectionRef);\n    const composedKeydown = composeEventHandlers((e) => {\n      var _a;\n      (_a = props.onKeydown) == null ? void 0 : _a.call(props, e);\n    }, (e) => {\n      const { currentTarget, code, target } = e;\n      const isKeydownContained = currentTarget.contains(target);\n      if (isKeydownContained) {\n      }\n      if (EVENT_CODE.tab === code) {\n        e.stopImmediatePropagation();\n      }\n      e.preventDefault();\n      if (target !== unref(contentRef))\n        return;\n      if (!FIRST_LAST_KEYS.includes(code))\n        return;\n      const items = getItems().filter((item) => !item.disabled);\n      const targets = items.map((item) => item.ref);\n      if (LAST_KEYS.includes(code)) {\n        targets.reverse();\n      }\n      focusFirst(targets);\n    });\n    const handleKeydown = (e) => {\n      composedKeydown(e);\n      onKeydown(e);\n    };\n    return {\n      size,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      dropdownKls,\n      role,\n      triggerId,\n      dropdownListWrapperRef,\n      handleKeydown,\n      onBlur,\n      onFocus,\n      onMousedown\n    };\n  }\n});\nconst _hoisted_1 = [\"role\", \"aria-labelledby\"];\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"ul\", {\n    ref: _ctx.dropdownListWrapperRef,\n    class: normalizeClass(_ctx.dropdownKls),\n    style: normalizeStyle(_ctx.rovingFocusGroupRootStyle),\n    tabindex: -1,\n    role: _ctx.role,\n    \"aria-labelledby\": _ctx.triggerId,\n    onBlur: _cache[0] || (_cache[0] = (...args) => _ctx.onBlur && _ctx.onBlur(...args)),\n    onFocus: _cache[1] || (_cache[1] = (...args) => _ctx.onFocus && _ctx.onFocus(...args)),\n    onKeydown: _cache[2] || (_cache[2] = withModifiers((...args) => _ctx.handleKeydown && _ctx.handleKeydown(...args), [\"self\"])),\n    onMousedown: _cache[3] || (_cache[3] = withModifiers((...args) => _ctx.onMousedown && _ctx.onMousedown(...args), [\"self\"]))\n  }, [\n    renderSlot(_ctx.$slots, \"default\")\n  ], 46, _hoisted_1);\n}\nvar DropdownMenu = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-menu.vue\"]]);\n\nexport { DropdownMenu as default };\n//# sourceMappingURL=dropdown-menu.mjs.map\n", "import '../../utils/index.mjs';\nimport Dropdown from './src/dropdown2.mjs';\nimport DropdownItem from './src/dropdown-item.mjs';\nimport DropdownMenu from './src/dropdown-menu.mjs';\nexport { DROPDOWN_COLLECTION_INJECTION_KEY, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY, ElCollection, ElCollectionItem, FIRST_KEYS, FIRST_LAST_KEYS, LAST_KEYS, dropdownItemProps, dropdownMenuProps, dropdownProps } from './src/dropdown.mjs';\nimport './src/instance.mjs';\nexport { DROPDOWN_INJECTION_KEY } from './src/tokens.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\n\nconst ElDropdown = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu\n});\nconst ElDropdownItem = withNoopInstall(DropdownItem);\nconst ElDropdownMenu = withNoopInstall(DropdownMenu);\n\nexport { ElDropdown, ElDropdownItem, ElDropdownMenu, ElDropdown as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["rovingFocusGroupProps", "buildProps", "style", "type", "definePropType", "String", "Array", "Object", "currentTabId", "defaultCurrentTabId", "loop", "Boolean", "dir", "values", "default", "orientation", "onBlur", "Function", "onFocus", "onMousedown", "ElCollection", "ElCollectionItem", "COLLECTION_INJECTION_KEY", "COLLECTION_ITEM_INJECTION_KEY", "createCollectionWithScope", "ROVING_FOCUS_GROUP_INJECTION_KEY", "Symbol", "ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY", "MAP_KEY_TO_FOCUS_INTENT", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "PageUp", "Home", "PageDown", "End", "getFocusIntent", "event", "key", "EVENT_CODE", "right", "left", "getDirectionAwareKey", "includes", "up", "down", "focusFirst", "elements", "activeElement", "prevActive", "document", "element", "focus", "CURRENT_TAB_ID_CHANGE_EVT", "ENTRY_FOCUS_EVT", "EVT_OPTS", "bubbles", "cancelable", "_sfc_main", "defineComponent", "name", "inheritAttrs", "props", "emits", "setup", "emit", "_a", "currentTabbedId", "ref", "isBackingOut", "isClickFocus", "rovingFocusGroupRef", "getItems", "inject", "rovingFocusGroupRootStyle", "computed", "outline", "composeEventHandlers", "e", "_a2", "call", "value", "isKeyboardFocus", "unref", "target", "currentTarget", "entryFocusEvt", "Event", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "candidateNodes", "find", "active", "id", "map", "provide", "readonly", "toRef", "tabIndex", "onItemFocus", "tabbedId", "onItemShiftTab", "watch", "val", "useEventListener", "args", "ElRovingFocusGroup", "_export_sfc", "components", "ElFocusGroupCollection", "ElRovingFocusGroupImpl", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "renderSlot", "$slots", "_component_el_roving_focus_group_impl", "resolveComponent", "_component_el_focus_group_collection", "openBlock", "createBlock", "withCtx", "createVNode", "normalizeProps", "guardReactiveProps", "$attrs", "_", "ElRovingFocusItem", "ElRovingFocusCollectionItem", "useId", "rovingFocusGroupItemRef", "handleMousedown", "preventDefault", "handleFocus", "handleKeydown", "shift<PERSON>ey", "tab", "focusIntent", "reverse", "currentIdx", "indexOf", "atIdx", "array", "idx", "length", "slice", "nextTick", "isCurrentTab", "_component_el_roving_focus_collection_item", "DROPDOWN_INJECTION_KEY", "ButtonGroup", "ElButtonGroup", "ElButton", "Dropdown", "ElScrollbar", "ElDropdownCollection", "ElTooltip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ElIcon", "dropdownProps", "_instance", "getCurrentInstance", "ns", "useNamespace", "t", "useLocale", "triggeringElementRef", "referenceElementRef", "popperRef", "contentRef", "scrollbar", "isUsingKeyboard", "triggerKeys", "enter", "space", "wrapStyle", "maxHeight", "addUnit", "dropdownTriggerKls", "m", "dropdownSize", "trigger", "<PERSON><PERSON><PERSON><PERSON>", "defaultTriggerId", "triggerId", "handleClose", "onClose", "triggeringElement", "trigger2", "prevTriggeringElement", "_b", "_c", "$el", "removeEventListener", "onAutofocusTriggerEnter", "addEventListener", "immediate", "onBeforeUnmount", "useFormSize", "role", "onItemEnter", "onItemLeave", "contentEl", "instance", "handleClick", "command<PERSON><PERSON>ler", "hideOnClick", "handleCurrentTabIdChange", "handlerMainButtonClick", "handleEntryFocus", "stopImmediatePropagation", "handleOpen", "onOpen", "handleBeforeShowTooltip", "handleShowTooltip", "handleBeforeHideTooltip", "onFocusAfterTrapped", "preventScroll", "_component_el_dropdown_collection", "_component_el_roving_focus_group", "_component_el_scrollbar", "_component_el_only_child", "_component_el_tooltip", "_component_el_button", "_component_arrow_down", "_component_el_icon", "_component_el_button_group", "createElementBlock", "class", "normalizeClass", "b", "is", "disabled", "effect", "popperOptions", "hideTimeout", "placement", "popperClass", "showTimeout", "splitButton", "transition", "namespace", "teleported", "pure", "persistent", "onBeforeShow", "onShow", "onBeforeHide", "createSlots", "content", "tag", "onCurrentTabIdChange", "onEntryFocus", "fn", "tabindex", "mergeProps", "buttonProps", "size", "onClick", "createCommentVNode", "dropdownItemProps", "menuRole", "collectionItemRef", "dropdownCollectionItemRef", "rovingFocusCollectionItemRef", "COLLECTION_ITEM_INJECTION_KEY$1", "handleItemKeydown", "itemRef", "composeRefs", "code", "dataset", "COLLECTION_ITEM_SIGN", "_hoisted_1", "useDropdown", "elDropdown", "_elDropdownSize", "DropdownItem", "ElDropdownCollectionItem", "ElDropdownItemImpl", "Fragment", "divided", "bem", "createElementVNode", "be", "$emit", "onKeydown", "withModifiers", "onPointermove", "onPointerleave", "icon", "resolveDynamicComponent", "attrs", "textContent", "handlePointerMove", "whenMouse", "contains", "handlePointerLeave", "command", "propsAndAttrs", "_component_el_dropdown_item_impl", "_component_el_roving_focus_item", "_component_el_dropdown_collection_item", "textValue", "onClickimpl", "dropdownMenuProps", "focusTrapRef", "FOCUS_TRAP_INJECTION_KEY", "collectionRef", "dropdownCollectionRef", "rovingFocusGroupCollectionRef", "COLLECTION_INJECTION_KEY$1", "dropdownKls", "bm", "dropdownListWrapperRef", "<PERSON><PERSON><PERSON><PERSON>", "FIRST_LAST_KEYS", "targets", "LAST_KEYS", "DropdownMenu", "normalizeStyle", "ElDropdown", "withInstall", "ElDropdownItem", "withNoopInstall", "ElDropdownMenu"], "mappings": "glBAKA,MAAMA,GAAwBC,EAAW,CACvCC,MAAO,CAAEC,KAAMC,EAAe,CAACC,OAAQC,MAAOC,UAC9CC,aAAc,CACZL,KAAMC,EAAeC,SAEvBI,oBAAqBJ,OACrBK,KAAMC,QACNC,IAAK,CACHT,KAAME,OACNQ,OAAQ,CAAC,MAAO,OAChBC,QAAS,OAEXC,YAAa,CACXZ,KAAMC,EAAeC,SAEvBW,OAAQC,SACRC,QAASD,SACTE,YAAaF,YAETG,aACJA,GAAAC,iBACAA,GAAAC,yBACAA,GAAAC,8BACAA,IACEC,EAA0B,oBC7BxBC,GAAmCC,OAAO,sBAC1CC,GAAwCD,OAAO,0BCE/CE,GAA0B,CAC9BC,UAAW,OACXC,QAAS,OACTC,WAAY,OACZC,UAAW,OACXC,OAAQ,QACRC,KAAM,QACNC,SAAU,OACVC,IAAK,QAcDC,GAAiB,CAACC,EAAOvB,EAAaH,KAC1C,MAAM2B,EAbqB,EAACA,EAAK3B,KACjC,GAAY,QAARA,EACK,OAAA2B,EACT,OAAQA,GACN,KAAKC,EAAWC,MACd,OAAOD,EAAWE,KACpB,KAAKF,EAAWE,KACd,OAAOF,EAAWC,MACpB,QACS,OAAAF,EACV,EAGWI,CAAqBL,EAAMC,IAAK3B,GACxC,KAAgB,aAAhBG,GAA8B,CAACyB,EAAWE,KAAMF,EAAWC,OAAOG,SAASL,IAE3D,eAAhBxB,GAAgC,CAACyB,EAAWK,GAAIL,EAAWM,MAAMF,SAASL,IAE9E,OAAOX,GAAwBW,EAAG,EAK9BQ,GAAcC,IACZ,MAAEC,cAAeC,GAAeC,SACtC,IAAA,MAAWC,KAAWJ,EAAU,CAC9B,GAAII,IAAYF,EACd,OAEF,GADAE,EAAQC,QACJH,IAAeC,SAASF,cAC1B,MACH,GCnCGK,GAA4B,qBAC5BC,GAAkB,8BAClBC,GAAW,CAAEC,SAAS,EAAOC,YAAY,GACzCC,GAAYC,EAAgB,CAChCC,KAAM,yBACNC,cAAc,EACdC,MAAO/D,GACPgE,MAAO,CAACV,GAA2B,cACnC,KAAAW,CAAMF,GAAOG,KAAEA,IACT,IAAAC,EACE,MAAAC,EAAkBC,EAA8D,OAAzDF,EAAKJ,EAAMvD,cAAgBuD,EAAMtD,qBAA+B0D,EAAK,MAC5FG,EAAeD,GAAI,GACnBE,EAAeF,GAAI,GACnBG,EAAsBH,EAAI,OAC1BI,SAAEA,GAAaC,EAAOpD,QAA0B,GAChDqD,EAA4BC,GAAS,IAClC,CACL,CACEC,QAAS,QAEXd,EAAM7D,SASJiB,EAAc2D,GAAsBC,IACpC,IAAAC,EACyB,OAA5BA,EAAMjB,EAAM5C,cAAgC6D,EAAIC,KAAKlB,EAAOgB,EAAC,IAC7D,KACDR,EAAaW,OAAQ,CAAA,IAEjBhE,EAAU4D,GAAsBC,IAChC,IAAAC,EACqB,OAAxBA,EAAMjB,EAAM7C,UAA4B8D,EAAIC,KAAKlB,EAAOgB,EAAC,IACxDA,IACI,MAAAI,GAAmBC,EAAMb,IACzBc,OAAEA,EAAQC,cAAAA,GAAkBP,EAClC,GAAIM,IAAWC,GAAiBH,IAAoBC,EAAMd,GAAe,CACvE,MAAMiB,EAAgB,IAAIC,MAAMjC,GAAiBC,IAE7C,GADa,MAAjB8B,GAAiCA,EAAcG,cAAcF,IACxDA,EAAcG,iBAAkB,CACnC,MAAMC,EAAQlB,IAAWmB,QAAQC,GAASA,EAAKC,YAIzCC,EADa,CAFAJ,EAAMK,MAAMH,GAASA,EAAKI,SACzBN,EAAMK,MAAMH,GAASA,EAAKK,KAAOd,EAAMhB,QACXuB,GAAOC,OAAOjF,SAC5BwF,KAAKN,GAASA,EAAKxB,MACrDtB,GAAWgD,EACZ,CACF,CACDxB,EAAaW,OAAQ,CAAA,IAEjBlE,EAAS8D,GAAsBC,IAC/B,IAAAC,EACoB,OAAvBA,EAAMjB,EAAM/C,SAA2BgE,EAAIC,KAAKlB,EAAOgB,EAAC,IACxD,KACDT,EAAaY,OAAQ,CAAA,IAKvBkB,EAAQ3E,GAAkC,CACxC2C,gBAAiBiC,EAASjC,GAC1B1D,KAAM4F,EAAMvC,EAAO,QACnBwC,SAAU3B,GAAS,IACVQ,EAAMd,IAAqB,EAAA,IAEpCE,sBACAG,4BACA5D,YAAauF,EAAMvC,EAAO,eAC1BnD,IAAK0F,EAAMvC,EAAO,OAClByC,YAnDmBC,IACnBvC,EAAKZ,GAA2BmD,EAAQ,EAmDxCC,eAjDqB,KACrBpC,EAAaY,OAAQ,CAAA,EAiDrBlE,SACAE,UACAC,gBAEFwF,GAAM,IAAM5C,EAAMvD,eAAeoG,IACfxC,EAAAc,MAAe,MAAP0B,EAAcA,EAAM,IAAA,IAE7BC,EAAArC,EAAqBjB,IAtBb,IAAIuD,KACtB5C,EAAA,gBAAiB4C,EAAI,GAsB7B,IClEH,IAAIC,GAAqCC,EAtBvBpD,EAAgB,CAChCC,KAAM,qBACNoD,WAAY,CACVC,uBAAwB9F,GACxB+F,uBDyFyCH,EAAYrD,GAAW,CAAC,CAAC,SAHtE,SAAqByD,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GACjD,OAAAC,EAAWN,EAAKO,OAAQ,UACjC,GAC8F,CAAC,SAAU,oCCvEzC,CAAC,CAAC,SAflE,SAAqBP,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GAClD,MAAAG,EAAwCC,EAAiB,8BACzDC,EAAuCD,EAAiB,6BAC9D,OAAOE,IAAaC,EAAYF,EAAsC,KAAM,CAC1EhH,QAASmH,GAAQ,IAAM,CACrBC,EAAYN,EAAuCO,EAAeC,EAAmBhB,EAAKiB,SAAU,CAClGvH,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,cAE1BW,EAAG,GACF,OAELA,EAAG,GAEP,GAC0F,CAAC,SAAU,4BCsFrG,IAAIC,GAAoCvB,EArGtBpD,EAAgB,CAChCqD,WAAY,CACVuB,4BAA6BnH,IAE/B0C,MAAO,CACL+B,UAAW,CACT3F,KAAMQ,QACNG,SAAS,GAEXmF,OAAQ,CACN9F,KAAMQ,QACNG,SAAS,IAGbkD,MAAO,CAAC,YAAa,QAAS,WAC9B,KAAAC,CAAMF,GAAOG,KAAEA,IACP,MAAAE,gBAAEA,OAAiB1D,EAAM8F,YAAAA,EAAAE,eAAaA,GAAmBhC,EAAOjD,QAAkC,IAClGgD,SAAEA,GAAaC,EAAOpD,QAA0B,GAChD4E,EAAKuC,IACLC,EAA0BrE,EAAI,MAC9BsE,EAAkB7D,GAAsBC,IAC5Cb,EAAK,YAAaa,EAAC,IACjBA,IACGhB,EAAM+B,UAGGU,EAAApB,EAAMc,IAFlBnB,EAAE6D,gBAGH,IAEGC,EAAc/D,GAAsBC,IACxCb,EAAK,QAASa,EAAC,IACd,KACWyB,EAAApB,EAAMc,GAAG,IAEjB4C,EAAgBhE,GAAsBC,IAC1Cb,EAAK,UAAWa,EAAC,IACfA,IACF,MAAMxC,IAAEA,EAAAwG,SAAKA,EAAU1D,OAAAA,EAAAC,cAAQA,GAAkBP,EAC7C,GAAAxC,IAAQC,EAAWwG,KAAOD,EAE5B,gBAEF,GAAI1D,IAAWC,EACb,OACI,MAAA2D,EAAc5G,GAAe0C,GACnC,GAAIkE,EAAa,CACflE,EAAE6D,iBAEF,IAAI5F,EADUyB,IAAWmB,QAAQC,GAASA,EAAKC,YAC1BK,KAAKN,GAASA,EAAKxB,MACxC,OAAQ4E,GACN,IAAK,OACHjG,EAASkG,UACT,MAEF,IAAK,OACL,IAAK,OAAQ,CACS,SAAhBD,GACFjG,EAASkG,UAEL,MAAAC,EAAanG,EAASoG,QAAQ9D,GACzBtC,EAAAtC,EAAKwE,OHvCCmE,EGuC8BF,EAAa,GHvClDG,EGuC2BtG,GHtClCmD,KAAI,CAACmC,EAAGiB,IAAQD,GAAOC,EAAMF,GAASC,EAAME,WGsCkBxG,EAASyG,MAAMN,EAAa,GAC7F,KACD,EAKHO,GAAS,KACP3G,GAAWC,EAAQ,GAEtB,CHjDc,IAACsG,EAAOD,CGiDtB,IAEGM,EAAe/E,GAAS,IAAMR,EAAgBc,QAAUE,EAAMc,KAQ7D,OAPPE,EAAQzE,GAAuC,CAC7C+G,0BACAnC,SAAU3B,GAAS,IAAMQ,EAAMuE,GAAgB,GAAM,IACrDhB,kBACAE,cACAC,kBAEK,CACL5C,KACA4C,gBACAD,cACAF,kBAEH,IAe4D,CAAC,CAAC,SAbjE,SAAqBvB,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GAClD,MAAAmC,EAA6C/B,EAAiB,mCAC7D,OAAAE,IAAaC,EAAY4B,EAA4C,CAC1E1D,GAAIkB,EAAKlB,GACTJ,UAAWsB,EAAKtB,UAChBG,OAAQmB,EAAKnB,QACZ,CACDnF,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,cAE1BW,EAAG,GACF,EAAG,CAAC,KAAM,YAAa,UAC5B,GACyF,CAAC,SAAU,2BCjHpG,MAAMuB,GAAyBnI,OAAO,eCyB9BoI,YAAaC,IAAkBC,EA8RvC,IAAIC,GAA2BjD,EA7RbpD,EAAgB,CAChCC,KAAM,aACNoD,WAAY,CACV+C,WACAD,iBACAG,cACAC,qBAAsB/I,EACtBgJ,YACArD,sBACAsD,YAAaC,EACbC,SACJvI,UAAIA,GAEF+B,MAAOyG,EACPxG,MAAO,CAAC,iBAAkB,QAAS,WACnC,KAAAC,CAAMF,GAAOG,KAAEA,IACb,MAAMuG,EAAYC,IACZC,EAAKC,EAAa,aAClBC,EAAEA,GAAMC,IACRC,EAAuB1G,IACvB2G,EAAsB3G,IACtB4G,EAAY5G,EAAI,MAChB6G,EAAa7G,EAAI,MACjB8G,EAAY9G,EAAI,MAChB7D,EAAe6D,EAAI,MACnB+G,EAAkB/G,GAAI,GACtBgH,EAAc,CAAC7I,EAAW8I,MAAO9I,EAAW+I,MAAO/I,EAAWM,MAC9D0I,EAAY5G,GAAS,KAAO,CAChC6G,UAAWC,EAAQ3H,EAAM0H,eAErBE,EAAqB/G,GAAS,IAAM,CAAC+F,EAAGiB,EAAEC,EAAa3G,UACvD4G,EAAUlH,GAAS,IAAMmH,EAAUhI,EAAM+H,WACzCE,EAAmBvD,IAAQvD,MAC3B+G,EAAYrH,GAAS,IAClBb,EAAMmC,IAAM8F,IAuBrB,SAASE,IACH,IAAA/H,EACsB,OAAzBA,EAAK8G,EAAU/F,QAA0Bf,EAAGgI,SAC9C,CAxBKxF,EAAA,CAACoE,EAAsBe,IAAU,EAAEM,EAAmBC,IAAYC,MACtE,IAAInI,EAAIoI,EAAIC,GACqE,OAA5ErI,EAA8B,MAAzBmI,OAAgC,EAASA,EAAsBG,UAAe,EAAStI,EAAGuI,sBAC5EJ,EAAAG,IAAIC,oBAAoB,eAAgBC,IAES,OAApEJ,EAA0B,MAArBH,OAA4B,EAASA,EAAkBK,UAAe,EAASF,EAAGG,sBACxEN,EAAAK,IAAIC,oBAAoB,eAAgBC,IAEc,OAApEH,EAA0B,MAArBJ,OAA4B,EAASA,EAAkBK,UAAe,EAASD,EAAGI,mBAAqBP,EAASzJ,SAAS,UAChHwJ,EAAAK,IAAIG,iBAAiB,eAAgBD,EACxD,GACA,CAAEE,WAAW,IAChBC,GAAgB,KACd,IAAI3I,EAAIoI,GACkE,OAArEA,EAA0C,OAApCpI,EAAK4G,EAAqB7F,YAAiB,EAASf,EAAGsI,UAAe,EAASF,EAAGG,sBAC3F3B,EAAqB7F,MAAMuH,IAAIC,oBAAoB,eAAgBC,EACpE,IAaH,MAAMd,EAAekB,IAIrB,SAASJ,IACP,IAAIxI,EAAIoI,EAC8D,OAArEA,EAA0C,OAApCpI,EAAK4G,EAAqB7F,YAAiB,EAASf,EAAGsI,MAAwBF,EAAGlJ,OAC1F,CA4BD+C,EAAQyD,GAAwB,CAC9BqB,aACA8B,KAAMpI,GAAS,IAAMb,EAAMiJ,OAC3Bf,YACAb,kBACA6B,YAhCF,WACC,EAgCCC,YA/BF,WACQ,MAAAC,EAAY/H,EAAM8F,GAChBY,EAAA5G,MAAMtC,SAAS,WAA0B,MAAbuK,GAA6BA,EAAU9J,SAC3E7C,EAAa0E,MAAQ,IACtB,IA6BDkB,EAAQ,aAAc,CACpBgH,SAAU3C,EACVoB,eACAwB,YAzDF,cAEC,EAwDCC,eA9CF,YAA2BxG,GACpB5C,EAAA,aAAc4C,EACpB,EA6CCgF,QAASxF,EAAMvC,EAAO,WACtBwJ,YAAajH,EAAMvC,EAAO,iBAYrB,MAAA,CACL8G,IACAF,KACAQ,YACAK,YACAG,qBACAE,eACAI,YACAZ,cACA7K,eACAgN,yBAxDF,SAAkCtH,GAChC1F,EAAa0E,MAAQgB,CACtB,EAuDCuH,uBAd8BnL,IAC9B4B,EAAK,QAAS5B,EAAK,EAcnBoL,iBAvDF,SAA0B3I,GACnBqG,EAAgBlG,QACnBH,EAAE6D,iBACF7D,EAAE4I,2BAEL,EAmDCzB,cACA0B,WA/EF,WACM,IAAAzJ,EACsB,OAAzBA,EAAK8G,EAAU/F,QAA0Bf,EAAG0J,QAC9C,EA6ECC,wBApDF,WACE5J,EAAK,kBAAkB,EACxB,EAmDC6J,kBAlDF,SAA2BzL,GACqB,aAAhC,MAATA,OAAgB,EAASA,EAAMnC,OAClC+K,EAAWhG,MAAM7B,OAEpB,EA+CC2K,wBA9CF,WACE9J,EAAK,kBAAkB,EACxB,EA6CC+J,oBA5B2BlJ,IAC3B,IAAIZ,EAAIoI,EACRxH,EAAE6D,iBAC4D,OAA7D2D,EAAgC,OAA1BpI,EAAK+G,EAAWhG,YAAiB,EAASf,EAAGd,QAA0BkJ,EAAGtH,KAAKd,EAAI,CACxF+J,eAAe,GAChB,EAwBDjD,YACAC,aACAH,uBACAC,sBAEH,IAuImD,CAAC,CAAC,SArIxD,SAAqB5D,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GACpD,IAAAtD,EACE,MAAAgK,EAAoCtG,EAAiB,0BACrDuG,EAAmCvG,EAAiB,yBACpDwG,EAA0BxG,EAAiB,gBAC3CyG,EAA2BzG,EAAiB,iBAC5C0G,EAAwB1G,EAAiB,cACzC2G,EAAuB3G,EAAiB,aACxC4G,EAAwB5G,EAAiB,cACzC6G,EAAqB7G,EAAiB,WACtC8G,EAA6B9G,EAAiB,mBAC7C,OAAAE,IAAa6G,GAAmB,MAAO,CAC5CC,MAAOC,GAAe,CAAC1H,EAAKuD,GAAGoE,IAAK3H,EAAKuD,GAAGqE,GAAG,WAAY5H,EAAK6H,aAC/D,CACD/G,EAAYqG,EAAuB,CACjClK,IAAK,YACL2I,KAAM5F,EAAK4F,KACXkC,OAAQ9H,EAAK8H,OACb,sBAAuB,CAAC,SAAU,OAClC,iBAAkB9H,EAAK+H,cACvB,oBAAoB,EACpB,aAA+B,UAAjB/H,EAAK0E,QAAsB1E,EAAKgI,YAAc,EAC5D,eAAe,EACfC,UAAWjI,EAAKiI,UAChB,eAAgB,CAACjI,EAAKuD,GAAG5F,EAAE,UAAWqC,EAAKkI,aAC3C,oBAAwD,OAAlCnL,EAAKiD,EAAK4D,0BAA+B,EAAS7G,EAAGsI,IAC3EX,QAAS1E,EAAK0E,QACd,eAAgB1E,EAAKiE,YACrB,oBAAqBjE,EAAK8D,WAC1B,aAA+B,UAAjB9D,EAAK0E,QAAsB1E,EAAKmI,YAAc,EAC5D,2BAA2B,EAC3B,cAAenI,EAAK2D,qBACpB,qBAAsB3D,EAAKoI,YAC3BP,SAAU7H,EAAK6H,SACfQ,WAAY,GAAGrI,EAAKuD,GAAG+E,UAAUxK,oBACjCyK,WAAYvI,EAAKuI,WACjBC,KAAM,GACNC,WAAY,GACZC,aAAc1I,EAAK0G,wBACnBiC,OAAQ3I,EAAK2G,kBACbiC,aAAc5I,EAAK4G,yBAClBiC,GAAY,CACbC,QAASjI,GAAQ,IAAM,CACrBC,EAAYmG,EAAyB,CACnChK,IAAK,YACL,aAAc+C,EAAKoE,UACnB2E,IAAK,MACL,aAAc/I,EAAKuD,GAAG5F,EAAE,SACvB,CACDjE,QAASmH,GAAQ,IAAM,CACrBC,EAAYkG,EAAkC,CAC5C1N,KAAM0G,EAAK1G,KACX,iBAAkB0G,EAAK5G,aACvBO,YAAa,aACbqP,qBAAsBhJ,EAAKoG,yBAC3B6C,aAAcjJ,EAAKsG,kBAClB,CACD5M,QAASmH,GAAQ,IAAM,CACrBC,EAAYiG,EAAmC,KAAM,CACnDrN,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,eAE1BW,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,OAAQ,iBAAkB,uBAAwB,oBAE3DA,EAAG,GACF,EAAG,CAAC,aAAc,kBAEvBA,EAAG,GACF,CACAlB,EAAKoI,iBAeF,EAfgB,CAClB3L,KAAM,UACNyM,GAAIrI,GAAQ,IAAM,CAChBC,EAAYoG,EAA0B,CACpCpI,GAAIkB,EAAK6E,UACT5H,IAAK,uBACL2I,KAAM,SACNuD,SAAUnJ,EAAKmJ,UACd,CACDzP,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,cAE1BW,EAAG,GACF,EAAG,CAAC,KAAM,mBAGf,KAAM,CAAC,OAAQ,SAAU,iBAAkB,aAAc,YAAa,eAAgB,oBAAqB,UAAW,eAAgB,oBAAqB,aAAc,cAAe,qBAAsB,WAAY,aAAc,aAAc,eAAgB,SAAU,iBACpRlB,EAAKoI,aAAezH,IAAaC,EAAY2G,EAA4B,CAAEpM,IAAK,GAAK,CACnFzB,QAASmH,GAAQ,IAAM,CACrBC,EAAYsG,EAAsBgC,GAAW,CAAEnM,IAAK,uBAAyB+C,EAAKqJ,YAAa,CAC7FC,KAAMtJ,EAAKyE,aACX1L,KAAMiH,EAAKjH,KACX8O,SAAU7H,EAAK6H,SACfsB,SAAUnJ,EAAKmJ,SACfI,QAASvJ,EAAKqG,yBACZ,CACF3M,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,cAE1BW,EAAG,GACF,GAAI,CAAC,OAAQ,OAAQ,WAAY,WAAY,YAChDJ,EAAYsG,EAAsBgC,GAAW,CAC3CtK,GAAIkB,EAAK6E,UACT5H,IAAK,wBACJ+C,EAAKqJ,YAAa,CACnBzD,KAAM,SACN0D,KAAMtJ,EAAKyE,aACX1L,KAAMiH,EAAKjH,KACX0O,MAAOzH,EAAKuD,GAAG5F,EAAE,gBACjBkK,SAAU7H,EAAK6H,SACfsB,SAAUnJ,EAAKmJ,SACf,aAAcnJ,EAAKyD,EAAE,gCACnB,CACF/J,QAASmH,GAAQ,IAAM,CACrBC,EAAYwG,EAAoB,CAC9BG,MAAOC,GAAe1H,EAAKuD,GAAG5F,EAAE,UAC/B,CACDjE,QAASmH,GAAQ,IAAM,CACrBC,EAAYuG,MAEdnG,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,GAAI,CAAC,KAAM,OAAQ,OAAQ,QAAS,WAAY,WAAY,kBAEjEA,EAAG,KACCsI,GAAmB,QAAQ,IAChC,EACL,GACgF,CAAC,SAAU,kBCrS3F,MAAMjN,GAAYC,EAAgB,CAChCC,KAAM,mBACNoD,WAAY,CACVsD,UAEFxG,MAAO8M,EACP7M,MAAO,CAAC,cAAe,eAAgB,QAAS,aAChD,KAAAC,CAAMqE,GAAGpE,KAAEA,IACH,MAAAyG,EAAKC,EAAa,aAChBoC,KAAM8D,GAAapM,EAAOmF,QAAwB,IAClDkH,kBAAmBC,GAA8BtM,EAAOnD,OAA+B,IACvFwP,kBAAmBE,GAAiCvM,EAAOwM,QAAiC,IAC9FxI,wBACJA,EAAAnC,SACAA,EAAAsC,YACAA,EACAC,cAAeqI,EAAAxI,gBACfA,GACEjE,EAAO/C,QAAuC,GAC5CyP,EAAUC,EAAYL,EAA2BC,EAA8BvI,GAC/EsE,EAAOpI,GAAS,IACG,SAAnBkM,EAAS5L,MACJ,WACqB,eAAnB4L,EAAS5L,MACX,OAEF,WAEH4D,EAAgBhE,GAAsBC,IACpC,MAAAuM,KAAEA,GAASvM,EACjB,GAAIuM,IAAS9O,EAAW8I,OAASgG,IAAS9O,EAAW+I,MAI5C,OAHPxG,EAAE6D,iBACF7D,EAAE4I,2BACFzJ,EAAK,YAAaa,IACX,CACR,GACAoM,GACI,MAAA,CACLxG,KACAyG,UACAG,QAAS,CACPC,CAACA,GAAuB,IAE1BxE,OACAzG,WACAsC,cACAC,gBACAH,kBAEH,IAEG8I,GAAa,CAAC,gBAAiB,WAAY,QC5DjD,MAAMC,GAAc,KAClB,MAAMC,EAAajN,EAAO,aAAc,CAAE,GACpCkN,EAAkBhN,GAAS,IAAoB,MAAd+M,OAAqB,EAASA,EAAW9F,eACzE,MAAA,CACL8F,aACAC,kBACJ,ECkGA,IAAIC,GAA+B7K,EAtGjBpD,EAAgB,CAChCC,KAAM,iBACNoD,WAAY,CACV6K,yBAA0BzQ,EAC1BkH,qBACAwJ,mBFoFqC/K,EAAYrD,GAAW,CAAC,CAAC,SA9BlE,SAAqByD,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GAClD,MAAAiH,EAAqB7G,EAAiB,WAC5C,OAAOE,IAAa6G,GAAmBoD,GAAU,KAAM,CACrD5K,EAAK6K,SAAWlK,IAAa6G,GAAmB,KAAM4B,GAAW,CAC/DjO,IAAK,EACLyK,KAAM,YACN6B,MAAOzH,EAAKuD,GAAGuH,IAAI,OAAQ,OAAQ,YAClC9K,EAAKiB,QAAS,KAAM,KAAOuI,GAAmB,QAAQ,GACzDuB,GAAmB,KAAM3B,GAAW,CAAEnM,IAAK+C,EAAKgK,SAAW,IAAKhK,EAAKmK,WAAYnK,EAAKiB,QAAU,CAC9F,gBAAiBjB,EAAK6H,SACtBJ,MAAO,CAACzH,EAAKuD,GAAGyH,GAAG,OAAQ,QAAShL,EAAKuD,GAAGqE,GAAG,WAAY5H,EAAK6H,WAChEsB,SAAUnJ,EAAKb,SACfyG,KAAM5F,EAAK4F,KACX2D,QAAStJ,EAAO,KAAOA,EAAO,GAAMtC,GAAMqC,EAAKiL,MAAM,YAAatN,IAClE7D,QAASmG,EAAO,KAAOA,EAAO,GAAK,IAAIP,IAASM,EAAKyB,aAAezB,EAAKyB,eAAe/B,IACxFwL,UAAWjL,EAAO,KAAOA,EAAO,GAAKkL,IAAc,IAAIzL,IAASM,EAAK0B,eAAiB1B,EAAK0B,iBAAiBhC,IAAO,CAAC,UACpH3F,YAAakG,EAAO,KAAOA,EAAO,GAAK,IAAIP,IAASM,EAAKuB,iBAAmBvB,EAAKuB,mBAAmB7B,IACpG0L,cAAenL,EAAO,KAAOA,EAAO,GAAMtC,GAAMqC,EAAKiL,MAAM,cAAetN,IAC1E0N,eAAgBpL,EAAO,KAAOA,EAAO,GAAMtC,GAAMqC,EAAKiL,MAAM,eAAgBtN,MAC1E,CACFqC,EAAKsL,MAAQ3K,IAAaC,EAAY0G,EAAoB,CAAEnM,IAAK,GAAK,CACpEzB,QAASmH,GAAQ,IAAM,EACpBF,IAAaC,EAAY2K,GAAwBvL,EAAKsL,WAEzDpK,EAAG,KACCsI,GAAmB,QAAQ,GACjClJ,EAAWN,EAAKO,OAAQ,YACvB,GAAI8J,KACN,GACL,GAC0F,CAAC,SAAU,6BElFnG3N,cAAc,EACdC,MAAO8M,EACP7M,MAAO,CAAC,cAAe,eAAgB,SACvC,KAAAC,CAAMF,GAAOG,KAAEA,EAAA0O,MAAMA,IACb,MAAAjB,WAAEA,GAAeD,KACjBjH,EAAYC,IACZ0G,EAAU/M,EAAI,MACdwO,EAAcjO,GAAS,KAC3B,IAAIT,EAAIoI,EACA,OAAiE,OAAjEA,EAA8B,OAAxBpI,EAAKiB,EAAMgM,SAAoB,EAASjN,EAAG0O,aAAuBtG,EAAK,EAAA,KAEjFU,YAAEA,EAAaC,YAAAA,GAAgBxI,EAAOmF,QAAwB,GAC9DiJ,EAAoBhO,GAAsBC,IAC9Cb,EAAK,cAAea,GACbA,EAAEW,mBACRqN,GAAWhO,IACZ,GAAIhB,EAAMkL,SAER,YADA/B,EAAYnI,GAGd,MAAMM,EAASN,EAAEO,cACbD,IAAWlC,SAASF,eAAiBoC,EAAO2N,SAAS7P,SAASF,iBAGlEgK,EAAYlI,GACPA,EAAEW,kBACK,MAAAL,GAAgBA,EAAOhC,QAClC,KAEG4P,EAAqBnO,GAAsBC,IAC/Cb,EAAK,eAAgBa,GACdA,EAAEW,mBACRqN,GAAWhO,IACZmI,EAAYnI,EAAC,KAsBR,MAAA,CACLsI,YArBkBvI,GAAsBC,IACxC,IAAIhB,EAAMkL,SAIH,OADP/K,EAAK,QAASa,GACI,YAAXA,EAAE5E,MAAsB4E,EAAEW,gBAAA,IAC/BX,IACF,IAAIZ,EAAIoI,EAAIC,EACRzI,EAAMkL,SACRlK,EAAE4I,6BAG+D,OAA9DxJ,EAAmB,MAAdwN,OAAqB,EAASA,EAAWpE,kBAAuB,EAASpJ,EAAGe,SACnD,OAAhCqH,EAAKoF,EAAWtE,cAAgCd,EAAGtH,KAAK0M,IAEvB,OAAnCnF,EAAKmF,EAAWrE,iBAAmCd,EAAGvH,KAAK0M,EAAY5N,EAAMmP,QAASzI,EAAW1F,GAAC,IAOnG+N,oBACAG,qBACAJ,cACAM,cARoBvO,GAAS,KACtB,IAAKb,KAAU6O,MASzB,IAiCuD,CAAC,CAAC,SA/B5D,SAAqBxL,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GACpD,IAAAtD,EACE,MAAAiP,EAAmCvL,EAAiB,yBACpDwL,EAAkCxL,EAAiB,wBACnDyL,EAAyCzL,EAAiB,+BACzD,OAAAE,IAAaC,EAAYsL,EAAwC,CACtErE,SAAU7H,EAAK6H,SACf,aAAuC,OAAxB9K,EAAKiD,EAAKmM,WAAqBpP,EAAKiD,EAAKyL,aACvD,CACD/R,QAASmH,GAAQ,IAAM,CACrBC,EAAYmL,EAAiC,CAC3CvN,WAAYsB,EAAK6H,UAChB,CACDnO,QAASmH,GAAQ,IAAM,CACrBC,EAAYkL,EAAkC5C,GAAWpJ,EAAK+L,cAAe,CAC3EV,eAAgBrL,EAAK6L,mBACrBT,cAAepL,EAAK0L,kBACpBU,YAAapM,EAAKiG,cAChB,CACFvM,QAASmH,GAAQ,IAAM,CACrBP,EAAWN,EAAKO,OAAQ,cAE1BW,EAAG,GACF,GAAI,CAAC,iBAAkB,gBAAiB,mBAE7CA,EAAG,GACF,EAAG,CAAC,iBAETA,EAAG,GACF,EAAG,CAAC,WAAY,cACrB,GACoF,CAAC,SAAU,uBC9F/F,MAAM3E,GAAYC,EAAgB,CAChCC,KAAM,iBACNE,MAAO0P,EACP,KAAAxP,CAAMF,GACE,MAAA4G,EAAKC,EAAa,aAClBgH,gBAAEA,GAAoBF,KACtBhB,EAAOkB,EAAgB1M,OACvBwO,aAAEA,EAAcpB,UAAAA,GAAc5N,EAAOiP,OAA0B,IAC/DzI,WAAEA,EAAY8B,KAAAA,EAAAf,UAAMA,GAAcvH,EAAOmF,QAAwB,IAC/D+J,cAAeC,EAAApP,SAAuBA,GAAaC,EAAOpD,OAA0B,IACtFkD,oBACJA,EAAAG,0BACAA,EAAA4B,SACAA,EAAAvF,OACAA,EAAAE,QACAA,EAAAC,YACAA,GACEuD,EAAOjD,QAAkC,IACrCmS,cAAeE,GAAkCpP,EAAOqP,QAA4B,GACtFC,EAAcpP,GAAS,IACpB,CAAC+F,EAAGoE,EAAE,QAASpE,EAAGsJ,GAAG,OAAgB,MAARvD,OAAe,EAASA,EAAKxL,UAE7DgP,EAAyB7C,EAAYnG,EAAY2I,EAAuBH,EAAclP,EAAqBsP,GAC3GK,EAAkBrP,GAAsBC,IACxC,IAAAZ,EACsB,OAAzBA,EAAKJ,EAAMuO,YAA8BnO,EAAGc,KAAKlB,EAAOgB,EAAC,IACxDA,IACF,MAAMO,cAAEA,EAAAgM,KAAeA,EAAMjM,OAAAA,GAAWN,EAQpC,GAPuBO,EAAc0N,SAAS3N,GAG9C7C,EAAWwG,MAAQsI,GACrBvM,EAAE4I,2BAEJ5I,EAAE6D,iBACEvD,IAAWD,EAAM8F,GACnB,OACE,IAACkJ,EAAgBxR,SAAS0O,GAC5B,OACI,MACA+C,EADQ5P,IAAWmB,QAAQC,IAAUA,EAAKoJ,WAC1B9I,KAAKN,GAASA,EAAKxB,MACrCiQ,EAAU1R,SAAS0O,IACrB+C,EAAQnL,UAEVnG,GAAWsR,EAAO,IAMb,MAAA,CACL3D,OACA/L,4BACA4B,WACAyN,cACAhH,OACAf,YACAiI,yBACApL,cAZqB/D,IACrBoP,EAAgBpP,GAChBuN,EAAUvN,EAAC,EAWX/D,SACAE,UACAC,cAEH,IAEGsQ,GAAa,CAAC,OAAQ,mBAiB5B,IAAI8C,GAA+BvN,EAAYrD,GAAW,CAAC,CAAC,SAhB5D,SAAqByD,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,GACjD,OAAAM,IAAa6G,GAAmB,KAAM,CAC3CvK,IAAK+C,EAAK8M,uBACVrF,MAAOC,GAAe1H,EAAK4M,aAC3B9T,MAAOsU,GAAepN,EAAKzC,2BAC3B4L,UAAU,EACVvD,KAAM5F,EAAK4F,KACX,kBAAmB5F,EAAK6E,UACxBjL,OAAQqG,EAAO,KAAOA,EAAO,GAAK,IAAIP,IAASM,EAAKpG,QAAUoG,EAAKpG,UAAU8F,IAC7E5F,QAASmG,EAAO,KAAOA,EAAO,GAAK,IAAIP,IAASM,EAAKlG,SAAWkG,EAAKlG,WAAW4F,IAChFwL,UAAWjL,EAAO,KAAOA,EAAO,GAAKkL,IAAc,IAAIzL,IAASM,EAAK0B,eAAiB1B,EAAK0B,iBAAiBhC,IAAO,CAAC,UACpH3F,YAAakG,EAAO,KAAOA,EAAO,GAAKkL,IAAc,IAAIzL,IAASM,EAAKjG,aAAeiG,EAAKjG,eAAe2F,IAAO,CAAC,WACjH,CACDY,EAAWN,EAAKO,OAAQ,YACvB,GAAI8J,GACT,GACoF,CAAC,SAAU,uBC5F1F,MAACgD,GAAaC,EAAYzK,GAAU,CACvC4H,gBACA0C,kBAEII,GAAiBC,EAAgB/C,IACjCgD,GAAiBD,EAAgBL", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}