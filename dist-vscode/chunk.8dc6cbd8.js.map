{"version": 3, "file": "chunk.8dc6cbd8.js", "sources": ["../node_modules/element-plus/es/components/drawer/src/drawer.mjs", "../node_modules/element-plus/es/components/drawer/src/drawer2.mjs", "../node_modules/element-plus/es/components/drawer/index.mjs"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport '../../dialog/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { dialogProps, dialogEmits } from '../../dialog/src/dialog.mjs';\n\nconst drawerProps = buildProps({\n  ...dialogProps,\n  direction: {\n    type: String,\n    default: \"rtl\",\n    values: [\"ltr\", \"rtl\", \"ttb\", \"btt\"]\n  },\n  size: {\n    type: [String, Number],\n    default: \"30%\"\n  },\n  withHeader: {\n    type: Boolean,\n    default: true\n  },\n  modalFade: {\n    type: Boolean,\n    default: true\n  },\n  headerAriaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst drawerEmits = dialogEmits;\n\nexport { drawerEmits, drawerProps };\n//# sourceMappingURL=drawer.mjs.map\n", "import { defineComponent, useSlots, computed, ref, openBlock, createBlock, Teleport, createVNode, Transition, unref, withCtx, withDirectives, createElementVNode, mergeProps, withModifiers, normalizeClass, createElementBlock, renderSlot, toDisplayString, createCommentVNode, vShow } from 'vue';\nimport { Close } from '@element-plus/icons-vue';\nimport { ElOverlay } from '../../overlay/index.mjs';\nimport '../../focus-trap/index.mjs';\nimport '../../dialog/index.mjs';\nimport '../../../utils/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { drawerProps, drawerEmits } from './drawer.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useDialog } from '../../dialog/src/use-dialog.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\n\nconst _hoisted_1 = [\"aria-label\", \"aria-labelledby\", \"aria-describedby\"];\nconst _hoisted_2 = [\"id\", \"aria-level\"];\nconst _hoisted_3 = [\"aria-label\"];\nconst _hoisted_4 = [\"id\"];\nconst __default__ = defineComponent({\n  name: \"ElDrawer\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: drawerProps,\n  emits: drawerEmits,\n  setup(__props, { expose }) {\n    const props = __props;\n    const slots = useSlots();\n    useDeprecated({\n      scope: \"el-drawer\",\n      from: \"the title slot\",\n      replacement: \"the header slot\",\n      version: \"3.0.0\",\n      ref: \"https://element-plus.org/en-US/component/drawer.html#slots\"\n    }, computed(() => !!slots.title));\n    useDeprecated({\n      scope: \"el-drawer\",\n      from: \"custom-class\",\n      replacement: \"class\",\n      version: \"2.3.0\",\n      ref: \"https://element-plus.org/en-US/component/drawer.html#attributes\",\n      type: \"Attribute\"\n    }, computed(() => !!props.customClass));\n    const drawerRef = ref();\n    const focusStartRef = ref();\n    const ns = useNamespace(\"drawer\");\n    const { t } = useLocale();\n    const {\n      afterEnter,\n      afterLeave,\n      beforeLeave,\n      visible,\n      rendered,\n      titleId,\n      bodyId,\n      onModalClick,\n      onCloseRequested,\n      handleClose\n    } = useDialog(props, drawerRef);\n    const isHorizontal = computed(() => props.direction === \"rtl\" || props.direction === \"ltr\");\n    const drawerSize = computed(() => addUnit(props.size));\n    expose({\n      handleClose,\n      afterEnter,\n      afterLeave\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Teleport, {\n        to: \"body\",\n        disabled: !_ctx.appendToBody\n      }, [\n        createVNode(Transition, {\n          name: unref(ns).b(\"fade\"),\n          onAfterEnter: unref(afterEnter),\n          onAfterLeave: unref(afterLeave),\n          onBeforeLeave: unref(beforeLeave),\n          persisted: \"\"\n        }, {\n          default: withCtx(() => [\n            withDirectives(createVNode(unref(ElOverlay), {\n              mask: _ctx.modal,\n              \"overlay-class\": _ctx.modalClass,\n              \"z-index\": _ctx.zIndex,\n              onClick: unref(onModalClick)\n            }, {\n              default: withCtx(() => [\n                createVNode(unref(ElFocusTrap), {\n                  loop: \"\",\n                  trapped: unref(visible),\n                  \"focus-trap-el\": drawerRef.value,\n                  \"focus-start-el\": focusStartRef.value,\n                  onReleaseRequested: unref(onCloseRequested)\n                }, {\n                  default: withCtx(() => [\n                    createElementVNode(\"div\", mergeProps({\n                      ref_key: \"drawerRef\",\n                      ref: drawerRef,\n                      \"aria-modal\": \"true\",\n                      \"aria-label\": _ctx.title || void 0,\n                      \"aria-labelledby\": !_ctx.title ? unref(titleId) : void 0,\n                      \"aria-describedby\": unref(bodyId)\n                    }, _ctx.$attrs, {\n                      class: [unref(ns).b(), _ctx.direction, unref(visible) && \"open\", _ctx.customClass],\n                      style: unref(isHorizontal) ? \"width: \" + unref(drawerSize) : \"height: \" + unref(drawerSize),\n                      role: \"dialog\",\n                      onClick: _cache[1] || (_cache[1] = withModifiers(() => {\n                      }, [\"stop\"]))\n                    }), [\n                      createElementVNode(\"span\", {\n                        ref_key: \"focusStartRef\",\n                        ref: focusStartRef,\n                        class: normalizeClass(unref(ns).e(\"sr-focus\")),\n                        tabindex: \"-1\"\n                      }, null, 2),\n                      _ctx.withHeader ? (openBlock(), createElementBlock(\"header\", {\n                        key: 0,\n                        class: normalizeClass(unref(ns).e(\"header\"))\n                      }, [\n                        !_ctx.$slots.title ? renderSlot(_ctx.$slots, \"header\", {\n                          key: 0,\n                          close: unref(handleClose),\n                          titleId: unref(titleId),\n                          titleClass: unref(ns).e(\"title\")\n                        }, () => [\n                          !_ctx.$slots.title ? (openBlock(), createElementBlock(\"span\", {\n                            key: 0,\n                            id: unref(titleId),\n                            role: \"heading\",\n                            \"aria-level\": _ctx.headerAriaLevel,\n                            class: normalizeClass(unref(ns).e(\"title\"))\n                          }, toDisplayString(_ctx.title), 11, _hoisted_2)) : createCommentVNode(\"v-if\", true)\n                        ]) : renderSlot(_ctx.$slots, \"title\", { key: 1 }, () => [\n                          createCommentVNode(\" DEPRECATED SLOT \")\n                        ]),\n                        _ctx.showClose ? (openBlock(), createElementBlock(\"button\", {\n                          key: 2,\n                          \"aria-label\": unref(t)(\"el.drawer.close\"),\n                          class: normalizeClass(unref(ns).e(\"close-btn\")),\n                          type: \"button\",\n                          onClick: _cache[0] || (_cache[0] = (...args) => unref(handleClose) && unref(handleClose)(...args))\n                        }, [\n                          createVNode(unref(ElIcon), {\n                            class: normalizeClass(unref(ns).e(\"close\"))\n                          }, {\n                            default: withCtx(() => [\n                              createVNode(unref(Close))\n                            ]),\n                            _: 1\n                          }, 8, [\"class\"])\n                        ], 10, _hoisted_3)) : createCommentVNode(\"v-if\", true)\n                      ], 2)) : createCommentVNode(\"v-if\", true),\n                      unref(rendered) ? (openBlock(), createElementBlock(\"div\", {\n                        key: 1,\n                        id: unref(bodyId),\n                        class: normalizeClass(unref(ns).e(\"body\"))\n                      }, [\n                        renderSlot(_ctx.$slots, \"default\")\n                      ], 10, _hoisted_4)) : createCommentVNode(\"v-if\", true),\n                      _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", {\n                        key: 2,\n                        class: normalizeClass(unref(ns).e(\"footer\"))\n                      }, [\n                        renderSlot(_ctx.$slots, \"footer\")\n                      ], 2)) : createCommentVNode(\"v-if\", true)\n                    ], 16, _hoisted_1)\n                  ]),\n                  _: 3\n                }, 8, [\"trapped\", \"focus-trap-el\", \"focus-start-el\", \"onReleaseRequested\"])\n              ]),\n              _: 3\n            }, 8, [\"mask\", \"overlay-class\", \"z-index\", \"onClick\"]), [\n              [vShow, unref(visible)]\n            ])\n          ]),\n          _: 3\n        }, 8, [\"name\", \"onAfterEnter\", \"onAfterLeave\", \"onBeforeLeave\"])\n      ], 8, [\"disabled\"]);\n    };\n  }\n});\nvar Drawer = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"drawer.vue\"]]);\n\nexport { Drawer as default };\n//# sourceMappingURL=drawer2.mjs.map\n", "import '../../utils/index.mjs';\nimport Drawer from './src/drawer2.mjs';\nexport { drawerEmits, drawerProps } from './src/drawer.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\n\nconst ElDrawer = withInstall(Drawer);\n\nexport { ElDrawer, ElDrawer as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["drawerProps", "buildProps", "dialogProps", "direction", "type", "String", "default", "values", "size", "Number", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "modalFade", "headerAriaLevel", "drawerEmits", "dialogEmits", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "__default__", "defineComponent", "name", "inheritAttrs", "<PERSON><PERSON><PERSON><PERSON>", "withInstall", "props", "emits", "setup", "__props", "expose", "slots", "useSlots", "useDeprecated", "scope", "from", "replacement", "version", "ref", "computed", "title", "customClass", "drawerRef", "focusStartRef", "ns", "useNamespace", "t", "useLocale", "afterEnter", "afterLeave", "beforeLeave", "visible", "rendered", "titleId", "bodyId", "onModalClick", "onCloseRequested", "handleClose", "useDialog", "isHorizontal", "drawerSize", "addUnit", "_ctx", "_cache", "openBlock", "createBlock", "Teleport", "to", "disabled", "appendToBody", "createVNode", "Transition", "unref", "b", "onAfterEnter", "onAfterLeave", "onBeforeLeave", "persisted", "withCtx", "withDirectives", "ElOverlay", "mask", "modal", "modalClass", "zIndex", "onClick", "ElFocusTrap", "loop", "trapped", "value", "onReleaseRequested", "createElementVNode", "mergeProps", "ref_key", "$attrs", "class", "style", "role", "withModifiers", "normalizeClass", "e", "tabindex", "createElementBlock", "key", "$slots", "renderSlot", "createCommentVNode", "close", "titleClass", "id", "toDisplayString", "showClose", "args", "ElIcon", "Close", "_", "footer", "vShow"], "mappings": "wZAKA,MAAMA,EAAcC,EAAW,IAC1BC,EACHC,UAAW,CACTC,KAAMC,OACNC,QAAS,MACTC,OAAQ,CAAC,MAAO,MAAO,MAAO,QAEhCC,KAAM,CACJJ,KAAM,CAACC,OAAQI,QACfH,QAAS,OAEXI,WAAY,CACVN,KAAMO,QACNL,SAAS,GAEXM,UAAW,CACTR,KAAMO,QACNL,SAAS,GAEXO,gBAAiB,CACfT,KAAMC,OACNC,QAAS,OAGPQ,EAAcC,ECZdC,EAAa,CAAC,aAAc,kBAAmB,oBAC/CC,EAAa,CAAC,KAAM,cACpBC,EAAa,CAAC,cACdC,EAAa,CAAC,MACdC,EAAcC,EAAgB,CAClCC,KAAM,WACNC,cAAc,IClBX,MAACC,EAAWC,IDoBiCJ,EAAA,IAC7CD,EACHM,MAAO1B,EACP2B,MAAOb,EACP,KAAAc,CAAMC,GAASC,OAAEA,IACf,MAAMJ,EAAQG,EACRE,EAAQC,IACAC,EAAA,CACZC,MAAO,YACPC,KAAM,iBACNC,YAAa,kBACbC,QAAS,QACTC,IAAK,8DACJC,GAAS,MAAQR,EAAMS,SACZP,EAAA,CACZC,MAAO,YACPC,KAAM,eACNC,YAAa,QACbC,QAAS,QACTC,IAAK,kEACLlC,KAAM,aACLmC,GAAS,MAAQb,EAAMe,eAC1B,MAAMC,EAAYJ,IACZK,EAAgBL,IAChBM,EAAKC,EAAa,WAClBC,EAAEA,GAAMC,KACRC,WACJA,EAAAC,WACAA,EAAAC,YACAA,EAAAC,QACAA,EAAAC,SACAA,EAAAC,QACAA,EAAAC,OACAA,EAAAC,aACAA,EAAAC,iBACAA,EAAAC,YACAA,GACEC,EAAUhC,EAAOgB,GACfiB,EAAepB,GAAS,IAA0B,QAApBb,EAAMvB,WAA2C,QAApBuB,EAAMvB,YACjEyD,EAAarB,GAAS,IAAMsB,EAAQnC,EAAMlB,QAMzC,OALAsB,EAAA,CACL2B,cACAT,aACAC,eAEK,CAACa,EAAMC,KACLC,IAAaC,EAAYC,EAAU,CACxCC,GAAI,OACJC,UAAWN,EAAKO,cACf,CACDC,EAAYC,EAAY,CACtBjD,KAAMkD,EAAM5B,GAAI6B,EAAE,QAClBC,aAAcF,EAAMxB,GACpB2B,aAAcH,EAAMvB,GACpB2B,cAAeJ,EAAMtB,GACrB2B,UAAW,IACV,CACDvE,QAASwE,GAAQ,IAAM,CACrBC,EAAeT,EAAYE,EAAMQ,GAAY,CAC3CC,KAAMnB,EAAKoB,MACX,gBAAiBpB,EAAKqB,WACtB,UAAWrB,EAAKsB,OAChBC,QAASb,EAAMjB,IACd,CACDjD,QAASwE,GAAQ,IAAM,CACrBR,EAAYE,EAAMc,GAAc,CAC9BC,KAAM,GACNC,QAAShB,EAAMrB,GACf,gBAAiBT,EAAU+C,MAC3B,iBAAkB9C,EAAc8C,MAChCC,mBAAoBlB,EAAMhB,IACzB,CACDlD,QAASwE,GAAQ,IAAM,CACrBa,EAAmB,MAAOC,EAAW,CACnCC,QAAS,YACTvD,IAAKI,EACL,aAAc,OACd,aAAcoB,EAAKtB,YAAS,EAC5B,kBAAoBsB,EAAKtB,WAAyB,EAAjBgC,EAAMnB,GACvC,mBAAoBmB,EAAMlB,IACzBQ,EAAKgC,OAAQ,CACdC,MAAO,CAACvB,EAAM5B,GAAI6B,IAAKX,EAAK3D,UAAWqE,EAAMrB,IAAY,OAAQW,EAAKrB,aACtEuD,MAAOxB,EAAMb,GAAgB,UAAYa,EAAMZ,GAAc,WAAaY,EAAMZ,GAChFqC,KAAM,SACNZ,QAAStB,EAAO,KAAOA,EAAO,GAAKmC,GAAc,QAC9C,CAAC,YACF,CACFP,EAAmB,OAAQ,CACzBE,QAAS,gBACTvD,IAAKK,EACLoD,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,aAClCC,SAAU,MACT,KAAM,GACTvC,EAAKpD,YAAcsD,IAAasC,EAAmB,SAAU,CAC3DC,IAAK,EACLR,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,YACjC,CACAtC,EAAK0C,OAAOhE,MAaRiE,EAAW3C,EAAK0C,OAAQ,QAAS,CAAED,IAAK,IAAK,IAAM,CACtDG,EAAmB,wBAdAD,EAAW3C,EAAK0C,OAAQ,SAAU,CACrDD,IAAK,EACLI,MAAOnC,EAAMf,GACbJ,QAASmB,EAAMnB,GACfuD,WAAYpC,EAAM5B,GAAIwD,EAAE,WACvB,IAAM,CACNtC,EAAK0C,OAAOhE,MAMsCkE,EAAmB,QAAQ,IANxD1C,IAAasC,EAAmB,OAAQ,CAC5DC,IAAK,EACLM,GAAIrC,EAAMnB,GACV4C,KAAM,UACN,aAAcnC,EAAKjD,gBACnBkF,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,WACjCU,EAAgBhD,EAAKtB,OAAQ,GAAIvB,OAItC6C,EAAKiD,WAAa/C,IAAasC,EAAmB,SAAU,CAC1DC,IAAK,EACL,aAAc/B,EAAM1B,EAAN0B,CAAS,mBACvBuB,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,cAClChG,KAAM,SACNiF,QAAStB,EAAO,KAAOA,EAAO,GAAK,IAAIiD,IAASxC,EAAMf,IAAgBe,EAAMf,EAANe,IAAsBwC,KAC3F,CACD1C,EAAYE,EAAMyC,GAAS,CACzBlB,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,WACjC,CACD9F,QAASwE,GAAQ,IAAM,CACrBR,EAAYE,EAAM0C,OAEpBC,EAAG,GACF,EAAG,CAAC,WACN,GAAIjG,IAAewF,EAAmB,QAAQ,IAChD,IAAMA,EAAmB,QAAQ,GACpClC,EAAMpB,IAAaY,IAAasC,EAAmB,MAAO,CACxDC,IAAK,EACLM,GAAIrC,EAAMlB,GACVyC,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,UACjC,CACDK,EAAW3C,EAAK0C,OAAQ,YACvB,GAAIrF,IAAeuF,EAAmB,QAAQ,GACjD5C,EAAK0C,OAAOY,QAAUpD,IAAasC,EAAmB,MAAO,CAC3DC,IAAK,EACLR,MAAOI,EAAe3B,EAAM5B,GAAIwD,EAAE,YACjC,CACDK,EAAW3C,EAAK0C,OAAQ,WACvB,IAAME,EAAmB,QAAQ,IACnC,GAAI1F,MAETmG,EAAG,GACF,EAAG,CAAC,UAAW,gBAAiB,iBAAkB,0BAEvDA,EAAG,GACF,EAAG,CAAC,OAAQ,gBAAiB,UAAW,YAAa,CACtD,CAACE,EAAO7C,EAAMrB,SAGlBgE,EAAG,GACF,EAAG,CAAC,OAAQ,eAAgB,eAAgB,mBAC9C,EAAG,CAAC,aAEV,IAEiD,CAAC,CAAC,SAAU", "x_google_ignoreList": [0, 1, 2]}