{"version": 3, "file": "chunk.27187e73.js", "sources": ["../node_modules/hotkeys-js/dist/hotkeys.esm.js"], "sourcesContent": ["/**! \n * hotkeys-js v3.13.5 \n * A simple micro-library for defining and dispatching keyboard shortcuts. It has no dependencies. \n * \n * Copyright (c) 2024 kenny wong <<EMAIL>> \n * https://github.com/jaywcjlove/hotkeys-js.git \n * \n * @website: https://jaywcjlove.github.io/hotkeys-js\n \n * Licensed under the MIT license \n */\n\nconst isff = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase().indexOf('firefox') > 0 : false;\n\n// 绑定事件\nfunction addEvent(object, event, method, useCapture) {\n  if (object.addEventListener) {\n    object.addEventListener(event, method, useCapture);\n  } else if (object.attachEvent) {\n    object.attachEvent(\"on\".concat(event), method);\n  }\n}\nfunction removeEvent(object, event, method, useCapture) {\n  if (object.removeEventListener) {\n    object.removeEventListener(event, method, useCapture);\n  } else if (object.deachEvent) {\n    object.deachEvent(\"on\".concat(event), method);\n  }\n}\n\n// 修饰键转换成对应的键码\nfunction getMods(modifier, key) {\n  const mods = key.slice(0, key.length - 1);\n  for (let i = 0; i < mods.length; i++) mods[i] = modifier[mods[i].toLowerCase()];\n  return mods;\n}\n\n// 处理传的key字符串转换成数组\nfunction getKeys(key) {\n  if (typeof key !== 'string') key = '';\n  key = key.replace(/\\s/g, ''); // 匹配任何空白字符,包括空格、制表符、换页符等等\n  const keys = key.split(','); // 同时设置多个快捷键，以','分割\n  let index = keys.lastIndexOf('');\n\n  // 快捷键可能包含','，需特殊处理\n  for (; index >= 0;) {\n    keys[index - 1] += ',';\n    keys.splice(index, 1);\n    index = keys.lastIndexOf('');\n  }\n  return keys;\n}\n\n// 比较修饰键的数组\nfunction compareArray(a1, a2) {\n  const arr1 = a1.length >= a2.length ? a1 : a2;\n  const arr2 = a1.length >= a2.length ? a2 : a1;\n  let isIndex = true;\n  for (let i = 0; i < arr1.length; i++) {\n    if (arr2.indexOf(arr1[i]) === -1) isIndex = false;\n  }\n  return isIndex;\n}\n\n// Special Keys\nconst _keyMap = {\n  backspace: 8,\n  '⌫': 8,\n  tab: 9,\n  clear: 12,\n  enter: 13,\n  '↩': 13,\n  return: 13,\n  esc: 27,\n  escape: 27,\n  space: 32,\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  del: 46,\n  delete: 46,\n  ins: 45,\n  insert: 45,\n  home: 36,\n  end: 35,\n  pageup: 33,\n  pagedown: 34,\n  capslock: 20,\n  num_0: 96,\n  num_1: 97,\n  num_2: 98,\n  num_3: 99,\n  num_4: 100,\n  num_5: 101,\n  num_6: 102,\n  num_7: 103,\n  num_8: 104,\n  num_9: 105,\n  num_multiply: 106,\n  num_add: 107,\n  num_enter: 108,\n  num_subtract: 109,\n  num_decimal: 110,\n  num_divide: 111,\n  '⇪': 20,\n  ',': 188,\n  '.': 190,\n  '/': 191,\n  '`': 192,\n  '-': isff ? 173 : 189,\n  '=': isff ? 61 : 187,\n  ';': isff ? 59 : 186,\n  '\\'': 222,\n  '[': 219,\n  ']': 221,\n  '\\\\': 220\n};\n\n// Modifier Keys\nconst _modifier = {\n  // shiftKey\n  '⇧': 16,\n  shift: 16,\n  // altKey\n  '⌥': 18,\n  alt: 18,\n  option: 18,\n  // ctrlKey\n  '⌃': 17,\n  ctrl: 17,\n  control: 17,\n  // metaKey\n  '⌘': 91,\n  cmd: 91,\n  command: 91\n};\nconst modifierMap = {\n  16: 'shiftKey',\n  18: 'altKey',\n  17: 'ctrlKey',\n  91: 'metaKey',\n  shiftKey: 16,\n  ctrlKey: 17,\n  altKey: 18,\n  metaKey: 91\n};\nconst _mods = {\n  16: false,\n  18: false,\n  17: false,\n  91: false\n};\nconst _handlers = {};\n\n// F1~F12 special key\nfor (let k = 1; k < 20; k++) {\n  _keyMap[\"f\".concat(k)] = 111 + k;\n}\n\nlet _downKeys = []; // 记录摁下的绑定键\nlet winListendFocus = null; // window是否已经监听了focus事件\nlet _scope = 'all'; // 默认热键范围\nconst elementEventMap = new Map(); // 已绑定事件的节点记录\n\n// 返回键码\nconst code = x => _keyMap[x.toLowerCase()] || _modifier[x.toLowerCase()] || x.toUpperCase().charCodeAt(0);\nconst getKey = x => Object.keys(_keyMap).find(k => _keyMap[k] === x);\nconst getModifier = x => Object.keys(_modifier).find(k => _modifier[k] === x);\n\n// 设置获取当前范围（默认为'所有'）\nfunction setScope(scope) {\n  _scope = scope || 'all';\n}\n// 获取当前范围\nfunction getScope() {\n  return _scope || 'all';\n}\n// 获取摁下绑定键的键值\nfunction getPressedKeyCodes() {\n  return _downKeys.slice(0);\n}\nfunction getPressedKeyString() {\n  return _downKeys.map(c => getKey(c) || getModifier(c) || String.fromCharCode(c));\n}\nfunction getAllKeyCodes() {\n  const result = [];\n  Object.keys(_handlers).forEach(k => {\n    _handlers[k].forEach(_ref => {\n      let {\n        key,\n        scope,\n        mods,\n        shortcut\n      } = _ref;\n      result.push({\n        scope,\n        shortcut,\n        mods,\n        keys: key.split('+').map(v => code(v))\n      });\n    });\n  });\n  return result;\n}\n\n// 表单控件控件判断 返回 Boolean\n// hotkey is effective only when filter return true\nfunction filter(event) {\n  const target = event.target || event.srcElement;\n  const {\n    tagName\n  } = target;\n  let flag = true;\n  // ignore: isContentEditable === 'true', <input> and <textarea> when readOnly state is false, <select>\n  if (target.isContentEditable || (tagName === 'INPUT' || tagName === 'TEXTAREA' || tagName === 'SELECT') && !target.readOnly) {\n    flag = false;\n  }\n  return flag;\n}\n\n// 判断摁下的键是否为某个键，返回true或者false\nfunction isPressed(keyCode) {\n  if (typeof keyCode === 'string') {\n    keyCode = code(keyCode); // 转换成键码\n  }\n  return _downKeys.indexOf(keyCode) !== -1;\n}\n\n// 循环删除handlers中的所有 scope(范围)\nfunction deleteScope(scope, newScope) {\n  let handlers;\n  let i;\n\n  // 没有指定scope，获取scope\n  if (!scope) scope = getScope();\n  for (const key in _handlers) {\n    if (Object.prototype.hasOwnProperty.call(_handlers, key)) {\n      handlers = _handlers[key];\n      for (i = 0; i < handlers.length;) {\n        if (handlers[i].scope === scope) {\n          const deleteItems = handlers.splice(i, 1);\n          deleteItems.forEach(_ref2 => {\n            let {\n              element\n            } = _ref2;\n            return removeKeyEvent(element);\n          });\n        } else {\n          i++;\n        }\n      }\n    }\n  }\n\n  // 如果scope被删除，将scope重置为all\n  if (getScope() === scope) setScope(newScope || 'all');\n}\n\n// 清除修饰键\nfunction clearModifier(event) {\n  let key = event.keyCode || event.which || event.charCode;\n  const i = _downKeys.indexOf(key);\n\n  // 从列表中清除按压过的键\n  if (i >= 0) {\n    _downKeys.splice(i, 1);\n  }\n  // 特殊处理 cmmand 键，在 cmmand 组合快捷键 keyup 只执行一次的问题\n  if (event.key && event.key.toLowerCase() === 'meta') {\n    _downKeys.splice(0, _downKeys.length);\n  }\n\n  // 修饰键 shiftKey altKey ctrlKey (command||metaKey) 清除\n  if (key === 93 || key === 224) key = 91;\n  if (key in _mods) {\n    _mods[key] = false;\n\n    // 将修饰键重置为false\n    for (const k in _modifier) if (_modifier[k] === key) hotkeys[k] = false;\n  }\n}\nfunction unbind(keysInfo) {\n  // unbind(), unbind all keys\n  if (typeof keysInfo === 'undefined') {\n    Object.keys(_handlers).forEach(key => {\n      Array.isArray(_handlers[key]) && _handlers[key].forEach(info => eachUnbind(info));\n      delete _handlers[key];\n    });\n    removeKeyEvent(null);\n  } else if (Array.isArray(keysInfo)) {\n    // support like : unbind([{key: 'ctrl+a', scope: 's1'}, {key: 'ctrl-a', scope: 's2', splitKey: '-'}])\n    keysInfo.forEach(info => {\n      if (info.key) eachUnbind(info);\n    });\n  } else if (typeof keysInfo === 'object') {\n    // support like unbind({key: 'ctrl+a, ctrl+b', scope:'abc'})\n    if (keysInfo.key) eachUnbind(keysInfo);\n  } else if (typeof keysInfo === 'string') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    // support old method\n    // eslint-disable-line\n    let [scope, method] = args;\n    if (typeof scope === 'function') {\n      method = scope;\n      scope = '';\n    }\n    eachUnbind({\n      key: keysInfo,\n      scope,\n      method,\n      splitKey: '+'\n    });\n  }\n}\n\n// 解除绑定某个范围的快捷键\nconst eachUnbind = _ref3 => {\n  let {\n    key,\n    scope,\n    method,\n    splitKey = '+'\n  } = _ref3;\n  const multipleKeys = getKeys(key);\n  multipleKeys.forEach(originKey => {\n    const unbindKeys = originKey.split(splitKey);\n    const len = unbindKeys.length;\n    const lastKey = unbindKeys[len - 1];\n    const keyCode = lastKey === '*' ? '*' : code(lastKey);\n    if (!_handlers[keyCode]) return;\n    // 判断是否传入范围，没有就获取范围\n    if (!scope) scope = getScope();\n    const mods = len > 1 ? getMods(_modifier, unbindKeys) : [];\n    const unbindElements = [];\n    _handlers[keyCode] = _handlers[keyCode].filter(record => {\n      // 通过函数判断，是否解除绑定，函数相等直接返回\n      const isMatchingMethod = method ? record.method === method : true;\n      const isUnbind = isMatchingMethod && record.scope === scope && compareArray(record.mods, mods);\n      if (isUnbind) unbindElements.push(record.element);\n      return !isUnbind;\n    });\n    unbindElements.forEach(element => removeKeyEvent(element));\n  });\n};\n\n// 对监听对应快捷键的回调函数进行处理\nfunction eventHandler(event, handler, scope, element) {\n  if (handler.element !== element) {\n    return;\n  }\n  let modifiersMatch;\n\n  // 看它是否在当前范围\n  if (handler.scope === scope || handler.scope === 'all') {\n    // 检查是否匹配修饰符（如果有返回true）\n    modifiersMatch = handler.mods.length > 0;\n    for (const y in _mods) {\n      if (Object.prototype.hasOwnProperty.call(_mods, y)) {\n        if (!_mods[y] && handler.mods.indexOf(+y) > -1 || _mods[y] && handler.mods.indexOf(+y) === -1) {\n          modifiersMatch = false;\n        }\n      }\n    }\n\n    // 调用处理程序，如果是修饰键不做处理\n    if (handler.mods.length === 0 && !_mods[16] && !_mods[18] && !_mods[17] && !_mods[91] || modifiersMatch || handler.shortcut === '*') {\n      handler.keys = [];\n      handler.keys = handler.keys.concat(_downKeys);\n      if (handler.method(event, handler) === false) {\n        if (event.preventDefault) event.preventDefault();else event.returnValue = false;\n        if (event.stopPropagation) event.stopPropagation();\n        if (event.cancelBubble) event.cancelBubble = true;\n      }\n    }\n  }\n}\n\n// 处理keydown事件\nfunction dispatch(event, element) {\n  const asterisk = _handlers['*'];\n  let key = event.keyCode || event.which || event.charCode;\n\n  // 表单控件过滤 默认表单控件不触发快捷键\n  if (!hotkeys.filter.call(this, event)) return;\n\n  // Gecko(Firefox)的command键值224，在Webkit(Chrome)中保持一致\n  // Webkit左右 command 键值不一样\n  if (key === 93 || key === 224) key = 91;\n\n  /**\n   * Collect bound keys\n   * If an Input Method Editor is processing key input and the event is keydown, return 229.\n   * https://stackoverflow.com/questions/25043934/is-it-ok-to-ignore-keydown-events-with-keycode-229\n   * http://lists.w3.org/Archives/Public/www-dom/2010JulSep/att-0182/keyCode-spec.html\n   */\n  if (_downKeys.indexOf(key) === -1 && key !== 229) _downKeys.push(key);\n  /**\n   * Jest test cases are required.\n   * ===============================\n   */\n  ['ctrlKey', 'altKey', 'shiftKey', 'metaKey'].forEach(keyName => {\n    const keyNum = modifierMap[keyName];\n    if (event[keyName] && _downKeys.indexOf(keyNum) === -1) {\n      _downKeys.push(keyNum);\n    } else if (!event[keyName] && _downKeys.indexOf(keyNum) > -1) {\n      _downKeys.splice(_downKeys.indexOf(keyNum), 1);\n    } else if (keyName === 'metaKey' && event[keyName] && _downKeys.length === 3) {\n      /**\n       * Fix if Command is pressed:\n       * ===============================\n       */\n      if (!(event.ctrlKey || event.shiftKey || event.altKey)) {\n        _downKeys = _downKeys.slice(_downKeys.indexOf(keyNum));\n      }\n    }\n  });\n  /**\n   * -------------------------------\n   */\n\n  if (key in _mods) {\n    _mods[key] = true;\n\n    // 将特殊字符的key注册到 hotkeys 上\n    for (const k in _modifier) {\n      if (_modifier[k] === key) hotkeys[k] = true;\n    }\n    if (!asterisk) return;\n  }\n\n  // 将 modifierMap 里面的修饰键绑定到 event 中\n  for (const e in _mods) {\n    if (Object.prototype.hasOwnProperty.call(_mods, e)) {\n      _mods[e] = event[modifierMap[e]];\n    }\n  }\n  /**\n   * https://github.com/jaywcjlove/hotkeys/pull/129\n   * This solves the issue in Firefox on Windows where hotkeys corresponding to special characters would not trigger.\n   * An example of this is ctrl+alt+m on a Swedish keyboard which is used to type μ.\n   * Browser support: https://caniuse.com/#feat=keyboardevent-getmodifierstate\n   */\n  if (event.getModifierState && !(event.altKey && !event.ctrlKey) && event.getModifierState('AltGraph')) {\n    if (_downKeys.indexOf(17) === -1) {\n      _downKeys.push(17);\n    }\n    if (_downKeys.indexOf(18) === -1) {\n      _downKeys.push(18);\n    }\n    _mods[17] = true;\n    _mods[18] = true;\n  }\n\n  // 获取范围 默认为 `all`\n  const scope = getScope();\n  // 对任何快捷键都需要做的处理\n  if (asterisk) {\n    for (let i = 0; i < asterisk.length; i++) {\n      if (asterisk[i].scope === scope && (event.type === 'keydown' && asterisk[i].keydown || event.type === 'keyup' && asterisk[i].keyup)) {\n        eventHandler(event, asterisk[i], scope, element);\n      }\n    }\n  }\n  // key 不在 _handlers 中返回\n  if (!(key in _handlers)) return;\n  const handlerKey = _handlers[key];\n  const keyLen = handlerKey.length;\n  for (let i = 0; i < keyLen; i++) {\n    if (event.type === 'keydown' && handlerKey[i].keydown || event.type === 'keyup' && handlerKey[i].keyup) {\n      if (handlerKey[i].key) {\n        const record = handlerKey[i];\n        const {\n          splitKey\n        } = record;\n        const keyShortcut = record.key.split(splitKey);\n        const _downKeysCurrent = []; // 记录当前按键键值\n        for (let a = 0; a < keyShortcut.length; a++) {\n          _downKeysCurrent.push(code(keyShortcut[a]));\n        }\n        if (_downKeysCurrent.sort().join('') === _downKeys.sort().join('')) {\n          // 找到处理内容\n          eventHandler(event, record, scope, element);\n        }\n      }\n    }\n  }\n}\nfunction hotkeys(key, option, method) {\n  _downKeys = [];\n  const keys = getKeys(key); // 需要处理的快捷键列表\n  let mods = [];\n  let scope = 'all'; // scope默认为all，所有范围都有效\n  let element = document; // 快捷键事件绑定节点\n  let i = 0;\n  let keyup = false;\n  let keydown = true;\n  let splitKey = '+';\n  let capture = false;\n  let single = false; // 单个callback\n\n  // 对为设定范围的判断\n  if (method === undefined && typeof option === 'function') {\n    method = option;\n  }\n  if (Object.prototype.toString.call(option) === '[object Object]') {\n    if (option.scope) scope = option.scope; // eslint-disable-line\n    if (option.element) element = option.element; // eslint-disable-line\n    if (option.keyup) keyup = option.keyup; // eslint-disable-line\n    if (option.keydown !== undefined) keydown = option.keydown; // eslint-disable-line\n    if (option.capture !== undefined) capture = option.capture; // eslint-disable-line\n    if (typeof option.splitKey === 'string') splitKey = option.splitKey; // eslint-disable-line\n    if (option.single === true) single = true; // eslint-disable-line\n  }\n  if (typeof option === 'string') scope = option;\n\n  // 如果只允许单个callback，先unbind\n  if (single) unbind(key, scope);\n\n  // 对于每个快捷键进行处理\n  for (; i < keys.length; i++) {\n    key = keys[i].split(splitKey); // 按键列表\n    mods = [];\n\n    // 如果是组合快捷键取得组合快捷键\n    if (key.length > 1) mods = getMods(_modifier, key);\n\n    // 将非修饰键转化为键码\n    key = key[key.length - 1];\n    key = key === '*' ? '*' : code(key); // *表示匹配所有快捷键\n\n    // 判断key是否在_handlers中，不在就赋一个空数组\n    if (!(key in _handlers)) _handlers[key] = [];\n    _handlers[key].push({\n      keyup,\n      keydown,\n      scope,\n      mods,\n      shortcut: keys[i],\n      method,\n      key: keys[i],\n      splitKey,\n      element\n    });\n  }\n  // 在全局document上设置快捷键\n  if (typeof element !== 'undefined' && window) {\n    if (!elementEventMap.has(element)) {\n      const keydownListener = function () {\n        let event = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.event;\n        return dispatch(event, element);\n      };\n      const keyupListenr = function () {\n        let event = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.event;\n        dispatch(event, element);\n        clearModifier(event);\n      };\n      elementEventMap.set(element, {\n        keydownListener,\n        keyupListenr,\n        capture\n      });\n      addEvent(element, 'keydown', keydownListener, capture);\n      addEvent(element, 'keyup', keyupListenr, capture);\n    }\n    if (!winListendFocus) {\n      const listener = () => {\n        _downKeys = [];\n      };\n      winListendFocus = {\n        listener,\n        capture\n      };\n      addEvent(window, 'focus', listener, capture);\n    }\n  }\n}\nfunction trigger(shortcut) {\n  let scope = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'all';\n  Object.keys(_handlers).forEach(key => {\n    const dataList = _handlers[key].filter(item => item.scope === scope && item.shortcut === shortcut);\n    dataList.forEach(data => {\n      if (data && data.method) {\n        data.method();\n      }\n    });\n  });\n}\n\n// 销毁事件,unbind之后判断element上是否还有键盘快捷键，如果没有移除监听\nfunction removeKeyEvent(element) {\n  const values = Object.values(_handlers).flat();\n  const findindex = values.findIndex(_ref4 => {\n    let {\n      element: el\n    } = _ref4;\n    return el === element;\n  });\n  if (findindex < 0) {\n    const {\n      keydownListener,\n      keyupListenr,\n      capture\n    } = elementEventMap.get(element) || {};\n    if (keydownListener && keyupListenr) {\n      removeEvent(element, 'keyup', keyupListenr, capture);\n      removeEvent(element, 'keydown', keydownListener, capture);\n      elementEventMap.delete(element);\n    }\n  }\n  if (values.length <= 0 || elementEventMap.size <= 0) {\n    // 移除所有的元素上的监听\n    const eventKeys = Object.keys(elementEventMap);\n    eventKeys.forEach(el => {\n      const {\n        keydownListener,\n        keyupListenr,\n        capture\n      } = elementEventMap.get(el) || {};\n      if (keydownListener && keyupListenr) {\n        removeEvent(el, 'keyup', keyupListenr, capture);\n        removeEvent(el, 'keydown', keydownListener, capture);\n        elementEventMap.delete(el);\n      }\n    });\n    // 清空 elementEventMap\n    elementEventMap.clear();\n    // 清空 _handlers\n    Object.keys(_handlers).forEach(key => delete _handlers[key]);\n    // 移除window上的focus监听\n    if (winListendFocus) {\n      const {\n        listener,\n        capture\n      } = winListendFocus;\n      removeEvent(window, 'focus', listener, capture);\n      winListendFocus = null;\n    }\n  }\n}\nconst _api = {\n  getPressedKeyString,\n  setScope,\n  getScope,\n  deleteScope,\n  getPressedKeyCodes,\n  getAllKeyCodes,\n  isPressed,\n  filter,\n  trigger,\n  unbind,\n  keyMap: _keyMap,\n  modifier: _modifier,\n  modifierMap\n};\nfor (const a in _api) {\n  if (Object.prototype.hasOwnProperty.call(_api, a)) {\n    hotkeys[a] = _api[a];\n  }\n}\nif (typeof window !== 'undefined') {\n  const _hotkeys = window.hotkeys;\n  hotkeys.noConflict = deep => {\n    if (deep && window.hotkeys === hotkeys) {\n      window.hotkeys = _hotkeys;\n    }\n    return hotkeys;\n  };\n  window.hotkeys = hotkeys;\n}\n\nexport { hotkeys as default };\n"], "names": ["isff", "navigator", "userAgent", "toLowerCase", "indexOf", "addEvent", "object", "event", "method", "useCapture", "addEventListener", "attachEvent", "concat", "removeEvent", "removeEventListener", "deachEvent", "getMods", "modifier", "key", "mods", "slice", "length", "i", "get<PERSON><PERSON><PERSON>", "keys", "replace", "split", "index", "lastIndexOf", "splice", "_keyMap", "backspace", "tab", "clear", "enter", "return", "esc", "escape", "space", "left", "up", "right", "down", "del", "delete", "ins", "insert", "home", "end", "pageup", "pagedown", "capslock", "num_0", "num_1", "num_2", "num_3", "num_4", "num_5", "num_6", "num_7", "num_8", "num_9", "num_multiply", "num_add", "num_enter", "num_subtract", "num_decimal", "num_divide", "_modifier", "shift", "alt", "option", "ctrl", "control", "cmd", "command", "modifierMap", "shift<PERSON>ey", "ctrl<PERSON>ey", "altKey", "metaKey", "_mods", "_handlers", "k", "_downKeys", "winListendFocus", "_scope", "elementEventMap", "Map", "code", "x", "toUpperCase", "charCodeAt", "setScope", "scope", "getScope", "unbind", "keysInfo", "Object", "for<PERSON>ach", "Array", "isArray", "info", "eachUnbind", "removeKeyEvent", "_len", "arguments", "args", "_key", "splitKey", "_ref3", "<PERSON><PERSON><PERSON>", "unbind<PERSON>eys", "len", "last<PERSON>ey", "keyCode", "unbindElements", "filter", "record", "isUnbind", "a1", "a2", "arr1", "arr2", "isIndex", "compareArray", "push", "element", "<PERSON><PERSON><PERSON><PERSON>", "handler", "modifiersMatch", "y", "prototype", "hasOwnProperty", "call", "shortcut", "preventDefault", "returnValue", "stopPropagation", "cancelBubble", "dispatch", "asterisk", "which", "charCode", "hotkeys", "this", "keyName", "keyNum", "e", "getModifierState", "type", "keydown", "keyup", "handler<PERSON><PERSON>", "keyLen", "keyShortcut", "_downKeysCurrent", "a", "sort", "join", "document", "capture", "single", "toString", "window", "has", "keydownListener", "keyupListenr", "clearModifier", "set", "listener", "values", "flat", "findIndex", "_ref4", "el", "get", "size", "_api", "getPressedKeyString", "map", "c", "<PERSON><PERSON><PERSON>", "find", "getModifier", "String", "fromCharCode", "deleteScope", "newScope", "handlers", "_ref2", "getPressedKeyCodes", "getAllKeyCodes", "result", "_ref", "v", "isPressed", "target", "srcElement", "tagName", "flag", "isContentEditable", "readOnly", "trigger", "item", "data", "keyMap", "_hotkeys", "noConflict", "deep"], "mappings": "+uEAYMA,EAA4B,oBAAdC,WAA4BA,UAAUC,UAAUC,cAAcC,QAAQ,WAAa,EAGvG,SAASC,EAASC,EAAQC,EAAOC,EAAQC,GACnCH,EAAOI,iBACFJ,EAAAI,iBAAiBH,EAAOC,EAAQC,GAC9BH,EAAOK,aAChBL,EAAOK,YAAY,KAAKC,OAAOL,GAAQC,EAE3C,CACA,SAASK,EAAYP,EAAQC,EAAOC,EAAQC,GACtCH,EAAOQ,oBACFR,EAAAQ,oBAAoBP,EAAOC,EAAQC,GACjCH,EAAOS,YAChBT,EAAOS,WAAW,KAAKH,OAAOL,GAAQC,EAE1C,CAGA,SAASQ,EAAQC,EAAUC,GACzB,MAAMC,EAAOD,EAAIE,MAAM,EAAGF,EAAIG,OAAS,GACvC,IAAA,IAASC,EAAI,EAAGA,EAAIH,EAAKE,OAAQC,IAAKH,EAAKG,GAAKL,EAASE,EAAKG,GAAGnB,eAC1D,OAAAgB,CACT,CAGA,SAASI,EAAQL,GACI,iBAARA,IAAwBA,EAAA,IAE7B,MAAAM,GADAN,EAAAA,EAAIO,QAAQ,MAAO,KACRC,MAAM,KACnB,IAAAC,EAAQH,EAAKI,YAAY,IAG7B,KAAOD,GAAS,GACTH,EAAAG,EAAQ,IAAM,IACdH,EAAAK,OAAOF,EAAO,GACXA,EAAAH,EAAKI,YAAY,IAEpB,OAAAJ,CACT,CAcA,MAAMM,EAAU,CACdC,UAAW,EACX,IAAK,EACLC,IAAK,EACLC,MAAO,GACPC,MAAO,GACP,IAAK,GACLC,OAAQ,GACRC,IAAK,GACLC,OAAQ,GACRC,MAAO,GACPC,KAAM,GACNC,GAAI,GACJC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,OAAQ,GACRC,IAAK,GACLC,OAAQ,GACRC,KAAM,GACNC,IAAK,GACLC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IACPC,MAAO,IACPC,MAAO,IACPC,MAAO,IACPC,MAAO,IACPC,MAAO,IACPC,aAAc,IACdC,QAAS,IACTC,UAAW,IACXC,aAAc,IACdC,YAAa,IACbC,WAAY,IACZ,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAKnE,EAAO,IAAM,IAClB,IAAKA,EAAO,GAAK,IACjB,IAAKA,EAAO,GAAK,IACjB,IAAM,IACN,IAAK,IACL,IAAK,IACL,KAAM,KAIFoE,EAAY,CAEhB,IAAK,GACLC,MAAO,GAEP,IAAK,GACLC,IAAK,GACLC,OAAQ,GAER,IAAK,GACLC,KAAM,GACNC,QAAS,GAET,IAAK,GACLC,IAAK,GACLC,QAAS,IAELC,EAAc,CAClB,GAAI,WACJ,GAAI,SACJ,GAAI,UACJ,GAAI,UACJC,SAAU,GACVC,QAAS,GACTC,OAAQ,GACRC,QAAS,IAELC,EAAQ,CACZ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,GAEAC,EAAY,CAAA,EAGlB,IAAA,IAASC,EAAI,EAAGA,EAAI,GAAIA,IACtBrD,EAAQ,IAAIlB,OAAOuE,IAAM,IAAMA,EAGjC,IAAIC,EAAY,GACZC,EAAkB,KAClBC,EAAS,MACb,MAAMC,MAAsBC,IAGtBC,EAAYC,GAAA5D,EAAQ4D,EAAEvF,gBAAkBiE,EAAUsB,EAAEvF,gBAAkBuF,EAAEC,cAAcC,WAAW,GAKvG,SAASC,EAASC,GAChBR,EAASQ,GAAS,KACpB,CAEA,SAASC,IACP,OAAOT,GAAU,KACnB,CAyGA,SAASU,EAAOC,GAEV,QAAoB,IAAbA,EACTC,OAAO1E,KAAK0D,GAAWiB,SAAejF,IACpCkF,MAAMC,QAAQnB,EAAUhE,KAASgE,EAAUhE,GAAKiF,SAAQG,GAAQC,EAAWD,YACpEpB,EAAUhE,EAAG,IAEtBsF,EAAe,WACN,GAAAJ,MAAMC,QAAQJ,GAEvBA,EAASE,SAAgBG,IACnBA,EAAKpF,KAAKqF,EAAWD,EAAI,SAEnC,GAAiC,iBAAbL,EAEZA,EAAS/E,KAAKqF,EAAWN,QACjC,GAAiC,iBAAbA,EAAuB,CACvC,IAAA,IAASQ,EAAOC,UAAUrF,OAAQsF,EAAO,IAAIP,MAAMK,EAAO,EAAIA,EAAO,EAAI,GAAIG,EAAO,EAAGA,EAAOH,EAAMG,IAClGD,EAAKC,EAAO,GAAKF,UAAUE,GAIzB,IAACd,EAAOtF,GAAUmG,EACD,mBAAVb,IACAtF,EAAAsF,EACDA,EAAA,IAECS,EAAA,CACTrF,IAAK+E,EACLH,QACAtF,SACAqG,SAAU,KAEb,CACH,CAGA,MAAMN,EAAsBO,IACtB,IAAA5F,IACFA,EAAA4E,MACAA,EAAAtF,OACAA,EAAAqG,SACAA,EAAW,KACTC,EACiBvF,EAAQL,GAChBiF,SAAqBY,IAC1B,MAAAC,EAAaD,EAAUrF,MAAMmF,GAC7BI,EAAMD,EAAW3F,OACjB6F,EAAUF,EAAWC,EAAM,GAC3BE,EAAsB,MAAZD,EAAkB,IAAMzB,EAAKyB,GACzC,IAAChC,EAAUiC,GAAU,OAEpBrB,IAAOA,EAAQC,KACpB,MAAM5E,EAAO8F,EAAM,EAAIjG,EAAQoD,EAAW4C,GAAc,GAClDI,EAAiB,GACvBlC,EAAUiC,GAAWjC,EAAUiC,GAASE,QAAiBC,IAEvD,MACMC,IADmB/G,GAAS8G,EAAO9G,SAAWA,IACf8G,EAAOxB,QAAUA,GA9R5D,SAAsB0B,EAAIC,GACxB,MAAMC,EAAOF,EAAGnG,QAAUoG,EAAGpG,OAASmG,EAAKC,EACrCE,EAAOH,EAAGnG,QAAUoG,EAAGpG,OAASoG,EAAKD,EAC3C,IAAII,GAAU,EACd,IAAA,IAAStG,EAAI,EAAGA,EAAIoG,EAAKrG,OAAQC,KACD,IAA1BqG,EAAKvH,QAAQsH,EAAKpG,MAAsBsG,GAAA,GAEvC,OAAAA,CACT,CAsRqEC,CAAaP,EAAOnG,KAAMA,GAEzF,OADIoG,GAAyBH,EAAAU,KAAKR,EAAOS,UACjCR,CAAA,IAEVH,EAAejB,SAAQ4B,GAAWvB,EAAeuB,IAAQ,GAC1D,EAIH,SAASC,EAAazH,EAAO0H,EAASnC,EAAOiC,GACvC,GAAAE,EAAQF,UAAYA,EACtB,OAEE,IAAAG,EAGJ,GAAID,EAAQnC,QAAUA,GAA2B,QAAlBmC,EAAQnC,MAAiB,CAErCoC,EAAAD,EAAQ9G,KAAKE,OAAS,EACvC,IAAA,MAAW8G,KAAKlD,EACViB,OAAOkC,UAAUC,eAAeC,KAAKrD,EAAOkD,MACzClD,EAAMkD,IAAMF,EAAQ9G,KAAKf,SAAS+H,OAAWlD,EAAMkD,KAAuC,IAAjCF,EAAQ9G,KAAKf,SAAS+H,MACjED,GAAA,IAMK,IAAxBD,EAAQ9G,KAAKE,QAAiB4D,EAAM,KAAQA,EAAM,KAAQA,EAAM,KAAQA,EAAM,OAAOiD,GAAuC,MAArBD,EAAQM,WACjHN,EAAQzG,KAAO,GACfyG,EAAQzG,KAAOyG,EAAQzG,KAAKZ,OAAOwE,IACI,IAAnC6C,EAAQzH,OAAOD,EAAO0H,KACpB1H,EAAMiI,eAAgBjI,EAAMiI,iBAAsBjI,EAAMkI,aAAc,EACtElI,EAAMmI,iBAAiBnI,EAAMmI,kBAC7BnI,EAAMoI,eAAcpI,EAAMoI,cAAe,IAGlD,CACH,CAGA,SAASC,EAASrI,EAAOwH,GACjB,MAAAc,EAAW3D,EAAU,KAC3B,IAAIhE,EAAMX,EAAM4G,SAAW5G,EAAMuI,OAASvI,EAAMwI,SAGhD,IAAKC,EAAQ3B,OAAOiB,KAAKW,KAAM1I,GAAQ,OAqCvC,GAjCY,KAARW,GAAsB,MAARA,IAAmBA,EAAA,SAQjCkE,EAAUhF,QAAQc,IAAuB,MAARA,GAAakE,EAAU0C,KAAK5G,GAKjE,CAAC,UAAW,SAAU,WAAY,WAAWiF,SAAmB+C,IACxD,MAAAC,EAASvE,EAAYsE,GACvB3I,EAAM2I,KAA8C,IAAlC9D,EAAUhF,QAAQ+I,GACtC/D,EAAU0C,KAAKqB,IACL5I,EAAM2I,IAAY9D,EAAUhF,QAAQ+I,IAAc,EAC5D/D,EAAUvD,OAAOuD,EAAUhF,QAAQ+I,GAAS,GACvB,YAAZD,GAAyB3I,EAAM2I,IAAiC,IAArB9D,EAAU/D,SAKxDd,EAAMuE,SAAWvE,EAAMsE,UAAYtE,EAAMwE,SAC7CK,EAAYA,EAAUhE,MAAMgE,EAAUhF,QAAQ+I,KAEjD,IAMCjI,KAAO+D,EAAO,CAChBA,EAAM/D,IAAO,EAGb,IAAA,MAAWiE,KAAKf,EACVA,EAAUe,KAAOjE,IAAK8H,EAAQ7D,IAAK,GAEzC,IAAK0D,EAAU,MAChB,CAGD,IAAA,MAAWO,KAAKnE,EACViB,OAAOkC,UAAUC,eAAeC,KAAKrD,EAAOmE,KAC9CnE,EAAMmE,GAAK7I,EAAMqE,EAAYwE,KAS7B7I,EAAM8I,oBAAsB9I,EAAMwE,QAAWxE,EAAMuE,UAAYvE,EAAM8I,iBAAiB,eACtD,IAA9BjE,EAAUhF,QAAQ,KACpBgF,EAAU0C,KAAK,KAEiB,IAA9B1C,EAAUhF,QAAQ,KACpBgF,EAAU0C,KAAK,IAEjB7C,EAAM,KAAM,EACZA,EAAM,KAAM,GAId,MAAMa,EAAQC,IAEd,GAAI8C,EACF,IAAA,IAASvH,EAAI,EAAGA,EAAIuH,EAASxH,OAAQC,IAC/BuH,EAASvH,GAAGwE,QAAUA,IAAyB,YAAfvF,EAAM+I,MAAsBT,EAASvH,GAAGiI,SAA0B,UAAfhJ,EAAM+I,MAAoBT,EAASvH,GAAGkI,QAC3HxB,EAAazH,EAAOsI,EAASvH,GAAIwE,EAAOiC,GAK9C,KAAM7G,KAAOgE,GAAY,OACnB,MAAAuE,EAAavE,EAAUhE,GACvBwI,EAASD,EAAWpI,OAC1B,IAAA,IAASC,EAAI,EAAGA,EAAIoI,EAAQpI,IAC1B,IAAmB,YAAff,EAAM+I,MAAsBG,EAAWnI,GAAGiI,SAA0B,UAAfhJ,EAAM+I,MAAoBG,EAAWnI,GAAGkI,QAC3FC,EAAWnI,GAAGJ,IAAK,CACf,MAAAoG,EAASmC,EAAWnI,IACpBuF,SACJA,GACES,EACEqC,EAAcrC,EAAOpG,IAAIQ,MAAMmF,GAC/B+C,EAAmB,GACzB,IAAA,IAASC,EAAI,EAAGA,EAAIF,EAAYtI,OAAQwI,IACtCD,EAAiB9B,KAAKrC,EAAKkE,EAAYE,KAErCD,EAAiBE,OAAOC,KAAK,MAAQ3E,EAAU0E,OAAOC,KAAK,KAEhD/B,EAAAzH,EAAO+G,EAAQxB,EAAOiC,EAEtC,CAGP,CACA,SAASiB,EAAQ9H,EAAKqD,EAAQ/D,GAC5B4E,EAAY,GACN,MAAA5D,EAAOD,EAAQL,GACrB,IAAIC,EAAO,GACP2E,EAAQ,MACRiC,EAAUiC,SACV1I,EAAI,EACJkI,GAAQ,EACRD,GAAU,EACV1C,EAAW,IACXoD,GAAU,EACVC,GAAS,EAqBN,SAlBQ,IAAX1J,GAA0C,mBAAX+D,IACxB/D,EAAA+D,GAEoC,oBAA3C2B,OAAOkC,UAAU+B,SAAS7B,KAAK/D,KAC7BA,EAAOuB,QAAOA,EAAQvB,EAAOuB,OAC7BvB,EAAOwD,UAASA,EAAUxD,EAAOwD,SACjCxD,EAAOiF,QAAOA,EAAQjF,EAAOiF,YACV,IAAnBjF,EAAOgF,UAAuBA,EAAUhF,EAAOgF,cAC5B,IAAnBhF,EAAO0F,UAAuBA,EAAU1F,EAAO0F,SACpB,iBAApB1F,EAAOsC,WAAuBA,EAAWtC,EAAOsC,WACrC,IAAlBtC,EAAO2F,SAA0BA,GAAA,IAEjB,iBAAX3F,IAA6BuB,EAAAvB,GAGpC2F,GAAQlE,EAAO9E,EAAK4E,GAGjBxE,EAAIE,EAAKH,OAAQC,IAEtBH,EAAO,IADPD,EAAMM,EAAKF,GAAGI,MAAMmF,IAIZxF,OAAS,IAAUF,EAAAH,EAAQoD,EAAWlD,KAI9CA,EAAc,OADRA,EAAAA,EAAIA,EAAIG,OAAS,IACH,IAAMoE,EAAKvE,MAGlBgE,IAAsBA,EAAAhE,GAAO,IAChCgE,EAAAhE,GAAK4G,KAAK,CAClB0B,QACAD,UACAzD,QACA3E,OACAoH,SAAU/G,EAAKF,GACfd,SACAU,IAAKM,EAAKF,GACVuF,WACAkB,YAIA,QAAmB,IAAZA,GAA2BqC,OAAQ,CAC5C,IAAK7E,EAAgB8E,IAAItC,GAAU,CACjC,MAAMuC,EAAkB,WAEf,OAAA1B,EADKlC,UAAUrF,OAAS,QAAsB,IAAjBqF,UAAU,GAAmBA,UAAU,GAAK0D,OAAO7J,MAChEwH,EAC/B,EACYwC,EAAe,WACf,IAAAhK,EAAQmG,UAAUrF,OAAS,QAAsB,IAAjBqF,UAAU,GAAmBA,UAAU,GAAK0D,OAAO7J,MACvFqI,EAASrI,EAAOwH,GAxSxB,SAAuBxH,GACrB,IAAIW,EAAMX,EAAM4G,SAAW5G,EAAMuI,OAASvI,EAAMwI,SAC1C,MAAAzH,EAAI8D,EAAUhF,QAAQc,GAa5B,GAVII,GAAK,GACG8D,EAAAvD,OAAOP,EAAG,GAGlBf,EAAMW,KAAmC,SAA5BX,EAAMW,IAAIf,eACfiF,EAAAvD,OAAO,EAAGuD,EAAU/D,QAIpB,KAARH,GAAsB,MAARA,IAAmBA,EAAA,IACjCA,KAAO+D,EAAO,CAChBA,EAAM/D,IAAO,EAGb,IAAA,MAAWiE,KAAKf,EAAeA,EAAUe,KAAOjE,IAAK8H,EAAQ7D,IAAK,EACnE,CACH,CAoRQqF,CAAcjK,EACtB,EACMgF,EAAgBkF,IAAI1C,EAAS,CAC3BuC,kBACAC,eACAN,YAEO5J,EAAA0H,EAAS,UAAWuC,EAAiBL,GACrC5J,EAAA0H,EAAS,QAASwC,EAAcN,EAC1C,CACD,IAAK5E,EAAiB,CACpB,MAAMqF,EAAW,KACftF,EAAY,EAAA,EAEIC,EAAA,CAChBqF,WACAT,WAEO5J,EAAA+J,OAAQ,QAASM,EAAUT,EACrC,CACF,CACH,CAcA,SAASzD,EAAeuB,GACtB,MAAM4C,EAASzE,OAAOyE,OAAOzF,GAAW0F,OAOxC,GANkBD,EAAOE,WAAmBC,IACtC,IACF/C,QAASgD,GACPD,EACJ,OAAOC,IAAOhD,CAAA,IAEA,EAAG,CACX,MAAAuC,gBACJA,EAAAC,aACAA,EAAAN,QACAA,GACE1E,EAAgByF,IAAIjD,IAAY,CAAA,EAChCuC,GAAmBC,IACT1J,EAAAkH,EAAS,QAASwC,EAAcN,GAChCpJ,EAAAkH,EAAS,UAAWuC,EAAiBL,GACjD1E,EAAgB3C,OAAOmF,GAE1B,CACD,GAAI4C,EAAOtJ,QAAU,GAAKkE,EAAgB0F,MAAQ,EAAG,CAoBnD,GAlBkB/E,OAAO1E,KAAK+D,GACpBY,SAAc4E,IAChB,MAAAT,gBACJA,EAAAC,aACAA,EAAAN,QACAA,GACE1E,EAAgByF,IAAID,IAAO,CAAA,EAC3BT,GAAmBC,IACT1J,EAAAkK,EAAI,QAASR,EAAcN,GAC3BpJ,EAAAkK,EAAI,UAAWT,EAAiBL,GAC5C1E,EAAgB3C,OAAOmI,GACxB,IAGHxF,EAAgBtD,QAETiE,OAAA1E,KAAK0D,GAAWiB,mBAAsBjB,EAAUhE,KAEnDmE,EAAiB,CACb,MAAAqF,SACJA,EAAAT,QACAA,GACE5E,EACQxE,EAAAuJ,OAAQ,QAASM,EAAUT,GACrB5E,EAAA,IACnB,CACF,CACH,CACA,MAAM6F,EAAO,CACXC,oBA7cF,WACE,OAAO/F,EAAUgG,KAASC,IAAAC,OAhBR5F,EAgBe2F,EAhBfnF,OAAO1E,KAAKM,GAASyJ,MAAUpG,GAAArD,EAAQqD,KAAOO,KAC9C,CAAKA,GAAAQ,OAAO1E,KAAK4C,GAAWmH,MAAUpG,GAAAf,EAAUe,KAAOO,IAelC8F,CAAYH,IAAMI,OAAOC,aAAaL,GAhBhE,IAAK3F,CAgB6D,GACjF,EA4cEG,WACAE,WACA4F,YAhaF,SAAqB7F,EAAO8F,GACtB,IAAAC,EACAvK,EAGCwE,IAAOA,EAAQC,KACpB,IAAA,MAAW7E,KAAOgE,EAChB,GAAIgB,OAAOkC,UAAUC,eAAeC,KAAKpD,EAAWhE,GAElD,IADA2K,EAAW3G,EAAUhE,GAChBI,EAAI,EAAGA,EAAIuK,EAASxK,QACvB,GAAIwK,EAASvK,GAAGwE,QAAUA,EAAO,CACX+F,EAAShK,OAAOP,EAAG,GAC3B6E,SAAiB2F,IACvB,IAAA/D,QACFA,GACE+D,EACJ,OAAOtF,EAAeuB,EAAO,GAEzC,MACUzG,IAOJyE,MAAeD,GAAOD,EAAS+F,GAAY,MACjD,EAsYEG,mBApdF,WACS,OAAA3G,EAAUhE,MAAM,EACzB,EAmdE4K,eA/cF,WACE,MAAMC,EAAS,GAiBR,OAhBP/F,OAAO1E,KAAK0D,GAAWiB,SAAahB,IACxBD,EAAAC,GAAGgB,SAAgB+F,IACvB,IAAAhL,IACFA,EAAA4E,MACAA,EAAA3E,KACAA,EAAAoH,SACAA,GACE2D,EACJD,EAAOnE,KAAK,CACVhC,QACAyC,WACApH,OACAK,KAAMN,EAAIQ,MAAM,KAAK0J,KAAIe,GAAK1G,EAAK0G,MACpC,GACF,IAEIF,CACT,EA6bEG,UA3aF,SAAmBjF,GAIV,MAHgB,iBAAZA,IACTA,EAAU1B,EAAK0B,KAEqB,IAA/B/B,EAAUhF,QAAQ+G,EAC3B,EAuaEE,OA1bF,SAAgB9G,GACR,MAAA8L,EAAS9L,EAAM8L,QAAU9L,EAAM+L,YAC/BC,QACJA,GACEF,EACJ,IAAIG,GAAO,EAKJ,OAHHH,EAAOI,oBAAkC,UAAZF,GAAmC,aAAZA,GAAsC,WAAZA,GAA0BF,EAAOK,YAC1GF,GAAA,GAEFA,CACT,EAgbEG,QAxEF,SAAiBpE,GACX,IAAAzC,EAAQY,UAAUrF,OAAS,QAAsB,IAAjBqF,UAAU,GAAmBA,UAAU,GAAK,MAChFR,OAAO1E,KAAK0D,GAAWiB,SAAejF,IACnBgE,EAAUhE,GAAKmG,QAAeuF,GAAAA,EAAK9G,QAAUA,GAAS8G,EAAKrE,WAAaA,IAChFpC,SAAgB0G,IACnBA,GAAQA,EAAKrM,QACfqM,EAAKrM,QACN,GACF,GAEL,EA+DEwF,SACA8G,OAAQhL,EACRb,SAAUmD,EACVQ,eAEF,IAAA,MAAWiF,KAAKqB,EACVhF,OAAOkC,UAAUC,eAAeC,KAAK4C,EAAMrB,KACrCb,EAAAa,GAAKqB,EAAKrB,IAGtB,GAAsB,oBAAXO,OAAwB,CACjC,MAAM2C,EAAW3C,OAAOpB,QACxBA,EAAQgE,WAAqBC,IACvBA,GAAQ7C,OAAOpB,UAAYA,IAC7BoB,OAAOpB,QAAU+D,GAEZ/D,GAEToB,OAAOpB,QAAUA,CACnB", "x_google_ignoreList": [0]}