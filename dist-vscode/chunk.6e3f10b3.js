import{d as e,U as a,z as s,r as t,o as l,A as r,i as o,B as i,I as d,u as n,b as f,aG as c,q as u,n as p,c as m,O as v,e as b,h,a6 as y,T as k,D as w}from"./index.05904f40.js";import{h as C,H as A,m as E,T as L,v as x,E as g,an as R,o as $,x as _}from"./chunk.8df321e8.js";import{a as S}from"./chunk.505381c5.js";import{u as z,v as B,w as j}from"./chunk.4e1e4273.js";import{e as I}from"./chunk.db8898e3.js";const T=C({...z,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0},headerAriaLevel:{type:String,default:"2"}}),q=B,D=["aria-label","aria-labelledby","aria-describedby"],H=["id","aria-level"],U=["aria-label"],O=["id"],F=e({name:"ElDrawer",inheritAttrs:!1});const G=_($(e({...F,props:T,emits:q,setup(e,{expose:C}){const $=e,_=a();A({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},s((()=>!!_.title))),A({scope:"el-drawer",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/drawer.html#attributes",type:"Attribute"},s((()=>!!$.customClass)));const z=t(),B=t(),T=E("drawer"),{t:q}=L(),{afterEnter:F,afterLeave:G,beforeLeave:M,visible:N,rendered:P,titleId:J,bodyId:K,onModalClick:Q,onCloseRequested:V,handleClose:W}=j($,z),X=s((()=>"rtl"===$.direction||"ltr"===$.direction)),Y=s((()=>x($.size)));return C({handleClose:W,afterEnter:F,afterLeave:G}),(e,a)=>(l(),r(w,{to:"body",disabled:!e.appendToBody},[o(k,{name:n(T).b("fade"),onAfterEnter:n(F),onAfterLeave:n(G),onBeforeLeave:n(M),persisted:""},{default:i((()=>[d(o(n(S),{mask:e.modal,"overlay-class":e.modalClass,"z-index":e.zIndex,onClick:n(Q)},{default:i((()=>[o(n(I),{loop:"",trapped:n(N),"focus-trap-el":z.value,"focus-start-el":B.value,onReleaseRequested:n(V)},{default:i((()=>[f("div",c({ref_key:"drawerRef",ref:z,"aria-modal":"true","aria-label":e.title||void 0,"aria-labelledby":e.title?void 0:n(J),"aria-describedby":n(K)},e.$attrs,{class:[n(T).b(),e.direction,n(N)&&"open",e.customClass],style:n(X)?"width: "+n(Y):"height: "+n(Y),role:"dialog",onClick:a[1]||(a[1]=u((()=>{}),["stop"]))}),[f("span",{ref_key:"focusStartRef",ref:B,class:p(n(T).e("sr-focus")),tabindex:"-1"},null,2),e.withHeader?(l(),m("header",{key:0,class:p(n(T).e("header"))},[e.$slots.title?v(e.$slots,"title",{key:1},(()=>[h(" DEPRECATED SLOT ")])):v(e.$slots,"header",{key:0,close:n(W),titleId:n(J),titleClass:n(T).e("title")},(()=>[e.$slots.title?h("v-if",!0):(l(),m("span",{key:0,id:n(J),role:"heading","aria-level":e.headerAriaLevel,class:p(n(T).e("title"))},b(e.title),11,H))])),e.showClose?(l(),m("button",{key:2,"aria-label":n(q)("el.drawer.close"),class:p(n(T).e("close-btn")),type:"button",onClick:a[0]||(a[0]=(...e)=>n(W)&&n(W)(...e))},[o(n(g),{class:p(n(T).e("close"))},{default:i((()=>[o(n(R))])),_:1},8,["class"])],10,U)):h("v-if",!0)],2)):h("v-if",!0),n(P)?(l(),m("div",{key:1,id:n(K),class:p(n(T).e("body"))},[v(e.$slots,"default")],10,O)):h("v-if",!0),e.$slots.footer?(l(),m("div",{key:2,class:p(n(T).e("footer"))},[v(e.$slots,"footer")],2)):h("v-if",!0)],16,D)])),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])])),_:3},8,["mask","overlay-class","z-index","onClick"]),[[y,n(N)]])])),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}}),[["__file","drawer.vue"]]));export{G as E};
//# sourceMappingURL=chunk.6e3f10b3.js.map
