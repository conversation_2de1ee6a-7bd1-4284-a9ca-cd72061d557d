import{d as e,r as t,z as o,g as s,o as n,A as a,B as l,I as i,b as r,n as u,u as c,f as p,V as f,h as d,e as m,O as v,c as g,F as y,a6 as h,q as b,i as C,T as x,aD as k,K as I,$ as S}from"./index.05904f40.js";import{h as T,A as E,k as _,ae as L,af as B,Y as H,E as M,o as z,ag as N,ah as $,a8 as j,O as w,V as A,ai as O}from"./chunk.8df321e8.js";const q=["success","info","warning","error"],F=T({customClass:{type:String,default:""},dangerouslyUseHTMLString:{type:Boolean,default:!1},duration:{type:Number,default:4500},icon:{type:E},id:{type:String,default:""},message:{type:_([String,Object]),default:""},offset:{type:Number,default:0},onClick:{type:_(Function),default:()=>{}},onClose:{type:_(Function),required:!0},position:{type:String,values:["top-right","top-left","bottom-right","bottom-left"],default:"top-right"},showClose:{type:Boolean,default:!0},title:{type:String,default:""},type:{type:String,values:[...q,""],default:""},zIndex:Number}),D=["id"],U=["textContent"],V={key:0},W=["innerHTML"],Z=e({name:"ElNotification"});var K=z(e({...Z,props:F,emits:{destroy:()=>!0},setup(e,{expose:k}){const I=e,{ns:S,zIndex:T}=L("notification"),{nextZIndex:E,currentZIndex:_}=T,{Close:z}=N,w=t(!1);let A;const O=o((()=>{const e=I.type;return e&&B[I.type]?S.m(e):""})),q=o((()=>I.type&&B[I.type]||I.icon)),F=o((()=>I.position.endsWith("right")?"right":"left")),Z=o((()=>I.position.startsWith("top")?"top":"bottom")),K=o((()=>{var e;return{[Z.value]:`${I.offset}px`,zIndex:null!=(e=I.zIndex)?e:_.value}}));function Y(){I.duration>0&&({stop:A}=$((()=>{w.value&&J()}),I.duration))}function G(){null==A||A()}function J(){w.value=!1}return s((()=>{Y(),E(),w.value=!0})),H(document,"keydown",(function({code:e}){e===j.delete||e===j.backspace?G():e===j.esc?w.value&&J():Y()})),k({visible:w,close:J}),(e,t)=>(n(),a(x,{name:c(S).b("fade"),onBeforeLeave:e.onClose,onAfterLeave:t[1]||(t[1]=t=>e.$emit("destroy")),persisted:""},{default:l((()=>[i(r("div",{id:e.id,class:u([c(S).b(),e.customClass,c(F)]),style:p(c(K)),role:"alert",onMouseenter:G,onMouseleave:Y,onClick:t[0]||(t[0]=(...t)=>e.onClick&&e.onClick(...t))},[c(q)?(n(),a(c(M),{key:0,class:u([c(S).e("icon"),c(O)])},{default:l((()=>[(n(),a(f(c(q))))])),_:1},8,["class"])):d("v-if",!0),r("div",{class:u(c(S).e("group"))},[r("h2",{class:u(c(S).e("title")),textContent:m(e.title)},null,10,U),i(r("div",{class:u(c(S).e("content")),style:p(e.title?void 0:{margin:0})},[v(e.$slots,"default",{},(()=>[e.dangerouslyUseHTMLString?(n(),g(y,{key:1},[d(" Caution here, message could've been compromised, never use user's input as message "),r("p",{innerHTML:e.message},null,8,W)],2112)):(n(),g("p",V,m(e.message),1))]))],6),[[h,e.message]]),e.showClose?(n(),a(c(M),{key:0,class:u(c(S).e("closeBtn")),onClick:b(J,["stop"])},{default:l((()=>[C(c(z))])),_:1},8,["class","onClick"])):d("v-if",!0)],2)],46,D),[[h,w.value]])])),_:3},8,["name","onBeforeLeave"]))}}),[["__file","notification.vue"]]);const Y={"top-left":[],"top-right":[],"bottom-left":[],"bottom-right":[]};let G=1;const J=function(e={},t=null){if(!w)return{close:()=>{}};("string"==typeof e||k(e))&&(e={message:e});const o=e.position||"top-right";let s=e.offset||0;Y[o].forEach((({vm:e})=>{var t;s+=((null==(t=e.el)?void 0:t.offsetHeight)||0)+16})),s+=16;const n="notification_"+G++,a=e.onClose,l={...e,offset:s,id:n,onClose:()=>{!function(e,t,o){const s=Y[t],n=s.findIndex((({vm:t})=>{var o;return(null==(o=t.component)?void 0:o.props.id)===e}));if(-1===n)return;const{vm:a}=s[n];if(!a)return;null==o||o(a);const l=a.el.offsetHeight,i=t.split("-")[0];s.splice(n,1);const r=s.length;if(r<1)return;for(let u=n;u<r;u++){const{el:e,component:t}=s[u].vm,o=Number.parseInt(e.style[i],10)-l-16;t.props.offset=o}}(n,o,a)}};let i=document.body;A(e.appendTo)?i=e.appendTo:I(e.appendTo)&&(i=document.querySelector(e.appendTo)),A(i)||(i=document.body);const r=document.createElement("div"),u=C(K,l,k(l.message)?{default:()=>l.message}:null);return u.appContext=null!=t?t:J._context,u.props.onDestroy=()=>{S(null,r)},S(u,r),Y[o].push({vm:u}),i.appendChild(r.firstElementChild),{close:()=>{u.component.exposed.visible.value=!1}}};q.forEach((e=>{J[e]=(t={})=>(("string"==typeof t||k(t))&&(t={message:t}),J({...t,type:e}))})),J.closeAll=function(){for(const e of Object.values(Y))e.forEach((({vm:e})=>{e.component.exposed.visible.value=!1}))},J._context=null;const P=O(J,"$notify");export{P as E};
//# sourceMappingURL=chunk.9a591dc8.js.map
