{"version": 3, "file": "chunk.aea9579b.js", "sources": ["../node_modules/element-plus/es/components/breadcrumb/src/constants.mjs", "../node_modules/element-plus/es/components/breadcrumb/src/breadcrumb.mjs", "../node_modules/element-plus/es/components/breadcrumb/src/breadcrumb2.mjs", "../node_modules/element-plus/es/components/breadcrumb/src/breadcrumb-item.mjs", "../node_modules/element-plus/es/components/breadcrumb/src/breadcrumb-item2.mjs", "../node_modules/element-plus/es/components/breadcrumb/index.mjs", "../node_modules/element-plus/es/components/timeline/src/timeline.mjs", "../node_modules/element-plus/es/components/timeline/src/timeline-item.mjs", "../node_modules/element-plus/es/components/timeline/src/timeline-item2.mjs", "../node_modules/element-plus/es/components/timeline/index.mjs", "../src/views/designCooperate/sketch/scale.vue", "../src/views/designCooperate/sketch/model/i18n/zh-cn.ts", "../src/views/designCooperate/sketch/model/index.ts", "../src/views/designCooperate/sketch/unit.vue", "../src/views/designCooperate/sketch/utils/helper.ts", "../src/views/designCooperate/sketch/utils/window.ts", "../src/views/designCooperate/sketch/utils/document.ts", "../src/views/designCooperate/sketch/slices.vue", "../src/views/designCooperate/sketch/colors.vue", "../src/views/designCooperate/sketch/artboards.vue", "../src/views/designCooperate/sketch/rulers.vue", "../src/views/designCooperate/sketch/model/interfaces.ts", "../src/views/designCooperate/sketch/layers.vue", "../src/views/designCooperate/sketch/notes.vue", "../src/views/designCooperate/sketch/screen.vue", "../src/views/designCooperate/sketch/history.vue", "../src/views/designCooperate/sketch/components/properties.vue", "../src/views/designCooperate/sketch/components/colorItem.vue", "../node_modules/codemirror/mode/clike/clike.js", "../node_modules/codemirror/addon/display/autorefresh.js", "../src/views/designCooperate/sketch/components/codemirror.vue", "../src/views/designCooperate/sketch/components/codeTemplate.vue", "../src/views/designCooperate/sketch/components/exportable.vue", "../src/views/designCooperate/sketch/inspector.vue", "../src/views/designCooperate/detail.vue"], "sourcesContent": ["const breadcrumbKey = Symbol(\"breadcrumbKey\");\n\nexport { breadcrumbKey };\n//# sourceMappingURL=constants.mjs.map\n", "import '../../../utils/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\n\nconst breadcrumbProps = buildProps({\n  separator: {\n    type: String,\n    default: \"/\"\n  },\n  separatorIcon: {\n    type: iconPropType\n  }\n});\n\nexport { breadcrumbProps };\n//# sourceMappingURL=breadcrumb.mjs.map\n", "import { defineComponent, ref, provide, onMounted, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { breadcrumbKey } from './constants.mjs';\nimport { breadcrumbProps } from './breadcrumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst __default__ = defineComponent({\n  name: \"ElBreadcrumb\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: breadcrumbProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"breadcrumb\");\n    const breadcrumb = ref();\n    provide(breadcrumbKey, props);\n    onMounted(() => {\n      const items = breadcrumb.value.querySelectorAll(`.${ns.e(\"item\")}`);\n      if (items.length) {\n        items[items.length - 1].setAttribute(\"aria-current\", \"page\");\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"breadcrumb\",\n        ref: breadcrumb,\n        class: normalizeClass(unref(ns).b()),\n        \"aria-label\": \"Breadcrumb\",\n        role: \"navigation\"\n      }, [\n        renderSlot(_ctx.$slots, \"default\")\n      ], 2);\n    };\n  }\n});\nvar Breadcrumb = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"breadcrumb.vue\"]]);\n\nexport { Breadcrumb as default };\n//# sourceMappingURL=breadcrumb2.mjs.map\n", "import '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\n\nconst breadcrumbItemProps = buildProps({\n  to: {\n    type: definePropType([String, Object]),\n    default: \"\"\n  },\n  replace: {\n    type: Boolean,\n    default: false\n  }\n});\n\nexport { breadcrumbItemProps };\n//# sourceMappingURL=breadcrumb-item.mjs.map\n", "import { defineComponent, getCurrentInstance, inject, ref, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createBlock, withCtx, resolveDynamicComponent, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { breadcrumbKey } from './constants.mjs';\nimport { breadcrumbItemProps } from './breadcrumb-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst __default__ = defineComponent({\n  name: \"ElBreadcrumbItem\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: breadcrumbItemProps,\n  setup(__props) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const breadcrumbContext = inject(breadcrumbKey, void 0);\n    const ns = useNamespace(\"breadcrumb\");\n    const router = instance.appContext.config.globalProperties.$router;\n    const link = ref();\n    const onClick = () => {\n      if (!props.to || !router)\n        return;\n      props.replace ? router.replace(props.to) : router.push(props.to);\n    };\n    return (_ctx, _cache) => {\n      var _a, _b;\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"item\"))\n      }, [\n        createElementVNode(\"span\", {\n          ref_key: \"link\",\n          ref: link,\n          class: normalizeClass([unref(ns).e(\"inner\"), unref(ns).is(\"link\", !!_ctx.to)]),\n          role: \"link\",\n          onClick\n        }, [\n          renderSlot(_ctx.$slots, \"default\")\n        ], 2),\n        ((_a = unref(breadcrumbContext)) == null ? void 0 : _a.separatorIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"separator\"))\n        }, {\n          default: withCtx(() => [\n            (openBlock(), createBlock(resolveDynamicComponent(unref(breadcrumbContext).separatorIcon)))\n          ]),\n          _: 1\n        }, 8, [\"class\"])) : (openBlock(), createElementBlock(\"span\", {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"separator\")),\n          role: \"presentation\"\n        }, toDisplayString((_b = unref(breadcrumbContext)) == null ? void 0 : _b.separator), 3))\n      ], 2);\n    };\n  }\n});\nvar BreadcrumbItem = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"breadcrumb-item.vue\"]]);\n\nexport { BreadcrumbItem as default };\n//# sourceMappingURL=breadcrumb-item2.mjs.map\n", "import '../../utils/index.mjs';\nimport Breadcrumb from './src/breadcrumb2.mjs';\nimport BreadcrumbItem from './src/breadcrumb-item2.mjs';\nexport { breadcrumbProps } from './src/breadcrumb.mjs';\nexport { breadcrumbItemProps } from './src/breadcrumb-item.mjs';\nexport { breadcrumbKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\n\nconst ElBreadcrumb = withInstall(Breadcrumb, {\n  BreadcrumbItem\n});\nconst ElBreadcrumbItem = withNoopInstall(BreadcrumbItem);\n\nexport { ElBreadcrumb, ElBreadcrumbItem, ElBreadcrumb as default };\n//# sourceMappingURL=index.mjs.map\n", "import { defineComponent, provide, h, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst Timeline = defineComponent({\n  name: \"ElTimeline\",\n  setup(_, { slots }) {\n    const ns = useNamespace(\"timeline\");\n    provide(\"timeline\", slots);\n    return () => {\n      return h(\"ul\", { class: [ns.b()] }, [renderSlot(slots, \"default\")]);\n    };\n  }\n});\n\nexport { Timeline as default };\n//# sourceMappingURL=timeline.mjs.map\n", "import '../../../utils/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\n\nconst timelineItemProps = buildProps({\n  timestamp: {\n    type: String,\n    default: \"\"\n  },\n  hideTimestamp: {\n    type: Boolean,\n    default: false\n  },\n  center: {\n    type: Boolean,\n    default: false\n  },\n  placement: {\n    type: String,\n    values: [\"top\", \"bottom\"],\n    default: \"bottom\"\n  },\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"danger\", \"info\"],\n    default: \"\"\n  },\n  color: {\n    type: String,\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: [\"normal\", \"large\"],\n    default: \"normal\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  hollow: {\n    type: Boolean,\n    default: false\n  }\n});\n\nexport { timelineItemProps };\n//# sourceMappingURL=timeline-item.mjs.map\n", "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, renderSlot, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { timelineItemProps } from './timeline-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst __default__ = defineComponent({\n  name: \"ElTimelineItem\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: timelineItemProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"timeline-item\");\n    const defaultNodeKls = computed(() => [\n      ns.e(\"node\"),\n      ns.em(\"node\", props.size || \"\"),\n      ns.em(\"node\", props.type || \"\"),\n      ns.is(\"hollow\", props.hollow)\n    ]);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"li\", {\n        class: normalizeClass([unref(ns).b(), { [unref(ns).e(\"center\")]: _ctx.center }])\n      }, [\n        createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"tail\"))\n        }, null, 2),\n        !_ctx.$slots.dot ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(defaultNodeKls)),\n          style: normalizeStyle({\n            backgroundColor: _ctx.color\n          })\n        }, [\n          _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass(unref(ns).e(\"icon\"))\n          }, {\n            default: withCtx(() => [\n              (openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))\n            ]),\n            _: 1\n          }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)\n        ], 6)) : createCommentVNode(\"v-if\", true),\n        _ctx.$slots.dot ? (openBlock(), createElementBlock(\"div\", {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"dot\"))\n        }, [\n          renderSlot(_ctx.$slots, \"dot\")\n        ], 2)) : createCommentVNode(\"v-if\", true),\n        createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"wrapper\"))\n        }, [\n          !_ctx.hideTimestamp && _ctx.placement === \"top\" ? (openBlock(), createElementBlock(\"div\", {\n            key: 0,\n            class: normalizeClass([unref(ns).e(\"timestamp\"), unref(ns).is(\"top\")])\n          }, toDisplayString(_ctx.timestamp), 3)) : createCommentVNode(\"v-if\", true),\n          createElementVNode(\"div\", {\n            class: normalizeClass(unref(ns).e(\"content\"))\n          }, [\n            renderSlot(_ctx.$slots, \"default\")\n          ], 2),\n          !_ctx.hideTimestamp && _ctx.placement === \"bottom\" ? (openBlock(), createElementBlock(\"div\", {\n            key: 1,\n            class: normalizeClass([unref(ns).e(\"timestamp\"), unref(ns).is(\"bottom\")])\n          }, toDisplayString(_ctx.timestamp), 3)) : createCommentVNode(\"v-if\", true)\n        ], 2)\n      ], 2);\n    };\n  }\n});\nvar TimelineItem = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"timeline-item.vue\"]]);\n\nexport { TimelineItem as default };\n//# sourceMappingURL=timeline-item2.mjs.map\n", "import '../../utils/index.mjs';\nimport Timeline from './src/timeline.mjs';\nimport TimelineItem from './src/timeline-item2.mjs';\nexport { timelineItemProps } from './src/timeline-item.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\n\nconst ElTimeline = withInstall(Timeline, {\n  TimelineItem\n});\nconst ElTimelineItem = withNoopInstall(TimelineItem);\n\nexport { ElTimeline, ElTimelineItem, ElTimeline as default };\n//# sourceMappingURL=index.mjs.map\n", "<template>\n  <div class=\"scale-div\">\n    <el-button type=\"info\" text :disabled=\"num <= 0.25\" @click=\"handleScaleChange(-0.25)\">\n      <i class=\"iconfont sy-gicon-jian\"></i>\n    </el-button>\n    <el-dropdown @command=\"handleCommand\" class=\"scale-num\" popper-class=\"scale-num-popper\" trigger=\"click\">\n      <span class=\"el-dropdown-link\"> {{ num * 100 }}% </span>\n      <template #dropdown>\n        <el-dropdown-menu>\n          <el-dropdown-item command=\"1\">\n            放大\n            <div class=\"dropdown-item-right\"><i class=\"iconfont sy-gicon-vuesax-linear-command-square\"></i>&nbsp;&nbsp;+&nbsp;&nbsp;<i class=\"iconfont sy-gicon-plus_app\"></i></div>\n          </el-dropdown-item>\n          <el-dropdown-item command=\"2\">\n            缩小\n            <div class=\"dropdown-item-right\"><i class=\"iconfont sy-gicon-vuesax-linear-command-square\"></i>&nbsp;&nbsp;+&nbsp;&nbsp;<i class=\"iconfont sy-gicon-jian1\"></i></div>\n          </el-dropdown-item>\n          <el-dropdown-item command=\"3\" disabled>全览</el-dropdown-item>\n          <el-dropdown-item command=\"4\" divided>200%</el-dropdown-item>\n          <el-dropdown-item command=\"5\">150%</el-dropdown-item>\n          <el-dropdown-item command=\"6\">100%</el-dropdown-item>\n          <el-dropdown-item command=\"7\">75%</el-dropdown-item>\n          <el-dropdown-item command=\"8\">50%</el-dropdown-item>\n          <el-dropdown-item command=\"8\">25%</el-dropdown-item>\n          <el-dropdown-item disabled divided>适应屏幕</el-dropdown-item>\n          <el-dropdown-item disabled>实际大小</el-dropdown-item>\n        </el-dropdown-menu>\n      </template>\n    </el-dropdown>\n    <el-button type=\"info\" text :disabled=\"num >= 4\" @click=\"handleScaleChange(0.25)\">\n      <i class=\"iconfont sy-gicon-plus-border\"></i>\n    </el-button>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, onMounted } from \"vue\";\nimport hotkeys from \"hotkeys-js\";\nconst props = defineProps<{\n  num: number;\n}>();\nconst emit = defineEmits([\"change\"]);\nonMounted(() => {\n  hotkeys(\"command-=\", { splitKey: \"-\" }, (event) => {\n    event.preventDefault();\n    handleScaleChange(0.25);\n  });\n  hotkeys(\"command+-\", (event) => {\n    event.preventDefault();\n    handleScaleChange(-0.25);\n  });\n});\nconst handleCommand = (command: string) => {\n  switch (command) {\n    case \"1\":\n      handleScaleChange(0.25);\n      break;\n    case \"2\":\n      handleScaleChange(-0.25);\n      break;\n    case \"3\":\n      break;\n    case \"4\":\n      emit(\"change\", 2);\n      break;\n    case \"5\":\n      emit(\"change\", 1.5);\n      break;\n    case \"6\":\n      emit(\"change\", 1);\n      break;\n    case \"7\":\n      emit(\"change\", 0.75);\n      break;\n    case \"8\":\n      emit(\"change\", 0.5);\n      break;\n    case \"9\":\n      emit(\"change\", 0.25);\n      break;\n    default:\n      break;\n  }\n};\nconst handleScaleChange = (num: number) => {\n  const sum = props.num + num;\n  if (sum < 0.25 || sum > 4) {\n    return;\n  }\n  emit(\"change\", sum);\n};\n</script>\n<style lang=\"less\">\n.scale-num-popper {\n  width: 170px;\n  .el-dropdown-menu__item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    font-size: 12px;\n  }\n}\n</style>\n<style lang=\"less\" scoped>\n.dropdown-item-right {\n  display: flex;\n}\n.scale-div {\n  width: 170px;\n  display: flex;\n  justify-content: center;\n  height: 100%;\n  align-items: center;\n  .el-button {\n    padding: 5px 10px;\n    .iconfont {\n      font-size: 24px;\n    }\n  }\n  .scale-num {\n    font-size: 14px;\n    font-weight: 500;\n    color: #303233;\n    text-align: center;\n    padding: 0 15px;\n  }\n}\n</style>\n", "import { ObjectAny } from \"@/types\";\n\nexport const data: ObjectAny = {\n  Coordinate: \"坐标\",\n  Overlay: \"高亮区域\",\n  \"Top Width\": \"宽度-顶部\",\n  \"Middle Width\": \"宽度-居中\",\n  \"Bottom Width\": \"宽度-底部\",\n  \"Left Height\": \"高度-左侧\",\n  \"Center Height\": \"高度-居中\",\n  \"Right Height\": \"高度-右侧\",\n  \"Vertical Distance\": \"垂直间距\",\n  \"Top Spacing\": \"上边距\",\n  \"Bottom Spacing\": \"下边距\",\n  \"Horizontal Distance\": \"水平间距\",\n  \"Left Spacing\": \"左边距\",\n  \"Right Spacing\": \"右边距\",\n  \"Label on top\": \"标记-顶部\",\n  \"Label on right\": \"标记-右侧\",\n  \"Make Note\": \"备注\",\n  \"Label on bottom\": \"标记-底部\",\n  \"Label on left\": \"标记-左侧\",\n  Influence: \"影响范围\",\n  \"Sizing by influence\": \"根据影响范围标记尺寸\",\n  Percentage: \"百分比\",\n  \"Sizing by percentage\": \"标记百分比尺寸\",\n  \"Toggle Hidden\": \"切换可见\",\n  \"Toggle Locked\": \"切换锁定\",\n  \"Clean Marks\": \"清除选定区域或全部标注\",\n  Settings: \"设置\",\n  \"Design resolution\": \"设计分辨率\",\n  \"Unit switch\": \"切换单位\",\n  \"Device switch\": \"切换设备\",\n  \"Convert to pixels\": \"转换为像素值\",\n  \"Convert to rem\": \"转换为 rem 值\",\n  FLOW: \"原型模式\",\n  NOTES: \"备注\",\n  PROPERTIES: \"属性\",\n  FILLS: \"填充\",\n  TYPEFACE: \"字体\",\n  BORDERS: \"边框\",\n  SHADOWS: \"阴影\",\n  \"CSS STYLE\": \"CSS 样式\",\n  \"CODE TEMPLATE\": \"代码模板\",\n  EXPORTABLE: \"切图\",\n  Gradient: \"渐变\",\n  Color: \"颜色\",\n  \"Layer Name\": \"图层名称\",\n  Weight: \"粗细\",\n  \"Style name\": \"样式名称\",\n  Custom: \"自定义\",\n  Standard: \"标准像素\",\n  \"iOS Devices\": \"iOS 设备\",\n  Points: \"标准点\",\n  Retina: \"视网膜\",\n  \"Retina HD\": \"高清视网膜\",\n  \"Android Devices\": \"安卓设备\",\n  \"Other Devices\": \"其他设备\",\n  \"Ubuntu Grid\": \"Ubuntu 网格\",\n  \"Web View\": \"网页\",\n  Scale: \"倍率\",\n  Units: \"单位\",\n  \"Device Unit\": \"设备单位\",\n  \"Design Resolution\": \"设计分辨率\",\n  \"%@px on Sketch = 1%@ on device\": \"%@px Sketch = 1%@ 设备\",\n  \"Color format\": \"颜色格式\",\n  \"Color hex\": \"色值\",\n  \"ARGB hex\": \"安卓色值\",\n  \"Artboard order\": \"画板排序\",\n  \"Order by artboard rows\": \"按画板行排序\",\n  \"Order by artboard columns\": \"按画板列排序\",\n  \"Order by alphabet\": \"按名称排序\",\n  \"Order by layer order\": \"按图层顺序\",\n  Positive: \"正序\",\n  Reverse: \"逆序\",\n  Save: \"保存\",\n  Width: \"宽度\",\n  Height: \"高度\",\n  Top: \"上面\",\n  Right: \"右侧\",\n  Bottom: \"下面\",\n  Left: \"左侧\",\n  \"Fill / Color\": \"填充 / 颜色\",\n  Border: \"边框\",\n  Opacity: \"不透明度\",\n  Radius: \"圆角\",\n  Shadow: \"外(内)阴影\",\n  Style: \"样式名称\",\n  \"Font size\": \"字号\",\n  Line: \"行高\",\n  Typeface: \"字体\",\n  Character: \"字间距\",\n  Paragraph: \"段落间距\",\n  \"Percentage of artboard\": \"基于画板百分比单位\",\n  Mark: \"标注\",\n  All: \"全选\",\n  None: \"全不选\",\n  \"Select filtered\": \"选中过滤的\",\n  \"Selection of Sketch\": \"Sketch 的选中画板\",\n  \"Current of Sketch\": \"Sketch 的当前画板\",\n  Filter: \" 过滤\",\n  Export: \"导出\",\n  Position: \"位置\",\n  Size: \"大小\",\n  Family: \"字体\",\n  Spacing: \"空间\",\n  Content: \"内容\",\n  \"All artboards\": \"全部画板\",\n  \"Start points\": \"起点画板\",\n  \"No slices added!\": \"未添加切图\",\n  \"No color names added!\": \"未添加颜色名称\",\n  \"Select 1 or 2 layers to mark!\": \"请选中 1 至 2 个图层!\",\n  \"Select a text layer to mark!\": \"请选中 1 个文本图层!\",\n  \"Select a layer to mark!\": \"请选中 1 个图层!\",\n  \"Select any layer to mark!\": \"请选中任意个图层!\",\n  \"Export spec\": \"导出规范\",\n  \"Export to:\": \"导出到:\",\n  \"Exporting...\": \"导出中...\",\n  \"Please wait for former task to exit\": \"请等待先前的任务完成\",\n  \"Cancelled by user\": \"用户取消了任务\",\n  \"Export complete! Takes %@ seconds\": \"导出完成! 耗时 %@ 秒\",\n  \"The slice not in current artboard.\": \"切图不在当前画板\",\n  \"Inside Border\": \"内边框\",\n  \"Outside Border\": \"外边框\",\n  \"Center Border\": \"中心边框\",\n  \"Inner Shadow\": \"内阴影\",\n  \"Outer Shadow\": \"外阴影\",\n  Offset: \"偏移\",\n  Effect: \"效果\",\n  Blur: \"模糊\",\n  Spread: \"扩散\",\n  \"No artboards!\": \"没有画板\",\n  \"You need add some artboards.\": \"您需要添加一些画板\",\n  \"No colors added!\": \"没有定义颜色\",\n  \"Select a layer to add exportable!\": \"请选中 1 个图层!\",\n  \"Import complete!\": \"导入完成!\",\n  \"Processing layer %@ of %@\": \"图层处理中... %@ / %@\",\n  \"Advanced mode\": \"高级模式\",\n  \"Export layer influence rect\": \"导出图层的影响尺寸\",\n  \"Keyboard shortcut\": \"快捷键\"\n};\n", "import { data } from \"./i18n/zh-cn\";\nimport { ArtboardData, ExportData } from \"./interfaces\";\nexport interface GroupData {\n  _id: string;\n  name: string;\n}\nexport type ProjectData = ExportData & {\n  colorNames: { [key: string]: string };\n  group: Partial<GroupData>;\n};\nexport interface State {\n  zoom: number;\n  unit: string;\n  scale: number;\n  artboardIndex?: number;\n  colorFormat: string;\n  current: ArtboardData;\n  selectedIndex?: number;\n  codeType: string;\n  targetIndex: number;\n  unitName: string;\n  tempTargetRect?: any;\n}\n\nexport function localize(str: string): string {\n  return data[str] ? data[str] : str;\n}\n\nexport enum Edge {\n  vtop = 0b100000,\n  vbottom = 0b010000,\n  vmiddle = 0b001000,\n  hleft = 0b000100,\n  hright = 0b000010,\n  hcenter = 0b000001\n}\n", "<template>\n  <div class=\"unit-div\">\n    <el-dropdown trigger=\"click\" @command=\"handleUnitChange\">\n      <span class=\"unit-div-link\">\n        {{ curUnit?.name || \"请选择\" }}<el-icon class=\"el-icon--right\"><arrow-down /></el-icon>\n      </span>\n      <template #dropdown>\n        <el-dropdown-menu>\n          <template v-for=\"(item, index) in unitsData\" :key=\"item.name\">\n            <el-dropdown-item disabled :divided=\"index != 0\">{{ item.name }}</el-dropdown-item>\n            <template v-for=\"(unit, i) in item.units\" :key=\"unit.name\">\n              <el-dropdown-item :command=\"unit\" :divided=\"i == 0\">{{ unit.name }}</el-dropdown-item>\n            </template>\n          </template>\n        </el-dropdown-menu>\n      </template>\n    </el-dropdown>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { ArrowDown } from \"@element-plus/icons-vue\";\nimport { localize } from \"./model\";\nimport { computed } from \"vue\";\nimport { sketchStore } from \"@/store\";\n\nconst store = sketchStore();\n\nconst unitsData = [\n  {\n    name: localize(\"Device switch\"),\n    units: [\n      { name: localize(\"Web View\") + \" - px\", unit: \"px\", scale: 1 },\n      { name: localize(\"iOS Devices\") + \" - pt\", unit: \"pt\", scale: 1 },\n      { name: localize(\"Android Devices\") + \" - dp/sp\", unit: \"dp/sp\", scale: 1 }\n    ]\n  },\n  {\n    name: localize(\"Convert to pixels\"),\n    units: [\n      { name: \"IOS \" + localize(\"Points\") + \" @1x\", unit: \"px\", scale: 1 },\n      { name: \"IOS \" + localize(\"Retina\") + \" @2x\", unit: \"px\", scale: 2 },\n      { name: \"IOS \" + localize(\"Retina HD\") + \" @3x\", unit: \"px\", scale: 3 },\n      { name: \"Android LDPI @0.75x\", unit: \"px\", scale: 0.75 },\n      { name: \"Android MDPI @1x\", unit: \"px\", scale: 1 },\n      { name: \"Android HDPI @1.5x\", unit: \"px\", scale: 1.5 },\n      { name: \"Android XHDPI @2x\", unit: \"px\", scale: 2 },\n      { name: \"Android XXHDPI @3x\", unit: \"px\", scale: 3 },\n      { name: \"Android XXXHDPI @4x\", unit: \"px\", scale: 4 }\n    ]\n  },\n  {\n    name: localize(\"Convert to others\"),\n    units: [\n      { name: \"CSS Rem 8px\", unit: \"rem\", scale: 1 / 8 },\n      { name: \"CSS Rem 10px\", unit: \"rem\", scale: 1 / 10 },\n      { name: \"CSS Rem 12px\", unit: \"rem\", scale: 1 / 12 },\n      { name: \"CSS Rem 14px\", unit: \"rem\", scale: 1 / 14 },\n      { name: \"CSS Rem 16px\", unit: \"rem\", scale: 1 / 16 },\n      { name: localize(\"Ubuntu Grid\"), unit: \"gu\", scale: 1 / 27 }\n    ]\n  }\n];\nconst curUnit = computed<any>(() => {\n  let cur: any = null;\n  if (!store.state.unitName) {\n    return unitsData[0].units[0];\n  }\n  unitsData.find((item) => {\n    const data = item.units.find((unit) => {\n      return unit.unit == store.state.unit && unit.name == store.state.unitName;\n    });\n    if (data) {\n      cur = data;\n      return true;\n    }\n  });\n  return cur;\n});\nconst handleUnitChange = (unit: any) => {\n  store.state.unit = unit.unit;\n  store.state.scale = unit.scale;\n  store.state.unitName = unit.name;\n  //   emit(\"change\", unit);\n};\n</script>\n<style lang=\"less\" scoped>\n.unit-div {\n  height: 100%;\n  margin-left: 15px;\n  .el-dropdown {\n    height: 100%;\n    display: inline-flex;\n    align-items: center;\n    cursor: pointer;\n  }\n  .unit-div-link {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n</style>\n", "import { sketchStore } from \"@/store\";\nimport { SMRect } from \"../model/interfaces\";\nimport { Edge } from \"../model\";\n\nexport function getEventTarget(eventNode: Element, event: Event, selector: string): any {\n  let current: any = event.target as HTMLElement;\n  while (current && current !== eventNode) {\n    if (current.matches(selector)) return current;\n    current = current.parentElement;\n  }\n  return undefined;\n}\n\nexport function unitSize(value: number, isText?: boolean) {\n  const { state, project } = sketchStore();\n  // logic point\n  const pt = value / project.resolution;\n  // convert to display value\n  const sz = Math.round(pt * state.scale * 100) / 100;\n  const units = state.unit.split(\"/\");\n  let unit = units[0];\n  if (units.length > 1 && isText) {\n    unit = units[1];\n  }\n  return sz + unit;\n}\n\nexport function getEdgeRect(event: MouseEvent): SMRect {\n  const { state } = sketchStore();\n  const screen = document.querySelector(\"#screen\") as HTMLElement;\n  const rect = screen.getBoundingClientRect();\n  let x = (event.pageX - rect.left) / state.zoom;\n  let y = (event.pageY - rect.top) / state.zoom;\n  let width = 10;\n  let height = 10;\n  const xScope = x >= 0 && x <= state.current.width;\n  const yScope = y >= 0 && y <= state.current.height;\n  // left and top\n  if (x <= 0 && y <= 0) {\n    x = -10;\n    y = -10;\n  }\n  // right and top\n  else if (x >= state.current.width && y <= 0) {\n    x = state.current.width;\n    y = -10;\n  }\n  // right and bottom\n  else if (x >= state.current.width && y >= state.current.height) {\n    x = state.current.width;\n    y = state.current.height;\n  }\n  // left and bottom\n  else if (x <= 0 && y >= state.current.height) {\n    x = -10;\n    y = state.current.height;\n  }\n  // top\n  else if (y <= 0 && xScope) {\n    x = 0;\n    y = -10;\n    width = state.current.width;\n  }\n  // right\n  else if (x >= state.current.width && yScope) {\n    x = state.current.width;\n    y = 0;\n    height = state.current.height;\n  }\n  // bottom\n  else if (y >= state.current.height && xScope) {\n    x = 0;\n    y = state.current.height;\n    width = state.current.width;\n  }\n  // left\n  else if (x <= 0 && yScope) {\n    x = -10;\n    y = 0;\n    height = state.current.height;\n  }\n  if (xScope && yScope) {\n    x = 0;\n    y = 0;\n    width = state.current.width;\n    height = state.current.height;\n  }\n  return {\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  };\n}\nexport function scaleSize(length: number) {\n  const { state } = sketchStore();\n  return Math.round((length / state.scale) * 10) / 10;\n}\nfunction getRect(index: number): SMRect {\n  const { state } = sketchStore();\n  const layer = state.current.layers[index];\n  return layer.rect;\n}\nfunction getIntersection(a: SMRect, b: SMRect): SMRect | undefined {\n  const x1 = Math.max(a.x, b.x);\n  const y1 = Math.max(a.y, b.y);\n  const x2 = Math.min(a.x + a.width, b.x + b.width);\n  const y2 = Math.min(a.y + a.height, b.y + b.height);\n  const width = x2 - x1;\n  const height = y2 - y1;\n  if (width <= 0 || height <= 0) {\n    // no intersection\n    return undefined;\n  }\n  return {\n    x: x1,\n    y: y1,\n    width: width,\n    height: height\n  };\n}\n\nexport function zoomSize(size: number) {\n  const { state, project } = sketchStore();\n  return (size * state.zoom) / project.resolution;\n}\nexport function percentageSize(size: number, size2: number) {\n  return Math.round((size / size2) * 1000) / 10 + \"%\";\n}\nfunction getDistance(selected: SMRect, target: SMRect) {\n  return {\n    top: selected.y - target.y,\n    right: target.x + target.width - selected.x - selected.width,\n    bottom: target.y + target.height - selected.y - selected.height,\n    left: selected.x - target.x\n  };\n}\nexport function distance() {\n  const { state } = sketchStore();\n  if (state.selectedIndex === undefined) return;\n  if (state.selectedIndex == state.targetIndex && !state.tempTargetRect) return;\n\n  const selectedRect: SMRect = getRect(state.selectedIndex);\n  const targetRect: SMRect = state.tempTargetRect || getRect(state.targetIndex);\n  let topData;\n  let rightData;\n  let bottomData;\n  let leftData;\n  const x = zoomSize(selectedRect.x + selectedRect.width / 2);\n  const y = zoomSize(selectedRect.y + selectedRect.height / 2);\n\n  const selectedX2 = selectedRect.x + selectedRect.width;\n  const selectedY2 = selectedRect.y + selectedRect.height;\n  const targetX2 = targetRect.x + targetRect.width;\n  const targetY2 = targetRect.y + targetRect.height;\n  if (!getIntersection(selectedRect, targetRect)) {\n    if (selectedRect.y > targetY2) {\n      //top\n      topData = {\n        x: x,\n        y: zoomSize(targetY2),\n        h: zoomSize(selectedRect.y - targetY2),\n        distance: unitSize(selectedRect.y - targetY2),\n        percentageDistance: percentageSize(selectedRect.y - targetY2, state.current.height)\n      };\n    }\n    if (selectedX2 < targetRect.x) {\n      //right\n      rightData = {\n        x: zoomSize(selectedX2),\n        y: y,\n        w: zoomSize(targetRect.x - selectedX2),\n        distance: unitSize(targetRect.x - selectedX2),\n        percentageDistance: percentageSize(targetRect.x - selectedX2, state.current.width)\n      };\n    }\n    if (selectedY2 < targetRect.y) {\n      //bottom\n      bottomData = {\n        x: x,\n        y: zoomSize(selectedY2),\n        h: zoomSize(targetRect.y - selectedY2),\n        distance: unitSize(targetRect.y - selectedY2),\n        percentageDistance: percentageSize(targetRect.y - selectedY2, state.current.height)\n      };\n    }\n    if (selectedRect.x > targetX2) {\n      //left\n      leftData = {\n        x: zoomSize(targetX2),\n        y: y,\n        w: zoomSize(selectedRect.x - targetX2),\n        distance: unitSize(selectedRect.x - targetX2),\n        percentageDistance: percentageSize(selectedRect.x - targetX2, state.current.width)\n      };\n    }\n  } else {\n    const distance = getDistance(selectedRect, targetRect);\n    if (distance.top != 0) {\n      //top\n      topData = {\n        x: x,\n        y: distance.top > 0 ? zoomSize(targetRect.y) : zoomSize(selectedRect.y),\n        h: zoomSize(Math.abs(distance.top)),\n        distance: unitSize(Math.abs(distance.top)),\n        percentageDistance: percentageSize(Math.abs(distance.top), state.current.height)\n      };\n    }\n    if (distance.right != 0) {\n      //right\n      rightData = {\n        x: distance.right > 0 ? zoomSize(selectedX2) : zoomSize(targetX2),\n        y: y,\n        w: zoomSize(Math.abs(distance.right)),\n        distance: unitSize(Math.abs(distance.right)),\n        percentageDistance: percentageSize(Math.abs(distance.right), state.current.width)\n      };\n    }\n    if (distance.bottom != 0) {\n      //bottom\n      bottomData = {\n        x: x,\n        y: distance.bottom > 0 ? zoomSize(selectedY2) : zoomSize(targetY2),\n        h: zoomSize(Math.abs(distance.bottom)),\n        distance: unitSize(Math.abs(distance.bottom)),\n        percentageDistance: percentageSize(Math.abs(distance.bottom), state.current.height)\n      };\n    }\n    if (distance.left != 0) {\n      //left\n      leftData = {\n        x: distance.left > 0 ? zoomSize(targetRect.x) : zoomSize(selectedRect.x),\n        y: y,\n        w: zoomSize(Math.abs(distance.left)),\n        distance: unitSize(Math.abs(distance.left)),\n        percentageDistance: percentageSize(Math.abs(distance.left), state.current.width)\n      };\n    }\n  }\n  if (topData) {\n    const td = document.querySelector(\"#td\") as HTMLElement;\n    td.style.left = topData.x + \"px\";\n    td.style.top = topData.y + \"px\";\n    td.style.height = topData.h + \"px\";\n    td.style.display = \"\";\n    const tdDiv = document.querySelector(\"#td div\") as HTMLElement;\n    tdDiv.setAttribute(\"data-height\", topData.distance);\n    tdDiv.setAttribute(\"percentage-height\", topData.percentageDistance);\n  }\n  if (rightData) {\n    const rd = document.querySelector(\"#rd\") as HTMLElement;\n    rd.style.left = rightData.x + \"px\";\n    rd.style.top = rightData.y + \"px\";\n    rd.style.width = rightData.w + \"px\";\n    rd.style.display = \"\";\n    const rdDiv = document.querySelector(\"#rd div\") as HTMLElement;\n    rdDiv.setAttribute(\"data-width\", rightData.distance);\n    rdDiv.setAttribute(\"percentage-width\", rightData.percentageDistance);\n  }\n  if (bottomData) {\n    const bd = document.querySelector(\"#bd\") as HTMLElement;\n    bd.style.left = bottomData.x + \"px\";\n    bd.style.top = bottomData.y + \"px\";\n    bd.style.height = bottomData.h + \"px\";\n    bd.style.display = \"\";\n    const bdDiv = document.querySelector(\"#bd div\") as HTMLElement;\n    bdDiv.setAttribute(\"data-height\", bottomData.distance);\n    bdDiv.setAttribute(\"percentage-height\", bottomData.percentageDistance);\n  }\n  if (leftData) {\n    const ld = document.querySelector(\"#ld\") as HTMLElement;\n    ld.style.left = leftData.x + \"px\";\n    ld.style.top = leftData.y + \"px\";\n    ld.style.width = leftData.w + \"px\";\n    ld.style.display = \"\";\n    const ldDiv = document.querySelector(\"#ld div\") as HTMLElement;\n    ldDiv.setAttribute(\"data-width\", leftData.distance);\n    ldDiv.setAttribute(\"percentage-width\", leftData.percentageDistance);\n  }\n  const selectedData: any = document.querySelector(\".selected\");\n  if (selectedData !== null) {\n    selectedData.classList.add(\"hidden\");\n  }\n}\n\nexport function alignElement(options: {\n  scroller: HTMLElement;\n  target: HTMLElement;\n  fromRect?: DOMRect;\n  toRect: DOMRect;\n  fromEdge: Edge;\n  toEdge: Edge;\n}): void {\n  const fromRect = options.fromRect || options.target.getBoundingClientRect();\n  const from = options.fromEdge;\n  const to = options.toEdge;\n  const fromHasV = !!(0b111000 & from);\n  const toHasV = !!(0b111000 & to);\n  const fromHasH = !!(0b000111 & from);\n  const toHasH = !!(0b000111 & to);\n  let offsetX = 0;\n  let offsetY = 0;\n  if (fromHasH && toHasH) {\n    offsetX = options.toRect.x - fromRect.x; // left-to-left offset\n    if (from & Edge.hcenter) offsetX -= fromRect.width / 2;\n    if (from & Edge.hright) offsetX -= fromRect.width;\n    if (to & Edge.hcenter) offsetX += options.toRect.width / 2;\n    if (to & Edge.hright) offsetX += options.toRect.width;\n  }\n  if (fromHasV && toHasV) {\n    offsetY = options.toRect.y - fromRect.y; // top-to-top offset\n    if (from & Edge.vmiddle) offsetY -= fromRect.height / 2;\n    if (from & Edge.vbottom) offsetY -= fromRect.height;\n    if (to & Edge.vmiddle) offsetY += options.toRect.height / 2;\n    if (to & Edge.vbottom) offsetY += options.toRect.height;\n  }\n  options.scroller.scrollTop -= offsetY;\n  options.scroller.scrollLeft -= offsetX;\n}\n", "import { hideDistance, mouseoutLayer } from \"./document\";\nexport let panMode = false;\nlet moving = false;\nlet moveData: any;\nexport function windowKeyDownEvent(event) {\n  if (event.which !== 32) return;\n  const cursor: any = document.getElementById(\"cursor\");\n  const screenViewer: any = document.querySelector(\".screen-viewer\");\n  cursor.style.display = \"\";\n  screenViewer.classList.add(\"moving-screen\");\n  mouseoutLayer();\n  hideDistance();\n  panMode = true;\n  event.preventDefault();\n}\n\nexport function windowKeyUpEvent(event) {\n  if (event.which !== 32) return;\n  const cursor: any = document.getElementById(\"cursor\");\n  const screenViewer: any = document.querySelector(\".screen-viewer\");\n  cursor.style.display = \"none\";\n  cursor.classList.remove(\"moving\");\n  screenViewer.classList.remove(\"moving-screen\");\n  panMode = false;\n  moving = false;\n  event.preventDefault();\n}\n\nexport function windowMouseMoveEvent(event) {\n  const cursor: any = document.getElementById(\"cursor\");\n  if (cursor !== null) {\n    cursor.style.left = event.clientX + \"px\";\n    cursor.style.top = event.clientY - 48 + \"px\";\n  }\n  if (!moving) return;\n  const viewer: any = document.querySelector(\".screen-viewer\");\n  viewer.scrollLeft = moveData.x - event.clientX + moveData.scrollLeft;\n  viewer.scrollTop = moveData.y - event.clientY + moveData.scrollTop;\n  event.preventDefault();\n}\nexport function windowMouseDownEvent(event) {\n  if (!panMode) return;\n  const cursor: any = document.getElementById(\"cursor\");\n  const viewer: any = document.querySelector(\".screen-viewer\");\n  cursor.classList.add(\"moving\");\n  moveData = {\n    x: event.clientX,\n    y: event.clientY,\n    scrollLeft: viewer.scrollLeft,\n    scrollTop: viewer.scrollTop\n  };\n  moving = true;\n  event.preventDefault();\n}\nexport function windowMouseUpEvent(event) {\n  if (!panMode || !moving) return;\n  const cursor: any = document.getElementById(\"cursor\");\n  const viewer: any = document.querySelector(\".screen-viewer\");\n  viewer.classList.remove(\"moving-screen\");\n  cursor.classList.remove(\"moving\");\n  moving = false;\n  event.preventDefault();\n}\n", "import { distance, getEdgeRect, getEventTarget } from \"./helper\";\nimport { sketchStore } from \"@/store\";\nimport { panMode } from \"./window\";\n\nexport function hideDistance() {\n  [\"#td\", \"#rd\", \"#bd\", \"#ld\"].forEach((s) => {\n    const el = document.querySelector(s) as HTMLElement;\n    if (el !== null) {\n      el.style.display = \"none\";\n    }\n  });\n  document.querySelector(\".selected\")?.classList.remove(\"hidden\");\n}\nfunction mouseoverLayer() {\n  const { state } = sketchStore();\n  if (state.targetIndex && state.selectedIndex == state.targetIndex) return false;\n  const target = document.querySelector(\"#layer-\" + state.targetIndex) as HTMLElement;\n  target.classList.add(\"hover\");\n  const rv = document.querySelector(\"#rv\") as HTMLElement;\n  rv.style.left = target.offsetLeft + \"px\";\n  rv.style.width = target.offsetWidth + \"px\";\n  const rh = document.querySelector(\"#rh\") as HTMLElement;\n  rh.style.top = target.offsetTop + \"px\";\n  rh.style.height = target.offsetHeight + \"px\";\n  const rulersHtml: any = document.querySelector(\"#rulers\");\n  if (rulersHtml !== null) {\n    rulersHtml.style.display = \"\";\n  }\n}\n\nexport function mouseoutLayer() {\n  document.querySelector(\".hover\")?.classList.remove(\"hover\");\n  const rulersHtml: any = document.querySelector(\"#rulers\");\n  if (rulersHtml !== null) {\n    rulersHtml.style.display = \"none\";\n  }\n}\nexport function selectedLayer() {\n  const { state } = sketchStore();\n  if (state.selectedIndex == undefined) return false;\n  document.querySelector(\".selected\")?.classList.remove(\"selected\");\n  document.querySelector(\"#layer-\" + state.selectedIndex)?.classList.add(\"selected\");\n  const rulersHtml: any = document.querySelector(\"#rulers\");\n  if (rulersHtml !== null) {\n    rulersHtml.style.display = \"none\";\n  }\n}\n\nfunction removeSelected() {\n  const { state } = sketchStore();\n  if (state.selectedIndex === undefined) return false;\n  document.querySelector(\"#layer-\" + state.selectedIndex)?.classList.remove(\"selected\");\n  const rulersHtml: any = document.querySelector(\"#rulers\");\n  if (rulersHtml !== null) {\n    rulersHtml.style.display = \"none\";\n  }\n  document.querySelector(\"#inspector\")?.classList.remove(\"active\");\n  state.selectedIndex = undefined;\n  state.tempTargetRect = undefined;\n  hideDistance();\n}\nexport const documentClickEvent = function (event) {\n  const store = sketchStore();\n  if (panMode) return;\n  if (getEventTarget(document.body, event, \".sketch-nav,.sketch-artboards\")) {\n    event.stopPropagation();\n    return;\n  }\n  store.slicesVisible = false;\n  store.historyVisible = false;\n  store.colorsVisible = false;\n  const target = event.target as HTMLElement;\n  if (target.classList.contains(\"layer\") || target.classList.contains(\"slices-layer\")) {\n    const selected = !target.classList.contains(\"slices-layer\")\n      ? target\n      : (document.querySelector(\".layer-\" + target.dataset.objectid) as HTMLElement);\n    store.state.selectedIndex = parseInt(selected.dataset.index!);\n\n    hideDistance();\n    mouseoutLayer();\n    selectedLayer();\n    return;\n  }\n  removeSelected();\n};\n\nexport const documentMouseMoveEvent = function (event) {\n  if (panMode) return;\n  const { state } = sketchStore();\n  mouseoutLayer();\n  hideDistance();\n  const target = event.target as HTMLElement;\n  if (\n    target.classList.contains(\"screen-viewer\") ||\n    target.classList.contains(\"screen-viewer-inner\")\n  ) {\n    state.tempTargetRect = getEdgeRect(event);\n    state.targetIndex = 0;\n    distance();\n  } else if (target.classList.contains(\"layer\")) {\n    state.targetIndex = parseInt((event.target as HTMLElement).dataset.index!);\n    state.tempTargetRect = undefined;\n    mouseoverLayer();\n    distance();\n  } else {\n    state.tempTargetRect = undefined;\n  }\n};\n", "<template>\n  <div @click.stop class=\"slices-div\" :class=\"{ show: store.slicesVisible }\">\n    <ul class=\"slices-wrapper\" v-if=\"slices && slices.length\">\n      <template v-for=\"(item, index) in slices\" :key=\"index\">\n        <li @click=\"handleClick(item.objectID)\" class=\"slices-item\" v-for=\"(info, i) in item.exportable\" :key=\"i\">\n          <!-- <div class=\"slices-layer\" :id=\"`slice-${item.objectID}`\" :data-objectId=\"item.objectID\"></div> -->\n          <picture class=\"slices-pic\">\n            <img :src=\"info.path\" alt=\"\" />\n          </picture>\n          <div class=\"slices-right\">\n            <div class=\"slices-title\">{{ info.name }}</div>\n            <div class=\"slices-info\">\n              <div class=\"slices-tips\">{{ unitSize(item.rect.width) }}&nbsp;&nbsp;x&nbsp;&nbsp;{{ unitSize(item.rect.height) }}</div>\n              <el-button type=\"primary\" size=\"small\" @click.stop=\"toCopy(info.path)\" round>复制链接</el-button>\n            </div>\n          </div>\n        </li>\n      </template>\n    </ul>\n    <div class=\"slices-placeholder\">该画板暂无切片，若有需要，请联系 UI</div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, watch, defineEmits } from \"vue\";\nimport { unitSize } from \"./utils/helper\";\nimport { sketchStore } from \"@/store\";\nimport { hideDistance, mouseoutLayer, selectedLayer } from \"./utils/document\";\nconst store = sketchStore();\nconst props = defineProps<{\n  slices: any[];\n}>();\nwatch(\n  () => props.slices,\n  (slices) => {\n    console.log(slices);\n  }\n);\nconst emit = defineEmits([\"copy\"]);\nconst toCopy = (text: string) => {\n  emit(\"copy\", text);\n};\nconst handleClick = (id) => {\n  const selected = document.querySelector(\".layer-\" + id) as HTMLElement;\n  store.state.selectedIndex = parseInt(selected.dataset.index!);\n  hideDistance();\n  mouseoutLayer();\n  selectedLayer();\n};\n</script>\n\n<style lang=\"less\" scoped>\n.slices-div {\n  width: 340px;\n  height: 100%;\n  background: #fff;\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 999;\n  transition: transform 0.3s ease-in-out;\n  transform: translateX(100%);\n  .slices-placeholder {\n    height: 100%;\n    width: 100%;\n    color: #909299;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 13px;\n  }\n  &.show {\n    transform: translateX(0);\n  }\n  .slices-item {\n    display: flex;\n    padding: 10px 20px;\n    cursor: pointer;\n    position: relative;\n    &:hover {\n      opacity: 0.9;\n    }\n  }\n\n  .slices-pic {\n    width: 70px;\n    height: 70px;\n    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%), linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%);\n    background-position:\n      0 0,\n      5px 5px;\n    background-size: 10px 10px;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    img {\n      width: 30px;\n      object-fit: contain;\n    }\n  }\n  .slices-right {\n    flex: 1;\n    margin-left: 8px;\n    .slices-title {\n      background: #f5f5fb;\n      border-radius: 4px;\n      padding: 0 12px;\n      display: inline-flex;\n      align-items: center;\n      font-size: 14px;\n      color: #303233;\n      width: 100%;\n      box-sizing: border-box;\n      height: 36px;\n    }\n    .slices-info {\n      display: flex;\n      width: 100%;\n      margin-top: 7px;\n      justify-content: space-between;\n      align-items: center;\n\n      .slices-tips {\n        font-size: 12px;\n        color: #909299;\n      }\n      .el-button {\n        position: absolute;\n        bottom: 10px;\n        right: 20px;\n      }\n    }\n  }\n}\n.slices-wrapper {\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n.slices-wrapper::-webkit-scrollbar {\n  display: none;\n}\n</style>\n", "<template>\n  <div @click.stop class=\"colors-div\" :class=\"{ show: store.colorsVisible }\">\n    <ul class=\"colors-wrapper\">\n      <template v-for=\"(item, index) in colors\" :key=\"index\">\n        <li class=\"colors-item\">\n          <div class=\"colors-show\" :style=\"{ background: item.color['css-rgba'] }\"></div>\n          <div class=\"colors-name\">\n            <b>{{ item.name }}</b>\n            <div class=\"colors-tips\">{{ item.color[colorFormat] }}</div>\n          </div>\n        </li>\n      </template>\n    </ul>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, watch } from \"vue\";\nimport { sketchStore } from \"@/store\";\nconst store = sketchStore();\nconst props = defineProps<{\n  colors: any[];\n  colorFormat: string;\n}>();\nwatch(\n  () => props.colors,\n  (slices) => {\n    console.log(slices);\n  }\n);\n</script>\n\n<style lang=\"less\" scoped>\n.colors-div {\n  width: 340px;\n  height: 100%;\n  background: #fff;\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 999;\n  transition: transform 0.3s ease-in-out;\n  transform: translateX(100%);\n  padding: 0 20px;\n  &.show {\n    transform: translateX(0);\n  }\n  .colors-item {\n    display: flex;\n    margin-top: 20px;\n    align-items: center;\n    .colors-show {\n      width: 30px;\n      height: 30px;\n      border-radius: 50%;\n    }\n    .colors-name {\n      padding-left: 10px;\n      .colors-tips {\n        font-size: 12px;\n        color: #999;\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <div class=\"sketch-artboards\">\n    <div class=\"search-box\">\n      <div class=\"search-name\" v-if=\"!searchInputStatus\">全部</div>\n      <div class=\"search-container\" v-else v-click-outside=\"handleOutsideClick\">\n        <el-input clearable style=\"width: 224px\" @clear=\"handleSearch\" ref=\"inputRef\" v-model=\"searchInput\" placeholder=\"搜索\" @keyup.enter=\"handleSearch\"></el-input>\n      </div>\n      <div class=\"search-icon\" @click.stop=\"onClickBtn()\">\n        <i class=\"iconfont icon-sousuo\"></i>\n      </div>\n    </div>\n    <el-divider />\n\n    <div class=\"list\">\n      <div class=\"tree-list-item\">\n        <div class=\"item-box\">\n          <div class=\"dnd-item\">\n            <div class=\"dnd-item-box\" @click=\"handleClick\">\n              <i :class=\"{ 'iconfont icon-jiantouzhankai': true, 'active-item': dndTtemStatus }\"></i>\n              <i class=\"iconfont icon-a-sucaiku3x\"></i>\n              <div class=\"name\">{{ group?.name || \"未分组\" }}</div>\n            </div>\n            <div v-show=\"dndTtemStatus\" :id=\"item._id\" :class=\"{ 'item-box-child': true, 'active-item': activeSketchId === item._id }\" v-for=\"item in filterList\" :key=\"item._id\" @click=\"chooseSketchActive(item._id)\">\n              <el-popover :show-after=\"500\" width=\"220px\" placement=\"right\" trigger=\"hover\">\n                <template #reference>\n                  <div class=\"item\">\n                    <div class=\"item-img-box\">\n                      <img alt=\"\" :src=\"item.imagePath\" />\n                    </div>\n                    <div class=\"item-name\">{{ item.name }}</div>\n                  </div>\n                </template>\n                <img style=\"width: 200px\" alt=\"\" :src=\"item.imagePath\" />\n              </el-popover>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { nextTick, ref, defineProps, watch, onMounted } from \"vue\";\nimport hotkeys from \"hotkeys-js\";\nimport { GroupData } from \"./model\";\nconst filterList = ref<any[]>([]);\nconst props = defineProps<{\n  activeSketchId: string;\n  group: Partial<GroupData>;\n  groupDataList: any[];\n}>();\nconst emit = defineEmits([\"change\"]);\nconst dndTtemStatus = ref(true);\nconst searchInput = ref(\"\");\nconst inputRef = ref<any>(null);\nconst searchInputStatus = ref(false);\n\nonMounted(() => {\n  hotkeys(\"command+f\", (event) => {\n    onClickBtn(true);\n    event.preventDefault();\n  });\n});\nwatch(\n  () => props.groupDataList,\n  () => {\n    handleSearch();\n    if (props.activeSketchId) {\n      nextTick(() => {\n        domScrollToViewById(props.activeSketchId);\n      });\n    }\n  }\n);\nconst onClickBtn = (flag?: boolean) => {\n  searchInputStatus.value = flag === undefined ? !searchInputStatus.value : flag;\n  nextTick(() => {\n    searchInputStatus.value ? inputRef.value?.focus() : domScrollToViewById(props.activeSketchId);\n  });\n};\n\nconst handleClick = () => {\n  dndTtemStatus.value = !dndTtemStatus.value;\n};\nconst chooseSketchActive = (id: string) => {\n  emit(\"change\", id);\n};\n\nconst handleOutsideClick = () => {\n  searchInputStatus.value = false;\n  searchInput.value = \"\";\n};\n\nconst handleSearch = () => {\n  filterList.value = props.groupDataList.filter((item: any) => item.slug.includes(searchInput.value));\n  dndTtemStatus.value = true;\n  if (!searchInput.value) {\n    nextTick(() => {\n      domScrollToViewById(props.activeSketchId);\n    });\n  }\n};\n\nconst domScrollToViewById = (id: string) => {\n  // const element = document.getElementById(id);\n  // if (element) {\n  //   element.scrollIntoView({ behavior: \"smooth\", block: \"start\", inline: \"nearest\" });\n  // }\n};\n</script>\n<style lang=\"less\" scoped>\n.sketch-artboards {\n  max-height: calc(100vh - 100px);\n  position: absolute;\n  top: 25px;\n  left: 24px;\n  z-index: 2022;\n  transition: left ease 0.5s;\n  padding: 24px 15px;\n  box-sizing: border-box;\n  background: #fff;\n  width: 264px;\n  border-radius: 10px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);\n  .el-divider {\n    margin: 10px 0;\n  }\n  .search-box {\n    line-height: 32px;\n    font-size: 14px;\n    font-weight: 600;\n    height: 32px;\n    box-sizing: border-box;\n    display: flex;\n    justify-content: space-between;\n    .search-name {\n      color: #333;\n    }\n    .search-icon {\n      cursor: pointer;\n      padding: 0 6px;\n      background-color: #f0f2f5;\n      border-radius: 4px;\n      .icon-sousuo {\n        font-size: 16px;\n      }\n    }\n  }\n  .search-container {\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    position: relative;\n    -webkit-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    box-sizing: border-box;\n    margin-right: 10px;\n  }\n  .list::-webkit-scrollbar {\n    /* WebKit */\n    width: 0;\n    height: 0;\n  }\n  .list {\n    max-height: calc(100vh - 200px);\n    overflow-y: auto;\n    overflow-x: hidden;\n    scrollbar-width: none;\n    -ms-overflow-style: none;\n    .tree-list-item {\n      margin-bottom: 4px;\n      margin-bottom: 4px;\n      user-select: none;\n      font-size: 14px;\n      .item-box {\n        transition: none !important;\n      }\n      .dnd-item {\n        padding: 0;\n        box-sizing: border-box;\n        display: block;\n        position: relative;\n        .dnd-item-box {\n          height: 40px;\n          line-height: 32px;\n          width: 100%;\n          display: flex;\n          padding: 4px;\n          box-sizing: border-box;\n          margin-bottom: 4px;\n          cursor: pointer;\n          &:hover {\n            background-color: #f0f2f5;\n          }\n          .icon-jiantouzhankai {\n            font-size: 8px;\n            transform: rotate(90deg);\n          }\n          .active-item {\n            transform: rotate(180deg);\n          }\n          .icon-a-sucaiku3x {\n            padding-left: 10px;\n            font-size: 13px;\n          }\n          .name {\n            display: inline-block;\n            margin-left: 10px;\n            color: #333333;\n          }\n        }\n        .item-box-child {\n          width: 100%;\n          cursor: pointer;\n          margin-bottom: 4px;\n          &:hover {\n            background-color: #f0f2f5;\n          }\n          &.active-item {\n            background-color: #f0f2f5;\n            font-weight: 700;\n          }\n          .item {\n            height: 48px;\n            line-height: 40px;\n            width: 100%;\n            display: flex;\n            padding: 4px 0;\n            box-sizing: border-box;\n            color: #333333;\n            justify-content: space-between;\n            flex-wrap: nowrap;\n            align-items: center;\n            .item-img-box {\n              height: 40px;\n              width: 40px;\n              min-width: 40px;\n              text-align: center;\n              line-height: 40px;\n              background: #e3e6ec;\n              border-radius: 2px;\n              display: flex;\n              align-items: center;\n              justify-content: space-around;\n              margin-right: 8px;\n              img {\n                width: auto;\n                height: auto;\n                border-radius: 0;\n                max-height: 40px;\n                max-width: 40px;\n              }\n            }\n            .item-name {\n              flex: 1;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n              text-align: left;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <div id=\"rulers\" style=\"display: none\">\n    <div id=\"rv\" class=\"ruler v\"></div>\n    <div id=\"rh\" class=\"ruler h\"></div>\n  </div>\n</template>\n<script lang=\"ts\" setup></script>\n<style lang=\"less\" scoped>\n.ruler {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 1px dashed #5c54f0;\n  box-sizing: border-box;\n}\n\n.ruler.h {\n  border-left: 0;\n  border-right: 0;\n}\n\n.ruler.v {\n  border-top: 0;\n  border-bottom: 0;\n}\n</style>\n", "import { ObjectAny } from \"@/types\";\n\nexport interface LayerStates {\n  isHidden: boolean;\n  isLocked: boolean;\n  isInSlice: boolean;\n  isMeaXure: boolean;\n  isEmptyText: boolean;\n  isInShapeGroup: boolean;\n}\nexport interface SMRect {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n}\nexport interface SMColor {\n  rgb: { r: number; g: number; b: number };\n  hsl: { h: number; s: number; l: number };\n  /**\n   * alpha value within 0-255\n   */\n  alpha: number;\n  \"color-hex\": string;\n  \"argb-hex\": string;\n  \"rgba-hex\": string;\n  \"css-rgba\": string;\n  \"css-hsla\": string;\n  \"ui-color\": string;\n}\nexport interface BorderData {\n  fillType: FillType;\n  position: BorderPosition;\n  thickness: number;\n  color: SMColor;\n  gradient: SMGradient;\n}\nexport interface SMGradientStop {\n  position: number;\n  color: SMColor;\n}\nexport interface SMGradient {\n  /** The type of the Gradient. */\n  type: GradientType;\n  /** The position of the start of the Gradient */\n  from: Point;\n  /** The position of the end of the Gradient. */\n  to: Point;\n  /**\n   * When the gradient is Radial, the from and\n   * to points makes one axis of the ellipse of\n   * the gradient while the aspect ratio\n   * determine the length of the orthogonal\n   * axis (aspectRatio === 1 means that it’s a circle).\n   */\n  aspectRatio: number;\n  /** The different stops of the Gradient */\n  colorStops: SMGradientStop[];\n}\nexport interface SMFillData {\n  fillType: FillType;\n  color: SMColor;\n  gradient: SMGradient;\n}\n\nexport enum SMType {\n  text = \"text\",\n  symbol = \"symbol\",\n  slice = \"slice\",\n  shape = \"shape\",\n  group = \"group\",\n  hotspot = \"hotspot\"\n}\n\nexport interface SMFlow {\n  targetId: string | \"back\";\n  animationType: AnimationType;\n}\nexport interface LayerData {\n  // shared\n  objectID: string;\n  type: SMType;\n  name: string;\n  rect: SMRect;\n  // slice\n  rotation: number;\n  radius: number[];\n  borders: BorderData[];\n  fills: SMFillData[];\n  shadows: SMShadow[];\n  opacity: number;\n  styleName: string;\n  // text\n  content: string;\n  color: SMColor;\n  fontSize: number;\n  fontFace: string;\n  textAlign: Alignment;\n  letterSpacing: number;\n  lineHeight: number;\n  // slice\n  exportable: SMExportable[];\n  // css\n  css: string[];\n  // flow\n  flow?: SMFlow;\n}\nexport interface SMNote {\n  rect: SMRect;\n  note: string;\n}\nexport interface ArtboardData {\n  // artboard: Artboard,\n  pageName: string;\n  pageObjectID: string;\n  name: string;\n  slug: string;\n  objectID: string;\n  width: number;\n  height: number;\n  imagePath?: string;\n  imageIconPath?: string;\n  imageBase64?: string;\n  notes: SMNote[];\n  layers: LayerData[];\n  flowStartPoint: boolean;\n}\n\nexport interface SMColorAsset {\n  name: string;\n  color: SMColor;\n}\n\nexport interface SMColorAsset {\n  name: string;\n  color: SMColor;\n}\nexport interface ExportData {\n  /**\n   * Design resolution, 2 represents @2x\n   */\n  resolution: number;\n  unit: string;\n  colorFormat: string;\n  artboards: ArtboardData[];\n  slices: any[];\n  colors: SMColorAsset[];\n  languages: ObjectAny;\n}\n\nexport interface SMExportFormat {\n  format: string;\n  scale: number;\n  prefix: string;\n  suffix: string;\n}\nexport enum shadowType {\n  outer = \"Outer\",\n  inner = \"Inner\"\n}\n\nexport interface SMShadow {\n  type: shadowType;\n  offsetX: number;\n  offsetY: number;\n  blurRadius: number;\n  spread: number;\n  color: SMColor;\n}\nexport interface SMExportable {\n  name: string;\n  format: string;\n  path: string;\n}\n", "<template>\n  <div id=\"layers\">\n    <template v-for=\"(layer, index) in current.layers\" :key=\"layer.objectID\">\n      <div\n        v-if=\"layer.type !== SMType.group && layer.type !== SMType.hotspot\"\n        :percentage-width=\"percentageSize(layer.rect.width, current.width)\"\n        :percentage-height=\"percentageSize(layer.rect.height, current.height)\"\n        :data-width=\"unitSize(layer.rect.width)\"\n        :data-height=\"unitSize(layer.rect.height)\"\n        :data-index=\"index\"\n        :id=\"`layer-${index}`\"\n        :style=\"computedStyle(layer)\"\n        :class=\"['layer', `layer-${layer.objectID}`, store.state.selectedIndex == index ? 'selected' : '']\"\n      >\n        <i class=\"tl\"></i><i class=\"tr\"></i><i class=\"bl\"></i><i class=\"br\"></i> <b class=\"et h\"></b><b class=\"er v\"></b><b class=\"eb h\"></b><b class=\"el v\"></b>\n      </div>\n    </template>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed } from \"vue\";\nimport { ArtboardData, SMType, LayerData } from \"./model/interfaces\";\nimport { sketchStore } from \"@/store\";\nimport { percentageSize, unitSize, zoomSize } from \"./utils/helper\";\n\nconst store = sketchStore();\nconst current = computed<ArtboardData>(() => {\n  return store.state.current;\n});\nconst computedStyle = (layer: LayerData) => {\n  const x = zoomSize(layer.rect.x);\n  const y = zoomSize(layer.rect.y);\n  const width = zoomSize(layer.rect.width);\n  const height = zoomSize(layer.rect.height);\n  return {\n    left: `${x}px`,\n    top: `${y}px`,\n    width: `${width}px`,\n    height: `${height}px`\n  };\n};\n</script>\n\n<style lang=\"less\">\n.layer {\n  position: absolute;\n  cursor: pointer;\n  z-index: 222;\n  box-sizing: border-box;\n  &.selected {\n    border: 1px solid #f33155;\n  }\n  &.hover {\n    border: 1px solid #5c54f0;\n  }\n  &.selected {\n    i {\n      display: block;\n    }\n\n    &:after,\n    &:before {\n      position: absolute;\n      display: block;\n      left: 50%;\n      top: -23px;\n      transform: translateX(-50%);\n      content: attr(data-width);\n      font-size: 12px;\n      color: #fff;\n      height: 12px;\n      line-height: 12px;\n      padding: 4px;\n      background: #f33155;\n      border-radius: 2px;\n      z-index: 1;\n      width: max-content;\n    }\n    &.hidden:after,\n    &.hidden:before {\n      display: none;\n    }\n    &:after {\n      content: attr(data-height);\n      left: auto;\n      right: 0;\n      top: 50%;\n      transform: translateX(calc(100% + 3px)) translateY(-50%);\n    }\n  }\n  b,\n  i {\n    position: absolute;\n    width: 5px;\n    height: 5px;\n    background: #fff;\n    border: 1px solid #f33155;\n    border-radius: 50%;\n    overflow: hidden;\n    display: none;\n  }\n  b {\n    width: 3px;\n    height: 3px;\n    background: #f33155;\n  }\n  .tl {\n    top: -3px;\n    left: -3px;\n  }\n  .tr {\n    top: -3px;\n    right: -3px;\n  }\n  .bl {\n    bottom: -3px;\n    left: -3px;\n  }\n  .br {\n    bottom: -3px;\n    right: -3px;\n  }\n}\n</style>\n", "<template>\n  <div id=\"notes\">\n    <div\n      class=\"note\"\n      v-for=\"(note, i) in current.notes\"\n      :key=\"i\"\n      :data-index=\"i + 1\"\n      :style=\"{\n        left: `${zoomSize(note.rect.x)}px`,\n        top: `${zoomSize(note.rect.y)}px`\n      }\"\n    >\n      <div style=\"white-space: nowrap; display: none\">\n        {{ note.note }}\n      </div>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed } from \"vue\";\nimport { sketchStore } from \"@/store\";\nimport { ArtboardData } from \"./model/interfaces\";\nimport { zoomSize } from \"./utils/helper\";\n\nconst store = sketchStore();\nconst current = computed<ArtboardData>(() => {\n  return store.state.current;\n});\n</script>\n<style lang=\"less\" scoped>\n.note {\n  position: absolute;\n  margin: -12px 0 0 -12px;\n  width: 24px;\n  height: 24px;\n  background: #f33155;\n  border-radius: 50%;\n  border: 2px solid #fff;\n  box-shadow: 0 0 3px rgba(0, 0, 0, 0.24);\n  &:before {\n    content: attr(data-index);\n    font-size: 12px;\n    display: block;\n    color: #fff;\n    text-align: center;\n    width: 100%;\n    height: 100%;\n    line-height: 20px;\n  }\n  &:hover {\n    box-shadow: 0 0 3px rgba(0, 0, 0, 0.64);\n  }\n  div {\n    position: absolute;\n    top: 50%;\n    left: 30px;\n    border-radius: 4px;\n    padding: 8px;\n    background: #fff;\n    box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);\n    -webkit-user-select: text;\n    color: #222;\n    transform: translateY(-50%);\n    z-index: 2;\n    &:before {\n      content: \"\";\n      position: absolute;\n      left: -7px;\n      top: 50%;\n      width: 8px;\n      height: 14px;\n      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAcCAMAAABf788oAAAANlBMVEUAAAAAAAAAAAAAAAAAAAAAAADR0dEAAAAAAAAAAAAAAAAAAADNzc0AAAAAAADa2trk5OT////5auFFAAAAEXRSTlMAAgYKEhydNCkYDiOfLieWjz4MUj4AAABrSURBVBjTZZFbDoAgEANVFHmIOve/rERjWGj/psnCbjv1gg7nGTpcFmtUdA4Mu7QmMLzGGMHwlvMNhs9yAY3D7qkamcYHr/75ys1IMRePNbbwsRo6oo/qt7rY6Ohxer4GpBFqyFqDFtWqfAD4dQrlWDmShAAAAABJRU5ErkJggg==)\n        no-repeat;\n      background-size: 8px 14px;\n      transform: translateY(-50%);\n    }\n  }\n}\n</style>\n", "<template>\n  <section class=\"screen-viewer\">\n    <div class=\"screen-viewer-inner\" :style=\"screenInnerStyle\">\n      <div id=\"screen\" :style=\"screenStyle\" class=\"screen\">\n        <Rulers />\n        <template v-if=\"funcShow\">\n          <!-- <div id=\"flows\"></div> -->\n          <Layers />\n          <Notes />\n        </template>\n        <Distance />\n      </div>\n    </div>\n    <div class=\"overlay\"></div>\n  </section>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, watch, nextTick, reactive, ref } from \"vue\";\nimport { alignElement, zoomSize } from \"./utils/helper\";\nimport { ArtboardData } from \"./model/interfaces\";\nimport { ObjectAny } from \"@/types\";\nimport Rulers from \"./rulers.vue\";\nimport Layers from \"./layers.vue\";\nimport Notes from \"./notes.vue\";\nimport Distance from \"./distance.vue\";\nimport { sketchStore } from \"@/store\";\nimport { Edge } from \"./model\";\nimport { isEmpty } from \"lodash\";\n\ninterface Point {\n  x: number;\n  y: number;\n}\nconst funcShow = ref(true);\nconst store = sketchStore();\nconst current = computed<ArtboardData>(() => {\n  return store.state.current;\n});\nconst screenStyle = computed(() => {\n  const imageData = current.value.imagePath;\n  return {\n    width: zoomSize(current.value.width) + \"px\",\n    height: zoomSize(current.value.height) + \"px\",\n    background: \"#FFF url(\" + imageData + \") no-repeat\",\n    backgroundSize: zoomSize(current.value.width) + \"px \" + zoomSize(current.value.height) + \"px\"\n  };\n});\nconst screenInnerStyle = reactive<ObjectAny>({});\n\nconst resetScroll = () => {\n  const viewer: any = document.querySelector(\".screen-viewer\");\n  const maxSize = Math.max(current.value.width, current.value.height, viewer!.clientWidth, viewer!.clientHeight) * 5;\n  screenInnerStyle.width = maxSize + \"px\";\n  screenInnerStyle.height = maxSize + \"px\";\n  nextTick(() => {\n    const screen: any = document.querySelector(\"#screen\") as HTMLElement;\n    screen.style.marginLeft = -zoomSize(current.value.width / 2) + \"px\";\n    screen.style.marginTop = -zoomSize(current.value.height / 2) + \"px\";\n    viewer.scrollLeft = (maxSize - viewer.clientWidth) / 2;\n    let suitHight = screen.clientHeight > viewer.clientHeight ? screen.clientHeight : viewer.clientHeight;\n    viewer.scrollTop = (maxSize - suitHight) / 2;\n  });\n};\nwatch(\n  () => current.value,\n  (val) => {\n    if (!isEmpty(val)) {\n      nextTick(() => {\n        resetScroll();\n      });\n    }\n  },\n  {\n    immediate: true\n  }\n);\nfunction screenPointOnViewerCenter(viewer: HTMLDivElement, screen: HTMLDivElement): Point {\n  let viewerRect = viewer.getBoundingClientRect();\n  let screenRect = screen.getBoundingClientRect();\n  let viewerCenter = <Point>{\n    x: viewerRect.x + viewerRect.width / 2,\n    y: viewerRect.y + viewerRect.height / 2\n  };\n  return {\n    x: viewerCenter.x - screenRect.x,\n    y: viewerCenter.y - screenRect.y\n  };\n}\nwatch(\n  () => store.state.zoom,\n  (val: number, oldVal: number) => {\n    funcShow.value = false;\n    let viewer = document.querySelector(\".screen-viewer\") as HTMLDivElement;\n    let screen = document.querySelector(\"#screen\") as HTMLDivElement;\n    let currentRect = screen.getBoundingClientRect();\n    let screenPoint = screenPointOnViewerCenter(viewer, screen);\n    let screenPoint2 = <Point>{\n      x: (screenPoint.x * val) / oldVal,\n      y: (screenPoint.y * val) / oldVal\n    };\n    alignElement({\n      scroller: viewer,\n      target: screen,\n      toRect: currentRect,\n      fromEdge: Edge.hleft | Edge.vtop,\n      toEdge: Edge.hleft | Edge.vtop\n    });\n    viewer.scrollLeft += screenPoint2.x - screenPoint.x;\n    viewer.scrollTop += screenPoint2.y - screenPoint.y;\n    funcShow.value = true;\n  }\n);\n</script>\n<style lang=\"less\" scoped>\n.screen-viewer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: calc(100vh - 48px);\n  background: #ffffff;\n  background-size: 16px 16px;\n  overflow: auto;\n  &::-webkit-scrollbar {\n    display: none;\n  }\n  .overlay {\n    display: none;\n    position: fixed;\n    width: 100vw;\n    height: calc(100vh);\n    top: 60px;\n    left: 0;\n    background: transparent;\n    overflow: hidden;\n    z-index: 2;\n    cursor: none;\n  }\n  &.moving-screen .overlay {\n    display: block;\n  }\n  .screen-viewer-inner {\n    position: relative;\n    margin: 0 auto;\n  }\n  .screen {\n    margin: 0 auto;\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    background: #fff;\n    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);\n  }\n}\n</style>\n", "<template>\n  <div @click.stop v-loading=\"loading\" class=\"history-div\" :class=\"{ show: store.historyVisible }\">\n    <el-timeline>\n      <el-timeline-item v-for=\"(item, index) in history\" :key=\"index\">\n        <div class=\"history-item\" :class=\"{ active: id == item._id }\" @click=\"emit('click', item._id)\">\n          <div class=\"history-item__user\">\n            <img :src=\"item.user.url\" alt=\"\" srcset=\"\" />\n            <div>{{ item.user.name }}</div>\n          </div>\n          <div class=\"history-item__content\">\n            <div class=\"slug\">{{ item.slug }}</div>\n            <div class=\"time\">{{ getTime(item.createTime) }} <el-tag type=\"danger\" v-if=\"item.isDeleted\">已删除</el-tag></div>\n          </div>\n        </div>\n      </el-timeline-item>\n    </el-timeline>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, defineEmits } from \"vue\";\nimport { sketchStore } from \"@/store\";\nconst store = sketchStore();\ndefineProps<{\n  history: any[];\n  id: string;\n  loading: boolean;\n}>();\nconst emit = defineEmits([\"click\"]);\nconst getTime = (time: number) => {\n  return new Date(time).toLocaleString();\n};\n</script>\n\n<style lang=\"less\" scoped>\n.history-div {\n  height: 100%;\n  background: #fff;\n  position: absolute;\n  width: 340px;\n  box-sizing: border-box;\n  top: 0;\n  right: 0;\n  z-index: 999;\n  padding: 20px;\n  transition: transform 0.3s ease-in-out;\n  transform: translateX(100%);\n\n  &.show {\n    transform: translateX(0);\n  }\n  .history-item {\n    background: #f5f5fb;\n    border-radius: 4px;\n    width: 272px;\n    height: 80px;\n    cursor: pointer;\n    display: flex;\n    padding: 10px;\n    box-sizing: border-box;\n    font-size: 12px;\n    &.active,\n    &:hover {\n      background: #f0f0f0;\n    }\n    &__user {\n      width: 80px;\n      text-align: center;\n      img {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        margin-right: 10px;\n      }\n      div {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n      }\n    }\n    &__content {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      .slug {\n        font-weight: 500;\n        font-size: 14px;\n      }\n      .time {\n        font-size: 12px;\n        color: #333;\n        width: 100%;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <section class=\"properties\">\n    <div class=\"properties-title\">{{ localize(\"PROPERTIES\") }}</div>\n    <div class=\"item\">\n      <div class=\"item-label\">\n        {{ localize(\"Position\") + \":\" }}\n      </div>\n      <ul class=\"item-value\">\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', unitSize(layerData.rect.x))\">\n            {{ unitSize(layerData.rect.x) }}\n          </div>\n          <div class=\"value-label\">\n            {{ localize(\"X\") }}\n          </div>\n        </li>\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', unitSize(layerData.rect.y))\">\n            {{ unitSize(layerData.rect.y) }}\n          </div>\n          <div class=\"value-label\">\n            {{ localize(\"Y\") }}\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div class=\"item\">\n      <div class=\"item-label\">\n        {{ localize(\"Size\") + \":\" }}\n      </div>\n      <ul class=\"item-value\">\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', unitSize(layerData.rect.width))\">\n            {{ unitSize(layerData.rect.width) }}\n          </div>\n          <div class=\"value-label\">\n            {{ localize(\"Width\") }}\n          </div>\n        </li>\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', unitSize(layerData.rect.height))\">\n            {{ unitSize(layerData.rect.height) }}\n          </div>\n          <div class=\"value-label\">\n            {{ localize(\"Height\") }}\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div v-if=\"typeof layerData.opacity == 'number'\" class=\"item\">\n      <div class=\"item-label\">\n        {{ localize(\"Opacity\") + \":\" }}\n      </div>\n      <ul class=\"item-value\">\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', Math.round(layerData.opacity * 10000) / 100 + '%')\">\n            {{ Math.round(layerData.opacity * 10000) / 100 + \"%\" }}\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div v-if=\"layerData.radius\" class=\"item\">\n      <div class=\"item-label\">\n        {{ localize(\"Radius\") + \":\" }}\n      </div>\n      <ul class=\"item-value\">\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', unitSize(layerData.radius[0]))\">\n            {{ unitSize(layerData.radius[0]) }}\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div v-if=\"layerData.styleName\" class=\"item\">\n      <div class=\"item-label\">\n        {{ localize(\"Style\") + \":\" }}\n      </div>\n      <ul class=\"item-value\">\n        <li>\n          <div class=\"value-input\" @click=\"emit('copy', layerData.styleName)\">\n            {{ layerData.styleName }}\n          </div>\n        </li>\n      </ul>\n    </div>\n  </section>\n</template>\n<script lang=\"ts\" setup>\nimport { ObjectAny } from \"@/types\";\nimport { defineProps, defineEmits } from \"vue\";\nimport { localize } from \"../model\";\nimport { unitSize } from \"../utils/helper\";\nconst emit = defineEmits([\"copy\"]);\ndefineProps<{\n  layerData: ObjectAny;\n}>();\n</script>\n<style lang=\"less\" scoped>\n.properties {\n  &-title {\n    font-family: PingFangSC-Medium;\n    font-size: 16px;\n    color: #303233;\n    font-weight: 500;\n    margin-top: 16px;\n  }\n  .item {\n    display: flex;\n    width: 100%;\n    margin-top: 10px;\n    &-label {\n      font-size: 14px;\n      color: #595959;\n      width: 70px;\n      margin-top: 8px;\n    }\n    &-value {\n      flex: 1;\n      display: inline-flex;\n      justify-content: space-between;\n    }\n    li {\n      flex: 1;\n    }\n    .value-input {\n      width: 107px;\n      height: 32px;\n      background: #f5f5fb;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #303233;\n      box-sizing: border-box;\n      padding-left: 12px;\n      display: inline-flex;\n      align-items: center;\n    }\n    .value-label {\n      margin-top: 5px;\n      font-size: 14px;\n      color: #909299;\n      padding-left: 12px;\n      line-height: 20px;\n    }\n  }\n}\n</style>\n", "<template>\n  <div class=\"color\" :data-name=\"store.project.colorNames ? store.project.colorNames[color['argb-hex']] : null\">\n    <label>\n      <em>\n        <i :style=\"`background-color: ${color['css-rgba']}`\"></i>\n      </em>\n    </label>\n    <span class=\"value-input\">\n      {{ color[store.state.colorFormat] }}\n    </span>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { sketchStore } from \"@/store\";\nimport { defineProps } from \"vue\";\ndefineProps<{\n  color: any;\n}>();\n\nconst store = sketchStore();\n</script>\n<style lang=\"less\">\n.color {\n  flex: 1;\n  height: 32px;\n  background: #f5f5fb;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #303233;\n  box-sizing: border-box;\n  padding-left: 12px;\n  line-height: 32px;\n  margin-bottom: 5px;\n  position: relative;\n  .value-input {\n    margin-left: 50px;\n  }\n  // display: flex;\n  // align-items: center;\n  &:last-child label:before {\n    display: none;\n  }\n  label {\n    margin-right: 15px;\n    position: absolute;\n    display: block;\n    width: 24px;\n    height: 24px;\n    padding: 0;\n    top: 4px;\n  }\n  i {\n    display: block;\n    width: 24px;\n    height: 24px;\n    padding: 0;\n    position: relative;\n  }\n}\n</style>\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nfunction Context(indented, column, type, info, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.info = info;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type, info) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\" && type != \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, info, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\nfunction typeBefore(stream, state, pos) {\n  if (state.prevToken == \"variable\" || state.prevToken == \"type\") return true;\n  if (/\\S(?:[^- ]>|[*\\]])\\s*$|\\*$/.test(stream.string.slice(0, pos))) return true;\n  if (state.typeAtEndOfLine && stream.column() == stream.indentation()) return true;\n}\n\nfunction isTopScope(context) {\n  for (;;) {\n    if (!context || context.type == \"top\") return true;\n    if (context.type == \"}\" && context.prev.info != \"namespace\") return false;\n    context = context.prev;\n  }\n}\n\nCodeMirror.defineMode(\"clike\", function(config, parserConfig) {\n  var indentUnit = config.indentUnit,\n      statementIndentUnit = parserConfig.statementIndentUnit || indentUnit,\n      dontAlignCalls = parserConfig.dontAlignCalls,\n      keywords = parserConfig.keywords || {},\n      types = parserConfig.types || {},\n      builtin = parserConfig.builtin || {},\n      blockKeywords = parserConfig.blockKeywords || {},\n      defKeywords = parserConfig.defKeywords || {},\n      atoms = parserConfig.atoms || {},\n      hooks = parserConfig.hooks || {},\n      multiLineStrings = parserConfig.multiLineStrings,\n      indentStatements = parserConfig.indentStatements !== false,\n      indentSwitch = parserConfig.indentSwitch !== false,\n      namespaceSeparator = parserConfig.namespaceSeparator,\n      isPunctuationChar = parserConfig.isPunctuationChar || /[\\[\\]{}\\(\\),;\\:\\.]/,\n      numberStart = parserConfig.numberStart || /[\\d\\.]/,\n      number = parserConfig.number || /^(?:0x[a-f\\d]+|0b[01]+|(?:\\d+\\.?\\d*|\\.\\d+)(?:e[-+]?\\d+)?)(u|ll?|l|f)?/i,\n      isOperatorChar = parserConfig.isOperatorChar || /[+\\-*&%=<>!?|\\/]/,\n      isIdentifierChar = parserConfig.isIdentifierChar || /[\\w\\$_\\xa1-\\uffff]/,\n      // An optional function that takes a {string} token and returns true if it\n      // should be treated as a builtin.\n      isReservedIdentifier = parserConfig.isReservedIdentifier || false;\n\n  var curPunc, isDefKeyword;\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    if (numberStart.test(ch)) {\n      stream.backUp(1)\n      if (stream.match(number)) return \"number\"\n      stream.next()\n    }\n    if (isPunctuationChar.test(ch)) {\n      curPunc = ch;\n      return null;\n    }\n    if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      }\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (isOperatorChar.test(ch)) {\n      while (!stream.match(/^\\/[\\/*]/, false) && stream.eat(isOperatorChar)) {}\n      return \"operator\";\n    }\n    stream.eatWhile(isIdentifierChar);\n    if (namespaceSeparator) while (stream.match(namespaceSeparator))\n      stream.eatWhile(isIdentifierChar);\n\n    var cur = stream.current();\n    if (contains(keywords, cur)) {\n      if (contains(blockKeywords, cur)) curPunc = \"newstatement\";\n      if (contains(defKeywords, cur)) isDefKeyword = true;\n      return \"keyword\";\n    }\n    if (contains(types, cur)) return \"type\";\n    if (contains(builtin, cur)\n        || (isReservedIdentifier && isReservedIdentifier(cur))) {\n      if (contains(blockKeywords, cur)) curPunc = \"newstatement\";\n      return \"builtin\";\n    }\n    if (contains(atoms, cur)) return \"atom\";\n    return \"variable\";\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) {end = true; break;}\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = null;\n      return \"string\";\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = null;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return \"comment\";\n  }\n\n  function maybeEOL(stream, state) {\n    if (parserConfig.typeFirstDefinitions && stream.eol() && isTopScope(state.context))\n      state.typeAtEndOfLine = typeBefore(stream, state, stream.pos)\n  }\n\n  // Interface\n\n  return {\n    startState: function(basecolumn) {\n      return {\n        tokenize: null,\n        context: new Context((basecolumn || 0) - indentUnit, 0, \"top\", null, false),\n        indented: 0,\n        startOfLine: true,\n        prevToken: null\n      };\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (stream.eatSpace()) { maybeEOL(stream, state); return null; }\n      curPunc = isDefKeyword = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\" || style == \"meta\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if (curPunc == \";\" || curPunc == \":\" || (curPunc == \",\" && stream.match(/^\\s*(?:\\/\\/.*)?$/, false)))\n        while (state.context.type == \"statement\") popContext(state);\n      else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n      else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n      else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n      else if (curPunc == \"}\") {\n        while (ctx.type == \"statement\") ctx = popContext(state);\n        if (ctx.type == \"}\") ctx = popContext(state);\n        while (ctx.type == \"statement\") ctx = popContext(state);\n      }\n      else if (curPunc == ctx.type) popContext(state);\n      else if (indentStatements &&\n               (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != \";\") ||\n                (ctx.type == \"statement\" && curPunc == \"newstatement\"))) {\n        pushContext(state, stream.column(), \"statement\", stream.current());\n      }\n\n      if (style == \"variable\" &&\n          ((state.prevToken == \"def\" ||\n            (parserConfig.typeFirstDefinitions && typeBefore(stream, state, stream.start) &&\n             isTopScope(state.context) && stream.match(/^\\s*\\(/, false)))))\n        style = \"def\";\n\n      if (hooks.token) {\n        var result = hooks.token(stream, state, style);\n        if (result !== undefined) style = result;\n      }\n\n      if (style == \"def\" && parserConfig.styleDefs === false) style = \"variable\";\n\n      state.startOfLine = false;\n      state.prevToken = isDefKeyword ? \"def\" : style || curPunc;\n      maybeEOL(stream, state);\n      return style;\n    },\n\n    indent: function(state, textAfter) {\n      if (state.tokenize != tokenBase && state.tokenize != null || state.typeAtEndOfLine && isTopScope(state.context))\n        return CodeMirror.Pass;\n      var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n      var closing = firstChar == ctx.type;\n      if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n      if (parserConfig.dontIndentStatements)\n        while (ctx.type == \"statement\" && parserConfig.dontIndentStatements.test(ctx.info))\n          ctx = ctx.prev\n      if (hooks.indent) {\n        var hook = hooks.indent(state, ctx, textAfter, indentUnit);\n        if (typeof hook == \"number\") return hook\n      }\n      var switchBlock = ctx.prev && ctx.prev.info == \"switch\";\n      if (parserConfig.allmanIndentation && /[{(]/.test(firstChar)) {\n        while (ctx.type != \"top\" && ctx.type != \"}\") ctx = ctx.prev\n        return ctx.indented\n      }\n      if (ctx.type == \"statement\")\n        return ctx.indented + (firstChar == \"{\" ? 0 : statementIndentUnit);\n      if (ctx.align && (!dontAlignCalls || ctx.type != \")\"))\n        return ctx.column + (closing ? 0 : 1);\n      if (ctx.type == \")\" && !closing)\n        return ctx.indented + statementIndentUnit;\n\n      return ctx.indented + (closing ? 0 : indentUnit) +\n        (!closing && switchBlock && !/^(?:case|default)\\b/.test(textAfter) ? indentUnit : 0);\n    },\n\n    electricInput: indentSwitch ? /^\\s*(?:case .*?:|default:|\\{\\}?|\\})$/ : /^\\s*[{}]$/,\n    blockCommentStart: \"/*\",\n    blockCommentEnd: \"*/\",\n    blockCommentContinue: \" * \",\n    lineComment: \"//\",\n    fold: \"brace\"\n  };\n});\n\n  function words(str) {\n    var obj = {}, words = str.split(\" \");\n    for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n    return obj;\n  }\n  function contains(words, word) {\n    if (typeof words === \"function\") {\n      return words(word);\n    } else {\n      return words.propertyIsEnumerable(word);\n    }\n  }\n  var cKeywords = \"auto if break case register continue return default do sizeof \" +\n    \"static else struct switch extern typedef union for goto while enum const \" +\n    \"volatile inline restrict asm fortran\";\n\n  // Keywords from https://en.cppreference.com/w/cpp/keyword includes C++20.\n  var cppKeywords = \"alignas alignof and and_eq audit axiom bitand bitor catch \" +\n  \"class compl concept constexpr const_cast decltype delete dynamic_cast \" +\n  \"explicit export final friend import module mutable namespace new noexcept \" +\n  \"not not_eq operator or or_eq override private protected public \" +\n  \"reinterpret_cast requires static_assert static_cast template this \" +\n  \"thread_local throw try typeid typename using virtual xor xor_eq\";\n\n  var objCKeywords = \"bycopy byref in inout oneway out self super atomic nonatomic retain copy \" +\n  \"readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd \" +\n  \"@interface @implementation @end @protocol @encode @property @synthesize @dynamic @class \" +\n  \"@public @package @private @protected @required @optional @try @catch @finally @import \" +\n  \"@selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available\";\n\n  var objCBuiltins = \"FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION \" +\n  \" NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER \" +\n  \"NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION \" +\n  \"NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT\"\n\n  // Do not use this. Use the cTypes function below. This is global just to avoid\n  // excessive calls when cTypes is being called multiple times during a parse.\n  var basicCTypes = words(\"int long char short double float unsigned signed \" +\n    \"void bool\");\n\n  // Do not use this. Use the objCTypes function below. This is global just to avoid\n  // excessive calls when objCTypes is being called multiple times during a parse.\n  var basicObjCTypes = words(\"SEL instancetype id Class Protocol BOOL\");\n\n  // Returns true if identifier is a \"C\" type.\n  // C type is defined as those that are reserved by the compiler (basicTypes),\n  // and those that end in _t (Reserved by POSIX for types)\n  // http://www.gnu.org/software/libc/manual/html_node/Reserved-Names.html\n  function cTypes(identifier) {\n    return contains(basicCTypes, identifier) || /.+_t$/.test(identifier);\n  }\n\n  // Returns true if identifier is a \"Objective C\" type.\n  function objCTypes(identifier) {\n    return cTypes(identifier) || contains(basicObjCTypes, identifier);\n  }\n\n  var cBlockKeywords = \"case do else for if switch while struct enum union\";\n  var cDefKeywords = \"struct enum union\";\n\n  function cppHook(stream, state) {\n    if (!state.startOfLine) return false\n    for (var ch, next = null; ch = stream.peek();) {\n      if (ch == \"\\\\\" && stream.match(/^.$/)) {\n        next = cppHook\n        break\n      } else if (ch == \"/\" && stream.match(/^\\/[\\/\\*]/, false)) {\n        break\n      }\n      stream.next()\n    }\n    state.tokenize = next\n    return \"meta\"\n  }\n\n  function pointerHook(_stream, state) {\n    if (state.prevToken == \"type\") return \"type\";\n    return false;\n  }\n\n  // For C and C++ (and ObjC): identifiers starting with __\n  // or _ followed by a capital letter are reserved for the compiler.\n  function cIsReservedIdentifier(token) {\n    if (!token || token.length < 2) return false;\n    if (token[0] != '_') return false;\n    return (token[1] == '_') || (token[1] !== token[1].toLowerCase());\n  }\n\n  function cpp14Literal(stream) {\n    stream.eatWhile(/[\\w\\.']/);\n    return \"number\";\n  }\n\n  function cpp11StringHook(stream, state) {\n    stream.backUp(1);\n    // Raw strings.\n    if (stream.match(/^(?:R|u8R|uR|UR|LR)/)) {\n      var match = stream.match(/^\"([^\\s\\\\()]{0,16})\\(/);\n      if (!match) {\n        return false;\n      }\n      state.cpp11RawStringDelim = match[1];\n      state.tokenize = tokenRawString;\n      return tokenRawString(stream, state);\n    }\n    // Unicode strings/chars.\n    if (stream.match(/^(?:u8|u|U|L)/)) {\n      if (stream.match(/^[\"']/, /* eat */ false)) {\n        return \"string\";\n      }\n      return false;\n    }\n    // Ignore this hook.\n    stream.next();\n    return false;\n  }\n\n  function cppLooksLikeConstructor(word) {\n    var lastTwo = /(\\w+)::~?(\\w+)$/.exec(word);\n    return lastTwo && lastTwo[1] == lastTwo[2];\n  }\n\n  // C#-style strings where \"\" escapes a quote.\n  function tokenAtString(stream, state) {\n    var next;\n    while ((next = stream.next()) != null) {\n      if (next == '\"' && !stream.eat('\"')) {\n        state.tokenize = null;\n        break;\n      }\n    }\n    return \"string\";\n  }\n\n  // C++11 raw string literal is <prefix>\"<delim>( anything )<delim>\", where\n  // <delim> can be a string up to 16 characters long.\n  function tokenRawString(stream, state) {\n    // Escape characters that have special regex meanings.\n    var delim = state.cpp11RawStringDelim.replace(/[^\\w\\s]/g, '\\\\$&');\n    var match = stream.match(new RegExp(\".*?\\\\)\" + delim + '\"'));\n    if (match)\n      state.tokenize = null;\n    else\n      stream.skipToEnd();\n    return \"string\";\n  }\n\n  function def(mimes, mode) {\n    if (typeof mimes == \"string\") mimes = [mimes];\n    var words = [];\n    function add(obj) {\n      if (obj) for (var prop in obj) if (obj.hasOwnProperty(prop))\n        words.push(prop);\n    }\n    add(mode.keywords);\n    add(mode.types);\n    add(mode.builtin);\n    add(mode.atoms);\n    if (words.length) {\n      mode.helperType = mimes[0];\n      CodeMirror.registerHelper(\"hintWords\", mimes[0], words);\n    }\n\n    for (var i = 0; i < mimes.length; ++i)\n      CodeMirror.defineMIME(mimes[i], mode);\n  }\n\n  def([\"text/x-csrc\", \"text/x-c\", \"text/x-chdr\"], {\n    name: \"clike\",\n    keywords: words(cKeywords),\n    types: cTypes,\n    blockKeywords: words(cBlockKeywords),\n    defKeywords: words(cDefKeywords),\n    typeFirstDefinitions: true,\n    atoms: words(\"NULL true false\"),\n    isReservedIdentifier: cIsReservedIdentifier,\n    hooks: {\n      \"#\": cppHook,\n      \"*\": pointerHook,\n    },\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def([\"text/x-c++src\", \"text/x-c++hdr\"], {\n    name: \"clike\",\n    keywords: words(cKeywords + \" \" + cppKeywords),\n    types: cTypes,\n    blockKeywords: words(cBlockKeywords + \" class try catch\"),\n    defKeywords: words(cDefKeywords + \" class namespace\"),\n    typeFirstDefinitions: true,\n    atoms: words(\"true false NULL nullptr\"),\n    dontIndentStatements: /^template$/,\n    isIdentifierChar: /[\\w\\$_~\\xa1-\\uffff]/,\n    isReservedIdentifier: cIsReservedIdentifier,\n    hooks: {\n      \"#\": cppHook,\n      \"*\": pointerHook,\n      \"u\": cpp11StringHook,\n      \"U\": cpp11StringHook,\n      \"L\": cpp11StringHook,\n      \"R\": cpp11StringHook,\n      \"0\": cpp14Literal,\n      \"1\": cpp14Literal,\n      \"2\": cpp14Literal,\n      \"3\": cpp14Literal,\n      \"4\": cpp14Literal,\n      \"5\": cpp14Literal,\n      \"6\": cpp14Literal,\n      \"7\": cpp14Literal,\n      \"8\": cpp14Literal,\n      \"9\": cpp14Literal,\n      token: function(stream, state, style) {\n        if (style == \"variable\" && stream.peek() == \"(\" &&\n            (state.prevToken == \";\" || state.prevToken == null ||\n             state.prevToken == \"}\") &&\n            cppLooksLikeConstructor(stream.current()))\n          return \"def\";\n      }\n    },\n    namespaceSeparator: \"::\",\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def(\"text/x-java\", {\n    name: \"clike\",\n    keywords: words(\"abstract assert break case catch class const continue default \" +\n                    \"do else enum extends final finally for goto if implements import \" +\n                    \"instanceof interface native new package private protected public \" +\n                    \"return static strictfp super switch synchronized this throw throws transient \" +\n                    \"try volatile while @interface\"),\n    types: words(\"var byte short int long float double boolean char void Boolean Byte Character Double Float \" +\n                 \"Integer Long Number Object Short String StringBuffer StringBuilder Void\"),\n    blockKeywords: words(\"catch class do else finally for if switch try while\"),\n    defKeywords: words(\"class interface enum @interface\"),\n    typeFirstDefinitions: true,\n    atoms: words(\"true false null\"),\n    number: /^(?:0x[a-f\\d_]+|0b[01_]+|(?:[\\d_]+\\.?\\d*|\\.\\d+)(?:e[-+]?[\\d_]+)?)(u|ll?|l|f)?/i,\n    hooks: {\n      \"@\": function(stream) {\n        // Don't match the @interface keyword.\n        if (stream.match('interface', false)) return false;\n\n        stream.eatWhile(/[\\w\\$_]/);\n        return \"meta\";\n      },\n      '\"': function(stream, state) {\n        if (!stream.match(/\"\"$/)) return false;\n        state.tokenize = tokenTripleString;\n        return state.tokenize(stream, state);\n      }\n    },\n    modeProps: {fold: [\"brace\", \"import\"]}\n  });\n\n  def(\"text/x-csharp\", {\n    name: \"clike\",\n    keywords: words(\"abstract as async await base break case catch checked class const continue\" +\n                    \" default delegate do else enum event explicit extern finally fixed for\" +\n                    \" foreach goto if implicit in init interface internal is lock namespace new\" +\n                    \" operator out override params private protected public readonly record ref required return sealed\" +\n                    \" sizeof stackalloc static struct switch this throw try typeof unchecked\" +\n                    \" unsafe using virtual void volatile while add alias ascending descending dynamic from get\" +\n                    \" global group into join let orderby partial remove select set value var yield\"),\n    types: words(\"Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func\" +\n                 \" Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32\" +\n                 \" UInt64 bool byte char decimal double short int long object\"  +\n                 \" sbyte float string ushort uint ulong\"),\n    blockKeywords: words(\"catch class do else finally for foreach if struct switch try while\"),\n    defKeywords: words(\"class interface namespace record struct var\"),\n    typeFirstDefinitions: true,\n    atoms: words(\"true false null\"),\n    hooks: {\n      \"@\": function(stream, state) {\n        if (stream.eat('\"')) {\n          state.tokenize = tokenAtString;\n          return tokenAtString(stream, state);\n        }\n        stream.eatWhile(/[\\w\\$_]/);\n        return \"meta\";\n      }\n    }\n  });\n\n  function tokenTripleString(stream, state) {\n    var escaped = false;\n    while (!stream.eol()) {\n      if (!escaped && stream.match('\"\"\"')) {\n        state.tokenize = null;\n        break;\n      }\n      escaped = stream.next() == \"\\\\\" && !escaped;\n    }\n    return \"string\";\n  }\n\n  function tokenNestedComment(depth) {\n    return function (stream, state) {\n      var ch\n      while (ch = stream.next()) {\n        if (ch == \"*\" && stream.eat(\"/\")) {\n          if (depth == 1) {\n            state.tokenize = null\n            break\n          } else {\n            state.tokenize = tokenNestedComment(depth - 1)\n            return state.tokenize(stream, state)\n          }\n        } else if (ch == \"/\" && stream.eat(\"*\")) {\n          state.tokenize = tokenNestedComment(depth + 1)\n          return state.tokenize(stream, state)\n        }\n      }\n      return \"comment\"\n    }\n  }\n\n  def(\"text/x-scala\", {\n    name: \"clike\",\n    keywords: words(\n      /* scala */\n      \"abstract case catch class def do else extends final finally for forSome if \" +\n      \"implicit import lazy match new null object override package private protected return \" +\n      \"sealed super this throw trait try type val var while with yield _ \" +\n\n      /* package scala */\n      \"assert assume require print println printf readLine readBoolean readByte readShort \" +\n      \"readChar readInt readLong readFloat readDouble\"\n    ),\n    types: words(\n      \"AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either \" +\n      \"Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable \" +\n      \"Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering \" +\n      \"Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder \" +\n      \"StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector \" +\n\n      /* package java.lang */\n      \"Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable \" +\n      \"Compiler Double Exception Float Integer Long Math Number Object Package Pair Process \" +\n      \"Runtime Runnable SecurityManager Short StackTraceElement StrictMath String \" +\n      \"StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void\"\n    ),\n    multiLineStrings: true,\n    blockKeywords: words(\"catch class enum do else finally for forSome if match switch try while\"),\n    defKeywords: words(\"class enum def object package trait type val var\"),\n    atoms: words(\"true false null\"),\n    indentStatements: false,\n    indentSwitch: false,\n    isOperatorChar: /[+\\-*&%=<>!?|\\/#:@]/,\n    hooks: {\n      \"@\": function(stream) {\n        stream.eatWhile(/[\\w\\$_]/);\n        return \"meta\";\n      },\n      '\"': function(stream, state) {\n        if (!stream.match('\"\"')) return false;\n        state.tokenize = tokenTripleString;\n        return state.tokenize(stream, state);\n      },\n      \"'\": function(stream) {\n        if (stream.match(/^(\\\\[^'\\s]+|[^\\\\'])'/)) return \"string-2\"\n        stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n        return \"atom\";\n      },\n      \"=\": function(stream, state) {\n        var cx = state.context\n        if (cx.type == \"}\" && cx.align && stream.eat(\">\")) {\n          state.context = new Context(cx.indented, cx.column, cx.type, cx.info, null, cx.prev)\n          return \"operator\"\n        } else {\n          return false\n        }\n      },\n\n      \"/\": function(stream, state) {\n        if (!stream.eat(\"*\")) return false\n        state.tokenize = tokenNestedComment(1)\n        return state.tokenize(stream, state)\n      }\n    },\n    modeProps: {closeBrackets: {pairs: '()[]{}\"\"', triples: '\"'}}\n  });\n\n  function tokenKotlinString(tripleString){\n    return function (stream, state) {\n      var escaped = false, next, end = false;\n      while (!stream.eol()) {\n        if (!tripleString && !escaped && stream.match('\"') ) {end = true; break;}\n        if (tripleString && stream.match('\"\"\"')) {end = true; break;}\n        next = stream.next();\n        if(!escaped && next == \"$\" && stream.match('{'))\n          stream.skipTo(\"}\");\n        escaped = !escaped && next == \"\\\\\" && !tripleString;\n      }\n      if (end || !tripleString)\n        state.tokenize = null;\n      return \"string\";\n    }\n  }\n\n  def(\"text/x-kotlin\", {\n    name: \"clike\",\n    keywords: words(\n      /*keywords*/\n      \"package as typealias class interface this super val operator \" +\n      \"var fun for is in This throw return annotation \" +\n      \"break continue object if else while do try when !in !is as? \" +\n\n      /*soft keywords*/\n      \"file import where by get set abstract enum open inner override private public internal \" +\n      \"protected catch finally out final vararg reified dynamic companion constructor init \" +\n      \"sealed field property receiver param sparam lateinit data inline noinline tailrec \" +\n      \"external annotation crossinline const operator infix suspend actual expect setparam value\"\n    ),\n    types: words(\n      /* package java.lang */\n      \"Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable \" +\n      \"Compiler Double Exception Float Integer Long Math Number Object Package Pair Process \" +\n      \"Runtime Runnable SecurityManager Short StackTraceElement StrictMath String \" +\n      \"StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray \" +\n      \"ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy \" +\n      \"LazyThreadSafetyMode LongArray Nothing ShortArray Unit\"\n    ),\n    intendSwitch: false,\n    indentStatements: false,\n    multiLineStrings: true,\n    number: /^(?:0x[a-f\\d_]+|0b[01_]+|(?:[\\d_]+(\\.\\d+)?|\\.\\d+)(?:e[-+]?[\\d_]+)?)(u|ll?|l|f)?/i,\n    blockKeywords: words(\"catch class do else finally for if where try while enum\"),\n    defKeywords: words(\"class val var object interface fun\"),\n    atoms: words(\"true false null this\"),\n    hooks: {\n      \"@\": function(stream) {\n        stream.eatWhile(/[\\w\\$_]/);\n        return \"meta\";\n      },\n      '*': function(_stream, state) {\n        return state.prevToken == '.' ? 'variable' : 'operator';\n      },\n      '\"': function(stream, state) {\n        state.tokenize = tokenKotlinString(stream.match('\"\"'));\n        return state.tokenize(stream, state);\n      },\n      \"/\": function(stream, state) {\n        if (!stream.eat(\"*\")) return false;\n        state.tokenize = tokenNestedComment(1);\n        return state.tokenize(stream, state)\n      },\n      indent: function(state, ctx, textAfter, indentUnit) {\n        var firstChar = textAfter && textAfter.charAt(0);\n        if ((state.prevToken == \"}\" || state.prevToken == \")\") && textAfter == \"\")\n          return state.indented;\n        if ((state.prevToken == \"operator\" && textAfter != \"}\" && state.context.type != \"}\") ||\n          state.prevToken == \"variable\" && firstChar == \".\" ||\n          (state.prevToken == \"}\" || state.prevToken == \")\") && firstChar == \".\")\n          return indentUnit * 2 + ctx.indented;\n        if (ctx.align && ctx.type == \"}\")\n          return ctx.indented + (state.context.type == (textAfter || \"\").charAt(0) ? 0 : indentUnit);\n      }\n    },\n    modeProps: {closeBrackets: {triples: '\"'}}\n  });\n\n  def([\"x-shader/x-vertex\", \"x-shader/x-fragment\"], {\n    name: \"clike\",\n    keywords: words(\"sampler1D sampler2D sampler3D samplerCube \" +\n                    \"sampler1DShadow sampler2DShadow \" +\n                    \"const attribute uniform varying \" +\n                    \"break continue discard return \" +\n                    \"for while do if else struct \" +\n                    \"in out inout\"),\n    types: words(\"float int bool void \" +\n                 \"vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 \" +\n                 \"mat2 mat3 mat4\"),\n    blockKeywords: words(\"for while do if else struct\"),\n    builtin: words(\"radians degrees sin cos tan asin acos atan \" +\n                    \"pow exp log exp2 sqrt inversesqrt \" +\n                    \"abs sign floor ceil fract mod min max clamp mix step smoothstep \" +\n                    \"length distance dot cross normalize ftransform faceforward \" +\n                    \"reflect refract matrixCompMult \" +\n                    \"lessThan lessThanEqual greaterThan greaterThanEqual \" +\n                    \"equal notEqual any all not \" +\n                    \"texture1D texture1DProj texture1DLod texture1DProjLod \" +\n                    \"texture2D texture2DProj texture2DLod texture2DProjLod \" +\n                    \"texture3D texture3DProj texture3DLod texture3DProjLod \" +\n                    \"textureCube textureCubeLod \" +\n                    \"shadow1D shadow2D shadow1DProj shadow2DProj \" +\n                    \"shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod \" +\n                    \"dFdx dFdy fwidth \" +\n                    \"noise1 noise2 noise3 noise4\"),\n    atoms: words(\"true false \" +\n                \"gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex \" +\n                \"gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 \" +\n                \"gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 \" +\n                \"gl_FogCoord gl_PointCoord \" +\n                \"gl_Position gl_PointSize gl_ClipVertex \" +\n                \"gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor \" +\n                \"gl_TexCoord gl_FogFragCoord \" +\n                \"gl_FragCoord gl_FrontFacing \" +\n                \"gl_FragData gl_FragDepth \" +\n                \"gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix \" +\n                \"gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse \" +\n                \"gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse \" +\n                \"gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose \" +\n                \"gl_ProjectionMatrixInverseTranspose \" +\n                \"gl_ModelViewProjectionMatrixInverseTranspose \" +\n                \"gl_TextureMatrixInverseTranspose \" +\n                \"gl_NormalScale gl_DepthRange gl_ClipPlane \" +\n                \"gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel \" +\n                \"gl_FrontLightModelProduct gl_BackLightModelProduct \" +\n                \"gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ \" +\n                \"gl_FogParameters \" +\n                \"gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords \" +\n                \"gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats \" +\n                \"gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits \" +\n                \"gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits \" +\n                \"gl_MaxDrawBuffers\"),\n    indentSwitch: false,\n    hooks: {\"#\": cppHook},\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def(\"text/x-nesc\", {\n    name: \"clike\",\n    keywords: words(cKeywords + \" as atomic async call command component components configuration event generic \" +\n                    \"implementation includes interface module new norace nx_struct nx_union post provides \" +\n                    \"signal task uses abstract extends\"),\n    types: cTypes,\n    blockKeywords: words(cBlockKeywords),\n    atoms: words(\"null true false\"),\n    hooks: {\"#\": cppHook},\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def(\"text/x-objectivec\", {\n    name: \"clike\",\n    keywords: words(cKeywords + \" \" + objCKeywords),\n    types: objCTypes,\n    builtin: words(objCBuiltins),\n    blockKeywords: words(cBlockKeywords + \" @synthesize @try @catch @finally @autoreleasepool @synchronized\"),\n    defKeywords: words(cDefKeywords + \" @interface @implementation @protocol @class\"),\n    dontIndentStatements: /^@.*$/,\n    typeFirstDefinitions: true,\n    atoms: words(\"YES NO NULL Nil nil true false nullptr\"),\n    isReservedIdentifier: cIsReservedIdentifier,\n    hooks: {\n      \"#\": cppHook,\n      \"*\": pointerHook,\n    },\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def(\"text/x-objectivec++\", {\n    name: \"clike\",\n    keywords: words(cKeywords + \" \" + objCKeywords + \" \" + cppKeywords),\n    types: objCTypes,\n    builtin: words(objCBuiltins),\n    blockKeywords: words(cBlockKeywords + \" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch\"),\n    defKeywords: words(cDefKeywords + \" @interface @implementation @protocol @class class namespace\"),\n    dontIndentStatements: /^@.*$|^template$/,\n    typeFirstDefinitions: true,\n    atoms: words(\"YES NO NULL Nil nil true false nullptr\"),\n    isReservedIdentifier: cIsReservedIdentifier,\n    hooks: {\n      \"#\": cppHook,\n      \"*\": pointerHook,\n      \"u\": cpp11StringHook,\n      \"U\": cpp11StringHook,\n      \"L\": cpp11StringHook,\n      \"R\": cpp11StringHook,\n      \"0\": cpp14Literal,\n      \"1\": cpp14Literal,\n      \"2\": cpp14Literal,\n      \"3\": cpp14Literal,\n      \"4\": cpp14Literal,\n      \"5\": cpp14Literal,\n      \"6\": cpp14Literal,\n      \"7\": cpp14Literal,\n      \"8\": cpp14Literal,\n      \"9\": cpp14Literal,\n      token: function(stream, state, style) {\n        if (style == \"variable\" && stream.peek() == \"(\" &&\n            (state.prevToken == \";\" || state.prevToken == null ||\n             state.prevToken == \"}\") &&\n            cppLooksLikeConstructor(stream.current()))\n          return \"def\";\n      }\n    },\n    namespaceSeparator: \"::\",\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  def(\"text/x-squirrel\", {\n    name: \"clike\",\n    keywords: words(\"base break clone continue const default delete enum extends function in class\" +\n                    \" foreach local resume return this throw typeof yield constructor instanceof static\"),\n    types: cTypes,\n    blockKeywords: words(\"case catch class else for foreach if switch try while\"),\n    defKeywords: words(\"function local class\"),\n    typeFirstDefinitions: true,\n    atoms: words(\"true false null\"),\n    hooks: {\"#\": cppHook},\n    modeProps: {fold: [\"brace\", \"include\"]}\n  });\n\n  // Ceylon Strings need to deal with interpolation\n  var stringTokenizer = null;\n  function tokenCeylonString(type) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while (!stream.eol()) {\n        if (!escaped && stream.match('\"') &&\n              (type == \"single\" || stream.match('\"\"'))) {\n          end = true;\n          break;\n        }\n        if (!escaped && stream.match('``')) {\n          stringTokenizer = tokenCeylonString(type);\n          end = true;\n          break;\n        }\n        next = stream.next();\n        escaped = type == \"single\" && !escaped && next == \"\\\\\";\n      }\n      if (end)\n          state.tokenize = null;\n      return \"string\";\n    }\n  }\n\n  def(\"text/x-ceylon\", {\n    name: \"clike\",\n    keywords: words(\"abstracts alias assembly assert assign break case catch class continue dynamic else\" +\n                    \" exists extends finally for function given if import in interface is let module new\" +\n                    \" nonempty object of out outer package return satisfies super switch then this throw\" +\n                    \" try value void while\"),\n    types: function(word) {\n        // In Ceylon all identifiers that start with an uppercase are types\n        var first = word.charAt(0);\n        return (first === first.toUpperCase() && first !== first.toLowerCase());\n    },\n    blockKeywords: words(\"case catch class dynamic else finally for function if interface module new object switch try while\"),\n    defKeywords: words(\"class dynamic function interface module object package value\"),\n    builtin: words(\"abstract actual aliased annotation by default deprecated doc final formal late license\" +\n                   \" native optional sealed see serializable shared suppressWarnings tagged throws variable\"),\n    isPunctuationChar: /[\\[\\]{}\\(\\),;\\:\\.`]/,\n    isOperatorChar: /[+\\-*&%=<>!?|^~:\\/]/,\n    numberStart: /[\\d#$]/,\n    number: /^(?:#[\\da-fA-F_]+|\\$[01_]+|[\\d_]+[kMGTPmunpf]?|[\\d_]+\\.[\\d_]+(?:[eE][-+]?\\d+|[kMGTPmunpf]|)|)/i,\n    multiLineStrings: true,\n    typeFirstDefinitions: true,\n    atoms: words(\"true false null larger smaller equal empty finished\"),\n    indentSwitch: false,\n    styleDefs: false,\n    hooks: {\n      \"@\": function(stream) {\n        stream.eatWhile(/[\\w\\$_]/);\n        return \"meta\";\n      },\n      '\"': function(stream, state) {\n          state.tokenize = tokenCeylonString(stream.match('\"\"') ? \"triple\" : \"single\");\n          return state.tokenize(stream, state);\n        },\n      '`': function(stream, state) {\n          if (!stringTokenizer || !stream.match('`')) return false;\n          state.tokenize = stringTokenizer;\n          stringTokenizer = null;\n          return state.tokenize(stream, state);\n        },\n      \"'\": function(stream) {\n        stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n        return \"atom\";\n      },\n      token: function(_stream, state, style) {\n          if ((style == \"variable\" || style == \"type\") &&\n              state.prevToken == \".\") {\n            return \"variable-2\";\n          }\n        }\n    },\n    modeProps: {\n        fold: [\"brace\", \"import\"],\n        closeBrackets: {triples: '\"'}\n    }\n  });\n\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"))\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod)\n  else // Plain browser env\n    mod(CodeMirror)\n})(function(CodeMirror) {\n  \"use strict\"\n\n  CodeMirror.defineOption(\"autoRefresh\", false, function(cm, val) {\n    if (cm.state.autoRefresh) {\n      stopListening(cm, cm.state.autoRefresh)\n      cm.state.autoRefresh = null\n    }\n    if (val && cm.display.wrapper.offsetHeight == 0)\n      startListening(cm, cm.state.autoRefresh = {delay: val.delay || 250})\n  })\n\n  function startListening(cm, state) {\n    function check() {\n      if (cm.display.wrapper.offsetHeight) {\n        stopListening(cm, state)\n        if (cm.display.lastWrapHeight != cm.display.wrapper.clientHeight)\n          cm.refresh()\n      } else {\n        state.timeout = setTimeout(check, state.delay)\n      }\n    }\n    state.timeout = setTimeout(check, state.delay)\n    state.hurry = function() {\n      clearTimeout(state.timeout)\n      state.timeout = setTimeout(check, 50)\n    }\n    CodeMirror.on(window, \"mouseup\", state.hurry)\n    CodeMirror.on(window, \"keyup\", state.hurry)\n  }\n\n  function stopListening(_cm, state) {\n    clearTimeout(state.timeout)\n    CodeMirror.off(window, \"mouseup\", state.hurry)\n    CodeMirror.off(window, \"keyup\", state.hurry)\n  }\n});\n", "<template>\n  <codemirror :value=\"code\" :options=\"options\" />\n</template>\n<script lang=\"ts\" setup>\nimport { defineProps, reactive } from \"vue\";\nimport codemirror from \"codemirror-editor-vue3\";\nimport \"codemirror/mode/css/css.js\";\nimport \"codemirror/mode/clike/clike.js\";\n// import \"codemirror/mode/android/android.js\";\nimport \"codemirror/addon/display/autorefresh\";\nimport { EditorConfiguration } from \"codemirror\";\nimport \"codemirror/theme/dracula.css\";\nconst props = defineProps<{\n  code: string;\n  mode: string;\n}>();\n\nconst options = reactive<EditorConfiguration>({\n  smartIndent: true,\n  tabSize: 4,\n  mode: props.mode,\n  lineNumbers: false,\n  autoRefresh: true,\n  lineWrapping: true\n});\n</script>\n<style lang=\"less\" scoped></style>\n", "<template>\n  <el-tabs v-model=\"active\" class=\"demo-tabs\">\n    <el-tab-pane label=\"css\" name=\"css\">\n      <div class=\"css-panel code-item\" @click=\"emit('copy', layerData.css.join('\\r\\n'))\">\n        <CodeMirror mode=\"css\" :code=\"layerData.css.join('\\r\\n')\" />\n        <!-- <el-input type=\"textarea\" :rows=\"layerData.css.length + 1\" readonly :value=\"layerData.css.join('\\r\\n')\" /> -->\n      </div>\n    </el-tab-pane>\n    <el-tab-pane label=\"android\" name=\"android\">\n      <CodeMirror mode=\"css\" :code=\"codeObj.android\" @click=\"emit('copy', codeObj.android)\" />\n    </el-tab-pane>\n    <el-tab-pane label=\"ios\" name=\"ios\">\n      <CodeMirror mode=\"clike\" :code=\"codeObj.ios\" @click=\"emit('copy', codeObj.ios)\" />\n    </el-tab-pane>\n  </el-tabs>\n</template>\n<script lang=\"ts\" setup>\nimport { ObjectAny } from \"@/types\";\nimport { defineProps, ref, computed, defineEmits } from \"vue\";\nimport { LayerData } from \"../model/interfaces\";\nimport { scaleSize, unitSize } from \"../utils/helper\";\nimport CodeMirror from \"./codemirror.vue\";\nconst props = defineProps<{\n  layerData: LayerData;\n}>();\nconst emit = defineEmits([\"copy\"]);\nconst active = ref<string>(\"css\");\nconst codeObj = computed<ObjectAny>(() => {\n  const obj: ObjectAny = {};\n  const layerData = props.layerData;\n  switch (layerData.type) {\n    case \"text\":\n      obj.android = `<TextView\\r\\n${getAndroidWithHeight(layerData)}android:text=\"${layerData.content}\"\\r\\nandroid:textColor=\"${layerData.color[\"argb-hex\"]}\"\\r\\nandroid:textSize=\"${unitSize(layerData.fontSize, true)}\"\\r\\n/>`;\n      obj.ios = `UILabel *label = [[UILabel alloc] init];\\r\\nlabel.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\\r\\nlabel.text = @\"${\n        layerData.content\n      }\";\\r\\nlabel.font = [UIFont fontWithName:@\"${layerData.fontFace}\" size:${unitSize(layerData.fontSize)}];\\r\\nlabel.textColor = [UIColor colorWithRed:${layerData.color.rgb.r}/255.0 green:${layerData.color.rgb.g}/255.0 blue:${layerData.color.rgb.b}/255.0 alpha:${\n        layerData.color.alpha\n      }/255.0];\\r\\n`;\n      break;\n    case \"shape\":\n      obj.android = `<View\\r\\n${getAndroidWithHeight(layerData)}${getAndroidShapeBackground(layerData)}/>`;\n      obj.ios = `UIView *view = [[UIView alloc] init];\\r\\nview.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\\r\\n${getIOSShapeBackground(layerData)}`;\n      break;\n    case \"slice\":\n      obj.android = `<ImageView\\r\\n${getAndroidWithHeight(layerData)}${getAndroidImageSrc(layerData)}/>`;\n      obj.ios = `UIImageView *imageView = [[UIImageView alloc] init];\\r\\nimageView.frame = CGRectMake(${scaleSize(layerData.rect.x)},${scaleSize(layerData.rect.y)},${scaleSize(layerData.rect.width)},${scaleSize(layerData.rect.height)});\\r\\n${getIOSImageSrc(layerData)}`;\n      break;\n  }\n  return obj;\n});\nfunction getIOSImageSrc(layerData: LayerData) {\n  if (layerData.type != \"slice\" || typeof layerData.exportable == \"undefined\") return \"\";\n  return 'imageView.image = [UIImage imageNamed:@\"' + layerData.exportable[0].name + \".\" + layerData.exportable[0].format + '\"];\\r\\n';\n}\n\nfunction getAndroidImageSrc(layerData: LayerData) {\n  if (layerData.type != \"slice\" || typeof layerData.exportable == \"undefined\") return \"\";\n  return 'android:src=\"@mipmap/' + layerData.exportable[0].name + \".\" + layerData.exportable[0].format + '\"\\r\\n';\n}\nfunction getIOSShapeBackground(layerData: LayerData) {\n  let colorCode = \"\";\n  if (layerData.type != \"shape\" || typeof layerData.fills == \"undefined\" || layerData.fills.length == 0) return colorCode;\n  let f;\n  for (f in layerData.fills) {\n    if (layerData.fills[f].fillType.toLowerCase() == \"color\") {\n      return \"view.backgroundColor = [UIColor colorWithRed:\" + layerData.fills[f].color.rgb.r + \"/255.0 green:\" + layerData.fills[f].color.rgb.g + \"/255.0 blue:\" + layerData.fills[f].color.rgb.b + \"/255.0 alpha:\" + layerData.fills[f].color.alpha + \"/255.0];\\r\\n\";\n    }\n  }\n  return colorCode;\n}\nfunction getAndroidShapeBackground(layerData: LayerData) {\n  let colorCode = \"\";\n  if (layerData.type != \"shape\" || typeof layerData.fills == \"undefined\" || layerData.fills.length == 0) return colorCode;\n  let f;\n  for (f in layerData.fills) {\n    if (layerData.fills[f].fillType.toLowerCase() == \"color\") {\n      return 'android:background=\"' + layerData.fills[f].color[\"argb-hex\"] + '\"\\r\\n';\n    }\n  }\n  return colorCode;\n}\nfunction getAndroidWithHeight(layerData: LayerData) {\n  return 'android:layout_width=\"' + unitSize(layerData.rect.width, false) + '\"\\r\\n' + 'android:layout_height=\"' + unitSize(layerData.rect.height, false) + '\"\\r\\n';\n}\n</script>\n<style lang=\"less\"></style>\n", "<template>\n  <section class=\"exportable\">\n    <div class=\"exportable-title\">{{ localize(\"EXPORTABLE\") }}</div>\n    <div class=\"item-image\">\n      <img :src=\"layerData.exportable[0].path\" alt=\"\" />\n    </div>\n    <div v-for=\"(exp, i) in layerData.exportable\" :key=\"i\" class=\"item items-group\">\n      <div class=\"item\">\n        <div class=\"value-input\">\n          {{ exp.name }}\n        </div>\n        <el-button type=\"primary\" size=\"small\" @click.stop round @click=\"emit('copy', exp.path)\">复制链接</el-button>\n      </div>\n    </div>\n  </section>\n</template>\n<script lang=\"ts\" setup>\nimport { ObjectAny } from \"@/types\";\nimport { localize } from \"../model\";\nimport { defineProps, defineEmits } from \"vue\";\nconst emit = defineEmits([\"copy\"]);\ndefineProps<{\n  layerData: ObjectAny;\n}>();\n</script>\n<style lang=\"less\">\n.exportable {\n  &-title {\n    font-family: PingFangSC-Medium;\n    font-size: 16px;\n    color: #303233;\n    font-weight: 500;\n    margin-top: 16px;\n  }\n  .item-image {\n    width: 100%;\n    height: 80px;\n    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%), linear-gradient(45deg, rgba(0, 0, 0, 0.2) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.2) 75%);\n    background-position:\n      0 0,\n      5px 5px;\n    background-size: 10px 10px;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    img {\n      height: 50px;\n      object-fit: contain;\n    }\n  }\n  .item {\n    display: flex;\n    width: 100%;\n    align-items: center;\n    margin-top: 10px;\n    .value-input {\n      min-width: 107px;\n      height: 32px;\n      background: #f5f5fb;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #303233;\n      box-sizing: border-box;\n      padding: 0 12px;\n      line-height: 32px;\n      margin-right: 20px;\n    }\n  }\n}\n</style>\n", "<template>\n  <div @click.stop class=\"inspector\" id=\"inspector\" :class=\"{ show: !!layerData }\">\n    <template v-if=\"layerData\">\n      <div class=\"inspector-title\" @click.stop=\"toCopy(layerData.name)\">{{ layerData.name }}</div>\n      <Properties :layer-data=\"layerData\" @copy=\"toCopy\" />\n      <el-divider />\n      <template v-if=\"layerData.fills && layerData.fills.length\">\n        <Fills :layer-data=\"layerData\" @copy=\"toCopy\" />\n        <el-divider />\n      </template>\n      <template v-if=\"layerData.type == 'text'\">\n        <Font :layer-data=\"layerData\" @copy=\"toCopy\" />\n        <el-divider />\n      </template>\n      <template v-if=\"layerData.borders && layerData.borders.length\">\n        <Borders :layer-data=\"layerData\" @copy=\"toCopy\" />\n        <el-divider />\n      </template>\n      <template v-if=\"layerData.shadows && layerData.shadows.length\">\n        <Shadows :layer-data=\"layerData\" @copy=\"toCopy\" />\n        <el-divider />\n      </template>\n      <template v-if=\"layerData.css && layerData.css.length\">\n        <CodeTemplate :layer-data=\"layerData\" @copy=\"toCopy\" />\n        <el-divider />\n      </template>\n      <template v-if=\"layerData.exportable && layerData.exportable.length\">\n        <Exportable :layer-data=\"layerData\" @copy=\"toCopy\" />\n      </template>\n    </template>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, defineEmits } from \"vue\";\nimport { sketchStore } from \"@/store\";\nimport Properties from \"./components/properties.vue\";\nimport Fills from \"./components/fills.vue\";\nimport Font from \"./components/font.vue\";\nimport Borders from \"./components/borders.vue\";\nimport Shadows from \"./components/shadows.vue\";\nimport CodeTemplate from \"./components/codeTemplate.vue\";\nimport Exportable from \"./components/exportable.vue\";\nimport { LayerData } from \"./model/interfaces\";\nconst store = sketchStore();\nconst layerData = computed<LayerData | null>(() => {\n  const state = store.state;\n  const current = state.current;\n  if (state.selectedIndex === undefined || !current || !current.layers || !state.current.layers[state.selectedIndex]) {\n    return null;\n  }\n  return state.current.layers[state.selectedIndex];\n});\nconst emit = defineEmits([\"copy\"]);\nconst toCopy = (text: string) => {\n  emit(\"copy\", text);\n};\n</script>\n<style lang=\"less\" scoped>\n.inspector {\n  width: 340px;\n  height: 100%;\n  overflow: auto;\n  background: #fff;\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 999;\n  transition: transform 0.3s ease-in-out;\n  transform: translateX(100%);\n  padding: 16px 20px;\n  box-sizing: border-box;\n  box-shadow: -2px 10px 10px 0 rgba(0, 0, 0, 0.2);\n  &::-webkit-scrollbar {\n    display: none;\n  }\n\n  &.show {\n    transform: translateX(0);\n  }\n  &-title {\n    height: 50px;\n    width: 100%;\n    background: #f5f5fb;\n    border-radius: 4px;\n    line-height: 50px;\n    text-align: center;\n    font-size: 14px;\n    color: #303233;\n  }\n}\n</style>\n", "<template>\n  <div class=\"sketch-detail\">\n    <header class=\"sketch-nav\">\n      <div class=\"nav-left\">\n        <el-breadcrumb :separator-icon=\"ArrowRight\">\n          <el-breadcrumb-item v-if=\"team\" replace :to=\"indexPath\">{{ team?.name }}</el-breadcrumb-item>\n          <el-breadcrumb-item v-if=\"project\" replace :to=\"projectPath\">{{ project?.name }}</el-breadcrumb-item>\n          <el-breadcrumb-item>{{ activeSketch ? activeSketch.name : \"\" }}</el-breadcrumb-item>\n        </el-breadcrumb>\n        <!-- <el-button class=\"nav-button\" @click=\"router.back()\" type=\"info\" text> <i class=\"iconfont icon-zuojiantou\"></i></el-button> -->\n        <!-- {{ route.query.name }} -  -->\n        <!-- {{ activeSketch ? activeSketch.name : \"\" }} -->\n      </div>\n      <ul class=\"sketch-nav-menu\">\n        <li>\n          <el-button @click=\"historyHandler\" class=\"nav-button\" type=\"info\" text> <img src=\"https://static.soyoung.com/sy-design/7x8p5l27n6np1706495684052.png\" alt=\"\" /></el-button>\n        </li>\n        <li>\n          <el-button @click.stop=\"store.colorsVisible = !store.colorsVisible\" class=\"nav-button\" type=\"info\" text><img src=\"https://static.soyoung.com/sy-pre/2own6t3xax3eb-1717146600634.png\" alt=\"\" /> </el-button>\n        </li>\n        <li>\n          <el-button @click.stop=\"store.slicesVisible = !store.slicesVisible\" class=\"nav-button\" type=\"info\" text> <img src=\"https://static.soyoung.com/sy-design/9j6t50seztbc1706495684053.png\" alt=\"\" /></el-button>\n        </li>\n        <li>\n          <!-- 缩放组件 -->\n          <Scale @change=\"handleScale\" :num=\"store.state.zoom\" />\n        </li>\n      </ul>\n      <div class=\"nav-right\">\n        <!--        <Notify />-->\n        <el-button v-if=\"activeSketch\" style=\"margin-right: 25px; width: 60px\" type=\"primary\" @click=\"share\">分享</el-button>\n        <Shortcut type=\"detail\" />\n        <Unit />\n      </div>\n    </header>\n    <main v-if=\"permission\">\n      <ArtBoards :active-sketch-id=\"activeSketchId\" :group=\"store.group\" :group-data-list=\"groupDataList\" @change=\"handleArtBoardsChange\" />\n      <Slices @copy=\"toCopy\" :slices=\"store.project.slices\" @close=\"store.slicesVisible = false\" />\n      <Colors :color-format=\"store.state.colorFormat\" :colors=\"store.project.colors\" @close=\"store.colorsVisible = false\" />\n      <Screen />\n      <History :id=\"activeSketchId\" :loading=\"historyLoading\" :history=\"historyList\" @click=\"handleArtBoardsChange\" />\n      <Inspector @copy=\"toCopy\" />\n      <div id=\"cursor\" class=\"cursor\" style=\"display: none\"></div>\n    </main>\n    <div v-else class=\"board-placeholder\">\n      <el-empty description=\"暂无该项目权限，请联系项目管理员添加\" />\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { ref, onMounted, computed } from \"vue\";\nimport Scale from \"./sketch/scale.vue\";\nimport Unit from \"./sketch/unit.vue\";\nimport Shortcut from \"./components/shortcut.vue\";\nimport Slices from \"./sketch/slices.vue\";\nimport Colors from \"./sketch/colors.vue\";\nimport ArtBoards from \"./sketch/artboards.vue\";\nimport Screen from \"./sketch/screen.vue\";\nimport History from \"./sketch/history.vue\";\nimport Inspector from \"./sketch/inspector.vue\";\nimport Notify from \"./components/notify.vue\";\nimport { getSketchDetailById, getSketchGroupList, getSketchHistory, getProjectDetailById } from \"@/api/design\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { sketchStore, smbStore } from \"@/store\";\nimport hotkeys from \"hotkeys-js\";\nimport { documentClickEvent, documentMouseMoveEvent } from \"./sketch/utils/document\";\nimport { windowKeyDownEvent, windowKeyUpEvent, windowMouseMoveEvent, windowMouseDownEvent, windowMouseUpEvent } from \"./sketch/utils/window\";\nimport useClipboard from \"vue-clipboard3\";\nimport { ElNotification, ElMessage } from \"element-plus\";\nimport { ArrowRight } from \"@element-plus/icons-vue\";\nimport { invite } from \"./utils\";\nimport { ObjectAny } from \"@/types\";\nimport { Permission } from \"@/model\";\nimport { handleShare } from \"./utils\";\nconst { toClipboard } = useClipboard({\n  appendToBody: false\n});\nconst router = useRouter();\nlet notifytion: any = null;\nconst toCopy = async (text: string) => {\n  if (notifytion) {\n    notifytion.close();\n  }\n  await toClipboard(text);\n  notifytion = ElNotification({\n    title: \"复制成功\",\n    type: \"success\"\n  });\n};\nconst store = sketchStore();\nconst smbInfo = smbStore();\n// 切片弹窗\nconst route = useRoute();\nconst groupDataList = ref<any>([]);\nconst historyList = ref<any[]>([]);\nconst historyLoading = ref<boolean>(false);\nconst project = ref<ObjectAny | null>(null);\nconst activeSketchId = ref(\"\");\nconst teamId = ref<string>(\"\");\nconst team = computed(() => {\n  return smbInfo.teamList.find((item) => item._id == teamId.value);\n});\n\nconst indexPath = computed(() => {\n  return {\n    path: \"/item/project/index\",\n    query: {\n      teamId: teamId.value\n    }\n  };\n});\n\nconst projectPath = computed(() => {\n  return {\n    path: \"/item/project/stage\",\n    query: {\n      teamId: teamId.value,\n      projectId: project.value?._id\n    }\n  };\n});\nconst activeSketch = computed<any>(() => {\n  return groupDataList.value.find((item) => {\n    return item._id === activeSketchId.value;\n  });\n});\nconst permission = computed<Permission | null>(() => {\n  if (!team.value) {\n    return null;\n  }\n  return team.value!.permission;\n});\nconst share = () => {\n  const breadcrumb = [team.value!.name];\n  if (project.value) {\n    breadcrumb.push(project.value.name);\n  }\n  if (activeSketch.value) {\n    breadcrumb.push(activeSketch.value.pageName);\n    breadcrumb.push(activeSketch.value.name);\n  }\n  handleShare(team.value!, Permission.PREVIEW, `/#/item/project/detail?id=${route.query.id}&teamId=${teamId.value}&`, breadcrumb);\n};\nconst getSketchDetail = async (id: string, init = false) => {\n  const args = {\n    id: id\n  };\n  const res = await getSketchDetailById(args);\n  if (res.code == 0) {\n    const data = res.data;\n    data.artboards = [JSON.parse(data.artboard)];\n    data.slices = JSON.parse(data.slices);\n    data.colors = JSON.parse(data.colors);\n    store.initState(data);\n    if (init) {\n      getGroupListById(data.groupId);\n      getProjectById(data.projectId);\n    }\n  }\n};\n\nconst getProjectById = async (id: string) => {\n  const res = await getProjectDetailById({\n    id\n  });\n  if (res.code == 0) {\n    project.value = res.data;\n  }\n};\nconst historyHandler = async () => {\n  store.historyVisible = true;\n  historyLoading.value = true;\n  try {\n    const args = {\n      id: activeSketch.value.artId,\n      groupId: route.query.groupId\n    };\n    const res = await getSketchHistory(args);\n    if (res.code == 0) {\n      const data = res.data;\n      historyList.value = data;\n    }\n  } catch (e) {\n    ElMessage.error((e as any).message);\n  } finally {\n    historyLoading.value = false;\n  }\n};\nconst getGroupListById = async (id: string) => {\n  const args = {\n    groupId: id\n  };\n  const res = await getSketchGroupList(args);\n  if (res.code == 0) {\n    groupDataList.value = res.data;\n  }\n};\n// 切换画板\nconst handleArtBoardsChange = (id: string) => {\n  if (id == activeSketchId.value) {\n    return;\n  }\n  activeSketchId.value = id;\n  getSketchDetail(id);\n};\n///  大小\nconst handleScale = (num: number) => {\n  if (num < 0.25 || num > 4) {\n    return;\n  }\n  store.state.zoom = num;\n};\n\nonMounted(() => {\n  init();\n  layerEvents();\n});\n\nconst init = async () => {\n  const query = route.query as ObjectAny;\n  if (!query.id) {\n    return;\n  }\n  if (query.iv_id) {\n    const inviteTeamId = await invite(query.iv_id);\n    if (!inviteTeamId) {\n      return;\n    }\n    teamId.value = inviteTeamId;\n    router.replace({\n      path: route.path,\n      query: {\n        id: query.id,\n        teamId: inviteTeamId\n      }\n    });\n  } else {\n    teamId.value = query.teamId;\n  }\n  await smbInfo.init();\n  activeSketchId.value = query.id;\n  return getSketchDetail(query.id, true);\n};\nconst setHotKeys = () => {\n  hotkeys(\"option+s\", (event) => {\n    event.preventDefault();\n    store.slicesVisible = !store.slicesVisible;\n  });\n  hotkeys(\"option+c\", (event) => {\n    event.preventDefault();\n    store.colorsVisible = !store.colorsVisible;\n  });\n  hotkeys(\"option+h\", (event) => {\n    event.preventDefault();\n    if (store.historyVisible) {\n      store.historyVisible = false;\n    } else {\n      historyHandler();\n    }\n  });\n};\n// const removeHotKeys = () => {\n//   hotkeys.unbind(\"shift+n\", () => openGroupDialog());\n// };\n\nfunction layerEvents() {\n  document.body.addEventListener(\"click\", documentClickEvent);\n  document.body.addEventListener(\"mousemove\", documentMouseMoveEvent);\n  window.addEventListener(\"keydown\", windowKeyDownEvent);\n  window.addEventListener(\"keyup\", windowKeyUpEvent);\n  window.addEventListener(\"mousemove\", windowMouseMoveEvent);\n  window.addEventListener(\"mousedown\", windowMouseDownEvent);\n  window.addEventListener(\"mouseup\", windowMouseUpEvent);\n  setHotKeys();\n}\n</script>\n<style>\n.screen-viewer-inner {\n  background-color: #f0f2f5;\n}\n</style>\n<style lang=\"less\" scoped>\n* {\n  margin: 0;\n  padding: 0;\n}\n.sketch-detail {\n  width: 100%;\n  height: 100vh;\n  position: relative;\n  overflow: hidden;\n\n  .sketch-nav {\n    height: 48px;\n    background: #fff;\n    display: flex;\n    position: relative;\n    z-index: 215;\n    .nav-left {\n      align-items: center;\n      padding-left: 15px;\n      display: flex;\n      font-size: 14px;\n      .nav-button {\n        margin-right: 10px;\n      }\n    }\n    .nav-button {\n      img {\n        width: 24px;\n        height: 24px;\n      }\n      .iconfont {\n        font-size: 14px;\n      }\n    }\n    .sketch-nav-menu {\n      flex: 1;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      li {\n        position: relative;\n        padding: 0 15px;\n        height: 100%;\n        display: inline-flex;\n        align-items: center;\n        &:not(:last-child) {\n          &::after {\n            content: \"\";\n            height: 20px;\n            width: 1px;\n            background-color: #e3e6ec;\n            position: absolute;\n            right: 0;\n            top: 50%;\n            transform: translateY(-50%);\n          }\n        }\n      }\n    }\n    .nav-right {\n      display: flex;\n      justify-content: space-around;\n      align-items: center;\n      margin-right: 50px;\n    }\n  }\n  main {\n    height: calc(100vh - 48px);\n    position: relative;\n  }\n  .cursor {\n    position: absolute;\n    margin: -8px 0 0 -8px;\n    top: 50%;\n    left: 50%;\n    width: 16px;\n    height: 17px;\n    background-size: 16px 17px;\n    background-image: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAiCAMAAAAJbCvNAAABVlBMVEUAAAAGBgYBAQEBAQEAAAAAAAAAAAAnJycAAAA3NzcDAwMNDQ0AAAAAAAAAAAAhISEbGxsYGBgPDw8CAgIDAwMBAQEAAAAAAAAAAAAAAAAAAAAvLy8UFBQCAgIBAQEAAAAAAAAAAAAAAAAAAAA+Pj5BQUE/Pz8zMzMCAgIDAwMDAwMCAgIBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA7OzsAAAAAAAAAAAAAAAD///8AAABhYWEdHR36+vpVVVX9/f329vbz8/Pb29uBgYF0dHRjY2MsLCz39/ft7e3r6+vR0dFxcXFSUlJNTU3X19fKysq4uLh3d3dXV1dJSUlGRkZERETv7+/o6Ojj4+O8vLy1tbW0tLSnp6empqaTk5OKioqGhoZtbW1nZ2dcXFxPT0/Dw8PCwsKwsLCurq6pqamhoaGZmZmNjY16enpeXl5JnG7xAAAAPHRSTlMA/fj9B1EC/jf+/vw8Ew7+/fz85t26mpRoYyX9/OLMoHlKMiL+/f398enY0cC1tLGojouGVh4Z/qpyQy7UG/vLAAACHUlEQVQ4T33QVVdbURBA4bk3QjwQIIa7u9T2QDwQxx2K1+X/v/QhlzRpWN2v51tnZo1IS4ERx4b8J4epaht+5cHpiKzGRfzmxH55R0ftseERZwuIqGGY6xLTLKSNoTlV7W0WY7Z8qjRhflgxADrUOMjcarQJjGgZiqHecBfAuB4Cfb1NwKcHwJ0OdgKc6jWw1S0y5rLXgd18AIqGdtVBGdjpti+r9sTrYqkrBSS1H+BEz4GjnojmvnutQQ79AnxTL8CxFoCkqXnYmakD58AkkPEYADUPQFVDGajOWmsOaxboU4DnEEBN80Dfgoj4og7/pu0QeNQ08NxZ32QPEp41cS6rqvluqT8FX4MJIPcEkHQDn9Qh65rP7le1W/dpKVEAcraArHpTQE71nvZO50TWjAxARU9eAaGwiEtvARJTnQCpNKTPIFEEuNCoiCwYnwGKWYCKG7bGobILcK0+EfHPevcafz4FoboLW9sA9xoQEdmY6Si8gN9BSDZArad+xvf66wVsbTeBMyNiHXrIs9cMOixwp6MW8E9PWmBnG44scOFdlJdi+tMCXQ2QcE/HG8D5Vj8CkO+Axymo7HKoDvmbfd5zA5AoQeYcCqWsLSzNBeZtP2guZ/O3ALEvWlMoveksQ7JH/sk56K6DmjkQTHM8+C+Qof6rg8vLqweNumzHN+7uNhDWeisiMVN1oA3YR10un8+1KSISiI/Z28Dr/QHPa6Em8DfwfAAAAABJRU5ErkJggg==\");\n    background-repeat: no-repeat;\n    cursor: none;\n    z-index: 1999;\n  }\n\n  .cursor.show {\n    display: block;\n  }\n\n  .cursor.moving {\n    background-image: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAiCAMAAAAJbCvNAAABMlBMVEUAAAAAAAA2NjYsLCwCAgIAAAAAAAAMDAwBAQECAgIBAQECAgIBAQEBAQEAAAAAAAAAAAA5OTkgICAICAgAAAAcHBwCAgIAAAAAAAAAAAAAAAAAAABDQ0M8PDwxMTE0NDQVFRUCAgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPDw8DAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8AAABhYWEcHBz6+vr39/dMTEzs7Oy8vLy4uLhWVlYqKiooKCj09PTw8PC1tbWnp6d2dnZZWVn8/Pzp6enj4+Pd3d3a2trNzc2tra2pqammpqaKioqAgIBzc3NwcHBmZmZdXV1TU1NHR0c+Pj7W1tbR0dHGxsbAwMCampqSkpKPj4+GhoZycnJsbGwkJCTy91wiAAAANnRSTlMAPP7+/ZcN/Pnr487Kv7QlEv7+/fz71jk2GAcE/v7+/fzzpaCUkGZZQTEsIAn826uEg3JtXFGryOxIAAABkElEQVR4AeXS1XLbQBjF8RXIDHbNdiBpOCnDOa4kg2Oww9w2KUPf/xW6s8q2sTXT6X1+t99/NJozK+6lR8ZmRPzDZpxc3sg9y2Sez3aNl+mUsx23TjpzxbJtmnaqMR08ZO8mGuNn4JA8AU75YiXl5ISW5SXwgTwAUGIfcAuM3thpoRnsAJhnC8DcY0gWj3HBmg5qPAdw1nMBfJ9Aai4AQ1aFllzENNcD2jSEsuOkEvyIEB1k44XrEicI6bCigtXoIbzFN5D2PMAbAG4L0im3VVD2AQyOIF3lgeY88MOC9JXBEEkf2rsHwHtLRup749jtikvTQU8Hflkor/hJB00ZdHUwKKwKJRLP70FRh+7ubXDOLRHY4GU4wEE0I7Q1foPyVh4mQeCay1nxx1OeqWC8K6MF4MrCmK/FX/W0PQKCgfr7gLd/ZDvirkiaX3DXRTE381wzHEFp5UvHQPeJmNFImlD8WOJXH35SzFpZGnba7eFPrleK1yMzEQocBtaEWI+R4aC+Va0YRrWm/jm7Uxf/5zeExWvZghlTSgAAAABJRU5ErkJggg==\");\n  }\n}\n.board {\n  &-placeholder {\n    z-index: 300;\n    width: 100vw;\n    height: 100vh;\n    padding: 50px 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n}\n</style>\n"], "names": ["breadcrumbKey", "Symbol", "breadcrumbProps", "buildProps", "separator", "type", "String", "default", "separatorIcon", "iconPropType", "__default__", "defineComponent", "name", "Breadcrumb", "props", "setup", "__props", "ns", "useNamespace", "breadcrumb", "ref", "provide", "onMounted", "items", "value", "querySelectorAll", "e", "length", "setAttribute", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "class", "normalizeClass", "unref", "b", "role", "renderSlot", "$slots", "breadcrumbItemProps", "to", "definePropType", "Object", "replace", "Boolean", "BreadcrumbItem", "instance", "getCurrentInstance", "breadcrumbContext", "inject", "router", "appContext", "config", "globalProperties", "$router", "link", "onClick", "push", "_a", "_b", "createElementVNode", "is", "createBlock", "ElIcon", "key", "withCtx", "resolveDynamicComponent", "_", "toDisplayString", "ElBreadcrumb", "withInstall", "ElBreadcrumbItem", "withNoopInstall", "Timeline", "slots", "h", "timelineItemProps", "timestamp", "hideTimestamp", "center", "placement", "values", "color", "size", "icon", "hollow", "TimelineItem", "defaultNodeKls", "computed", "em", "dot", "createCommentVNode", "style", "normalizeStyle", "backgroundColor", "ElTimeline", "ElTimelineItem", "emit", "__emit", "hotkeys", "splitKey", "event", "preventDefault", "handleScaleChange", "handleCommand", "command", "num", "sum", "data", "Coordinate", "Overlay", "Influence", "Percentage", "Settings", "FLOW", "NOTES", "PROPERTIES", "FILLS", "TYPEFACE", "BORDERS", "SHADOWS", "EXPORTABLE", "Gradient", "Color", "Weight", "Custom", "Standard", "Points", "Retina", "Scale", "Units", "Positive", "Reverse", "Save", "<PERSON><PERSON><PERSON>", "Height", "Top", "Right", "Bottom", "Left", "Border", "Opacity", "<PERSON><PERSON>", "Shadow", "Style", "Line", "Typeface", "Character", "Paragraph", "<PERSON>", "All", "None", "Filter", "Export", "Position", "Size", "Family", "Spacing", "Content", "Offset", "Effect", "Blur", "Spread", "localize", "str", "Edge", "store", "sketchStore", "unitsData", "units", "unit", "scale", "curUnit", "cur", "state", "unitName", "find", "item", "handleUnitChange", "unitSize", "isText", "project", "pt", "resolution", "sz", "Math", "round", "split", "scaleSize", "getRect", "index", "current", "layers", "rect", "zoomSize", "zoom", "percentageSize", "size2", "distance", "selectedIndex", "targetIndex", "tempTargetRect", "selectedRect", "targetRect", "topData", "rightData", "bottomData", "leftData", "x", "width", "y", "height", "selectedX2", "selectedY2", "targetX2", "targetY2", "a", "x1", "max", "y1", "min", "getIntersection", "target", "top", "selected", "right", "bottom", "left", "abs", "percentageDistance", "w", "td", "document", "querySelector", "display", "tdDiv", "rd", "rdDiv", "bd", "bdDiv", "ld", "ldDiv", "selectedData", "classList", "add", "moveData", "panMode", "moving", "windowKeyDownEvent", "which", "cursor", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "windowKeyUpEvent", "remove", "windowMouseMoveEvent", "clientX", "clientY", "viewer", "scrollLeft", "scrollTop", "windowMouseDownEvent", "windowMouseUpEvent", "hideDistance", "for<PERSON>ach", "s", "el", "mouseoutLayer", "rulersHtml", "<PERSON><PERSON><PERSON><PERSON>", "documentClickEvent", "eventNode", "selector", "matches", "parentElement", "getEventTarget", "body", "stopPropagation", "slicesVisible", "historyVisible", "colorsVisible", "contains", "dataset", "objectid", "parseInt", "documentMouseMoveEvent", "getBoundingClientRect", "pageX", "pageY", "xScope", "yScope", "getEdgeRect", "rv", "offsetLeft", "offsetWidth", "rh", "offsetTop", "offsetHeight", "watch", "slices", "console", "log", "id", "text", "colors", "filterList", "dndTtemStatus", "searchInput", "inputRef", "searchInputStatus", "onClickBtn", "groupDataList", "activeSketchId", "nextTick", "domScrollToViewById", "flag", "focus", "handleClick", "handleOutsideClick", "handleSearch", "filter", "slug", "includes", "_hoisted_1", "_withScopeId", "_createElementVNode", "SMType", "computedStyle", "layer", "funcShow", "screenStyle", "imageData", "imagePath", "background", "backgroundSize", "screenInnerStyle", "reactive", "val", "isEmpty", "maxSize", "clientWidth", "clientHeight", "screen", "marginLeft", "marginTop", "suitHight", "immediate", "oldVal", "currentRect", "screenPoint", "viewerRect", "screenRect", "viewerCenter", "screenPointOnViewerCenter", "screenPoint2", "options", "fromRect", "from", "fromEdge", "toEdge", "fromHasV", "toHasV", "offsetX", "offsetY", "toRect", "hcenter", "hright", "vmiddle", "v<PERSON><PERSON>", "scroller", "alignElement", "hleft", "vtop", "time", "Date", "toLocaleString", "CodeMirror", "Context", "indented", "column", "info", "align", "prev", "this", "pushContext", "col", "indent", "context", "popContext", "t", "typeBefore", "stream", "pos", "prevToken", "test", "string", "slice", "typeAtEndOfLine", "indentation", "isTopScope", "words", "obj", "i", "word", "propertyIsEnumerable", "defineMode", "parserConfig", "curPunc", "isDefKeyword", "indentUnit", "statementIndentUnit", "dontAlignCalls", "keywords", "types", "builtin", "blockKeywords", "defKeywords", "atoms", "hooks", "multiLineStrings", "indentStatements", "indentSwitch", "namespaceSeparator", "isPunctuationChar", "numberStart", "number", "isOperatorChar", "isIdentifierChar", "isReservedIdentifier", "tokenBase", "ch", "next", "result", "tokenize", "tokenString", "backUp", "match", "eat", "tokenComment", "skipToEnd", "eat<PERSON>hile", "quote", "escaped", "end", "maybeEnd", "maybeEOL", "typeFirstDefinitions", "eol", "startState", "basecolumn", "startOfLine", "token", "ctx", "sol", "eatSpace", "start", "styleDefs", "textAfter", "Pass", "firstChar", "char<PERSON>t", "closing", "dontIndentStatements", "hook", "switchBlock", "allmanIndentation", "electricInput", "blockCommentStart", "blockCommentEnd", "blockCommentContinue", "lineComment", "fold", "cKeywords", "cppKeywords", "objCKeywords", "objCBuiltins", "basicCTypes", "basicObjCTypes", "cTypes", "identifier", "objCTypes", "cBlockKeywords", "cDefKeywords", "cppHook", "peek", "pointer<PERSON>ook", "_stream", "cIsReservedIdentifier", "toLowerCase", "cpp14Literal", "cpp11StringHook", "cpp11RawStringDelim", "tokenRawString", "cppLooksLikeConstructor", "lastTwo", "exec", "tokenAtString", "delim", "RegExp", "def", "mimes", "mode", "prop", "hasOwnProperty", "helperType", "registerHelper", "defineMIME", "tokenTripleString", "tokenNestedComment", "depth", "tokenKotlinString", "tripleString", "skip<PERSON>o", "modeProps", "u", "U", "L", "R", "cx", "closeBrackets", "pairs", "triples", "intendSwitch", "stringTokenizer", "tokenCeylonString", "first", "toUpperCase", "mod", "require$$0", "startListening", "cm", "check", "wrapper", "stopListening", "lastWrapHeight", "refresh", "timeout", "setTimeout", "delay", "hurry", "clearTimeout", "on", "window", "_cm", "off", "defineOption", "autoRefresh", "smartIndent", "tabSize", "lineNumbers", "lineWrapping", "active", "codeObj", "layerData", "android", "getAndroidWithHeight", "content", "fontSize", "ios", "fontFace", "rgb", "r", "g", "alpha", "f", "colorCode", "fills", "fillType", "getAndroidShapeBackground", "getIOSShapeBackground", "exportable", "format", "getAndroidImageSrc", "getIOSImageSrc", "toCopy", "toClipboard", "useClipboard", "appendToBody", "useRouter", "notifytion", "async", "close", "ElNotification", "title", "smbInfo", "smbStore", "route", "useRoute", "historyList", "historyLoading", "teamId", "team", "teamList", "_id", "indexPath", "path", "query", "projectPath", "projectId", "activeSketch", "permission", "share", "pageName", "handleShare", "Permission", "PREVIEW", "getSketchDetail", "init2", "args", "res", "getSketchDetailById", "code", "artboards", "JSON", "parse", "artboard", "initState", "getGroupListById", "groupId", "getProjectById", "getProjectDetailById", "<PERSON><PERSON><PERSON><PERSON>", "artId", "getSketchHistory", "ElMessage", "error", "message", "getSketchGroupList", "handleArtBoardsChange", "handleScale", "addEventListener", "init", "iv_id", "inviteTeamId", "invite"], "mappings": "8wCAAA,MAAMA,GAAgBC,OAAO,iBCIvBC,GAAkBC,EAAW,CACjCC,UAAW,CACTC,KAAMC,OACNC,QAAS,KAEXC,cAAe,CACbH,KAAMI,KCHJC,GAAcC,EAAgB,CAClCC,KAAM,iBA6BR,IAAIC,KA3B8CF,EAAA,IAC7CD,GACHI,MAAOZ,GACP,KAAAa,CAAMC,GACJ,MAAMF,EAAQE,EACRC,EAAKC,EAAa,cAClBC,EAAaC,IAQZ,OAPPC,EAAQrB,GAAec,GACvBQ,GAAU,KACF,MAAAC,EAAQJ,EAAWK,MAAMC,iBAAiB,IAAIR,EAAGS,EAAE,WACrDH,EAAMI,QACRJ,EAAMA,EAAMI,OAAS,GAAGC,aAAa,eAAgB,OACtD,IAEI,CAACC,EAAMC,KACLC,IAAaC,EAAmB,MAAO,CAC5CC,QAAS,aACTb,IAAKD,EACLe,MAAOC,EAAeC,EAAMnB,GAAIoB,KAChC,aAAc,aACdC,KAAM,cACL,CACDC,EAAWV,EAAKW,OAAQ,YACvB,GAEN,IAEqD,CAAC,CAAC,SAAU,oBClCpE,MAAMC,GAAsBtC,EAAW,CACrCuC,GAAI,CACFrC,KAAMsC,EAAe,CAACrC,OAAQsC,SAC9BrC,QAAS,IAEXsC,QAAS,CACPxC,KAAMyC,QACNvC,SAAS,KCFPG,GAAcC,EAAgB,CAClCC,KAAM,qBAgDR,IAAImC,KA9C8CpC,EAAA,IAC7CD,GACHI,MAAO2B,GACP,KAAA1B,CAAMC,GACJ,MAAMF,EAAQE,EACRgC,EAAWC,IACXC,EAAoBC,EAAOnD,QAAe,GAC1CiB,EAAKC,EAAa,cAClBkC,EAASJ,EAASK,WAAWC,OAAOC,iBAAiBC,QACrDC,EAAOrC,IACPsC,EAAU,KACT5C,EAAM4B,IAAOU,IAEZtC,EAAA+B,QAAUO,EAAOP,QAAQ/B,EAAM4B,IAAMU,EAAOO,KAAK7C,EAAM4B,IAAE,EAE1D,MAAA,CAACb,EAAMC,KACZ,IAAI8B,EAAIC,EACD,OAAA9B,IAAaC,EAAmB,OAAQ,CAC7CE,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,UACjC,CACDoC,EAAmB,OAAQ,CACzB7B,QAAS,OACTb,IAAKqC,EACLvB,MAAOC,EAAe,CAACC,EAAMnB,GAAIS,EAAE,SAAUU,EAAMnB,GAAI8C,GAAG,SAAUlC,EAAKa,MACzEJ,KAAM,OACNoB,WACC,CACDnB,EAAWV,EAAKW,OAAQ,YACvB,IACiC,OAAlCoB,EAAKxB,EAAMc,SAA8B,EAASU,EAAGpD,gBAAkBuB,IAAaiC,EAAY5B,EAAM6B,GAAS,CAC/GC,IAAK,EACLhC,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,eACjC,CACDnB,QAAS4D,GAAQ,IAAM,EACpBpC,IAAaiC,EAAYI,EAAwBhC,EAAMc,GAAmB1C,oBAE7E6D,EAAG,GACF,EAAG,CAAC,YAActC,IAAaC,EAAmB,OAAQ,CAC3DkC,IAAK,EACLhC,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,cAClCY,KAAM,gBACLgC,EAAmD,OAAlCT,EAAKzB,EAAMc,SAA8B,EAASW,EAAGzD,WAAY,KACpF,EAAC,CAEP,IAEyD,CAAC,CAAC,SAAU,yBCjDxE,MAAMmE,GAAeC,EAAY3D,GAAY,CAC3CkC,oBAEI0B,GAAmBC,EAAgB3B,ICPnC4B,GAAWhE,EAAgB,CAC/BC,KAAM,aACN,KAAAG,CAAMsD,GAAGO,MAAEA,IACH,MAAA3D,EAAKC,EAAa,YAExB,OADAG,EAAQ,WAAYuD,GACb,IACEC,EAAE,KAAM,CAAE3C,MAAO,CAACjB,EAAGoB,MAAQ,CAACE,EAAWqC,EAAO,YAE1D,ICRGE,GAAoB3E,EAAW,CACnC4E,UAAW,CACT1E,KAAMC,OACNC,QAAS,IAEXyE,cAAe,CACb3E,KAAMyC,QACNvC,SAAS,GAEX0E,OAAQ,CACN5E,KAAMyC,QACNvC,SAAS,GAEX2E,UAAW,CACT7E,KAAMC,OACN6E,OAAQ,CAAC,MAAO,UAChB5E,QAAS,UAEXF,KAAM,CACJA,KAAMC,OACN6E,OAAQ,CAAC,UAAW,UAAW,UAAW,SAAU,QACpD5E,QAAS,IAEX6E,MAAO,CACL/E,KAAMC,OACNC,QAAS,IAEX8E,KAAM,CACJhF,KAAMC,OACN6E,OAAQ,CAAC,SAAU,SACnB5E,QAAS,UAEX+E,KAAM,CACJjF,KAAMI,GAER8E,OAAQ,CACNlF,KAAMyC,QACNvC,SAAS,KClCPG,GAAcC,EAAgB,CAClCC,KAAM,mBAiER,IAAI4E,KA/D8C7E,EAAA,IAC7CD,GACHI,MAAOgE,GACP,KAAA/D,CAAMC,GACJ,MAAMF,EAAQE,EACRC,EAAKC,EAAa,iBAClBuE,EAAiBC,GAAS,IAAM,CACpCzE,EAAGS,EAAE,QACLT,EAAG0E,GAAG,OAAQ7E,EAAMuE,MAAQ,IAC5BpE,EAAG0E,GAAG,OAAQ7E,EAAMT,MAAQ,IAC5BY,EAAG8C,GAAG,SAAUjD,EAAMyE,WAEjB,MAAA,CAAC1D,EAAMC,KACLC,IAAaC,EAAmB,KAAM,CAC3CE,MAAOC,EAAe,CAACC,EAAMnB,GAAIoB,IAAK,CAAE,CAACD,EAAMnB,GAAIS,EAAE,WAAYG,EAAKoD,WACrE,CACDnB,EAAmB,MAAO,CACxB5B,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,UACjC,KAAM,GACRG,EAAKW,OAAOoD,IAgBJC,EAAmB,QAAQ,IAhBhB9D,IAAaC,EAAmB,MAAO,CACzDkC,IAAK,EACLhC,MAAOC,EAAeC,EAAMqD,IAC5BK,MAAOC,EAAe,CACpBC,gBAAiBnE,EAAKuD,SAEvB,CACDvD,EAAKyD,MAAQvD,IAAaiC,EAAY5B,EAAM6B,GAAS,CACnDC,IAAK,EACLhC,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,UACjC,CACDnB,QAAS4D,GAAQ,IAAM,EACpBpC,IAAaiC,EAAYI,EAAwBvC,EAAKyD,WAEzDjB,EAAG,GACF,EAAG,CAAC,WAAawB,EAAmB,QAAQ,IAC9C,IACHhE,EAAKW,OAAOoD,KAAO7D,IAAaC,EAAmB,MAAO,CACxDkC,IAAK,EACLhC,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,SACjC,CACDa,EAAWV,EAAKW,OAAQ,QACvB,IAAMqD,EAAmB,QAAQ,GACpC/B,EAAmB,MAAO,CACxB5B,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,aACjC,CACAG,EAAKmD,eAAoC,QAAnBnD,EAAKqD,UAGcW,EAAmB,QAAQ,IAHlB9D,IAAaC,EAAmB,MAAO,CACxFkC,IAAK,EACLhC,MAAOC,EAAe,CAACC,EAAMnB,GAAIS,EAAE,aAAcU,EAAMnB,GAAI8C,GAAG,UAC7DO,EAAgBzC,EAAKkD,WAAY,IACpCjB,EAAmB,MAAO,CACxB5B,MAAOC,EAAeC,EAAMnB,GAAIS,EAAE,aACjC,CACDa,EAAWV,EAAKW,OAAQ,YACvB,GACFX,EAAKmD,eAAoC,WAAnBnD,EAAKqD,UAGcW,EAAmB,QAAQ,IAHf9D,IAAaC,EAAmB,MAAO,CAC3FkC,IAAK,EACLhC,MAAOC,EAAe,CAACC,EAAMnB,GAAIS,EAAE,aAAcU,EAAMnB,GAAI8C,GAAG,aAC7DO,EAAgBzC,EAAKkD,WAAY,KACnC,IACF,GAEN,IAEuD,CAAC,CAAC,SAAU,uBCnEtE,MAAMkB,GAAazB,EAAYG,GAAU,CACvCa,kBAEIU,GAAiBxB,EAAgBc,yoBC4BvC,MAAA1E,EAAAE,EAGAmF,EAAAC,EACA9E,GAAA,KACE+E,GAAA,YAAA,CAAAC,SAAA,MAAAC,IACEA,EAAAC,iBACAC,EAAA,IAAA,IAEFJ,GAAA,aAAAE,IACEA,EAAAC,iBACAC,GAAA,IAAA,GAAuB,IAG3B,MAAAC,EAAAC,IACE,OAAAA,GAAiB,IAAA,IAEbF,EAAA,KACA,MAAA,IAAA,IAEAA,GAAA,KACA,MAAA,IAAA,IAEA,MAAA,IAAA,IAEAN,EAAA,SAAA,GACA,MAAA,IAAA,IAEAA,EAAA,SAAA,KACA,MAAA,IAAA,IAEAA,EAAA,SAAA,GACA,MAAA,IAAA,IAEAA,EAAA,SAAA,KACA,MAAA,IAAA,IAEAA,EAAA,SAAA,IACA,MAAA,IAAA,IAEAA,EAAA,SAAA,KAGA,EAGNM,EAAAG,IACE,MAAAC,EAAA/F,EAAA8F,IAAAA,EACAC,EAAA,KAAAA,EAAA,GAGAV,EAAA,SAAAU,EAAA,upCCtFWC,GAAkB,CAC7BC,WAAY,KACZC,QAAS,OACT,YAAa,QACb,eAAgB,QAChB,eAAgB,QAChB,cAAe,QACf,gBAAiB,QACjB,eAAgB,QAChB,oBAAqB,OACrB,cAAe,MACf,iBAAkB,MAClB,sBAAuB,OACvB,eAAgB,MAChB,gBAAiB,MACjB,eAAgB,QAChB,iBAAkB,QAClB,YAAa,KACb,kBAAmB,QACnB,gBAAiB,QACjBC,UAAW,OACX,sBAAuB,aACvBC,WAAY,MACZ,uBAAwB,UACxB,gBAAiB,OACjB,gBAAiB,OACjB,cAAe,cACfC,SAAU,KACV,oBAAqB,QACrB,cAAe,OACf,gBAAiB,OACjB,oBAAqB,SACrB,iBAAkB,YAClBC,KAAM,OACNC,MAAO,KACPC,WAAY,KACZC,MAAO,KACPC,SAAU,KACVC,QAAS,KACTC,QAAS,KACT,YAAa,SACb,gBAAiB,OACjBC,WAAY,KACZC,SAAU,KACVC,MAAO,KACP,aAAc,OACdC,OAAQ,KACR,aAAc,OACdC,OAAQ,MACRC,SAAU,OACV,cAAe,SACfC,OAAQ,MACRC,OAAQ,MACR,YAAa,QACb,kBAAmB,OACnB,gBAAiB,OACjB,cAAe,YACf,WAAY,KACZC,MAAO,KACPC,MAAO,KACP,cAAe,OACf,oBAAqB,QACrB,iCAAkC,uBAClC,eAAgB,OAChB,YAAa,KACb,WAAY,OACZ,iBAAkB,OAClB,yBAA0B,SAC1B,4BAA6B,SAC7B,oBAAqB,QACrB,uBAAwB,QACxBC,SAAU,KACVC,QAAS,KACTC,KAAM,KACNC,MAAO,KACPC,OAAQ,KACRC,IAAK,KACLC,MAAO,KACPC,OAAQ,KACRC,KAAM,KACN,eAAgB,UAChBC,OAAQ,KACRC,QAAS,OACTC,OAAQ,KACRC,OAAQ,SACRC,MAAO,OACP,YAAa,KACbC,KAAM,KACNC,SAAU,KACVC,UAAW,MACXC,UAAW,OACX,yBAA0B,YAC1BC,KAAM,KACNC,IAAK,KACLC,KAAM,MACN,kBAAmB,QACnB,sBAAuB,eACvB,oBAAqB,eACrBC,OAAQ,MACRC,OAAQ,KACRC,SAAU,KACVC,KAAM,KACNC,OAAQ,KACRC,QAAS,KACTC,QAAS,KACT,gBAAiB,OACjB,eAAgB,OAChB,mBAAoB,QACpB,wBAAyB,UACzB,gCAAiC,iBACjC,+BAAgC,eAChC,0BAA2B,aAC3B,4BAA6B,YAC7B,cAAe,OACf,aAAc,OACd,eAAgB,SAChB,sCAAuC,aACvC,oBAAqB,UACrB,oCAAqC,gBACrC,qCAAsC,WACtC,gBAAiB,MACjB,iBAAkB,MAClB,gBAAiB,OACjB,eAAgB,MAChB,eAAgB,MAChBC,OAAQ,KACRC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACR,gBAAiB,OACjB,+BAAgC,YAChC,mBAAoB,SACpB,oCAAqC,aACrC,mBAAoB,QACpB,4BAA6B,mBAC7B,gBAAiB,OACjB,8BAA+B,YAC/B,oBAAqB,OCnHhB,SAASC,GAASC,GACvB,OAAOxD,GAAKwD,GAAOxD,GAAKwD,GAAOA,CACjC,CAEY,IAAAC,IAAAA,IACVA,EAAAA,OAAO,IAAP,OACAA,EAAAA,UAAU,IAAV,UACAA,EAAAA,UAAU,GAAV,UACAA,EAAAA,QAAQ,GAAR,QACAA,EAAAA,SAAS,GAAT,SACAA,EAAAA,UAAU,GAAV,UANUA,IAAAA,IAAA,CAAA,yFCHZ,MAAAC,EAAAC,IAEAC,EAAA,CAAkB,CAChB9J,KAAAyJ,GAAA,iBACgCM,MAAA,CACvB,CAAA/J,KAAAyJ,GAAA,YAAA,QAAAO,KAAA,KAAAC,MAAA,GACwD,CAAAjK,KAAAyJ,GAAA,eAAA,QAAAO,KAAA,KAAAC,MAAA,GACG,CAAAjK,KAAAyJ,GAAA,mBAAA,WAAAO,KAAA,QAAAC,MAAA,KAGpE,CACAjK,KAAAyJ,GAAA,qBACoCM,MAAA,CAC3B,CAAA/J,KAAA,OAAAyJ,GAAA,UAAA,OAAAO,KAAA,KAAAC,MAAA,GAC8D,CAAAjK,KAAA,OAAAyJ,GAAA,UAAA,OAAAO,KAAA,KAAAC,MAAA,GACA,CAAAjK,KAAA,OAAAyJ,GAAA,aAAA,OAAAO,KAAA,KAAAC,MAAA,GACG,CAAAjK,KAAA,sBAAAgK,KAAA,KAAAC,MAAA,KACf,CAAAjK,KAAA,mBAAAgK,KAAA,KAAAC,MAAA,GACN,CAAAjK,KAAA,qBAAAgK,KAAA,KAAAC,MAAA,KACI,CAAAjK,KAAA,oBAAAgK,KAAA,KAAAC,MAAA,GACH,CAAAjK,KAAA,qBAAAgK,KAAA,KAAAC,MAAA,GACC,CAAAjK,KAAA,sBAAAgK,KAAA,KAAAC,MAAA,KAGvD,CACAjK,KAAAyJ,GAAA,qBACoCM,MAAA,CAC3B,CAAA/J,KAAA,cAAAgK,KAAA,MAAAC,MAAA,EAAA,GAC4C,CAAAjK,KAAA,eAAAgK,KAAA,MAAAC,MAAA,IACE,CAAAjK,KAAA,eAAAgK,KAAA,MAAAC,MAAA,EAAA,IACA,CAAAjK,KAAA,eAAAgK,KAAA,MAAAC,MAAA,EAAA,IACA,CAAAjK,KAAA,eAAAgK,KAAA,MAAAC,MAAA,EAAA,IACA,CAAAjK,KAAAyJ,GAAA,eAAAO,KAAA,KAAAC,MAAA,EAAA,OAKzDC,EAAApF,GAAA,KACE,IAAAqF,EAAA,KACA,OAAAP,EAAAQ,MAAAC,UAGAP,EAAAQ,MAAAC,IACE,MAAArE,EAAAqE,EAAAR,MAAAO,MAAAN,GACEA,EAAAA,MAAAJ,EAAAQ,MAAAJ,MAAAA,EAAAhK,MAAA4J,EAAAQ,MAAAC,WAEF,GAAAnE,EAEE,OADAA,EAAAA,GACA,CAAO,IAGXiE,GAXEL,EAAA,GAAAC,MAAA,EAWF,IAEFS,EAAAR,IACEJ,EAAAQ,MAAAJ,KAAAA,EAAAA,KACAJ,EAAAQ,MAAAH,MAAAD,EAAAC,MACAL,EAAAQ,MAAAC,SAAAL,EAAAhK,IAAA,0nBCpEc,SAAAyK,GAAS7J,EAAe8J,GACtC,MAAMN,MAAEA,EAAAO,QAAOA,GAAYd,IAErBe,EAAKhK,EAAQ+J,EAAQE,WAErBC,EAAKC,KAAKC,MAAMJ,EAAKR,EAAMH,MAAQ,KAAO,IAC1CF,EAAQK,EAAMJ,KAAKiB,MAAM,KAC3B,IAAAjB,EAAOD,EAAM,GAIjB,OAHIA,EAAMhJ,OAAS,GAAK2J,IACtBV,EAAOD,EAAM,IAERe,EAAKd,CACd,CAqEO,SAASkB,GAAUnK,GAClB,MAAAqJ,MAAEA,GAAUP,IAClB,OAAOkB,KAAKC,MAAOjK,EAASqJ,EAAMH,MAAS,IAAM,EACnD,CACA,SAASkB,GAAQC,GACT,MAAAhB,MAAEA,GAAUP,IAElB,OADcO,EAAMiB,QAAQC,OAAOF,GACtBG,IACf,CAoBO,SAASC,GAAS/G,GACvB,MAAM2F,MAAEA,EAAAO,QAAOA,GAAYd,IACnB,OAAApF,EAAO2F,EAAMqB,KAAQd,EAAQE,UACvC,CACgB,SAAAa,GAAejH,EAAckH,GAC3C,OAAOZ,KAAKC,MAAOvG,EAAOkH,EAAS,KAAQ,GAAK,GAClD,CASO,SAASC,KACR,MAAAxB,MAAEA,GAAUP,IAClB,QAA4B,IAAxBO,EAAMyB,cAA6B,OACvC,GAAIzB,EAAMyB,eAAiBzB,EAAM0B,cAAgB1B,EAAM2B,eAAgB,OAEjE,MAAAC,EAAuBb,GAAQf,EAAMyB,eACrCI,EAAqB7B,EAAM2B,gBAAkBZ,GAAQf,EAAM0B,aAC7D,IAAAI,EACAC,EACAC,EACAC,EACJ,MAAMC,EAAId,GAASQ,EAAaM,EAAIN,EAAaO,MAAQ,GACnDC,EAAIhB,GAASQ,EAAaQ,EAAIR,EAAaS,OAAS,GAEpDC,EAAaV,EAAaM,EAAIN,EAAaO,MAC3CI,EAAaX,EAAaQ,EAAIR,EAAaS,OAC3CG,EAAWX,EAAWK,EAAIL,EAAWM,MACrCM,EAAWZ,EAAWO,EAAIP,EAAWQ,OAC3C,GApDF,SAAyBK,EAAWrL,GAClC,MAAMsL,EAAKhC,KAAKiC,IAAIF,EAAER,EAAG7K,EAAE6K,GACrBW,EAAKlC,KAAKiC,IAAIF,EAAEN,EAAG/K,EAAE+K,GAGrBD,EAFKxB,KAAKmC,IAAIJ,EAAER,EAAIQ,EAAEP,MAAO9K,EAAE6K,EAAI7K,EAAE8K,OAExBQ,EACbN,EAFK1B,KAAKmC,IAAIJ,EAAEN,EAAIM,EAAEL,OAAQhL,EAAE+K,EAAI/K,EAAEgL,QAExBQ,EAChB,KAAAV,GAAS,GAAKE,GAAU,GAIrB,MAAA,CACLH,EAAGS,EACHP,EAAGS,EACHV,QACAE,SAEJ,CAmCOU,CAAgBnB,EAAcC,GAyC5B,CACCL,MAAAA,GApE6BwB,EAoEQnB,EAnEtC,CACLoB,KAFiBC,EAoEYtB,GAlEfQ,EAAIY,EAAOZ,EACzBe,MAAOH,EAAOd,EAAIc,EAAOb,MAAQe,EAAShB,EAAIgB,EAASf,MACvDiB,OAAQJ,EAAOZ,EAAIY,EAAOX,OAASa,EAASd,EAAIc,EAASb,OACzDgB,KAAMH,EAAShB,EAAIc,EAAOd,IAgEN,GAAhBV,EAASyB,MAEDnB,EAAA,CACRI,IACAE,EAAGZ,EAASyB,IAAM,EAAI7B,GAASS,EAAWO,GAAKhB,GAASQ,EAAaQ,GACrEvI,EAAGuH,GAAST,KAAK2C,IAAI9B,EAASyB,MAC9BzB,SAAUnB,GAASM,KAAK2C,IAAI9B,EAASyB,MACrCM,mBAAoBjC,GAAeX,KAAK2C,IAAI9B,EAASyB,KAAMjD,EAAMiB,QAAQoB,UAGvD,GAAlBb,EAAS2B,QAECpB,EAAA,CACVG,EAAGV,EAAS2B,MAAQ,EAAI/B,GAASkB,GAAclB,GAASoB,GACxDJ,IACAoB,EAAGpC,GAAST,KAAK2C,IAAI9B,EAAS2B,QAC9B3B,SAAUnB,GAASM,KAAK2C,IAAI9B,EAAS2B,QACrCI,mBAAoBjC,GAAeX,KAAK2C,IAAI9B,EAAS2B,OAAQnD,EAAMiB,QAAQkB,SAGxD,GAAnBX,EAAS4B,SAEEpB,EAAA,CACXE,IACAE,EAAGZ,EAAS4B,OAAS,EAAIhC,GAASmB,GAAcnB,GAASqB,GACzD5I,EAAGuH,GAAST,KAAK2C,IAAI9B,EAAS4B,SAC9B5B,SAAUnB,GAASM,KAAK2C,IAAI9B,EAAS4B,SACrCG,mBAAoBjC,GAAeX,KAAK2C,IAAI9B,EAAS4B,QAASpD,EAAMiB,QAAQoB,UAG3D,GAAjBb,EAAS6B,OAEApB,EAAA,CACTC,EAAGV,EAAS6B,KAAO,EAAIjC,GAASS,EAAWK,GAAKd,GAASQ,EAAaM,GACtEE,IACAoB,EAAGpC,GAAST,KAAK2C,IAAI9B,EAAS6B,OAC9B7B,SAAUnB,GAASM,KAAK2C,IAAI9B,EAAS6B,OACrCE,mBAAoBjC,GAAeX,KAAK2C,IAAI9B,EAAS6B,MAAOrD,EAAMiB,QAAQkB,QAGhF,MAlFMP,EAAaQ,EAAIK,IAETX,EAAA,CACRI,IACAE,EAAGhB,GAASqB,GACZ5I,EAAGuH,GAASQ,EAAaQ,EAAIK,GAC7BjB,SAAUnB,GAASuB,EAAaQ,EAAIK,GACpCc,mBAAoBjC,GAAeM,EAAaQ,EAAIK,EAAUzC,EAAMiB,QAAQoB,UAG5EC,EAAaT,EAAWK,IAEdH,EAAA,CACVG,EAAGd,GAASkB,GACZF,IACAoB,EAAGpC,GAASS,EAAWK,EAAII,GAC3Bd,SAAUnB,GAASwB,EAAWK,EAAII,GAClCiB,mBAAoBjC,GAAeO,EAAWK,EAAII,EAAYtC,EAAMiB,QAAQkB,SAG5EI,EAAaV,EAAWO,IAEbJ,EAAA,CACXE,IACAE,EAAGhB,GAASmB,GACZ1I,EAAGuH,GAASS,EAAWO,EAAIG,GAC3Bf,SAAUnB,GAASwB,EAAWO,EAAIG,GAClCgB,mBAAoBjC,GAAeO,EAAWO,EAAIG,EAAYvC,EAAMiB,QAAQoB,UAG5ET,EAAaM,EAAIM,IAERP,EAAA,CACTC,EAAGd,GAASoB,GACZJ,IACAoB,EAAGpC,GAASQ,EAAaM,EAAIM,GAC7BhB,SAAUnB,GAASuB,EAAaM,EAAIM,GACpCe,mBAAoBjC,GAAeM,EAAaM,EAAIM,EAAUxC,EAAMiB,QAAQkB,SAhEpF,IAAqBe,EAAkBF,EA8GrC,GAAIlB,EAAS,CACL,MAAA2B,EAAKC,SAASC,cAAc,OAC/BF,EAAA3I,MAAMuI,KAAOvB,EAAQI,EAAI,KACzBuB,EAAA3I,MAAMmI,IAAMnB,EAAQM,EAAI,KACxBqB,EAAA3I,MAAMuH,OAASP,EAAQjI,EAAI,KAC9B4J,EAAG3I,MAAM8I,QAAU,GACb,MAAAC,EAAQH,SAASC,cAAc,WAC/BE,EAAAjN,aAAa,cAAekL,EAAQN,UACpCqC,EAAAjN,aAAa,oBAAqBkL,EAAQyB,mBAClD,CACA,GAAIxB,EAAW,CACP,MAAA+B,EAAKJ,SAASC,cAAc,OAC/BG,EAAAhJ,MAAMuI,KAAOtB,EAAUG,EAAI,KAC3B4B,EAAAhJ,MAAMmI,IAAMlB,EAAUK,EAAI,KAC1B0B,EAAAhJ,MAAMqH,MAAQJ,EAAUyB,EAAI,KAC/BM,EAAGhJ,MAAM8I,QAAU,GACb,MAAAG,EAAQL,SAASC,cAAc,WAC/BI,EAAAnN,aAAa,aAAcmL,EAAUP,UACrCuC,EAAAnN,aAAa,mBAAoBmL,EAAUwB,mBACnD,CACA,GAAIvB,EAAY,CACR,MAAAgC,EAAKN,SAASC,cAAc,OAC/BK,EAAAlJ,MAAMuI,KAAOrB,EAAWE,EAAI,KAC5B8B,EAAAlJ,MAAMmI,IAAMjB,EAAWI,EAAI,KAC3B4B,EAAAlJ,MAAMuH,OAASL,EAAWnI,EAAI,KACjCmK,EAAGlJ,MAAM8I,QAAU,GACb,MAAAK,EAAQP,SAASC,cAAc,WAC/BM,EAAArN,aAAa,cAAeoL,EAAWR,UACvCyC,EAAArN,aAAa,oBAAqBoL,EAAWuB,mBACrD,CACA,GAAItB,EAAU,CACN,MAAAiC,EAAKR,SAASC,cAAc,OAC/BO,EAAApJ,MAAMuI,KAAOpB,EAASC,EAAI,KAC1BgC,EAAApJ,MAAMmI,IAAMhB,EAASG,EAAI,KACzB8B,EAAApJ,MAAMqH,MAAQF,EAASuB,EAAI,KAC9BU,EAAGpJ,MAAM8I,QAAU,GACb,MAAAO,EAAQT,SAASC,cAAc,WAC/BQ,EAAAvN,aAAa,aAAcqL,EAAST,UACpC2C,EAAAvN,aAAa,mBAAoBqL,EAASsB,mBAClD,CACM,MAAAa,EAAoBV,SAASC,cAAc,aAC5B,OAAjBS,GACWA,EAAAC,UAAUC,IAAI,SAE/B,CC1RO,IAEHC,GAFOC,IAAU,EACjBC,IAAS,EAEN,SAASC,GAAmBnJ,GACjC,GAAoB,KAAhBA,EAAMoJ,MAAc,OAClB,MAAAC,EAAclB,SAASmB,eAAe,UACtCC,EAAoBpB,SAASC,cAAc,kBACjDiB,EAAO9J,MAAM8I,QAAU,GACVkB,EAAAT,UAAUC,IAAI,2BAGjBE,IAAA,EACVjJ,EAAMC,gBACR,CAEO,SAASuJ,GAAiBxJ,GAC/B,GAAoB,KAAhBA,EAAMoJ,MAAc,OAClB,MAAAC,EAAclB,SAASmB,eAAe,UACtCC,EAAoBpB,SAASC,cAAc,kBACjDiB,EAAO9J,MAAM8I,QAAU,OAChBgB,EAAAP,UAAUW,OAAO,UACXF,EAAAT,UAAUW,OAAO,iBACpBR,IAAA,EACDC,IAAA,EACTlJ,EAAMC,gBACR,CAEO,SAASyJ,GAAqB1J,GAC7B,MAAAqJ,EAAclB,SAASmB,eAAe,UAK5C,GAJe,OAAXD,IACKA,EAAA9J,MAAMuI,KAAO9H,EAAM2J,QAAU,KACpCN,EAAO9J,MAAMmI,IAAM1H,EAAM4J,QAAU,GAAK,OAErCV,GAAQ,OACP,MAAAW,EAAc1B,SAASC,cAAc,kBAC3CyB,EAAOC,WAAad,GAASrC,EAAI3G,EAAM2J,QAAUX,GAASc,WAC1DD,EAAOE,UAAYf,GAASnC,EAAI7G,EAAM4J,QAAUZ,GAASe,UACzD/J,EAAMC,gBACR,CACO,SAAS+J,GAAqBhK,GACnC,IAAKiJ,GAAS,OACR,MAAAI,EAAclB,SAASmB,eAAe,UACtCO,EAAc1B,SAASC,cAAc,kBACpCiB,EAAAP,UAAUC,IAAI,UACVC,GAAA,CACTrC,EAAG3G,EAAM2J,QACT9C,EAAG7G,EAAM4J,QACTE,WAAYD,EAAOC,WACnBC,UAAWF,EAAOE,WAEXb,IAAA,EACTlJ,EAAMC,gBACR,CACO,SAASgK,GAAmBjK,GAC7B,IAACiJ,KAAYC,GAAQ,OACnB,MAAAG,EAAclB,SAASmB,eAAe,UACxBnB,SAASC,cAAc,kBACpCU,UAAUW,OAAO,iBACjBJ,EAAAP,UAAUW,OAAO,UACfP,IAAA,EACTlJ,EAAMC,gBACR,CC1DO,SAASiK,KACd,CAAC,MAAO,MAAO,MAAO,OAAOC,SAASC,IAC9B,MAAAC,EAAKlC,SAASC,cAAcgC,GACvB,OAAPC,IACFA,EAAG9K,MAAM8I,QAAU,OACrB,IAEFF,SAASC,cAAc,cAAcU,UAAUW,OAAO,SACxD,CAkBO,SAASa,KACdnC,SAASC,cAAc,WAAWU,UAAUW,OAAO,SAC7C,MAAAc,EAAkBpC,SAASC,cAAc,WAC5B,OAAfmC,IACFA,EAAWhL,MAAM8I,QAAU,OAE/B,CACO,SAASmC,KACR,MAAA/F,MAAEA,GAAUP,IAClB,GAA2B,MAAvBO,EAAMyB,cAAmC,OAAA,EAC7CiC,SAASC,cAAc,cAAcU,UAAUW,OAAO,YACtDtB,SAASC,cAAc,UAAY3D,EAAMyB,gBAAgB4C,UAAUC,IAAI,YACjE,MAAAwB,EAAkBpC,SAASC,cAAc,WAC5B,OAAfmC,IACFA,EAAWhL,MAAM8I,QAAU,OAE/B,CAea,MAAAoC,GAAqB,SAAUzK,GAC1C,MAAMiE,EAAQC,IACV,GAAA+E,GAAS,OACb,GF5Dc,SAAeyB,EAAoB1K,EAAc2K,GAC/D,IAAIjF,EAAe1F,EAAMyH,OAClB,KAAA/B,GAAWA,IAAYgF,GAAW,CACnC,GAAAhF,EAAQkF,QAAQD,GAAkB,OAAAjF,EACtCA,EAAUA,EAAQmF,aACpB,CAEF,CEqDMC,CAAe3C,SAAS4C,KAAM/K,EAAO,iCAEvC,YADAA,EAAMgL,kBAGR/G,EAAMgH,eAAgB,EACtBhH,EAAMiH,gBAAiB,EACvBjH,EAAMkH,eAAgB,EACtB,MAAM1D,EAASzH,EAAMyH,OACjB,GAAAA,EAAOqB,UAAUsC,SAAS,UAAY3D,EAAOqB,UAAUsC,SAAS,gBAAiB,CACnF,MAAMzD,EAAYF,EAAOqB,UAAUsC,SAAS,gBAEvCjD,SAASC,cAAc,UAAYX,EAAO4D,QAAQC,UADnD7D,EAOJ,OALAxD,EAAMQ,MAAMyB,cAAgBqF,SAAS5D,EAAS0D,QAAQ5F,0BAMxD,EAlCF,WACQ,MAAAhB,MAAEA,GAAUP,IAClB,QAA4B,IAAxBO,EAAMyB,cAAoC,OAAA,EAC9CiC,SAASC,cAAc,UAAY3D,EAAMyB,gBAAgB4C,UAAUW,OAAO,YACpE,MAAAc,EAAkBpC,SAASC,cAAc,WAC5B,OAAfmC,IACFA,EAAWhL,MAAM8I,QAAU,QAE7BF,SAASC,cAAc,eAAeU,UAAUW,OAAO,UACvDhF,EAAMyB,mBAAgB,EACtBzB,EAAM2B,oBAAiB,MAEzB,GAwBA,EAEaoF,GAAyB,SAAUxL,GAC1C,GAAAiJ,GAAS,OACP,MAAAxE,MAAEA,GAAUP,cAGlB,MAAMuD,EAASzH,EAAMyH,OAEnBA,EAAOqB,UAAUsC,SAAS,kBAC1B3D,EAAOqB,UAAUsC,SAAS,wBAEpB3G,EAAA2B,eFrEH,SAAqBpG,GACpB,MAAAyE,MAAEA,GAAUP,IAEZ0B,EADSuC,SAASC,cAAc,WAClBqD,wBACpB,IAAI9E,GAAK3G,EAAM0L,MAAQ9F,EAAKkC,MAAQrD,EAAMqB,KACtCe,GAAK7G,EAAM2L,MAAQ/F,EAAK8B,KAAOjD,EAAMqB,KACrCc,EAAQ,GACRE,EAAS,GACb,MAAM8E,EAASjF,GAAK,GAAKA,GAAKlC,EAAMiB,QAAQkB,MACtCiF,EAAShF,GAAK,GAAKA,GAAKpC,EAAMiB,QAAQoB,OAmDrC,OAjDHH,GAAK,GAAKE,GAAK,GACbF,GAAA,GACAE,GAAA,IAGGF,GAAKlC,EAAMiB,QAAQkB,OAASC,GAAK,GACxCF,EAAIlC,EAAMiB,QAAQkB,MACdC,GAAA,IAGGF,GAAKlC,EAAMiB,QAAQkB,OAASC,GAAKpC,EAAMiB,QAAQoB,QACtDH,EAAIlC,EAAMiB,QAAQkB,MAClBC,EAAIpC,EAAMiB,QAAQoB,QAGXH,GAAK,GAAKE,GAAKpC,EAAMiB,QAAQoB,QAChCH,GAAA,GACJE,EAAIpC,EAAMiB,QAAQoB,QAGXD,GAAK,GAAK+E,GACbjF,EAAA,EACAE,GAAA,GACJD,EAAQnC,EAAMiB,QAAQkB,OAGfD,GAAKlC,EAAMiB,QAAQkB,OAASiF,GACnClF,EAAIlC,EAAMiB,QAAQkB,MACdC,EAAA,EACJC,EAASrC,EAAMiB,QAAQoB,QAGhBD,GAAKpC,EAAMiB,QAAQoB,QAAU8E,GAChCjF,EAAA,EACJE,EAAIpC,EAAMiB,QAAQoB,OAClBF,EAAQnC,EAAMiB,QAAQkB,OAGfD,GAAK,GAAKkF,IACblF,GAAA,GACAE,EAAA,EACJC,EAASrC,EAAMiB,QAAQoB,QAErB8E,GAAUC,IACRlF,EAAA,EACAE,EAAA,EACJD,EAAQnC,EAAMiB,QAAQkB,MACtBE,EAASrC,EAAMiB,QAAQoB,QAElB,CACLH,IACAE,IACAD,QACAE,SAEJ,CEG2BgF,CAAY9L,GACnCyE,EAAM0B,YAAc,QAEXsB,EAAOqB,UAAUsC,SAAS,UACnC3G,EAAM0B,YAAcoF,SAAUvL,EAAMyH,OAAuB4D,QAAQ5F,OACnEhB,EAAM2B,oBAAiB,EAxF3B,WACQ,MAAA3B,MAAEA,GAAUP,IAClB,GAAIO,EAAM0B,aAAe1B,EAAMyB,eAAiBzB,EAAM0B,YAAoB,OAAA,EAC1E,MAAMsB,EAASU,SAASC,cAAc,UAAY3D,EAAM0B,aACjDsB,EAAAqB,UAAUC,IAAI,SACf,MAAAgD,EAAK5D,SAASC,cAAc,OAC/B2D,EAAAxM,MAAMuI,KAAOL,EAAOuE,WAAa,KACjCD,EAAAxM,MAAMqH,MAAQa,EAAOwE,YAAc,KAChC,MAAAC,EAAK/D,SAASC,cAAc,OAC/B8D,EAAA3M,MAAMmI,IAAMD,EAAO0E,UAAY,KAC/BD,EAAA3M,MAAMuH,OAASW,EAAO2E,aAAe,KAClC,MAAA7B,EAAkBpC,SAASC,cAAc,WAC5B,OAAfmC,IACFA,EAAWhL,MAAM8I,QAAU,GAE/B,UA6EI5D,EAAM2B,oBAAiB,CAE3B,wXChFA,MAAAnC,EAAAC,IACA3J,EAAAE,EAGA4R,GAAA,IAAA9R,EAAA+R,SACcA,IAEVC,QAAAC,IAAAF,EAAA,IAGJ,MAAA1M,EAAAC,0SAIA,CAAA4M,IACE,MAAA9E,EAAAQ,SAAAC,cAAA,UAAAqE,GACAxI,EAAAQ,MAAAyB,cAAAqF,SAAA5D,EAAA0D,QAAA5F,qSALFiH,cACE9M,EAAA,OAAA8M,GADF,IAAAA,yTCpBA,MAAAzI,EAAAC,IACA3J,EAAAE,SAIA4R,GAAA,IAAA9R,EAAAoS,SACcL,IAEVC,QAAAC,IAAAF,EAAA,8gCCmBJ,MAAAM,EAAA/R,EAAA,IACAN,EAAAE,EAKAmF,EAAAC,EACAgN,EAAAhS,GAAA,GACAiS,EAAAjS,EAAA,IACAkS,EAAAlS,EAAA,MACAmS,EAAAnS,GAAA,GAEAE,GAAA,KACE+E,GAAA,aAAAE,IACEiN,GAAA,GACAjN,EAAAC,gBAAA,GAAqB,IAGzBoM,GAAA,IAAA9R,EAAA2S,gBACc,SAGV3S,EAAA4S,gBACEC,GAAA,KACEC,EAAA9S,EAAA4S,eAAA,GACD,IAIP,MAAAF,EAAAK,IACEN,EAAA/R,WAAA,IAAAqS,GAAAN,EAAA/R,MAAAqS,EACAF,GAAA,KACEJ,EAAA/R,MAAA8R,EAAA9R,OAAAsS,QAAAF,EAAA9S,EAAA4S,eAAA,GAA4F,EAIhGK,EAAA,KACEX,EAAA5R,OAAA4R,EAAA5R,KAAA,EAMFwS,EAAA,KACET,EAAA/R,OAAA,EACA6R,EAAA7R,MAAA,EAAA,EAGFyS,EAAA,KACEd,EAAA3R,MAAAV,EAAA2S,cAAAS,QAAA/I,GAAAA,EAAAgJ,KAAAC,SAAAf,EAAA7R,SACA4R,EAAA5R,OAAA,EACA6R,EAAA7R,OACEmS,GAAA,KACEC,EAAA9S,EAAA4S,eAAA,GACD,EAILE,EAAAZ,IAAA,8yBAnBAA,aACE7M,EAAA,SAAA6M,GADF,IAAAA,kaCnFOqB,GAAA,CAAYrB,GAAA,qCACVsB,IAAA,IAAAC,EAAA,MAAA,CAAQvB,GAAA,iCACRsB,IAAA,IAAAC,EAAA,MAAA,CAAQvB,GAAA,uIC8DL,IAAAwB,IAAAA,IACVA,EAAO,KAAA,OACPA,EAAS,OAAA,SACTA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAU,QAAA,UANAA,IAAAA,IAAA,CAAA,kZCxCZ,MAAAhK,EAAAC,IACAwB,EAAAvG,GAAA,IACE8E,EAAAQ,MAAAiB,UAEFwI,EAAAC,IAKE,CAAOrG,KAAA,GAJPjC,GAAAsI,EAAAvI,KAAAe,OAKYe,IAAA,GAJZ7B,GAAAsI,EAAAvI,KAAAiB,OAKWD,MAAA,GAJXf,GAAAsI,EAAAvI,KAAAgB,WAKiBE,OAAA,GAJjBjB,GAAAsI,EAAAvI,KAAAkB,2pBCTF,MAAA7C,EAAAC,IACAwB,EAAAvG,GAAA,IACE8E,EAAAQ,MAAAiB,s+BCOF,MAAA0I,EAAAvT,GAAA,GACAoJ,EAAAC,IACAwB,EAAAvG,GAAA,IACE8E,EAAAQ,MAAAiB,UAEF2I,EAAAlP,GAAA,KACE,MAAAmP,EAAA5I,EAAAzK,MAAAsT,UACA,MAAA,CAAO3H,MAAAf,GAAAH,EAAAzK,MAAA2L,OAAA,KACkCE,OAAAjB,GAAAH,EAAAzK,MAAA6L,QAAA,KACE0H,WAAA,YAAAF,EAAA,cACHG,eAAA5I,GAAAH,EAAAzK,MAAA2L,OAAA,MAAAf,GAAAH,EAAAzK,MAAA6L,QAAA,KACmD,IAG7F4H,EAAAC,EAAA,CAAA,UAgBAtC,GAAA,IAAA3G,EAAAzK,QACgB2T,IAEZC,GAAAA,QAAAD,IACExB,GAAA,KAlBN,MACE,MAAAvD,EAAA1B,SAAAC,cAAA,kBACA0G,EAAA,EAAA1J,KAAAiC,IAAA3B,EAAAzK,MAAA2L,MAAAlB,EAAAzK,MAAA6L,OAAA+C,EAAAkF,YAAAlF,EAAAmF,cACAN,EAAA9H,MAAAkI,EAAA,KACAJ,EAAA5H,OAAAgI,EAAA,KACA1B,GAAA,KACE,MAAA6B,EAAA9G,SAAAC,cAAA,WACA6G,EAAA1P,MAAA2P,YAAArJ,GAAAH,EAAAzK,MAAA2L,MAAA,GAAA,KACAqI,EAAA1P,MAAA4P,WAAAtJ,GAAAH,EAAAzK,MAAA6L,OAAA,GAAA,KACA+C,EAAAC,YAAAgF,EAAAjF,EAAAkF,aAAA,EACA,IAAAK,EAAAH,EAAAD,aAAAnF,EAAAmF,aAAAC,EAAAD,aAAAnF,EAAAmF,aACAnF,EAAAE,WAAA+E,EAAAM,GAAA,CAAA,GAA2C,OASxC,GAEL,CACAC,WAAA,IAgBFhD,GAAA,IAAApI,EAAAQ,MAAAqB,OACoB,CAAA8I,EAAAU,KAEhBlB,EAAAnT,OAAA,EACA,IAAA4O,EAAA1B,SAAAC,cAAA,kBACA6G,EAAA9G,SAAAC,cAAA,WACAmH,EAAAN,EAAAxD,wBACA+D,EAnBJ,SAAA3F,EAAAoF,GACE,IAAAQ,EAAA5F,EAAA4B,wBACAiE,EAAAT,EAAAxD,wBACAkE,EAA0BF,EAAA9I,EAAA8I,EAAA7I,MAAA,EAA1B+I,EACuCF,EAAA5I,EAAA4I,EAAA3I,OAAA,EAGvC,MAAA,CAAOH,EAAAgJ,EAAAD,EAAA/I,EAC0BE,EAAA8I,EAAAD,EAAA7I,EAEjC,CASE+I,CAAA/F,EAAAoF,GACAY,EAA0BL,EAAA7I,EAAAiI,EAAAU,EAA1BO,EAC6BL,EAAA3I,EAAA+H,EAAAU,GV4L1B,SAAsBQ,GAQ3B,MAAMC,EAAWD,EAAQC,UAAYD,EAAQrI,OAAOgE,wBAC9CuE,EAAOF,EAAQG,SACf9T,EAAK2T,EAAQI,OACbC,KAAc,GAAWH,GACzBI,KAAY,GAAWjU,GAG7B,IAAIkU,EAAU,EACVC,EAAU,KAHM,EAAWN,OACb,EAAW7T,KAIjBkU,EAAAP,EAAQS,OAAO5J,EAAIoJ,EAASpJ,EAClCqJ,EAAOhM,GAAKwM,UAASH,GAAWN,EAASnJ,MAAQ,GACjDoJ,EAAOhM,GAAKyM,SAAQJ,GAAWN,EAASnJ,OACxCzK,EAAK6H,GAAKwM,UAAoBH,GAAAP,EAAQS,OAAO3J,MAAQ,GACrDzK,EAAK6H,GAAKyM,SAAQJ,GAAWP,EAAQS,OAAO3J,QAE9CuJ,GAAYC,IACJE,EAAAR,EAAQS,OAAO1J,EAAIkJ,EAASlJ,EAClCmJ,EAAOhM,GAAK0M,UAASJ,GAAWP,EAASjJ,OAAS,GAClDkJ,EAAOhM,GAAK2M,UAASL,GAAWP,EAASjJ,QACzC3K,EAAK6H,GAAK0M,UAAoBJ,GAAAR,EAAQS,OAAOzJ,OAAS,GACtD3K,EAAK6H,GAAK2M,UAASL,GAAWR,EAAQS,OAAOzJ,SAEnDgJ,EAAQc,SAAS7G,WAAauG,EAC9BR,EAAQc,SAAS9G,YAAcuG,CACjC,CU1NIQ,CAAA,CAAaD,SAAA/G,EACDpC,OAAAwH,EACFsB,OAAAhB,EACAU,SAAAjM,GAAA8M,MAAA9M,GAAA+M,KACoBb,OAAAlM,GAAA8M,MAAA9M,GAAA+M,OAG9BlH,EAAAC,YAAA+F,EAAAL,EAAA7I,EACAkD,EAAAE,WAAA8F,EAAAL,EAAA3I,EACAuH,EAAAnT,OAAA,CAAA,0eCxFJ,MAAAgJ,EAAAC,IAMAtE,EAAAC,qfACAmR,eACE,IAAAC,KAAAD,GAAAE,+HADF,IAAAF,uqBCgEA,MAAApR,EAAAC,svDCxEA,MAAAoE,EAAAC,wtJCVG,SAASiN,GAGZ,SAASC,EAAQC,EAAUC,EAAQxX,EAAMyX,EAAMC,EAAOC,GACpDC,KAAKL,SAAWA,EAChBK,KAAKJ,OAASA,EACdI,KAAK5X,KAAOA,EACZ4X,KAAKH,KAAOA,EACZG,KAAKF,MAAQA,EACbE,KAAKD,KAAOA,CACb,CACD,SAASE,EAAYlN,EAAOmN,EAAK9X,EAAMyX,GACrC,IAAIM,EAASpN,EAAM4M,SAGZ,OAFH5M,EAAMqN,SAAiC,aAAtBrN,EAAMqN,QAAQhY,MAA+B,aAARA,IACxD+X,EAASpN,EAAMqN,QAAQT,UAClB5M,EAAMqN,QAAU,IAAIV,EAAQS,EAAQD,EAAK9X,EAAMyX,EAAM,KAAM9M,EAAMqN,QACzE,CACD,SAASC,EAAWtN,GACd,IAAAuN,EAAIvN,EAAMqN,QAAQhY,KAGf,MAFE,KAALkY,GAAiB,KAALA,GAAiB,KAALA,IACpBvN,EAAA4M,SAAW5M,EAAMqN,QAAQT,UAC1B5M,EAAMqN,QAAUrN,EAAMqN,QAAQL,IACtC,CAEQ,SAAAQ,EAAWC,EAAQzN,EAAO0N,GACjC,MAAuB,YAAnB1N,EAAM2N,WAA8C,QAAnB3N,EAAM2N,aACvC,6BAA6BC,KAAKH,EAAOI,OAAOC,MAAM,EAAGJ,QACzD1N,EAAM+N,iBAAmBN,EAAOZ,UAAYY,EAAOO,qBAAvD,CACD,CAED,SAASC,EAAWZ,GACT,OAAA,CACH,IAACA,GAA2B,OAAhBA,EAAQhY,KAAsB,OAAA,EAC9C,GAAoB,KAAhBgY,EAAQhY,MAAoC,aAArBgY,EAAQL,KAAKF,KAA4B,OAAA,EACpEO,EAAUA,EAAQL,IACnB,CACF,CAmNC,SAASkB,EAAM5O,GAEb,IADA,IAAI6O,EAAM,CAAA,EAAID,EAAQ5O,EAAIuB,MAAM,KACvBuN,EAAI,EAAGA,EAAIF,EAAMvX,SAAUyX,EAAOF,EAAAA,EAAME,KAAM,EAChD,OAAAD,CACR,CACQ,SAAAxH,EAASuH,EAAOG,GACnB,MAAiB,mBAAVH,EACFA,EAAMG,GAENH,EAAMI,qBAAqBD,EAErC,CA5NH3B,EAAW6B,WAAW,SAAS,SAASjW,EAAQkW,GAC9C,IAuBIC,EAASC,EAvBTC,EAAarW,EAAOqW,WACpBC,EAAsBJ,EAAaI,qBAAuBD,EAC1DE,EAAiBL,EAAaK,eAC9BC,EAAWN,EAAaM,UAAY,CAAE,EACtCC,EAAQP,EAAaO,OAAS,CAAE,EAChCC,EAAUR,EAAaQ,SAAW,CAAE,EACpCC,EAAgBT,EAAaS,eAAiB,CAAE,EAChDC,EAAcV,EAAaU,aAAe,CAAE,EAC5CC,EAAQX,EAAaW,OAAS,CAAE,EAChCC,EAAQZ,EAAaY,OAAS,CAAE,EAChCC,EAAmBb,EAAaa,iBAChCC,GAAqD,IAAlCd,EAAac,iBAChCC,GAA6C,IAA9Bf,EAAae,aAC5BC,EAAqBhB,EAAagB,mBAClCC,EAAoBjB,EAAaiB,mBAAqB,qBACtDC,EAAclB,EAAakB,aAAe,SAC1CC,EAASnB,EAAamB,QAAU,yEAChCC,EAAiBpB,EAAaoB,gBAAkB,mBAChDC,EAAmBrB,EAAaqB,kBAAoB,qBAGpDC,EAAuBtB,EAAasB,uBAAwB,EAIvD,SAAAC,EAAUtC,EAAQzN,GACrB,IAAAgQ,EAAKvC,EAAOwC,OACZ,GAAAb,EAAMY,GAAK,CACb,IAAIE,EAASd,EAAMY,GAAIvC,EAAQzN,GAC/B,IAAe,IAAXkQ,EAAyB,OAAAA,CAC9B,CACG,GAAM,KAANF,GAAmB,KAANA,EAER,OADDhQ,EAAAmQ,SAAWC,EAAYJ,GACtBhQ,EAAMmQ,SAAS1C,EAAQzN,GAE5B,GAAA0P,EAAY9B,KAAKoC,GAAK,CAEpB,GADJvC,EAAO4C,OAAO,GACV5C,EAAO6C,MAAMX,GAAgB,MAAA,SACjClC,EAAOwC,MACR,CACG,GAAAR,EAAkB7B,KAAKoC,GAElB,OADGvB,EAAAuB,EACH,KAET,GAAU,KAANA,EAAW,CACT,GAAAvC,EAAO8C,IAAI,KAEN,OADPvQ,EAAMmQ,SAAWK,EACVA,EAAa/C,EAAQzN,GAE1B,GAAAyN,EAAO8C,IAAI,KAEN,OADP9C,EAAOgD,YACA,SAEV,CACG,GAAAb,EAAehC,KAAKoC,GAAK,CACpB,MAACvC,EAAO6C,MAAM,YAAY,IAAU7C,EAAO8C,IAAIX,KAC/C,MAAA,UACR,CAEG,GADJnC,EAAOiD,SAASb,GACZL,EAA2B,KAAA/B,EAAO6C,MAAMd,IAC1C/B,EAAOiD,SAASb,GAEd,IAAA9P,EAAM0N,EAAOxM,UACb,OAAA0F,EAASmI,EAAU/O,IACjB4G,EAASsI,EAAelP,KAAgB0O,EAAA,gBACxC9H,EAASuI,EAAanP,KAAqB2O,GAAA,GACxC,WAEL/H,EAASoI,EAAOhP,GAAa,OAC7B4G,EAASqI,EAASjP,IACd+P,GAAwBA,EAAqB/P,IAC/C4G,EAASsI,EAAelP,KAAgB0O,EAAA,gBACrC,WAEL9H,EAASwI,EAAOpP,GAAa,OAC1B,UACR,CAED,SAASqQ,EAAYO,GACZ,OAAA,SAASlD,EAAQzN,GAEtB,IADI,IAAiBiQ,EAAjBW,GAAU,EAAaC,GAAM,EACA,OAAzBZ,EAAOxC,EAAOwC,SAAiB,CACjC,GAAAA,GAAQU,IAAUC,EAAS,CAAOC,GAAA,EAAM,KAAM,CACxCD,GAACA,GAAmB,MAARX,CACvB,CAGM,OAFHY,IAASD,IAAWvB,KACtBrP,EAAMmQ,SAAW,MACZ,QACb,CACG,CAEQ,SAAAK,EAAa/C,EAAQzN,GAErB,IADP,IAAsBgQ,EAAlBc,GAAW,EACRd,EAAKvC,EAAOwC,QAAQ,CACrB,GAAM,KAAND,GAAac,EAAU,CACzB9Q,EAAMmQ,SAAW,KACjB,KACD,CACDW,EAAkB,KAANd,CACb,CACM,MAAA,SACR,CAEQ,SAAAe,EAAStD,EAAQzN,GACpBwO,EAAawC,sBAAwBvD,EAAOwD,OAAShD,EAAWjO,EAAMqN,WACxErN,EAAM+N,gBAAkBP,EAAWC,EAAQzN,EAAOyN,EAAOC,KAC5D,CAIM,MAAA,CACLwD,WAAY,SAASC,GACZ,MAAA,CACLhB,SAAU,KACV9C,QAAS,IAAIV,GAASwE,GAAc,GAAKxC,EAAY,EAAG,MAAO,MAAM,GACrE/B,SAAU,EACVwE,aAAa,EACbzD,UAAW,KAEd,EAED0D,MAAO,SAAS5D,EAAQzN,GACtB,IAAIsR,EAAMtR,EAAMqN,QAMZ,GALAI,EAAO8D,QACQ,MAAbD,EAAIvE,QAAeuE,EAAIvE,OAAQ,GAC7B/M,EAAA4M,SAAWa,EAAOO,cACxBhO,EAAMoR,aAAc,GAElB3D,EAAO+D,WAA8C,OAAhCT,EAAStD,EAAQzN,GAAe,KACzDyO,EAAUC,EAAe,KACzB,IAAI5T,GAASkF,EAAMmQ,UAAYJ,GAAWtC,EAAQzN,GAC9C,GAAS,WAATlF,GAA+B,QAATA,EAAwB,OAAAA,EAG9C,GAFa,MAAbwW,EAAIvE,QAAeuE,EAAIvE,OAAQ,GAEpB,KAAX0B,GAA6B,KAAXA,GAA8B,KAAXA,GAAkBhB,EAAO6C,MAAM,oBAAoB,GACnF,KAAsB,aAAtBtQ,EAAMqN,QAAQhY,MAAqBiY,EAAWtN,QAAK,GACxC,KAAXyO,EAAgBvB,EAAYlN,EAAOyN,EAAOZ,SAAU,UAAG,GAC5C,KAAX4B,EAAgBvB,EAAYlN,EAAOyN,EAAOZ,SAAU,UAAG,GAC5C,KAAX4B,EAAgBvB,EAAYlN,EAAOyN,EAAOZ,SAAU,UAAG,GAC5C,KAAX4B,EAAgB,CACvB,KAAmB,aAAZ6C,EAAIjc,MAAqBic,EAAMhE,EAAWtN,GAEjD,IADgB,KAAZsR,EAAIjc,OAAaic,EAAMhE,EAAWtN,IACnB,aAAZsR,EAAIjc,MAAqBic,EAAMhE,EAAWtN,EAClD,MACQyO,GAAW6C,EAAIjc,KAAMiY,EAAWtN,GAChCsP,KACe,KAAZgC,EAAIjc,MAA2B,OAAZic,EAAIjc,OAA6B,KAAXoZ,GAC9B,aAAZ6C,EAAIjc,MAAkC,gBAAXoZ,IACpCvB,EAAYlN,EAAOyN,EAAOZ,SAAU,YAAaY,EAAOxM,WAS1D,GANa,YAATnG,IACqB,OAAnBkF,EAAM2N,WACLa,EAAawC,sBAAwBxD,EAAWC,EAAQzN,EAAOyN,EAAOgE,QACtExD,EAAWjO,EAAMqN,UAAYI,EAAO6C,MAAM,UAAU,MACjDxV,EAAA,OAENsU,EAAMiC,MAAO,CACf,IAAInB,EAASd,EAAMiC,MAAM5D,EAAQzN,EAAOlF,QACzB,IAAXoV,IAA8BpV,EAAAoV,EACnC,CAOM,MALM,OAATpV,IAA6C,IAA3B0T,EAAakD,YAA6B5W,EAAA,YAEhEkF,EAAMoR,aAAc,EACdpR,EAAA2N,UAAYe,EAAe,MAAQ5T,GAAS2T,EAClDsC,EAAStD,EAAQzN,GACVlF,CACR,EAEDsS,OAAQ,SAASpN,EAAO2R,GAClB,GAAA3R,EAAMmQ,UAAYJ,GAA+B,MAAlB/P,EAAMmQ,UAAoBnQ,EAAM+N,iBAAmBE,EAAWjO,EAAMqN,SACrG,OAAOX,EAAWkF,KACpB,IAAIN,EAAMtR,EAAMqN,QAASwE,EAAYF,GAAaA,EAAUG,OAAO,GAC/DC,EAAUF,GAAaP,EAAIjc,KAE/B,GADgB,aAAZic,EAAIjc,MAAoC,KAAbwc,IAAkBP,EAAMA,EAAItE,MACvDwB,EAAawD,qBACf,KAAmB,aAAZV,EAAIjc,MAAuBmZ,EAAawD,qBAAqBpE,KAAK0D,EAAIxE,OAC3EwE,EAAMA,EAAItE,KACd,GAAIoC,EAAMhC,OAAQ,CAChB,IAAI6E,EAAO7C,EAAMhC,OAAOpN,EAAOsR,EAAKK,EAAWhD,GAC/C,GAAmB,iBAARsD,EAAyB,OAAAA,CACrC,CACD,IAAIC,EAAcZ,EAAItE,MAAyB,UAAjBsE,EAAItE,KAAKF,KACvC,GAAI0B,EAAa2D,mBAAqB,OAAOvE,KAAKiE,GAAY,CAC5D,KAAmB,OAAZP,EAAIjc,MAA6B,KAAZic,EAAIjc,MAAaic,EAAMA,EAAItE,KACvD,OAAOsE,EAAI1E,QACZ,CACD,MAAgB,aAAZ0E,EAAIjc,KACCic,EAAI1E,UAAyB,KAAbiF,EAAmB,EAAIjD,IAC5C0C,EAAIvE,OAAW8B,GAA8B,KAAZyC,EAAIjc,KAEzB,KAAZic,EAAIjc,MAAgB0c,EAGjBT,EAAI1E,UAAYmF,EAAU,EAAIpD,IACjCoD,IAAWG,GAAgB,sBAAsBtE,KAAK+D,GAA0B,EAAbhD,GAH9D2C,EAAI1E,SAAWgC,EAFf0C,EAAIzE,QAAUkF,EAAU,EAAI,EAMtC,EAEDK,cAAe7C,EAAe,uCAAyC,YACvE8C,kBAAmB,KACnBC,gBAAiB,KACjBC,qBAAsB,MACtBC,YAAa,KACbC,KAAM,QAEV,IAcE,IAAIC,EAAY,8KAKZC,EAAc,6YAOdC,EAAe,maAMfC,EAAe,gTAOfC,EAAc5E,EAAM,8DAKpB6E,EAAiB7E,EAAM,2CAM3B,SAAS8E,EAAOC,GACd,OAAOtM,EAASmM,EAAaG,IAAe,QAAQrF,KAAKqF,EAC1D,CAGD,SAASC,EAAUD,GACjB,OAAOD,EAAOC,IAAetM,EAASoM,EAAgBE,EACvD,CAED,IAAIE,EAAiB,qDACjBC,EAAe,oBAEV,SAAAC,EAAQ5F,EAAQzN,GACvB,IAAKA,EAAMoR,YAAoB,OAAA,EAC/B,IAAA,IAASpB,EAAIC,EAAO,KAAMD,EAAKvC,EAAO6F,QAAS,CAC7C,GAAU,MAANtD,GAAcvC,EAAO6C,MAAM,OAAQ,CAC9BL,EAAAoD,EACP,KACR,IAAuB,KAANrD,GAAavC,EAAO6C,MAAM,aAAa,GAChD,MAEF7C,EAAOwC,MACR,CAEM,OADPjQ,EAAMmQ,SAAWF,EACV,MACR,CAEQ,SAAAsD,EAAYC,EAASxT,GAC5B,MAAuB,QAAnBA,EAAM2N,WAA4B,MAEvC,CAID,SAAS8F,EAAsBpC,GACzB,SAACA,GAASA,EAAM1a,OAAS,GACb,KAAZ0a,EAAM,IACU,KAAZA,EAAM,IAAeA,EAAM,KAAOA,EAAM,GAAGqC,cACpD,CAED,SAASC,EAAalG,GAEb,OADPA,EAAOiD,SAAS,WACT,QACR,CAEQ,SAAAkD,EAAgBnG,EAAQzN,GAG3B,GAFJyN,EAAO4C,OAAO,GAEV5C,EAAO6C,MAAM,uBAAwB,CACnC,IAAAA,EAAQ7C,EAAO6C,MAAM,yBACzB,QAAKA,IAGCtQ,EAAA6T,oBAAsBvD,EAAM,GAClCtQ,EAAMmQ,SAAW2D,EACVA,EAAerG,EAAQzN,GAC/B,CAEG,OAAAyN,EAAO6C,MAAM,mBACX7C,EAAO6C,MAAM,SAAmB,IAC3B,UAKX7C,EAAOwC,QACA,EACR,CAED,SAAS8D,EAAwB1F,GAC3B,IAAA2F,EAAU,kBAAkBC,KAAK5F,GACrC,OAAO2F,GAAWA,EAAQ,IAAMA,EAAQ,EACzC,CAGQ,SAAAE,EAAczG,EAAQzN,GAE7B,IADI,IAAAiQ,EAC6B,OAAzBA,EAAOxC,EAAOwC,SACpB,GAAY,KAARA,IAAgBxC,EAAO8C,IAAI,KAAM,CACnCvQ,EAAMmQ,SAAW,KACjB,KACD,CAEI,MAAA,QACR,CAIQ,SAAA2D,EAAerG,EAAQzN,GAE9B,IAAImU,EAAQnU,EAAM6T,oBAAoBhc,QAAQ,WAAY,QAMnD,OALK4V,EAAO6C,MAAM,IAAI8D,OAAO,SAAWD,EAAQ,MAErDnU,EAAMmQ,SAAW,KAEjB1C,EAAOgD,YACF,QACR,CAEQ,SAAA4D,EAAIC,EAAOC,GACE,iBAATD,IAAmBA,EAAQ,CAACA,IACvC,IAAIpG,EAAQ,GACZ,SAAS5J,EAAI6J,GACP,GAAAA,EAAK,IAAA,IAASqG,KAAQrG,EAASA,EAAIsG,eAAeD,IACpDtG,EAAMvV,KAAK6b,EACd,CACDlQ,EAAIiQ,EAAKzF,UACTxK,EAAIiQ,EAAKxF,OACTzK,EAAIiQ,EAAKvF,SACT1K,EAAIiQ,EAAKpF,OACLjB,EAAMvX,SACH4d,EAAAG,WAAaJ,EAAM,GACxB5H,EAAWiI,eAAe,YAAaL,EAAM,GAAIpG,IAGnD,IAAA,IAASE,EAAI,EAAGA,EAAIkG,EAAM3d,SAAUyX,EAClC1B,EAAWkI,WAAWN,EAAMlG,GAAImG,EACnC,CAsHQ,SAAAM,EAAkBpH,EAAQzN,GAE1B,IADP,IAAI4Q,GAAU,GACNnD,EAAOwD,OAAO,CACpB,IAAKL,GAAWnD,EAAO6C,MAAM,OAAQ,CACnCtQ,EAAMmQ,SAAW,KACjB,KACD,CACDS,EAA2B,MAAjBnD,EAAOwC,SAAmBW,CACrC,CACM,MAAA,QACR,CAED,SAASkE,EAAmBC,GACnB,OAAA,SAAUtH,EAAQzN,GAEhB,IADH,IAAAgQ,EACGA,EAAKvC,EAAOwC,QAAQ,CACzB,GAAU,KAAND,GAAavC,EAAO8C,IAAI,KAAM,CAChC,GAAa,GAATwE,EAAY,CACd/U,EAAMmQ,SAAW,KACjB,KACZ,CAEmB,OADDnQ,EAAAmQ,SAAW2E,EAAmBC,EAAQ,GACrC/U,EAAMmQ,SAAS1C,EAAQzN,EAE1C,IAAyB,KAANgQ,GAAavC,EAAO8C,IAAI,KAE1B,OADDvQ,EAAAmQ,SAAW2E,EAAmBC,EAAQ,GACrC/U,EAAMmQ,SAAS1C,EAAQzN,EAEjC,CACM,MAAA,SACR,CACF,CAoED,SAASgV,EAAkBC,GAClB,OAAA,SAAUxH,EAAQzN,GAEhB,IADH,IAAiBiQ,EAAjBW,GAAU,EAAaC,GAAM,GACzBpD,EAAOwD,OAAO,CACpB,IAAKgE,IAAiBrE,GAAWnD,EAAO6C,MAAM,KAAO,CAAOO,GAAA,EAAM,KAAM,CACxE,GAAIoE,GAAgBxH,EAAO6C,MAAM,OAAQ,CAAOO,GAAA,EAAM,KAAM,CAC5DZ,EAAOxC,EAAOwC,QACVW,GAAmB,KAARX,GAAexC,EAAO6C,MAAM,MACzC7C,EAAOyH,OAAO,KAChBtE,GAAWA,GAAmB,MAARX,IAAiBgF,CACxC,CAGM,OAFHpE,GAAQoE,IACVjV,EAAMmQ,SAAW,MACZ,QACR,CACF,CAtODkE,EAAI,CAAC,cAAe,WAAY,eAAgB,CAC9Cze,KAAM,QACNkZ,SAAUZ,EAAMwE,GAChB3D,MAAOiE,EACP/D,cAAef,EAAMiF,GACrBjE,YAAahB,EAAMkF,GACnBpC,sBAAsB,EACtB7B,MAAOjB,EAAM,mBACb4B,qBAAsB2D,EACtBrE,MAAO,CACL,IAAKiE,EACL,IAAKE,GAEP4B,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG1B4B,EAAA,CAAC,gBAAiB,iBAAkB,CACtCze,KAAM,QACNkZ,SAAUZ,EAAMwE,EAAY,IAAMC,GAClC5D,MAAOiE,EACP/D,cAAef,EAAMiF,EAAiB,oBACtCjE,YAAahB,EAAMkF,EAAe,oBAClCpC,sBAAsB,EACtB7B,MAAOjB,EAAM,2BACb8D,qBAAsB,aACtBnC,iBAAkB,sBAClBC,qBAAsB2D,EACtBrE,MAAO,CACL,IAAKiE,EACL,IAAKE,EACL6B,EAAKxB,EACLyB,EAAKzB,EACL0B,EAAK1B,EACL2B,EAAK3B,EACL,EAAKD,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACLtC,MAAO,SAAS5D,EAAQzN,EAAOlF,GAC7B,GAAa,YAATA,GAAwC,KAAjB2S,EAAO6F,SACV,KAAnBtT,EAAM2N,WAAuC,MAAnB3N,EAAM2N,WACb,KAAnB3N,EAAM2N,YACPoG,EAAwBtG,EAAOxM,WAC1B,MAAA,KACV,GAEHuO,mBAAoB,KACpB2F,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG9B4B,EAAI,cAAe,CACjBze,KAAM,QACNkZ,SAAUZ,EAAM,8SAKhBa,MAAOb,EAAM,sKAEbe,cAAef,EAAM,uDACrBgB,YAAahB,EAAM,mCACnB8C,sBAAsB,EACtB7B,MAAOjB,EAAM,mBACbyB,OAAQ,iFACRP,MAAO,CACL,IAAK,SAAS3B,GAER,OAAAA,EAAO6C,MAAM,aAAa,KAE9B7C,EAAOiD,SAAS,WACT,OACR,EACD,IAAK,SAASjD,EAAQzN,GAChB,QAACyN,EAAO6C,MAAM,SAClBtQ,EAAMmQ,SAAW0E,EACV7U,EAAMmQ,SAAS1C,EAAQzN,GAC/B,GAEHmV,UAAW,CAAC1C,KAAM,CAAC,QAAS,aAG9B4B,EAAI,gBAAiB,CACnBze,KAAM,QACNkZ,SAAUZ,EAAM,4iBAOhBa,MAAOb,EAAM,sPAIbe,cAAef,EAAM,sEACrBgB,YAAahB,EAAM,+CACnB8C,sBAAsB,EACtB7B,MAAOjB,EAAM,mBACbkB,MAAO,CACL,IAAK,SAAS3B,EAAQzN,GAChB,OAAAyN,EAAO8C,IAAI,MACbvQ,EAAMmQ,SAAW+D,EACVA,EAAczG,EAAQzN,KAE/ByN,EAAOiD,SAAS,WACT,OACR,KAqCL2D,EAAI,eAAgB,CAClBze,KAAM,QACNkZ,SAAUZ,EAER,uWAQFa,MAAOb,EACL,muBAYFmB,kBAAkB,EAClBJ,cAAef,EAAM,0EACrBgB,YAAahB,EAAM,oDACnBiB,MAAOjB,EAAM,mBACboB,kBAAkB,EAClBC,cAAc,EACdK,eAAgB,sBAChBR,MAAO,CACL,IAAK,SAAS3B,GAEL,OADPA,EAAOiD,SAAS,WACT,MACR,EACD,IAAK,SAASjD,EAAQzN,GAChB,QAACyN,EAAO6C,MAAM,QAClBtQ,EAAMmQ,SAAW0E,EACV7U,EAAMmQ,SAAS1C,EAAQzN,GAC/B,EACD,IAAK,SAASyN,GACR,OAAAA,EAAO6C,MAAM,wBAAgC,YACjD7C,EAAOiD,SAAS,sBACT,OACR,EACD,IAAK,SAASjD,EAAQzN,GACpB,IAAIwV,EAAKxV,EAAMqN,QACX,QAAW,KAAXmI,EAAGngB,OAAemgB,EAAGzI,QAASU,EAAO8C,IAAI,QAC3CvQ,EAAMqN,QAAU,IAAIV,EAAQ6I,EAAG5I,SAAU4I,EAAG3I,OAAQ2I,EAAGngB,KAAMmgB,EAAG1I,KAAM,KAAM0I,EAAGxI,MACxE,WAIV,EAED,IAAK,SAASS,EAAQzN,GAChB,QAACyN,EAAO8C,IAAI,OACVvQ,EAAAmQ,SAAW2E,EAAmB,GAC7B9U,EAAMmQ,SAAS1C,EAAQzN,GAC/B,GAEHmV,UAAW,CAACM,cAAe,CAACC,MAAO,WAAYC,QAAS,QAoB1DtB,EAAI,gBAAiB,CACnBze,KAAM,QACNkZ,SAAUZ,EAER,kgBAUFa,MAAOb,EAEL,2eAOF0H,cAAc,EACdtG,kBAAkB,EAClBD,kBAAkB,EAClBM,OAAQ,mFACRV,cAAef,EAAM,2DACrBgB,YAAahB,EAAM,sCACnBiB,MAAOjB,EAAM,wBACbkB,MAAO,CACL,IAAK,SAAS3B,GAEL,OADPA,EAAOiD,SAAS,WACT,MACR,EACD,IAAK,SAAS8C,EAASxT,GACd,MAAmB,KAAnBA,EAAM2N,UAAmB,WAAa,UAC9C,EACD,IAAK,SAASF,EAAQzN,GAEb,OADPA,EAAMmQ,SAAW6E,EAAkBvH,EAAO6C,MAAM,OACzCtQ,EAAMmQ,SAAS1C,EAAQzN,EAC/B,EACD,IAAK,SAASyN,EAAQzN,GAChB,QAACyN,EAAO8C,IAAI,OACVvQ,EAAAmQ,SAAW2E,EAAmB,GAC7B9U,EAAMmQ,SAAS1C,EAAQzN,GAC/B,EACDoN,OAAQ,SAASpN,EAAOsR,EAAKK,EAAWhD,GACtC,IAAIkD,EAAYF,GAAaA,EAAUG,OAAO,GAC9C,MAAwB,KAAnB9R,EAAM2N,WAAuC,KAAnB3N,EAAM2N,WAAkC,IAAbgE,EAElC,YAAnB3R,EAAM2N,WAAwC,KAAbgE,GAA0C,KAAtB3R,EAAMqN,QAAQhY,MACnD,YAAnB2K,EAAM2N,WAAwC,KAAbkE,IACb,KAAnB7R,EAAM2N,WAAuC,KAAnB3N,EAAM2N,YAAkC,KAAbkE,EAClC,EAAblD,EAAiB2C,EAAI1E,SAC1B0E,EAAIvE,OAAqB,KAAZuE,EAAIjc,KACZic,EAAI1E,UAAY5M,EAAMqN,QAAQhY,OAASsc,GAAa,IAAIG,OAAO,GAAK,EAAInD,QAD7E,EALK3O,EAAM4M,QAOhB,GAEHuI,UAAW,CAACM,cAAe,CAACE,QAAS,QAGnCtB,EAAA,CAAC,oBAAqB,uBAAwB,CAChDze,KAAM,QACNkZ,SAAUZ,EAAM,oLAMhBa,MAAOb,EAAM,yFAGbe,cAAef,EAAM,+BACrBc,QAASd,EAAM,uoBAefiB,MAAOjB,EAAM,myCA2BbqB,cAAc,EACdH,MAAO,CAAC,IAAKiE,GACb8B,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG9B4B,EAAI,cAAe,CACjBze,KAAM,QACNkZ,SAAUZ,EAAMwE,EAAY,yMAG5B3D,MAAOiE,EACP/D,cAAef,EAAMiF,GACrBhE,MAAOjB,EAAM,mBACbkB,MAAO,CAAC,IAAKiE,GACb8B,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG9B4B,EAAI,oBAAqB,CACvBze,KAAM,QACNkZ,SAAUZ,EAAMwE,EAAY,IAAME,GAClC7D,MAAOmE,EACPlE,QAASd,EAAM2E,GACf5D,cAAef,EAAMiF,EAAiB,oEACtCjE,YAAahB,EAAMkF,EAAe,gDAClCpB,qBAAsB,QACtBhB,sBAAsB,EACtB7B,MAAOjB,EAAM,0CACb4B,qBAAsB2D,EACtBrE,MAAO,CACL,IAAKiE,EACL,IAAKE,GAEP4B,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG9B4B,EAAI,sBAAuB,CACzBze,KAAM,QACNkZ,SAAUZ,EAAMwE,EAAY,IAAME,EAAe,IAAMD,GACvD5D,MAAOmE,EACPlE,QAASd,EAAM2E,GACf5D,cAAef,EAAMiF,EAAiB,oFACtCjE,YAAahB,EAAMkF,EAAe,gEAClCpB,qBAAsB,mBACtBhB,sBAAsB,EACtB7B,MAAOjB,EAAM,0CACb4B,qBAAsB2D,EACtBrE,MAAO,CACL,IAAKiE,EACL,IAAKE,EACL6B,EAAKxB,EACLyB,EAAKzB,EACL0B,EAAK1B,EACL2B,EAAK3B,EACL,EAAKD,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACLtC,MAAO,SAAS5D,EAAQzN,EAAOlF,GAC7B,GAAa,YAATA,GAAwC,KAAjB2S,EAAO6F,SACV,KAAnBtT,EAAM2N,WAAuC,MAAnB3N,EAAM2N,WACb,KAAnB3N,EAAM2N,YACPoG,EAAwBtG,EAAOxM,WAC1B,MAAA,KACV,GAEHuO,mBAAoB,KACpB2F,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAG9B4B,EAAI,kBAAmB,CACrBze,KAAM,QACNkZ,SAAUZ,EAAM,mKAEhBa,MAAOiE,EACP/D,cAAef,EAAM,yDACrBgB,YAAahB,EAAM,wBACnB8C,sBAAsB,EACtB7B,MAAOjB,EAAM,mBACbkB,MAAO,CAAC,IAAKiE,GACb8B,UAAW,CAAC1C,KAAM,CAAC,QAAS,cAI9B,IAAIoD,EAAkB,KACtB,SAASC,EAAkBzgB,GAClB,OAAA,SAASoY,EAAQzN,GAEf,IADH,IAAiBiQ,EAAjBW,GAAU,EAAaC,GAAM,GACzBpD,EAAOwD,OAAO,CAChB,IAACL,GAAWnD,EAAO6C,MAAM,OACd,UAARjb,GAAoBoY,EAAO6C,MAAM,OAAQ,CACxCO,GAAA,EACN,KACD,CACD,IAAKD,GAAWnD,EAAO6C,MAAM,MAAO,CAClCuF,EAAkBC,EAAkBzgB,GAC9Bwb,GAAA,EACN,KACD,CACDZ,EAAOxC,EAAOwC,OACdW,EAAkB,UAARvb,IAAqBub,GAAmB,MAARX,CAC3C,CAGM,OAFHY,IACA7Q,EAAMmQ,SAAW,MACd,QACR,CACF,CAEDkE,EAAI,gBAAiB,CACnBze,KAAM,QACNkZ,SAAUZ,EAAM,kRAIhBa,MAAO,SAASV,GAER,IAAA0H,EAAQ1H,EAAKyD,OAAO,GACxB,OAAQiE,IAAUA,EAAMC,eAAiBD,IAAUA,EAAMrC,aAC5D,EACDzE,cAAef,EAAM,sGACrBgB,YAAahB,EAAM,gEACnBc,QAASd,EAAM,iLAEfuB,kBAAmB,sBACnBG,eAAgB,sBAChBF,YAAa,SACbC,OAAQ,iGACRN,kBAAkB,EAClB2B,sBAAsB,EACtB7B,MAAOjB,EAAM,uDACbqB,cAAc,EACdmC,WAAW,EACXtC,MAAO,CACL,IAAK,SAAS3B,GAEL,OADPA,EAAOiD,SAAS,WACT,MACR,EACD,IAAK,SAASjD,EAAQzN,GAEX,OADPA,EAAMmQ,SAAW2F,EAAkBrI,EAAO6C,MAAM,MAAQ,SAAW,UAC5DtQ,EAAMmQ,SAAS1C,EAAQzN,EAC/B,EACH,IAAK,SAASyN,EAAQzN,GAClB,SAAK6V,IAAoBpI,EAAO6C,MAAM,QACtCtQ,EAAMmQ,SAAW0F,EACCA,EAAA,KACX7V,EAAMmQ,SAAS1C,EAAQzN,GAC/B,EACH,IAAK,SAASyN,GAEL,OADPA,EAAOiD,SAAS,sBACT,MACR,EACDW,MAAO,SAASmC,EAASxT,EAAOlF,GAC5B,IAAc,YAATA,GAAgC,QAATA,IACL,KAAnBkF,EAAM2N,UACD,MAAA,YAEV,GAELwH,UAAW,CACP1C,KAAM,CAAC,QAAS,UAChBgD,cAAe,CAACE,QAAS,OAIjC,CAx6BIM,CAAIC,MCKL,SAASxJ,GAYD,SAAAyJ,EAAeC,EAAIpW,GAC1B,SAASqW,IACHD,EAAGxS,QAAQ0S,QAAQ3O,cACrB4O,EAAcH,EAAIpW,GACdoW,EAAGxS,QAAQ4S,gBAAkBJ,EAAGxS,QAAQ0S,QAAQ/L,cAClD6L,EAAGK,WAELzW,EAAM0W,QAAUC,WAAWN,EAAOrW,EAAM4W,MAE3C,CACD5W,EAAM0W,QAAUC,WAAWN,EAAOrW,EAAM4W,OACxC5W,EAAM6W,MAAQ,WACZC,aAAa9W,EAAM0W,SACb1W,EAAA0W,QAAUC,WAAWN,EAAO,GACnC,EACD3J,EAAWqK,GAAGC,OAAQ,UAAWhX,EAAM6W,OACvCnK,EAAWqK,GAAGC,OAAQ,QAAShX,EAAM6W,MACtC,CAEQ,SAAAN,EAAcU,EAAKjX,GAC1B8W,aAAa9W,EAAM0W,SACnBhK,EAAWwK,IAAIF,OAAQ,UAAWhX,EAAM6W,OACxCnK,EAAWwK,IAAIF,OAAQ,QAAShX,EAAM6W,MACvC,CAhCDnK,EAAWyK,aAAa,eAAe,GAAO,SAASf,EAAIjM,GACrDiM,EAAGpW,MAAMoX,cACGb,EAAAH,EAAIA,EAAGpW,MAAMoX,aAC3BhB,EAAGpW,MAAMoX,YAAc,MAErBjN,GAA0C,GAAnCiM,EAAGxS,QAAQ0S,QAAQ3O,cACbwO,EAAAC,EAAIA,EAAGpW,MAAMoX,YAAc,CAACR,MAAOzM,EAAIyM,OAAS,KACrE,GA0BA,CAzCIX,CAAIC,uECOR,MAKA7K,EAAAnB,EAAA,CAA8CmN,aAAA,EAC/BC,QAAA,EACJ/C,KAPXve,EAOWue,KACGgD,aAAA,EACCH,aAAA,EACAI,cAAA,yKCAf,MAAA1hB,EAAAE,EAGAmF,EAAAC,EACAqc,EAAArhB,EAAA,OACAshB,EAAAhd,GAAA,KACE,MAAAyT,EAAA,CAAA,EACAwJ,EAAA7hB,EAAA6hB,UACA,OAAAA,EAAAtiB,MAAwB,IAAA,OAEpB8Y,EAAAyJ,QAAA,gBAAcC,EAAAF,mBAAAA,EAAAG,kCAAiFH,EAAAvd,MAAA,qCAAsDiG,GAAAsX,EAAAI,UAAA,YACrJ5J,EAAA6J,IAAA,wEAAUlX,GAAA6W,EAAAxW,KAAAe,MAAApB,GAAA6W,EAAAxW,KAAAiB,MAAAtB,GAAA6W,EAAAxW,KAAAgB,UAAArB,GAAA6W,EAAAxW,KAAAkB,+BAAyMsV,EAAAG,oDAEnNH,EAAAM,kBAAA5X,GAAAsX,EAAAI,0DAAqGJ,EAAAvd,MAAA8d,IAAAC,iBAAAR,EAAAvd,MAAA8d,IAAAE,gBAAAT,EAAAvd,MAAA8d,IAAA7gB,iBAAAsgB,EAAAvd,MAAAie,oBAGrG,MAAA,IAAA,QAEAlK,EAAAyJ,QAAA,YAAcC,EAAAF,KA8BpB,SAAAA,GACE,IAEAW,EAFAC,EAAA,GACA,GAAA,SAAAZ,EAAAtiB,WAAA,IAAAsiB,EAAAa,OAAA,GAAAb,EAAAa,MAAA7hB,OAAuG,OAAA4hB,EAEvG,IAAAD,KAAAX,EAAAa,MACE,GAAA,SAAAb,EAAAa,MAAAF,GAAAG,SAAA/E,cACE,MAAA,uBAAAiE,EAAAa,MAAAF,GAAAle,MAAA,YAAA,QAGJ,OAAAme,CAAO,CAvCWG,CAAAf,OACdxJ,EAAA6J,IAAA,oEAAUlX,GAAA6W,EAAAxW,KAAAe,MAAApB,GAAA6W,EAAAxW,KAAAiB,MAAAtB,GAAA6W,EAAAxW,KAAAgB,UAAArB,GAAA6W,EAAAxW,KAAAkB,gBAkBhB,SAAAsV,GACE,IAEAW,EAFAC,EAAA,GACA,GAAA,SAAAZ,EAAAtiB,WAAA,IAAAsiB,EAAAa,OAAA,GAAAb,EAAAa,MAAA7hB,OAAuG,OAAA4hB,EAEvG,IAAAD,KAAAX,EAAAa,MACE,GAAA,SAAAb,EAAAa,MAAAF,GAAAG,SAAA/E,cACE,MAAA,gDAAAiE,EAAAa,MAAAF,GAAAle,MAAA8d,IAAAC,EAAA,gBAAAR,EAAAa,MAAAF,GAAAle,MAAA8d,IAAAE,EAAA,eAAAT,EAAAa,MAAAF,GAAAle,MAAA8d,IAAA7gB,EAAA,gBAAAsgB,EAAAa,MAAAF,GAAAle,MAAAie,MAAA,eAGJ,OAAAE,CAAO,CA3B4MI,CAAAhB,KAC/M,MAAA,IAAA,QAEAxJ,EAAAyJ,QAAA,iBAAcC,EAAAF,KAWpB,SAAAA,GACE,MAAA,SAAAA,EAAAtiB,WAAA,IAAAsiB,EAAAiB,WAA6E,GAC7E,wBAAAjB,EAAAiB,WAAA,GAAAhjB,KAAA,IAAA+hB,EAAAiB,WAAA,GAAAC,OAAA,OAAuG,CAbrFC,CAAAnB,OACdxJ,EAAA6J,IAAA,wFAAUlX,GAAA6W,EAAAxW,KAAAe,MAAApB,GAAA6W,EAAAxW,KAAAiB,MAAAtB,GAAA6W,EAAAxW,KAAAgB,UAAArB,GAAA6W,EAAAxW,KAAAkB,gBAKhB,SAAAsV,GACE,MAAA,SAAAA,EAAAtiB,WAAA,IAAAsiB,EAAAiB,WAA6E,GAC7E,2CAAAjB,EAAAiB,WAAA,GAAAhjB,KAAA,IAAA+hB,EAAAiB,WAAA,GAAAC,OAAA,SAA0H,CAP6GE,CAAApB,KAGvO,OAAAxJ,CAAA,IAiCF,SAAA0J,EAAAF,GACE,MAAA,yBAAAtX,GAAAsX,EAAAxW,KAAAgB,OAAA,GAAA,+BAAA9B,GAAAsX,EAAAxW,KAAAkB,QAAA,GAAA,OAAyJ,g7BC9D3J,MAAAlH,EAAAC,0hBCuBA,MAAAoE,EAAAC,IACAkY,EAAAjd,GAAA,KACE,MAAAsF,EAAAR,EAAAQ,MACAiB,EAAAjB,EAAAiB,QACA,YAAA,IAAAjB,EAAAyB,eAAAR,GAAAA,EAAAC,QAAAlB,EAAAiB,QAAAC,OAAAlB,EAAAyB,eAGAzB,EAAAiB,QAAAC,OAAAlB,EAAAyB,eAFE,IAEF,IAEFtG,EAAAC,EACA4d,EAAA/Q,IACE9M,EAAA,OAAA8M,EAAA,4zDCoBF,MAAAgR,YAAAA,GAAAC,GAAA,CAAqCC,cAAA,IAGrC/gB,EAAAghB,IACA,IAAAC,EAAA,KACA,MAAAL,EAAAM,MAAArR,IACEoR,GACEA,EAAAE,cAEFN,EAAAhR,GACAoR,EAAAG,GAAA,CAA4BC,MAAA,OACnBpkB,KAAA,WACD,EAGVmK,EAAAC,IACAia,EAAAC,IAEAC,EAAAC,IACApR,EAAArS,EAAA,IACA0jB,EAAA1jB,EAAA,IACA2jB,EAAA3jB,GAAA,GACAmK,EAAAnK,EAAA,MACAsS,EAAAtS,EAAA,IACA4jB,EAAA5jB,EAAA,IACA6jB,EAAAvf,GAAA,IACEgf,EAAAQ,SAAAha,MAAAC,GAAAA,EAAAga,KAAAH,EAAAxjB,UAGF4jB,EAAA1f,GAAA,KACE,CAAO2f,KAAA,sBACCC,MAAA,CACCN,OAAAA,EAAAxjB,WAMX+jB,EAAA7f,GAAA,KACE,CAAO2f,KAAA,sBACCC,MAAA,CACCN,OAAAA,EAAAxjB,MACUgkB,UAAAja,EAAA/J,OAAA2jB,SAKrBM,EAAA/f,GAAA,IACE+N,EAAAjS,MAAA0J,MAAAC,GACEA,EAAAga,MAAAzR,EAAAlS,UAGJkkB,EAAAhgB,GAAA,IACEuf,EAAAzjB,MAGAyjB,EAAAzjB,MAAAkkB,WAFE,OAIJC,EAAA,KACE,MAAAxkB,EAAA,CAAA8jB,EAAAzjB,MAAAZ,MACA2K,EAAA/J,OACEL,EAAAwC,KAAA4H,EAAA/J,MAAAZ,MAEF6kB,EAAAjkB,QACEL,EAAAwC,KAAA8hB,EAAAjkB,MAAAokB,UACAzkB,EAAAwC,KAAA8hB,EAAAjkB,MAAAZ,OAEFilB,GAAAZ,EAAAzjB,MAAAskB,GAAAC,QAAA,6BAAAnB,EAAAU,MAAAtS,aAAAgS,EAAAxjB,SAAAL,EAAA,EAEF6kB,EAAA1B,MAAAtR,EAAAiT,GAAA,KACE,MAAAC,EAAA,CAAalT,MAGbmT,QAAAC,EAAAF,GACA,GAAA,GAAAC,EAAAE,KAAA,CACE,MAAAvf,EAAAqf,EAAArf,KACAA,EAAAwf,UAAA,CAAAC,KAAAC,MAAA1f,EAAA2f,WACA3f,EAAA+L,OAAA0T,KAAAC,MAAA1f,EAAA+L,QACA/L,EAAAoM,OAAAqT,KAAAC,MAAA1f,EAAAoM,QACA1I,EAAAkc,UAAA5f,GACAmf,IACEU,EAAA7f,EAAA8f,SACAC,EAAA/f,EAAA0e,WACF,GAIJqB,EAAAvC,MAAAtR,IACE,MAAAmT,QAAAW,GAAA,CAAuC9T,OAGvC,GAAAmT,EAAAE,OACE9a,EAAA/J,MAAA2kB,EAAArf,KAAoB,EAGxBigB,EAAAzC,UACE9Z,EAAAiH,gBAAA,EACAsT,EAAAvjB,OAAA,EACA,IACE,MAAA0kB,EAAA,CAAalT,GAAAyS,EAAAjkB,MAAAwlB,MACYJ,QAAAhC,EAAAU,MAAAsB,SAGzBT,QAAAc,GAAAf,GACA,GAAA,GAAAC,EAAAE,KAAA,CACE,MAAAvf,EAAAqf,EAAArf,KACAge,EAAAtjB,MAAAsF,CAAoB,CACtB,OAAApF,GAEAwlB,EAAAC,MAAAzlB,EAAA0lB,QAAkC,CAAA,QAElCrC,EAAAvjB,OAAA,CAAuB,GAG3BmlB,EAAArC,MAAAtR,IACE,MAAAkT,EAAA,CAAaU,QAAA5T,GAGbmT,QAAAkB,GAAAnB,GACA,GAAAC,EAAAE,OACE5S,EAAAjS,MAAA2kB,EAAArf,KAA0B,EAI9BwgB,EAAAtU,IACEA,GAAAU,EAAAlS,QAGAkS,EAAAlS,MAAAwR,EACAgT,EAAAhT,GAAA,EAGFuU,EAAA3gB,IACEA,EAAA,KAAAA,EAAA,IAGA4D,EAAAQ,MAAAqB,KAAAzF,EAAA,EAGFtF,GAAA,SAqDEoN,SAAA4C,KAAAkW,iBAAA,QAAAxW,IACAtC,SAAA4C,KAAAkW,iBAAA,YAAAzV,IACAiQ,OAAAwF,iBAAA,UAAA9X,IACAsS,OAAAwF,iBAAA,QAAAzX,IACAiS,OAAAwF,iBAAA,YAAAvX,IACA+R,OAAAwF,iBAAA,YAAAjX,IACAyR,OAAAwF,iBAAA,UAAAhX,IA5BAnK,GAAA,YAAAE,IACEA,EAAAC,iBACAgE,EAAAgH,eAAAhH,EAAAgH,aAAA,IAEFnL,GAAA,YAAAE,IACEA,EAAAC,iBACAgE,EAAAkH,eAAAlH,EAAAkH,aAAA,IAEFrL,GAAA,YAAAE,IACEA,EAAAC,iBACAgE,EAAAiH,eACEjH,EAAAiH,gBAAA,KAEe,OAvCrB,MAAAgW,EAAAnD,UACE,MAAAgB,EAAAV,EAAAU,MACA,GAAAA,EAAAtS,GAAA,CAGA,GAAAsS,EAAAoC,MAAA,CACE,MAAAC,QAAAC,GAAAtC,EAAAoC,OACA,IAAAC,EACE,OAEF3C,EAAAxjB,MAAAmmB,EACAvkB,EAAAP,QAAA,CAAewiB,KAAAT,EAAAS,KACDC,MAAA,CACLtS,GAAAsS,EAAAtS,GACKgS,OAAA2C,IAGb,MAED3C,EAAAxjB,MAAA8jB,EAAAN,OAIF,aAFAN,EAAA+C,OACA/T,EAAAlS,MAAA8jB,EAAAtS,GACAgT,EAAAV,EAAAtS,IAAA,EApBE,CAoBF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29]}