import{_ as e,d as t,c as r}from"./chunk.8df321e8.js";import{d as n,r as a,w as o,o as i,c as l,u as s,e as c,M as u,g as d,a3 as f,A as m,t as p,af as v,i as g,B as k,b as h,h as y,n as w,C as b}from"./index.05904f40.js";import{E as x}from"./chunk.6d705473.js";import{S as M,a as T}from"./chunk.ecdc8f95.js";import{l as S}from"./chunk.4ae50552.js";import{a as I,r as j,F as E}from"./chunk.74ff160c.js";import{d as _}from"./chunk.984ebab6.js";import"./chunk.380b3154.js";import"./chunk.db8898e3.js";import"./chunk.034f7efa.js";const C=["srcDoc"],z=e(n({__name:"preview",props:{code:{},appState:{}},setup(e){const t=e,r=a(""),n=S.throttle((()=>{console.log(t.code),r.value=t.code}),200,{trailing:!1});return o((()=>t.code),(e=>{n()})),(e,t)=>(i(),l("iframe",{title:"预览",srcDoc:s(r)},null,8,C))}}),[["__scopeId","data-v-c9e4e772"]]),N=e(n({__name:"codePreview",props:{code:{}},setup(e){const t=e,r=a();return o((()=>t.code),(e=>{r.value&&(r.value.scrollTop=r.value.scrollHeight)})),(e,t)=>(i(),l("div",{ref_key:"scrollRef",ref:r,class:"code-review"},c(e.code),513))}}),[["__scopeId","data-v-47ffcd24"]]);var A,O={};function V(){return A||(A=1,e=I(),t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1},e.defineMode("xml",(function(n,a){var o,i,l=n.indentUnit,s={},c=a.htmlMode?t:r;for(var u in c)s[u]=c[u];for(var u in a)s[u]=a[u];function d(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(p("atom","]]>")):null:e.match("--")?r(p("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(v(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=p("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=f,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function f(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=d,o=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return o="equals",null;if("<"==r){t.tokenize=d,t.state=w,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(r)?(t.tokenize=m(r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function m(e){var t=function(t,r){for(;!t.eol();)if(t.next()==e){r.tokenize=f;break}return"string"};return t.isInAttribute=!0,t}function p(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=d;break}r.next()}return e}}function v(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=v(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=d;break}return r.tokenize=v(e-1),r.tokenize(t,r)}}return"meta"}}function g(e){return e&&e.toLowerCase()}function k(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function h(e){e.context&&(e.context=e.context.prev)}function y(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!s.contextGrabbers.hasOwnProperty(g(r))||!s.contextGrabbers[g(r)].hasOwnProperty(g(t)))return;h(e)}}function w(e,t,r){return"openTag"==e?(r.tagStart=t.column(),b):"closeTag"==e?x:w}function b(e,t,r){return"word"==e?(r.tagName=t.current(),i="tag",S):s.allowMissingTagName&&"endTag"==e?(i="tag bracket",S(e,t,r)):(i="error",b)}function x(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&s.implicitlyClosed.hasOwnProperty(g(r.context.tagName))&&h(r),r.context&&r.context.tagName==n||!1===s.matchClosing?(i="tag",M):(i="tag error",T)}return s.allowMissingTagName&&"endTag"==e?(i="tag bracket",M(e,t,r)):(i="error",T)}function M(e,t,r){return"endTag"!=e?(i="error",M):(h(r),w)}function T(e,t,r){return i="error",M(e,t,r)}function S(e,t,r){if("word"==e)return i="attribute",I;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,a=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(g(n))?y(r,n):(y(r,n),r.context=new k(r,n,a==r.indented)),w}return i="error",S}function I(e,t,r){return"equals"==e?j:(s.allowMissing||(i="error"),S(e,t,r))}function j(e,t,r){return"string"==e?E:"word"==e&&s.allowUnquoted?(i="string",S):(i="error",S(e,t,r))}function E(e,t,r){return"string"==e?E:S(e,t,r)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:w,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(i=null,t.state=t.state(o||r,e,t),i&&(r="error"==i?r+" error":i)),r},indent:function(t,r,n){var a=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(a&&a.noIndent)return e.Pass;if(t.tokenize!=f&&t.tokenize!=d)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==s.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;a;){if(a.tagName==o[2]){a=a.prev;break}if(!s.implicitlyClosed.hasOwnProperty(g(a.tagName)))break;a=a.prev}else if(o)for(;a;){var i=s.contextGrabbers[g(a.tagName)];if(!i||!i.hasOwnProperty(g(o[2])))break;a=a.prev}for(;a&&a.prev&&!a.startOfLine;)a=a.prev;return a?a.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==j&&(e.state=S)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})),O;var e,t,r}var P,$={};function D(){return P||(P=1,(e=I()).defineMode("javascript",(function(t,r){var n,a,o=t.indentUnit,i=r.statementIndent,l=r.jsonld,s=r.json||l,c=!1!==r.trackScope,u=r.typescript,d=r.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),a=e("keyword d"),o=e("operator"),i={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:a,break:a,continue:a,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:i,false:i,null:i,undefined:i,NaN:i,Infinity:i,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),m=/[+\-*&%=<>!?|~^@]/,p=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function v(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function g(e,t,r){return n=e,a=r,t}function k(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=h(r),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return g("number","number");if("."==r&&e.match(".."))return g("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return g(r);if("="==r&&e.eat(">"))return g("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return g("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),g("number","number");if("/"==r)return e.eat("*")?(t.tokenize=y,y(e,t)):e.eat("/")?(e.skipToEnd(),g("comment","comment")):at(e,t,1)?(v(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),g("regexp","string-2")):(e.eat("="),g("operator","operator",e.current()));if("`"==r)return t.tokenize=w,w(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),g("meta","meta");if("#"==r&&e.eatWhile(d))return g("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),g("comment","comment");if(m.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?g("."):g("operator","operator",e.current());if(d.test(r)){e.eatWhile(d);var n=e.current();if("."!=t.lastType){if(f.propertyIsEnumerable(n)){var a=f[n];return g(a.type,a.style,n)}if("async"==n&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return g("async","keyword",n)}return g("variable","variable",n)}}function h(e){return function(t,r){var n,a=!1;if(l&&"@"==t.peek()&&t.match(p))return r.tokenize=k,g("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||a);)a=!a&&"\\"==n;return a||(r.tokenize=k),g("string","string")}}function y(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=k;break}n="*"==r}return g("comment","comment")}function w(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=k;break}n=!n&&"\\"==r}return g("quasi","string-2",e.current())}var b="([{}])";function x(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(u){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var a=0,o=!1,i=r-1;i>=0;--i){var l=e.string.charAt(i),s=b.indexOf(l);if(s>=0&&s<3){if(!a){++i;break}if(0==--a){"("==l&&(o=!0);break}}else if(s>=3&&s<6)++a;else if(d.test(l))o=!0;else if(/["'\/`]/.test(l))for(;;--i){if(0==i)return;if(e.string.charAt(i-1)==l&&"\\"!=e.string.charAt(i-2)){i--;break}}else if(o&&!a){++i;break}}o&&!a&&(t.fatArrowAt=i)}}var M={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function T(e,t,r,n,a,o){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=o,null!=n&&(this.align=n)}function S(e,t){if(!c)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}function I(e,t,r,n,a){var o=e.cc;for(j.state=e,j.stream=a,j.marked=null,j.cc=o,j.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():s?F:R)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return j.marked?j.marked:"variable"==r&&S(e,n)?"variable-2":t}}var j={state:null,column:null,marked:null,cc:null};function E(){for(var e=arguments.length-1;e>=0;e--)j.cc.push(arguments[e])}function _(){return E.apply(null,arguments),!0}function C(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function z(e){var t=j.state;if(j.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=N(e,t.context);if(null!=n)return void(t.context=n)}else if(!C(e,t.localVars))return void(t.localVars=new V(e,t.localVars));r.globalVars&&!C(e,t.globalVars)&&(t.globalVars=new V(e,t.globalVars))}}function N(e,t){if(t){if(t.block){var r=N(e,t.prev);return r?r==t.prev?t:new O(r,t.vars,!0):null}return C(e,t.vars)?t:new O(t.prev,new V(e,t.vars),!1)}return null}function A(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function O(e,t,r){this.prev=e,this.vars=t,this.block=r}function V(e,t){this.name=e,this.next=t}var P=new V("this",new V("arguments",null));function $(){j.state.context=new O(j.state.context,j.state.localVars,!1),j.state.localVars=P}function D(){j.state.context=new O(j.state.context,j.state.localVars,!0),j.state.localVars=null}function q(){j.state.localVars=j.state.context.vars,j.state.context=j.state.context.prev}function W(e,t){var r=function(){var r=j.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var a=r.lexical;a&&")"==a.type&&a.align;a=a.prev)n=a.indented;r.lexical=new T(n,j.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function G(){var e=j.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function L(e){function t(r){return r==e?_():";"==e||"}"==r||")"==r||"]"==r?E():_(t)}return t}function R(e,t){return"var"==e?_(W("vardef",t),Ee,L(";"),G):"keyword a"==e?_(W("form"),Y,R,G):"keyword b"==e?_(W("form"),R,G):"keyword d"==e?j.stream.match(/^\s*$/,!1)?_():_(W("stat"),J,L(";"),G):"debugger"==e?_(L(";")):"{"==e?_(W("}"),D,fe,G,q):";"==e?_():"if"==e?("else"==j.state.lexical.info&&j.state.cc[j.state.cc.length-1]==G&&j.state.cc.pop()(),_(W("form"),Y,R,G,Oe)):"function"==e?_(De):"for"==e?_(W("form"),D,Ve,R,q,G):"class"==e||u&&"interface"==t?(j.marked="keyword",_(W("form","class"==e?e:t),Re,G)):"variable"==e?u&&"declare"==t?(j.marked="keyword",_(R)):u&&("module"==t||"enum"==t||"type"==t)&&j.stream.match(/^\s*\w/,!1)?(j.marked="keyword","enum"==t?_(tt):"type"==t?_(We,L("operator"),ke,L(";")):_(W("form"),_e,L("{"),W("}"),fe,G,G)):u&&"namespace"==t?(j.marked="keyword",_(W("form"),F,R,G)):u&&"abstract"==t?(j.marked="keyword",_(R)):_(W("stat"),oe):"switch"==e?_(W("form"),Y,L("{"),W("}","switch"),D,fe,G,G,q):"case"==e?_(F,L(":")):"default"==e?_(L(":")):"catch"==e?_(W("form"),$,U,R,G,q):"export"==e?_(W("stat"),Ye,G):"import"==e?_(W("stat"),Je,G):"async"==e?_(R):"@"==t?_(F,R):E(W("stat"),F,L(";"),G)}function U(e){if("("==e)return _(Ge,L(")"))}function F(e,t){return H(e,t,!1)}function B(e,t){return H(e,t,!0)}function Y(e){return"("!=e?E():_(W(")"),J,L(")"),G)}function H(e,t,r){if(j.state.fatArrowAt==j.stream.start){var n=r?te:ee;if("("==e)return _($,W(")"),ue(Ge,")"),G,L("=>"),n,q);if("variable"==e)return E($,_e,L("=>"),n,q)}var a=r?Q:K;return M.hasOwnProperty(e)?_(a):"function"==e?_(De,a):"class"==e||u&&"interface"==t?(j.marked="keyword",_(W("form"),Le,G)):"keyword c"==e||"async"==e?_(r?B:F):"("==e?_(W(")"),J,L(")"),G,a):"operator"==e||"spread"==e?_(r?B:F):"["==e?_(W("]"),et,G,a):"{"==e?de(le,"}",null,a):"quasi"==e?E(X,a):"new"==e?_(re(r)):_()}function J(e){return e.match(/[;\}\)\],]/)?E():E(F)}function K(e,t){return","==e?_(J):Q(e,t,!1)}function Q(e,t,r){var n=0==r?K:Q,a=0==r?F:B;return"=>"==e?_($,r?te:ee,q):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?_(n):u&&"<"==t&&j.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?_(W(">"),ue(ke,">"),G,n):"?"==t?_(F,L(":"),a):_(a):"quasi"==e?E(X,n):";"!=e?"("==e?de(B,")","call",n):"."==e?_(ie,n):"["==e?_(W("]"),J,L("]"),G,n):u&&"as"==t?(j.marked="keyword",_(ke,n)):"regexp"==e?(j.state.lastType=j.marked="operator",j.stream.backUp(j.stream.pos-j.stream.start-1),_(a)):void 0:void 0}function X(e,t){return"quasi"!=e?E():"${"!=t.slice(t.length-2)?_(X):_(J,Z)}function Z(e){if("}"==e)return j.marked="string-2",j.state.tokenize=w,_(X)}function ee(e){return x(j.stream,j.state),E("{"==e?R:F)}function te(e){return x(j.stream,j.state),E("{"==e?R:B)}function re(e){return function(t){return"."==t?_(e?ae:ne):"variable"==t&&u?_(Se,e?Q:K):E(e?B:F)}}function ne(e,t){if("target"==t)return j.marked="keyword",_(K)}function ae(e,t){if("target"==t)return j.marked="keyword",_(Q)}function oe(e){return":"==e?_(G,R):E(K,L(";"),G)}function ie(e){if("variable"==e)return j.marked="property",_()}function le(e,t){return"async"==e?(j.marked="property",_(le)):"variable"==e||"keyword"==j.style?(j.marked="property","get"==t||"set"==t?_(se):(u&&j.state.fatArrowAt==j.stream.start&&(r=j.stream.match(/^\s*:\s*/,!1))&&(j.state.fatArrowAt=j.stream.pos+r[0].length),_(ce))):"number"==e||"string"==e?(j.marked=l?"property":j.style+" property",_(ce)):"jsonld-keyword"==e?_(ce):u&&A(t)?(j.marked="keyword",_(le)):"["==e?_(F,me,L("]"),ce):"spread"==e?_(B,ce):"*"==t?(j.marked="keyword",_(le)):":"==e?E(ce):void 0;var r}function se(e){return"variable"!=e?E(ce):(j.marked="property",_(De))}function ce(e){return":"==e?_(B):"("==e?E(De):void 0}function ue(e,t,r){function n(a,o){if(r?r.indexOf(a)>-1:","==a){var i=j.state.lexical;return"call"==i.info&&(i.pos=(i.pos||0)+1),_((function(r,n){return r==t||n==t?E():E(e)}),n)}return a==t||o==t?_():r&&r.indexOf(";")>-1?E(e):_(L(t))}return function(r,a){return r==t||a==t?_():E(e,n)}}function de(e,t,r){for(var n=3;n<arguments.length;n++)j.cc.push(arguments[n]);return _(W(t,r),ue(e,t),G)}function fe(e){return"}"==e?_():E(R,fe)}function me(e,t){if(u){if(":"==e)return _(ke);if("?"==t)return _(me)}}function pe(e,t){if(u&&(":"==e||"in"==t))return _(ke)}function ve(e){if(u&&":"==e)return j.stream.match(/^\s*\w+\s+is\b/,!1)?_(F,ge,ke):_(ke)}function ge(e,t){if("is"==t)return j.marked="keyword",_()}function ke(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(j.marked="keyword",_("typeof"==t?B:ke)):"variable"==e||"void"==t?(j.marked="type",_(Te)):"|"==t||"&"==t?_(ke):"string"==e||"number"==e||"atom"==e?_(Te):"["==e?_(W("]"),ue(ke,"]",","),G,Te):"{"==e?_(W("}"),ye,G,Te):"("==e?_(ue(Me,")"),he,Te):"<"==e?_(ue(ke,">"),ke):"quasi"==e?E(be,Te):void 0}function he(e){if("=>"==e)return _(ke)}function ye(e){return e.match(/[\}\)\]]/)?_():","==e||";"==e?_(ye):E(we,ye)}function we(e,t){return"variable"==e||"keyword"==j.style?(j.marked="property",_(we)):"?"==t||"number"==e||"string"==e?_(we):":"==e?_(ke):"["==e?_(L("variable"),pe,L("]"),we):"("==e?E(qe,we):e.match(/[;\}\)\],]/)?void 0:_()}function be(e,t){return"quasi"!=e?E():"${"!=t.slice(t.length-2)?_(be):_(ke,xe)}function xe(e){if("}"==e)return j.marked="string-2",j.state.tokenize=w,_(be)}function Me(e,t){return"variable"==e&&j.stream.match(/^\s*[?:]/,!1)||"?"==t?_(Me):":"==e?_(ke):"spread"==e?_(Me):E(ke)}function Te(e,t){return"<"==t?_(W(">"),ue(ke,">"),G,Te):"|"==t||"."==e||"&"==t?_(ke):"["==e?_(ke,L("]"),Te):"extends"==t||"implements"==t?(j.marked="keyword",_(ke)):"?"==t?_(ke,L(":"),ke):void 0}function Se(e,t){if("<"==t)return _(W(">"),ue(ke,">"),G,Te)}function Ie(){return E(ke,je)}function je(e,t){if("="==t)return _(ke)}function Ee(e,t){return"enum"==t?(j.marked="keyword",_(tt)):E(_e,me,Ne,Ae)}function _e(e,t){return u&&A(t)?(j.marked="keyword",_(_e)):"variable"==e?(z(t),_()):"spread"==e?_(_e):"["==e?de(ze,"]"):"{"==e?de(Ce,"}"):void 0}function Ce(e,t){return"variable"!=e||j.stream.match(/^\s*:/,!1)?("variable"==e&&(j.marked="property"),"spread"==e?_(_e):"}"==e?E():"["==e?_(F,L("]"),L(":"),Ce):_(L(":"),_e,Ne)):(z(t),_(Ne))}function ze(){return E(_e,Ne)}function Ne(e,t){if("="==t)return _(B)}function Ae(e){if(","==e)return _(Ee)}function Oe(e,t){if("keyword b"==e&&"else"==t)return _(W("form","else"),R,G)}function Ve(e,t){return"await"==t?_(Ve):"("==e?_(W(")"),Pe,G):void 0}function Pe(e){return"var"==e?_(Ee,$e):"variable"==e?_($e):E($e)}function $e(e,t){return")"==e?_():";"==e?_($e):"in"==t||"of"==t?(j.marked="keyword",_(F,$e)):E(F,$e)}function De(e,t){return"*"==t?(j.marked="keyword",_(De)):"variable"==e?(z(t),_(De)):"("==e?_($,W(")"),ue(Ge,")"),G,ve,R,q):u&&"<"==t?_(W(">"),ue(Ie,">"),G,De):void 0}function qe(e,t){return"*"==t?(j.marked="keyword",_(qe)):"variable"==e?(z(t),_(qe)):"("==e?_($,W(")"),ue(Ge,")"),G,ve,q):u&&"<"==t?_(W(">"),ue(Ie,">"),G,qe):void 0}function We(e,t){return"keyword"==e||"variable"==e?(j.marked="type",_(We)):"<"==t?_(W(">"),ue(Ie,">"),G):void 0}function Ge(e,t){return"@"==t&&_(F,Ge),"spread"==e?_(Ge):u&&A(t)?(j.marked="keyword",_(Ge)):u&&"this"==e?_(me,Ne):E(_e,me,Ne)}function Le(e,t){return"variable"==e?Re(e,t):Ue(e,t)}function Re(e,t){if("variable"==e)return z(t),_(Ue)}function Ue(e,t){return"<"==t?_(W(">"),ue(Ie,">"),G,Ue):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(j.marked="keyword"),_(u?ke:F,Ue)):"{"==e?_(W("}"),Fe,G):void 0}function Fe(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&A(t))&&j.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(j.marked="keyword",_(Fe)):"variable"==e||"keyword"==j.style?(j.marked="property",_(Be,Fe)):"number"==e||"string"==e?_(Be,Fe):"["==e?_(F,me,L("]"),Be,Fe):"*"==t?(j.marked="keyword",_(Fe)):u&&"("==e?E(qe,Fe):";"==e||","==e?_(Fe):"}"==e?_():"@"==t?_(F,Fe):void 0}function Be(e,t){if("!"==t)return _(Be);if("?"==t)return _(Be);if(":"==e)return _(ke,Ne);if("="==t)return _(B);var r=j.state.lexical.prev;return E(r&&"interface"==r.info?qe:De)}function Ye(e,t){return"*"==t?(j.marked="keyword",_(Ze,L(";"))):"default"==t?(j.marked="keyword",_(F,L(";"))):"{"==e?_(ue(He,"}"),Ze,L(";")):E(R)}function He(e,t){return"as"==t?(j.marked="keyword",_(L("variable"))):"variable"==e?E(B,He):void 0}function Je(e){return"string"==e?_():"("==e?E(F):"."==e?E(K):E(Ke,Qe,Ze)}function Ke(e,t){return"{"==e?de(Ke,"}"):("variable"==e&&z(t),"*"==t&&(j.marked="keyword"),_(Xe))}function Qe(e){if(","==e)return _(Ke,Qe)}function Xe(e,t){if("as"==t)return j.marked="keyword",_(Ke)}function Ze(e,t){if("from"==t)return j.marked="keyword",_(F)}function et(e){return"]"==e?_():E(ue(B,"]"))}function tt(){return E(W("form"),_e,L("{"),W("}"),ue(rt,"}"),G,G)}function rt(){return E(_e,Ne)}function nt(e,t){return"operator"==e.lastType||","==e.lastType||m.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function at(e,t,r){return t.tokenize==k&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return $.lex=D.lex=!0,q.lex=!0,G.lex=!0,{startState:function(e){var t={tokenize:k,lastType:"sof",cc:[],lexical:new T((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new O(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),x(e,t)),t.tokenize!=y&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=a&&"--"!=a?n:"incdec",I(t,r,n,a,e))},indent:function(t,n){if(t.tokenize==y||t.tokenize==w)return e.Pass;if(t.tokenize!=k)return 0;var a,l=n&&n.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var u=t.cc[c];if(u==G)s=s.prev;else if(u!=Oe&&u!=q)break}for(;("stat"==s.type||"form"==s.type)&&("}"==l||(a=t.cc[t.cc.length-1])&&(a==K||a==Q)&&!/^[,\.=+\-*:?[\(]/.test(n));)s=s.prev;i&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var d=s.type,f=l==d;return"vardef"==d?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==d&&"{"==l?s.indented:"form"==d?s.indented+o:"stat"==d?s.indented+(nt(t,n)?i||o:0):"switch"!=s.info||f||0==r.doubleIndentSwitch?s.align?s.column+(f?0:1):s.indented+(f?0:o):s.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:s?null:"/*",blockCommentEnd:s?null:"*/",blockCommentContinue:s?null:" * ",lineComment:s?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:s?"json":"javascript",jsonldMode:l,jsonMode:s,expressionAllowed:at,skipExpression:function(t){I(t,"atom","atom","true",new e.StringStream("",2,null))}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})),$;var e}!function(e){var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};function r(e,t,r){var n=e.current(),a=n.search(t);return a>-1?e.backUp(n.length-a):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}var n={};function a(e){var t=n[e];return t||(n[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}function o(e,t){var r=e.match(a(t));return r?/^\s*(.*?)\s*$/.exec(r[2])[1]:""}function i(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function l(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),a=e[r],o=a.length-1;o>=0;o--)n.unshift(a[o])}function s(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(!n[0]||n[1].test(o(t,n[0])))return n[2]}}e.defineMode("htmlmixed",(function(n,a){var o=e.getMode(n,{name:"xml",htmlMode:!0,multilineTagIndentFactor:a.multilineTagIndentFactor,multilineTagIndentPastTag:a.multilineTagIndentPastTag,allowMissingTagName:a.allowMissingTagName}),c={},u=a&&a.tags,d=a&&a.scriptTypes;if(l(t,c),u&&l(u,c),d)for(var f=d.length-1;f>=0;f--)c.script.unshift(["type",d[f].matches,d[f].mode]);function m(t,a){var l,u=o.token(t,a.htmlState),d=/\btag\b/.test(u);if(d&&!/[<>\s\/]/.test(t.current())&&(l=a.htmlState.tagName&&a.htmlState.tagName.toLowerCase())&&c.hasOwnProperty(l))a.inTag=l+" ";else if(a.inTag&&d&&/>$/.test(t.current())){var f=/^([\S]+) (.*)/.exec(a.inTag);a.inTag=null;var p=">"==t.current()&&s(c[f[1]],f[2]),v=e.getMode(n,p),g=i(f[1],!0),k=i(f[1],!1);a.token=function(e,t){return e.match(g,!1)?(t.token=m,t.localState=t.localMode=null,null):r(e,k,t.localMode.token(e,t.localState))},a.localMode=v,a.localState=e.startState(v,o.indent(a.htmlState,"",""))}else a.inTag&&(a.inTag+=t.current(),t.eol()&&(a.inTag+=" "));return u}return{startState:function(){return{token:m,inTag:null,localMode:null,localState:null,htmlState:e.startState(o)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(o,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?o.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||o}}}}),"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}(I(),V(),D(),j());const q=n({__name:"codeMirror",props:{code:{}},setup(e){const t=e,r=a(""),n=a(),l=u({mode:"text/html",theme:"monokai"});o((()=>t.code),(e=>{r.value=e}),{immediate:!0}),d((()=>{setTimeout((()=>{n.value?.refresh()}),1e3)})),f((()=>{n.value?.destroy()}));const c=(e,t)=>{console.log(e),console.log(t.getValue())},p=e=>{console.log(e.focus())};return(e,t)=>(i(),m(s(E),{value:r.value,"onUpdate:value":t[0]||(t[0]=e=>r.value=e),options:l,border:"",ref_key:"cmRef",ref:n,onChange:c,onReady:p},null,8,["value","options"]))}}),W={}.VITE_WS_BACKEND_URL||"wss://design-ws.sy.soyoung.com/generate-code",G="生成代码错误。";const L={class:"screen-body"},R={class:"screen-body__left"},U=["src"],F={class:"screen-body__content"},B={key:0,class:"screen-body__right"},Y=e(n({__name:"index",setup(e){const n=p(),c=a("INITIAL"),u=a(""),d=a(""),f=a([]);a([]),a("");const S=a(!1),I=a("desktop"),j=a({}),E=v(),C=a({isImageGenerationEnabled:!0,editorTheme:"cobalt"});const A=async e=>{try{S.value=!0;const{code:t,data:r,message:n}=await _({id:e});if(t!==x.OK)throw new Error(n);j.value=r,function(e,t){const r=e/t;I.value=r<=1.5?"mobile":"desktop"}(r.width,r.height),d.value=await P()}catch(t){console.log(t)}S.value=!1},O=()=>{V({generationType:"create",image:d.value})},V=e=>{f.value=[],c.value="CODING";!function(e,r,n,a,o){const i=new WebSocket(`${W}`);i.addEventListener("open",(()=>{i.send(JSON.stringify({event:"message",data:e}))})),i.addEventListener("message",(async e=>{const o=JSON.parse(e.data);"chunk"===o.type?r(o.value):"status"===o.type?a(o.value):"setCode"===o.type?n(o.value):"error"===o.type&&(console.error("Error generating code",o.value),t({message:o.value,type:"error"}))})),i.addEventListener("close",(e=>{console.log("Connection closed",e.code,e.reason),1e3!=e.code?(console.error("WebSocket error code",e),t({message:G,type:"error"})):o()})),i.addEventListener("error",(e=>{console.error("WebSocket error",e),t({message:G,type:"error"})}))}({...e,...C.value},(e=>{u.value+=e}),(e=>{u.value=e}),(e=>{f.value=[...f.value,e]}),(()=>{c.value="CODE_READY"}))},P=()=>new Promise(((e,t)=>{const r=new Image;r.onload=function(){e(function(e){let t=document.createElement("canvas");return t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0),t.toDataURL("image/png").replace(/^data:image\/?[A-z]*;base64,/,"")}(r))},r.crossOrigin="*",r.onerror=t,r.src=j.value.url}));return o((()=>E.query),(e=>{console.log(e),e.id&&A(e.id)}),{immediate:!0}),(e,t)=>{const a=r;return i(),l("div",{class:w({home:!0,"home-theme":s(n).themeShow})},[g(T,null,{default:k((()=>[g(M)])),_:1}),h("div",L,[h("div",R,["CODING"===c.value?(i(),m(N,{key:0,code:u.value},null,8,["code"])):y("",!0),h("div",{class:w({scan:"CODING"===c.value,"screen-body__left-image":!0})},[h("img",{loading:"lazy",style:{width:"100%"},src:j.value.url,alt:"Reference"},null,8,U)],2),g(a,{disabled:S.value,onClick:O},{default:k((()=>[b("开始生成")])),_:1},8,["disabled"])]),h("div",F,[h("div",{class:w({"preview-desktop":"desktop"===I.value,"preview-mobile":"mobile"===I.value})},["CODING"===c.value||"CODE_READY"===c.value?(i(),m(z,{key:0,appState:c.value,code:u.value,device:I.value},null,8,["appState","code","device"])):y("",!0)],2)]),"CODE_READY"===c.value?(i(),l("div",B,[g(q,{code:u.value},null,8,["code"])])):y("",!0)])],2)}}}),[["__scopeId","data-v-cc000a10"]]);export{Y as default};
//# sourceMappingURL=chunk.f8c4f303.js.map
