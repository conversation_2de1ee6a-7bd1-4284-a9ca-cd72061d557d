import{aF as s,d as a,z as e,o as l,c as o,b as n,O as t,n as c,u as i,A as r,B as u,i as p,q as f,h as d,f as k,T as g}from"./index.7c7944d0.js";import{h as m,j as y,u as b,m as v,an as h,E as C,o as B,x as E}from"./chunk.25a51fc3.js";const $=(s="")=>s.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d"),_=a=>s(a),x=m({type:{type:String,values:["success","info","warning","danger",""],default:""},closable:Boolean,disableTransitions:Boolean,hit:Boolean,color:{type:String,default:""},size:{type:String,values:y,default:""},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),S={close:s=>s instanceof MouseEvent,click:s=>s instanceof MouseEvent},T=a({name:"ElTag"});const j=E(B(a({...T,props:x,emits:S,setup(s,{emit:a}){const m=s,y=b(),B=v("tag"),E=e((()=>{const{type:s,hit:a,effect:e,closable:l,round:o}=m;return[B.b(),B.is("closable",l),B.m(s),B.m(y.value),B.m(e),B.is("hit",a),B.is("round",o)]})),$=s=>{a("close",s)},_=s=>{a("click",s)};return(s,a)=>s.disableTransitions?(l(),o("span",{key:0,class:c(i(E)),style:k({backgroundColor:s.color}),onClick:_},[n("span",{class:c(i(B).e("content"))},[t(s.$slots,"default")],2),s.closable?(l(),r(i(C),{key:0,class:c(i(B).e("close")),onClick:f($,["stop"])},{default:u((()=>[p(i(h))])),_:1},8,["class","onClick"])):d("v-if",!0)],6)):(l(),r(g,{key:1,name:`${i(B).namespace.value}-zoom-in-center`,appear:""},{default:u((()=>[n("span",{class:c(i(E)),style:k({backgroundColor:s.color}),onClick:_},[n("span",{class:c(i(B).e("content"))},[t(s.$slots,"default")],2),s.closable?(l(),r(i(C),{key:0,class:c(i(B).e("close")),onClick:f($,["stop"])},{default:u((()=>[p(i(h))])),_:1},8,["class","onClick"])):d("v-if",!0)],6)])),_:3},8,["name"]))}}),[["__file","tag.vue"]]));export{j as E,_ as c,$ as e,x as t};
//# sourceMappingURL=chunk.5b40f4f0.js.map
