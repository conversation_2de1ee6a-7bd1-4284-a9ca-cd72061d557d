{"version": 3, "file": "chunk.27426d56.js", "sources": ["../node_modules/element-plus/es/components/radio/src/radio.mjs", "../node_modules/element-plus/es/components/radio/src/constants.mjs", "../node_modules/element-plus/es/components/radio/src/use-radio.mjs", "../node_modules/element-plus/es/components/radio/src/radio2.mjs", "../node_modules/element-plus/es/components/radio/src/radio-button.mjs", "../node_modules/element-plus/es/components/radio/src/radio-button2.mjs", "../node_modules/element-plus/es/components/radio/src/radio-group.mjs", "../node_modules/element-plus/es/components/radio/src/radio-group2.mjs", "../node_modules/element-plus/es/components/radio/index.mjs", "../node_modules/element-plus/es/components/progress/src/progress.mjs", "../node_modules/element-plus/es/components/progress/src/progress2.mjs", "../node_modules/element-plus/es/components/progress/index.mjs", "../src/views/layouts/sketch/components/project.vue", "../src/views/layouts/sketch/components/group.vue", "../src/views/layouts/sketch/folderChoose.vue"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\n\nconst radioPropsBase = buildProps({\n  size: useSizeProp,\n  disabled: Boolean,\n  label: {\n    type: [String, Number, Boolean],\n    default: \"\"\n  }\n});\nconst radioProps = buildProps({\n  ...radioPropsBase,\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: \"\"\n  },\n  name: {\n    type: String,\n    default: \"\"\n  },\n  border: Boolean\n});\nconst radioEmits = {\n  [UPDATE_MODEL_EVENT]: (val) => isString(val) || isNumber(val) || isBoolean(val),\n  [CHANGE_EVENT]: (val) => isString(val) || isNumber(val) || isBoolean(val)\n};\n\nexport { radioEmits, radioProps, radioPropsBase };\n//# sourceMappingURL=radio.mjs.map\n", "const radioGroupKey = Symbol(\"radioGroupKey\");\n\nexport { radioGroupKey };\n//# sourceMappingURL=constants.mjs.map\n", "import { ref, inject, computed } from 'vue';\nimport '../../../constants/index.mjs';\nimport '../../form/index.mjs';\nimport { radioGroupKey } from './constants.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\n\nconst useRadio = (props, emit) => {\n  const radioRef = ref();\n  const radioGroup = inject(radioGroupKey, void 0);\n  const isGroup = computed(() => !!radioGroup);\n  const modelValue = computed({\n    get() {\n      return isGroup.value ? radioGroup.modelValue : props.modelValue;\n    },\n    set(val) {\n      if (isGroup.value) {\n        radioGroup.changeEvent(val);\n      } else {\n        emit && emit(UPDATE_MODEL_EVENT, val);\n      }\n      radioRef.value.checked = props.modelValue === props.label;\n    }\n  });\n  const size = useFormSize(computed(() => radioGroup == null ? void 0 : radioGroup.size));\n  const disabled = useFormDisabled(computed(() => radioGroup == null ? void 0 : radioGroup.disabled));\n  const focus = ref(false);\n  const tabIndex = computed(() => {\n    return disabled.value || isGroup.value && modelValue.value !== props.label ? -1 : 0;\n  });\n  return {\n    radioRef,\n    isGroup,\n    radioGroup,\n    focus,\n    size,\n    disabled,\n    tabIndex,\n    modelValue\n  };\n};\n\nexport { useRadio };\n//# sourceMappingURL=use-radio.mjs.map\n", "import { defineComponent, nextTick, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, withDirectives, isRef, withModifiers, vModelRadio, renderSlot, createTextVNode, toDisplayString } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { radioProps, radioEmits } from './radio.mjs';\nimport { useRadio } from './use-radio.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst _hoisted_1 = [\"value\", \"name\", \"disabled\"];\nconst __default__ = defineComponent({\n  name: \"ElRadio\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: radioProps,\n  emits: radioEmits,\n  setup(__props, { emit }) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const { radioRef, radioGroup, focus, size, disabled, modelValue } = useRadio(props, emit);\n    function handleChange() {\n      nextTick(() => emit(\"change\", modelValue.value));\n    }\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass([\n          unref(ns).b(),\n          unref(ns).is(\"disabled\", unref(disabled)),\n          unref(ns).is(\"focus\", unref(focus)),\n          unref(ns).is(\"bordered\", _ctx.border),\n          unref(ns).is(\"checked\", unref(modelValue) === _ctx.label),\n          unref(ns).m(unref(size))\n        ])\n      }, [\n        createElementVNode(\"span\", {\n          class: normalizeClass([\n            unref(ns).e(\"input\"),\n            unref(ns).is(\"disabled\", unref(disabled)),\n            unref(ns).is(\"checked\", unref(modelValue) === _ctx.label)\n          ])\n        }, [\n          withDirectives(createElementVNode(\"input\", {\n            ref_key: \"radioRef\",\n            ref: radioRef,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event) => isRef(modelValue) ? modelValue.value = $event : null),\n            class: normalizeClass(unref(ns).e(\"original\")),\n            value: _ctx.label,\n            name: _ctx.name || ((_a = unref(radioGroup)) == null ? void 0 : _a.name),\n            disabled: unref(disabled),\n            type: \"radio\",\n            onFocus: _cache[1] || (_cache[1] = ($event) => focus.value = true),\n            onBlur: _cache[2] || (_cache[2] = ($event) => focus.value = false),\n            onChange: handleChange,\n            onClick: _cache[3] || (_cache[3] = withModifiers(() => {\n            }, [\"stop\"]))\n          }, null, 42, _hoisted_1), [\n            [vModelRadio, unref(modelValue)]\n          ]),\n          createElementVNode(\"span\", {\n            class: normalizeClass(unref(ns).e(\"inner\"))\n          }, null, 2)\n        ], 2),\n        createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).e(\"label\")),\n          onKeydown: _cache[4] || (_cache[4] = withModifiers(() => {\n          }, [\"stop\"]))\n        }, [\n          renderSlot(_ctx.$slots, \"default\", {}, () => [\n            createTextVNode(toDisplayString(_ctx.label), 1)\n          ])\n        ], 34)\n      ], 2);\n    };\n  }\n});\nvar Radio = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"radio.vue\"]]);\n\nexport { Radio as default };\n//# sourceMappingURL=radio2.mjs.map\n", "import '../../../utils/index.mjs';\nimport { radioPropsBase } from './radio.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\n\nconst radioButtonProps = buildProps({\n  ...radioPropsBase,\n  name: {\n    type: String,\n    default: \"\"\n  }\n});\n\nexport { radioButtonProps };\n//# sourceMappingURL=radio-button.mjs.map\n", "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, withDirectives, createElementVNode, isRef, withModifiers, vModelRadio, normalizeStyle, renderSlot, createTextVNode, toDisplayString } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { useRadio } from './use-radio.mjs';\nimport { radioButtonProps } from './radio-button.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst _hoisted_1 = [\"value\", \"name\", \"disabled\"];\nconst __default__ = defineComponent({\n  name: \"ElRadioButton\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: radioButtonProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const { radioRef, focus, size, disabled, modelValue, radioGroup } = useRadio(props);\n    const activeStyle = computed(() => {\n      return {\n        backgroundColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        borderColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        boxShadow: (radioGroup == null ? void 0 : radioGroup.fill) ? `-1px 0 0 0 ${radioGroup.fill}` : \"\",\n        color: (radioGroup == null ? void 0 : radioGroup.textColor) || \"\"\n      };\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass([\n          unref(ns).b(\"button\"),\n          unref(ns).is(\"active\", unref(modelValue) === _ctx.label),\n          unref(ns).is(\"disabled\", unref(disabled)),\n          unref(ns).is(\"focus\", unref(focus)),\n          unref(ns).bm(\"button\", unref(size))\n        ])\n      }, [\n        withDirectives(createElementVNode(\"input\", {\n          ref_key: \"radioRef\",\n          ref: radioRef,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event) => isRef(modelValue) ? modelValue.value = $event : null),\n          class: normalizeClass(unref(ns).be(\"button\", \"original-radio\")),\n          value: _ctx.label,\n          type: \"radio\",\n          name: _ctx.name || ((_a = unref(radioGroup)) == null ? void 0 : _a.name),\n          disabled: unref(disabled),\n          onFocus: _cache[1] || (_cache[1] = ($event) => focus.value = true),\n          onBlur: _cache[2] || (_cache[2] = ($event) => focus.value = false),\n          onClick: _cache[3] || (_cache[3] = withModifiers(() => {\n          }, [\"stop\"]))\n        }, null, 42, _hoisted_1), [\n          [vModelRadio, unref(modelValue)]\n        ]),\n        createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).be(\"button\", \"inner\")),\n          style: normalizeStyle(unref(modelValue) === _ctx.label ? unref(activeStyle) : {}),\n          onKeydown: _cache[4] || (_cache[4] = withModifiers(() => {\n          }, [\"stop\"]))\n        }, [\n          renderSlot(_ctx.$slots, \"default\", {}, () => [\n            createTextVNode(toDisplayString(_ctx.label), 1)\n          ])\n        ], 38)\n      ], 2);\n    };\n  }\n});\nvar RadioButton = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"radio-button.vue\"]]);\n\nexport { RadioButton as default };\n//# sourceMappingURL=radio-button2.mjs.map\n", "import '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { radioEmits } from './radio.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\n\nconst radioGroupProps = buildProps({\n  id: {\n    type: String,\n    default: void 0\n  },\n  size: useSizeProp,\n  disabled: Boolean,\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: \"\"\n  },\n  fill: {\n    type: String,\n    default: \"\"\n  },\n  label: {\n    type: String,\n    default: void 0\n  },\n  textColor: {\n    type: String,\n    default: \"\"\n  },\n  name: {\n    type: String,\n    default: void 0\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  }\n});\nconst radioGroupEmits = radioEmits;\n\nexport { radioGroupEmits, radioGroupProps };\n//# sourceMappingURL=radio-group.mjs.map\n", "import { defineComponent, ref, nextTick, onMounted, computed, provide, reactive, toRefs, watch, openBlock, createElementBlock, unref, normalizeClass, renderSlot } from 'vue';\nimport '../../form/index.mjs';\nimport '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport { radioGroupProps, radioGroupEmits } from './radio-group.mjs';\nimport { radioGroupKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\n\nconst _hoisted_1 = [\"id\", \"aria-label\", \"aria-labelledby\"];\nconst __default__ = defineComponent({\n  name: \"ElRadioGroup\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: radioGroupProps,\n  emits: radioGroupEmits,\n  setup(__props, { emit }) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const radioId = useId();\n    const radioGroupRef = ref();\n    const { formItem } = useFormItem();\n    const { inputId: groupId, isLabeledByFormItem } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const changeEvent = (value) => {\n      emit(UPDATE_MODEL_EVENT, value);\n      nextTick(() => emit(\"change\", value));\n    };\n    onMounted(() => {\n      const radios = radioGroupRef.value.querySelectorAll(\"[type=radio]\");\n      const firstLabel = radios[0];\n      if (!Array.from(radios).some((radio) => radio.checked) && firstLabel) {\n        firstLabel.tabIndex = 0;\n      }\n    });\n    const name = computed(() => {\n      return props.name || radioId.value;\n    });\n    provide(radioGroupKey, reactive({\n      ...toRefs(props),\n      changeEvent,\n      name\n    }));\n    watch(() => props.modelValue, () => {\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch((err) => debugWarn(err));\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        id: unref(groupId),\n        ref_key: \"radioGroupRef\",\n        ref: radioGroupRef,\n        class: normalizeClass(unref(ns).b(\"group\")),\n        role: \"radiogroup\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.label || \"radio-group\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? unref(formItem).labelId : void 0\n      }, [\n        renderSlot(_ctx.$slots, \"default\")\n      ], 10, _hoisted_1);\n    };\n  }\n});\nvar RadioGroup = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"radio-group.vue\"]]);\n\nexport { RadioGroup as default };\n//# sourceMappingURL=radio-group2.mjs.map\n", "import '../../utils/index.mjs';\nimport Radio from './src/radio2.mjs';\nimport RadioButton from './src/radio-button2.mjs';\nimport RadioGroup from './src/radio-group2.mjs';\nexport { radioEmits, radioProps, radioPropsBase } from './src/radio.mjs';\nexport { radioGroupEmits, radioGroupProps } from './src/radio-group.mjs';\nexport { radioButtonProps } from './src/radio-button.mjs';\nexport { radioGroupKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\n\nconst ElRadio = withInstall(Radio, {\n  RadioButton,\n  RadioGroup\n});\nconst ElRadioGroup = withNoopInstall(RadioGroup);\nconst ElRadioButton = withNoopInstall(RadioButton);\n\nexport { ElRadio, ElRadioButton, ElRadioGroup, ElRadio as default };\n//# sourceMappingURL=index.mjs.map\n", "import '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\n\nconst progressProps = buildProps({\n  type: {\n    type: String,\n    default: \"line\",\n    values: [\"line\", \"circle\", \"dashboard\"]\n  },\n  percentage: {\n    type: Number,\n    default: 0,\n    validator: (val) => val >= 0 && val <= 100\n  },\n  status: {\n    type: String,\n    default: \"\",\n    values: [\"\", \"success\", \"exception\", \"warning\"]\n  },\n  indeterminate: {\n    type: Boolean,\n    default: false\n  },\n  duration: {\n    type: Number,\n    default: 3\n  },\n  strokeWidth: {\n    type: Number,\n    default: 6\n  },\n  strokeLinecap: {\n    type: definePropType(String),\n    default: \"round\"\n  },\n  textInside: {\n    type: Boolean,\n    default: false\n  },\n  width: {\n    type: Number,\n    default: 126\n  },\n  showText: {\n    type: Boolean,\n    default: true\n  },\n  color: {\n    type: definePropType([\n      String,\n      Array,\n      Function\n    ]),\n    default: \"\"\n  },\n  striped: Boolean,\n  stripedFlow: Boolean,\n  format: {\n    type: definePropType(Function),\n    default: (percentage) => `${percentage}%`\n  }\n});\n\nexport { progressProps };\n//# sourceMappingURL=progress.mjs.map\n", "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, renderSlot, toDisplayString, createCommentVNode, createBlock, withCtx, resolveDynamicComponent } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { WarningFilled, CircleCheck, CircleClose, Check, Close } from '@element-plus/icons-vue';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport { progressProps } from './progress.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isString, isFunction } from '@vue/shared';\n\nconst _hoisted_1 = [\"aria-valuenow\"];\nconst _hoisted_2 = { viewBox: \"0 0 100 100\" };\nconst _hoisted_3 = [\"d\", \"stroke\", \"stroke-linecap\", \"stroke-width\"];\nconst _hoisted_4 = [\"d\", \"stroke\", \"opacity\", \"stroke-linecap\", \"stroke-width\"];\nconst _hoisted_5 = { key: 0 };\nconst __default__ = defineComponent({\n  name: \"ElProgress\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: progressProps,\n  setup(__props) {\n    const props = __props;\n    const STATUS_COLOR_MAP = {\n      success: \"#13ce66\",\n      exception: \"#ff4949\",\n      warning: \"#e6a23c\",\n      default: \"#20a0ff\"\n    };\n    const ns = useNamespace(\"progress\");\n    const barStyle = computed(() => ({\n      width: `${props.percentage}%`,\n      animationDuration: `${props.duration}s`,\n      backgroundColor: getCurrentColor(props.percentage)\n    }));\n    const relativeStrokeWidth = computed(() => (props.strokeWidth / props.width * 100).toFixed(1));\n    const radius = computed(() => {\n      if ([\"circle\", \"dashboard\"].includes(props.type)) {\n        return Number.parseInt(`${50 - Number.parseFloat(relativeStrokeWidth.value) / 2}`, 10);\n      }\n      return 0;\n    });\n    const trackPath = computed(() => {\n      const r = radius.value;\n      const isDashboard = props.type === \"dashboard\";\n      return `\n          M 50 50\n          m 0 ${isDashboard ? \"\" : \"-\"}${r}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? \"-\" : \"\"}${r * 2}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? \"\" : \"-\"}${r * 2}\n          `;\n    });\n    const perimeter = computed(() => 2 * Math.PI * radius.value);\n    const rate = computed(() => props.type === \"dashboard\" ? 0.75 : 1);\n    const strokeDashoffset = computed(() => {\n      const offset = -1 * perimeter.value * (1 - rate.value) / 2;\n      return `${offset}px`;\n    });\n    const trailPathStyle = computed(() => ({\n      strokeDasharray: `${perimeter.value * rate.value}px, ${perimeter.value}px`,\n      strokeDashoffset: strokeDashoffset.value\n    }));\n    const circlePathStyle = computed(() => ({\n      strokeDasharray: `${perimeter.value * rate.value * (props.percentage / 100)}px, ${perimeter.value}px`,\n      strokeDashoffset: strokeDashoffset.value,\n      transition: \"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s\"\n    }));\n    const stroke = computed(() => {\n      let ret;\n      if (props.color) {\n        ret = getCurrentColor(props.percentage);\n      } else {\n        ret = STATUS_COLOR_MAP[props.status] || STATUS_COLOR_MAP.default;\n      }\n      return ret;\n    });\n    const statusIcon = computed(() => {\n      if (props.status === \"warning\") {\n        return WarningFilled;\n      }\n      if (props.type === \"line\") {\n        return props.status === \"success\" ? CircleCheck : CircleClose;\n      } else {\n        return props.status === \"success\" ? Check : Close;\n      }\n    });\n    const progressTextSize = computed(() => {\n      return props.type === \"line\" ? 12 + props.strokeWidth * 0.4 : props.width * 0.111111 + 2;\n    });\n    const content = computed(() => props.format(props.percentage));\n    function getColors(color) {\n      const span = 100 / color.length;\n      const seriesColors = color.map((seriesColor, index) => {\n        if (isString(seriesColor)) {\n          return {\n            color: seriesColor,\n            percentage: (index + 1) * span\n          };\n        }\n        return seriesColor;\n      });\n      return seriesColors.sort((a, b) => a.percentage - b.percentage);\n    }\n    const getCurrentColor = (percentage) => {\n      var _a;\n      const { color } = props;\n      if (isFunction(color)) {\n        return color(percentage);\n      } else if (isString(color)) {\n        return color;\n      } else {\n        const colors = getColors(color);\n        for (const color2 of colors) {\n          if (color2.percentage > percentage)\n            return color2.color;\n        }\n        return (_a = colors[colors.length - 1]) == null ? void 0 : _a.color;\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([\n          unref(ns).b(),\n          unref(ns).m(_ctx.type),\n          unref(ns).is(_ctx.status),\n          {\n            [unref(ns).m(\"without-text\")]: !_ctx.showText,\n            [unref(ns).m(\"text-inside\")]: _ctx.textInside\n          }\n        ]),\n        role: \"progressbar\",\n        \"aria-valuenow\": _ctx.percentage,\n        \"aria-valuemin\": \"0\",\n        \"aria-valuemax\": \"100\"\n      }, [\n        _ctx.type === \"line\" ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(ns).b(\"bar\"))\n        }, [\n          createElementVNode(\"div\", {\n            class: normalizeClass(unref(ns).be(\"bar\", \"outer\")),\n            style: normalizeStyle({ height: `${_ctx.strokeWidth}px` })\n          }, [\n            createElementVNode(\"div\", {\n              class: normalizeClass([\n                unref(ns).be(\"bar\", \"inner\"),\n                { [unref(ns).bem(\"bar\", \"inner\", \"indeterminate\")]: _ctx.indeterminate },\n                { [unref(ns).bem(\"bar\", \"inner\", \"striped\")]: _ctx.striped },\n                { [unref(ns).bem(\"bar\", \"inner\", \"striped-flow\")]: _ctx.stripedFlow }\n              ]),\n              style: normalizeStyle(unref(barStyle))\n            }, [\n              (_ctx.showText || _ctx.$slots.default) && _ctx.textInside ? (openBlock(), createElementBlock(\"div\", {\n                key: 0,\n                class: normalizeClass(unref(ns).be(\"bar\", \"innerText\"))\n              }, [\n                renderSlot(_ctx.$slots, \"default\", { percentage: _ctx.percentage }, () => [\n                  createElementVNode(\"span\", null, toDisplayString(unref(content)), 1)\n                ])\n              ], 2)) : createCommentVNode(\"v-if\", true)\n            ], 6)\n          ], 6)\n        ], 2)) : (openBlock(), createElementBlock(\"div\", {\n          key: 1,\n          class: normalizeClass(unref(ns).b(\"circle\")),\n          style: normalizeStyle({ height: `${_ctx.width}px`, width: `${_ctx.width}px` })\n        }, [\n          (openBlock(), createElementBlock(\"svg\", _hoisted_2, [\n            createElementVNode(\"path\", {\n              class: normalizeClass(unref(ns).be(\"circle\", \"track\")),\n              d: unref(trackPath),\n              stroke: `var(${unref(ns).cssVarName(\"fill-color-light\")}, #e5e9f2)`,\n              \"stroke-linecap\": _ctx.strokeLinecap,\n              \"stroke-width\": unref(relativeStrokeWidth),\n              fill: \"none\",\n              style: normalizeStyle(unref(trailPathStyle))\n            }, null, 14, _hoisted_3),\n            createElementVNode(\"path\", {\n              class: normalizeClass(unref(ns).be(\"circle\", \"path\")),\n              d: unref(trackPath),\n              stroke: unref(stroke),\n              fill: \"none\",\n              opacity: _ctx.percentage ? 1 : 0,\n              \"stroke-linecap\": _ctx.strokeLinecap,\n              \"stroke-width\": unref(relativeStrokeWidth),\n              style: normalizeStyle(unref(circlePathStyle))\n            }, null, 14, _hoisted_4)\n          ]))\n        ], 6)),\n        (_ctx.showText || _ctx.$slots.default) && !_ctx.textInside ? (openBlock(), createElementBlock(\"div\", {\n          key: 2,\n          class: normalizeClass(unref(ns).e(\"text\")),\n          style: normalizeStyle({ fontSize: `${unref(progressTextSize)}px` })\n        }, [\n          renderSlot(_ctx.$slots, \"default\", { percentage: _ctx.percentage }, () => [\n            !_ctx.status ? (openBlock(), createElementBlock(\"span\", _hoisted_5, toDisplayString(unref(content)), 1)) : (openBlock(), createBlock(unref(ElIcon), { key: 1 }, {\n              default: withCtx(() => [\n                (openBlock(), createBlock(resolveDynamicComponent(unref(statusIcon))))\n              ]),\n              _: 1\n            }))\n          ])\n        ], 6)) : createCommentVNode(\"v-if\", true)\n      ], 10, _hoisted_1);\n    };\n  }\n});\nvar Progress = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"progress.vue\"]]);\n\nexport { Progress as default };\n//# sourceMappingURL=progress2.mjs.map\n", "import '../../utils/index.mjs';\nimport Progress from './src/progress2.mjs';\nexport { progressProps } from './src/progress.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\n\nconst ElProgress = withInstall(Progress);\n\nexport { ElProgress, ElProgress as default };\n//# sourceMappingURL=index.mjs.map\n", "<template>\n  <el-drawer class=\"sketch-drawer\" :model-value=\"visible\" size=\"100%\" :with-header=\"false\">\n    <div class=\"sketch-drawer-header\">\n      <div class=\"sketch-drawer-header-back\" @click=\"emit('close')\">\n        <el-icon><Back /></el-icon>\n      </div>\n      <el-input class=\"sketch-drawer-header-filter\" v-model=\"filterText\" placeholder=\"搜索文件夹 / 项目\">\n        <template #prepend>\n          <el-button type=\"text\" :icon=\"Search\" />\n        </template>\n        <template #append>\n          <el-button @click=\"openAddLog\" type=\"text\" :icon=\"Plus\"></el-button>\n        </template>\n      </el-input>\n    </div>\n    <el-tree v-loading=\"loading\" class=\"group-tree\" @node-click=\"handleClick\" :expand-on-click-node=\"false\" :data=\"list\" node-key=\"_id\" default-expand-all>\n      <template #default=\"{ data }\">\n        <div v-if=\"data.type !== 'sketch'\" class=\"group-tree-node\">\n          <div class=\"node-content\" v-if=\"data.type == 'project'\">\n            <img class=\"node-icon\" src=\"https://static.soyoung.com/sy-pre/4stqnpoyi72d-1708945800688.png\" alt=\"\" /><span class=\"node-name\">{{ data.name }}</span>\n          </div>\n          <div class=\"node-content\" v-else>\n            <img class=\"node-icon\" src=\"https://static.soyoung.com/sy-pre/3pflew99abmn-1708416600703.png\" alt=\"\" /><span class=\"node-name\">{{ data.name }}</span>\n          </div>\n          <div v-if=\"data.type !== 'project'\" class=\"node-right-bar\">\n            <div class=\"node-right-bar-handle\">\n              <el-button @click.stop type=\"text\" @click=\"openAddLog(data)\"> 新建项目 </el-button>\n            </div>\n          </div>\n        </div>\n      </template>\n    </el-tree>\n    <!-- <ul v-loading=\"loading\" class=\"sketch-drawer-body\">\n      <li\n        v-for=\"item in activeList\"\n        :class=\"{\n          active: id == item._id\n        }\"\n        @click=\"handleClick(item)\"\n        :key=\"item._id\"\n      >\n        <el-icon><Files /></el-icon> {{ item.name }}\n      </li>\n      <el-empty v-if=\"!activeList.length\" description=\"暂无项目\" />\n    </ul> -->\n  </el-drawer>\n  <el-dialog class=\"sketch-add\" v-model=\"addSmbInfo.visible\" title=\"新建项目\" width=\"70%\">\n    <div class=\"sketch-add-item\">\n      <el-input placeholder=\"请输入项目名称\" v-model=\"addSmbInfo.name\"></el-input>\n    </div>\n    <div class=\"sketch-add-item-btn\"><el-checkbox v-model=\"autoCheck\" style=\"margin-right: 10px\" /> 创建后自动选择</div>\n    <template #footer>\n      <span class=\"sketch-add-footer\">\n        <el-button @click=\"handleAddClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleAddSubmit\"> 确定 </el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n<script lang=\"ts\" setup>\nimport { ref, watch, reactive, defineProps, defineEmits } from \"vue\";\nimport { Search, Back, Plus } from \"@element-plus/icons-vue\";\nimport { addProject, getTeamTreeList } from \"@/api/design\";\nimport { ObjectAny } from \"@/types\";\n\nconst props = defineProps<{\n  visible: boolean;\n  id: string;\n  teamId: string;\n}>();\n\nconst emit = defineEmits([\"change\", \"close\"]);\nconst loading = ref(false);\nconst filterText = ref(\"\");\nconst list = ref<any[]>([]);\nconst autoCheck = ref(false);\n\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (visible) {\n      getProjects();\n    } else {\n      filterText.value = \"\";\n    }\n  }\n);\n\nconst addSmbInfo = reactive<ObjectAny>({\n  visible: false,\n  name: \"\"\n});\nconst handleAddClose = () => {\n  addSmbInfo.visible = false;\n  addSmbInfo.folderId = null;\n  addSmbInfo.name = \"\";\n};\nconst handleAddSubmit = async () => {\n  if (!addSmbInfo.name) {\n    return;\n  }\n  const res = await addProject({\n    teamId: props.teamId,\n    name: addSmbInfo.name,\n    folderId: addSmbInfo.folderId || null\n  });\n  if (res.code === 0) {\n    handleAddClose();\n    if (autoCheck.value) {\n      emit(\"change\", res.data);\n      return;\n    }\n    getProjects();\n  }\n};\nconst openAddLog = (data: ObjectAny) => {\n  addSmbInfo.visible = true;\n  addSmbInfo.folderId = data._id;\n};\n\nconst getProjects = async () => {\n  loading.value = true;\n  const res = await getTeamTreeList({\n    teamId: props.teamId\n  });\n  list.value = res.data;\n  loading.value = false;\n};\n\nconst handleClick = (node) => {\n  if (node.type !== \"project\") {\n    return;\n  }\n  emit(\"change\", node);\n};\n</script>\n<style lang=\"less\" scoped>\n.sketch-drawer {\n  &-header {\n    height: 44px;\n    display: flex;\n    align-items: center;\n    &-back {\n      font-weight: 600;\n      font-size: 24px;\n      margin-right: 10px;\n      display: inline-flex;\n\n      align-items: center;\n      cursor: pointer;\n      transition: color 0.3s;\n      &:hover {\n        color: #5c54f0;\n      }\n    }\n    &-filter {\n      height: 38px;\n      border-radius: 5px;\n      flex: 1;\n      ::v-deep {\n        .el-input-group__prepend {\n          background: transparent;\n        }\n      }\n    }\n  }\n  &-body {\n    height: calc(100% - 44px);\n    padding: 15px 5px;\n    box-sizing: border-box;\n  }\n  .el-tree {\n    height: 100%;\n    padding: 10px 0;\n    ::v-deep {\n      .el-tree-node__content {\n        height: 40px;\n      }\n    }\n  }\n  .sketch-tree-node {\n    display: flex;\n    width: 100%;\n    align-items: center;\n    font-size: 15px;\n    justify-content: space-between;\n    height: 50px;\n    box-sizing: border-box;\n    padding: 5px;\n    &:hover {\n      .el-button {\n        display: block;\n      }\n    }\n    .el-button {\n      font-size: 16px;\n      display: none;\n    }\n    .el-icon {\n      margin-right: 10px;\n    }\n  }\n}\n.sketch-add {\n  .sketch-add-item-btn {\n    display: flex;\n    align-items: center;\n    margin-top: 10px;\n  }\n}\n.group-tree {\n  overflow: overlay;\n  // padding: 0 12px;\n  ::v-deep {\n    .el-tree-node__content {\n      height: 35px;\n      box-sizing: border-box;\n    }\n  }\n  .group-tree-node {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: calc(100% - 30px);\n    height: 100%;\n    .iconfont {\n      font-size: 12px;\n      margin-right: 5px;\n    }\n    &:hover {\n      .node-right-bar-handle {\n        // display: block;\n        visibility: visible;\n        position: relative;\n      }\n    }\n    .node-root {\n      font-size: 16px;\n      color: #303233;\n      font-family: PingFangSC-Medium;\n      font-weight: 500;\n    }\n    .node-content {\n      display: flex;\n      // width: calc(100% - 60px);\n      align-items: center;\n      .node-icon {\n        display: block;\n        width: 15px;\n        height: 15px;\n        align-self: center;\n      }\n      .node-name {\n        margin-left: 8px;\n        font-family: PingFangSC-Regular;\n        font-size: 14px;\n        color: #303233;\n        font-weight: 400;\n        display: block;\n        // width: 200px;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n    .node-label {\n      flex: 0 0 160px;\n      align-items: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    .node-right-bar {\n      display: flex;\n      align-items: center;\n      .node-count {\n        font-size: 12px;\n        color: #8e8e8e;\n      }\n      &-handle {\n        // display: none;\n        visibility: hidden;\n        margin-left: 10px;\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <el-drawer class=\"sketch-drawer\" :model-value=\"visible\" size=\"100%\" :with-header=\"false\">\n    <div class=\"sketch-drawer-header\">\n      <div class=\"sketch-drawer-header-back\" @click=\"emit('close')\">\n        <el-icon><Back /></el-icon>\n      </div>\n      <el-input class=\"sketch-drawer-header-filter\" v-model=\"filterText\" placeholder=\"搜索分组\">\n        <template #prepend>\n          <el-button type=\"text\" :icon=\"Search\" />\n        </template>\n        <template #append>\n          <el-button @click=\"openAddLog()\" type=\"text\" :icon=\"Plus\"></el-button>\n        </template>\n      </el-input>\n    </div>\n    <div v-loading=\"loading\" class=\"sketch-drawer-body\">\n      <el-tree :auto-expand-parent=\"false\" node-key=\"_id\" highlight-current :expand-on-click-node=\"false\" ref=\"treeRef\" :current-node-key=\"id\" class=\"filter-tree\" :data=\"list\" :props=\"defaultProps\" @node-click=\"nodeClick\" default-expand-all :filter-node-method=\"filterNode\">\n        <template #default=\"{ node, data }\">\n          <div class=\"sketch-tree-node\">\n            <div>\n              <el-icon><FolderOpened /></el-icon><span>{{ node.label }}</span>\n            </div>\n            <el-button @click.stop=\"openAddLog(data)\" type=\"text\" :icon=\"FolderAdd\"></el-button>\n          </div>\n        </template>\n        <template #empty>\n          <el-empty description=\"暂无项目\" />\n        </template>\n      </el-tree>\n    </div>\n  </el-drawer>\n  <el-dialog class=\"sketch-add\" v-model=\"addSmbInfo.visible\" title=\"新建分组\" width=\"70%\">\n    <div v-if=\"addSmbInfo.parent\" class=\"sketch-add-item\">{{ getFullPath() }} /</div>\n    <div class=\"sketch-add-item\">\n      <el-input placeholder=\"请输入分组名称\" v-model=\"addSmbInfo.name\"></el-input>\n    </div>\n    <template #footer>\n      <span class=\"sketch-add-footer\">\n        <el-button @click=\"handleAddClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleAddSubmit\"> 确定 </el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n<script lang=\"ts\" setup>\nimport { ref, watch, reactive, defineProps, defineEmits } from \"vue\";\nimport { Search, Back, Plus, FolderOpened, FolderAdd } from \"@element-plus/icons-vue\";\nimport { addGroup, getGroupList } from \"@/api/design\";\nimport { ElTree } from \"element-plus\";\nimport { ObjectAny } from \"@/types\";\nconst treeRef = ref<InstanceType<typeof ElTree>>();\nconst props = defineProps<{\n  visible: boolean;\n  id: string;\n  projectId: string;\n}>();\n\nconst defaultProps = {\n  label: \"name\",\n  children: \"children\"\n};\nconst emit = defineEmits([\"change\", \"close\"]);\nconst loading = ref(false);\nconst filterText = ref(\"\");\nconst list = ref<ObjectAny[]>([]);\nwatch(filterText, (val) => {\n  treeRef.value!.filter(val);\n});\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (visible) {\n      getGroups();\n    } else {\n      filterText.value = \"\";\n    }\n  }\n);\n\nwatch(\n  () => props.projectId,\n  (projectId) => {\n    if (projectId) {\n      projectChage();\n    }\n  }\n);\n\nconst projectChage = async () => {\n  await getGroups();\n  if (list.value.length) {\n    nodeClick(list.value.find((item) => item.name === \"未分组\") || list.value[0]);\n  }\n  console.log(list.value);\n};\nconst addSmbInfo = reactive<{ visible: boolean; name: string; parent: any }>({\n  visible: false,\n  name: \"\",\n  parent: null\n});\nconst getFullPath = () => {\n  return addSmbInfo.parent?.fullPath.map(({ name }) => name).join(\" / \");\n};\nconst handleAddClose = () => {\n  addSmbInfo.visible = false;\n  addSmbInfo.parent = null;\n  addSmbInfo.name = \"\";\n};\nconst handleAddSubmit = async () => {\n  if (!addSmbInfo.name) {\n    return;\n  }\n  const res = await addGroup({\n    projectId: props.projectId,\n    parentId: addSmbInfo.parent?._id,\n    name: addSmbInfo.name\n  });\n  if (res.code === 0) {\n    getGroups();\n    handleAddClose();\n  }\n};\nconst openAddLog = (parent?: any) => {\n  addSmbInfo.visible = true;\n  addSmbInfo.parent = parent;\n};\n\nconst getGroups = async () => {\n  loading.value = true;\n  const res = await getGroupList({\n    projectId: props.projectId\n  });\n  list.value = res.data;\n  loading.value = false;\n};\nconst nodeClick = (node) => {\n  emit(\"change\", node);\n};\nconst filterNode = (value: string, data) => {\n  if (!value) return true;\n  return data.label.includes(value);\n};\n</script>\n<style lang=\"less\" scoped>\n.sketch-drawer {\n  &-header {\n    height: 44px;\n    display: flex;\n    align-items: center;\n    &-back {\n      font-weight: 600;\n      font-size: 24px;\n      margin-right: 10px;\n      display: inline-flex;\n\n      align-items: center;\n      cursor: pointer;\n      transition: color 0.3s;\n      &:hover {\n        color: #5c54f0;\n      }\n    }\n    &-filter {\n      height: 38px;\n      border-radius: 5px;\n      flex: 1;\n      ::v-deep {\n        .el-input-group__prepend {\n          background: transparent;\n        }\n      }\n    }\n  }\n  &-body {\n    height: calc(100% - 44px);\n  }\n  .el-tree {\n    height: 100%;\n    padding: 10px 0;\n    ::v-deep {\n      .el-tree-node__content {\n        height: 40px;\n      }\n    }\n  }\n  .sketch-tree-node {\n    display: flex;\n    width: 100%;\n    align-items: center;\n    font-size: 15px;\n    justify-content: space-between;\n    height: 50px;\n    box-sizing: border-box;\n    padding: 5px;\n    &:hover {\n      .el-button {\n        display: block;\n      }\n    }\n    .el-button {\n      font-size: 16px;\n      display: none;\n    }\n    .el-icon {\n      margin-right: 10px;\n    }\n  }\n}\n</style>\n", "<template>\n  <div class=\"sketch-progress\" v-if=\"progressInfo !== null\">\n    <div class=\"sketch-progress-content\" v-if=\"!progressInfo.done\">\n      <el-progress :color=\"customColors\" :stroke-width=\"10\" indeterminate :width=\"200\" type=\"dashboard\" :percentage=\"+progressInfo.process\">\n        <template #default=\"{ percentage }\">\n          <span class=\"percentage-value\">{{ percentage }}%</span>\n          <span class=\"percentage-label\">{{ progressInfo.name || \"\" }}</span>\n        </template>\n      </el-progress>\n    </div>\n    <template v-else>\n      <div class=\"sketch-progress-done\">\n        <el-icon class=\"sketch-progress-done-icon\"><CircleCheckFilled /></el-icon>\n        <div class=\"sketch-progress-done-text\">上传成功</div>\n        <div class=\"sketch-progress-done-desc\">\n          新增画板： {{ progressInfo.add.length }} <br />\n          更新画板： {{ progressInfo.update.length }}\n        </div>\n      </div>\n      <div class=\"sketch-progress-btn\">\n        <el-button type=\"primary\" @click=\"openExternal\" round>查看设计</el-button>\n        <el-button @click=\"progressInfo = null\" round>继续上传</el-button>\n        <el-button @click=\"close\" round>关 闭</el-button>\n      </div>\n    </template>\n  </div>\n  <template v-else>\n    <div class=\"sketch\" v-if=\"userInfo.ssoId\">\n      <div class=\"sketch-logo\">\n        <img loading=\"lazy\" src=\"https://static.soyoung.com/sy-design/2gv1oqwpo580u1716895637825.png\" alt=\"新氧画廊LOGO\" />\n        <!-- <el-divider direction=\"vertical\" /> -->\n        <div class=\"sketch-bar\">\n          <el-dropdown @command=\"handleCommand\" trigger=\"click\">\n            <span class=\"sketch-dropdown-link\">\n              <div>{{ team.current?.name || \"-\" }}</div>\n              <el-icon class=\"el-icon--right\"><arrow-down /></el-icon>\n            </span>\n            <template #dropdown>\n              <el-dropdown-menu>\n                <el-dropdown-item v-for=\"(item, i) in team.list\" :disabled=\"item.permission !== Permission.OWNER && item.permission !== Permission.MANAGE\" :command=\"i\" :key=\"item._id\">\n                  <div>\n                    {{ item.name }}\n                    <div v-if=\"item.permission !== Permission.OWNER && item.permission !== Permission.MANAGE\">(无上传权限)</div>\n                  </div>\n                </el-dropdown-item>\n                <el-dropdown-item command=\"create\">\n                  <el-icon><Plus /></el-icon>\n                  新增团队\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n          <div class=\"sketch__userInfo\" v-if=\"userInfo.ssoId\">\n            <img loading=\"lazy\" class=\"sketch__avatar\" :src=\"userInfo.url || 'https://static.soyoung.com/sy-pre/<EMAIL>'\" alt=\"\" />\n          </div>\n        </div>\n      </div>\n      <div class=\"sketch-selected\">\n        <!-- {{ artBoardList.length }} -->\n        <div v-if=\"!artBoardList.length\" class=\"sketch-selected-placeholder\">\n          <el-empty description=\"未选中画板\" />\n        </div>\n        <ul v-else class=\"sketch-selected-list\">\n          <li v-for=\"(name, index) in artBoardList\" class=\"sketch-selected-li\" :key=\"index\">\n            <el-icon><Monitor /></el-icon> {{ name }}\n          </li>\n        </ul>\n      </div>\n      <div class=\"sketch-radio\">\n        <el-radio-group v-model=\"sketchSelectType\">\n          <el-radio label=\"1\"\n            >该页面全部画板<span v-if=\"sketchSelectType == '1'\">({{ artBoardList.length }})</span></el-radio\n          >\n          <el-radio label=\"2\"\n            >选中画板 <span v-if=\"sketchSelectType == '2'\">({{ artBoardList.length }})</span></el-radio\n          >\n        </el-radio-group>\n      </div>\n      <div class=\"sketch-info\" @click=\"projectClick\">\n        <div class=\"sketch-info-label\">项目</div>\n        <div class=\"sketch-info-content\">\n          <div class=\"sketch-info-content__text\">\n            {{ project.current?.name || \"未选择\" }}\n          </div>\n          <el-icon><ArrowRightBold /></el-icon>\n        </div>\n      </div>\n      <div class=\"sketch-info\" @click=\"groupClick\">\n        <div class=\"sketch-info-label\">分组</div>\n        <div class=\"sketch-info-content\">\n          <div class=\"sketch-info-content__text\">\n            {{ getGroupName() }}\n          </div>\n          <el-icon><ArrowRightBold /></el-icon>\n        </div>\n      </div>\n      <!-- <div class=\"sketch-info\" @click=\"versionClick\">\n      <div class=\"sketch-info-label\">版本</div>\n      <div class=\"sketch-info-content\">\n        <div class=\"sketch-info-content__text\">\n          {{ version?.name || \"未选择\" }}\n        </div>\n        <el-icon><ArrowRightBold /></el-icon>\n      </div>\n    </div> -->\n      <div class=\"sketch-submit\">\n        <el-button class=\"submit-button\" @click=\"submit\" type=\"primary\" round>上 传</el-button>\n        <el-dropdown @command=\"settingCommand\" trigger=\"click\">\n          <span class=\"sketch-setting\">\n            <el-icon><Setting /></el-icon>\n            设置\n          </span>\n          <template #dropdown>\n            <el-dropdown-menu class=\"setting-menu\">\n              <el-dropdown-item disabled>\n                <div class=\"setting-menu-item\">\n                  <div>版本更新</div>\n                  <span>v1.0</span>\n                </div>\n              </el-dropdown-item>\n              <el-dropdown-item command=\"jumpTo\"> 前往画廊 </el-dropdown-item>\n              <el-dropdown-item divided command=\"logout\"> 退出登录 </el-dropdown-item>\n            </el-dropdown-menu>\n          </template>\n        </el-dropdown>\n      </div>\n    </div>\n    <div v-else class=\"sketch-login\">\n      <img src=\"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png\" alt=\"\" />\n      <el-button color=\"#626aef\" size=\"large\" :disabled=\"!!uuid\" @click=\"ssoLogin\" dark>登 录</el-button>\n    </div>\n  </template>\n\n  <el-dialog class=\"sketch-add\" v-model=\"addTeamInfo.visible\" title=\"新建团队\" width=\"70%\">\n    <div class=\"sketch-add-item\">\n      <el-input placeholder=\"请输入团队名称\" v-model=\"addTeamInfo.name\"></el-input>\n    </div>\n    <template #footer>\n      <span class=\"sketch-add-footer\">\n        <el-button @click=\"handleAddClose\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleAddSubmit\"> 确定 </el-button>\n      </span>\n    </template>\n  </el-dialog>\n  <ProjectDrawer :visible=\"project.visible\" :id=\"project.current?._id\" :teamId=\"team.current?._id\" @close=\"handleCloseProject\" @change=\"handleProjectChange\" />\n  <GroupDrawer :visible=\"group.visible\" :id=\"group.current?._id\" :projectId=\"project.current?._id\" @close=\"handleCloseGroup\" @change=\"handleGroupChange\" />\n</template>\n<script lang=\"ts\" setup>\nimport { ref, onMounted, watch, reactive } from \"vue\";\nimport { ElMessage } from \"element-plus\";\nimport { userInfoStore } from \"@/store\";\nimport { Monitor, ArrowRightBold, ArrowDown, CircleCheckFilled, Plus, Setting } from \"@element-plus/icons-vue\";\nimport { upload } from \"@/api/common\";\nimport { getTeamList, addSketch, sendStatus, addTeam } from \"@/api/design\";\nimport { ObjectAny } from \"@/types/index\";\nimport ProjectDrawer from \"./components/project.vue\";\nimport GroupDrawer from \"./components/group.vue\";\nimport { FormItem } from \".\";\nimport { Permission } from \"@/model\";\nimport { setUserLogout, userCheckPoling, getUserInfo } from \"@/api/login\";\nimport { createPoller } from \"@/utils/date\";\nimport { de } from \"element-plus/es/locale\";\nconst sketchSelectType = ref(\"2\");\nconst userInfo = userInfoStore();\nconst customColors = [\n  { color: \"#f56c6c\", percentage: 20 },\n  { color: \"#e6a23c\", percentage: 40 },\n  { color: \"#5cb87a\", percentage: 60 },\n  { color: \"#1989fa\", percentage: 80 },\n  { color: \"#6f7ad3\", percentage: 100 }\n];\n\nconst team = reactive<FormItem>({\n  list: [],\n  current: null\n});\nconst addTeamInfo = reactive<ObjectAny>({\n  visible: false,\n  name: \"\"\n});\nconst project = ref<ObjectAny>({});\nconst group = ref<ObjectAny>({});\nconst uuid = ref<string>(\"\");\n// const version = ref<ObjectAny>({});\nconst artBoardList = ref([]);\nconst timer = ref<any>(null);\nconst poller = ref<ObjectAny | null>(null);\n\nconst progressInfo = ref<null | ObjectAny>(null);\nwatch(\n  () => sketchSelectType.value,\n  () => {\n    window.postMessage(\"getSelectedBoard\", sketchSelectType.value);\n  }\n);\n\nconst submit = () => {\n  if (!group.value?.current) {\n    ElMessage.error(\"请选择分组\");\n    return;\n  }\n  progressInfo.value = {\n    process: 0\n  };\n  window.postMessage(\"submit\", sketchSelectType.value);\n};\nwindow.sketchError = (e) => {\n  console.log(\"sketchError\", e);\n};\nwindow.sketchResult = (jsonString) => {\n  try {\n    uploadJson(jsonString);\n  } catch (error) {\n    console.log(error);\n  }\n};\n// 上传sketch 资源 到cdn\nwindow.uploadInfo = (info) => {\n  // setTimeout(() => {\n  //   window.postMessage(\"uploadResult\", {\n  //     exportable: [],\n  //     index: info.index,\n  //     type: info.type\n  //   });\n  // }, 1000);\n  if (info.type === \"slice\") {\n    uploadSlice(info.export, info.index);\n  } else {\n    uploadArtboard(info.export, info.index);\n  }\n};\n// 上传进度\nwindow.sketchProcess = (process) => {\n  progressInfo.value = process;\n};\n// 获取当前画板的信息\nwindow.sketchArtBoard = (list) => {\n  artBoardList.value = list;\n};\n// const getFullPath = () => {\n//   return addSmbInfo.parent?.fullPath.map(({ name }) => name).join(\" / \");\n// };\n\nconst uploadSlice = async (exportInfo, index) => {\n  const info: ObjectAny = {\n    exportable: [],\n    index,\n    type: \"slice\"\n  };\n  for (let i = 0; i < exportInfo.length; i++) {\n    const item = exportInfo[i];\n    const path = await uploadBuffer(item);\n    info.exportable.push({\n      path,\n      format: item.format,\n      name: item.path\n    });\n  }\n  window.postMessage(\"uploadResult\", info);\n};\n\nconst uploadArtboard = async (exportInfo, index) => {\n  const info: ObjectAny = {\n    exportable: {\n      format: exportInfo.format,\n      name: exportInfo.name\n    },\n    index,\n    type: \"artboard\"\n  };\n  info.exportable.path = await uploadBuffer(exportInfo);\n  window.postMessage(\"uploadResult\", info);\n};\nconst uploadBuffer = async (options) => {\n  const formData = new FormData();\n  const arraybuffer = new Int8Array(options.buffer.data);\n  const blob = new Blob([arraybuffer], { type: options.format });\n  const file = new File([blob], options.name + \".\" + options.format, {\n    type: options.format\n  });\n  formData.append(\"file\", file);\n  const { data } = await upload(formData);\n  return data.data.url;\n};\n\nconst handleCommand = async (command) => {\n  if (command === \"create\") {\n    addTeamInfo.visible = true;\n    return;\n  }\n  if (command === undefined) {\n    return;\n  }\n  team.current = team.list[command];\n  window.localStorage.setItem(\"teamId\", team.current!._id);\n  // team.def = await getTeamDefFolder();\n  project.value = {};\n  group.value = {};\n};\n\n// const getTeamDefFolder = async () => {\n//   const res = await getDefFolder({\n//     teamId: team.current!._id,\n//     type: \"default\"\n//   });\n//   return res.data;\n// };\n\nconst getGroupName = () => {\n  if (!group.value?.current) {\n    return \"未选择\";\n  }\n  return group.value.current.fullPath.map(({ name }) => name).join(\"/\");\n};\nconst handleProjectChange = (info) => {\n  project.value = {\n    current: info,\n    visible: false\n  };\n  group.value = {};\n};\nconst handleCloseProject = () => {\n  project.value.visible = false;\n};\nconst handleGroupChange = (info) => {\n  group.value = {\n    current: info,\n    visible: false\n  };\n};\nconst handleCloseGroup = () => {\n  group.value.visible = false;\n};\nconst getTeam = async () => {\n  const res = await getTeamList({});\n  team.list = res.data;\n  if (res.data?.length) {\n    const teamId = window.localStorage.getItem(\"teamId\");\n    let index;\n    if (teamId) {\n      index = res.data.findIndex((item) => item._id === teamId);\n    }\n    handleCommand(index || res.data.findIndex((item) => item.permission === Permission.OWNER || item.permission === Permission.MANAGE));\n  }\n};\n\nfunction categorizeSlices(slices, artboards) {\n  // 创建返回对象\n  let categorizedSlices = {};\n\n  // 遍历每个 slice\n  slices.forEach((slice) => {\n    // 查找对应的 artboard\n    artboards.forEach((artboard) => {\n      artboard.layers.forEach((layer) => {\n        // 检查 slice 是否属于这个 artboard\n        if (layer.objectID === slice.objectID) {\n          // 如果这个 artboard 还没有在返回对象中，初始化它\n          if (!categorizedSlices[artboard.objectID]) {\n            categorizedSlices[artboard.objectID] = [];\n          }\n          // 添加 slice 到对应的 artboard\n          categorizedSlices[artboard.objectID].push(slice);\n        }\n      });\n    });\n  });\n\n  return categorizedSlices;\n}\nconst uploadJson = async (json) => {\n  // progressInfo.value = null;\n  //   console.log(JSON.stringify(jsonString));\n  const categorizedSlices = categorizeSlices(json.slices, json.artboards);\n  progressInfo.value = {\n    process: 0,\n    name: \"上传json\"\n  };\n  const resultList = await Promise.all(\n    json.artboards.map(async (artboard, index) => {\n      artboard.layers.forEach((layer) => {\n        if (layer.exportable) {\n          layer.exportable.forEach((exportable) => {\n            delete exportable.buffer;\n          });\n        }\n      })\n      const res = await addSketch({\n        artboard: JSON.stringify(artboard),\n        name: artboard.name,\n        slices: JSON.stringify(categorizedSlices[artboard.objectID] || []),\n        unit: json.unit,\n        artId: artboard.objectID,\n        pageId: artboard.pageObjectID,\n        thumb: artboard.imagePath,\n        resolution: json.resolution,\n        colorFormat: json.colorFormat,\n        colors: JSON.stringify(json.colors),\n        groupId: group.value.current?._id,\n        projectId: project.value.current?._id,\n        index\n      });\n      return res.data;\n    })\n  );\n  const { add, update } = resultList.reduce(\n    (prev, next) => {\n      prev[next.updated ? \"update\" : \"add\"].push(next.name);\n      return prev;\n    },\n    {\n      add: [],\n      update: []\n    }\n  );\n  progressInfo.value = {\n    process: 100,\n    done: true,\n    add,\n    update,\n    name: \"上传成功\"\n  };\n  ElMessage.success(\"上传成功\");\n  sendStatus({\n    projectId: project.value.current?._id,\n    groupId: group.value.current?._id,\n    sketchs: resultList\n  });\n  setTimeout(() => {\n    // window.postMessage(\"closed\");\n  }, 2000);\n};\n// 点击项目\nconst projectClick = () => {\n  project.value.visible = true;\n};\n// 点击分组\nconst groupClick = () => {\n  if (!project.value.current) {\n    return;\n  }\n  group.value.visible = true;\n};\n// 点击版本\n// const versionClick = () => {\n//   if (!group.value.current) {\n//     return;\n//   }\n//   version.value.visible = true;\n// };\nonMounted(() => {\n  if (!userInfo.ssoId) {\n    // ssoLogin();\n    return;\n  }\n  init();\n});\nfunction getUUid() {\n  let s: any[] = [];\n  let hexDigits = \"0123456789abcdef\";\n  for (let i = 0; i < 32; i++) {\n    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);\n  }\n  s[14] = \"4\"; // bits 12-15 of the time_hi_and_version field to 0010\n  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01\n  s[8] = s[13] = s[18] = s[23];\n  let uuid = s.join(\"\");\n  return uuid;\n}\n\nconst checkStatus = async () => {\n  const res = await userCheckPoling({\n    token: uuid.value\n  });\n  if (res.code !== 0) {\n    ElMessage.error(res.msg);\n    uuid.value = \"\";\n    poller.value?.stop();\n    return;\n  }\n  if (res?.data === 1) {\n    uuid.value = \"\";\n    poller.value?.stop();\n    ElMessage.success(\"登录成功\");\n    refreshUserInfo();\n  }\n};\n// 登陆\nconst ssoLogin = async () => {\n  uuid.value = getUUid();\n  window.postMessage(\"openExternalUrl\", `${window.location.origin}/#/item/project/check?from=gallery-sketch&token=${uuid.value}`);\n  // window.location.href = \"/api/user/login?mode=mobile&return_url=\" + encodeURIComponent(window.location.href);\n  poller.value = createPoller(checkStatus, 2000, 30);\n  poller.value.start();\n};\n\nconst refreshUserInfo = async () => {\n  const res = await getUserInfo({});\n  if (res.status == 200 && res.data.code == 0) {\n    const payload = res.data.data;\n    userInfo.updateInfo({\n      syUserName: payload.syUserName,\n      syUid: payload.syUid,\n      ssoId: payload.ssoId,\n      type: payload.type,\n      syData: payload.syData,\n      url: payload.url,\n      name: payload.name\n    });\n    init();\n  }\n};\nconst openHeartBeat = () => {\n  timer.value = setInterval(() => {\n    window.postMessage(\"getSelectedBoard\", sketchSelectType.value);\n  }, 1000);\n};\n\nconst handleAddClose = () => {\n  addTeamInfo.visible = false;\n  addTeamInfo.name = \"\";\n};\n\nconst handleAddSubmit = async () => {\n  if (!addTeamInfo.name) {\n    return;\n  }\n  const res = await addTeam({\n    name: addTeamInfo.name\n  });\n  if (res.code === 0) {\n    ElMessage.success(\"添加成功\");\n    getTeam();\n    handleAddClose();\n  }\n};\nconst close = () => {\n  window.postMessage(\"closed\");\n};\nconst openExternal = () => {\n  window.postMessage(\"openExternalUrl\", `${window.location.origin}/#/item/project/stage?teamId=${team.current!._id}&projectId=${project.value.current?._id}`);\n};\nconst settingCommand = (command) => {\n  switch (command) {\n    case \"logout\":\n      setUserLogout();\n      break;\n    case \"jumpTo\":\n      window.postMessage(\"openExternalUrl\", `${window.location.origin}/#/item/project/index?teamId=${team.current!._id}`);\n      break;\n    default:\n      break;\n  }\n};\nconst init = () => {\n  getTeam();\n  openHeartBeat();\n};\n</script>\n<style lang=\"less\" scoped>\n.sketch {\n  background: #fff;\n  height: 100vh;\n  width: 100%;\n  box-sizing: border-box;\n  position: relative;\n  user-select: none;\n  &-logo {\n    height: 60px;\n    display: flex;\n    padding: 0 15px;\n    align-items: center;\n    justify-content: space-between;\n    background: #5c54f0;\n    img {\n      height: 18px;\n    }\n    .sketch-dropdown-link {\n      font-family: PingFangSC-Medium;\n      font-size: 16px;\n      color: #ffffff;\n      font-weight: 500;\n      white-space: nowrap;\n      display: flex;\n      max-width: calc(100vw - 165px);\n      justify-content: space-between;\n      margin-left: 10px;\n      border-left: 1px solid #fff;\n      padding-left: 10px;\n      div {\n        width: calc(100% - 20px);\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n    .sketch-bar {\n      flex: 1;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      //\n      .el-dropdown {\n        cursor: pointer;\n      }\n      .sketch__userInfo {\n        display: flex;\n        align-items: center;\n        max-width: 100px;\n      }\n      .sketch__avatar {\n        border-radius: 50%;\n        height: 25px;\n        border: 1px solid #fff;\n      }\n      .sketch__username {\n        color: #fff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        margin-left: 5px;\n        font-size: 12px;\n      }\n    }\n  }\n  &-selected {\n    margin: 20px;\n    height: 200px;\n    padding: 20px;\n    background: #f5f5f5;\n    border-radius: 8px;\n    box-sizing: border-box;\n    font-size: 13px;\n    user-select: none;\n    &-placeholder {\n      width: 100%;\n      height: 100%;\n      position: relative;\n      .el-empty {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        ::v-deep {\n          .el-empty__image {\n            height: 60px;\n          }\n        }\n      }\n    }\n    &-list {\n      height: 100%;\n      width: 100%;\n      overflow: overlay;\n    }\n    &-li {\n      display: flex;\n      align-items: center;\n      margin-bottom: 10px;\n      .el-icon {\n        margin-right: 5px;\n      }\n    }\n  }\n  &-radio {\n    text-align: center;\n  }\n  &-info {\n    margin: 10px;\n    font-size: 13px;\n    height: 44px;\n    display: flex;\n    align-items: center;\n    border: 1px solid #c7c7c7;\n    border-radius: 5px;\n    transition: border 0.3s;\n    cursor: pointer;\n    &:hover {\n      border: 1px solid #5c54f0;\n    }\n    &-label {\n      width: 80px;\n      text-align: center;\n      height: 26px;\n      line-height: 26px;\n      border-right: 1px solid #c7c7c7;\n    }\n    &-content {\n      flex: 1;\n      display: flex;\n      padding: 0 10px;\n      &__text {\n        flex: 1;\n      }\n      .el-icon {\n        font-size: 15px;\n      }\n    }\n  }\n  &-submit {\n    position: absolute;\n    bottom: 25px;\n    left: 20px;\n    right: 20px;\n    .submit-button {\n      height: 45px;\n      width: 100%;\n      margin-bottom: 16px;\n    }\n    .sketch-setting {\n      display: flex;\n      align-items: center;\n      font-family: PingFangSC-Medium;\n      font-size: 14px;\n      color: #262626;\n      letter-spacing: 0;\n      font-weight: 500;\n      .el-icon {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  &-login {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    height: 100vh;\n    box-sizing: border-box;\n    padding-top: 30vh;\n    .el-button {\n      margin-top: 50px;\n      width: 130px;\n    }\n  }\n}\n.sketch-progress {\n  background: rgba(255, 255, 255, 0.8);\n  height: 100vh;\n  box-sizing: border-box;\n  padding-top: 100px;\n  padding-bottom: 40px;\n  display: flex;\n  flex-direction: column;\n  &-content {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  &-done {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    align-items: center;\n    &-icon {\n      font-size: 70px;\n      color: #5c54f0;\n    }\n    &-text {\n      margin-top: 30px;\n      font-family: PingFangSC-Medium;\n      font-size: 24px;\n      color: rgba(0, 0, 0, 0.85);\n      text-align: center;\n      line-height: 32px;\n      font-weight: 500;\n    }\n    &-desc {\n      font-family: PingFangSC-Regular;\n      font-size: 14px;\n      color: rgba(0, 0, 0, 0.45);\n      text-align: center;\n      line-height: 22px;\n      font-weight: 400;\n      margin-top: 8px;\n    }\n  }\n  &-btn {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    .el-button {\n      width: 80%;\n      margin-bottom: 16px;\n      height: 40px;\n    }\n  }\n  .percentage-value {\n    display: block;\n    margin-top: 10px;\n    font-weight: 500;\n    font-size: 28px;\n  }\n  .percentage-label {\n    display: block;\n    margin-top: 10px;\n    font-size: 15px;\n    font-weight: 500;\n  }\n}\n.setting-menu {\n  width: 160px;\n  &-item {\n    display: flex;\n    justify-content: space-between;\n    width: 100%;\n  }\n}\n</style>\n"], "names": ["radioPropsBase", "buildProps", "size", "useSizeProp", "disabled", "Boolean", "label", "type", "String", "Number", "default", "radioProps", "modelValue", "name", "border", "radioEmits", "UPDATE_MODEL_EVENT", "val", "isString", "isNumber", "isBoolean", "CHANGE_EVENT", "radioGroupKey", "Symbol", "useRadio", "props", "emit", "radioRef", "ref", "radioGroup", "inject", "isGroup", "computed", "get", "value", "set", "changeEvent", "checked", "useFormSize", "useFormDisabled", "focus", "tabIndex", "_hoisted_1", "__default__", "defineComponent", "Radio", "emits", "setup", "__props", "ns", "useNamespace", "handleChange", "nextTick", "_ctx", "_cache", "_a", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "b", "is", "m", "createElementVNode", "e", "withDirectives", "ref_key", "$event", "isRef", "onFocus", "onBlur", "onChange", "onClick", "withModifiers", "vModelRadio", "onKeydown", "renderSlot", "$slots", "createTextVNode", "toDisplayString", "radioButtonProps", "RadioButton", "activeStyle", "backgroundColor", "fill", "borderColor", "boxShadow", "color", "textColor", "bm", "be", "style", "normalizeStyle", "radioGroupProps", "id", "validateEvent", "radioGroupEmits", "RadioGroup", "radioId", "useId", "radioGroupRef", "formItem", "useFormItem", "inputId", "groupId", "isLabeledByFormItem", "useFormItemInputId", "formItemContext", "onMounted", "radios", "querySelectorAll", "firstLabel", "Array", "from", "some", "radio", "provide", "reactive", "toRefs", "watch", "validate", "catch", "err", "debugWarn", "role", "labelId", "ElRadio", "withInstall", "ElRadioGroup", "withNoopInstall", "progressProps", "values", "percentage", "validator", "status", "indeterminate", "duration", "strokeWidth", "strokeLinecap", "definePropType", "textInside", "width", "showText", "Function", "striped", "stripedFlow", "format", "_hoisted_2", "viewBox", "_hoisted_3", "_hoisted_4", "_hoisted_5", "key", "ElProgress", "STATUS_COLOR_MAP", "success", "exception", "warning", "barStyle", "animationDuration", "getCurrentColor", "relativeStrokeWidth", "toFixed", "radius", "includes", "parseInt", "parseFloat", "trackPath", "r", "isDashboard", "perimeter", "Math", "PI", "rate", "strokeDashoffset", "trailPathStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circlePathStyle", "transition", "stroke", "ret", "statusIcon", "WarningFilled", "CircleCheck", "CircleClose", "Check", "Close", "progressTextSize", "content", "isFunction", "colors", "span", "length", "map", "seriesColor", "index", "sort", "a", "getColors", "color2", "height", "bem", "createCommentVNode", "d", "cssVarName", "opacity", "fontSize", "createBlock", "ElIcon", "withCtx", "resolveDynamicComponent", "_", "__emit", "loading", "filterText", "list", "autoCheck", "visible", "addSmbInfo", "handleAddClose", "folderId", "handleAddSubmit", "async", "res", "addProject", "teamId", "code", "data", "openAddLog", "_id", "getProjects", "getTeamTreeList", "handleClick", "node", "treeRef", "defaultProps", "children", "filter", "projectId", "projectChage", "getGroups", "nodeClick", "find", "item", "console", "log", "parent", "addGroup", "parentId", "getGroupList", "filterNode", "fullPath", "join", "sketchSelectType", "userInfo", "userInfoStore", "customColors", "team", "current", "addTeamInfo", "project", "group", "uuid", "artBoardList", "timer", "poller", "progressInfo", "window", "postMessage", "submit", "process", "ElMessage", "error", "sketchError", "sketchResult", "jsonString", "uploadJson", "uploadInfo", "info", "uploadSlice", "export", "uploadArtboard", "sketchProcess", "sketchArtBoard", "exportInfo", "exportable", "i", "path", "uploadBuffer", "push", "options", "formData", "FormData", "arraybuffer", "Int8Array", "buffer", "blob", "Blob", "file", "File", "append", "upload", "url", "handleCommand", "command", "localStorage", "setItem", "handleProjectChange", "handleCloseProject", "handleGroupChange", "handleCloseGroup", "getTeam", "getTeamList", "getItem", "findIndex", "permission", "Permission", "OWNER", "MANAGE", "json", "categorizedSlices", "slices", "artboards", "for<PERSON>ach", "slice", "artboard", "layers", "layer", "objectID", "categorizeSlices", "resultList", "Promise", "all", "addSketch", "JSON", "stringify", "unit", "artId", "pageId", "pageObjectID", "thumb", "imagePath", "resolution", "colorFormat", "add", "update", "reduce", "prev", "next", "updated", "done", "sendStatus", "sketchs", "setTimeout", "projectClick", "groupClick", "ssoId", "checkStatus", "userCheckPoling", "token", "msg", "stop", "sso<PERSON><PERSON>in", "s", "hexDigits", "substr", "floor", "random", "getUUid", "location", "origin", "<PERSON><PERSON><PERSON><PERSON>", "start", "refreshUserInfo", "getUserInfo", "payload", "updateInfo", "syUserName", "syUid", "syData", "addTeam", "close", "openExternal", "settingCommand", "init", "setInterval"], "mappings": "0vCASA,MAAMA,GAAiBC,EAAW,CAChCC,KAAMC,EACNC,SAAUC,QACVC,MAAO,CACLC,KAAM,CAACC,OAAQC,OAAQJ,SACvBK,QAAS,MAGPC,GAAaV,EAAW,IACzBD,GACHY,WAAY,CACVL,KAAM,CAACC,OAAQC,OAAQJ,SACvBK,QAAS,IAEXG,KAAM,CACJN,KAAMC,OACNE,QAAS,IAEXI,OAAQT,UAEJU,GAAa,CACjBC,CAACA,IAAsBC,GAAQC,EAASD,IAAQE,EAASF,IAAQG,EAAUH,GAC3EI,CAACA,IAAgBJ,GAAQC,EAASD,IAAQE,EAASF,IAAQG,EAAUH,IC/BjEK,GAAgBC,OAAO,iBCOvBC,GAAW,CAACC,EAAOC,KACvB,MAAMC,EAAWC,IACXC,EAAaC,EAAOR,QAAe,GACnCS,EAAUC,GAAS,MAAQH,IAC3BjB,EAAaoB,EAAS,CAC1BC,IAAM,IACGF,EAAQG,MAAQL,EAAWjB,WAAaa,EAAMb,WAEvD,GAAAuB,CAAIlB,GACEc,EAAQG,MACVL,EAAWO,YAAYnB,GAEfS,GAAAA,EAAKV,GAAoBC,GAEnCU,EAASO,MAAMG,QAAUZ,EAAMb,aAAea,EAAMnB,KACrD,IAEGJ,EAAOoC,EAAYN,GAAS,IAAoB,MAAdH,OAAqB,EAASA,EAAW3B,QAC3EE,EAAWmC,EAAgBP,GAAS,IAAoB,MAAdH,OAAqB,EAASA,EAAWzB,YACnFoC,EAAQZ,GAAI,GACZa,EAAWT,GAAS,IACjB5B,EAAS8B,OAASH,EAAQG,OAAStB,EAAWsB,QAAUT,EAAMnB,OAAa,EAAA,IAE7E,MAAA,CACLqB,WACAI,UACAF,WAAAA,EACAW,QACAtC,OACAE,WACAqC,WACA7B,aACJ,EChCM8B,GAAa,CAAC,QAAS,OAAQ,YAC/BC,GAAcC,EAAgB,CAClC/B,KAAM,YAkER,IAAIgC,KAhE8CD,EAAA,IAC7CD,GACHlB,MAAOd,GACPmC,MAAO/B,GACP,KAAAgC,CAAMC,GAAStB,KAAEA,IACf,MAAMD,EAAQuB,EACRC,EAAKC,EAAa,UAClBvB,SAAEA,EAAUE,WAAAA,EAAYW,MAAAA,EAAAtC,KAAOA,EAAME,SAAAA,EAAAQ,WAAUA,GAAeY,GAASC,EAAOC,GACpF,SAASyB,IACPC,IAAS,IAAM1B,EAAK,SAAUd,EAAWsB,QAC1C,CACM,MAAA,CAACmB,EAAMC,KACR,IAAAC,EACG,OAAAC,IAAaC,EAAmB,QAAS,CAC9CC,MAAOC,EAAe,CACpBC,EAAMX,GAAIY,IACVD,EAAMX,GAAIa,GAAG,WAAYF,EAAMxD,IAC/BwD,EAAMX,GAAIa,GAAG,QAASF,EAAMpB,IAC5BoB,EAAMX,GAAIa,GAAG,WAAYT,EAAKvC,QAC9B8C,EAAMX,GAAIa,GAAG,UAAWF,EAAMhD,KAAgByC,EAAK/C,OACnDsD,EAAMX,GAAIc,EAAEH,EAAM1D,OAEnB,CACD8D,EAAmB,OAAQ,CACzBN,MAAOC,EAAe,CACpBC,EAAMX,GAAIgB,EAAE,SACZL,EAAMX,GAAIa,GAAG,WAAYF,EAAMxD,IAC/BwD,EAAMX,GAAIa,GAAG,UAAWF,EAAMhD,KAAgByC,EAAK/C,UAEpD,CACD4D,EAAeF,EAAmB,QAAS,CACzCG,QAAS,WACTvC,IAAKD,EACL,sBAAuB2B,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAMzD,GAAcA,EAAWsB,MAAQkC,EAAS,MAC7GV,MAAOC,EAAeC,EAAMX,GAAIgB,EAAE,aAClC/B,MAAOmB,EAAK/C,MACZO,KAAMwC,EAAKxC,OAAqC,OAA3B0C,EAAKK,EAAM/B,SAAuB,EAAS0B,EAAG1C,MACnET,SAAUwD,EAAMxD,GAChBG,KAAM,QACN+D,QAAShB,EAAO,KAAOA,EAAO,GAAMc,GAAW5B,EAAMN,OAAQ,GAC7DqC,OAAQjB,EAAO,KAAOA,EAAO,GAAMc,GAAW5B,EAAMN,OAAQ,GAC5DsC,SAAUrB,EACVsB,QAASnB,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAIhC,IAAa,CACxB,CAACiC,EAAaf,EAAMhD,MAEtBoD,EAAmB,OAAQ,CACzBN,MAAOC,EAAeC,EAAMX,GAAIgB,EAAE,WACjC,KAAM,IACR,GACHD,EAAmB,OAAQ,CACzBN,MAAOC,EAAeC,EAAMX,GAAIgB,EAAE,UAClCW,UAAWtB,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAChD,CAAC,WACH,CACDG,EAAWxB,EAAKyB,OAAQ,UAAW,CAAE,GAAE,IAAM,CAC3CC,GAAgBC,GAAgB3B,EAAK/C,OAAQ,OAE9C,KACF,EAAC,CAEP,IAEgD,CAAC,CAAC,SAAU,eCvE/D,MAAM2E,GAAmBhF,EAAW,IAC/BD,GACHa,KAAM,CACJN,KAAMC,OACNE,QAAS,MCDPgC,GAAa,CAAC,QAAS,OAAQ,YAC/BC,GAAcC,EAAgB,CAClC/B,KAAM,kBA0DR,IAAIqE,KAxD8CtC,EAAA,IAC7CD,GACHlB,MAAOwD,GACP,KAAAlC,CAAMC,GACJ,MAAMvB,EAAQuB,EACRC,EAAKC,EAAa,UAClBvB,SAAEA,EAAUa,MAAAA,EAAAtC,KAAOA,EAAME,SAAAA,EAAAQ,WAAUA,EAAYiB,WAAAA,GAAeL,GAASC,GACvE0D,EAAcnD,GAAS,KACpB,CACLoD,iBAAgC,MAAdvD,OAAqB,EAASA,EAAWwD,OAAS,GACpEC,aAA4B,MAAdzD,OAAqB,EAASA,EAAWwD,OAAS,GAChEE,WAA0B,MAAd1D,OAAqB,EAASA,EAAWwD,MAAQ,cAAcxD,EAAWwD,OAAS,GAC/FG,OAAsB,MAAd3D,OAAqB,EAASA,EAAW4D,YAAc,OAG5D,MAAA,CAACpC,EAAMC,KACR,IAAAC,EACG,OAAAC,IAAaC,EAAmB,QAAS,CAC9CC,MAAOC,EAAe,CACpBC,EAAMX,GAAIY,EAAE,UACZD,EAAMX,GAAIa,GAAG,SAAUF,EAAMhD,KAAgByC,EAAK/C,OAClDsD,EAAMX,GAAIa,GAAG,WAAYF,EAAMxD,IAC/BwD,EAAMX,GAAIa,GAAG,QAASF,EAAMpB,IAC5BoB,EAAMX,GAAIyC,GAAG,SAAU9B,EAAM1D,OAE9B,CACDgE,EAAeF,EAAmB,QAAS,CACzCG,QAAS,WACTvC,IAAKD,EACL,sBAAuB2B,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAMzD,GAAcA,EAAWsB,MAAQkC,EAAS,MAC7GV,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,SAAU,mBAC7CzD,MAAOmB,EAAK/C,MACZC,KAAM,QACNM,KAAMwC,EAAKxC,OAAqC,OAA3B0C,EAAKK,EAAM/B,SAAuB,EAAS0B,EAAG1C,MACnET,SAAUwD,EAAMxD,GAChBkE,QAAShB,EAAO,KAAOA,EAAO,GAAMc,GAAW5B,EAAMN,OAAQ,GAC7DqC,OAAQjB,EAAO,KAAOA,EAAO,GAAMc,GAAW5B,EAAMN,OAAQ,GAC5DuC,QAASnB,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAIhC,IAAa,CACxB,CAACiC,EAAaf,EAAMhD,MAEtBoD,EAAmB,OAAQ,CACzBN,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,SAAU,UAC7CC,MAAOC,GAAejC,EAAMhD,KAAgByC,EAAK/C,MAAQsD,EAAMuB,GAAe,IAC9EP,UAAWtB,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAChD,CAAC,WACH,CACDG,EAAWxB,EAAKyB,OAAQ,UAAW,CAAE,GAAE,IAAM,CAC3CC,GAAgBC,GAAgB3B,EAAK/C,OAAQ,OAE9C,KACF,EAAC,CAEP,IAEsD,CAAC,CAAC,SAAU,sBC7DrE,MAAMwF,GAAkB7F,EAAW,CACjC8F,GAAI,CACFxF,KAAMC,OACNE,aAAS,GAEXR,KAAMC,EACNC,SAAUC,QACVO,WAAY,CACVL,KAAM,CAACC,OAAQC,OAAQJ,SACvBK,QAAS,IAEX2E,KAAM,CACJ9E,KAAMC,OACNE,QAAS,IAEXJ,MAAO,CACLC,KAAMC,OACNE,aAAS,GAEX+E,UAAW,CACTlF,KAAMC,OACNE,QAAS,IAEXG,KAAM,CACJN,KAAMC,OACNE,aAAS,GAEXsF,cAAe,CACbzF,KAAMF,QACNK,SAAS,KAGPuF,GAAkBlF,GCxBlB2B,GAAa,CAAC,KAAM,aAAc,mBAClCC,GAAcC,EAAgB,CAClC/B,KAAM,iBAsDR,IAAIqF,KApD8CtD,EAAA,IAC7CD,GACHlB,MAAOqE,GACPhD,MAAOmD,GACP,KAAAlD,CAAMC,GAAStB,KAAEA,IACf,MAAMD,EAAQuB,EACRC,EAAKC,EAAa,SAClBiD,EAAUC,IACVC,EAAgBzE,KAChB0E,SAAEA,GAAaC,KACbC,QAASC,EAAAC,oBAASA,GAAwBC,EAAmBlF,EAAO,CAC1EmF,gBAAiBN,IAMnBO,IAAU,KACR,MAAMC,EAAST,EAAcnE,MAAM6E,iBAAiB,gBAC9CC,EAAaF,EAAO,IACrBG,MAAMC,KAAKJ,GAAQK,MAAMC,GAAUA,EAAM/E,WAAY2E,IACxDA,EAAWvE,SAAW,EACvB,IAEG,MAAA5B,EAAOmB,GAAS,IACbP,EAAMZ,MAAQsF,EAAQjE,QAYxB,OAVPmF,GAAQ/F,GAAegG,GAAS,IAC3BC,GAAO9F,GACVW,YAhBmBF,IACnBR,EAAKV,GAAoBkB,GACzBkB,IAAS,IAAM1B,EAAK,SAAUQ,IAAM,EAepCrB,UAEI2G,IAAA,IAAM/F,EAAMb,aAAY,KACxBa,EAAMuE,gBACI,MAAAM,GAAgBA,EAASmB,SAAS,UAAUC,OAAOC,GAAQC,MACxE,IAEI,CAACvE,EAAMC,KACLE,IAAaC,EAAmB,MAAO,CAC5CsC,GAAInC,EAAM6C,GACVtC,QAAS,gBACTvC,IAAKyE,EACL3C,MAAOC,EAAeC,EAAMX,GAAIY,EAAE,UAClCgE,KAAM,aACN,aAAejE,EAAM8C,QAAqD,EAA9BrD,EAAK/C,OAAS,cAC1D,kBAAmBsD,EAAM8C,GAAuB9C,EAAM0C,GAAUwB,aAAU,GACzE,CACDjD,EAAWxB,EAAKyB,OAAQ,YACvB,GAAIpC,IAEV,IAEqD,CAAC,CAAC,SAAU,qBC5DpE,MAAMqF,GAAUC,EAAYnF,GAAO,CACjCqC,eACAgB,gBAEI+B,GAAeC,EAAgBhC,IACfgC,EAAgBhD,ICZtC,MAAMiD,GAAgBlI,EAAW,CAC/BM,KAAM,CACJA,KAAMC,OACNE,QAAS,OACT0H,OAAQ,CAAC,OAAQ,SAAU,cAE7BC,WAAY,CACV9H,KAAME,OACNC,QAAS,EACT4H,UAAYrH,GAAQA,GAAO,GAAKA,GAAO,KAEzCsH,OAAQ,CACNhI,KAAMC,OACNE,QAAS,GACT0H,OAAQ,CAAC,GAAI,UAAW,YAAa,YAEvCI,cAAe,CACbjI,KAAMF,QACNK,SAAS,GAEX+H,SAAU,CACRlI,KAAME,OACNC,QAAS,GAEXgI,YAAa,CACXnI,KAAME,OACNC,QAAS,GAEXiI,cAAe,CACbpI,KAAMqI,EAAepI,QACrBE,QAAS,SAEXmI,WAAY,CACVtI,KAAMF,QACNK,SAAS,GAEXoI,MAAO,CACLvI,KAAME,OACNC,QAAS,KAEXqI,SAAU,CACRxI,KAAMF,QACNK,SAAS,GAEX8E,MAAO,CACLjF,KAAMqI,EAAe,CACnBpI,OACAyG,MACA+B,WAEFtI,QAAS,IAEXuI,QAAS5I,QACT6I,YAAa7I,QACb8I,OAAQ,CACN5I,KAAMqI,EAAeI,UACrBtI,QAAU2H,GAAe,GAAGA,QCjD1B3F,GAAa,CAAC,iBACd0G,GAAa,CAAEC,QAAS,eACxBC,GAAa,CAAC,IAAK,SAAU,iBAAkB,gBAC/CC,GAAa,CAAC,IAAK,SAAU,UAAW,iBAAkB,gBAC1DC,GAAa,CAAEC,IAAK,GACpB9G,GAAcC,EAAgB,CAClC/B,KAAM,eCXR,MAAM6I,GAAa1B,IDa+BpF,EAAA,IAC7CD,GACHlB,MAAO0G,GACP,KAAApF,CAAMC,GACJ,MAAMvB,EAAQuB,EACR2G,EAAmB,CACvBC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTpJ,QAAS,WAELuC,EAAKC,EAAa,YAClB6G,EAAW/H,GAAS,KAAO,CAC/B8G,MAAO,GAAGrH,EAAM4G,cAChB2B,kBAAmB,GAAGvI,EAAMgH,YAC5BrD,gBAAiB6E,EAAgBxI,EAAM4G,gBAEnC6B,EAAsBlI,GAAS,KAAOP,EAAMiH,YAAcjH,EAAMqH,MAAQ,KAAKqB,QAAQ,KACrFC,EAASpI,GAAS,IAClB,CAAC,SAAU,aAAaqI,SAAS5I,EAAMlB,MAClCE,OAAO6J,SAAS,IAAG,GAAK7J,OAAO8J,WAAWL,EAAoBhI,OAAS,GAAK,IAE9E,IAEHsI,EAAYxI,GAAS,KACzB,MAAMyI,EAAIL,EAAOlI,MACXwI,EAA6B,cAAfjJ,EAAMlB,KACnB,MAAA,sCAEGmK,EAAc,GAAK,MAAMD,kBAC3BA,KAAKA,aAAaC,EAAc,IAAM,KAAS,EAAJD,kBAC3CA,KAAKA,aAAaC,EAAc,GAAK,MAAU,EAAJD,eAAK,IAGpDE,EAAY3I,GAAS,IAAM,EAAI4I,KAAKC,GAAKT,EAAOlI,QAChD4I,EAAO9I,GAAS,IAAqB,cAAfP,EAAMlB,KAAuB,IAAO,IAC1DwK,EAAmB/I,GAAS,IAEzB,IADa,EAAA2I,EAAUzI,OAAS,EAAI4I,EAAK5I,OAAS,QAGrD8I,EAAiBhJ,GAAS,KAAO,CACrCiJ,gBAAiB,GAAGN,EAAUzI,MAAQ4I,EAAK5I,YAAYyI,EAAUzI,UACjE6I,iBAAkBA,EAAiB7I,UAE/BgJ,EAAkBlJ,GAAS,KAAO,CACtCiJ,gBAAiB,GAAGN,EAAUzI,MAAQ4I,EAAK5I,OAAST,EAAM4G,WAAa,WAAWsC,EAAUzI,UAC5F6I,iBAAkBA,EAAiB7I,MACnCiJ,WAAY,yEAERC,EAASpJ,GAAS,KAClB,IAAAqJ,EAMG,OAJCA,EADJ5J,EAAM+D,MACFyE,EAAgBxI,EAAM4G,YAEtBsB,EAAiBlI,EAAM8G,SAAWoB,EAAiBjJ,QAEpD2K,CAAA,IAEHC,EAAatJ,GAAS,IACL,YAAjBP,EAAM8G,OACDgD,EAEU,SAAf9J,EAAMlB,KACgB,YAAjBkB,EAAM8G,OAAuBiD,EAAcC,EAE1B,YAAjBhK,EAAM8G,OAAuBmD,EAAQC,IAG1CC,EAAmB5J,GAAS,IACV,SAAfP,EAAMlB,KAAkB,GAAyB,GAApBkB,EAAMiH,YAAkC,QAAdjH,EAAMqH,MAAmB,IAEnF+C,EAAU7J,GAAS,IAAMP,EAAM0H,OAAO1H,EAAM4G,cAc5C,MAAA4B,EAAmB5B,IACnB,IAAA9E,EACE,MAAAiC,MAAEA,GAAU/D,EACd,GAAAqK,GAAWtG,GACb,OAAOA,EAAM6C,GACrB,GAAiBnH,EAASsE,GACX,OAAAA,EACF,CACC,MAAAuG,EArBV,SAAmBvG,GACX,MAAAwG,EAAO,IAAMxG,EAAMyG,OAUlB,OATczG,EAAM0G,KAAI,CAACC,EAAaC,IACvClL,EAASiL,GACJ,CACL3G,MAAO2G,EACP9D,YAAa+D,EAAQ,GAAKJ,GAGvBG,IAEWE,MAAK,CAACC,EAAGzI,IAAMyI,EAAEjE,WAAaxE,EAAEwE,YACrD,CASkBkE,CAAU/G,GACzB,IAAA,MAAWgH,KAAUT,EACnB,GAAIS,EAAOnE,WAAaA,EACtB,OAAOmE,EAAOhH,MAEV,OAAmC,OAAnCjC,EAAKwI,EAAOA,EAAOE,OAAS,SAAc,EAAS1I,EAAGiC,KAC/D,GAEI,MAAA,CAACnC,EAAMC,KACLE,IAAaC,EAAmB,MAAO,CAC5CC,MAAOC,EAAe,CACpBC,EAAMX,GAAIY,IACVD,EAAMX,GAAIc,EAAEV,EAAK9C,MACjBqD,EAAMX,GAAIa,GAAGT,EAAKkF,QAClB,CACE,CAAC3E,EAAMX,GAAIc,EAAE,kBAAmBV,EAAK0F,SACrC,CAACnF,EAAMX,GAAIc,EAAE,gBAAiBV,EAAKwF,cAGvChB,KAAM,cACN,gBAAiBxE,EAAKgF,WACtB,gBAAiB,IACjB,gBAAiB,OAChB,CACa,SAAdhF,EAAK9C,MAAmBiD,IAAaC,EAAmB,MAAO,CAC7DgG,IAAK,EACL/F,MAAOC,EAAeC,EAAMX,GAAIY,EAAE,SACjC,CACDG,EAAmB,MAAO,CACxBN,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,MAAO,UAC1CC,MAAOC,GAAe,CAAE4G,OAAQ,GAAGpJ,EAAKqF,mBACvC,CACD1E,EAAmB,MAAO,CACxBN,MAAOC,EAAe,CACpBC,EAAMX,GAAI0C,GAAG,MAAO,SACpB,CAAE,CAAC/B,EAAMX,GAAIyJ,IAAI,MAAO,QAAS,kBAAmBrJ,EAAKmF,eACzD,CAAE,CAAC5E,EAAMX,GAAIyJ,IAAI,MAAO,QAAS,YAAarJ,EAAK4F,SACnD,CAAE,CAACrF,EAAMX,GAAIyJ,IAAI,MAAO,QAAS,iBAAkBrJ,EAAK6F,eAE1DtD,MAAOC,GAAejC,EAAMmG,KAC3B,EACA1G,EAAK0F,UAAY1F,EAAKyB,OAAOpE,UAAY2C,EAAKwF,YAAcrF,IAAaC,EAAmB,MAAO,CAClGgG,IAAK,EACL/F,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,MAAO,eACzC,CACDd,EAAWxB,EAAKyB,OAAQ,UAAW,CAAEuD,WAAYhF,EAAKgF,aAAc,IAAM,CACxErE,EAAmB,OAAQ,KAAMgB,GAAgBpB,EAAMiI,IAAW,OAEnE,IAAMc,GAAmB,QAAQ,IACnC,IACF,IACF,KAAOnJ,IAAaC,EAAmB,MAAO,CAC/CgG,IAAK,EACL/F,MAAOC,EAAeC,EAAMX,GAAIY,EAAE,WAClC+B,MAAOC,GAAe,CAAE4G,OAAQ,GAAGpJ,EAAKyF,UAAWA,MAAO,GAAGzF,EAAKyF,aACjE,EACAtF,IAAaC,EAAmB,MAAO2F,GAAY,CAClDpF,EAAmB,OAAQ,CACzBN,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,SAAU,UAC7CiH,EAAGhJ,EAAM4G,GACTY,OAAQ,OAAOxH,EAAMX,GAAI4J,WAAW,gCACpC,iBAAkBxJ,EAAKsF,cACvB,eAAgB/E,EAAMsG,GACtB7E,KAAM,OACNO,MAAOC,GAAejC,EAAMoH,KAC3B,KAAM,GAAI1B,IACbtF,EAAmB,OAAQ,CACzBN,MAAOC,EAAeC,EAAMX,GAAI0C,GAAG,SAAU,SAC7CiH,EAAGhJ,EAAM4G,GACTY,OAAQxH,EAAMwH,GACd/F,KAAM,OACNyH,QAASzJ,EAAKgF,WAAa,EAAI,EAC/B,iBAAkBhF,EAAKsF,cACvB,eAAgB/E,EAAMsG,GACtBtE,MAAOC,GAAejC,EAAMsH,KAC3B,KAAM,GAAI3B,QAEd,KACFlG,EAAK0F,WAAY1F,EAAKyB,OAAOpE,SAAa2C,EAAKwF,WAavC8D,GAAmB,QAAQ,IAb0BnJ,IAAaC,EAAmB,MAAO,CACnGgG,IAAK,EACL/F,MAAOC,EAAeC,EAAMX,GAAIgB,EAAE,SAClC2B,MAAOC,GAAe,CAAEkH,SAAU,GAAGnJ,EAAMgI,UAC1C,CACD/G,EAAWxB,EAAKyB,OAAQ,UAAW,CAAEuD,WAAYhF,EAAKgF,aAAc,IAAM,CACvEhF,EAAKkF,QAAsG/E,IAAawJ,GAAYpJ,EAAMqJ,GAAS,CAAExD,IAAK,GAAK,CAC9J/I,QAASwM,IAAQ,IAAM,EACpB1J,IAAawJ,GAAYG,GAAwBvJ,EAAM0H,SAE1D8B,EAAG,MAJW5J,IAAaC,EAAmB,OAAQ+F,GAAYxE,GAAgBpB,EAAMiI,IAAW,QAOtG,KACF,GAAInJ,IAEV,IAEmD,CAAC,CAAC,SAAU,swBE9IlE,MAAAjB,EAAAuB,EAMAtB,EAAA2L,EACAC,EAAA1L,GAAA,GACA2L,EAAA3L,EAAA,IACA4L,EAAA5L,EAAA,IACA6L,EAAA7L,GAAA,GAEA4F,IAAA,IAAA/F,EAAAiM,UACcA,IAEVA,MAGEH,EAAArL,MAAA,EAAmB,IAKzB,MAAAyL,EAAArG,GAAA,CAAuCoG,SAAA,EAC5B7M,KAAA,KAGX+M,EAAA,KACED,EAAAD,SAAA,EACAC,EAAAE,SAAA,KACAF,EAAA9M,KAAA,EAAA,EAEFiN,EAAAC,UACE,IAAAJ,EAAA9M,KACE,OAEF,MAAAmN,QAAAC,GAAA,CAA6BC,OAAAzM,EAAAyM,OACbrN,KAAA8M,EAAA9M,KACGgN,SAAAF,EAAAE,UAAA,OAGnB,GAAA,IAAAG,EAAAG,KAAA,CAEE,OAAAV,EAAAvL,MAEE,YADAR,EAAA,SAAAsM,EAAAI,SAGU,GAGhBC,EAAAD,IACET,EAAAD,SAAA,EACAC,EAAAE,SAAAO,EAAAE,GAAA,EAGFC,EAAAR,UACET,EAAApL,OAAA,EACA,MAAA8L,QAAAQ,GAAA,CAAkCN,OAAAzM,EAAAyM,SAGlCV,EAAAtL,MAAA8L,EAAAI,KACAd,EAAApL,OAAA,CAAA,EAGFuM,EAAAC,IACE,YAAAA,EAAAnO,MAGAmB,EAAA,SAAAgN,EAAA,2mECnFF,MAAAC,EAAA/M,IACAH,EAAAuB,EAMA4L,EAAA,CAAqBtO,MAAA,OACZuO,SAAA,YAGTnN,EAAA2L,EACAC,EAAA1L,GAAA,GACA2L,EAAA3L,EAAA,IACA4L,EAAA5L,EAAA,IACA4F,GAAA+F,GAAAtM,IACE0N,EAAAzM,MAAA4M,OAAA7N,EAAA,IAEFuG,IAAA,IAAA/F,EAAAiM,UACcA,IAEVA,MAGEH,EAAArL,MAAA,EAAmB,IAKzBsF,IAAA,IAAA/F,EAAAsN,YACcA,IAEVA,MACe,IAKnB,MAAAC,EAAAjB,gBACEkB,IACAzB,EAAAtL,MAAA+J,QACEiD,EAAA1B,EAAAtL,MAAAiN,MAAAC,GAAA,QAAAA,EAAAvO,QAAA2M,EAAAtL,MAAA,IAEFmN,QAAAC,IAAA9B,EAAAtL,MAAA,EAEFyL,EAAArG,GAAA,CAA6EoG,SAAA,EAClE7M,KAAA,GACH0O,OAAA,OAMR3B,EAAA,KACED,EAAAD,SAAA,EACAC,EAAA4B,OAAA,KACA5B,EAAA9M,KAAA,EAAA,EAEFiN,EAAAC,UACE,IAAAJ,EAAA9M,KACE,OAOF,WALA2O,GAAA,CAA2BT,UAAAtN,EAAAsN,UACRU,SAAA9B,EAAA4B,QAAAjB,IACYzN,KAAA8M,EAAA9M,QAG/BsN,eAEiB,EAGnBE,EAAAkB,IACE5B,EAAAD,SAAA,EACAC,EAAA4B,OAAAA,CAAA,EAGFN,EAAAlB,UACET,EAAApL,OAAA,EACA,MAAA8L,QAAA0B,GAAA,CAA+BX,UAAAtN,EAAAsN,YAG/BvB,EAAAtL,MAAA8L,EAAAI,KACAd,EAAApL,OAAA,CAAA,EAEFgN,EAAAR,IACEhN,EAAA,SAAAgN,EAAA,EAEFiB,EAAA,CAAAzN,EAAAkM,KACElM,GACAkM,EAAA9N,MAAA+J,SAAAnI,0iDAvCAyL,EAAA4B,QAAAK,SAAA1D,KAAA,EAAArL,UAAAA,IAAAgP,KAAA,2sDC6DF,MAAAC,EAAAlO,EAAA,KACAmO,EAAAC,KACAC,EAAA,CAAqB,CAAAzK,MAAA,UAAA6C,WAAA,IACgB,CAAA7C,MAAA,UAAA6C,WAAA,IACA,CAAA7C,MAAA,UAAA6C,WAAA,IACA,CAAA7C,MAAA,UAAA6C,WAAA,IACA,CAAA7C,MAAA,UAAA6C,WAAA,MAIrC6H,EAAA5I,GAAA,CAAgCkG,KAAA,GACvB2C,QAAA,OAGTC,EAAA9I,GAAA,CAAwCoG,SAAA,EAC7B7M,KAAA,KAGXwP,EAAAzO,EAAA,CAAA,GACA0O,EAAA1O,EAAA,CAAA,GACA2O,EAAA3O,EAAA,IAEA4O,EAAA5O,EAAA,IACA6O,EAAA7O,EAAA,MACA8O,EAAA9O,EAAA,MAEA+O,EAAA/O,EAAA,MACA4F,IAAA,IAAAsI,EAAA5N,QACyB,KAErB0O,OAAAC,YAAA,mBAAAf,EAAA5N,MAAA,IAIJ,MAAA4O,EAAA,KACER,EAAApO,OAAAiO,SAIAQ,EAAAzO,MAAA,CAAqB6O,QAAA,GAGrBH,OAAAC,YAAA,SAAAf,EAAA5N,QANE8O,EAAAC,MAAA,QAMF,EAEFL,OAAAM,YAAAjN,IACEoL,QAAAC,IAAA,cAAArL,EAAA,EAEF2M,OAAAO,aAAAC,IACE,IACEC,EAAAD,EAAqB,OAAAH,GAErB5B,QAAAC,IAAA2B,EAAiB,GAIrBL,OAAAU,WAAAC,IAQE,UAAAA,EAAAhR,KACEiR,EAAAD,EAAAE,OAAAF,EAAAnF,OAEAsF,EAAAH,EAAAE,OAAAF,EAAAnF,MAAsC,EAI1CwE,OAAAe,cAAAZ,IACEJ,EAAAzO,MAAA6O,CAAA,EAGFH,OAAAgB,eAAApE,IACEgD,EAAAtO,MAAAsL,CAAA,EAMF,MAAAgE,EAAAzD,MAAA8D,EAAAzF,KACE,MAAAmF,EAAA,CAAwBO,WAAA,GACT1F,QACb7L,KAAA,SAGF,IAAA,IAAAwR,EAAA,EAAAA,EAAAF,EAAA5F,OAAA8F,IAAA,CACE,MAAA3C,EAAAyC,EAAAE,GACAC,QAAAC,EAAA7C,GACAmC,EAAAO,WAAAI,KAAA,CAAqBF,OACnB7I,OAAAiG,EAAAjG,OACatI,KAAAuO,EAAA4C,MAEd,CAEHpB,OAAAC,YAAA,eAAAU,EAAA,EAGFG,EAAA3D,MAAA8D,EAAAzF,KACE,MAAAmF,EAAA,CAAwBO,WAAA,CACV3I,OAAA0I,EAAA1I,OACStI,KAAAgR,EAAAhR,MAErBuL,QACA7L,KAAA,YAGFgR,EAAAO,WAAAE,WAAAC,EAAAJ,GACAjB,OAAAC,YAAA,eAAAU,EAAA,EAEFU,EAAAlE,MAAAoE,IACE,MAAAC,EAAA,IAAAC,SACAC,EAAA,IAAAC,UAAAJ,EAAAK,OAAApE,MACAqE,EAAA,IAAAC,KAAA,CAAAJ,GAAA,CAAA/R,KAAA4R,EAAAhJ,SACAwJ,EAAA,IAAAC,KAAA,CAAAH,GAAAN,EAAAtR,KAAA,IAAAsR,EAAAhJ,OAAA,CAAmE5I,KAAA4R,EAAAhJ,SAGnEiJ,EAAAS,OAAA,OAAAF,GACA,MAAAvE,KAAAA,SAAA0E,GAAAV,GACA,OAAAhE,EAAAA,KAAA2E,GAAA,EAGFC,EAAAjF,MAAAkF,IACE,WAAAA,OAIA,IAAAA,IAGA/C,EAAAC,QAAAD,EAAA1C,KAAAyF,GACArC,OAAAsC,aAAAC,QAAA,SAAAjD,EAAAC,QAAA7B,KAEA+B,EAAAnO,MAAA,GACAoO,EAAApO,MAAA,IAVEkO,EAAA1C,SAAA,GA2BJ0F,EAAA7B,IACElB,EAAAnO,MAAA,CAAgBiO,QAAAoB,EACL7D,SAAA,GAGX4C,EAAApO,MAAA,IAEFmR,EAAA,KACEhD,EAAAnO,MAAAwL,SAAA,CAAA,EAEF4F,EAAA/B,IACEjB,EAAApO,MAAA,CAAciO,QAAAoB,EACH7D,SAAA,EACA,EAGb6F,EAAA,KACEjD,EAAApO,MAAAwL,SAAA,CAAA,EAEF8F,EAAAzF,UACE,MAAAC,QAAAyF,GAAA,CAAA,GAEA,GADAvD,EAAA1C,KAAAQ,EAAAI,KACAJ,EAAAI,MAAAnC,OAAA,CACE,MAAAiC,EAAA0C,OAAAsC,aAAAQ,QAAA,UACA,IAAAtH,EACA8B,IACE9B,EAAA4B,EAAAI,KAAAuF,WAAAvE,GAAAA,EAAAd,MAAAJ,KAEF8E,EAAA5G,GAAA4B,EAAAI,KAAAuF,WAAAvE,GAAAA,EAAAwE,aAAAC,GAAAC,OAAA1E,EAAAwE,aAAAC,GAAAE,SAAkI,GA4BtI,MAAA1C,EAAAtD,MAAAiG,IAGE,MAAAC,EA3BF,SAAAC,EAAAC,GAEE,IAAAF,EAAA,CAAA,EAoBA,OAjBAC,EAAAE,SAAAC,IAEEF,EAAAC,SAAAE,IACEA,EAAAC,OAAAH,SAAAI,IAEEA,EAAAC,WAAAJ,EAAAI,WAEER,EAAAK,EAAAG,YACER,EAAAK,EAAAG,UAAA,IAGFR,EAAAK,EAAAG,UAAAvC,KAAAmC,GAA+C,GACjD,GACD,IAILJ,CAAO,CAKPS,CAAAV,EAAAE,OAAAF,EAAAG,WACAxD,EAAAzO,MAAA,CAAqB6O,QAAA,EACVlQ,KAAA,UAGX,MAAA8T,QAAAC,QAAAC,IAAiCb,EAAAG,UAAAjI,KAAA6B,MAAAuG,EAAAlI,KAE7BkI,EAAAC,OAAAH,SAAAI,IACEA,EAAA1C,YACE0C,EAAA1C,WAAAsC,SAAAtC,WACEA,EAAAU,MAAA,GACD,IAkBL,aAfAsC,GAAA,CAA4BR,SAAAS,KAAAC,UAAAV,GACOzT,KAAAyT,EAAAzT,KAClBqT,OAAAa,KAAAC,UAAAf,EAAAK,EAAAG,WAAA,IACkDQ,KAAAjB,EAAAiB,KACtDC,MAAAZ,EAAAG,SACKU,OAAAb,EAAAc,aACCC,MAAAf,EAAAgB,UACDC,WAAAvB,EAAAuB,WACCC,YAAAxB,EAAAwB,YACCzJ,OAAAgJ,KAAAC,UAAAhB,EAAAjI,QACgBtF,QAAA6J,EAAApO,MAAAiO,SAAA7B,IACJS,UAAAsB,EAAAnO,MAAAiO,SAAA7B,IACIlC,WAGpCgC,IAAA,MAGJqH,IAAAA,EAAAC,OAAAA,GAAAf,EAAAgB,QAAmC,CAAAC,EAAAC,KAE/BD,EAAAC,EAAAC,QAAA,SAAA,OAAA5D,KAAA2D,EAAAhV,MACA+U,IACF,CACAH,IAAA,GACQC,OAAA,KAIV/E,EAAAzO,MAAA,CAAqB6O,QAAA,IACVgF,MAAA,EACHN,MACNC,SACA7U,KAAA,QAGFmQ,EAAApH,QAAA,QACAoM,GAAA,CAAWjH,UAAAsB,EAAAnO,MAAAiO,SAAA7B,IACyB7H,QAAA6J,EAAApO,MAAAiO,SAAA7B,IACJ2H,QAAAtB,IAGhCuB,YAAA,QAAiB,IAAA,EAKnBC,EAAA,KACE9F,EAAAnO,MAAAwL,SAAA,CAAA,EAGF0I,EAAA,KACE/F,EAAAnO,MAAAiO,UAGAG,EAAApO,MAAAwL,SAAA,EAAA,EASF7G,IAAA,KACEkJ,EAAAsG,cAmBF,MAAAC,EAAAvI,UACE,MAAAC,QAAAuI,GAAA,CAAkCC,MAAAjG,EAAArO,QAGlC,GAAA,IAAA8L,EAAAG,KAIE,OAHA6C,EAAAC,MAAAjD,EAAAyI,KACAlG,EAAArO,MAAA,QACAwO,EAAAxO,OAAAwU,OAGF,IAAA1I,GAAAI,OACEmC,EAAArO,MAAA,GACAwO,EAAAxO,OAAAwU,OACA1F,EAAApH,QAAA,YACgB,EAIpB+M,EAAA5I,UACEwC,EAAArO,MAhCF,WACE,IAAA0U,EAAA,GACAC,EAAA,mBACA,IAAA,IAAA9E,EAAA,EAAAA,EAAA,GAAAA,IACE6E,EAAA7E,GAAA8E,EAAAC,OAAAlM,KAAAmM,MAAA,GAAAnM,KAAAoM,UAAA,GAMF,OAJAJ,EAAA,IAAA,IACAA,EAAA,IAAAC,EAAAC,OAAA,EAAAF,EAAA,IAAA,EAAA,GACAA,EAAA,GAAAA,EAAA,IAAAA,EAAA,IAAAA,EAAA,IACAA,EAAA/G,KAAA,GACO,CAsBPoH,GACArG,OAAAC,YAAA,kBAAA,GAAAD,OAAAsG,SAAAC,yDAAA5G,EAAArO,SAEAwO,EAAAxO,MAAAkV,GAAAd,EAAA,IAAA,IACA5F,EAAAxO,MAAAmV,SAGFC,EAAAvJ,UACE,MAAAC,QAAAuJ,GAAA,CAAA,GACA,GAAA,KAAAvJ,EAAAzF,QAAA,GAAAyF,EAAAI,KAAAD,KAAA,CACE,MAAAqJ,EAAAxJ,EAAAI,KAAAA,KACA2B,EAAA0H,WAAA,CAAoBC,WAAAF,EAAAE,WACEC,MAAAH,EAAAG,MACLtB,MAAAmB,EAAAnB,MACA9V,KAAAiX,EAAAjX,KACDqX,OAAAJ,EAAAI,OACE7E,IAAAyE,EAAAzE,IACHlS,KAAA2W,EAAA3W,UAGV,GAST+M,EAAA,KACEwC,EAAA1C,SAAA,EACA0C,EAAAvP,KAAA,EAAA,EAGFiN,EAAAC,UACE,IAAAqC,EAAAvP,KACE,OAKF,WAHAgX,GAAA,CAA0BhX,KAAAuP,EAAAvP,QAG1BsN,OACE6C,EAAApH,QAAA,gBAEe,EAGnBkO,EAAA,KACElH,OAAAC,YAAA,SAAA,EAEFkH,EAAA,KACEnH,OAAAC,YAAA,kBAAA,GAAAD,OAAAsG,SAAAC,sCAAAjH,EAAAC,QAAA7B,iBAAA+B,EAAAnO,MAAAiO,SAAA7B,MAAA,EAEF0J,EAAA/E,IACE,OAAAA,GAAiB,IAAA,cAGb,MAAA,IAAA,SAEArC,OAAAC,YAAA,kBAAA,GAAAD,OAAAsG,SAAAC,sCAAAjH,EAAAC,QAAA7B,OAGA,EAGN2J,EAAA,SAzCExH,EAAAvO,MAAAgW,aAAA,KACEtH,OAAAC,YAAA,mBAAAf,EAAA5N,MAAA,GAA6D,inFA7M/DoO,EAAApO,OAAAiO,QAGAG,EAAApO,MAAAiO,QAAAP,SAAA1D,KAAA,EAAArL,UAAAA,IAAAgP,KAAA,KAFE", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}