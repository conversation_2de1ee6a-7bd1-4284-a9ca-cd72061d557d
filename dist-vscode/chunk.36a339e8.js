import{ac as e,a1 as t,E as o,d as a,_ as i,ad as s,c as n,$ as l,Q as r}from"./chunk.25a51fc3.js";import{E as d}from"./chunk.fd6abe75.js";/* empty css              */import{E as c}from"./chunk.5705a63a.js";import{E as u,a as p,b as h}from"./chunk.884698ee.js";import"./chunk.c5fb43ac.js";import"./chunk.615a7c87.js";/* empty css              */import{h as g,S as v}from"./chunk.503d5b6b.js";/* empty css              */import{am as m,d as y,y as f,X as _,r as b,z as w,g as k,a3 as x,w as I,an as C,E as j,o as E,c as M,I as V,a6 as D,b as T,i as B,B as L,F as P,a as A,A as G,n as q,u as W,e as H,k as K,C as R,q as F,h as S,ao as $,ap as z,aq as O,ar as N,p as U,j as X,af as Y,ag as Q,l as J,M as Z,as as ee,at as te,au as oe,av as ae,aw as ie}from"./index.7c7944d0.js";import{l as se}from"./chunk.79b97198.js";import{P as ne,E as le}from"./chunk.6d705473.js";import{v as re}from"./chunk.d5d38f7a.js";import{a as de}from"./chunk.48f0fbfa.js";import{E as ce}from"./chunk.616f76d1.js";import{h as ue,i as pe}from"./chunk.81490edf.js";import{E as he}from"./chunk.a37e6231.js";import"./chunk.42823e83.js";import"./chunk.b2d62236.js";const ge=m("boardStore",{state:()=>({hoverGroupInfo:{id:null,groupId:null,source:null}}),actions:{setHoverGroupInfo(e){this.hoverGroupInfo=e}}}),ve={class:"board"},me={class:"board-loading"},ye={class:"board-toolbar"},fe={class:"bottom-toolbar__middle"},_e="#5c54f0",be=i(y({__name:"board",props:{renderData:{},loading:{type:Boolean},name:{},teamId:{},permission:{}},emits:["update:loading"],setup(i,{emit:s}){const n=ge(),l=f(),r=i,d=s;_().proxy;const c=b(null),u=b(new Map),p=b([]),h=b(100),v=w((()=>r.permission!==ne.PREVIEW&&!0!==m.value.draggable)),m=b({width:0,height:0,x:0,y:0,draggable:!1});k((()=>{R(),window.addEventListener("keydown",F),window.addEventListener("keyup",S),window.addEventListener("resize",y)})),x((()=>{window.removeEventListener("keydown",F),window.removeEventListener("keyup",S),window.removeEventListener("resize",y)}));const y=()=>{m.value.width=window.innerWidth,m.value.height=window.innerHeight},R=()=>{g("command-=",{splitKey:"-"},(e=>{e.preventDefault(),O(!0)})),g("command+-",(e=>{e.preventDefault(),O(!1)}))},F=e=>{32===e.keyCode&&(m.value.draggable=!0)},S=()=>{m.value.draggable=!1},$=async e=>{let t={x1:0,x2:0,y1:0,y2:0},o=e[0];const a=[];await Promise.all(e.map((async e=>{e.position_x||(e.position_x=e.x),e.position_y||(e.position_y=e.y);let{x1:i,x2:s,y1:n,y2:l}=t;i=e.position_x>i?i:e.position_x,s=e.position_x<s?s:e.position_x,n=e.position_y>n?n:e.position_y,l=e.position_y<l?l:e.position_y,t={x1:i,x2:s,y1:n,y2:l},e.position_x<o.position_x&&e.position_y<o.position_y&&(o=e),e.imageElement=await(e=>{const t=document.createElement("canvas");t.width=e.width,t.height=e.height;const o=t.getContext("2d");o.fillStyle="#F3F4F6",o.fillRect(0,0,e.width,e.height);const a=new Image;return a.crossOrigin="anonymous",a.src="https://static.soyoung.com/sy-pre/image-1741831800627.png",new Promise((i=>{a.onload=()=>{const s=(e.width-e.width/3)/2,n=(e.height-e.width/3)/2;o.drawImage(a,s,n,e.width/3,e.width/3);const l=t.toDataURL("image/png"),r=new Image;r.src=l,i(r)}}))})(e),e.borderVisible=!1,u.value.set(e._id,e),a.push(((e,t)=>new Promise((o=>{const a=e;let i=e+"?imageView2/0/format/webp";const s=(n,l=!1)=>{const r=new Image;r.src=n,r.onload=()=>{t.imageElement=r,o()},r.onerror=()=>{l||i===a?(console.log("load failed:",e),o()):s(a,!0)}};s(i)})))(e.imagePath,e))}))),K().then((async()=>{await Promise.all(a),u.value=new Map(u.value)}));const i=t.x2-t.x1,s=t.y2-t.y1,l=document.body.clientWidth/i,r=document.body.clientHeight/s;let c=Number(Math.min(l,r).toFixed(2));c<.3&&(c=.3),c>1&&(c=.8),h.value=Number((100*c).toFixed(2)),m.value.scale={x:c,y:c},console.log("左上角的图",o);const p=u.value.get(n.hoverGroupInfo.id);o=p||o,n.setHoverGroupInfo({id:o._id,groupId:o.groupId,source:"konva"}),z(o),console.log("初始化konva",m.value),d("update:loading",!1)},z=e=>{const t=e.position_x||e.x,o=e.position_y||e.y;console.log("handleMovePositionXYToScreenCenter",t,o);const a=document.body.clientWidth,i=document.body.clientHeight,s=h.value/100;let n=a/2-t*s,l=i/2-o*s;m.value.x=n,m.value.y=l,console.log("configKonva",m.value)};I((()=>r.renderData),(e=>{Array.isArray(e)&&(async e=>{console.log("initKonva",e),m.value.width=document.body.clientWidth,m.value.height=document.body.clientHeight,u.value=new Map,Array.isArray(e)&&0!=e.length?$(e):(d("update:loading",!1),setTimeout((()=>{a.error("没有发现可用数据")}),200))})(se.cloneDeep(r.renderData))}),{deep:!0}),I((()=>r.loading),(e=>{console.log("newVal-props.loading",e)}),{immediate:!0}),I((()=>n.hoverGroupInfo),(e=>{if(e){const t=u.value.get(e.id);if(!t)return void console.log("没有找到元素");N(t._id,!0),"groupTree"===e.source&&z(t)}}),{deep:!0});const O=e=>{if(e){if(h.value>=300)return;h.value+=5}else{if(h.value<=5)return;h.value-=5}m.value.scale={x:h.value/100,y:h.value/100}},N=(e,t)=>{const o=u.value.get(e);o&&(u.value.forEach((e=>{e.borderVisible=!1})),o.borderVisible=t)},U=se.debounce((({id:e,x:t,y:o})=>{console.log("handleChangeSketchPosition",t,o),C({id:e,x:t,y:o})}),100),X=(e,t,o,a)=>{p.value.push({points:[e,t,o,a],stroke:"red",strokeWidth:2})};return(a,i)=>{const s=j("v-image"),d=j("v-rect"),g=j("v-text"),y=j("v-group"),f=j("v-line"),_=j("v-layer"),b=j("v-stage"),w=o,k=re;return E(),M("div",ve,[V(T("div",me,null,512),[[D,a.loading],[k,a.loading]]),B(b,{ref_key:"stage",ref:c,config:m.value,onDragend:i[0]||(i[0]=e=>{var t;"Stage"===(t=e).target.getClassName()&&(m.value.x=t.target.x(),m.value.y=t.target.y())}),onWheel:i[1]||(i[1]=e=>(e=>{e.evt.preventDefault();let t=m.value.x,o=m.value.y,a={x:t-e.evt.deltaX,y:o-e.evt.deltaY};m.value.x=a.x,m.value.y=a.y})(e))},{default:L((()=>[B(_,{ref:"boardLayer",config:{}},{default:L((()=>[(E(!0),M(P,null,A(u.value,(([e,t])=>(E(),G(y,{key:`group-${e}`,config:{x:t.position_x,y:t.position_y,draggable:v.value},onDblclick:e=>{return o=t._id,void l.push({path:"/item/project/detail",query:{id:o,teamId:r.teamId}});var o},onClick:e=>{return o=t._id,a=t.groupId,void(u.value.get(o)&&(N(o,!0),n.setHoverGroupInfo({id:o,groupId:a,source:"konva"})));var o,a},onMouseenter:e=>(({id:e,e:t})=>{if(u.value.size>0){const t=Array.from(u.value.keys()).pop();if(t!==e){const o=u.value.get(t),a=u.value.get(e);o&&a&&(u.value.delete(e),u.value.set(e,a))}}t.evt.target.style.cursor="pointer",p.value=[]})({id:t._id,e:e}),onMouseleave:e=>(({id:e,e:t})=>{t.evt.target.style.cursor="default",p.value=[]})({id:t._id,e:e}),onDragmove:e=>(({id:e,e:t})=>{if(!t.target.getStage().getPointerPosition())return;p.value=[];const o=t.target.attrs.x,a=t.target.attrs.y,i=u.value.get(e);if(!i)return;i.position_x=o,i.position_y=a,U({id:e,x:o,y:a});const s=10,n=20;u.value.forEach((l=>{const r=o<l.position_x,d=a<l.position_y,c=r?o+i.width+n>l.position_x&&o<l.position_x+l.width:o-n<l.position_x+l.width&&o>l.position_x,u=d?a+i.height+n>l.position_y&&a<l.position_y+l.height:a-n<l.position_y+l.height&&a>l.position_y;if(l._id!==e){if(c&&u){const e=o,s=o+i.width,r=a,d=a+i.height,c=l.position_x,u=l.position_x+l.width,p=l.position_y,h=l.position_y+l.height,g=c+.75*l.width,v=u-.75*l.width,m=p+.75*l.height,y=h-.75*l.height,f=o<l.position_x;console.log("isLeft",f?"左边":"右边"),console.log("movedX",o),console.log("currentGroupLeft - collisionThreshold < groupRight",e-n<u);const _=s+n>c&&s+n<g,b=e-n<u&&e-n>v,w=d+n>p&&d+n<m,k=r-n<h&&r-n>y;console.log("isCollisionTop",w),console.log("isCollisionBottom",k),console.log("isCollisionLeft",_),console.log("isCollisionRight",b);let x="";switch(b?x="right":_?x="left":k?x="bottom":w&&(x="top"),x){case"right":i.position_x=l.position_x+l.width,t.target.attrs.x=i.position_x;break;case"left":i.position_x=l.position_x-i.width,t.target.attrs.x=i.position_x;break;case"bottom":i.position_y=l.position_y+l.height,t.target.attrs.y=i.position_y;break;case"top":i.position_y=l.position_y-i.height,t.target.attrs.y=i.position_y}}const e=Math.abs(o-l.position_x)<s,p=Math.abs(o+i.width-(l.position_x+l.width))<s,h=Math.abs(a-l.position_y)<=s,g=Math.abs(a+i.height-(l.position_y+l.height))<s,v=Math.abs(o-(l.position_x+l.width))<s,m=Math.abs(o+i.width-l.position_x)<s,y=Math.abs(a-(l.position_y+l.height))<s,f=Math.abs(a+i.height-l.position_y)<s;if(v){const e=d?a:l.position_y,t=d?l.position_y+l.height:a;X(o,e,o,t)}if(m){const e=d?a:l.position_y,t=d?l.position_y+l.height:a;X(o+i.width,e,o+i.width,t)}if(y){const e=r?o:l.position_x,t=r?l.position_x+l.width:o;X(e,a,t,a)}if(f){const e=r?o:l.position_x,t=r?l.position_x+l.width:o;X(e,a+i.height,t,a+i.height)}if(h){const e=r?o:l.position_x,t=r?l.position_x+l.width:o;X(e,a,t,a)}if(g){const e=r?o:l.position_x,t=r?l.position_x+l.width:o;X(e,a+i.height,t,a+i.height)}if(e){const e=a<l.position_y,t=e?a:l.position_y,i=e?l.position_y+l.height:a;X(o,t,o,i)}if(p){const e=d?a:l.position_y,t=d?l.position_y+l.height:a;X(o+i.width,e,o+i.width,t)}}}))})({e:e,id:t._id}),onDragend:e=>(({id:e,e:t})=>{console.log(e,t),p.value=[]})({e:e,id:t._id}),onMousedown:e=>(({id:e,e:t})=>{console.log(t,e)})({e:e,id:t._id})},{default:L((()=>[B(s,{config:{image:t.imageElement,width:t.width||100,height:t.height||100}},null,8,["config"]),B(d,{config:{visible:t.borderVisible||!1,width:t.width,height:t.height,stroke:_e,strokeWidth:4}},null,8,["config"]),B(g,{config:{x:0,y:-40,text:t.name,fontSize:24,fontFamily:"Calibri",fill:t.borderVisible?_e:"#555",align:"left"}},null,8,["config"])])),_:2},1032,["config","onDblclick","onClick","onMouseenter","onMouseleave","onDragmove","onDragend","onMousedown"])))),128)),(E(!0),M(P,null,A(p.value,((e,t)=>(E(),G(f,{key:`helperLine-${t}`,config:e},null,8,["config"])))),128))])),_:1},512)])),_:1},8,["config"]),T("div",ye,[T("div",{class:q({"bottom-toolbar__left":!0,"bottom-toolbar__left-disable":h.value<10}),onClick:i[2]||(i[2]=e=>O(!1))},[B(w,null,{default:L((()=>[B(W(e))])),_:1})],2),T("div",fe,H(h.value)+"%",1),T("div",{class:q({"bottom-toolbar__right":!0,"bottom-toolbar__right-disable":h.value>300}),onClick:i[3]||(i[3]=e=>O(!0))},[B(w,null,{default:L((()=>[B(W(t))])),_:1})],2)])])}}}),[["__scopeId","data-v-431556b7"]]),we=e=>(U("data-v-ddb5f92b"),e=e(),X(),e),ke=["id"],xe={key:0,class:"group-tree-node"},Ie=we((()=>T("i",{class:"iconfont icon-a-sucaiku3x"},null,-1))),Ce={key:0,class:"node-right-bar"},je={class:"node-count"},Ee=we((()=>T("span",{style:{transform:"rotate(90deg)"}},[T("i",{class:"iconfont icon-gengduo svg-icon"})],-1))),Me={class:"node-label"},Ve={key:0,class:"node-right-bar"},De=we((()=>T("span",{style:{transform:"rotate(90deg)"}},[T("i",{class:"iconfont icon-gengduo svg-icon"})],-1))),Te=i(y({__name:"groupTree",props:{treeList:{},permission:{}},emits:["add","move","nodeClick","refresh"],setup(e,{emit:t}){const i=ge(),l=t,r={children:"subs"},d=(e,t,o)=>{console.log("tree drop:",t.label,o);const a={id:e.data._id,parentId:t.data._id,type:"sketch"===e.data.type?"sketch":"group"};l("move",a)},c=(e,t)=>"sketch"!==t.data.type&&e.data.groupId!=t.data._id,g=e=>{console.log("nodeClick:",e),e.type&&"sketch"===e.type||Array.isArray(e.subs)&&0!==e.subs.length?l("nodeClick",e):a.warning("该分组下没有素材")};return I((()=>i.hoverGroupInfo),(e=>{if(e){const t=document.getElementById(`groupTree-${e.id}`);t&&t.scrollIntoView({behavior:"smooth",block:"center"})}}),{deep:!0}),(e,t)=>{const v=n,m=u,y=p,f=h,_=o,b=de;return E(),G(b,{draggable:"","allow-drop":c,class:"group-tree",onNodeDrop:d,onNodeClick:g,"expand-on-click-node":!1,props:r,data:e.treeList,"node-key":"_id","default-expand-all":""},{default:L((({data:o})=>[T("div",{id:`groupTree-${o._id}`,style:{display:"flex",flex:"1",height:"100%"}},["sketch"!==o.type?(E(),M("div",xe,[T("div",null,[Ie,R(),T("span",null,H(o.name),1)]),e.permission!==W(ne).PREVIEW?(E(),M("div",Ce,[T("span",je,H(o.count||0),1),T("div",{class:"node-right-bar-handle",onClick:t[0]||(t[0]=F((()=>{}),["stop"]))},[B(f,{trigger:"click",placement:"bottom-start"},{dropdown:L((()=>[B(y,null,{default:L((()=>[B(m,{onClick:F((e=>(async e=>{const t=await ce.prompt("请输入分组名称","重命名",{confirmButtonText:"确认",cancelButtonText:"取消",inputValue:e.name});if(!t.value)return void a({type:"error",message:"名字不能为空"});0===(await z({name:t.value,id:e._id})).code?(a({type:"success",message:"重命名成功"}),l("refresh")):a({type:"error",message:"重命名失败"})})(o)),["stop"])},{default:L((()=>[R("重命名")])),_:2},1032,["onClick"]),B(m,{onClick:F((e=>(async e=>{if(e.subs.length)return a.warning("该分组下有子项目，请先删除子项目");await ce.confirm(`确定删除“${e.name}”分组吗？`,"注意",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});try{const t=await $({id:e._id});if(t.code!==le.OK)throw t.msg;a.success("删除成功"),l("refresh")}catch(t){a.error(t)}console.log(e.subs)})(o)),["stop"])},{default:L((()=>[R("删除分组")])),_:2},1032,["onClick"])])),_:2},1024)])),default:L((()=>[B(v,{type:"text",style:{"margin-right":"10px","margin-top":"2px"}},{default:L((()=>[Ee])),_:1})])),_:2},1024)])])):S("",!0)])):(E(),M("div",{key:1,class:q(["sketch-tree-node",{"sketch-tree-node--hover":o._id===W(i)?.hoverGroupInfo?.id}])},[T("div",Me,[B(_,{class:"iconfont"},{default:L((()=>[B(W(s))])),_:1}),T("span",null,H(o.name),1)]),e.permission!==W(ne).PREVIEW?(E(),M("div",Ve,[T("div",{class:"node-right-bar-handle",onClick:t[1]||(t[1]=F((()=>{}),["stop"]))},[B(f,{trigger:"click",placement:"bottom-start"},{dropdown:L((()=>[B(y,null,{default:L((()=>[B(m,{onClick:F((e=>(async e=>{const t=await ce.prompt("请输入设计图名称","重命名",{confirmButtonText:"确认",cancelButtonText:"取消",inputValue:e.name});if(!t.value)return void a({type:"error",message:"名字不能为空"});0===(await N({name:t.value,id:e._id})).code?(a({type:"success",message:"重命名成功"}),l("refresh")):a({type:"error",message:"重命名失败"})})(o)),["stop"])},{default:L((()=>[R("重命名")])),_:2},1032,["onClick"]),B(m,{onClick:F((e=>(async e=>{await ce.confirm(`确定删除“${e.name}”素材吗？`,"注意",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});try{const t=await O({id:e._id});if(t.code!==le.OK)throw t.msg;a.success("删除成功"),l("refresh")}catch(t){a.error(t)}})(o)),["stop"])},{default:L((()=>[R("删除")])),_:2},1032,["onClick"])])),_:2},1024)])),default:L((()=>[B(v,{type:"text"},{default:L((()=>[De])),_:1})])),_:2},1024)])])):S("",!0)],2))],8,ke)])),_:1},8,["data"])}}}),[["__scopeId","data-v-ddb5f92b"]]),Be=e=>(U("data-v-7f8bdb3a"),e=e(),X(),e),Le={class:"web-header-bar"},Pe={class:"bar-project-name-box"},Ae={class:"breadcrumb-divider"},Ge={class:"project-menu-wrapper-box"},qe={class:"el-dropdown-link"},We={class:"bar-project-menu"},He={key:0,class:"web-list-section-wrap"},Ke={key:0,class:"section-total-wrap",style:{width:"142px",padding:"0 !important"}},Re=[Be((()=>T("span",{class:"total-name"},"全部",-1))),Be((()=>T("div",{class:"total-name__icon"},[T("div",{class:"show-exban-button"},[T("i",{class:"iconfont icon-jiantouzhankai"})])],-1)))],Fe={key:1,class:"nav-tree-content nav-tree-content-show"},Se={class:"section-total-wrap"},$e=Be((()=>T("span",{class:"total-name"},"全部",-1))),ze={class:"total-num"},Oe=Be((()=>T("div",{class:"total-name__icon"},[T("div",{class:"show-exban-button"},[T("i",{class:"iconfont icon-jiantoushouqi"})])],-1))),Ne=Be((()=>T("div",{class:"section-line"},null,-1))),Ue={class:"section-group-list"},Xe={key:2,class:"board-placeholder"},Ye={key:0,class:"sketch-add-item"},Qe={class:"sketch-add-item"},Je={class:"sketch-add-footer"},Ze=i(y({__name:"list",setup(e){const t=f(),i=Y(),s=b(!1),m=b(""),y=b(""),_=Q();J();const I=ge(),C=w((()=>m.value?j.value.find((e=>e._id===m.value)):null)),j=b([]),V=b({}),D=b(!0),q=b([]),K=Z({visible:!1,name:"",parent:null}),F=()=>{const e=[O.value.name];C.value&&e.push(C.value.name),ue(O.value,ne.PREVIEW,`/#/item/project/stage?projectId=${m.value}&`,e)},$=()=>{t.replace({path:"/item/project/index",query:{teamId:y.value}})},z=()=>{},O=w((()=>_.teamList.find((e=>e._id==y.value)))),N=w((()=>O.value?O.value.permission:null)),U=()=>{s.value=!s.value},X=async()=>{try{D.value=!0;const e=await ee({projectId:m.value});if(e.code!==le.OK)throw e.msg;V.value=e.data;const t=e.data.list.find((e=>"sketch"===e.type||(Array.isArray(e.subs)&&e.subs.length>0?e.subs.find((e=>"sketch"===e.type)):void 0)));q.value=t?ve(t.subs):[]}catch(e){a.error(e)}},se=async()=>{const e=i.query;if(e.iv_id){delete e.teamId;const o=await pe(e.iv_id);if(!o)return;y.value=o,t.replace({path:i.path,query:{projectId:e.projectId,teamId:o}})}else y.value=e.teamId;await _.init(),await(async()=>{try{const e=await ie({teamId:i.query.teamId});if(e.code!==le.OK)throw e.msg;if(j.value=e.data.reverse(),e.data.length){let t;i.query.projectId&&(t=e.data.find((e=>{if(e._id===i.query.projectId)return!0}))),we(t?t._id:e.data[0]._id)}}catch(e){a.error(e)}})()},re=async e=>{"sketch"===e.type?await ye({id:e.id,groupId:e.parentId}):await fe({id:e.id,parentId:e.parentId}),await X()},de=(e,t)=>{for(const o of e){if(o._id===t)return o;if(Array.isArray(o.subs)&&o.subs.length>0){const e=de(o.subs,t);if(e)return e}}return null},ce=async e=>{if("sketch"===e.type){if(I.hoverGroupInfo.groupId!==e.groupId&&V.value.list){let t=de(V.value.list,e.groupId);t&&await me(t)}I.setHoverGroupInfo({id:e._id,groupId:e.groupId,source:"groupTree"})}else me(e)},ve=e=>{const t=[];return e.forEach((e=>{"sketch"==e.type?t.push(e):e.subs&&e.subs.length&&t.push(...ve(e.subs))})),t},me=async e=>{D.value=!0,q.value=ve(e.subs),D.value=!1},ye=async e=>{const t=await te(e);t.code!=le.OK&&a.error(t.msg)},fe=async e=>{const t=await oe(e);t.code!=le.OK&&a.error(t.msg)},_e=e=>{console.log("list-handleBoardGroupClick",e)},we=e=>{m.value!==e&&(t.replace({query:{...i.query,projectId:e}}),m.value=e,X())},ke=()=>{K.visible=!1,K.parent=null,K.name=""},xe=async()=>{if(!K.name)return;0===(await ae({projectId:m.value,parentId:K.parent?._id,name:K.name})).code&&(X(),ke())};k((()=>{se(),Ie()})),x((()=>{Ce()}));const Ie=()=>{g("shift+n",(()=>je()))},Ce=()=>{g.unbind("shift+n",(()=>je()))},je=e=>{K.visible=!0,K.parent=e};return(e,t)=>{const a=o,i=u,g=p,f=h,_=n,b=c,w=he,k=d;return E(),M(P,null,[T("div",{class:"web-list",onContextmenu:z},[T("div",Le,[T("div",{class:"web-header-logo"},[T("img",{src:"https://static.soyoung.com/sy-pre/2ou3xg3nvzrkw-1709791800662.png",alt:"logo",onClick:$})]),T("div",Pe,[O.value?(E(),M(P,{key:0},[T("div",{class:"breadcrumb-button",onClick:$},H(O.value.name),1),T("div",Ae,[B(a,{class:"el-icon--right"},{default:L((()=>[B(W(l))])),_:1})])],64)):S("",!0),T("div",Ge,[B(f,{trigger:"click",onCommand:we},{dropdown:L((()=>[B(g,{class:"project-dropdown-menu"},{default:L((()=>[(E(!0),M(P,null,A(j.value,(e=>(E(),G(i,{command:e._id,key:e._id,disabled:e._id===m.value},{default:L((()=>[R(H(e.name),1)])),_:2},1032,["command","disabled"])))),128))])),_:1})])),default:L((()=>[T("span",qe,[R(H(C.value?.name||"")+" ",1),B(a,{class:"el-icon--right"},{default:L((()=>[B(W(r))])),_:1})])])),_:1})])]),T("div",We,[C.value?(E(),G(_,{key:0,style:{"margin-right":"25px",width:"60px"},type:"primary",onClick:F},{default:L((()=>[R("分享")])),_:1})):S("",!0),B(v,{type:"list"})])]),N.value?(E(),M("div",He,[s.value?(E(),M("div",Ke,[T("div",{class:"hide-section",style:{background:"white"},onClick:U},Re)])):(E(),M("div",Fe,[T("div",Se,[T("div",{class:"section-total",onClick:U},[$e,T("div",ze,H(V.value.total),1),Oe]),Ne]),T("div",Ue,[B(Te,{permission:N.value,"tree-list":V.value.list,onAdd:je,onNodeClick:ce,onMove:re,onRefresh:X},null,8,["permission","tree-list"])])]))])):S("",!0),N.value?(E(),G(be,{key:1,class:"board","team-id":y.value,permission:N.value,renderData:q.value,name:C.value?.name,loading:D.value,"onUpdate:loading":t[0]||(t[0]=e=>D.value=e),onHandleBoardGroupClick:_e},null,8,["team-id","permission","renderData","name","loading"])):(E(),M("div",Xe,[B(b,{description:"暂无该项目权限，请联系项目管理员添加"})]))],32),B(k,{class:"sketch-add",modelValue:K.visible,"onUpdate:modelValue":t[2]||(t[2]=e=>K.visible=e),title:"新建分组",width:"400px"},{footer:L((()=>[T("span",Je,[B(_,{onClick:ke},{default:L((()=>[R("取消")])),_:1}),B(_,{type:"primary",onClick:xe},{default:L((()=>[R(" 确定 ")])),_:1})])])),default:L((()=>[K.parent?(E(),M("div",Ye,H(K.parent?.fullPath.map((({name:e})=>e)).join(" / "))+" /",1)):S("",!0),T("div",Qe,[B(w,{placeholder:"请输入分组名称",modelValue:K.name,"onUpdate:modelValue":t[1]||(t[1]=e=>K.name=e)},null,8,["modelValue"])])])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-7f8bdb3a"]]);export{Ze as default};
//# sourceMappingURL=chunk.36a339e8.js.map
