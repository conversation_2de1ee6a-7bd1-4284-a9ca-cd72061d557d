import{aI as e,aJ as t,d as r,r as n,aE as i,X as o,z as a,u as l,w as s,R as c,o as u,c as h,A as f,aG as d,V as p,n as g,f as m,k as v,g as y,aL as b}from"./index.05904f40.js";var w,x={exports:{}};function k(){return w||(w=1,x.exports=function(){var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=n||i||o,l=a&&(n?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),c=s&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\/(\d+)/.exec(e),h=u&&+u[1],f=/Opera\//.test(e),d=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),g=/PhantomJS/.test(e),m=d&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),v=/Android/.test(e),y=m||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=m||/Mac/.test(t),w=/\bCrOS\b/.test(e),x=/win/i.test(t),k=f&&e.match(/Version\/(\d*\.\d*)/);k&&(k=Number(k[1])),k&&k>=15&&(f=!1,s=!0);var C=b&&(c||f&&(null==k||k<12.11)),S=r||a&&l>=9;function M(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var L,T=function(e,t){var r=e.className,n=M(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function D(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function A(e,t){return D(e).appendChild(t)}function O(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function N(e,t,r,n){var i=O(e,t,r,n);return i.setAttribute("role","presentation"),i}function _(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function F(e){var t,r=e.ownerDocument||e;try{t=e.activeElement}catch(Ie){t=r.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function E(e,t){var r=e.className;M(t).test(r)||(e.className+=(r?" ":"")+t)}function W(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!M(r[n]).test(t)&&(t+=" "+r[n]);return t}L=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(Ie){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function z(e){return e.display.wrapper.ownerDocument}function I(e){return H(e.display.wrapper)}function H(e){return e.getRootNode?e.getRootNode():e.ownerDocument}function R(e){return z(e).defaultView}function B(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function j(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function V(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=r-a%r,o=l+1}}m?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(P=function(e){try{e.select()}catch(t){}});var U=function(){this.id=null,this.f=null,this.time=0,this.handler=B(this.onTimeout,this)};function K(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}U.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},U.prototype.set=function(e,t){this.f=t;var r=+new Date+e;(!this.id||r<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=r)};var G=50,q={toString:function(){return"CodeMirror.Pass"}},$={scroll:!1},X={origin:"*mouse"},Y={origin:"+move"};function Z(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||i+a>=t)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var Q=[""];function J(e){for(;Q.length<=e;)Q.push(ee(Q)+" ");return Q[e]}function ee(e){return e[e.length-1]}function te(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function re(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}function ne(){}function ie(e,t){var r;return Object.create?r=Object.create(e):(ne.prototype=e,r=new ne),t&&j(t,r),r}var oe=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ae(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||oe.test(e))}function le(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ae(e))||t.test(e):ae(e)}function se(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ce=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ue(e){return e.charCodeAt(0)>=768&&ce.test(e)}function he(e,t,r){for(;(r<0?t>0:t<e.length)&&ue(e.charAt(t));)t+=r;return t}function fe(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function de(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}var pe=null;function ge(e,t,r){var n;pe=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:pe=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:pe=i)}return null!=n?n:pe}var me=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function r(r){return r<=247?e.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?t.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L"}var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,a=/[Lb1n]/,l=/[1n]/;function s(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var c="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!n.test(e))return!1;for(var u=e.length,h=[],f=0;f<u;++f)h.push(r(e.charCodeAt(f)));for(var d=0,p=c;d<u;++d){var g=h[d];"m"==g?h[d]=p:p=g}for(var m=0,v=c;m<u;++m){var y=h[m];"1"==y&&"r"==v?h[m]="n":o.test(y)&&(v=y,"r"==y&&(h[m]="R"))}for(var b=1,w=h[0];b<u-1;++b){var x=h[b];"+"==x&&"1"==w&&"1"==h[b+1]?h[b]="1":","!=x||w!=h[b+1]||"1"!=w&&"n"!=w||(h[b]=w),w=x}for(var k=0;k<u;++k){var C=h[k];if(","==C)h[k]="N";else if("%"==C){var S=void 0;for(S=k+1;S<u&&"%"==h[S];++S);for(var M=k&&"!"==h[k-1]||S<u&&"1"==h[S]?"1":"N",L=k;L<S;++L)h[L]=M;k=S-1}}for(var T=0,D=c;T<u;++T){var A=h[T];"L"==D&&"1"==A?h[T]="L":o.test(A)&&(D=A)}for(var O=0;O<u;++O)if(i.test(h[O])){var N=void 0;for(N=O+1;N<u&&i.test(h[N]);++N);for(var _="L"==(O?h[O-1]:c),F=_==("L"==(N<u?h[N]:c))?_?"L":"R":c,E=O;E<N;++E)h[E]=F;O=N-1}for(var W,P=[],z=0;z<u;)if(a.test(h[z])){var I=z;for(++z;z<u&&a.test(h[z]);++z);P.push(new s(0,I,z))}else{var H=z,R=P.length,B="rtl"==t?1:0;for(++z;z<u&&"L"!=h[z];++z);for(var j=H;j<z;)if(l.test(h[j])){H<j&&(P.splice(R,0,new s(1,H,j)),R+=B);var V=j;for(++j;j<z&&l.test(h[j]);++j);P.splice(R,0,new s(2,V,j)),R+=B,H=j}else++j;H<z&&P.splice(R,0,new s(1,H,z))}return"ltr"==t&&(1==P[0].level&&(W=e.match(/^\s+/))&&(P[0].from=W[0].length,P.unshift(new s(0,0,W[0].length))),1==ee(P).level&&(W=e.match(/\s+$/))&&(ee(P).to-=W[0].length,P.push(new s(0,u-W[0].length,u)))),"rtl"==t?P.reverse():P}}();function ve(e,t){var r=e.order;return null==r&&(r=e.order=me(e.text,t)),r}var ye=[],be=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||ye).concat(r)}};function we(e,t){return e._handlers&&e._handlers[t]||ye}function xe(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=K(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function ke(e,t){var r=we(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function Ce(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ke(e,r||t.type,e,t),Ae(t)||t.codemirrorIgnore}function Se(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==K(r,t[n])&&r.push(t[n])}function Me(e,t){return we(e,t).length>0}function Le(e){e.prototype.on=function(e,t){be(this,e,t)},e.prototype.off=function(e,t){xe(this,e,t)}}function Te(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function De(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Ae(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Oe(e){Te(e),De(e)}function Ne(e){return e.target||e.srcElement}function _e(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var Fe,Ee,We=function(){if(a&&l<9)return!1;var e=O("div");return"draggable"in e||"dragDrop"in e}();function Pe(e){if(null==Fe){var t=O("span","​");A(e,O("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Fe=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&l<8))}var r=Fe?O("span","​"):O("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function ze(e){if(null!=Ee)return Ee;var t=A(e,document.createTextNode("AخA")),r=L(t,0,1).getBoundingClientRect(),n=L(t,1,2).getBoundingClientRect();return D(e),!(!r||r.left==r.right)&&(Ee=n.right-r.right<3)}var Ie,He=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},Re=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(Ie){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(Ie){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Be="oncopy"in(Ie=O("div"))||(Ie.setAttribute("oncopy","return;"),"function"==typeof Ie.oncopy),je=null;function Ve(e){if(null!=je)return je;var t=A(e,O("span","x")),r=t.getBoundingClientRect(),n=L(t,0,1).getBoundingClientRect();return je=Math.abs(r.left-n.left)>1}var Ue={},Ke={};function Ge(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ue[e]=t}function qe(e,t){Ke[e]=t}function $e(e){if("string"==typeof e&&Ke.hasOwnProperty(e))e=Ke[e];else if(e&&"string"==typeof e.name&&Ke.hasOwnProperty(e.name)){var t=Ke[e.name];"string"==typeof t&&(t={name:t}),(e=ie(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return $e("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return $e("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Xe(e,t){t=$e(t);var r=Ue[t.name];if(!r)return Xe(e,"text/plain");var n=r(e,t);if(Ye.hasOwnProperty(t.name)){var i=Ye[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var Ye={};function Ze(e,t){j(t,Ye.hasOwnProperty(e)?Ye[e]:Ye[e]={})}function Qe(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Je(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function et(e,t,r){return!e.startState||e.startState(t,r)}var tt=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function rt(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function nt(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function it(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function ot(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function at(e){if(null==e.parent)return null;for(var t=e.parent,r=K(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function lt(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return r+a}function st(e,t){return t>=e.first&&t<e.first+e.size}function ct(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ut(e,t,r){if(void 0===r&&(r=null),!(this instanceof ut))return new ut(e,t,r);this.line=e,this.ch=t,this.sticky=r}function ht(e,t){return e.line-t.line||e.ch-t.ch}function ft(e,t){return e.sticky==t.sticky&&0==ht(e,t)}function dt(e){return ut(e.line,e.ch)}function pt(e,t){return ht(e,t)<0?t:e}function gt(e,t){return ht(e,t)<0?e:t}function mt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function vt(e,t){if(t.line<e.first)return ut(e.first,0);var r=e.first+e.size-1;return t.line>r?ut(r,rt(e,r).text.length):yt(t,rt(e,t.line).text.length)}function yt(e,t){var r=e.ch;return null==r||r>t?ut(e.line,t):r<0?ut(e.line,0):e}function bt(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=vt(e,t[n]);return r}tt.prototype.eol=function(){return this.pos>=this.string.length},tt.prototype.sol=function(){return this.pos==this.lineStart},tt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},tt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},tt.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},tt.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},tt.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},tt.prototype.skipToEnd=function(){this.pos=this.string.length},tt.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},tt.prototype.backUp=function(e){this.pos-=e},tt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=V(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?V(this.string,this.lineStart,this.tabSize):0)},tt.prototype.indentation=function(){return V(this.string,null,this.tabSize)-(this.lineStart?V(this.string,this.lineStart,this.tabSize):0)},tt.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},tt.prototype.current=function(){return this.string.slice(this.start,this.pos)},tt.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},tt.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},tt.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var wt=function(e,t){this.state=e,this.lookAhead=t},xt=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function kt(e,t,r,n){var i=[e.state.modeGen],o={};Nt(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var a=r.state,l=function(n){r.baseTokens=i;var l=e.state.overlays[n],s=1,c=0;r.state=!0,Nt(e,t.text,l.mode,r,(function(e,t){for(var r=s;c<e;){var n=i[s];n>e&&i.splice(s,1,e,i[s+1],n),s+=2,c=Math.min(e,n)}if(t)if(l.opaque)i.splice(r,s-r,e,"overlay "+t),s=r+2;else for(;r<s;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}}),o),r.state=a,r.baseTokens=null,r.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)l(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function Ct(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=St(e,at(t)),i=t.text.length>e.options.maxHighlightLength&&Qe(e.doc.mode,n.state),o=kt(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function St(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new xt(n,!0,t);var o=_t(e,t,r),a=o>n.first&&rt(n,o-1).stateAfter,l=a?xt.fromSaved(n,a,o):new xt(n,et(n.mode),o);return n.iter(o,t,(function(r){Mt(e,r.text,l);var n=l.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?l.save():null,l.nextLine()})),r&&(n.modeFrontier=l.line),l}function Mt(e,t,r,n){var i=e.doc.mode,o=new tt(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&Lt(i,r.state);!o.eol();)Tt(i,o,r.state),o.start=o.pos}function Lt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Je(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function Tt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Je(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}xt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},xt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},xt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},xt.fromSaved=function(e,t,r){return t instanceof wt?new xt(e,Qe(e.mode,t.state),r,t.lookAhead):new xt(e,Qe(e.mode,t),r)},xt.prototype.save=function(e){var t=!1!==e?Qe(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new wt(t,this.maxLookAhead):t};var Dt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function At(e,t,r,n){var i,o,a=e.doc,l=a.mode,s=rt(a,(t=vt(a,t)).line),c=St(e,t.line,r),u=new tt(s.text,e.options.tabSize,c);for(n&&(o=[]);(n||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=Tt(l,u,c.state),n&&o.push(new Dt(u,i,Qe(a.mode,c.state)));return n?o:new Dt(u,i,c.state)}function Ot(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|\\s)"+r[2]+"(?:$|\\s)").test(t[n])||(t[n]+=" "+r[2])}return e}function Nt(e,t,r,n,i,o,a){var l=r.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,c=0,u=null,h=new tt(t,e.options.tabSize,n),f=e.options.addModeClass&&[null];for(""==t&&Ot(Lt(r,n.state),o);!h.eol();){if(h.pos>e.options.maxHighlightLength?(l=!1,a&&Mt(e,t,n,h.pos),h.pos=t.length,s=null):s=Ot(Tt(r,h,n.state,f),o),f){var d=f[0].name;d&&(s="m-"+(s?d+" "+s:d))}if(!l||u!=s){for(;c<h.start;)i(c=Math.min(h.start,c+5e3),u);u=s}h.start=h.pos}for(;c<h.pos;){var p=Math.min(h.pos,c+5e3);i(p,u),c=p}}function _t(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=rt(o,l-1),c=s.stateAfter;if(c&&(!r||l+(c instanceof wt?c.lookAhead:0)<=o.modeFrontier))return l;var u=V(s.text,null,e.options.tabSize);(null==i||n>u)&&(i=l-1,n=u)}return i}function Ft(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=rt(e,n).stateAfter;if(i&&(!(i instanceof wt)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}}var Et=!1,Wt=!1;function Pt(){Et=!0}function zt(){Wt=!0}function It(e,t,r){this.marker=e,this.from=t,this.to=r}function Ht(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function Rt(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Bt(e,t,r){var n=r&&window.WeakSet&&(r.markedSpans||(r.markedSpans=new WeakSet));n&&e.markedSpans&&n.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],n&&n.add(e.markedSpans)),t.marker.attachLine(e)}function jt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new It(a,o.from,l?null:o.to))}}return n}function Vt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new It(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return n}function Ut(e,t){if(t.full)return null;var r=st(e,t.from.line)&&rt(e,t.from.line).markedSpans,n=st(e,t.to.line)&&rt(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==ht(t.from,t.to),l=jt(r,i,a),s=Vt(n,o,a),c=1==t.text.length,u=ee(t.text).length+(c?i:0);if(l)for(var h=0;h<l.length;++h){var f=l[h];if(null==f.to){var d=Ht(s,f.marker);d?c&&(f.to=null==d.to?null:d.to+u):f.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];null!=g.to&&(g.to+=u),null==g.from?Ht(l,g.marker)||(g.from=u,c&&(l||(l=[])).push(g)):(g.from+=u,c&&(l||(l=[])).push(g))}l&&(l=Kt(l)),s&&s!=l&&(s=Kt(s));var m=[l];if(!c){var v,y=t.text.length-2;if(y>0&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new It(l[b].marker,null,null));for(var w=0;w<y;++w)m.push(v);m.push(s)}return m}function Kt(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Gt(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=K(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],l=a.find(0),s=0;s<i.length;++s){var c=i[s];if(!(ht(c.to,l.from)<0||ht(c.from,l.to)>0)){var u=[s,1],h=ht(c.from,l.from),f=ht(c.to,l.to);(h<0||!a.inclusiveLeft&&!h)&&u.push({from:c.from,to:l.from}),(f>0||!a.inclusiveRight&&!f)&&u.push({from:l.to,to:c.to}),i.splice.apply(i,u),s+=u.length-3}}return i}function qt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function $t(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Xt(e){return e.inclusiveLeft?-1:0}function Yt(e){return e.inclusiveRight?1:0}function Zt(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=ht(n.from,i.from)||Xt(e)-Xt(t);if(o)return-o;var a=ht(n.to,i.to)||Yt(e)-Yt(t);return a||t.id-e.id}function Qt(e,t){var r,n=Wt&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Zt(r,i.marker)<0)&&(r=i.marker);return r}function Jt(e){return Qt(e,!0)}function er(e){return Qt(e,!1)}function tr(e,t){var r,n=Wt&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Zt(r,o.marker)<0)&&(r=o.marker)}return r}function rr(e,t,r,n,i){var o=rt(e,t),a=Wt&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var c=s.marker.find(0),u=ht(c.from,r)||Xt(s.marker)-Xt(i),h=ht(c.to,n)||Yt(s.marker)-Yt(i);if(!(u>=0&&h<=0||u<=0&&h>=0)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?ht(c.to,r)>=0:ht(c.to,r)>0)||u>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?ht(c.from,n)<=0:ht(c.from,n)<0)))return!0}}}function nr(e){for(var t;t=Jt(e);)e=t.find(-1,!0).line;return e}function ir(e){for(var t;t=er(e);)e=t.find(1,!0).line;return e}function or(e){for(var t,r;t=er(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function ar(e,t){var r=rt(e,t),n=nr(r);return r==n?t:at(n)}function lr(e,t){if(t>e.lastLine())return t;var r,n=rt(e,t);if(!sr(e,n))return t;for(;r=er(n);)n=r.find(1,!0).line;return at(n)+1}function sr(e,t){var r=Wt&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&cr(e,t,n))return!0}}function cr(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return cr(e,n.line,Ht(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&cr(e,t,i))return!0}function ur(e){for(var t=0,r=(e=nr(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==r)break;t+=l.height}return t}function hr(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=Jt(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=er(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function fr(e){var t=e.display,r=e.doc;t.maxLine=rt(r,r.first),t.maxLineLength=hr(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=hr(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}var dr=function(e,t,r){this.text=e,$t(this,t),this.height=r?r(this):1};function pr(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),qt(e),$t(e,r);var i=n?n(e):1;i!=e.height&&ot(e,i)}function gr(e){e.parent=null,qt(e)}dr.prototype.lineNo=function(){return at(this)},Le(dr);var mr={},vr={};function yr(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?vr:mr;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function br(e,t){var r=N("span",null,null,s?"padding-right: .1px":null),n={pre:N("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;n.pos=0,n.addToken=xr,ze(e.display.measure)&&(a=ve(o,e.doc.direction))&&(n.addToken=Cr(n.addToken,a)),n.map=[],Mr(o,n,Ct(e,o,t!=e.display.externalMeasured&&at(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=W(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=W(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Pe(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var l=n.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return ke(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=W(n.pre.className,n.textClass||"")),n}function wr(e){var t=O("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function xr(e,t,r,n,i,o,s){if(t){var c,u=e.splitSpaces?kr(t,e.trailingSpace):t,h=e.cm.state.specialChars,f=!1;if(h.test(t)){c=document.createDocumentFragment();for(var d=0;;){h.lastIndex=d;var p=h.exec(t),g=p?p.index-d:t.length-d;if(g){var m=document.createTextNode(u.slice(d,d+g));a&&l<9?c.appendChild(O("span",[m])):c.appendChild(m),e.map.push(e.pos,e.pos+g,m),e.col+=g,e.pos+=g}if(!p)break;d+=g+1;var v=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=c.appendChild(O("span",J(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=c.appendChild(O("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),a&&l<9?c.appendChild(O("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),a&&l<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),r||n||i||f||o||s){var w=r||"";n&&(w+=n),i&&(w+=i);var x=O("span",[c],w,o);if(s)for(var k in s)s.hasOwnProperty(k)&&"style"!=k&&"class"!=k&&x.setAttribute(k,s[k]);return e.content.appendChild(x)}e.content.appendChild(c)}}function kr(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function Cr(e,t){return function(r,n,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var c=r.pos,u=c+n.length;;){for(var h=void 0,f=0;f<t.length&&!((h=t[f]).to>c&&h.from<=c);f++);if(h.to>=u)return e(r,n,i,o,a,l,s);e(r,n.slice(0,h.to-c),i,o,null,l,s),o=null,n=n.slice(h.to-c),c=h.to}}}function Sr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function Mr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,l,s,c,u,h,f,d=i.length,p=0,g=1,m="",v=0;;){if(v==p){s=c=u=l="",f=null,h=null,v=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],k=x.marker;if("bookmark"==k.type&&x.from==p&&k.widgetNode)y.push(k);else if(x.from<=p&&(null==x.to||x.to>p||k.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,c=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&x.from==p&&(u+=" "+k.startStyle),k.endStyle&&x.to==v&&(b||(b=[])).push(k.endStyle,x.to),k.title&&((f||(f={})).title=k.title),k.attributes)for(var C in k.attributes)(f||(f={}))[C]=k.attributes[C];k.collapsed&&(!h||Zt(h.marker,k)<0)&&(h=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var S=0;S<b.length;S+=2)b[S+1]==v&&(c+=" "+b[S]);if(!h||h.from==p)for(var M=0;M<y.length;++M)Sr(t,0,y[M]);if(h&&(h.from||0)==p){if(Sr(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=d)break;for(var L=Math.min(d,v);;){if(m){var T=p+m.length;if(!h){var D=T>L?m.slice(0,L-p):m;t.addToken(t,D,a?a+s:s,u,p+D.length==v?c:"",l,f)}if(T>=L){m=m.slice(L-p),p=L;break}p=T,u=""}m=i.slice(o,o=r[g++]),a=yr(r[g++],t.cm.options)}}else for(var A=1;A<r.length;A+=2)t.addToken(t,i.slice(o,o=r[A]),yr(r[A+1],t.cm.options))}function Lr(e,t,r){this.line=t,this.rest=or(t),this.size=this.rest?at(ee(this.rest))-r+1:1,this.node=this.text=null,this.hidden=sr(e,t)}function Tr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new Lr(e.doc,rt(e.doc,o),o);n=o+a.size,i.push(a)}return i}var Dr=null;function Ar(e){Dr?Dr.ops.push(e):e.ownsGroup=Dr={ops:[e],delayedCallbacks:[]}}function Or(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function Nr(e,t){var r=e.ownsGroup;if(r)try{Or(r)}finally{Dr=null,t(r)}}var _r=null;function Fr(e,t){var r=we(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);Dr?n=Dr.delayedCallbacks:_r?n=_r:(n=_r=[],setTimeout(Er,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},a=0;a<r.length;++a)o(a)}}function Er(){var e=_r;_r=null;for(var t=0;t<e.length;++t)e[t]()}function Wr(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?Hr(e,t):"gutter"==o?Br(e,t,r,n):"class"==o?Rr(e,t):"widget"==o&&jr(e,t,n)}t.changes=null}function Pr(e){return e.node==e.text&&(e.node=O("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&l<8&&(e.node.style.zIndex=2)),e.node}function zr(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=Pr(t);t.background=n.insertBefore(O("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}function Ir(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):br(e,t)}function Hr(e,t){var r=t.text.className,n=Ir(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Rr(e,t)):r&&(t.text.className=r)}function Rr(e,t){zr(e,t),t.line.wrapClass?Pr(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Br(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=Pr(t);t.gutterBackground=O("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=Pr(t),l=t.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(O("div",ct(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var c=e.display.gutterSpecs[s].className,u=o.hasOwnProperty(c)&&o[c];u&&l.appendChild(O("div",[u],"CodeMirror-gutter-elt","left: "+n.gutterLeft[c]+"px; width: "+n.gutterWidth[c]+"px"))}}}function jr(e,t,r){t.alignable&&(t.alignable=null);for(var n=M("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,n.test(i.className)&&t.node.removeChild(i);Ur(e,t,r)}function Vr(e,t,r,n){var i=Ir(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Rr(e,t),Br(e,t,r,n),Ur(e,t,n),t.node}function Ur(e,t,r){if(Kr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)Kr(e,t.rest[n],t,r,!1)}function Kr(e,t,r,n,i){if(t.widgets)for(var o=Pr(r),a=0,l=t.widgets;a<l.length;++a){var s=l[a],c=O("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Gr(s,c,r,n),e.display.input.setUneditable(c),i&&s.above?o.insertBefore(c,r.gutter||r.text):o.appendChild(c),Fr(s,"redraw")}}function Gr(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function qr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!_(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),A(t.display.measure,O("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function $r(e,t){for(var r=Ne(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Xr(e){return e.lineSpace.offsetTop}function Yr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Zr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=A(e.measure,O("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Qr(e){return G-e.display.nativeBarWidth}function Jr(e){return e.display.scroller.clientWidth-Qr(e)-e.display.barWidth}function en(e){return e.display.scroller.clientHeight-Qr(e)-e.display.barHeight}function tn(e,t,r){var n=e.options.lineWrapping,i=n&&Jr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],c=a[l+1];Math.abs(s.bottom-c.bottom)>2&&o.push((s.bottom+c.top)/2-r.top)}}o.push(r.bottom-r.top)}}function rn(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(at(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function nn(e,t){var r=at(t=nr(t)),n=e.display.externalMeasured=new Lr(e.doc,t,r);n.lineN=r;var i=n.built=br(e,n);return n.text=i.pre,A(e.display.lineMeasure,i.pre),n}function on(e,t,r,n){return sn(e,ln(e,t),r,n)}function an(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Bn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function ln(e,t){var r=at(t),n=an(e,r);n&&!n.text?n=null:n&&n.changes&&(Wr(e,n,r,Pn(e)),e.curOp.forceUpdate=!0),n||(n=nn(e,t));var i=rn(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function sn(e,t,r,n,i){t.before&&(r=-1);var o,a=r+(n||"");return t.cache.hasOwnProperty(a)?o=t.cache[a]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(tn(e,t.view,t.rect),t.hasHeights=!0),(o=dn(e,t,r,n)).bogus||(t.cache[a]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var cn,un={left:0,right:0,top:0,bottom:0};function hn(e,t,r){for(var n,i,o,a,l,s,c=0;c<e.length;c+=3)if(l=e[c],s=e[c+1],t<l?(i=0,o=1,a="left"):t<s?o=1+(i=t-l):(c==e.length-3||t==s&&e[c+3]>t)&&(i=(o=s-l)-1,t>=s&&(a="right")),null!=i){if(n=e[c+2],l==s&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)n=e[2+(c-=3)],a="left";if("right"==r&&i==s-l)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)n=e[(c+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function fn(e,t){var r=un;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function dn(e,t,r,n){var i,o=hn(t.map,r,n),s=o.node,c=o.start,u=o.end,h=o.collapse;if(3==s.nodeType){for(var f=0;f<4;f++){for(;c&&ue(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ue(t.line.text.charAt(o.coverStart+u));)++u;if((i=a&&l<9&&0==c&&u==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():fn(L(s,c,u).getClientRects(),n)).left||i.right||0==c)break;u=c,c-=1,h="right"}a&&l<11&&(i=pn(e.display.measure,i))}else{var d;c>0&&(h=n="right"),i=e.options.lineWrapping&&(d=s.getClientRects()).length>1?d["right"==n?d.length-1:0]:s.getBoundingClientRect()}if(a&&l<9&&!c&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+Wn(e.display),top:p.top,bottom:p.bottom}:un}for(var g=i.top-t.rect.top,m=i.bottom-t.rect.top,v=(g+m)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],k={left:("right"==h?i.right:i.left)-t.rect.left,right:("left"==h?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=g,k.rbottom=m),k}function pn(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Ve(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function gn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function mn(e){e.display.externalMeasure=null,D(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)gn(e.display.view[t])}function vn(e){mn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function yn(e){return u&&v?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function bn(e){return u&&v?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function wn(e){var t=nr(e).widgets,r=0;if(t)for(var n=0;n<t.length;++n)t[n].above&&(r+=qr(t[n]));return r}function xn(e,t,r,n,i){if(!i){var o=wn(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var a=ur(t);if("local"==n?a+=Xr(e.display):a-=e.display.viewOffset,"page"==n||"window"==n){var l=e.display.lineSpace.getBoundingClientRect();a+=l.top+("window"==n?0:bn(z(e)));var s=l.left+("window"==n?0:yn(z(e)));r.left+=s,r.right+=s}return r.top+=a,r.bottom+=a,r}function kn(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=yn(z(e)),i-=bn(z(e));else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function Cn(e,t,r,n,i){return n||(n=rt(e.doc,t.line)),xn(e,n,on(e,n,t.ch,i),r)}function Sn(e,t,r,n,i,o){function a(t,a){var l=sn(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,xn(e,n,l,r)}n=n||rt(e.doc,t.line),i||(i=ln(e,n));var l=ve(n,e.doc.direction),s=t.ch,c=t.sticky;if(s>=n.text.length?(s=n.text.length,c="before"):s<=0&&(s=0,c="after"),!l)return a("before"==c?s-1:s,"before"==c);function u(e,t,r){return a(r?e-1:e,1==l[t].level!=r)}var h=ge(l,s,c),f=pe,d=u(s,h,"before"==c);return null!=f&&(d.other=u(s,f,"before"!=c)),d}function Mn(e,t){var r=0;t=vt(e.doc,t),e.options.lineWrapping||(r=Wn(e.display)*t.ch);var n=rt(e.doc,t.line),i=ur(n)+Xr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function Ln(e,t,r,n,i){var o=ut(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function Tn(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return Ln(n.first,0,null,-1,-1);var i=lt(n,r),o=n.first+n.size-1;if(i>o)return Ln(n.first+n.size-1,rt(n,o).text.length,null,1,1);t<0&&(t=0);for(var a=rt(n,i);;){var l=Nn(e,a,i,t,r),s=tr(a,l.ch+(l.xRel>0||l.outside>0?1:0));if(!s)return l;var c=s.find(1);if(c.line==i)return c;a=rt(n,i=c.line)}}function Dn(e,t,r,n){n-=wn(t);var i=t.text.length,o=fe((function(t){return sn(e,r,t-1).bottom<=n}),i,0);return{begin:o,end:i=fe((function(t){return sn(e,r,t).top>n}),o,i)}}function An(e,t,r,n){return r||(r=ln(e,t)),Dn(e,t,r,xn(e,t,sn(e,r,n),"line").top)}function On(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function Nn(e,t,r,n,i){i-=ur(t);var o=ln(e,t),a=wn(t),l=0,s=t.text.length,c=!0,u=ve(t,e.doc.direction);if(u){var h=(e.options.lineWrapping?Fn:_n)(e,t,r,o,u,n,i);l=(c=1!=h.level)?h.from:h.to-1,s=c?h.to:h.from-1}var f,d,p=null,g=null,m=fe((function(t){var r=sn(e,o,t);return r.top+=a,r.bottom+=a,!!On(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(p=t,g=r),!0)}),l,s),v=!1;if(g){var y=n-g.left<g.right-n,b=y==c;m=p+(b?0:1),d=b?"after":"before",f=y?g.left:g.right}else{c||m!=s&&m!=l||m++,d=0==m?"after":m==t.text.length?"before":sn(e,o,m-(c?1:0)).bottom+a<=i==c?"after":"before";var w=Sn(e,ut(r,m,d),"line",t,o);f=w.left,v=i<w.top?-1:i>=w.bottom?1:0}return Ln(r,m=he(t.text,m,1),d,v,n-f)}function _n(e,t,r,n,i,o,a){var l=fe((function(l){var s=i[l],c=1!=s.level;return On(Sn(e,ut(r,c?s.to:s.from,c?"before":"after"),"line",t,n),o,a,!0)}),0,i.length-1),s=i[l];if(l>0){var c=1!=s.level,u=Sn(e,ut(r,c?s.from:s.to,c?"after":"before"),"line",t,n);On(u,o,a,!0)&&u.top>a&&(s=i[l-1])}return s}function Fn(e,t,r,n,i,o,a){var l=Dn(e,t,n,a),s=l.begin,c=l.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,h=null,f=0;f<i.length;f++){var d=i[f];if(!(d.from>=c||d.to<=s)){var p=sn(e,n,1!=d.level?Math.min(c,d.to)-1:Math.max(s,d.from)).right,g=p<o?o-p+1e9:p-o;(!u||h>g)&&(u=d,h=g)}}return u||(u=i[i.length-1]),u.from<s&&(u={from:s,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}function En(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==cn){cn=O("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)cn.appendChild(document.createTextNode("x")),cn.appendChild(O("br"));cn.appendChild(document.createTextNode("x"))}A(e.measure,cn);var r=cn.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),D(e.measure),r||1}function Wn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=O("span","xxxxxxxxxx"),r=O("pre",[t],"CodeMirror-line-like");A(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Pn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var l=e.display.gutterSpecs[a].className;r[l]=o.offsetLeft+o.clientLeft+i,n[l]=o.clientWidth}return{fixedPos:zn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function zn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function In(e){var t=En(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/Wn(e.display)-3);return function(i){if(sr(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function Hn(e){var t=e.doc,r=In(e);t.iter((function(e){var t=r(e);t!=e.height&&ot(e,t)}))}function Rn(e,t,r,n){var i=e.display;if(!r&&"true"==Ne(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(h){return null}var s,c=Tn(e,o,a);if(n&&c.xRel>0&&(s=rt(e.doc,c.line).text).length==c.ch){var u=V(s,s.length,e.options.tabSize)-s.length;c=ut(c.line,Math.max(0,Math.round((o-Zr(e.display).left)/Wn(e.display))-u))}return c}function Bn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function jn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Wt&&ar(e.doc,t)<i.viewTo&&Un(e);else if(r<=i.viewFrom)Wt&&lr(e.doc,r+n)>i.viewFrom?Un(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Un(e);else if(t<=i.viewFrom){var o=Kn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Un(e)}else if(r>=i.viewTo){var a=Kn(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):Un(e)}else{var l=Kn(e,t,t,-1),s=Kn(e,r,r+n,1);l&&s?(i.view=i.view.slice(0,l.index).concat(Tr(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=n):Un(e)}var c=i.externalMeasured;c&&(r<c.lineN?c.lineN+=n:t<c.lineN+c.size&&(i.externalMeasured=null))}function Vn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Bn(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==K(a,r)&&a.push(r)}}}function Un(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Kn(e,t,r,n){var i,o=Bn(e,t),a=e.display.view;if(!Wt||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(n>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,r+=i}for(;ar(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Gn(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=Tr(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=Tr(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Bn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(Tr(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Bn(e,r)))),n.viewTo=r}function qn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function $n(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Xn(e,t){void 0===t&&(t=!0);var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=e.options.$customCursor;a&&(t=!0);for(var l=0;l<r.sel.ranges.length;l++)if(t||l!=r.sel.primIndex){var s=r.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var c=s.empty();if(a){var u=a(e,s);u&&Yn(e,u,i)}else(c||e.options.showCursorWhenSelecting)&&Yn(e,s.head,i);c||Qn(e,s,o)}}return n}function Yn(e,t,r){var n=Sn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(O("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=Cn(e,t,"div",null,null),a=o.right-o.left;i.style.width=(a>0?a:e.defaultCharWidth())+"px"}if(n.other){var l=r.appendChild(O("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=n.other.left+"px",l.style.top=n.other.top+"px",l.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function Zn(e,t){return e.top-t.top||e.left-t.left}function Qn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),a=Zr(e.display),l=a.left,s=Math.max(n.sizerWidth,Jr(e)-n.sizer.offsetLeft)-a.right,c="ltr"==i.direction;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?s-e:r)+"px;\n                             height: "+(n-t)+"px"))}function h(t,r,n){var o,a,h=rt(i,t),f=h.text.length;function d(r,n){return Cn(e,ut(t,r),"div",h,n)}function p(t,r,n){var i=An(e,h,null,t),o="ltr"==r==("after"==n)?"left":"right";return d("after"==n?i.begin:i.end-(/\s/.test(h.text.charAt(i.end-1))?2:1),o)[o]}var g=ve(h,i.direction);return de(g,r||0,null==n?f:n,(function(e,t,i,h){var m="ltr"==i,v=d(e,m?"left":"right"),y=d(t-1,m?"right":"left"),b=null==r&&0==e,w=null==n&&t==f,x=0==h,k=!g||h==g.length-1;if(y.top-v.top<=3){var C=(c?w:b)&&k,S=(c?b:w)&&x?l:(m?v:y).left,M=C?s:(m?y:v).right;u(S,v.top,M-S,v.bottom)}else{var L,T,D,A;m?(L=c&&b&&x?l:v.left,T=c?s:p(e,i,"before"),D=c?l:p(t,i,"after"),A=c&&w&&k?s:y.right):(L=c?p(e,i,"before"):l,T=!c&&b&&x?s:v.right,D=!c&&w&&k?l:y.left,A=c?p(t,i,"after"):s),u(L,v.top,T-L,v.bottom),v.bottom<y.top&&u(l,v.bottom,null,y.top),u(D,y.top,A-D,y.bottom)}(!o||Zn(v,o)<0)&&(o=v),Zn(y,o)<0&&(o=y),(!a||Zn(v,a)<0)&&(a=v),Zn(y,a)<0&&(a=y)})),{start:o,end:a}}var f=t.from(),d=t.to();if(f.line==d.line)h(f.line,f.ch,d.ch);else{var p=rt(i,f.line),g=rt(i,d.line),m=nr(p)==nr(g),v=h(f.line,f.ch,m?p.text.length+1:null).end,y=h(d.line,m?0:null,d.ch).start;m&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(l,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(l,v.bottom,null,y.top)}r.appendChild(o)}function Jn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||ni(e),t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function ei(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||ri(e))}function ti(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&ni(e))}),100)}function ri(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(ke(e,"focus",e,t),e.state.focused=!0,E(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Jn(e))}function ni(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ke(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function ii(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,s=0;s<t.view.length;s++){var c=t.view[s],u=e.options.lineWrapping,h=void 0,f=0;if(!c.hidden){if(i+=c.line.height,a&&l<8){var d=c.node.offsetTop+c.node.offsetHeight;h=d-r,r=d}else{var p=c.node.getBoundingClientRect();h=p.bottom-p.top,!u&&c.text.firstChild&&(f=c.text.firstChild.getBoundingClientRect().right-p.left-1)}var g=c.line.height-h;if((g>.005||g<-.005)&&(i<n&&(o-=g),ot(c.line,h),oi(c.line),c.rest))for(var m=0;m<c.rest.length;m++)oi(c.rest[m]);if(f>e.display.sizerWidth){var v=Math.ceil(f/Wn(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=c.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function oi(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function ai(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Xr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=lt(t,n),a=lt(t,i);if(r&&r.ensure){var l=r.ensure.from.line,s=r.ensure.to.line;l<o?(o=l,a=lt(t,ur(rt(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=lt(t,ur(rt(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function li(e,t){if(!Ce(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null,o=r.wrapper.ownerDocument;if(t.top+n.top<0?i=!0:t.bottom+n.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),null!=i&&!g){var a=O("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Xr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Qr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(a),a.scrollIntoView(i),e.display.lineSpace.removeChild(a)}}}function si(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==t.sticky?ut(t.line,t.ch+1,"before"):t,t=t.ch?ut(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var a=!1,l=Sn(e,t),s=r&&r!=t?Sn(e,r):l,c=ui(e,i={left:Math.min(l.left,s.left),top:Math.min(l.top,s.top)-n,right:Math.max(l.left,s.left),bottom:Math.max(l.bottom,s.bottom)+n}),u=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=c.scrollTop&&(vi(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=c.scrollLeft&&(bi(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(a=!0)),!a)break}return i}function ci(e,t){var r=ui(e,t);null!=r.scrollTop&&vi(e,r.scrollTop),null!=r.scrollLeft&&bi(e,r.scrollLeft)}function ui(e,t){var r=e.display,n=En(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=en(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var l=e.doc.height+Yr(r),s=t.top<n,c=t.bottom>l-n;if(t.top<i)a.scrollTop=s?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?l:t.bottom)-o);u!=i&&(a.scrollTop=u)}var h=e.options.fixedGutter?0:r.gutters.offsetWidth,f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft-h,d=Jr(e)-r.gutters.offsetWidth,p=t.right-t.left>d;return p&&(t.right=t.left+d),t.left<10?a.scrollLeft=0:t.left<f?a.scrollLeft=Math.max(0,t.left+h-(p?0:10)):t.right>d+f-3&&(a.scrollLeft=t.right+(p?0:10)-d),a}function hi(e,t){null!=t&&(gi(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function fi(e){gi(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function di(e,t,r){null==t&&null==r||gi(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function pi(e,t){gi(e),e.curOp.scrollToPos=t}function gi(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,mi(e,Mn(e,t.from),Mn(e,t.to),t.margin))}function mi(e,t,r,n){var i=ui(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});di(e,i.scrollLeft,i.scrollTop)}function vi(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||$i(e,{top:t}),yi(e,t,!0),r&&$i(e),Ri(e,100))}function yi(e,t,r){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function bi(e,t,r,n){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,Qi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function wi(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+Yr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Qr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var xi=function(e,t,r){this.cm=r;var n=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),be(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),be(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,a&&l<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};xi.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},xi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},xi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},xi.prototype.zeroWidthHack=function(){var e=b&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new U,this.disableVert=new U},xi.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,n)}e.style.visibility="",t.set(1e3,n)},xi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var ki=function(){};function Ci(e,t){t||(t=wi(e));var r=e.display.barWidth,n=e.display.barHeight;Si(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&ii(e),Si(e,wi(e)),r=e.display.barWidth,n=e.display.barHeight}function Si(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}ki.prototype.update=function(){return{bottom:0,right:0}},ki.prototype.setScrollLeft=function(){},ki.prototype.setScrollTop=function(){},ki.prototype.clear=function(){};var Mi={native:xi,null:ki};function Li(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Mi[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),be(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?bi(e,t):vi(e,t)}),e),e.display.scrollbars.addClass&&E(e.display.wrapper,e.display.scrollbars.addClass)}var Ti=0;function Di(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Ti,markArrays:null},Ar(e.curOp)}function Ai(e){var t=e.curOp;t&&Nr(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;Oi(e)}))}function Oi(e){for(var t=e.ops,r=0;r<t.length;r++)Ni(t[r]);for(var n=0;n<t.length;n++)_i(t[n]);for(var i=0;i<t.length;i++)Fi(t[i]);for(var o=0;o<t.length;o++)Ei(t[o]);for(var a=0;a<t.length;a++)Wi(t[a])}function Ni(e){var t=e.cm,r=t.display;Vi(t),e.updateMaxLine&&fr(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ji(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function _i(e){e.updatedDisplay=e.mustUpdate&&Gi(e.cm,e.update)}function Fi(e){var t=e.cm,r=t.display;e.updatedDisplay&&ii(t),e.barMeasure=wi(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=on(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Qr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Jr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Ei(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&bi(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==F(I(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Ci(t,e.barMeasure),e.updatedDisplay&&Zi(t,e.barMeasure),e.selectionChanged&&Jn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&ei(e.cm)}function Wi(e){var t=e.cm,r=t.display,n=t.doc;e.updatedDisplay&&qi(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&yi(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&bi(t,e.scrollLeft,!0,!0),e.scrollToPos&&li(t,si(t,vt(n,e.scrollToPos.from),vt(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||ke(i[a],"hide");if(o)for(var l=0;l<o.length;++l)o[l].lines.length&&ke(o[l],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&ke(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Pi(e,t){if(e.curOp)return t();Di(e);try{return t()}finally{Ai(e)}}function zi(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Di(e);try{return t.apply(e,arguments)}finally{Ai(e)}}}function Ii(e){return function(){if(this.curOp)return e.apply(this,arguments);Di(this);try{return e.apply(this,arguments)}finally{Ai(this)}}}function Hi(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Di(t);try{return e.apply(this,arguments)}finally{Ai(t)}}}function Ri(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,B(Bi,e))}function Bi(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=St(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(n.line>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength?Qe(t.mode,n.state):null,s=kt(e,o,n,!0);l&&(n.state=l),o.styles=s.styles;var c=o.styleClasses,u=s.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var h=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),f=0;!h&&f<a.length;++f)h=a[f]!=o.styles[f];h&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&Mt(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return Ri(e,e.options.workDelay),!0})),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&Pi(e,(function(){for(var t=0;t<i.length;t++)Vn(e,i[t],"text")}))}}var ji=function(e,t,r){var n=e.display;this.viewport=t,this.visible=ai(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Jr(e),this.force=r,this.dims=Pn(e),this.events=[]};function Vi(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Qr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Qr(e)+"px",t.scrollbarsClipped=!0)}function Ui(e){if(e.hasFocus())return null;var t=F(I(e));if(!t||!_(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=R(e).getSelection();n.anchorNode&&n.extend&&_(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function Ki(e){if(e&&e.activeElt&&e.activeElt!=F(H(e.activeElt))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&_(document.body,e.anchorNode)&&_(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,r=t.defaultView.getSelection(),n=t.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),r.removeAllRanges(),r.addRange(n),r.extend(e.focusNode,e.focusOffset)}}function Gi(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Un(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==qn(e))return!1;Ji(e)&&(Un(e),t.dims=Pn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),Wt&&(o=ar(e.doc,o),a=lr(e.doc,a));var l=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;Gn(e,o,a),r.viewOffset=ur(rt(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var s=qn(e);if(!l&&0==s&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var c=Ui(e);return s>4&&(r.lineDiv.style.display="none"),Xi(e,r.updateLineNumbers,t.dims),s>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,Ki(c),D(r.cursorDiv),D(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,l&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Ri(e,400)),r.updateLineNumbers=null,!0}function qi(e,t){for(var r=t.viewport,n=!0;;n=!1){if(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Jr(e))n&&(t.visible=ai(e.display,e.doc,r));else if(r&&null!=r.top&&(r={top:Math.min(e.doc.height+Yr(e.display)-en(e),r.top)}),t.visible=ai(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Gi(e,t))break;ii(e);var i=wi(e);$n(e),Ci(e,i),Zi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function $i(e,t){var r=new ji(e,t);if(Gi(e,r)){ii(e),qi(e,r);var n=wi(e);$n(e),Ci(e,n),Zi(e,n),r.finish()}}function Xi(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function l(t){var r=t.nextSibling;return s&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var c=n.view,u=n.viewFrom,h=0;h<c.length;h++){var f=c[h];if(f.hidden);else if(f.node&&f.node.parentNode==o){for(;a!=f.node;)a=l(a);var d=i&&null!=t&&t<=u&&f.lineNumber;f.changes&&(K(f.changes,"gutter")>-1&&(d=!1),Wr(e,f,u,r)),d&&(D(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(ct(e.options,u)))),a=f.node.nextSibling}else{var p=Vr(e,f,u,r);o.insertBefore(p,a)}u+=f.size}for(;a;)a=l(a)}function Yi(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Fr(e,"gutterChanged",e)}function Zi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Qr(e)+"px"}function Qi(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=zn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var l=r[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function Ji(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=ct(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(O("div",[O("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",Yi(e.display),!0}return!1}function eo(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:a})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function to(e){var t=e.gutters,r=e.gutterSpecs;D(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,a=i.style,l=t.appendChild(O("div",null,"CodeMirror-gutter "+o));a&&(l.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=l,l.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",Yi(e)}function ro(e){to(e.display),jn(e),Qi(e)}function no(e,t,n,i){var o=this;this.input=n,o.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=N("div",null,"CodeMirror-code"),o.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=O("div",null,"CodeMirror-cursors"),o.measure=O("div",null,"CodeMirror-measure"),o.lineMeasure=O("div",null,"CodeMirror-measure"),o.lineSpace=N("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=N("div",[o.lineSpace],"CodeMirror-lines");o.mover=O("div",[c],null,"position: relative"),o.sizer=O("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=O("div",null,null,"position: absolute; height: "+G+"px; width: 1px;"),o.gutters=O("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=O("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=O("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),u&&h>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),a&&l<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),s||r&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=eo(i.gutters,i.lineNumbers),to(o),n.init(o)}ji.prototype.signal=function(e,t){Me(e,t)&&this.events.push(arguments)},ji.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ke.apply(null,this.events[e])};var io=0,oo=null;function ao(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function lo(e){var t=ao(e);return t.x*=oo,t.y*=oo,t}function so(e,t){u&&102==h&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout((function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""}),100));var n=ao(t),i=n.x,o=n.y,a=oo;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,a=1);var l=e.display,c=l.scroller,d=c.scrollWidth>c.clientWidth,p=c.scrollHeight>c.clientHeight;if(i&&d||o&&p){if(o&&b&&s)e:for(var g=t.target,m=l.view;g!=c;g=g.parentNode)for(var v=0;v<m.length;v++)if(m[v].node==g){e.display.currentWheelTarget=g;break e}if(i&&!r&&!f&&null!=a)return o&&p&&vi(e,Math.max(0,c.scrollTop+o*a)),bi(e,Math.max(0,c.scrollLeft+i*a)),(!o||o&&p)&&Te(t),void(l.wheelStartX=null);if(o&&null!=a){var y=o*a,w=e.doc.scrollTop,x=w+l.wrapper.clientHeight;y<0?w=Math.max(0,w+y-50):x=Math.min(e.doc.height,x+y+50),$i(e,{top:w,bottom:x})}io<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=c.scrollLeft,l.wheelStartY=c.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var e=c.scrollLeft-l.wheelStartX,t=c.scrollTop-l.wheelStartY,r=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,r&&(oo=(oo*io+r)/(io+1),++io)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}a?oo=-.53:r?oo=15:u?oo=-.7:d&&(oo=-1/3);var co=function(e,t){this.ranges=e,this.primIndex=t};co.prototype.primary=function(){return this.ranges[this.primIndex]},co.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!ft(r.anchor,n.anchor)||!ft(r.head,n.head))return!1}return!0},co.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new uo(dt(this.ranges[t].anchor),dt(this.ranges[t].head));return new co(e,this.primIndex)},co.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},co.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(ht(t,n.from())>=0&&ht(e,n.to())<=0)return r}return-1};var uo=function(e,t){this.anchor=e,this.head=t};function ho(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort((function(e,t){return ht(e.from(),t.from())})),r=K(t,i);for(var o=1;o<t.length;o++){var a=t[o],l=t[o-1],s=ht(l.to(),a.from());if(n&&!a.empty()?s>0:s>=0){var c=gt(l.from(),a.from()),u=pt(l.to(),a.to()),h=l.empty()?a.from()==a.head:l.from()==l.head;o<=r&&--r,t.splice(--o,2,new uo(h?u:c,h?c:u))}}return new co(t,r)}function fo(e,t){return new co([new uo(e,t||e)],0)}function po(e){return e.text?ut(e.from.line+e.text.length-1,ee(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function go(e,t){if(ht(e,t.from)<0)return e;if(ht(e,t.to)<=0)return po(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=po(t).ch-t.to.ch),ut(r,n)}function mo(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new uo(go(i.anchor,t),go(i.head,t)))}return ho(e.cm,r,e.sel.primIndex)}function vo(e,t,r){return e.line==t.line?ut(r.line,e.ch-t.ch+r.ch):ut(r.line+(e.line-t.line),e.ch)}function yo(e,t,r){for(var n=[],i=ut(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=vo(l.from,i,o),c=vo(po(l),i,o);if(i=l.to,o=c,"around"==r){var u=e.sel.ranges[a],h=ht(u.head,u.anchor)<0;n[a]=new uo(h?c:s,h?s:c)}else n[a]=new uo(s,s)}return new co(n,e.sel.primIndex)}function bo(e){e.doc.mode=Xe(e.options,e.doc.modeOption),wo(e)}function wo(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Ri(e,100),e.state.modeGen++,e.curOp&&jn(e)}function xo(e,t){return 0==t.from.ch&&0==t.to.ch&&""==ee(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function ko(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){pr(e,r,i,n),Fr(e,"change",e,t)}function a(e,t){for(var r=[],o=e;o<t;++o)r.push(new dr(c[o],i(o),n));return r}var l=t.from,s=t.to,c=t.text,u=rt(e,l.line),h=rt(e,s.line),f=ee(c),d=i(c.length-1),p=s.line-l.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(xo(e,t)){var g=a(0,c.length-1);o(h,h.text,d),p&&e.remove(l.line,p),g.length&&e.insert(l.line,g)}else if(u==h)if(1==c.length)o(u,u.text.slice(0,l.ch)+f+u.text.slice(s.ch),d);else{var m=a(1,c.length-1);m.push(new dr(f+u.text.slice(s.ch),d,n)),o(u,u.text.slice(0,l.ch)+c[0],i(0)),e.insert(l.line+1,m)}else if(1==c.length)o(u,u.text.slice(0,l.ch)+c[0]+h.text.slice(s.ch),i(0)),e.remove(l.line+1,p);else{o(u,u.text.slice(0,l.ch)+c[0],i(0)),o(h,f+h.text.slice(s.ch),d);var v=a(1,c.length-1);p>1&&e.remove(l.line+1,p-1),e.insert(l.line+1,v)}Fr(e,"change",e,t)}function Co(e,t,r){function n(e,i,o){if(e.linked)for(var a=0;a<e.linked.length;++a){var l=e.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;r&&!s||(t(l.doc,s),n(l.doc,e,s))}}}n(e,null,!0)}function So(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Hn(e),bo(e),Mo(e),e.options.direction=t.direction,e.options.lineWrapping||fr(e),e.options.mode=t.modeOption,jn(e)}function Mo(e){("rtl"==e.doc.direction?E:T)(e.display.lineDiv,"CodeMirror-rtl")}function Lo(e){Pi(e,(function(){Mo(e),jn(e)}))}function To(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Do(e,t){var r={from:dt(t.from),to:po(t),text:nt(e,t.from,t.to)};return Wo(e,r,t.from.line,t.to.line+1),Co(e,(function(e){return Wo(e,r,t.from.line,t.to.line+1)}),!0),r}function Ao(e){for(;e.length&&ee(e).ranges;)e.pop()}function Oo(e,t){return t?(Ao(e.done),ee(e.done)):e.done.length&&!ee(e.done).ranges?ee(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),ee(e.done)):void 0}function No(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>l-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=Oo(i,i.lastOp==n)))a=ee(o.changes),0==ht(t.from,t.to)&&0==ht(t.from,a.to)?a.to=po(t):o.changes.push(Do(e,t));else{var s=ee(i.done);for(s&&s.ranges||Eo(e.sel,i.done),o={changes:[Do(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||ke(e,"historyAdded")}function _o(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Fo(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||_o(e,o,ee(i.done),t))?i.done[i.done.length-1]=t:Eo(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&Ao(i.undone)}function Eo(e,t){var r=ee(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Wo(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function Po(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function zo(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(Po(r[i]));return n}function Io(e,t){var r=zo(e,t),n=Ut(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else a&&(r[i]=a)}return r}function Ho(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?co.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];n.push({changes:l});for(var s=0;s<a.length;++s){var c=a[s],u=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),t)for(var h in c)(u=h.match(/^spans_(\d+)$/))&&K(t,Number(u[1]))>-1&&(ee(l)[h]=c[h],delete c[h])}}}return n}function Ro(e,t,r,n){if(n){var i=e.anchor;if(r){var o=ht(t,i)<0;o!=ht(r,i)<0?(i=t,t=r):o!=ht(t,r)<0&&(t=r)}return new uo(i,t)}return new uo(r||t,t)}function Bo(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),qo(e,new co([Ro(e.sel.primary(),t,r,i)],0),n)}function jo(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Ro(e.sel.ranges[o],t[o],null,i);qo(e,ho(e.cm,n,e.sel.primIndex),r)}function Vo(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,qo(e,ho(e.cm,i,e.sel.primIndex),n)}function Uo(e,t,r,n){qo(e,fo(t,r),n)}function Ko(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new uo(vt(e,t[r].anchor),vt(e,t[r].head))},origin:r&&r.origin};return ke(e,"beforeSelectionChange",e,n),e.cm&&ke(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?ho(e.cm,n.ranges,n.ranges.length-1):t}function Go(e,t,r){var n=e.history.done,i=ee(n);i&&i.ranges?(n[n.length-1]=t,$o(e,t,r)):qo(e,t,r)}function qo(e,t,r){$o(e,t,r),Fo(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function $o(e,t,r){(Me(e,"beforeSelectionChange")||e.cm&&Me(e.cm,"beforeSelectionChange"))&&(t=Ko(e,t,r));var n=r&&r.bias||(ht(t.primary().head,e.sel.primary().head)<0?-1:1);Xo(e,Zo(e,t,n,!0)),r&&!1===r.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||fi(e.cm)}function Xo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Se(e.cm)),Fr(e,"cursorActivity",e))}function Yo(e){Xo(e,Zo(e,e.sel,null,!1))}function Zo(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Jo(e,a.anchor,l&&l.anchor,r,n),c=a.head==a.anchor?s:Jo(e,a.head,l&&l.head,r,n);(i||s!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new uo(s,c))}return i?ho(e.cm,i,t.primIndex):t}function Qo(e,t,r,n,i){var o=rt(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker,c="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,u="selectRight"in s?!s.selectRight:s.inclusiveRight;if((null==l.from||(c?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(u?l.to>=t.ch:l.to>t.ch))){if(i&&(ke(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(r){var h=s.find(n<0?1:-1),f=void 0;if((n<0?u:c)&&(h=ea(e,h,-n,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(f=ht(h,r))&&(n<0?f<0:f>0))return Qo(e,h,t,n,i)}var d=s.find(n<0?-1:1);return(n<0?c:u)&&(d=ea(e,d,n,d.line==t.line?o:null)),d?Qo(e,d,t,n,i):null}}return t}function Jo(e,t,r,n,i){var o=n||1,a=Qo(e,t,r,o,i)||!i&&Qo(e,t,r,o,!0)||Qo(e,t,r,-o,i)||!i&&Qo(e,t,r,-o,!0);return a||(e.cantEdit=!0,ut(e.first,0))}function ea(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?vt(e,ut(t.line-1)):null:r>0&&t.ch==(n||rt(e,t.line)).text.length?t.line<e.first+e.size-1?ut(t.line+1,0):null:new ut(t.line,t.ch+r)}function ta(e){e.setSelection(ut(e.firstLine(),0),ut(e.lastLine()),$)}function ra(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=vt(e,t)),r&&(n.to=vt(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),ke(e,"beforeChange",e,n),e.cm&&ke(e.cm,"beforeChange",e.cm,n),n.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function na(e,t,r){if(e.cm){if(!e.cm.curOp)return zi(e.cm,na)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(Me(e,"beforeChange")||e.cm&&Me(e.cm,"beforeChange"))||(t=ra(e,t,!0))){var n=Et&&!r&&Gt(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)ia(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else ia(e,t)}}function ia(e,t){if(1!=t.text.length||""!=t.text[0]||0!=ht(t.from,t.to)){var r=mo(e,t);No(e,t,r,e.cm?e.cm.curOp.id:NaN),la(e,t,r,Ut(e,t));var n=[];Co(e,(function(e,r){r||-1!=K(n,e.history)||(fa(e.history,t),n.push(e.history)),la(e,t,null,Ut(e,t))}))}}function oa(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,a=e.sel,l="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,c=0;c<l.length&&(i=l[c],r?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=l.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=l.pop()).ranges){if(n)return void l.push(i);break}if(Eo(i,s),r&&!i.equals(e.sel))return void qo(e,i,{clearRedo:!1});a=i}var u=[];Eo(a,s),s.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var h=Me(e,"beforeChange")||e.cm&&Me(e.cm,"beforeChange"),f=function(r){var n=i.changes[r];if(n.origin=t,h&&!ra(e,n,!1))return l.length=0,{};u.push(Do(e,n));var o=r?mo(e,n):ee(l);la(e,n,o,Io(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:po(n)});var a=[];Co(e,(function(e,t){t||-1!=K(a,e.history)||(fa(e.history,n),a.push(e.history)),la(e,n,null,Io(e,n))}))},d=i.changes.length-1;d>=0;--d){var p=f(d);if(p)return p.v}}}}function aa(e,t){if(0!=t&&(e.first+=t,e.sel=new co(te(e.sel.ranges,(function(e){return new uo(ut(e.anchor.line+t,e.anchor.ch),ut(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){jn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Vn(e.cm,n,"gutter")}}function la(e,t,r,n){if(e.cm&&!e.cm.curOp)return zi(e.cm,la)(e,t,r,n);if(t.to.line<e.first)aa(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);aa(e,i),t={from:ut(e.first,0),to:ut(t.to.line+i,t.to.ch),text:[ee(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ut(o,rt(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=nt(e,t.from,t.to),r||(r=mo(e,t)),e.cm?sa(e.cm,t,n):ko(e,t,n),$o(e,r,$),e.cantEdit&&Jo(e,ut(e.firstLine(),0))&&(e.cantEdit=!1)}}function sa(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=at(nr(rt(n,o.line))),n.iter(s,a.line+1,(function(e){if(e==i.maxLine)return l=!0,!0}))),n.sel.contains(t.from,t.to)>-1&&Se(e),ko(n,t,r,In(e)),e.options.lineWrapping||(n.iter(s,o.line+t.text.length,(function(e){var t=hr(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)})),l&&(e.curOp.updateMaxLine=!0)),Ft(n,o.line),Ri(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?jn(e):o.line!=a.line||1!=t.text.length||xo(e.doc,t)?jn(e,o.line,a.line+1,c):Vn(e,o.line,"text");var u=Me(e,"changes"),h=Me(e,"change");if(h||u){var f={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};h&&Fr(e,"change",e,f),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}function ca(e,t,r,n,i){var o;n||(n=r),ht(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),na(e,{from:r,to:n,text:t,origin:i})}function ua(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function ha(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)ua(o.ranges[l].anchor,t,r,n),ua(o.ranges[l].head,t,r,n)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(r<c.from.line)c.from=ut(c.from.line+n,c.from.ch),c.to=ut(c.to.line+n,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function fa(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;ha(e.done,r,n,i),ha(e.undone,r,n,i)}function da(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=rt(e,mt(e,t)):i=at(t),null==i?null:(n(o,i)&&e.cm&&Vn(e.cm,i,r),o)}function pa(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function ga(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}uo.prototype.from=function(){return gt(this.anchor,this.head)},uo.prototype.to=function(){return pt(this.anchor,this.head)},uo.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},pa.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,gr(i),Fr(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},ga.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),a=n.height;if(n.removeInner(e,o),this.height-=a-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof pa))){var l=[];this.collapse(l),this.children=[new pa(l)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,l=a;l<i.lines.length;){var s=new pa(i.lines.slice(l,l+=25));i.height-=s.height,this.children.splice(++n,0,s),s.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new ga(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=K(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new ga(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var ma=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function va(e,t,r){ur(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&hi(e,r)}function ya(e,t,r,n){var i=new ma(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),da(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!sr(e,t)){var n=ur(t)<e.scrollTop;ot(t,t.height+qr(i)),n&&hi(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&Fr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:at(t)),i}ma.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=at(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=qr(this);ot(r,Math.max(0,r.height-o)),e&&(Pi(e,(function(){va(e,r,-o),Vn(e,n,"widget")})),Fr(e,"lineWidgetCleared",e,this,n))}},ma.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=qr(this)-t;i&&(sr(this.doc,n)||ot(n,n.height+i),r&&Pi(r,(function(){r.curOp.forceUpdate=!0,va(r,n,i),Fr(r,"lineWidgetChanged",r,e,at(n))})))},Le(ma);var ba=0,wa=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++ba};function xa(e,t,r,n,i){if(n&&n.shared)return Ca(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return zi(e.cm,xa)(e,t,r,n,i);var o=new wa(e,i),a=ht(t,r);if(n&&j(n,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=N("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(rr(e,t.line,t,r,o)||t.line!=r.line&&rr(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");zt()}o.addToHistory&&No(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var l,s=t.line,c=e.cm;if(e.iter(s,r.line+1,(function(n){c&&o.collapsed&&!c.options.lineWrapping&&nr(n)==c.display.maxLine&&(l=!0),o.collapsed&&s!=t.line&&ot(n,0),Bt(n,new It(o,s==t.line?t.ch:null,s==r.line?r.ch:null),e.cm&&e.cm.curOp),++s})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){sr(e,t)&&ot(t,0)})),o.clearOnEnter&&be(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Pt(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++ba,o.atomic=!0),c){if(l&&(c.curOp.updateMaxLine=!0),o.collapsed)jn(c,t.line,r.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=r.line;u++)Vn(c,u,"text");o.atomic&&Yo(c.doc),Fr(c,"markerAdded",c,o)}return o}wa.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Di(e),Me(this,"clear")){var r=this.find();r&&Fr(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],l=Ht(a.markedSpans,this);e&&!this.collapsed?Vn(e,at(a),"text"):e&&(null!=l.to&&(i=at(a)),null!=l.from&&(n=at(a))),a.markedSpans=Rt(a.markedSpans,l),null==l.from&&this.collapsed&&!sr(this.doc,a)&&e&&ot(a,En(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var c=nr(this.lines[s]),u=hr(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&jn(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Yo(e.doc)),e&&Fr(e,"markerCleared",e,this,n,i),t&&Ai(e),this.parent&&this.parent.clear()}},wa.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=Ht(o.markedSpans,this);if(null!=a.from&&(r=ut(t?o:at(o),a.from),-1==e))return r;if(null!=a.to&&(n=ut(t?o:at(o),a.to),1==e))return n}return r&&{from:r,to:n}},wa.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&Pi(n,(function(){var i=t.line,o=at(t.line),a=an(n,o);if(a&&(gn(a),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!sr(r.doc,i)&&null!=r.height){var l=r.height;r.height=null;var s=qr(r)-l;s&&ot(i,i.height+s)}Fr(n,"markerChanged",n,e)}))},wa.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=K(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},wa.prototype.detachLine=function(e){if(this.lines.splice(K(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Le(wa);var ka=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function Ca(e,t,r,n,i){(n=j(n)).shared=!1;var o=[xa(e,t,r,n,i)],a=o[0],l=n.widgetNode;return Co(e,(function(e){l&&(n.widgetNode=l.cloneNode(!0)),o.push(xa(e,vt(e,t),vt(e,r),n,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=ee(o)})),new ka(o,a)}function Sa(e){return e.findMarks(ut(e.first,0),e.clipPos(ut(e.lastLine())),(function(e){return e.parent}))}function Ma(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(ht(o,a)){var l=xa(e,o,a,n.primary,n.primary.type);n.markers.push(l),l.parent=n}}}function La(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];Co(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==K(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}ka.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Fr(this,"clear")}},ka.prototype.find=function(e,t){return this.primary.find(e,t)},Le(ka);var Ta=0,Da=function(e,t,r,n,i){if(!(this instanceof Da))return new Da(e,t,r,n,i);null==r&&(r=0),ga.call(this,[new pa([new dr("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=ut(r,0);this.sel=fo(o),this.history=new To(null),this.id=++Ta,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),ko(this,{from:o,to:o,text:e}),qo(this,fo(o),$)};Da.prototype=ie(ga.prototype,{constructor:Da,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=it(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Hi((function(e){var t=ut(this.first,0),r=this.first+this.size-1;na(this,{from:t,to:ut(r,rt(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&di(this.cm,0,0),qo(this,fo(t),$)})),replaceRange:function(e,t,r,n){ca(this,e,t=vt(this,t),r=r?vt(this,r):t,n)},getRange:function(e,t,r){var n=nt(this,vt(this,e),vt(this,t));return!1===r?n:""===r?n.join(""):n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(st(this,e))return rt(this,e)},getLineNumber:function(e){return at(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=rt(this,e)),nr(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return vt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Hi((function(e,t,r){Uo(this,vt(this,"number"==typeof e?ut(e,t||0):e),null,r)})),setSelection:Hi((function(e,t,r){Uo(this,vt(this,e),vt(this,t||e),r)})),extendSelection:Hi((function(e,t,r){Bo(this,vt(this,e),t&&vt(this,t),r)})),extendSelections:Hi((function(e,t){jo(this,bt(this,e),t)})),extendSelectionsBy:Hi((function(e,t){jo(this,bt(this,te(this.sel.ranges,e)),t)})),setSelections:Hi((function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new uo(vt(this,e[i].anchor),vt(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),qo(this,ho(this.cm,n,t),r)}})),addSelection:Hi((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new uo(vt(this,e),vt(this,t||e))),qo(this,ho(this.cm,n,n.length-1),r)})),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=nt(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=nt(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Hi((function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var l=t&&"end"!=t&&yo(this,n,t),s=n.length-1;s>=0;s--)na(this,n[s]);l?Go(this,l):this.cm&&fi(this.cm)})),undo:Hi((function(){oa(this,"undo")})),redo:Hi((function(){oa(this,"redo")})),undoSelection:Hi((function(){oa(this,"undo",!0)})),redoSelection:Hi((function(){oa(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){var e=this;this.history=new To(this.history),Co(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Ho(this.history.done),undone:Ho(this.history.undone)}},setHistory:function(e){var t=this.history=new To(this.history);t.done=Ho(e.done.slice(0),null,!0),t.undone=Ho(e.undone.slice(0),null,!0)},setGutterMarker:Hi((function(e,t,r){return da(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&se(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:Hi((function(e){var t=this;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&da(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,se(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!st(this,e))return null;if(t=e,!(e=rt(this,e)))return null}else if(null==(t=at(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Hi((function(e,t,r){return da(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(M(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:Hi((function(e,t,r){return da(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(M(r));if(!o)return!1;var a=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0}))})),addLineWidget:Hi((function(e,t,r){return ya(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return xa(this,vt(this,e),vt(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return xa(this,e=vt(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=rt(this,(e=vt(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=vt(this,e),t=vt(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||r&&!r(s.marker)||n.push(s.marker.parent||s.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),vt(this,ut(r,t))},indexFromPos:function(e){var t=(e=vt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new Da(it(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new Da(it(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Ma(n,Sa(this)),n},unlinkDoc:function(e){if(e instanceof jl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),La(Sa(this));break}if(e.history==this.history){var r=[e.id];Co(e,(function(e){return r.push(e.id)}),!0),e.history=new To(null),e.history.done=Ho(this.history.done,r),e.history.undone=Ho(this.history.undone,r)}},iterLinkedDocs:function(e){Co(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):He(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Hi((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&Lo(this.cm))}))}),Da.prototype.eachLine=Da.prototype.iter;var Aa=0;function Oa(e){var t=this;if(Fa(t),!Ce(t,e)&&!$r(t.display,e)){Te(e),a&&(Aa=+new Date);var r=Rn(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(){++l==i&&zi(t,(function(){var e={from:r=vt(t.doc,r),to:r,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};na(t.doc,e),Go(t.doc,fo(vt(t.doc,r),vt(t.doc,po(e))))}))()},c=function(e,r){if(t.options.allowDropFileTypes&&-1==K(t.options.allowDropFileTypes,e.type))s();else{var n=new FileReader;n.onerror=function(){return s()},n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[r]=e),s()},n.readAsText(e)}},u=0;u<n.length;u++)c(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var h=e.dataTransfer.getData("Text");if(h){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),$o(t.doc,fo(r,r)),f)for(var d=0;d<f.length;++d)ca(t.doc,"",f[d].anchor,f[d].head,"drag");t.replaceSelection(h,"around","paste"),t.display.input.focus()}}catch(p){}}}}function Na(e,t){if(a&&(!e.state.draggingText||+new Date-Aa<100))Oe(t);else if(!Ce(e,t)&&!$r(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!d)){var r=O("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",f&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),f&&r.parentNode.removeChild(r)}}function _a(e,t){var r=Rn(e,t);if(r){var n=document.createDocumentFragment();Yn(e,r,n),e.display.dragCursor||(e.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),A(e.display.dragCursor,n)}}function Fa(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Ea(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation((function(){for(var t=0;t<r.length;t++)e(r[t])}))}}var Wa=!1;function Pa(){Wa||(za(),Wa=!0)}function za(){var e;be(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Ea(Ia)}),100))})),be(window,"blur",(function(){return Ea(ni)}))}function Ia(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Ha={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Ra=0;Ra<10;Ra++)Ha[Ra+48]=Ha[Ra+96]=String(Ra);for(var Ba=65;Ba<=90;Ba++)Ha[Ba]=String.fromCharCode(Ba);for(var ja=1;ja<=12;ja++)Ha[ja+111]=Ha[ja+63235]="F"+ja;var Va={};function Ua(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Ka(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=te(r.split(" "),Ua),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=n):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[r]}for(var c in t)e[c]=t[c];return e}function Ga(e,t,r,n){var i=(t=Ya(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Ga(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=Ga(e,t.fallthrough[o],r,n);if(a)return a}}}function qa(e){var t="string"==typeof e?e:Ha[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function $a(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(C?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(C?t.ctrlKey:t.metaKey)&&"Mod"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Xa(e,t){if(f&&34==e.keyCode&&e.char)return!1;var r=Ha[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),$a(r,e,t))}function Ya(e){return"string"==typeof e?Va[e]:e}function Za(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&ht(o.from,ee(n).to)<=0;){var a=n.pop();if(ht(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}Pi(e,(function(){for(var t=n.length-1;t>=0;t--)ca(e.doc,"",n[t].from,n[t].to,"+delete");fi(e)}))}function Qa(e,t,r){var n=he(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Ja(e,t,r){var n=Qa(e,t.ch,r);return null==n?null:new ut(t.line,n,r<0?"after":"before")}function el(e,t,r,n,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=ve(r,t.doc.direction);if(o){var a,l=i<0?ee(o):o[0],s=i<0==(1==l.level)?"after":"before";if(l.level>0||"rtl"==t.doc.direction){var c=ln(t,r);a=i<0?r.text.length-1:0;var u=sn(t,c,a).top;a=fe((function(e){return sn(t,c,e).top==u}),i<0==(1==l.level)?l.from:l.to-1,a),"before"==s&&(a=Qa(r,a,1))}else a=i<0?l.to:l.from;return new ut(n,a,s)}}return new ut(n,i<0?r.text.length:0,i<0?"before":"after")}function tl(e,t,r,n){var i=ve(t,e.doc.direction);if(!i)return Ja(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=ge(i,r.ch,r.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(n>0?a.to>r.ch:a.from<r.ch))return Ja(t,r,n);var l,s=function(e,r){return Qa(t,e instanceof ut?e.ch:e,r)},c=function(r){return e.options.lineWrapping?(l=l||ln(e,t),An(e,t,l,r)):{begin:0,end:t.text.length}},u=c("before"==r.sticky?s(r,-1):r.ch);if("rtl"==e.doc.direction||1==a.level){var h=1==a.level==n<0,f=s(r,h?1:-1);if(null!=f&&(h?f<=a.to&&f<=u.end:f>=a.from&&f>=u.begin)){var d=h?"before":"after";return new ut(r.line,f,d)}}var p=function(e,t,n){for(var o=function(e,t){return t?new ut(r.line,s(e,1),"before"):new ut(r.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],l=t>0==(1!=a.level),c=l?n.begin:s(n.end,-1);if(a.from<=c&&c<a.to)return o(c,l);if(c=l?a.from:s(a.to,-1),n.begin<=c&&c<n.end)return o(c,l)}},g=p(o+n,n,u);if(g)return g;var m=n>0?u.end:s(u.begin,-1);return null==m||n>0&&m==t.text.length||!(g=p(n>0?0:i.length-1,n,c(m)))?null:g}Va.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Va.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Va.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Va.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Va.default=b?Va.macDefault:Va.pcDefault;var rl={selectAll:ta,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),$)},killLine:function(e){return Za(e,(function(t){if(t.empty()){var r=rt(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:ut(t.head.line+1,0)}:{from:t.head,to:ut(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Za(e,(function(t){return{from:ut(t.from().line,0),to:vt(e.doc,ut(t.to().line+1,0))}}))},delLineLeft:function(e){return Za(e,(function(e){return{from:ut(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Za(e,(function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Za(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ut(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ut(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return nl(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return ol(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return il(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),Y)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),Y)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?ol(e,t.head):n}),Y)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=V(e.getLine(o.line),o.ch,n);t.push(J(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Pi(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=rt(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ut(i.line,i.ch-1)),i.ch>0)i=new ut(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ut(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=rt(e.doc,i.line-1).text;a&&(i=new ut(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),ut(i.line-1,a.length-1),i,"+transpose"))}r.push(new uo(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return Pi(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);fi(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function nl(e,t){var r=rt(e.doc,t),n=nr(r);return n!=r&&(t=at(n)),el(!0,e,n,t,1)}function il(e,t){var r=rt(e.doc,t),n=ir(r);return n!=r&&(t=at(n)),el(!0,e,r,t,-1)}function ol(e,t){var r=nl(e,t.line),n=rt(e.doc,r.line),i=ve(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(r.ch,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return ut(r.line,a?0:o,r.sticky)}return r}function al(e,t,r){if("string"==typeof t&&!(t=rl[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=q}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function ll(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Ga(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Ga(t,e.options.extraKeys,r,e)||Ga(t,e.options.keyMap,r,e)}var sl=new U;function cl(e,t,r,n){var i=e.state.keySeq;if(i){if(qa(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:sl.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),ul(e,i+" "+t,r,n))return!0}return ul(e,t,r,n)}function ul(e,t,r,n){var i=ll(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&Fr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(Te(r),Jn(e)),!!i}function hl(e,t){var r=Xa(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?cl(e,"Shift-"+r,t,(function(t){return al(e,t,!0)}))||cl(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return al(e,t)})):cl(e,r,t,(function(t){return al(e,t)})))}function fl(e,t,r){return cl(e,"'"+r+"'",t,(function(t){return al(e,t,!0)}))}var dl=null;function pl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=F(I(t)),Ce(t,e)))){a&&l<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var i=hl(t,e);f&&(dl=i?n:null,i||88!=n||Be||!(b?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),r&&!b&&!i&&46==n&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||gl(t)}}function gl(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),xe(document,"keyup",r),xe(document,"mouseover",r))}E(t,"CodeMirror-crosshair"),be(document,"keyup",r),be(document,"mouseover",r)}function ml(e){16==e.keyCode&&(this.doc.sel.shift=!1),Ce(this,e)}function vl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||$r(t.display,e)||Ce(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(f&&r==dl)return dl=null,void Te(e);if(!f||e.which&&!(e.which<10)||!hl(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(fl(t,e,i)||t.display.input.onKeyPress(e))}}}var yl,bl,wl=400,xl=function(e,t,r){this.time=e,this.pos=t,this.button=r};function kl(e,t){var r=+new Date;return bl&&bl.compare(r,e,t)?(yl=bl=null,"triple"):yl&&yl.compare(r,e,t)?(bl=new xl(r,e,t),yl=null,"double"):(yl=new xl(r,e,t),bl=null,"single")}function Cl(e){var t=this,r=t.display;if(!(Ce(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,$r(r,e))s||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!_l(t,e)){var n=Rn(t,e),i=_e(e),o=n?kl(n,i):"single";R(t).focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&Sl(t,i,n,o,e)||(1==i?n?Ll(t,n,o,e):Ne(e)==r.scroller&&Te(e):2==i?(n&&Bo(t.doc,n),setTimeout((function(){return r.input.focus()}),20)):3==i&&(S?t.display.input.onContextMenu(e):ti(t)))}}function Sl(e,t,r,n,i){var o="Click";return"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o),cl(e,$a(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,(function(t){if("string"==typeof t&&(t=rl[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=q}finally{e.state.suppressEdits=!1}return n}))}function Ml(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=w?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey),null==i.addNew&&(i.addNew=b?r.metaKey:r.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?r.altKey:r.ctrlKey)),i}function Ll(e,t,r,n){a?setTimeout(B(ei,e),0):e.curOp.focus=F(I(e));var i,o=Ml(e,r,n),l=e.doc.sel;e.options.dragDrop&&We&&!e.isReadOnly()&&"single"==r&&(i=l.contains(t))>-1&&(ht((i=l.ranges[i]).from(),t)<0||t.xRel>0)&&(ht(i.to(),t)>0||t.xRel<0)?Tl(e,n,t,o):Al(e,n,t,o)}function Tl(e,t,r,n){var i=e.display,o=!1,c=zi(e,(function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:ti(e)),xe(i.wrapper.ownerDocument,"mouseup",c),xe(i.wrapper.ownerDocument,"mousemove",u),xe(i.scroller,"dragstart",h),xe(i.scroller,"drop",c),o||(Te(t),n.addNew||Bo(e.doc,r,null,null,n.extend),s&&!d||a&&9==l?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},h=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=c,c.copy=!n.moveOnDrag,be(i.wrapper.ownerDocument,"mouseup",c),be(i.wrapper.ownerDocument,"mousemove",u),be(i.scroller,"dragstart",h),be(i.scroller,"drop",c),e.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Dl(e,t,r){if("char"==r)return new uo(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new uo(ut(t.line,0),vt(e.doc,ut(t.line+1,0)));var n=r(e,t);return new uo(n.from,n.to)}function Al(e,t,r,n){a&&ti(e);var i=e.display,o=e.doc;Te(t);var l,s,c=o.sel,u=c.ranges;if(n.addNew&&!n.extend?(s=o.sel.contains(r),l=s>-1?u[s]:new uo(r,r)):(l=o.sel.primary(),s=o.sel.primIndex),"rectangle"==n.unit)n.addNew||(l=new uo(r,r)),r=Rn(e,t,!0,!0),s=-1;else{var h=Dl(e,r,n.unit);l=n.extend?Ro(l,h.anchor,h.head,n.extend):h}n.addNew?-1==s?(s=u.length,qo(o,ho(e,u.concat([l]),s),{scroll:!1,origin:"*mouse"})):u.length>1&&u[s].empty()&&"char"==n.unit&&!n.extend?(qo(o,ho(e,u.slice(0,s).concat(u.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):Vo(o,s,l,X):(s=0,qo(o,new co([l],0),X),c=o.sel);var f=r;function d(t){if(0!=ht(f,t))if(f=t,"rectangle"==n.unit){for(var i=[],a=e.options.tabSize,u=V(rt(o,r.line).text,r.ch,a),h=V(rt(o,t.line).text,t.ch,a),d=Math.min(u,h),p=Math.max(u,h),g=Math.min(r.line,t.line),m=Math.min(e.lastLine(),Math.max(r.line,t.line));g<=m;g++){var v=rt(o,g).text,y=Z(v,d,a);d==p?i.push(new uo(ut(g,y),ut(g,y))):v.length>y&&i.push(new uo(ut(g,y),ut(g,Z(v,p,a))))}i.length||i.push(new uo(r,r)),qo(o,ho(e,c.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=l,x=Dl(e,t,n.unit),k=w.anchor;ht(x.anchor,k)>0?(b=x.head,k=gt(w.from(),x.anchor)):(b=x.anchor,k=pt(w.to(),x.head));var C=c.ranges.slice(0);C[s]=Ol(e,new uo(vt(o,k),b)),qo(o,ho(e,C,s),X)}}var p=i.wrapper.getBoundingClientRect(),g=0;function m(t){var r=++g,a=Rn(e,t,!0,"rectangle"==n.unit);if(a)if(0!=ht(a,f)){e.curOp.focus=F(I(e)),d(a);var l=ai(i,o);(a.line>=l.to||a.line<l.from)&&setTimeout(zi(e,(function(){g==r&&m(t)})),150)}else{var s=t.clientY<p.top?-20:t.clientY>p.bottom?20:0;s&&setTimeout(zi(e,(function(){g==r&&(i.scroller.scrollTop+=s,m(t))})),50)}}function v(t){e.state.selectingText=!1,g=1/0,t&&(Te(t),i.input.focus()),xe(i.wrapper.ownerDocument,"mousemove",y),xe(i.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=zi(e,(function(e){0!==e.buttons&&_e(e)?m(e):v(e)})),b=zi(e,v);e.state.selectingText=b,be(i.wrapper.ownerDocument,"mousemove",y),be(i.wrapper.ownerDocument,"mouseup",b)}function Ol(e,t){var r=t.anchor,n=t.head,i=rt(e.doc,r.line);if(0==ht(r,n)&&r.sticky==n.sticky)return t;var o=ve(i);if(!o)return t;var a=ge(o,r.ch,r.sticky),l=o[a];if(l.from!=r.ch&&l.to!=r.ch)return t;var s,c=a+(l.from==r.ch==(1!=l.level)?0:1);if(0==c||c==o.length)return t;if(n.line!=r.line)s=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=ge(o,n.ch,n.sticky),h=u-a||(n.ch-r.ch)*(1==l.level?-1:1);s=u==c-1||u==c?h<0:h>0}var f=o[c+(s?-1:0)],d=s==(1==f.level),p=d?f.from:f.to,g=d?"after":"before";return r.ch==p&&r.sticky==g?t:new uo(new ut(r.line,p,g),n)}function Nl(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(u){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&Te(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!Me(e,r))return Ae(t);o-=l.top-a.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var c=a.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i)return ke(e,r,e,lt(e.doc,o),e.display.gutterSpecs[s].className,t),Ae(t)}}function _l(e,t){return Nl(e,t,"gutterClick",!0)}function Fl(e,t){$r(e.display,t)||El(e,t)||Ce(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function El(e,t){return!!Me(e,"gutterContextMenu")&&Nl(e,t,"gutterContextMenu",!1)}function Wl(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),vn(e)}xl.prototype.compare=function(e,t,r){return this.time+wl>e&&0==ht(t,this.pos)&&r==this.button};var Pl={toString:function(){return"CodeMirror.Init"}},zl={},Il={};function Hl(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=Pl&&i(e,t,r)}:i)}e.defineOption=r,e.Init=Pl,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,bo(e)}),!0),r("indentUnit",2,bo,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){wo(e),vn(e),jn(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(ut(n,o))}n++}));for(var i=r.length-1;i>=0;i--)ca(e.doc,t,r[i],ut(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=Pl&&e.refresh()})),r("specialCharPlaceholder",wr,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),r("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),r("rtlMoveVisually",!x),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){Wl(e),ro(e)}),!0),r("keyMap","default",(function(e,t,r){var n=Ya(t),i=r!=Pl&&Ya(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("configureMouse",null),r("lineWrapping",!1,Bl,!0),r("gutters",[],(function(e,t){e.display.gutterSpecs=eo(t,e.options.lineNumbers),ro(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?zn(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return Ci(e)}),!0),r("scrollbarStyle","native",(function(e){Li(e),Ci(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=eo(e.options.gutters,t),ro(e)}),!0),r("firstLineNumber",1,ro,!0),r("lineNumberFormatter",(function(e){return e}),ro,!0),r("showCursorWhenSelecting",!1,$n,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("pasteLinesPerSelection",!0),r("selectionsMayTouch",!1),r("readOnly",!1,(function(e,t){"nocursor"==t&&(ni(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),r("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,Rl),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,$n,!0),r("singleCursorHeightPerLine",!0,$n,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,wo,!0),r("addModeClass",!1,wo,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,wo,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null),r("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),r("phrases",null)}function Rl(e,t,r){if(!t!=!(r&&r!=Pl)){var n=e.display.dragFunctions,i=t?be:xe;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function Bl(e){e.options.lineWrapping?(E(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),fr(e)),Hn(e),jn(e),vn(e),setTimeout((function(){return Ci(e)}),100)}function jl(e,t){var r=this;if(!(this instanceof jl))return new jl(e,t);this.options=t=t?j(t):{},j(zl,t,!1);var n=t.value;"string"==typeof n?n=new Da(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new jl.inputStyles[t.inputStyle](this),o=this.display=new no(e,n,i,t);for(var c in o.wrapper.CodeMirror=this,Wl(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Li(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new U,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),a&&l<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),Vl(this),Pa(),Di(this),this.curOp.forceUpdate=!0,So(this,n),t.autofocus&&!y||this.hasFocus()?setTimeout((function(){r.hasFocus()&&!r.state.focused&&ri(r)}),20):ni(this),Il)Il.hasOwnProperty(c)&&Il[c](this,t[c],Pl);Ji(this),t.finishInit&&t.finishInit(this);for(var u=0;u<Ul.length;++u)Ul[u](this);Ai(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Vl(e){var t=e.display;be(t.scroller,"mousedown",zi(e,Cl)),be(t.scroller,"dblclick",a&&l<11?zi(e,(function(t){if(!Ce(e,t)){var r=Rn(e,t);if(r&&!_l(e,t)&&!$r(e.display,t)){Te(t);var n=e.findWordAt(r);Bo(e.doc,n.anchor,n.head)}}})):function(t){return Ce(e,t)||Te(t)}),be(t.scroller,"contextmenu",(function(t){return Fl(e,t)})),be(t.input.getField(),"contextmenu",(function(r){t.scroller.contains(r.target)||Fl(e,r)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),(n=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}be(t.scroller,"touchstart",(function(i){if(!Ce(e,i)&&!o(i)&&!_l(e,i)){t.input.ensurePolled(),clearTimeout(r);var a=+new Date;t.activeTouch={start:a,moved:!1,prev:a-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),be(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),be(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!$r(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!n.prev||s(n,n.prev)?new uo(a,a):!n.prev.prev||s(n,n.prev.prev)?e.findWordAt(a):new uo(ut(a.line,0),vt(e.doc,ut(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Te(r)}i()})),be(t.scroller,"touchcancel",i),be(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(vi(e,t.scroller.scrollTop),bi(e,t.scroller.scrollLeft,!0),ke(e,"scroll",e))})),be(t.scroller,"mousewheel",(function(t){return so(e,t)})),be(t.scroller,"DOMMouseScroll",(function(t){return so(e,t)})),be(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){Ce(e,t)||Oe(t)},over:function(t){Ce(e,t)||(_a(e,t),Oe(t))},start:function(t){return Na(e,t)},drop:zi(e,Oa),leave:function(t){Ce(e,t)||Fa(e)}};var c=t.input.getField();be(c,"keyup",(function(t){return ml.call(e,t)})),be(c,"keydown",zi(e,pl)),be(c,"keypress",zi(e,vl)),be(c,"focus",(function(t){return ri(e,t)})),be(c,"blur",(function(t){return ni(e,t)}))}jl.defaults=zl,jl.optionHandlers=Il;var Ul=[];function Kl(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=St(e,t).state:r="prev");var a=e.options.tabSize,l=rt(o,t),s=V(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var c,u=l.text.match(/^\s*/)[0];if(n||/\S/.test(l.text)){if("smart"==r&&((c=o.mode.indent(i,l.text.slice(u.length),l.text))==q||c>150)){if(!n)return;r="prev"}}else c=0,r="not";"prev"==r?c=t>o.first?V(rt(o,t-1).text,null,a):0:"add"==r?c=s+e.options.indentUnit:"subtract"==r?c=s-e.options.indentUnit:"number"==typeof r&&(c=s+r),c=Math.max(0,c);var h="",f=0;if(e.options.indentWithTabs)for(var d=Math.floor(c/a);d;--d)f+=a,h+="\t";if(f<c&&(h+=J(c-f)),h!=u)return ca(o,h,ut(t,0),ut(t,u.length),"+input"),l.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<u.length){var m=ut(t,u.length);Vo(o,p,new uo(m,m));break}}}jl.defineInitHook=function(e){return Ul.push(e)};var Gl=null;function ql(e){Gl=e}function $l(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a=+new Date-200,l="paste"==i||e.state.pasteIncoming>a,s=He(t),c=null;if(l&&n.ranges.length>1)if(Gl&&Gl.text.join("\n")==t){if(n.ranges.length%Gl.text.length==0){c=[];for(var u=0;u<Gl.text.length;u++)c.push(o.splitLines(Gl.text[u]))}}else s.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(c=te(s,(function(e){return[e]})));for(var h=e.curOp.updateInput,f=n.ranges.length-1;f>=0;f--){var d=n.ranges[f],p=d.from(),g=d.to();d.empty()&&(r&&r>0?p=ut(p.line,p.ch-r):e.state.overwrite&&!l?g=ut(g.line,Math.min(rt(o,g.line).text.length,g.ch+ee(s).length)):l&&Gl&&Gl.lineWise&&Gl.text.join("\n")==s.join("\n")&&(p=g=ut(p.line,0)));var m={from:p,to:g,text:c?c[f%c.length]:s,origin:i||(l?"paste":e.state.cutIncoming>a?"cut":"+input")};na(e.doc,m),Fr(e,"inputRead",e,m)}t&&!l&&Yl(e,t),fi(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Xl(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||Pi(t,(function(){return $l(t,r,0,null,"paste")})),!0}function Yl(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=Kl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(rt(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Kl(e,i.head.line,"smart"));a&&Fr(e,"electricInput",e,i.head.line)}}}function Zl(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:ut(i,0),head:ut(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Ql(e,t,r,n){e.setAttribute("autocorrect",r?"on":"off"),e.setAttribute("autocapitalize",n?"on":"off"),e.setAttribute("spellcheck",!!t)}function Jl(){var e=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=O("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),m&&(e.style.border="1px solid black"),t}function es(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){R(this).focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&zi(this,t[e])(this,r,i),ke(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Ya(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Ii((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");re(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,jn(this)})),removeOverlay:Ii((function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void jn(this)}})),indentLine:Ii((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),st(this.doc,e)&&Kl(this,e,t,r)})),indentSelection:Ii((function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(Kl(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&fi(this));else{var o=i.from(),a=i.to(),l=Math.max(r,o.line);r=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var s=l;s<r;++s)Kl(this,s,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[n].from().ch>0&&Vo(this.doc,n,new uo(o,c[n].to()),$)}}})),getTokenAt:function(e,t){return At(this,e,t)},getLineTokens:function(e,t){return At(this,ut(e),t,!0)},getTokenTypeAt:function(e){e=vt(this.doc,e);var t,r=Ct(this,rt(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var l=i[o[t][a]];l&&n.push(l)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var s=0;s<i._global.length;s++){var c=i._global[s];c.pred(o,this)&&-1==K(n,c.val)&&n.push(c.val)}return n},getStateAfter:function(e,t){var r=this.doc;return St(this,(e=mt(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Sn(this,null==e?r.head:"object"==typeof e?vt(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return Cn(this,vt(this.doc,e),t||"page")},coordsChar:function(e,t){return Tn(this,(e=kn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=kn(this,{top:e,left:0},t||"page").top,lt(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=rt(this.doc,e)}else n=e;return xn(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-ur(n):0)},defaultTextHeight:function(){return En(this.display)},defaultCharWidth:function(){return Wn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display,a=(e=Sn(this,vt(this.doc,e))).bottom,l=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)a=e.top;else if("above"==n||"near"==n){var s=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>s)&&e.top>t.offsetHeight?a=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=s&&(a=e.bottom),l+t.offsetWidth>c&&(l=c-t.offsetWidth)}t.style.top=a+"px",t.style.left=t.style.right="","right"==i?(l=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=l+"px"),r&&ci(this,{left:l,top:a,right:l+t.offsetWidth,bottom:a+t.offsetHeight})},triggerOnKeyDown:Ii(pl),triggerOnKeyPress:Ii(vl),triggerOnKeyUp:ml,triggerOnMouseDown:Ii(Cl),execCommand:function(e){if(rl.hasOwnProperty(e))return rl[e].call(null,this)},triggerElectric:Ii((function(e){Yl(this,e)})),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=vt(this.doc,e),a=0;a<t&&!(o=ts(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Ii((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?ts(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),Y)})),deleteH:Ii((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Za(this,(function(r){var i=ts(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=vt(this.doc,e),l=0;l<t;++l){var s=Sn(this,a,"div");if(null==o?o=s.left:s.left=o,(a=rs(this,s,i,r)).hitSide)break}return a},moveV:Ii((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(a){if(o)return e<0?a.from():a.to();var l=Sn(r,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=rs(r,l,e,t);return"page"==t&&a==n.sel.primary()&&hi(r,Cn(r,s,"div").top-l.top),s}),Y),i.length)for(var a=0;a<n.sel.ranges.length;a++)n.sel.ranges[a].goalColumn=i[a]})),findWordAt:function(e){var t=rt(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),a=le(o,i)?function(e){return le(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!le(e)};r>0&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new uo(ut(e.line,r),ut(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?E(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),ke(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==F(I(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Ii((function(e,t){di(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Qr(this)-this.display.barHeight,width:e.scrollWidth-Qr(this)-this.display.barWidth,clientHeight:en(this),clientWidth:Jr(this)}},scrollIntoView:Ii((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ut(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?pi(this,e):mi(this,e.from,e.to,e.margin)})),setSize:Ii((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&mn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Vn(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,ke(this,"refresh",this)})),operation:function(e){return Pi(this,e)},startOperation:function(){return Di(this)},endOperation:function(){return Ai(this)},refresh:Ii((function(){var e=this.display.cachedTextHeight;jn(this),this.curOp.forceUpdate=!0,vn(this),di(this,this.doc.scrollLeft,this.doc.scrollTop),Yi(this.display),(null==e||Math.abs(e-En(this.display))>.5||this.options.lineWrapping)&&Hn(this),ke(this,"refresh",this)})),swapDoc:Ii((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),So(this,e),vn(this),this.display.input.reset(),di(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Fr(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Le(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}function ts(e,t,r,n,i){var o=t,a=r,l=rt(e,t.line),s=i&&"rtl"==e.direction?-r:r;function c(){var r=t.line+s;return!(r<e.first||r>=e.first+e.size)&&(t=new ut(r,t.ch,t.sticky),l=rt(e,r))}function u(o){var a;if("codepoint"==n){var u=l.text.charCodeAt(t.ch+(r>0?0:-1));if(isNaN(u))a=null;else{var h=r>0?u>=55296&&u<56320:u>=56320&&u<57343;a=new ut(t.line,Math.max(0,Math.min(l.text.length,t.ch+r*(h?2:1))),-r)}}else a=i?tl(e.cm,l,t,r):Ja(l,t,r);if(null==a){if(o||!c())return!1;t=el(i,e.cm,l,t.line,s)}else t=a;return!0}if("char"==n||"codepoint"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var h=null,f="group"==n,d=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;!(r<0)||u(!p);p=!1){var g=l.text.charAt(t.ch)||"\n",m=le(g,d)?"w":f&&"\n"==g?"n":!f||/\s/.test(g)?null:"p";if(!f||p||m||(m="s"),h&&h!=m){r<0&&(r=1,u(),t.sticky="after");break}if(m&&(h=m),r>0&&!u(!p))break}var v=Jo(e,t,o,a,!0);return ft(o,v)&&(v.hitSide=!0),v}function rs(e,t,r,n){var i,o,a=e.doc,l=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,R(e).innerHeight||a(e).documentElement.clientHeight),c=Math.max(s-.5*En(e.display),3);i=(r>0?t.bottom:t.top)+r*c}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=Tn(e,l,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}var ns=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new U,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function is(e,t){var r=an(e,t.line);if(!r||r.hidden)return null;var n=rt(e.doc,t.line),i=rn(r,n,t.line),o=ve(n,e.doc.direction),a="left";o&&(a=ge(o,t.ch)%2?"right":"left");var l=hn(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function os(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function as(e,t){return t&&(e.bad=!0),e}function ls(e,t,r,n,i){var o="",a=!1,l=e.doc.lineSeparator(),s=!1;function c(e){return function(t){return t.id==e}}function u(){a&&(o+=l,s&&(o+=l),a=s=!1)}function h(e){e&&(u(),o+=e)}function f(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(r)return void h(r);var o,d=t.getAttribute("cm-marker");if(d){var p=e.findMarks(ut(n,0),ut(i+1,0),c(+d));return void(p.length&&(o=p[0].find(0))&&h(nt(e.doc,o.from,o.to).join(l)))}if("false"==t.getAttribute("contenteditable"))return;var g=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;g&&u();for(var m=0;m<t.childNodes.length;m++)f(t.childNodes[m]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),g&&(a=!0)}else 3==t.nodeType&&h(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;f(t),t!=r;)t=t.nextSibling,s=!1;return o}function ss(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return as(e.clipPos(ut(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return cs(o,t,r)}}function cs(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!_(n,t))return as(ut(at(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?ee(e.rest):e.line;return as(ut(at(o),o.text.length),i)}var a=3==t.nodeType?t:null,l=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));l.parentNode!=n;)l=l.parentNode;var s=e.measure,c=s.maps;function u(t,r,n){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?s.map:c[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==r){var u=at(i<0?e.line:e.rest[i]),h=o[a]+n;return(n<0||l!=t)&&(h=o[a+(n?1:0)]),ut(u,h)}}}var h=u(a,l,r);if(h)return as(h,i);for(var f=l.nextSibling,d=a?a.nodeValue.length-r:0;f;f=f.nextSibling){if(h=u(f,f.firstChild,0))return as(ut(h.line,h.ch-d),i);d+=f.textContent.length}for(var p=l.previousSibling,g=r;p;p=p.previousSibling){if(h=u(p,p.firstChild,-1))return as(ut(h.line,h.ch+g),i);g+=p.textContent.length}}ns.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function a(e){if(o(e)&&!Ce(n,e)){if(n.somethingSelected())ql({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=Zl(n);ql({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,$),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var a=Gl.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a)return void e.preventDefault()}var l=Jl(),s=l.firstChild;Ql(s),n.display.lineSpace.insertBefore(l,n.display.lineSpace.firstChild),s.value=Gl.text.join("\n");var c=F(H(i));P(s),setTimeout((function(){n.display.lineSpace.removeChild(l),c.focus(),c==i&&r.showPrimarySelection()}),50)}}i.contentEditable=!0,Ql(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),be(i,"paste",(function(e){!o(e)||Ce(n,e)||Xl(e,n)||l<=11&&setTimeout(zi(n,(function(){return t.updateFromDOM()})),20)})),be(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),be(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),be(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),be(i,"touchstart",(function(){return r.forceCompositionEnd()})),be(i,"input",(function(){t.composing||t.readFromDOMSoon()})),be(i,"copy",a),be(i,"cut",a)},ns.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},ns.prototype.prepareSelection=function(){var e=Xn(this.cm,!1);return e.focus=F(H(this.div))==this.div,e},ns.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},ns.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},ns.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=ss(t,e.anchorNode,e.anchorOffset),l=ss(t,e.focusNode,e.focusOffset);if(!a||a.bad||!l||l.bad||0!=ht(gt(a,l),i)||0!=ht(pt(a,l),o)){var s=t.display.view,c=i.line>=t.display.viewFrom&&is(t,i)||{node:s[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&is(t,o);if(!u){var h=s[s.length-1].measure,f=h.maps?h.maps[h.maps.length-1]:h.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(c&&u){var d,p=e.rangeCount&&e.getRangeAt(0);try{d=L(c.node,c.offset,u.offset,u.node)}catch(Ie){}d&&(!r&&t.state.focused?(e.collapse(c.node,c.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&null==e.anchorNode?e.addRange(p):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},ns.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},ns.prototype.showMultipleSelections=function(e){A(this.cm.display.cursorDiv,e.cursors),A(this.cm.display.selectionDiv,e.selection)},ns.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},ns.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return _(this.div,t)},ns.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&F(H(this.div))==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},ns.prototype.blur=function(){this.div.blur()},ns.prototype.getField=function(){return this.div},ns.prototype.supportsTouch=function(){return!0},ns.prototype.receivedFocus=function(){var e=this,t=this;function r(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,r))}this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):Pi(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,r)},ns.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},ns.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&u&&this.cm.display.gutterSpecs.length&&os(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=ss(t,e.anchorNode,e.anchorOffset),n=ss(t,e.focusNode,e.focusOffset);r&&n&&Pi(t,(function(){qo(t.doc,fo(r,n),$),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}}},ns.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=ut(a.line-1,rt(n.doc,a.line-1).length)),l.ch==rt(n.doc,l.line).text.length&&l.line<n.lastLine()&&(l=ut(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=Bn(n,a.line))?(t=at(i.view[0].line),r=i.view[0].node):(t=at(i.view[e].line),r=i.view[e-1].node.nextSibling);var s,c,u=Bn(n,l.line);if(u==i.view.length-1?(s=i.viewTo-1,c=i.lineDiv.lastChild):(s=at(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!r)return!1;for(var h=n.doc.splitLines(ls(n,r,c,t,s)),f=nt(n.doc,ut(t,0),ut(s,rt(n.doc,s).text.length));h.length>1&&f.length>1;)if(ee(h)==ee(f))h.pop(),f.pop(),s--;else{if(h[0]!=f[0])break;h.shift(),f.shift(),t++}for(var d=0,p=0,g=h[0],m=f[0],v=Math.min(g.length,m.length);d<v&&g.charCodeAt(d)==m.charCodeAt(d);)++d;for(var y=ee(h),b=ee(f),w=Math.min(y.length-(1==h.length?d:0),b.length-(1==f.length?d:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==h.length&&1==f.length&&t==a.line)for(;d&&d>a.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)d--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var x=ut(t,d),k=ut(s,f.length?ee(f).length-p:0);return h.length>1||h[0]||ht(x,k)?(ca(n.doc,h,x,k,"+input"),!0):void 0},ns.prototype.ensurePolled=function(){this.forceCompositionEnd()},ns.prototype.reset=function(){this.forceCompositionEnd()},ns.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},ns.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},ns.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Pi(this.cm,(function(){return jn(e.cm)}))},ns.prototype.setUneditable=function(e){e.contentEditable="false"},ns.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||zi(this.cm,$l)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},ns.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},ns.prototype.onContextMenu=function(){},ns.prototype.resetPosition=function(){},ns.prototype.needsContentAttribute=!0;var us=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new U,this.hasSelection=!1,this.composing=null,this.resetting=!1};function hs(e,t){if((t=t?j(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=F(H(e));t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=l.getValue()}var i;if(e.form&&(be(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(Ie){}}t.finishInit=function(r){r.save=n,r.getTextArea=function(){return e},r.toTextArea=function(){r.toTextArea=isNaN,n(),e.parentNode.removeChild(r.getWrapperElement()),e.style.display="",e.form&&(xe(e.form,"submit",n),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var l=jl((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return l}function fs(e){e.off=xe,e.on=be,e.wheelEventPixels=lo,e.Doc=Da,e.splitLines=He,e.countColumn=V,e.findColumn=Z,e.isWordChar=ae,e.Pass=q,e.signal=ke,e.Line=dr,e.changeEnd=po,e.scrollbarModel=Mi,e.Pos=ut,e.cmpPos=ht,e.modes=Ue,e.mimeModes=Ke,e.resolveMode=$e,e.getMode=Xe,e.modeExtensions=Ye,e.extendMode=Ze,e.copyState=Qe,e.startState=et,e.innerMode=Je,e.commands=rl,e.keyMap=Va,e.keyName=Xa,e.isModifierKey=qa,e.lookupKey=Ga,e.normalizeKeyMap=Ka,e.StringStream=tt,e.SharedTextMarker=ka,e.TextMarker=wa,e.LineWidget=ma,e.e_preventDefault=Te,e.e_stopPropagation=De,e.e_stop=Oe,e.addClass=E,e.contains=_,e.rmClass=T,e.keyNames=Ha}us.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!Ce(n,e)){if(n.somethingSelected())ql({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=Zl(n);ql({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,$):(r.prevInput="",i.value=t.text.join("\n"),P(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),m&&(i.style.width="0px"),be(i,"input",(function(){a&&l>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),be(i,"paste",(function(e){Ce(n,e)||Xl(e,n)||(n.state.pasteIncoming=+new Date,r.fastPoll())})),be(i,"cut",o),be(i,"copy",o),be(e.scroller,"paste",(function(t){if(!$r(e,t)&&!Ce(n,t)){if(!i.dispatchEvent)return n.state.pasteIncoming=+new Date,void r.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),be(e.lineSpace,"selectstart",(function(t){$r(e,t)||Te(t)})),be(i,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),be(i,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},us.prototype.createField=function(e){this.wrapper=Jl(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;Ql(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},us.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},us.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Xn(e);if(e.options.moveInputWithCursor){var i=Sn(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},us.prototype.showSelection=function(e){var t=this.cm.display;A(t.cursorDiv,e.cursors),A(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},us.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&P(this.textarea),a&&l>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",a&&l>=9&&(this.hasSelection=null));this.resetting=!1}},us.prototype.getField=function(){return this.textarea},us.prototype.supportsTouch=function(){return!1},us.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||F(H(this.textarea))!=this.textarea))try{this.textarea.focus()}catch(Ie){}},us.prototype.blur=function(){this.textarea.blur()},us.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},us.prototype.receivedFocus=function(){this.slowPoll()},us.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},us.prototype.fastPoll=function(){var e=!1,t=this;function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},us.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||Re(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(a&&l>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,c=Math.min(n.length,i.length);s<c&&n.charCodeAt(s)==i.charCodeAt(s);)++s;return Pi(t,(function(){$l(t,i.slice(s),n.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},us.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},us.prototype.onKeyPress=function(){a&&l>=9&&(this.hasSelection=null),this.fastPoll()},us.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=Rn(r,e),c=n.scroller.scrollTop;if(o&&!f){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(o)&&zi(r,qo)(r.doc,fo(o),$);var u,h=i.style.cssText,d=t.wrapper.style.cssText,p=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(u=i.ownerDocument.defaultView.scrollY),n.input.focus(),s&&i.ownerDocument.defaultView.scrollTo(null,u),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),a&&l>=9&&m(),S){Oe(e);var g=function(){xe(window,"mouseup",g),setTimeout(v,20)};be(window,"mouseup",g)}else setTimeout(v,50)}function m(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=d,i.style.cssText=h,a&&l<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=c),null!=i.selectionStart)){(!a||a&&l<9)&&m();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?zi(r,ta)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},us.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},us.prototype.setUneditable=function(){},us.prototype.needsContentAttribute=!1,Hl(jl),es(jl);var ds="iter insert remove copy getEditor constructor".split(" ");for(var ps in Da.prototype)Da.prototype.hasOwnProperty(ps)&&K(ds,ps)<0&&(jl.prototype[ps]=function(e){return function(){return e.apply(this.doc,arguments)}}(Da.prototype[ps]));return Le(Da),jl.inputStyles={textarea:us,contenteditable:ns},jl.defineMode=function(e){jl.defaults.mode||"null"==e||(jl.defaults.mode=e),Ge.apply(this,arguments)},jl.defineMIME=qe,jl.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),jl.defineMIME("text/plain","null"),jl.defineExtension=function(e,t){jl.prototype[e]=t},jl.defineDocExtension=function(e,t){Da.prototype[e]=t},jl.fromTextArea=hs,fs(jl),jl.version="5.65.16",jl}()),x.exports}const C=t(k());!function(e){var t=e.Pos,r="http://www.w3.org/2000/svg";function n(e,t){this.mv=e,this.type=t,this.classes="left"==t?{chunk:"CodeMirror-merge-l-chunk",start:"CodeMirror-merge-l-chunk-start",end:"CodeMirror-merge-l-chunk-end",insert:"CodeMirror-merge-l-inserted",del:"CodeMirror-merge-l-deleted",connect:"CodeMirror-merge-l-connect"}:{chunk:"CodeMirror-merge-r-chunk",start:"CodeMirror-merge-r-chunk-start",end:"CodeMirror-merge-r-chunk-end",insert:"CodeMirror-merge-r-inserted",del:"CodeMirror-merge-r-deleted",connect:"CodeMirror-merge-r-connect"}}function i(t){t.diffOutOfDate&&(t.diff=O(t.orig.getValue(),t.edit.getValue(),t.mv.options.ignoreWhitespace),t.chunks=N(t.diff),t.diffOutOfDate=!1,e.signal(t.edit,"updateDiff",t.diff))}n.prototype={constructor:n,init:function(t,r,n){this.edit=this.mv.edit,(this.edit.state.diffViews||(this.edit.state.diffViews=[])).push(this),this.orig=e(t,j({value:r,readOnly:!this.mv.options.allowEditingOriginals},j(n))),"align"==this.mv.options.connect&&(this.edit.state.trackAlignable||(this.edit.state.trackAlignable=new q(this.edit)),this.orig.state.trackAlignable=new q(this.orig)),this.lockButton.title=this.edit.phrase("Toggle locked scrolling"),this.lockButton.setAttribute("aria-label",this.lockButton.title),this.orig.state.diffViews=[this];var i=n.chunkClassLocation||"background";"[object Array]"!=Object.prototype.toString.call(i)&&(i=[i]),this.classes.classLocation=i,this.diff=O(A(r),A(n.value),this.mv.options.ignoreWhitespace),this.chunks=N(this.diff),this.diffOutOfDate=this.dealigned=!1,this.needsScrollSync=null,this.showDifferences=!1!==n.showDifferences},registerEvents:function(e){this.forceUpdate=a(this),u(this,!0,!1),l(this,e)},setShowDifferences:function(e){(e=!1!==e)!=this.showDifferences&&(this.showDifferences=e,this.forceUpdate("full"))}};var o=!1;function a(t){var r,n={from:0,to:0,marked:[]},a={from:0,to:0,marked:[]},l=!1;function c(e){o=!0,l=!1,"full"==e&&(t.svg&&R(t.svg),t.copyButtons&&R(t.copyButtons),f(t.edit,n.marked,t.classes),f(t.orig,a.marked,t.classes),n.from=n.to=a.from=a.to=0),i(t),t.showDifferences&&(d(t.edit,t.diff,n,DIFF_INSERT,t.classes),d(t.orig,t.diff,a,DIFF_DELETE,t.classes)),"align"==t.mv.options.connect&&x(t),m(t),null!=t.needsScrollSync&&s(t,t.needsScrollSync),o=!1}function u(e){o||(t.dealigned=!0,h(e))}function h(e){o||l||(clearTimeout(r),!0===e&&(l=!0),r=setTimeout(c,!0===e?20:250))}function p(e,r){t.diffOutOfDate||(t.diffOutOfDate=!0,n.from=n.to=a.from=a.to=0),u(r.text.length-1!=r.to.line-r.from.line)}function g(){t.diffOutOfDate=!0,t.dealigned=!0,c("full")}return t.edit.on("change",p),t.orig.on("change",p),t.edit.on("swapDoc",g),t.orig.on("swapDoc",g),"align"==t.mv.options.connect&&(e.on(t.edit.state.trackAlignable,"realign",u),e.on(t.orig.state.trackAlignable,"realign",u)),t.edit.on("viewportChange",(function(){h(!1)})),t.orig.on("viewportChange",(function(){h(!1)})),c(),c}function l(e,t){e.edit.on("scroll",(function(){s(e,!0)&&m(e)})),e.orig.on("scroll",(function(){s(e,!1)&&m(e),t&&s(t,!0)&&m(t)}))}function s(e,t){if(e.diffOutOfDate)return e.lockScroll&&null==e.needsScrollSync&&(e.needsScrollSync=t),!1;if(e.needsScrollSync=null,!e.lockScroll)return!0;var r,n,i=+new Date;if(t?(r=e.edit,n=e.orig):(r=e.orig,n=e.edit),r.state.scrollSetBy==e&&(r.state.scrollSetAt||0)+250>i)return!1;var o=r.getScrollInfo();if("align"==e.mv.options.connect)m=o.top;else{var a,l,s=.5*o.clientHeight,u=o.top+s,h=r.lineAtHeight(u,"local"),f=E(e.chunks,h,t),d=c(r,t?f.edit:f.orig),p=c(n,t?f.orig:f.edit),g=(u-d.top)/(d.bot-d.top),m=p.top-s+g*(p.bot-p.top);if(m>o.top&&(l=o.top/s)<1)m=m*l+o.top*(1-l);else if((a=o.height-o.clientHeight-o.top)<s){var v=n.getScrollInfo();v.height-v.clientHeight-m>a&&(l=a/s)<1&&(m=m*l+(v.height-v.clientHeight-a)*(1-l))}}return n.scrollTo(o.left,m),n.state.scrollSetAt=i,n.state.scrollSetBy=e,!0}function c(e,t){var r=t.after;return null==r&&(r=e.lastLine()+1),{top:e.heightAtLine(t.before||0,"local"),bot:e.heightAtLine(r,"local")}}function u(t,r,n){t.lockScroll=r,r&&0!=n&&s(t,DIFF_INSERT)&&m(t),(r?e.addClass:e.rmClass)(t.lockButton,"CodeMirror-merge-scrolllock-enabled")}function h(e,t,r){for(var n=r.classLocation,i=0;i<n.length;i++)e.removeLineClass(t,n[i],r.chunk),e.removeLineClass(t,n[i],r.start),e.removeLineClass(t,n[i],r.end)}function f(t,r,n){for(var i=0;i<r.length;++i){var o=r[i];o instanceof e.TextMarker?o.clear():o.parent&&h(t,o,n)}r.length=0}function d(e,t,r,n,i){var o=e.getViewport();e.operation((function(){r.from==r.to||o.from-r.to>20||r.from-o.to>20?(f(e,r.marked,i),g(e,t,n,r.marked,o.from,o.to,i),r.from=o.from,r.to=o.to):(o.from<r.from&&(g(e,t,n,r.marked,o.from,r.from,i),r.from=o.from),o.to>r.to&&(g(e,t,n,r.marked,r.to,o.to,i),r.to=o.to))}))}function p(e,t,r,n,i,o){for(var a=r.classLocation,l=e.getLineHandle(t),s=0;s<a.length;s++)n&&e.addLineClass(l,a[s],r.chunk),i&&e.addLineClass(l,a[s],r.start),o&&e.addLineClass(l,a[s],r.end);return l}function g(e,r,n,i,o,a,l){var s=t(0,0),c=t(o,0),u=e.clipPos(t(a-1)),h=n==DIFF_DELETE?l.del:l.insert;function f(t,r){for(var n=Math.max(o,t),s=Math.min(a,r),c=n;c<s;++c)i.push(p(e,c,l,!0,c==t,c==r-1));t==r&&n==r&&s==r&&(n?i.push(p(e,n-1,l,!1,!1,!0)):i.push(p(e,n,l,!1,!0,!1)))}for(var d=0,g=!1,m=0;m<r.length;++m){var v=r[m],y=v[0],b=v[1];if(y==DIFF_EQUAL){var w=s.line+(F(r,m)?0:1);V(s,b);var x=s.line+(_(r,m)?1:0);x>w&&(g&&(f(d,w),g=!1),d=x)}else if(g=!0,y==n){var k=V(s,b,!0),C=X(c,s),S=$(u,k);Y(C,S)||i.push(e.markText(C,S,{className:h})),s=k}}g&&f(d,s.line+1)}function m(e){if(e.showDifferences){if(e.svg){R(e.svg);var t=e.gap.offsetWidth;B(e.svg,"width",t,"height",e.gap.offsetHeight)}e.copyButtons&&R(e.copyButtons);for(var r=e.edit.getViewport(),n=e.orig.getViewport(),i=e.mv.wrap.getBoundingClientRect().top,o=i-e.edit.getScrollerElement().getBoundingClientRect().top+e.edit.getScrollInfo().top,a=i-e.orig.getScrollerElement().getBoundingClientRect().top+e.orig.getScrollInfo().top,l=0;l<e.chunks.length;l++){var s=e.chunks[l];s.editFrom<=r.to&&s.editTo>=r.from&&s.origFrom<=n.to&&s.origTo>=n.from&&S(e,s,a,o,t)}}}function v(e,t){for(var r=0,n=0,i=0;i<t.length;i++){var o=t[i];if(o.editTo>e&&o.editFrom<=e)return null;if(o.editFrom>e)break;r=o.editTo,n=o.origTo}return n+(e-r)}function y(e,t,r){for(var n=e.state.trackAlignable,i=e.firstLine(),o=0,a=[],l=0;;l++){for(var s=t[l],c=s?r?s.origFrom:s.editFrom:1e9;o<n.alignable.length;o+=2){var u=n.alignable[o]+1;if(!(u<=i)){if(!(u<=c))break;a.push(u)}}if(!s)break;a.push(i=r?s.origTo:s.editTo)}return a}function b(e,t,r,n){var i=0,o=0,a=0,l=0;e:for(;;i++){var s=e[i],c=t[o];if(!s&&null==c)break;for(var u=s?s[0]:1e9,h=null==c?1e9:c;a<r.length;){var f=r[a];if(f.origFrom<=h&&f.origTo>h){o++,i--;continue e}if(f.editTo>u){if(f.editFrom<=u)continue e;break}l+=f.origTo-f.origFrom-(f.editTo-f.editFrom),a++}if(u==h-l)s[n]=h,o++;else if(u<h-l)s[n]=u+l;else{var d=[h-l,null,null];d[n]=h,e.splice(i,0,d),o++}}}function w(e,t){var r=y(e.edit,e.chunks,!1),n=[];if(t)for(var i=0,o=0;i<t.chunks.length;i++){for(var a=t.chunks[i].editTo;o<r.length&&r[o]<a;)o++;o!=r.length&&r[o]==a||r.splice(o++,0,a)}for(i=0;i<r.length;i++)n.push([r[i],null,null]);return b(n,y(e.orig,e.chunks,!0),e.chunks,1),t&&b(n,y(t.orig,t.chunks,!0),t.chunks,2),n}function x(e,t){if(e.dealigned||t){if(!e.orig.curOp)return e.orig.operation((function(){x(e,t)}));e.dealigned=!1;var r=e.mv.left==e?e.mv.right:e.mv.left;r&&(i(r),r.dealigned=!1);for(var n=w(e,r),o=e.mv.aligners,a=0;a<o.length;a++)o[a].clear();o.length=0;var l=[e.edit,e.orig],s=[],c=[];for(r&&l.push(r.orig),a=0;a<l.length;a++)s.push(l[a].getScrollInfo().top),c.push(-l[a].getScrollerElement().getBoundingClientRect().top);(c[0]!=c[1]||3==l.length&&c[1]!=c[2])&&k(l,c,[0,0,0],o);for(var u=0;u<n.length;u++)k(l,c,n[u],o);for(a=0;a<l.length;a++)l[a].scrollTo(null,s[a])}}function k(e,t,r,n){for(var i=-1e8,o=[],a=0;a<e.length;a++)if(null!=r[a]){var l=e[a].heightAtLine(r[a],"local")-t[a];o[a]=l,i=Math.max(i,l)}for(a=0;a<e.length;a++)if(null!=r[a]){var s=i-o[a];s>1&&n.push(C(e[a],r[a],s))}}function C(e,t,r){var n=!0;t>e.lastLine()&&(t--,n=!1);var i=document.createElement("div");return i.className="CodeMirror-merge-spacer",i.style.height=r+"px",i.style.minWidth="1px",e.addLineWidget(t,i,{height:r,above:n,mergeSpacer:!0,handleMouseEvents:!0})}function S(e,t,n,i,o){var a="left"==e.type,l=e.orig.heightAtLine(t.origFrom,"local",!0)-n;if(e.svg){var s=l,c=e.edit.heightAtLine(t.editFrom,"local",!0)-i;if(a){var u=s;s=c,c=u}var h=e.orig.heightAtLine(t.origTo,"local",!0)-n,f=e.edit.heightAtLine(t.editTo,"local",!0)-i;a&&(u=h,h=f,f=u);var d=" C "+o/2+" "+c+" "+o/2+" "+s+" "+(o+2)+" "+s,p=" C "+o/2+" "+h+" "+o/2+" "+f+" -1 "+f;B(e.svg.appendChild(document.createElementNS(r,"path")),"d","M -1 "+c+d+" L "+(o+2)+" "+h+p+" z","class",e.classes.connect)}if(e.copyButtons){var g=e.copyButtons.appendChild(H("div","left"==e.type?"⇝":"⇜","CodeMirror-merge-copy")),m=e.mv.options.allowEditingOriginals;if(g.title=e.edit.phrase(m?"Push to left":"Revert chunk"),g.chunk=t,g.style.top=(t.origTo>t.origFrom?l:e.edit.heightAtLine(t.editFrom,"local")-i)+"px",g.setAttribute("role","button"),g.setAttribute("tabindex","0"),g.setAttribute("aria-label",g.title),m){var v=e.edit.heightAtLine(t.editFrom,"local")-i,y=e.copyButtons.appendChild(H("div","right"==e.type?"⇝":"⇜","CodeMirror-merge-copy-reverse"));y.title="Push to right",y.chunk={editFrom:t.origFrom,editTo:t.origTo,origFrom:t.editFrom,origTo:t.editTo},y.style.top=v+"px","right"==e.type?y.style.left="2px":y.style.right="2px",y.setAttribute("role","button"),y.setAttribute("tabindex","0"),y.setAttribute("aria-label",y.title)}}}function M(e,r,n,i){if(!e.diffOutOfDate){var o=i.origTo>n.lastLine()?t(i.origFrom-1):t(i.origFrom,0),a=t(i.origTo,0),l=i.editTo>r.lastLine()?t(i.editFrom-1):t(i.editFrom,0),s=t(i.editTo,0),c=e.mv.options.revertChunk;c?c(e.mv,n,o,a,r,l,s):r.replaceRange(n.getRange(o,a),l,s)}}var L,T=e.MergeView=function(t,r){if(!(this instanceof T))return new T(t,r);this.options=r;var i=r.origLeft,o=null==r.origRight?r.orig:r.origRight,a=null!=i,l=null!=o,s=1+(a?1:0)+(l?1:0),c=[],u=this.left=null,h=this.right=null,f=this;if(a){u=this.left=new n(this,"left");var d=H("div",null,"CodeMirror-merge-pane CodeMirror-merge-left");c.push(d),c.push(D(u))}var p=H("div",null,"CodeMirror-merge-pane CodeMirror-merge-editor");if(c.push(p),l){h=this.right=new n(this,"right"),c.push(D(h));var g=H("div",null,"CodeMirror-merge-pane CodeMirror-merge-right");c.push(g)}(l?g:p).className+=" CodeMirror-merge-pane-rightmost",c.push(H("div",null,null,"height: 0; clear: both;"));var v=this.wrap=t.appendChild(H("div",c,"CodeMirror-merge CodeMirror-merge-"+s+"pane"));this.edit=e(p,j(r)),u&&u.init(d,i,r),h&&h.init(g,o,r),r.collapseIdentical&&this.editor().operation((function(){I(f,r.collapseIdentical)})),"align"==r.connect&&(this.aligners=[],x(this.left||this.right,!0)),u&&u.registerEvents(h),h&&h.registerEvents(u);var y=function(){u&&m(u),h&&m(h)};e.on(window,"resize",y);var b=setInterval((function(){for(var t=v.parentNode;t&&t!=document.body;t=t.parentNode);t||(clearInterval(b),e.off(window,"resize",y))}),5e3)};function D(t){var n=t.lockButton=H("div",null,"CodeMirror-merge-scrolllock");n.setAttribute("role","button"),n.setAttribute("tabindex","0");var i=H("div",[n],"CodeMirror-merge-scrolllock-wrap");e.on(n,"click",(function(){u(t,!t.lockScroll)})),e.on(n,"keyup",(function(e){("Enter"===e.key||"Space"===e.code)&&u(t,!t.lockScroll)}));var o=[i];if(!1!==t.mv.options.revertButtons){t.copyButtons=H("div",null,"CodeMirror-merge-copybuttons-"+t.type);var a=function(e){var r=e.target||e.srcElement;r.chunk&&("CodeMirror-merge-copy-reverse"!=r.className?M(t,t.edit,t.orig,r.chunk):M(t,t.orig,t.edit,r.chunk))};e.on(t.copyButtons,"click",a),e.on(t.copyButtons,"keyup",(function(e){("Enter"===e.key||"Space"===e.code)&&a(e)})),o.unshift(t.copyButtons)}if("align"!=t.mv.options.connect){var l=document.createElementNS&&document.createElementNS(r,"svg");l&&!l.createSVGRect&&(l=null),t.svg=l,l&&o.push(l)}return t.gap=H("div",o,"CodeMirror-merge-gap")}function A(e){return"string"==typeof e?e:e.getValue()}function O(e,t,r){L||(L=new diff_match_patch);for(var n=L.diff_main(e,t),i=0;i<n.length;++i){var o=n[i];(r?/[^ \t]/.test(o[1]):o[1])?i&&n[i-1][0]==o[0]&&(n.splice(i--,1),n[i][1]+=o[1]):n.splice(i--,1)}return n}function N(e){var r=[];if(!e.length)return r;for(var n=0,i=0,o=t(0,0),a=t(0,0),l=0;l<e.length;++l){var s=e[l],c=s[0];if(c==DIFF_EQUAL){var u=!F(e,l)||o.line<n||a.line<i?1:0,h=o.line+u,f=a.line+u;V(o,s[1],null,a);var d=_(e,l)?1:0,p=o.line+d,g=a.line+d;p>h&&(l&&r.push({origFrom:i,origTo:f,editFrom:n,editTo:h}),n=p,i=g)}else V(c==DIFF_INSERT?o:a,s[1])}return(n<=o.line||i<=a.line)&&r.push({origFrom:i,origTo:a.line+1,editFrom:n,editTo:o.line+1}),r}function _(e,t){if(t==e.length-1)return!0;var r=e[t+1][1];return!(1==r.length&&t<e.length-2||10!=r.charCodeAt(0))&&(t==e.length-2||((r=e[t+2][1]).length>1||t==e.length-3)&&10==r.charCodeAt(0))}function F(e,t){if(0==t)return!0;var r=e[t-1][1];return 10==r.charCodeAt(r.length-1)&&(1==t||10==(r=e[t-2][1]).charCodeAt(r.length-1))}function E(e,t,r){for(var n,i,o,a,l=0;l<e.length;l++){var s=e[l],c=r?s.editFrom:s.origFrom,u=r?s.editTo:s.origTo;null==i&&(c>t?(i=s.editFrom,a=s.origFrom):u>t&&(i=s.editTo,a=s.origTo)),u<=t?(n=s.editTo,o=s.origTo):c<=t&&(n=s.editFrom,o=s.origFrom)}return{edit:{before:n,after:i},orig:{before:o,after:a}}}function W(r,n,i){r.addLineClass(n,"wrap","CodeMirror-merge-collapsed-line");var o=document.createElement("span");o.className="CodeMirror-merge-collapsed-widget",o.title=r.phrase("Identical text collapsed. Click to expand.");var a=r.markText(t(n,0),t(i-1),{inclusiveLeft:!0,inclusiveRight:!0,replacedWith:o,clearOnEnter:!0});function l(){a.clear(),r.removeLineClass(n,"wrap","CodeMirror-merge-collapsed-line")}return a.explicitlyCleared&&l(),e.on(o,"click",l),a.on("clear",l),e.on(o,"click",l),{mark:a,clear:l}}function P(e,t){var r=[];function n(){for(var e=0;e<r.length;e++)r[e].clear()}for(var i=0;i<t.length;i++){var o=t[i],a=W(o.cm,o.line,o.line+e);r.push(a),a.mark.on("clear",n)}return r[0].mark}function z(e,t,r,n){for(var i=0;i<e.chunks.length;i++)for(var o=e.chunks[i],a=o.editFrom-t;a<o.editTo+t;a++){var l=a+r;l>=0&&l<n.length&&(n[l]=!1)}}function I(e,t){"number"!=typeof t&&(t=2);for(var r=[],n=e.editor(),i=n.firstLine(),o=i,a=n.lastLine();o<=a;o++)r.push(!0);e.left&&z(e.left,t,i,r),e.right&&z(e.right,t,i,r);for(var l=0;l<r.length;l++)if(r[l]){for(var s=l+i,c=1;l<r.length-1&&r[l+1];l++,c++);if(c>t){var u=[{line:s,cm:n}];e.left&&u.push({line:v(s,e.left.chunks),cm:e.left.orig}),e.right&&u.push({line:v(s,e.right.chunks),cm:e.right.orig});var h=P(c,u);e.options.onCollapse&&e.options.onCollapse(e,s,c,h)}}}function H(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function R(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild)}function B(e){for(var t=1;t<arguments.length;t+=2)e.setAttribute(arguments[t],arguments[t+1])}function j(e,t){for(var r in t||(t={}),e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}function V(e,r,n,i){for(var o=n?t(e.line,e.ch):e,a=0;;){var l=r.indexOf("\n",a);if(-1==l)break;++o.line,i&&++i.line,a=l+1}return o.ch=(a?0:o.ch)+(r.length-a),i&&(i.ch=(a?0:i.ch)+(r.length-a)),o}T.prototype={constructor:T,editor:function(){return this.edit},rightOriginal:function(){return this.right&&this.right.orig},leftOriginal:function(){return this.left&&this.left.orig},setShowDifferences:function(e){this.right&&this.right.setShowDifferences(e),this.left&&this.left.setShowDifferences(e)},rightChunks:function(){if(this.right)return i(this.right),this.right.chunks},leftChunks:function(){if(this.left)return i(this.left),this.left.chunks}};var U=1,K=2,G=4;function q(e){this.cm=e,this.alignable=[],this.height=e.doc.height;var t=this;e.on("markerAdded",(function(e,r){if(r.collapsed){var n=r.find(1);null!=n&&t.set(n.line,G)}})),e.on("markerCleared",(function(e,r,n,i){null!=i&&r.collapsed&&t.check(i,G,t.hasMarker)})),e.on("markerChanged",this.signal.bind(this)),e.on("lineWidgetAdded",(function(e,r,n){r.mergeSpacer||(r.above?t.set(n-1,K):t.set(n,U))})),e.on("lineWidgetCleared",(function(e,r,n){r.mergeSpacer||(r.above?t.check(n-1,K,t.hasWidgetBelow):t.check(n,U,t.hasWidget))})),e.on("lineWidgetChanged",this.signal.bind(this)),e.on("change",(function(e,r){var n=r.from.line,i=r.to.line-r.from.line,o=r.text.length-1,a=n+o;(i||o)&&t.map(n,i,o),t.check(a,G,t.hasMarker),(i||o)&&t.check(r.from.line,G,t.hasMarker)})),e.on("viewportChange",(function(){t.cm.doc.height!=t.height&&t.signal()}))}function $(e,t){return(e.line-t.line||e.ch-t.ch)<0?e:t}function X(e,t){return(e.line-t.line||e.ch-t.ch)>0?e:t}function Y(e,t){return e.line==t.line&&e.ch==t.ch}function Z(e,t,r){for(var n=e.length-1;n>=0;n--){var i=e[n],o=(r?i.origTo:i.editTo)-1;if(o<t)return o}}function Q(e,t,r){for(var n=0;n<e.length;n++){var i=e[n],o=r?i.origFrom:i.editFrom;if(o>t)return o}}function J(t,r){var n=null,o=t.state.diffViews,a=t.getCursor().line;if(o)for(var l=0;l<o.length;l++){var s=o[l],c=t==s.orig;i(s);var u=r<0?Z(s.chunks,a,c):Q(s.chunks,a,c);null==u||null!=n&&!(r<0?u>n:u<n)||(n=u)}if(null==n)return e.Pass;t.setCursor(n,0)}q.prototype={signal:function(){e.signal(this,"realign"),this.height=this.cm.doc.height},set:function(e,t){for(var r=-1;r<this.alignable.length;r+=2){var n=this.alignable[r]-e;if(0==n){if((this.alignable[r+1]&t)==t)return;return this.alignable[r+1]|=t,void this.signal()}if(n>0)break}this.signal(),this.alignable.splice(r,0,e,t)},find:function(e){for(var t=0;t<this.alignable.length;t+=2)if(this.alignable[t]==e)return t;return-1},check:function(e,t,r){var n=this.find(e);if(-1!=n&&this.alignable[n+1]&t&&!r.call(this,e)){this.signal();var i=this.alignable[n+1]&~t;i?this.alignable[n+1]=i:this.alignable.splice(n,2)}},hasMarker:function(e){var t=this.cm.getLineHandle(e);if(t.markedSpans)for(var r=0;r<t.markedSpans.length;r++)if(t.markedSpans[r].marker.collapsed&&null!=t.markedSpans[r].to)return!0;return!1},hasWidget:function(e){var t=this.cm.getLineHandle(e);if(t.widgets)for(var r=0;r<t.widgets.length;r++)if(!t.widgets[r].above&&!t.widgets[r].mergeSpacer)return!0;return!1},hasWidgetBelow:function(e){if(e==this.cm.lastLine())return!1;var t=this.cm.getLineHandle(e+1);if(t.widgets)for(var r=0;r<t.widgets.length;r++)if(t.widgets[r].above&&!t.widgets[r].mergeSpacer)return!0;return!1},map:function(e,t,r){for(var n=r-t,i=e+t,o=-1,a=-1,l=0;l<this.alignable.length;l+=2){var s=this.alignable[l];s==e&&this.alignable[l+1]&K&&(o=l),s==i&&this.alignable[l+1]&K&&(a=l),s<=e||(s<i?this.alignable.splice(l--,2):this.alignable[l]+=n)}if(o>-1){var c=this.alignable[o+1];c==K?this.alignable.splice(o,2):this.alignable[o+1]=c&~K}a>-1&&r&&this.set(e+r,K)}},e.commands.goNextDiff=function(e){return J(e,1)},e.commands.goPrevDiff=function(e){return J(e,-1)}}(k());var S,M,L,T={exports:{}};S=T,L=-1,(M=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32}).Diff=function(e,t){return[e,t]},M.prototype.diff_main=function(e,t,r,n){void 0===n&&(n=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout);var i=n;if(null==e||null==t)throw new Error("Null input. (diff_main)");if(e==t)return e?[new M.Diff(0,e)]:[];void 0===r&&(r=!0);var o=r,a=this.diff_commonPrefix(e,t),l=e.substring(0,a);e=e.substring(a),t=t.substring(a),a=this.diff_commonSuffix(e,t);var s=e.substring(e.length-a);e=e.substring(0,e.length-a),t=t.substring(0,t.length-a);var c=this.diff_compute_(e,t,o,i);return l&&c.unshift(new M.Diff(0,l)),s&&c.push(new M.Diff(0,s)),this.diff_cleanupMerge(c),c},M.prototype.diff_compute_=function(e,t,r,n){var i;if(!e)return[new M.Diff(1,t)];if(!t)return[new M.Diff(L,e)];var o=e.length>t.length?e:t,a=e.length>t.length?t:e,l=o.indexOf(a);if(-1!=l)return i=[new M.Diff(1,o.substring(0,l)),new M.Diff(0,a),new M.Diff(1,o.substring(l+a.length))],e.length>t.length&&(i[0][0]=i[2][0]=L),i;if(1==a.length)return[new M.Diff(L,e),new M.Diff(1,t)];var s=this.diff_halfMatch_(e,t);if(s){var c=s[0],u=s[1],h=s[2],f=s[3],d=s[4],p=this.diff_main(c,h,r,n),g=this.diff_main(u,f,r,n);return p.concat([new M.Diff(0,d)],g)}return r&&e.length>100&&t.length>100?this.diff_lineMode_(e,t,n):this.diff_bisect_(e,t,n)},M.prototype.diff_lineMode_=function(e,t,r){var n=this.diff_linesToChars_(e,t);e=n.chars1,t=n.chars2;var i=n.lineArray,o=this.diff_main(e,t,!1,r);this.diff_charsToLines_(o,i),this.diff_cleanupSemantic(o),o.push(new M.Diff(0,""));for(var a=0,l=0,s=0,c="",u="";a<o.length;){switch(o[a][0]){case 1:s++,u+=o[a][1];break;case L:l++,c+=o[a][1];break;case 0:if(l>=1&&s>=1){o.splice(a-l-s,l+s),a=a-l-s;for(var h=this.diff_main(c,u,!1,r),f=h.length-1;f>=0;f--)o.splice(a,0,h[f]);a+=h.length}s=0,l=0,c="",u=""}a++}return o.pop(),o},M.prototype.diff_bisect_=function(e,t,r){for(var n=e.length,i=t.length,o=Math.ceil((n+i)/2),a=o,l=2*o,s=new Array(l),c=new Array(l),u=0;u<l;u++)s[u]=-1,c[u]=-1;s[a+1]=0,c[a+1]=0;for(var h=n-i,f=h%2!=0,d=0,p=0,g=0,m=0,v=0;v<o&&!((new Date).getTime()>r);v++){for(var y=-v+d;y<=v-p;y+=2){for(var b=a+y,w=(T=y==-v||y!=v&&s[b-1]<s[b+1]?s[b+1]:s[b-1]+1)-y;T<n&&w<i&&e.charAt(T)==t.charAt(w);)T++,w++;if(s[b]=T,T>n)p+=2;else if(w>i)d+=2;else if(f&&(C=a+h-y)>=0&&C<l&&-1!=c[C]&&T>=(k=n-c[C]))return this.diff_bisectSplit_(e,t,T,w,r)}for(var x=-v+g;x<=v-m;x+=2){for(var k,C=a+x,S=(k=x==-v||x!=v&&c[C-1]<c[C+1]?c[C+1]:c[C-1]+1)-x;k<n&&S<i&&e.charAt(n-k-1)==t.charAt(i-S-1);)k++,S++;if(c[C]=k,k>n)m+=2;else if(S>i)g+=2;else if(!f){var T;if((b=a+h-x)>=0&&b<l&&-1!=s[b])if(w=a+(T=s[b])-b,T>=(k=n-k))return this.diff_bisectSplit_(e,t,T,w,r)}}}return[new M.Diff(L,e),new M.Diff(1,t)]},M.prototype.diff_bisectSplit_=function(e,t,r,n,i){var o=e.substring(0,r),a=t.substring(0,n),l=e.substring(r),s=t.substring(n),c=this.diff_main(o,a,!1,i),u=this.diff_main(l,s,!1,i);return c.concat(u)},M.prototype.diff_linesToChars_=function(e,t){var r=[],n={};function i(e){for(var t="",i=0,a=-1,l=r.length;a<e.length-1;){-1==(a=e.indexOf("\n",i))&&(a=e.length-1);var s=e.substring(i,a+1);(n.hasOwnProperty?n.hasOwnProperty(s):void 0!==n[s])?t+=String.fromCharCode(n[s]):(l==o&&(s=e.substring(i),a=e.length),t+=String.fromCharCode(l),n[s]=l,r[l++]=s),i=a+1}return t}r[0]="";var o=4e4,a=i(e);return o=65535,{chars1:a,chars2:i(t),lineArray:r}},M.prototype.diff_charsToLines_=function(e,t){for(var r=0;r<e.length;r++){for(var n=e[r][1],i=[],o=0;o<n.length;o++)i[o]=t[n.charCodeAt(o)];e[r][1]=i.join("")}},M.prototype.diff_commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var r=0,n=Math.min(e.length,t.length),i=n,o=0;r<i;)e.substring(o,i)==t.substring(o,i)?o=r=i:n=i,i=Math.floor((n-r)/2+r);return i},M.prototype.diff_commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var r=0,n=Math.min(e.length,t.length),i=n,o=0;r<i;)e.substring(e.length-i,e.length-o)==t.substring(t.length-i,t.length-o)?o=r=i:n=i,i=Math.floor((n-r)/2+r);return i},M.prototype.diff_commonOverlap_=function(e,t){var r=e.length,n=t.length;if(0==r||0==n)return 0;r>n?e=e.substring(r-n):r<n&&(t=t.substring(0,r));var i=Math.min(r,n);if(e==t)return i;for(var o=0,a=1;;){var l=e.substring(i-a),s=t.indexOf(l);if(-1==s)return o;a+=s,0!=s&&e.substring(i-a)!=t.substring(0,a)||(o=a,a++)}},M.prototype.diff_halfMatch_=function(e,t){if(this.Diff_Timeout<=0)return null;var r=e.length>t.length?e:t,n=e.length>t.length?t:e;if(r.length<4||2*n.length<r.length)return null;var i=this;function o(e,t,r){for(var n,o,a,l,s=e.substring(r,r+Math.floor(e.length/4)),c=-1,u="";-1!=(c=t.indexOf(s,c+1));){var h=i.diff_commonPrefix(e.substring(r),t.substring(c)),f=i.diff_commonSuffix(e.substring(0,r),t.substring(0,c));u.length<f+h&&(u=t.substring(c-f,c)+t.substring(c,c+h),n=e.substring(0,r-f),o=e.substring(r+h),a=t.substring(0,c-f),l=t.substring(c+h))}return 2*u.length>=e.length?[n,o,a,l,u]:null}var a,l,s,c,u,h=o(r,n,Math.ceil(r.length/4)),f=o(r,n,Math.ceil(r.length/2));return h||f?(a=f?h&&h[4].length>f[4].length?h:f:h,e.length>t.length?(l=a[0],s=a[1],c=a[2],u=a[3]):(c=a[0],u=a[1],l=a[2],s=a[3]),[l,s,c,u,a[4]]):null},M.prototype.diff_cleanupSemantic=function(e){for(var t=!1,r=[],n=0,i=null,o=0,a=0,l=0,s=0,c=0;o<e.length;)0==e[o][0]?(r[n++]=o,a=s,l=c,s=0,c=0,i=e[o][1]):(1==e[o][0]?s+=e[o][1].length:c+=e[o][1].length,i&&i.length<=Math.max(a,l)&&i.length<=Math.max(s,c)&&(e.splice(r[n-1],0,new M.Diff(L,i)),e[r[n-1]+1][0]=1,n--,o=--n>0?r[n-1]:-1,a=0,l=0,s=0,c=0,i=null,t=!0)),o++;for(t&&this.diff_cleanupMerge(e),this.diff_cleanupSemanticLossless(e),o=1;o<e.length;){if(e[o-1][0]==L&&1==e[o][0]){var u=e[o-1][1],h=e[o][1],f=this.diff_commonOverlap_(u,h),d=this.diff_commonOverlap_(h,u);f>=d?(f>=u.length/2||f>=h.length/2)&&(e.splice(o,0,new M.Diff(0,h.substring(0,f))),e[o-1][1]=u.substring(0,u.length-f),e[o+1][1]=h.substring(f),o++):(d>=u.length/2||d>=h.length/2)&&(e.splice(o,0,new M.Diff(0,u.substring(0,d))),e[o-1][0]=1,e[o-1][1]=h.substring(0,h.length-d),e[o+1][0]=L,e[o+1][1]=u.substring(d),o++),o++}o++}},M.prototype.diff_cleanupSemanticLossless=function(e){function t(e,t){if(!e||!t)return 6;var r=e.charAt(e.length-1),n=t.charAt(0),i=r.match(M.nonAlphaNumericRegex_),o=n.match(M.nonAlphaNumericRegex_),a=i&&r.match(M.whitespaceRegex_),l=o&&n.match(M.whitespaceRegex_),s=a&&r.match(M.linebreakRegex_),c=l&&n.match(M.linebreakRegex_),u=s&&e.match(M.blanklineEndRegex_),h=c&&t.match(M.blanklineStartRegex_);return u||h?5:s||c?4:i&&!a&&l?3:a||l?2:i||o?1:0}for(var r=1;r<e.length-1;){if(0==e[r-1][0]&&0==e[r+1][0]){var n=e[r-1][1],i=e[r][1],o=e[r+1][1],a=this.diff_commonSuffix(n,i);if(a){var l=i.substring(i.length-a);n=n.substring(0,n.length-a),i=l+i.substring(0,i.length-a),o=l+o}for(var s=n,c=i,u=o,h=t(n,i)+t(i,o);i.charAt(0)===o.charAt(0);){n+=i.charAt(0),i=i.substring(1)+o.charAt(0),o=o.substring(1);var f=t(n,i)+t(i,o);f>=h&&(h=f,s=n,c=i,u=o)}e[r-1][1]!=s&&(s?e[r-1][1]=s:(e.splice(r-1,1),r--),e[r][1]=c,u?e[r+1][1]=u:(e.splice(r+1,1),r--))}r++}},M.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,M.whitespaceRegex_=/\s/,M.linebreakRegex_=/[\r\n]/,M.blanklineEndRegex_=/\n\r?\n$/,M.blanklineStartRegex_=/^\r?\n\r?\n/,M.prototype.diff_cleanupEfficiency=function(e){for(var t=!1,r=[],n=0,i=null,o=0,a=!1,l=!1,s=!1,c=!1;o<e.length;)0==e[o][0]?(e[o][1].length<this.Diff_EditCost&&(s||c)?(r[n++]=o,a=s,l=c,i=e[o][1]):(n=0,i=null),s=c=!1):(e[o][0]==L?c=!0:s=!0,i&&(a&&l&&s&&c||i.length<this.Diff_EditCost/2&&a+l+s+c==3)&&(e.splice(r[n-1],0,new M.Diff(L,i)),e[r[n-1]+1][0]=1,n--,i=null,a&&l?(s=c=!0,n=0):(o=--n>0?r[n-1]:-1,s=c=!1),t=!0)),o++;t&&this.diff_cleanupMerge(e)},M.prototype.diff_cleanupMerge=function(e){e.push(new M.Diff(0,""));for(var t,r=0,n=0,i=0,o="",a="";r<e.length;)switch(e[r][0]){case 1:i++,a+=e[r][1],r++;break;case L:n++,o+=e[r][1],r++;break;case 0:n+i>1?(0!==n&&0!==i&&(0!==(t=this.diff_commonPrefix(a,o))&&(r-n-i>0&&0==e[r-n-i-1][0]?e[r-n-i-1][1]+=a.substring(0,t):(e.splice(0,0,new M.Diff(0,a.substring(0,t))),r++),a=a.substring(t),o=o.substring(t)),0!==(t=this.diff_commonSuffix(a,o))&&(e[r][1]=a.substring(a.length-t)+e[r][1],a=a.substring(0,a.length-t),o=o.substring(0,o.length-t))),r-=n+i,e.splice(r,n+i),o.length&&(e.splice(r,0,new M.Diff(L,o)),r++),a.length&&(e.splice(r,0,new M.Diff(1,a)),r++),r++):0!==r&&0==e[r-1][0]?(e[r-1][1]+=e[r][1],e.splice(r,1)):r++,i=0,n=0,o="",a=""}""===e[e.length-1][1]&&e.pop();var l=!1;for(r=1;r<e.length-1;)0==e[r-1][0]&&0==e[r+1][0]&&(e[r][1].substring(e[r][1].length-e[r-1][1].length)==e[r-1][1]?(e[r][1]=e[r-1][1]+e[r][1].substring(0,e[r][1].length-e[r-1][1].length),e[r+1][1]=e[r-1][1]+e[r+1][1],e.splice(r-1,1),l=!0):e[r][1].substring(0,e[r+1][1].length)==e[r+1][1]&&(e[r-1][1]+=e[r+1][1],e[r][1]=e[r][1].substring(e[r+1][1].length)+e[r+1][1],e.splice(r+1,1),l=!0)),r++;l&&this.diff_cleanupMerge(e)},M.prototype.diff_xIndex=function(e,t){var r,n=0,i=0,o=0,a=0;for(r=0;r<e.length&&(1!==e[r][0]&&(n+=e[r][1].length),e[r][0]!==L&&(i+=e[r][1].length),!(n>t));r++)o=n,a=i;return e.length!=r&&e[r][0]===L?a:a+(t-o)},M.prototype.diff_prettyHtml=function(e){for(var t=[],r=/&/g,n=/</g,i=/>/g,o=/\n/g,a=0;a<e.length;a++){var l=e[a][0],s=e[a][1].replace(r,"&amp;").replace(n,"&lt;").replace(i,"&gt;").replace(o,"&para;<br>");switch(l){case 1:t[a]='<ins style="background:#e6ffe6;">'+s+"</ins>";break;case L:t[a]='<del style="background:#ffe6e6;">'+s+"</del>";break;case 0:t[a]="<span>"+s+"</span>"}}return t.join("")},M.prototype.diff_text1=function(e){for(var t=[],r=0;r<e.length;r++)1!==e[r][0]&&(t[r]=e[r][1]);return t.join("")},M.prototype.diff_text2=function(e){for(var t=[],r=0;r<e.length;r++)e[r][0]!==L&&(t[r]=e[r][1]);return t.join("")},M.prototype.diff_levenshtein=function(e){for(var t=0,r=0,n=0,i=0;i<e.length;i++){var o=e[i][0],a=e[i][1];switch(o){case 1:r+=a.length;break;case L:n+=a.length;break;case 0:t+=Math.max(r,n),r=0,n=0}}return t+=Math.max(r,n)},M.prototype.diff_toDelta=function(e){for(var t=[],r=0;r<e.length;r++)switch(e[r][0]){case 1:t[r]="+"+encodeURI(e[r][1]);break;case L:t[r]="-"+e[r][1].length;break;case 0:t[r]="="+e[r][1].length}return t.join("\t").replace(/%20/g," ")},M.prototype.diff_fromDelta=function(e,t){for(var r=[],n=0,i=0,o=t.split(/\t/g),a=0;a<o.length;a++){var l=o[a].substring(1);switch(o[a].charAt(0)){case"+":try{r[n++]=new M.Diff(1,decodeURI(l))}catch(u){throw new Error("Illegal escape in diff_fromDelta: "+l)}break;case"-":case"=":var s=parseInt(l,10);if(isNaN(s)||s<0)throw new Error("Invalid number in diff_fromDelta: "+l);var c=e.substring(i,i+=s);"="==o[a].charAt(0)?r[n++]=new M.Diff(0,c):r[n++]=new M.Diff(L,c);break;default:if(o[a])throw new Error("Invalid diff operation in diff_fromDelta: "+o[a])}}if(i!=e.length)throw new Error("Delta length ("+i+") does not equal source text length ("+e.length+").");return r},M.prototype.match_main=function(e,t,r){if(null==e||null==t||null==r)throw new Error("Null input. (match_main)");return r=Math.max(0,Math.min(r,e.length)),e==t?0:e.length?e.substring(r,r+t.length)==t?r:this.match_bitap_(e,t,r):-1},M.prototype.match_bitap_=function(e,t,r){if(t.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var n=this.match_alphabet_(t),i=this;function o(e,n){var o=e/t.length,a=Math.abs(r-n);return i.Match_Distance?o+a/i.Match_Distance:a?1:o}var a=this.Match_Threshold,l=e.indexOf(t,r);-1!=l&&(a=Math.min(o(0,l),a),-1!=(l=e.lastIndexOf(t,r+t.length))&&(a=Math.min(o(0,l),a)));var s,c,u=1<<t.length-1;l=-1;for(var h,f=t.length+e.length,d=0;d<t.length;d++){for(s=0,c=f;s<c;)o(d,r+c)<=a?s=c:f=c,c=Math.floor((f-s)/2+s);f=c;var p=Math.max(1,r-c+1),g=Math.min(r+c,e.length)+t.length,m=Array(g+2);m[g+1]=(1<<d)-1;for(var v=g;v>=p;v--){var y=n[e.charAt(v-1)];if(m[v]=0===d?(m[v+1]<<1|1)&y:(m[v+1]<<1|1)&y|(h[v+1]|h[v])<<1|1|h[v+1],m[v]&u){var b=o(d,v-1);if(b<=a){if(a=b,!((l=v-1)>r))break;p=Math.max(1,2*r-l)}}}if(o(d+1,r)>a)break;h=m}return l},M.prototype.match_alphabet_=function(e){for(var t={},r=0;r<e.length;r++)t[e.charAt(r)]=0;for(r=0;r<e.length;r++)t[e.charAt(r)]|=1<<e.length-r-1;return t},M.prototype.patch_addContext_=function(e,t){if(0!=t.length){if(null===e.start2)throw Error("patch not initialized");for(var r=t.substring(e.start2,e.start2+e.length1),n=0;t.indexOf(r)!=t.lastIndexOf(r)&&r.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)n+=this.Patch_Margin,r=t.substring(e.start2-n,e.start2+e.length1+n);n+=this.Patch_Margin;var i=t.substring(e.start2-n,e.start2);i&&e.diffs.unshift(new M.Diff(0,i));var o=t.substring(e.start2+e.length1,e.start2+e.length1+n);o&&e.diffs.push(new M.Diff(0,o)),e.start1-=i.length,e.start2-=i.length,e.length1+=i.length+o.length,e.length2+=i.length+o.length}},M.prototype.patch_make=function(e,t,r){var n,i;if("string"==typeof e&&"string"==typeof t&&void 0===r)n=e,(i=this.diff_main(n,t,!0)).length>2&&(this.diff_cleanupSemantic(i),this.diff_cleanupEfficiency(i));else if(e&&"object"==typeof e&&void 0===t&&void 0===r)i=e,n=this.diff_text1(i);else if("string"==typeof e&&t&&"object"==typeof t&&void 0===r)n=e,i=t;else{if("string"!=typeof e||"string"!=typeof t||!r||"object"!=typeof r)throw new Error("Unknown call format to patch_make.");n=e,i=r}if(0===i.length)return[];for(var o=[],a=new M.patch_obj,l=0,s=0,c=0,u=n,h=n,f=0;f<i.length;f++){var d=i[f][0],p=i[f][1];switch(l||0===d||(a.start1=s,a.start2=c),d){case 1:a.diffs[l++]=i[f],a.length2+=p.length,h=h.substring(0,c)+p+h.substring(c);break;case L:a.length1+=p.length,a.diffs[l++]=i[f],h=h.substring(0,c)+h.substring(c+p.length);break;case 0:p.length<=2*this.Patch_Margin&&l&&i.length!=f+1?(a.diffs[l++]=i[f],a.length1+=p.length,a.length2+=p.length):p.length>=2*this.Patch_Margin&&l&&(this.patch_addContext_(a,u),o.push(a),a=new M.patch_obj,l=0,u=h,s=c)}1!==d&&(s+=p.length),d!==L&&(c+=p.length)}return l&&(this.patch_addContext_(a,u),o.push(a)),o},M.prototype.patch_deepCopy=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r],i=new M.patch_obj;i.diffs=[];for(var o=0;o<n.diffs.length;o++)i.diffs[o]=new M.Diff(n.diffs[o][0],n.diffs[o][1]);i.start1=n.start1,i.start2=n.start2,i.length1=n.length1,i.length2=n.length2,t[r]=i}return t},M.prototype.patch_apply=function(e,t){if(0==e.length)return[t,[]];e=this.patch_deepCopy(e);var r=this.patch_addPadding(e);t=r+t+r,this.patch_splitMax(e);for(var n=0,i=[],o=0;o<e.length;o++){var a,l,s=e[o].start2+n,c=this.diff_text1(e[o].diffs),u=-1;if(c.length>this.Match_MaxBits?-1!=(a=this.match_main(t,c.substring(0,this.Match_MaxBits),s))&&(-1==(u=this.match_main(t,c.substring(c.length-this.Match_MaxBits),s+c.length-this.Match_MaxBits))||a>=u)&&(a=-1):a=this.match_main(t,c,s),-1==a)i[o]=!1,n-=e[o].length2-e[o].length1;else if(i[o]=!0,n=a-s,c==(l=-1==u?t.substring(a,a+c.length):t.substring(a,u+this.Match_MaxBits)))t=t.substring(0,a)+this.diff_text2(e[o].diffs)+t.substring(a+c.length);else{var h=this.diff_main(c,l,!1);if(c.length>this.Match_MaxBits&&this.diff_levenshtein(h)/c.length>this.Patch_DeleteThreshold)i[o]=!1;else{this.diff_cleanupSemanticLossless(h);for(var f,d=0,p=0;p<e[o].diffs.length;p++){var g=e[o].diffs[p];0!==g[0]&&(f=this.diff_xIndex(h,d)),1===g[0]?t=t.substring(0,a+f)+g[1]+t.substring(a+f):g[0]===L&&(t=t.substring(0,a+f)+t.substring(a+this.diff_xIndex(h,d+g[1].length))),g[0]!==L&&(d+=g[1].length)}}}}return[t=t.substring(r.length,t.length-r.length),i]},M.prototype.patch_addPadding=function(e){for(var t=this.Patch_Margin,r="",n=1;n<=t;n++)r+=String.fromCharCode(n);for(n=0;n<e.length;n++)e[n].start1+=t,e[n].start2+=t;var i=e[0],o=i.diffs;if(0==o.length||0!=o[0][0])o.unshift(new M.Diff(0,r)),i.start1-=t,i.start2-=t,i.length1+=t,i.length2+=t;else if(t>o[0][1].length){var a=t-o[0][1].length;o[0][1]=r.substring(o[0][1].length)+o[0][1],i.start1-=a,i.start2-=a,i.length1+=a,i.length2+=a}return 0==(o=(i=e[e.length-1]).diffs).length||0!=o[o.length-1][0]?(o.push(new M.Diff(0,r)),i.length1+=t,i.length2+=t):t>o[o.length-1][1].length&&(a=t-o[o.length-1][1].length,o[o.length-1][1]+=r.substring(0,a),i.length1+=a,i.length2+=a),r},M.prototype.patch_splitMax=function(e){for(var t=this.Match_MaxBits,r=0;r<e.length;r++)if(!(e[r].length1<=t)){var n=e[r];e.splice(r--,1);for(var i=n.start1,o=n.start2,a="";0!==n.diffs.length;){var l=new M.patch_obj,s=!0;for(l.start1=i-a.length,l.start2=o-a.length,""!==a&&(l.length1=l.length2=a.length,l.diffs.push(new M.Diff(0,a)));0!==n.diffs.length&&l.length1<t-this.Patch_Margin;){var c=n.diffs[0][0],u=n.diffs[0][1];1===c?(l.length2+=u.length,o+=u.length,l.diffs.push(n.diffs.shift()),s=!1):c===L&&1==l.diffs.length&&0==l.diffs[0][0]&&u.length>2*t?(l.length1+=u.length,i+=u.length,s=!1,l.diffs.push(new M.Diff(c,u)),n.diffs.shift()):(u=u.substring(0,t-l.length1-this.Patch_Margin),l.length1+=u.length,i+=u.length,0===c?(l.length2+=u.length,o+=u.length):s=!1,l.diffs.push(new M.Diff(c,u)),u==n.diffs[0][1]?n.diffs.shift():n.diffs[0][1]=n.diffs[0][1].substring(u.length))}a=(a=this.diff_text2(l.diffs)).substring(a.length-this.Patch_Margin);var h=this.diff_text1(n.diffs).substring(0,this.Patch_Margin);""!==h&&(l.length1+=h.length,l.length2+=h.length,0!==l.diffs.length&&0===l.diffs[l.diffs.length-1][0]?l.diffs[l.diffs.length-1][1]+=h:l.diffs.push(new M.Diff(0,h))),s||e.splice(++r,0,l)}}},M.prototype.patch_toText=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e[r];return t.join("")},M.prototype.patch_fromText=function(e){var t=[];if(!e)return t;for(var r=e.split("\n"),n=0,i=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;n<r.length;){var o=r[n].match(i);if(!o)throw new Error("Invalid patch string: "+r[n]);var a=new M.patch_obj;for(t.push(a),a.start1=parseInt(o[1],10),""===o[2]?(a.start1--,a.length1=1):"0"==o[2]?a.length1=0:(a.start1--,a.length1=parseInt(o[2],10)),a.start2=parseInt(o[3],10),""===o[4]?(a.start2--,a.length2=1):"0"==o[4]?a.length2=0:(a.start2--,a.length2=parseInt(o[4],10)),n++;n<r.length;){var l=r[n].charAt(0);try{var s=decodeURI(r[n].substring(1))}catch(c){throw new Error("Illegal escape in patch_fromText: "+s)}if("-"==l)a.diffs.push(new M.Diff(L,s));else if("+"==l)a.diffs.push(new M.Diff(1,s));else if(" "==l)a.diffs.push(new M.Diff(0,s));else{if("@"==l)break;if(""!==l)throw new Error('Invalid patch mode "'+l+'" in: '+s)}n++}}return t},(M.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0}).prototype.toString=function(){for(var e,t=["@@ -"+(0===this.length1?this.start1+",0":1==this.length1?this.start1+1:this.start1+1+","+this.length1)+" +"+(0===this.length2?this.start2+",0":1==this.length2?this.start2+1:this.start2+1+","+this.length2)+" @@\n"],r=0;r<this.diffs.length;r++){switch(this.diffs[r][0]){case 1:e="+";break;case L:e="-";break;case 0:e=" "}t[r+1]=e+encodeURI(this.diffs[r][1])+"\n"}return t.join("").replace(/%20/g," ")},S.exports=M,S.exports.diff_match_patch=M,S.exports.DIFF_DELETE=L,S.exports.DIFF_INSERT=1,S.exports.DIFF_EQUAL=0;const D=t(T.exports);!function(e){function t(e,t){if(!e.hasOwnProperty(t))throw new Error("Undefined state "+t+" in simple mode")}function r(e,t){if(!e)return/(?:)/;var r="";return e instanceof RegExp?(e.ignoreCase&&(r="i"),e.unicode&&(r+="u"),e=e.source):e=String(e),new RegExp((!1===t?"":"^")+"(?:"+e+")",r)}function n(e){if(!e)return null;if(e.apply)return e;if("string"==typeof e)return e.replace(/\./g," ");for(var t=[],r=0;r<e.length;r++)t.push(e[r]&&e[r].replace(/\./g," "));return t}function i(e,i){(e.next||e.push)&&t(i,e.next||e.push),this.regex=r(e.regex),this.token=n(e.token),this.data=e}function o(e,t){return function(r,n){if(n.pending){var i=n.pending.shift();return 0==n.pending.length&&(n.pending=null),r.pos+=i.text.length,i.token}if(n.local){if(n.local.end&&r.match(n.local.end)){var o=n.local.endToken||null;return n.local=n.localState=null,o}var a;return o=n.local.mode.token(r,n.localState),n.local.endScan&&(a=n.local.endScan.exec(r.current()))&&(r.pos=r.start+a.index),o}for(var s=e[n.state],c=0;c<s.length;c++){var u=s[c],h=(!u.data.sol||r.sol())&&r.match(u.regex);if(h){u.data.next?n.state=u.data.next:u.data.push?((n.stack||(n.stack=[])).push(n.state),n.state=u.data.push):u.data.pop&&n.stack&&n.stack.length&&(n.state=n.stack.pop()),u.data.mode&&l(t,n,u.data.mode,u.token),u.data.indent&&n.indent.push(r.indentation()+t.indentUnit),u.data.dedent&&n.indent.pop();var f=u.token;if(f&&f.apply&&(f=f(h)),h.length>2&&u.token&&"string"!=typeof u.token){for(var d=2;d<h.length;d++)h[d]&&(n.pending||(n.pending=[])).push({text:h[d],token:u.token[d-1]});return r.backUp(h[0].length-(h[1]?h[1].length:0)),f[0]}return f&&f.join?f[0]:f}}return r.next(),null}}function a(e,t){if(e===t)return!0;if(!e||"object"!=typeof e||!t||"object"!=typeof t)return!1;var r=0;for(var n in e)if(e.hasOwnProperty(n)){if(!t.hasOwnProperty(n)||!a(e[n],t[n]))return!1;r++}for(var n in t)t.hasOwnProperty(n)&&r--;return 0==r}function l(t,n,i,o){var l;if(i.persistent)for(var s=n.persistentStates;s&&!l;s=s.next)(i.spec?a(i.spec,s.spec):i.mode==s.mode)&&(l=s);var c=l?l.mode:i.mode||e.getMode(t,i.spec),u=l?l.state:e.startState(c);i.persistent&&!l&&(n.persistentStates={mode:c,spec:i.spec,state:u,next:n.persistentStates}),n.localState=u,n.local={mode:c,end:i.end&&r(i.end),endScan:i.end&&!1!==i.forceEnd&&r(i.end,!1),endToken:o&&o.join?o[o.length-1]:o}}function s(e,t){for(var r=0;r<t.length;r++)if(t[r]===e)return!0}function c(t,r){return function(n,i,o){if(n.local&&n.local.mode.indent)return n.local.mode.indent(n.localState,i,o);if(null==n.indent||n.local||r.dontIndentStates&&s(n.state,r.dontIndentStates)>-1)return e.Pass;var a=n.indent.length-1,l=t[n.state];e:for(;;){for(var c=0;c<l.length;c++){var u=l[c];if(u.data.dedent&&!1!==u.data.dedentIfLineStart){var h=u.regex.exec(i);if(h&&h[0]){a--,(u.next||u.push)&&(l=t[u.next||u.push]),i=i.slice(h[0].length);continue e}}}break}return a<0?0:n.indent[a]}}e.defineSimpleMode=function(t,r){e.defineMode(t,(function(t){return e.simpleMode(t,r)}))},e.simpleMode=function(r,n){t(n,"start");var a={},l=n.meta||{},s=!1;for(var u in n)if(u!=l&&n.hasOwnProperty(u))for(var h=a[u]=[],f=n[u],d=0;d<f.length;d++){var p=f[d];h.push(new i(p,n)),(p.indent||p.dedent)&&(s=!0)}var g={startState:function(){return{state:"start",pending:null,local:null,localState:null,indent:s?[]:null}},copyState:function(t){var r={state:t.state,pending:t.pending,local:t.local,localState:null,indent:t.indent&&t.indent.slice(0)};t.localState&&(r.localState=e.copyState(t.local.mode,t.localState)),t.stack&&(r.stack=t.stack.slice(0));for(var n=t.persistentStates;n;n=n.next)r.persistentStates={mode:n.mode,spec:n.spec,state:n.state==t.localState?r.localState:e.copyState(n.mode,n.state),next:r.persistentStates};return r},token:o(a,r),innerMode:function(e){return e.local&&{mode:e.local.mode,state:e.localState}},indent:c(a,l)};if(l)for(var m in l)l.hasOwnProperty(m)&&(g[m]=l[m]);return g}}(k()),!window.CodeMirror&&(window.CodeMirror=C);const A=window.CodeMirror||C,O=r({name:"DefaultMode",props:{name:{type:String,default:"cm-textarea-"+ +new Date},value:{type:String,default:""},content:{type:String,default:""},options:{type:Object,default:()=>({})},cminstance:{type:Object,default:()=>null},placeholder:{type:String,default:""}},emits:{ready:e=>e,"update:cminstance":e=>e},setup(e,{emit:t}){const r=n(),i=n(null),o=()=>{i.value=b(A.fromTextArea(r.value,e.options)),t("update:cminstance",i.value);const n=s((()=>e.cminstance),(r=>{var o;r&&(null==(o=e.cminstance)||o.setValue(e.value||e.content)),t("ready",l(i)),null==n||n()}),{deep:!0})};return y((()=>{o()})),{textarea:r,initialize:o}}}),N=(e,t)=>{const r=e.__vccOpts||e;for(const[n,i]of t)r[n]=i;return r},_=["name","placeholder"];const F=N(O,[["render",function(e,t,r,n,i,o){return u(),h("textarea",{ref:"textarea",name:e.$props.name,placeholder:e.$props.placeholder},null,8,_)}]]);window.diff_match_patch=D,window.DIFF_DELETE=-1,window.DIFF_INSERT=1,window.DIFF_EQUAL=0;const E=r({name:"MergeMode",props:{options:{type:Object,default:()=>({})},cminstance:{type:Object,default:()=>({})}},emits:["update:cminstance","ready"],setup(e,{emit:t}){const r=n(),i=n(),o=()=>{r.value=b(A.MergeView(i.value,e.options)),t("update:cminstance",r.value),t("ready",r)};return y((()=>{o()})),{mergeView:i,initialize:o}}}),W={ref:"mergeView"};const P=N(E,[["render",function(e,t,r,n,i,o){return u(),h("div",W,null,512)}]]);const z=[{regex:/(\[.*?\])([ \t]*)(<error>[ \t])(.+)/,token:["tag","","error.strong","error.strong"],sol:!0},{regex:/(\[.*?\])([ \t]*)(<info>)(.+)(.?)/,token:["tag","","bracket","bracket","hr"],sol:!0},{regex:/(\[.*?\])([ \t]*)(<warning>)(.+)(.?)/,token:["tag","","comment","comment","hr"],sol:!0}];A.defineSimpleMode("fclog",{start:[...z,{regex:/.*/,token:"hr"}],error:[...z,{regex:/.*/,token:"error.strong"}],info:[...z,{regex:/.*/,token:"bracket"}],warning:[...z,{regex:/.*\[/,token:"comment"}]}),A.defineSimpleMode("log",{start:[{regex:/^[=]+[^=]*[=]+/,token:"strong"},{regex:/([^\w])([A-Z][\w]*)/,token:["","string"]},{regex:/(^[A-Z][\w]*)/,token:"string"}]});const I=r({name:"CodemirrorFclog",props:{value:{type:String,default:""},name:{type:String,default:"cm-textarea-"+ +new Date},options:{type:Object,default:()=>({})},cminstance:{type:Object,default:()=>({})},placeholder:{type:String,default:""}},emits:["update:cminstance","ready"],setup(e,{emit:t}){const r=n(),i=n(null),o=(t=e.cminstance)=>{t.getAllMarks().forEach((e=>e.clear()));const r=t.getValue(),n=[].concat(function(e){const t=/#link#(.+)#link#/g,r=[];let n;for(n=t.exec(e);n;){const i=document.createElement("a"),o=JSON.parse(n[1]),a=Object.entries(o);for(const[e,t]of a)i.setAttribute(e,t);i.className="editor_custom_link",i.innerHTML="logDownload",r.push({start:n.index,end:n.index+n[0].length,node:i}),n=t.exec(e)}return r}(r)).concat(function(e){const t=[];return function(){const r=/#log<(\w*)>log#((.|\r\n|\n)*?)#log<(\w*)>log#/g;let n;for(n=r.exec(e);n;){const i=n[0].replace(/\r\n/g,"\n").split("\n"),o=n[2].replace(/\r\n/g,"\n").split("\n"),a=document.createElement("span"),l=n[1];a.className=`c-editor--log__${l}`;let s=0;for(let e=0;e<i.length;e++){const r=i[e],l=o[e],c=a.cloneNode(!1);c.innerText=l,t.push({start:n.index+s,end:n.index+s+r.length,node:c}),s=s+r.length+1}n=r.exec(e)}}(),t}(r));for(let e=0;e<n.length;e++){const r=n[e];t.markText(t.posFromIndex(r.start),t.posFromIndex(r.end),{replacedWith:r.node})}},a=()=>{var n;i.value=b(A.fromTextArea(r.value,e.options)),t("update:cminstance",l(i)),null==(n=i.value)||n.on("change",o)};return s((()=>e.cminstance),(r=>{var n;r&&(o(e.cminstance),null==(n=e.cminstance)||n.setValue(e.value),t("ready",i))}),{deep:!0,immediate:!0}),y((()=>{a()})),{initialize:a,textarea:r}}}),H=["name","placeholder"];const R=N(I,[["render",function(e,t,r,n,i,o){return u(),h("textarea",{ref:"textarea",name:e.$props.name,placeholder:e.$props.placeholder},null,8,H)}]]),B={"update:value":()=>!0,change:(e,t)=>({value:e,cm:t}),input:()=>!0,ready:e=>e},j=["changes","scroll","beforeChange","cursorActivity","keyHandled","inputRead","electricInput","beforeSelectionChange","viewportChange","swapDoc","gutterClick","gutterContextMenu","focus","blur","refresh","optionChange","scrollCursorIntoView","update"],V={...B,...(()=>{const e={};return j.forEach((t=>{e[t]=(...e)=>e})),e})()},U={mode:"text",theme:"default",lineNumbers:!0,smartIndent:!0,indentUnit:2};const K=({props:e,cminstance:t,emit:r,internalInstance:n,content:i})=>{const o=a((()=>{var r;return e.merge?null==(r=l(t))?void 0:r.editor():l(t)}));return{listenerEvents:()=>{o.value.on("change",(t=>{const n=t.getValue();n===i.value&&""!==n||(i.value=n,r("update:value",i.value||""),r("input",i.value||" "),Promise.resolve().then((()=>{r("change",i.value,t)})),e.keepCursorInEnd&&function(e){Promise.resolve().then((()=>{const t=e.getScrollInfo();e.scrollTo(t.left,t.height)}))}(t))}));const t={};(()=>{const e=[];return Object.keys(null==n?void 0:n.vnode.props).forEach((t=>{if(t.startsWith("on")){const r=t.replace(t[2],t[2].toLowerCase()).slice(2);!B[r]&&e.push(r)}})),e})().filter((e=>!t[e]&&(t[e]=!0))).forEach((e=>{o.value.on(e,((...t)=>{r(e,...t)}))}))}}};const G=r({__name:"index",props:{value:{type:String,default:""},options:{type:Object,default:()=>U},globalOptions:{type:Object,default:()=>U},placeholder:{type:String,default:""},border:{type:Boolean,default:!1},width:{type:[String,Number],default:null},height:{type:[String,Number],default:null},originalStyle:{type:Boolean,default:!1},keepCursorInEnd:{type:Boolean,default:!1},merge:{type:Boolean,default:!1},name:{type:String,default:""},marker:{type:Function,default:()=>null},unseenLines:{type:Array,default:()=>[]}},emits:V,setup(e,{expose:t,emit:r}){var y,b;const w=e;"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");const t=Object(e);for(let r=1;r<arguments.length;r++){const e=arguments[r];if(null!=e)for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},writable:!0,configurable:!0});const x=n(null),k=n(""),C=i(F),S=n({...U,...w.globalOptions,...w.options}),M=o(),L=w.name||(null==(b=null==(y=null==M?void 0:M.parent)?void 0:y.type)?void 0:b.name)||void 0,T=n(null),D=a((()=>{var e;return w.merge?null==(e=l(x))?void 0:e.editor():l(x)})),{refresh:A,resize:O,destroy:N,containerHeight:_,reviseStyle:E}=function({props:e,cminstance:t,presetRef:r}){const i=n(null),o=n(null),s=a((()=>{var r;return e.merge?null==(r=l(t))?void 0:r.editor():l(t)})),c=()=>{v((()=>{var e;null==(e=s.value)||e.refresh()}))},u=()=>{var e;const t=null==(e=s.value)?void 0:e.getWrapperElement();null==t||t.remove()},h=()=>{const e=document.querySelector(".CodeMirror-gutters");return"0"!==(null==e?void 0:e.style.left.replace("px",""))};return{reload:()=>{var e,t,n;const i=null==(e=s.value)?void 0:e.getDoc().getHistory();null==(t=r.value)||t.initialize(),u(),null==(n=s.value)||n.getDoc().setHistory(i)},refresh:c,resize:(t=e.width,r=e.height)=>{var n;i.value=String(t).replace("px",""),o.value=String(r).replace("px","");const a=o.value;null==(n=s.value)||n.setSize(i.value,a)},destroy:u,containerHeight:o,reviseStyle:()=>{if(c(),!h())return;const e=setInterval((()=>{h()?c():clearInterval(e)}),60),t=setTimeout((()=>{clearInterval(e),clearTimeout(t)}),400)}}}({props:w,cminstance:x,presetRef:T}),{listenerEvents:W}=K({props:w,cminstance:x,emit:r,internalInstance:M,content:k}),z=()=>{void 0!==w.unseenLines&&void 0!==w.marker&&w.unseenLines.forEach((e=>{var t,r;const n=null==(t=x.value)?void 0:t.lineInfo(e);null==(r=x.value)||r.setGutterMarker(e,"breakpoints",null!=n&&n.gutterMarkers?null:w.marker())}))},I=()=>{W(),z(),O(w.width,w.height),r("ready",x.value),s([()=>w.width,()=>w.height],(([e,t])=>{O(e,t)}),{deep:!0})};return s((()=>w.options),(e=>{var t;for(const r in w.options)null==(t=D.value)||t.setOption(r,l(e[r]))}),{deep:!0}),s((()=>w.value),(e=>{(e=>{var t,r;e!==(null==(t=x.value)?void 0:t.getValue())&&(null==(r=x.value)||r.setValue(e),k.value=e,E()),z()})(e)})),s((()=>w.merge),(()=>{"fclog"!==w.options.mode&&"log"!==w.options.mode?w.merge?C.value=P:C.value=F:C.value=R}),{immediate:!0}),c((()=>{N()})),t({cminstance:x,resize:O,refresh:A,destroy:N}),(e,t)=>(u(),h("div",{class:g(["codemirror-container",{merge:e.$props.merge,bordered:e.$props.border||e.$props.merge&&!w.originalStyle,"width-auto":!e.$props.width||"100%"==e.$props.width,"height-auto":!e.$props.height||"100%"==e.$props.height,"original-style":w.originalStyle}]),style:m({height:l(_)+"px"})},[(u(),f(p(l(C)),d({ref_key:"presetRef",ref:T,cminstance:x.value,"onUpdate:cminstance":t[0]||(t[0]=e=>x.value=e),style:{height:"100%"}},{...e.$props,...e.$attrs,options:S.value,name:l(L),content:k.value},{onReady:I}),null,16,["cminstance"]))],6))}});!function(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&"undefined"!=typeof document){var n=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===r&&n.firstChild?n.insertBefore(i,n.firstChild):n.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}}(".codemirror-container {\n  position: relative;\n  display: inline-block;\n  height: 100%;\n  width: fit-content;\n  font-size: 12px;\n  overflow: hidden;\n}\n.codemirror-container.bordered {\n  border-radius: 4px;\n  border: 1px solid #dddddd;\n}\n.codemirror-container.width-auto {\n  width: 100%;\n}\n.codemirror-container.height-auto {\n  height: 100%;\n}\n.codemirror-container.height-auto .CodeMirror,\n.codemirror-container.height-auto .cm-s-default {\n  height: 100% !important;\n}\n.codemirror-container .editor_custom_link {\n  cursor: pointer;\n  color: #1474f1;\n  text-decoration: underline;\n}\n.codemirror-container .editor_custom_link:hover {\n  color: #04b4fa;\n}\n.codemirror-container:not(.original-style) .CodeMirror-lines .CodeMirror-placeholder.CodeMirror-line-like {\n  color: #666;\n}\n.codemirror-container:not(.original-style) .CodeMirror,\n.codemirror-container:not(.original-style) .CodeMirror-merge-pane {\n  height: 100%;\n  font-family: consolas !important;\n}\n.codemirror-container:not(.original-style) .CodeMirror-merge,\n.codemirror-container:not(.original-style) .CodeMirror-merge-right .CodeMirror {\n  height: 100%;\n  border: none !important;\n}\n.codemirror-container:not(.original-style) .c-editor--log__error {\n  color: #bb0606;\n  font-weight: bold;\n}\n.codemirror-container:not(.original-style) .c-editor--log__info {\n  color: #333333;\n  font-weight: bold;\n}\n.codemirror-container:not(.original-style) .c-editor--log__warning {\n  color: #ee9900;\n}\n.codemirror-container:not(.original-style) .c-editor--log__success {\n  color: #669600;\n}\n.codemirror-container:not(.original-style) .cm-header,\n.codemirror-container:not(.original-style) .cm-strong {\n  font-weight: bold;\n}\n");var q,$={};function X(){return q||(q=1,function(e){function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,l=r.tokenHooks,s=r.documentTypes||{},c=r.mediaTypes||{},u=r.mediaFeatures||{},h=r.mediaValueKeywords||{},f=r.propertyKeywords||{},d=r.nonStandardPropertyKeywords||{},p=r.fontProperties||{},g=r.counterDescriptors||{},m=r.colorKeywords||{},v=r.valueKeywords||{},y=r.allowNested,b=r.lineComment,w=!0===r.supportsAtComponent,x=!1!==t.highlightNonStandardPropertyKeywords;function k(e,t){return i=t,e}function C(e,t){var r=e.next();if(l[r]){var n=l[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),k("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?k(null,"compare"):'"'==r||"'"==r?(t.tokenize=S(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),k("atom","hash")):"!"==r?(e.match(/^\s*\w*/),k("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),k("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?k(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?k("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?k(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=M),k("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),k("property","word")):k(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),k("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?k("variable-2","variable-definition"):k("variable-2","variable")):e.match(/^\w+-/)?k("meta","meta"):void 0}function S(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),k("string","string")}}function M(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=S(")"),k(null,"(")}function L(e,t,r){this.type=e,this.indent=t,this.prev=r}function T(e,t,r,n){return e.context=new L(r,t.indentation()+(!1===n?0:a),e.context),r}function D(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function A(e,t,r){return _[r.context.type](e,t,r)}function O(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return A(e,t,r)}function N(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":m.hasOwnProperty(t)?"keyword":"variable"}var _={top:function(e,t,r){if("{"==e)return T(r,t,"block");if("}"==e&&r.context.prev)return D(r);if(w&&/@component/i.test(e))return T(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return T(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return T(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return T(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return T(r,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return T(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return f.hasOwnProperty(n)?(o="property","maybeprop"):d.hasOwnProperty(n)?(o=x?"string-2":"property","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?_.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?T(r,t,"prop"):A(e,t,r)},prop:function(e,t,r){if(";"==e)return D(r);if("{"==e&&y)return T(r,t,"propBlock");if("}"==e||"{"==e)return O(e,t,r);if("("==e)return T(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(t.current())){if("word"==e)N(t);else if("interpolation"==e)return T(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?D(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?O(e,t,r):")"==e?D(r):"("==e?T(r,t,"parens"):"interpolation"==e?T(r,t,"interpolation"):("word"==e&&N(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(o="variable-3",r.context.type):A(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&s.hasOwnProperty(t.current())?(o="tag",r.context.type):_.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return T(r,t,"atBlock_parens");if("}"==e||";"==e)return O(e,t,r);if("{"==e)return D(r)&&T(r,t,y?"block":"top");if("interpolation"==e)return T(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":c.hasOwnProperty(n)?"attribute":u.hasOwnProperty(n)?"property":h.hasOwnProperty(n)?"keyword":f.hasOwnProperty(n)?"property":d.hasOwnProperty(n)?x?"string-2":"property":v.hasOwnProperty(n)?"atom":m.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?O(e,t,r):"{"==e?D(r)&&T(r,t,y?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?D(r):"{"==e||"}"==e?O(e,t,r,2):_.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?T(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):A(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,D(r)):"word"==e?(o="@font-face"==r.stateArg&&!p.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!g.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?T(r,t,"top"):A(e,t,r)},at:function(e,t,r){return";"==e?D(r):"{"==e||"}"==e?O(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?D(r):"{"==e||";"==e?O(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new L(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||C)(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,"comment"!=i&&(t.state=_[t.state](i,e,t)),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:b,fold:"brace"}}));var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],l=t(a),s=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],c=t(s),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],h=t(u),f=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],d=t(f),p=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),g=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),m=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(m),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),w=r.concat(i).concat(a).concat(s).concat(u).concat(f).concat(m).concat(y);function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:d,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:d,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:d,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:l,propertyKeywords:h,nonStandardPropertyKeywords:d,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})}(k())),$}export{G as F,k as a,X as r};
//# sourceMappingURL=chunk.74ff160c.js.map
