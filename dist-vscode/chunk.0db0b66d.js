import{k as e,d as n,z as t,r as o,M as a,w as s,g as l,R as r,N as i,E as c,o as u,A as d,B as p,I as f,i as m,b as v,n as g,f as h,q as b,c as y,V as C,h as E,e as x,Y as B,O as w,C as k,a6 as T,T as M,K as I,aD as A,$ as S,_ as R,a0 as z,P}from"./index.05904f40.js";import{a8 as _,o as L,c as V,E as O,aq as $,ae as j,af as q,s as H,O as D,a0 as K,V as U}from"./chunk.8df321e8.js";import{E as N,a as F,b as W,c as X,d as Y}from"./chunk.505381c5.js";import{e as Z}from"./chunk.db8898e3.js";import{i as G}from"./chunk.6eac9d60.js";const J=e=>Array.from(e.querySelectorAll('a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])')).filter((e=>Q(e)&&(e=>"fixed"!==getComputedStyle(e).position&&null!==e.offsetParent)(e))),Q=e=>{if(e.tabIndex>0||0===e.tabIndex&&null!==e.getAttribute("tabIndex"))return!0;if(e.disabled)return!1;switch(e.nodeName){case"A":return!!e.href&&"ignore"!==e.rel;case"INPUT":return!("hidden"===e.type||"file"===e.type);case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},ee="_trap-focus-children",ne=[],te=e=>{if(0===ne.length)return;const n=ne[ne.length-1][ee];if(n.length>0&&e.code===_.tab){if(1===n.length)return e.preventDefault(),void(document.activeElement!==n[0]&&n[0].focus());const t=e.shiftKey,o=e.target===n[0],a=e.target===n[n.length-1];o&&t&&(e.preventDefault(),n[n.length-1].focus()),a&&!t&&(e.preventDefault(),n[0].focus())}},oe=n({name:"ElMessageBox",directives:{TrapFocus:{beforeMount(e){e[ee]=J(e),ne.push(e),ne.length<=1&&document.addEventListener("keydown",te)},updated(n){e((()=>{n[ee]=J(n)}))},unmounted(){ne.shift(),0===ne.length&&document.removeEventListener("keydown",te)}}},components:{ElButton:V,ElFocusTrap:Z,ElInput:N,ElOverlay:F,ElIcon:O,...$},inheritAttrs:!1,props:{buttonSize:{type:String,validator:G},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(n,{emit:c}){const{locale:u,zIndex:d,ns:p,size:f}=j("message-box",t((()=>n.buttonSize))),{t:m}=u,{nextZIndex:v}=d,g=o(!1),h=a({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:null,inputValidator:null,inputErrorMessage:"",message:null,modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:v()}),b=t((()=>{const e=h.type;return{[p.bm("icon",e)]:e&&q[e]}})),y=H(),C=H(),E=t((()=>h.icon||q[h.type]||"")),x=t((()=>!!h.message)),B=o(),w=o(),k=o(),T=o(),M=o(),I=t((()=>h.confirmButtonClass));s((()=>h.inputValue),(async t=>{await e(),"prompt"===n.boxType&&null!==t&&_()}),{immediate:!0}),s((()=>g.value),(t=>{var o,a;t&&("prompt"!==n.boxType&&(h.autofocus?k.value=null!=(a=null==(o=M.value)?void 0:o.$el)?a:B.value:k.value=B.value),h.zIndex=v()),"prompt"===n.boxType&&(t?e().then((()=>{var e;T.value&&T.value.$el&&(h.autofocus?k.value=null!=(e=L())?e:B.value:k.value=B.value)})):(h.editorErrorMessage="",h.validateError=!1))}));const A=t((()=>n.draggable));function S(){g.value&&(g.value=!1,e((()=>{h.action&&c("action",h.action)})))}W(B,w,A),l((async()=>{await e(),n.closeOnHashChange&&window.addEventListener("hashchange",S)})),r((()=>{n.closeOnHashChange&&window.removeEventListener("hashchange",S)}));const R=()=>{n.closeOnClickModal&&P(h.distinguishCancelAndClose?"close":"cancel")},z=Y(R),P=e=>{var t;("prompt"!==n.boxType||"confirm"!==e||_())&&(h.action=e,h.beforeClose?null==(t=h.beforeClose)||t.call(h,e,h,S):S())},_=()=>{if("prompt"===n.boxType){const e=h.inputPattern;if(e&&!e.test(h.inputValue||""))return h.editorErrorMessage=h.inputErrorMessage||m("el.messagebox.error"),h.validateError=!0,!1;const n=h.inputValidator;if("function"==typeof n){const e=n(h.inputValue);if(!1===e)return h.editorErrorMessage=h.inputErrorMessage||m("el.messagebox.error"),h.validateError=!0,!1;if("string"==typeof e)return h.editorErrorMessage=e,h.validateError=!0,!1}}return h.editorErrorMessage="",h.validateError=!1,!0},L=()=>{const e=T.value.$refs;return e.input||e.textarea},V=()=>{P("close")};return n.lockScroll&&X(g),{...i(h),ns:p,overlayEvent:z,visible:g,hasMessage:x,typeClass:b,contentId:y,inputId:C,btnSize:f,iconComponent:E,confirmButtonClasses:I,rootRef:B,focusStartRef:k,headerRef:w,inputRef:T,confirmRef:M,doClose:S,handleClose:V,onCloseRequested:()=>{n.closeOnPressEscape&&V()},handleWrapperClick:R,handleInputEnter:e=>{if("textarea"!==h.inputType)return e.preventDefault(),P("confirm")},handleAction:P,t:m}}}),ae=["aria-label","aria-describedby"],se=["aria-label"],le=["id"];var re=L(oe,[["render",function(e,n,t,o,a,s){const l=c("el-icon"),r=c("close"),i=c("el-input"),I=c("el-button"),A=c("el-focus-trap"),S=c("el-overlay");return u(),d(M,{name:"fade-in-linear",onAfterLeave:n[11]||(n[11]=n=>e.$emit("vanish")),persisted:""},{default:p((()=>[f(m(S,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:p((()=>[v("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:g(`${e.ns.namespace.value}-overlay-message-box`),onClick:n[8]||(n[8]=(...n)=>e.overlayEvent.onClick&&e.overlayEvent.onClick(...n)),onMousedown:n[9]||(n[9]=(...n)=>e.overlayEvent.onMousedown&&e.overlayEvent.onMousedown(...n)),onMouseup:n[10]||(n[10]=(...n)=>e.overlayEvent.onMouseup&&e.overlayEvent.onMouseup(...n))},[m(A,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:p((()=>[v("div",{ref:"rootRef",class:g([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:h(e.customStyle),tabindex:"-1",onClick:n[7]||(n[7]=b((()=>{}),["stop"]))},[null!==e.title&&void 0!==e.title?(u(),y("div",{key:0,ref:"headerRef",class:g(e.ns.e("header"))},[v("div",{class:g(e.ns.e("title"))},[e.iconComponent&&e.center?(u(),d(l,{key:0,class:g([e.ns.e("status"),e.typeClass])},{default:p((()=>[(u(),d(C(e.iconComponent)))])),_:1},8,["class"])):E("v-if",!0),v("span",null,x(e.title),1)],2),e.showClose?(u(),y("button",{key:0,type:"button",class:g(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:n[0]||(n[0]=n=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),onKeydown:n[1]||(n[1]=B(b((n=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),["prevent"]),["enter"]))},[m(l,{class:g(e.ns.e("close"))},{default:p((()=>[m(r)])),_:1},8,["class"])],42,se)):E("v-if",!0)],2)):E("v-if",!0),v("div",{id:e.contentId,class:g(e.ns.e("content"))},[v("div",{class:g(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(u(),d(l,{key:0,class:g([e.ns.e("status"),e.typeClass])},{default:p((()=>[(u(),d(C(e.iconComponent)))])),_:1},8,["class"])):E("v-if",!0),e.hasMessage?(u(),y("div",{key:1,class:g(e.ns.e("message"))},[w(e.$slots,"default",{},(()=>[e.dangerouslyUseHTMLString?(u(),d(C(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(u(),d(C(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:p((()=>[k(x(e.dangerouslyUseHTMLString?"":e.message),1)])),_:1},8,["for"]))]))],2)):E("v-if",!0)],2),f(v("div",{class:g(e.ns.e("input"))},[m(i,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":n[2]||(n[2]=n=>e.inputValue=n),type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:g({invalid:e.validateError}),onKeydown:B(e.handleInputEnter,["enter"])},null,8,["id","modelValue","type","placeholder","aria-invalid","class","onKeydown"]),v("div",{class:g(e.ns.e("errormsg")),style:h({visibility:e.editorErrorMessage?"visible":"hidden"})},x(e.editorErrorMessage),7)],2),[[T,e.showInput]])],10,le),v("div",{class:g(e.ns.e("btns"))},[e.showCancelButton?(u(),d(I,{key:0,loading:e.cancelButtonLoading,class:g([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:n[3]||(n[3]=n=>e.handleAction("cancel")),onKeydown:n[4]||(n[4]=B(b((n=>e.handleAction("cancel")),["prevent"]),["enter"]))},{default:p((()=>[k(x(e.cancelButtonText||e.t("el.messagebox.cancel")),1)])),_:1},8,["loading","class","round","size"])):E("v-if",!0),f(m(I,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,class:g([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:n[5]||(n[5]=n=>e.handleAction("confirm")),onKeydown:n[6]||(n[6]=B(b((n=>e.handleAction("confirm")),["prevent"]),["enter"]))},{default:p((()=>[k(x(e.confirmButtonText||e.t("el.messagebox.confirm")),1)])),_:1},8,["loading","class","round","disabled","size"]),[[T,e.showConfirmButton]])],2)],6)])),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,ae)])),_:3},8,["z-index","overlay-class","mask"]),[[T,e.visible]])])),_:3})}],["__file","index.vue"]]);const ie=new Map,ce=(e,n,t=null)=>{const o=m(re,e,P(e.message)||A(e.message)?{default:P(e.message)?e.message:()=>e.message}:null);return o.appContext=t,S(o,n),(e=>{let n=document.body;return e.appendTo&&(I(e.appendTo)&&(n=document.querySelector(e.appendTo)),U(e.appendTo)&&(n=e.appendTo),U(n)||(n=document.body)),n})(e).appendChild(n.firstElementChild),o.component},ue=(e,n)=>{const t=document.createElement("div");e.onVanish=()=>{S(null,t),ie.delete(a)},e.onAction=n=>{const t=ie.get(a);let s;s=e.showInput?{value:a.inputValue,action:n}:n,e.callback?e.callback(s,o.proxy):"cancel"===n||"close"===n?e.distinguishCancelAndClose&&"cancel"!==n?t.reject("close"):t.reject("cancel"):t.resolve(s)};const o=ce(e,t,n),a=o.proxy;for(const s in e)R(e,s)&&!R(a.$props,s)&&(a[s]=e[s]);return a.visible=!0,a};function de(e,n=null){if(!D)return Promise.reject();let t;return I(e)||A(e)?e={message:e}:t=e.callback,new Promise(((o,a)=>{const s=ue(e,null!=n?n:de._context);ie.set(s,{options:e,callback:t,resolve:o,reject:a})}))}const pe={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};["alert","confirm","prompt"].forEach((e=>{de[e]=function(e){return(n,t,o,a)=>{let s="";return z(t)?(o=t,s=""):s=K(t)?"":t,de(Object.assign({title:s,message:n,type:"",...pe[e]},o,{boxType:e}),a)}}(e)})),de.close=()=>{ie.forEach(((e,n)=>{n.doClose()})),ie.clear()},de._context=null;const fe=de;fe.install=e=>{fe._context=e._context,e.config.globalProperties.$msgbox=fe,e.config.globalProperties.$messageBox=fe,e.config.globalProperties.$alert=fe.alert,e.config.globalProperties.$confirm=fe.confirm,e.config.globalProperties.$prompt=fe.prompt};const me=fe;export{me as E};
//# sourceMappingURL=chunk.0db0b66d.js.map
