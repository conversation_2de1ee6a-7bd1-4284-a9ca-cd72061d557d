{"version": 3, "file": "chunk.0b77532e.js", "sources": ["../node_modules/element-plus/es/components/popover/src/popover.mjs", "../node_modules/element-plus/es/components/popover/src/popover2.mjs", "../node_modules/element-plus/es/components/popover/src/directive.mjs", "../node_modules/element-plus/es/components/popover/index.mjs"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport '../../tooltip/index.mjs';\nimport '../../dropdown/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useTooltipTriggerProps } from '../../tooltip/src/trigger.mjs';\nimport { dropdownProps } from '../../dropdown/src/dropdown.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\n\nconst popoverProps = buildProps({\n  trigger: useTooltipTriggerProps.trigger,\n  placement: dropdownProps.placement,\n  disabled: useTooltipTriggerProps.disabled,\n  visible: useTooltipContentProps.visible,\n  transition: useTooltipContentProps.transition,\n  popperOptions: dropdownProps.popperOptions,\n  tabindex: dropdownProps.tabindex,\n  content: useTooltipContentProps.content,\n  popperStyle: useTooltipContentProps.popperStyle,\n  popperClass: useTooltipContentProps.popperClass,\n  enterable: {\n    ...useTooltipContentProps.enterable,\n    default: true\n  },\n  effect: {\n    ...useTooltipContentProps.effect,\n    default: \"light\"\n  },\n  teleported: useTooltipContentProps.teleported,\n  title: String,\n  width: {\n    type: [String, Number],\n    default: 150\n  },\n  offset: {\n    type: Number,\n    default: void 0\n  },\n  showAfter: {\n    type: Number,\n    default: 0\n  },\n  hideAfter: {\n    type: Number,\n    default: 200\n  },\n  autoClose: {\n    type: Number,\n    default: 0\n  },\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  \"onUpdate:visible\": {\n    type: Function\n  }\n});\nconst popoverEmits = {\n  \"update:visible\": (value) => isBoolean(value),\n  \"before-enter\": () => true,\n  \"before-leave\": () => true,\n  \"after-enter\": () => true,\n  \"after-leave\": () => true\n};\n\nexport { popoverEmits, popoverProps };\n//# sourceMappingURL=popover.mjs.map\n", "import { defineComponent, computed, ref, unref, openBlock, createBlock, mergeProps, withCtx, createElementBlock, normalizeClass, toDisplayString, createCommentVNode, renderSlot, createTextVNode } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { popoverProps, popoverEmits } from './popover.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\n\nconst updateEventKeyRaw = `onUpdate:visible`;\nconst __default__ = defineComponent({\n  name: \"ElPopover\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: popoverProps,\n  emits: popoverEmits,\n  setup(__props, { expose, emit }) {\n    const props = __props;\n    const onUpdateVisible = computed(() => {\n      return props[updateEventKeyRaw];\n    });\n    const ns = useNamespace(\"popover\");\n    const tooltipRef = ref();\n    const popperRef = computed(() => {\n      var _a;\n      return (_a = unref(tooltipRef)) == null ? void 0 : _a.popperRef;\n    });\n    const style = computed(() => {\n      return [\n        {\n          width: addUnit(props.width)\n        },\n        props.popperStyle\n      ];\n    });\n    const kls = computed(() => {\n      return [ns.b(), props.popperClass, { [ns.m(\"plain\")]: !!props.content }];\n    });\n    const gpuAcceleration = computed(() => {\n      return props.transition === `${ns.namespace.value}-fade-in-linear`;\n    });\n    const hide = () => {\n      var _a;\n      (_a = tooltipRef.value) == null ? void 0 : _a.hide();\n    };\n    const beforeEnter = () => {\n      emit(\"before-enter\");\n    };\n    const beforeLeave = () => {\n      emit(\"before-leave\");\n    };\n    const afterEnter = () => {\n      emit(\"after-enter\");\n    };\n    const afterLeave = () => {\n      emit(\"update:visible\", false);\n      emit(\"after-leave\");\n    };\n    expose({\n      popperRef,\n      hide\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), mergeProps({\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef\n      }, _ctx.$attrs, {\n        trigger: _ctx.trigger,\n        placement: _ctx.placement,\n        disabled: _ctx.disabled,\n        visible: _ctx.visible,\n        transition: _ctx.transition,\n        \"popper-options\": _ctx.popperOptions,\n        tabindex: _ctx.tabindex,\n        content: _ctx.content,\n        offset: _ctx.offset,\n        \"show-after\": _ctx.showAfter,\n        \"hide-after\": _ctx.hideAfter,\n        \"auto-close\": _ctx.autoClose,\n        \"show-arrow\": _ctx.showArrow,\n        \"aria-label\": _ctx.title,\n        effect: _ctx.effect,\n        enterable: _ctx.enterable,\n        \"popper-class\": unref(kls),\n        \"popper-style\": unref(style),\n        teleported: _ctx.teleported,\n        persistent: _ctx.persistent,\n        \"gpu-acceleration\": unref(gpuAcceleration),\n        \"onUpdate:visible\": unref(onUpdateVisible),\n        onBeforeShow: beforeEnter,\n        onBeforeHide: beforeLeave,\n        onShow: afterEnter,\n        onHide: afterLeave\n      }), {\n        content: withCtx(() => [\n          _ctx.title ? (openBlock(), createElementBlock(\"div\", {\n            key: 0,\n            class: normalizeClass(unref(ns).e(\"title\")),\n            role: \"title\"\n          }, toDisplayString(_ctx.title), 3)) : createCommentVNode(\"v-if\", true),\n          renderSlot(_ctx.$slots, \"default\", {}, () => [\n            createTextVNode(toDisplayString(_ctx.content), 1)\n          ])\n        ]),\n        default: withCtx(() => [\n          _ctx.$slots.reference ? renderSlot(_ctx.$slots, \"reference\", { key: 0 }) : createCommentVNode(\"v-if\", true)\n        ]),\n        _: 3\n      }, 16, [\"trigger\", \"placement\", \"disabled\", \"visible\", \"transition\", \"popper-options\", \"tabindex\", \"content\", \"offset\", \"show-after\", \"hide-after\", \"auto-close\", \"show-arrow\", \"aria-label\", \"effect\", \"enterable\", \"popper-class\", \"popper-style\", \"teleported\", \"persistent\", \"gpu-acceleration\", \"onUpdate:visible\"]);\n    };\n  }\n});\nvar Popover = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"popover.vue\"]]);\n\nexport { Popover as default };\n//# sourceMappingURL=popover2.mjs.map\n", "const attachEvents = (el, binding) => {\n  const popperComponent = binding.arg || binding.value;\n  const popover = popperComponent == null ? void 0 : popperComponent.popperRef;\n  if (popover) {\n    popover.triggerRef = el;\n  }\n};\nvar PopoverDirective = {\n  mounted(el, binding) {\n    attachEvents(el, binding);\n  },\n  updated(el, binding) {\n    attachEvents(el, binding);\n  }\n};\nconst VPopover = \"popover\";\n\nexport { VPopover, PopoverDirective as default };\n//# sourceMappingURL=directive.mjs.map\n", "import '../../utils/index.mjs';\nimport Popover from './src/popover2.mjs';\nimport PopoverDirective, { VPopover } from './src/directive.mjs';\nexport { popoverEmits, popoverProps } from './src/popover.mjs';\nimport { withInstallDirective, withInstall } from '../../utils/vue/install.mjs';\n\nconst ElPopoverDirective = withInstallDirective(PopoverDirective, VPopover);\nconst ElPopover = withInstall(Popover, {\n  directive: ElPopoverDirective\n});\n\nexport { ElPopover, ElPopoverDirective, ElPopover as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["popoverProps", "buildProps", "trigger", "useTooltipTriggerProps", "placement", "dropdownProps", "disabled", "visible", "useTooltipContentProps", "transition", "popperOptions", "tabindex", "content", "popperStyle", "popperClass", "enterable", "default", "effect", "teleported", "title", "String", "width", "type", "Number", "offset", "showAfter", "hideAfter", "autoClose", "showArrow", "Boolean", "persistent", "Function", "popoverEmits", "value", "isBoolean", "__default__", "defineComponent", "name", "_sfc_main", "props", "emits", "setup", "__props", "expose", "emit", "onUpdateVisible", "computed", "ns", "useNamespace", "tooltipRef", "ref", "popperRef", "_a", "unref", "style", "addUnit", "kls", "b", "m", "gpuAcceleration", "namespace", "beforeEnter", "beforeLeave", "afterEnter", "afterLeave", "hide", "_ctx", "_cache", "openBlock", "createBlock", "ElTooltip", "mergeProps", "ref_key", "$attrs", "onBeforeShow", "onBeforeHide", "onShow", "onHide", "withCtx", "createElementBlock", "key", "class", "normalizeClass", "e", "role", "toDisplayString", "createCommentVNode", "renderSlot", "$slots", "createTextVNode", "reference", "_", "attachEvents", "el", "binding", "popperComponent", "arg", "popover", "triggerRef", "ElPopover", "withInstall", "directive", "withInstallDirective", "mounted", "updated"], "mappings": "+QASA,MAAMA,EAAeC,EAAW,CAC9BC,QAASC,EAAuBD,QAChCE,UAAWC,EAAcD,UACzBE,SAAUH,EAAuBG,SACjCC,QAASC,EAAuBD,QAChCE,WAAYD,EAAuBC,WACnCC,cAAeL,EAAcK,cAC7BC,SAAUN,EAAcM,SACxBC,QAASJ,EAAuBI,QAChCC,YAAaL,EAAuBK,YACpCC,YAAaN,EAAuBM,YACpCC,UAAW,IACNP,EAAuBO,UAC1BC,SAAS,GAEXC,OAAQ,IACHT,EAAuBS,OAC1BD,QAAS,SAEXE,WAAYV,EAAuBU,WACnCC,MAAOC,OACPC,MAAO,CACLC,KAAM,CAACF,OAAQG,QACfP,QAAS,KAEXQ,OAAQ,CACNF,KAAMC,OACNP,aAAS,GAEXS,UAAW,CACTH,KAAMC,OACNP,QAAS,GAEXU,UAAW,CACTJ,KAAMC,OACNP,QAAS,KAEXW,UAAW,CACTL,KAAMC,OACNP,QAAS,GAEXY,UAAW,CACTN,KAAMO,QACNb,SAAS,GAEXc,WAAY,CACVR,KAAMO,QACNb,SAAS,GAEX,mBAAoB,CAClBM,KAAMS,YAGJC,EAAe,CACnB,iBAAmBC,GAAUC,EAAUD,GACvC,eAAgB,KAAM,EACtB,eAAgB,KAAM,EACtB,cAAe,KAAM,EACrB,cAAe,KAAM,GCzDjBE,EAAcC,EAAgB,CAClCC,KAAM,cAEFC,EAA4CF,EAAA,IAC7CD,EACHI,MAAOvC,EACPwC,MAAOR,EACP,KAAAS,CAAMC,GAASC,OAAEA,EAAAC,KAAQA,IACvB,MAAML,EAAQG,EACRG,EAAkBC,GAAS,IACxBP,EAXa,sBAahBQ,EAAKC,EAAa,WAClBC,EAAaC,IACbC,EAAYL,GAAS,KACrB,IAAAM,EACJ,OAAmC,OAA3BA,EAAKC,EAAMJ,SAAuB,EAASG,EAAGD,SAAA,IAElDG,EAAQR,GAAS,IACd,CACL,CACEzB,MAAOkC,EAAQhB,EAAMlB,QAEvBkB,EAAM1B,eAGJ2C,EAAMV,GAAS,IACZ,CAACC,EAAGU,IAAKlB,EAAMzB,YAAa,CAAE,CAACiC,EAAGW,EAAE,YAAanB,EAAM3B,YAE1D+C,EAAkBb,GAAS,IACxBP,EAAM9B,aAAe,GAAGsC,EAAGa,UAAU3B,yBAMxC4B,EAAc,KAClBjB,EAAK,eAAc,EAEfkB,EAAc,KAClBlB,EAAK,eAAc,EAEfmB,EAAa,KACjBnB,EAAK,cAAa,EAEdoB,EAAa,KACjBpB,EAAK,kBAAkB,GACvBA,EAAK,cAAa,EAMb,OAJAD,EAAA,CACLQ,YACAc,KAnBW,KACP,IAAAb,EACuB,OAA1BA,EAAKH,EAAWhB,QAA0BmB,EAAGa,UAmBzC,CAACC,EAAMC,KACLC,IAAaC,EAAYhB,EAAMiB,GAAYC,EAAW,CAC3DC,QAAS,aACTtB,IAAKD,GACJiB,EAAKO,OAAQ,CACdvE,QAASgE,EAAKhE,QACdE,UAAW8D,EAAK9D,UAChBE,SAAU4D,EAAK5D,SACfC,QAAS2D,EAAK3D,QACdE,WAAYyD,EAAKzD,WACjB,iBAAkByD,EAAKxD,cACvBC,SAAUuD,EAAKvD,SACfC,QAASsD,EAAKtD,QACdY,OAAQ0C,EAAK1C,OACb,aAAc0C,EAAKzC,UACnB,aAAcyC,EAAKxC,UACnB,aAAcwC,EAAKvC,UACnB,aAAcuC,EAAKtC,UACnB,aAAcsC,EAAK/C,MACnBF,OAAQiD,EAAKjD,OACbF,UAAWmD,EAAKnD,UAChB,eAAgBsC,EAAMG,GACtB,eAAgBH,EAAMC,GACtBpC,WAAYgD,EAAKhD,WACjBY,WAAYoC,EAAKpC,WACjB,mBAAoBuB,EAAMM,GAC1B,mBAAoBN,EAAMR,GAC1B6B,aAAcb,EACdc,aAAcb,EACdc,OAAQb,EACRc,OAAQb,IACN,CACFpD,QAASkE,GAAQ,IAAM,CACrBZ,EAAK/C,OAASiD,IAAaW,EAAmB,MAAO,CACnDC,IAAK,EACLC,MAAOC,EAAe7B,EAAMN,GAAIoC,EAAE,UAClCC,KAAM,SACLC,EAAgBnB,EAAK/C,OAAQ,IAAMmE,EAAmB,QAAQ,GACjEC,EAAWrB,EAAKsB,OAAQ,UAAW,CAAE,GAAE,IAAM,CAC3CC,EAAgBJ,EAAgBnB,EAAKtD,SAAU,SAGnDI,QAAS8D,GAAQ,IAAM,CACrBZ,EAAKsB,OAAOE,UAAYH,EAAWrB,EAAKsB,OAAQ,YAAa,CAAER,IAAK,IAAOM,EAAmB,QAAQ,MAExGK,EAAG,GACF,GAAI,CAAC,UAAW,YAAa,WAAY,UAAW,aAAc,iBAAkB,WAAY,UAAW,SAAU,aAAc,aAAc,aAAc,aAAc,aAAc,SAAU,YAAa,eAAgB,eAAgB,aAAc,aAAc,mBAAoB,qBAExS,IC/GH,MAAMC,EAAe,CAACC,EAAIC,KAClB,MAAAC,EAAkBD,EAAQE,KAAOF,EAAQ7D,MACzCgE,EAA6B,MAAnBF,OAA0B,EAASA,EAAgB5C,UAC/D8C,IACFA,EAAQC,WAAaL,EACtB,EAUH,MCRMM,EAAYC,IF0GwB9D,EAAW,CAAC,CAAC,SAAU,iBE1G1B,CACrC+D,UAFyBC,EDCJ,CACrB,OAAAC,CAAQV,EAAIC,GACVF,EAAaC,EAAIC,EAClB,EACD,OAAAU,CAAQX,EAAIC,GACVF,EAAaC,EAAIC,EAClB,GAEc", "x_google_ignoreList": [0, 1, 2, 3]}