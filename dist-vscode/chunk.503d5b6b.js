import{_ as e}from"./chunk.25a51fc3.js";import{E as t,a as n,b as o}from"./chunk.884698ee.js";import"./chunk.c5fb43ac.js";import"./chunk.615a7c87.js";import{d as s,o as i,c,i as l,B as r,F as a,h as u,p as f,j as p,b as d,C as y}from"./index.7c7944d0.js";const h=e=>(f("data-v-fd70df59"),e=e(),p(),e),m={class:"shortcut-div"},g=h((()=>d("span",{class:"shortcut-div-link"},[d("i",{class:"sy-gicon-kuaijiejian",style:{"margin-right":"10px"}}),y("快捷键 ")],-1))),k=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"移动"),y(),d("span",null,[d("i",{class:"iconfont sy-gicon-kongge"}),y(" + "),d("i",{class:"iconfont sy-gicon-mouseM"})])],-1))),w=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"放大"),d("span",null,[d("i",{class:"iconfont sy-gicon-vuesax-linear-command-square"}),y(" + "),d("i",{class:"iconfont sy-gicon-plus_app"})])],-1))),v=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"缩小"),d("span",null,[d("i",{class:"iconfont sy-gicon-vuesax-linear-command-square"}),y(" + "),d("i",{class:"iconfont sy-gicon-jian1"})])],-1))),O=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"搜索"),d("span",null,[d("i",{class:"iconfont sy-gicon-vuesax-linear-command-square"}),y(" + "),d("i",{class:"iconfont"},"F")])],-1))),_=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"切片"),d("span",null,[d("i",{class:"iconfont sy-gicon-option"}),y(" + "),d("i",{class:"iconfont"},"S")])],-1))),E=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"颜色"),d("span",null,[d("i",{class:"iconfont sy-gicon-option"}),y(" + "),d("i",{class:"iconfont"},"C")])],-1))),b=h((()=>d("div",{class:"shortcut-item"},[d("span",null,"历史"),d("span",null,[d("i",{class:"iconfont sy-gicon-option"}),y(" + "),d("i",{class:"iconfont"},"h")])],-1))),j=e(s({__name:"shortcut",props:{type:{}},setup:e=>(e,s)=>{const f=t,p=n,d=o;return i(),c("div",m,[l(d,{trigger:"click"},{dropdown:r((()=>[l(p,null,{default:r((()=>[l(f,null,{default:r((()=>[k])),_:1}),l(f,null,{default:r((()=>[w])),_:1}),l(f,null,{default:r((()=>[v])),_:1}),"detail"===e.type?(i(),c(a,{key:0},[l(f,null,{default:r((()=>[O])),_:1}),l(f,null,{default:r((()=>[_])),_:1}),l(f,null,{default:r((()=>[E])),_:1}),l(f,null,{default:r((()=>[b])),_:1})],64)):u("",!0)])),_:1})])),default:r((()=>[g])),_:1})])}}),[["__scopeId","data-v-fd70df59"]]),K="undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().indexOf("firefox")>0;function x(e,t,n,o){e.addEventListener?e.addEventListener(t,n,o):e.attachEvent&&e.attachEvent("on".concat(t),n)}function C(e,t,n,o){e.removeEventListener?e.removeEventListener(t,n,o):e.deachEvent&&e.deachEvent("on".concat(t),n)}function L(e,t){const n=t.slice(0,t.length-1);for(let o=0;o<n.length;o++)n[o]=e[n[o].toLowerCase()];return n}function A(e){"string"!=typeof e&&(e="");const t=(e=e.replace(/\s/g,"")).split(",");let n=t.lastIndexOf("");for(;n>=0;)t[n-1]+=",",t.splice(n,1),n=t.lastIndexOf("");return t}const S={backspace:8,"⌫":8,tab:9,clear:12,enter:13,"↩":13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"⇪":20,",":188,".":190,"/":191,"`":192,"-":K?173:189,"=":K?61:187,";":K?59:186,"'":222,"[":219,"]":221,"\\":220},P={"⇧":16,shift:16,"⌥":18,alt:18,option:18,"⌃":17,ctrl:17,control:17,"⌘":91,cmd:91,command:91},M={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},I={16:!1,18:!1,17:!1,91:!1},T={};for(let W=1;W<20;W++)S["f".concat(W)]=111+W;let q=[],B=null,D="all";const F=new Map,N=e=>S[e.toLowerCase()]||P[e.toLowerCase()]||e.toUpperCase().charCodeAt(0);function U(e){D=e||"all"}function z(){return D||"all"}function G(e){if(void 0===e)Object.keys(T).forEach((e=>{Array.isArray(T[e])&&T[e].forEach((e=>R(e))),delete T[e]})),J(null);else if(Array.isArray(e))e.forEach((e=>{e.key&&R(e)}));else if("object"==typeof e)e.key&&R(e);else if("string"==typeof e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];let[s,i]=n;"function"==typeof s&&(i=s,s=""),R({key:e,scope:s,method:i,splitKey:"+"})}}const R=e=>{let{key:t,scope:n,method:o,splitKey:s="+"}=e;A(t).forEach((e=>{const t=e.split(s),i=t.length,c=t[i-1],l="*"===c?"*":N(c);if(!T[l])return;n||(n=z());const r=i>1?L(P,t):[],a=[];T[l]=T[l].filter((e=>{const t=(!o||e.method===o)&&e.scope===n&&function(e,t){const n=e.length>=t.length?e:t,o=e.length>=t.length?t:e;let s=!0;for(let i=0;i<n.length;i++)-1===o.indexOf(n[i])&&(s=!1);return s}(e.mods,r);return t&&a.push(e.element),!t})),a.forEach((e=>J(e)))}))};function V(e,t,n,o){if(t.element!==o)return;let s;if(t.scope===n||"all"===t.scope){s=t.mods.length>0;for(const e in I)Object.prototype.hasOwnProperty.call(I,e)&&(!I[e]&&t.mods.indexOf(+e)>-1||I[e]&&-1===t.mods.indexOf(+e))&&(s=!1);(0!==t.mods.length||I[16]||I[18]||I[17]||I[91])&&!s&&"*"!==t.shortcut||(t.keys=[],t.keys=t.keys.concat(q),!1===t.method(e,t)&&(e.preventDefault?e.preventDefault():e.returnValue=!1,e.stopPropagation&&e.stopPropagation(),e.cancelBubble&&(e.cancelBubble=!0)))}}function X(e,t){const n=T["*"];let o=e.keyCode||e.which||e.charCode;if(!H.filter.call(this,e))return;if(93!==o&&224!==o||(o=91),-1===q.indexOf(o)&&229!==o&&q.push(o),["ctrlKey","altKey","shiftKey","metaKey"].forEach((t=>{const n=M[t];e[t]&&-1===q.indexOf(n)?q.push(n):!e[t]&&q.indexOf(n)>-1?q.splice(q.indexOf(n),1):"metaKey"===t&&e[t]&&3===q.length&&(e.ctrlKey||e.shiftKey||e.altKey||(q=q.slice(q.indexOf(n))))})),o in I){I[o]=!0;for(const e in P)P[e]===o&&(H[e]=!0);if(!n)return}for(const l in I)Object.prototype.hasOwnProperty.call(I,l)&&(I[l]=e[M[l]]);e.getModifierState&&(!e.altKey||e.ctrlKey)&&e.getModifierState("AltGraph")&&(-1===q.indexOf(17)&&q.push(17),-1===q.indexOf(18)&&q.push(18),I[17]=!0,I[18]=!0);const s=z();if(n)for(let l=0;l<n.length;l++)n[l].scope===s&&("keydown"===e.type&&n[l].keydown||"keyup"===e.type&&n[l].keyup)&&V(e,n[l],s,t);if(!(o in T))return;const i=T[o],c=i.length;for(let l=0;l<c;l++)if(("keydown"===e.type&&i[l].keydown||"keyup"===e.type&&i[l].keyup)&&i[l].key){const n=i[l],{splitKey:o}=n,c=n.key.split(o),r=[];for(let e=0;e<c.length;e++)r.push(N(c[e]));r.sort().join("")===q.sort().join("")&&V(e,n,s,t)}}function H(e,t,n){q=[];const o=A(e);let s=[],i="all",c=document,l=0,r=!1,a=!0,u="+",f=!1,p=!1;for(void 0===n&&"function"==typeof t&&(n=t),"[object Object]"===Object.prototype.toString.call(t)&&(t.scope&&(i=t.scope),t.element&&(c=t.element),t.keyup&&(r=t.keyup),void 0!==t.keydown&&(a=t.keydown),void 0!==t.capture&&(f=t.capture),"string"==typeof t.splitKey&&(u=t.splitKey),!0===t.single&&(p=!0)),"string"==typeof t&&(i=t),p&&G(e,i);l<o.length;l++)s=[],(e=o[l].split(u)).length>1&&(s=L(P,e)),(e="*"===(e=e[e.length-1])?"*":N(e))in T||(T[e]=[]),T[e].push({keyup:r,keydown:a,scope:i,mods:s,shortcut:o[l],method:n,key:o[l],splitKey:u,element:c});if(void 0!==c&&window){if(!F.has(c)){const e=function(){return X(arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event,c)},t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event;X(e,c),function(e){let t=e.keyCode||e.which||e.charCode;const n=q.indexOf(t);if(n>=0&&q.splice(n,1),e.key&&"meta"===e.key.toLowerCase()&&q.splice(0,q.length),93!==t&&224!==t||(t=91),t in I){I[t]=!1;for(const e in P)P[e]===t&&(H[e]=!1)}}(e)};F.set(c,{keydownListener:e,keyupListenr:t,capture:f}),x(c,"keydown",e,f),x(c,"keyup",t,f)}if(!B){const e=()=>{q=[]};B={listener:e,capture:f},x(window,"focus",e,f)}}}function J(e){const t=Object.values(T).flat();if(t.findIndex((t=>{let{element:n}=t;return n===e}))<0){const{keydownListener:t,keyupListenr:n,capture:o}=F.get(e)||{};t&&n&&(C(e,"keyup",n,o),C(e,"keydown",t,o),F.delete(e))}if(t.length<=0||F.size<=0){if(Object.keys(F).forEach((e=>{const{keydownListener:t,keyupListenr:n,capture:o}=F.get(e)||{};t&&n&&(C(e,"keyup",n,o),C(e,"keydown",t,o),F.delete(e))})),F.clear(),Object.keys(T).forEach((e=>delete T[e])),B){const{listener:e,capture:t}=B;C(window,"focus",e,t),B=null}}}const Q={getPressedKeyString:function(){return q.map((e=>{return t=e,Object.keys(S).find((e=>S[e]===t))||(e=>Object.keys(P).find((t=>P[t]===e)))(e)||String.fromCharCode(e);var t}))},setScope:U,getScope:z,deleteScope:function(e,t){let n,o;e||(e=z());for(const s in T)if(Object.prototype.hasOwnProperty.call(T,s))for(n=T[s],o=0;o<n.length;)if(n[o].scope===e){n.splice(o,1).forEach((e=>{let{element:t}=e;return J(t)}))}else o++;z()===e&&U(t||"all")},getPressedKeyCodes:function(){return q.slice(0)},getAllKeyCodes:function(){const e=[];return Object.keys(T).forEach((t=>{T[t].forEach((t=>{let{key:n,scope:o,mods:s,shortcut:i}=t;e.push({scope:o,shortcut:i,mods:s,keys:n.split("+").map((e=>N(e)))})}))})),e},isPressed:function(e){return"string"==typeof e&&(e=N(e)),-1!==q.indexOf(e)},filter:function(e){const t=e.target||e.srcElement,{tagName:n}=t;let o=!0;return!t.isContentEditable&&("INPUT"!==n&&"TEXTAREA"!==n&&"SELECT"!==n||t.readOnly)||(o=!1),o},trigger:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";Object.keys(T).forEach((n=>{T[n].filter((n=>n.scope===t&&n.shortcut===e)).forEach((e=>{e&&e.method&&e.method()}))}))},unbind:G,keyMap:S,modifier:P,modifierMap:M};for(const W in Q)Object.prototype.hasOwnProperty.call(Q,W)&&(H[W]=Q[W]);if("undefined"!=typeof window){const e=window.hotkeys;H.noConflict=t=>(t&&window.hotkeys===H&&(window.hotkeys=e),H),window.hotkeys=H}export{j as S,H as h};
//# sourceMappingURL=chunk.503d5b6b.js.map
