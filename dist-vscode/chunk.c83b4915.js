import{r as e,M as n,a$ as t,N as o,d as l,a4 as s,B as a,I as i,i as r,a6 as u,T as d,k as c,K as v,a0 as m,a1 as b,b0 as g}from"./index.05904f40.js";import{ae as f,X as p,O as x,aQ as y,U as k}from"./chunk.8df321e8.js";function C(c){let v;const m=e(!1),b=n({...c,originalPosition:"",originalOverflow:"",visible:!1});function g(){var e,n;null==(n=null==(e=C.$el)?void 0:e.parentNode)||n.removeChild(C.$el)}function x(){if(!m.value)return;const e=b.parent;m.value=!1,e.vLoadingAddClassList=void 0,function(){const e=b.parent,n=C.ns;if(!e.vLoadingAddClassList){let t=e.getAttribute("loading-number");t=Number.parseInt(t)-1,t?e.setAttribute("loading-number",t.toString()):(p(e,n.bm("parent","relative")),e.removeAttribute("loading-number")),p(e,n.bm("parent","hidden"))}g(),k.unmount()}()}const y=l({name:"ElLoading",setup(e,{expose:n}){const{ns:t,zIndex:o}=f("loading");return n({ns:t,zIndex:o}),()=>{const e=b.spinner||b.svg,n=s("svg",{class:"circular",viewBox:b.svgViewBox?b.svgViewBox:"0 0 50 50",...e?{innerHTML:e}:{}},[s("circle",{class:"path",cx:"25",cy:"25",r:"20",fill:"none"})]),o=b.text?s("p",{class:t.b("text")},[b.text]):void 0;return s(d,{name:t.b("fade"),onAfterLeave:x},{default:a((()=>[i(r("div",{style:{backgroundColor:b.background||""},class:[t.b("mask"),b.customClass,b.fullscreen?"is-fullscreen":""]},[s("div",{class:t.b("spinner")},[n,o])]),[[u,b.visible]])]))})}}}),k=t(y),C=k.mount(document.createElement("div"));return{...o(b),setText:function(e){b.text=e},removeElLoadingChild:g,close:function(){var e;c.beforeClose&&!c.beforeClose()||(m.value=!0,clearTimeout(v),v=window.setTimeout(x,400),b.visible=!1,null==(e=c.closed)||e.call(c))},handleAfterLeave:x,vm:C,get $el(){return C.$el}}}let w;const L=function(e={}){if(!x)return;const n=h(e);if(n.fullscreen&&w)return w;const t=C({...n,closed:()=>{var e;null==(e=n.closed)||e.call(n),n.fullscreen&&(w=void 0)}});A(n,n.parent,t),$(n,n.parent,t),n.parent.vLoadingAddClassList=()=>$(n,n.parent,t);let o=n.parent.getAttribute("loading-number");return o=o?`${Number.parseInt(o)+1}`:"1",n.parent.setAttribute("loading-number",o),n.parent.appendChild(t.$el),c((()=>t.visible.value=n.visible)),n.fullscreen&&(w=t),t},h=e=>{var n,t,o,l;let s;return s=v(e.target)?null!=(n=document.querySelector(e.target))?n:document.body:e.target||document.body,{parent:s===document.body||e.body?document.body:s,background:e.background||"",svg:e.svg||"",svgViewBox:e.svgViewBox||"",spinner:e.spinner||!1,text:e.text||"",fullscreen:s===document.body&&(null==(t=e.fullscreen)||t),lock:null!=(o=e.lock)&&o,customClass:e.customClass||"",visible:null==(l=e.visible)||l,target:s}},A=async(e,n,t)=>{const{nextZIndex:o}=t.vm.zIndex||t.vm._.exposed.zIndex,l={};if(e.fullscreen)t.originalPosition.value=y(document.body,"position"),t.originalOverflow.value=y(document.body,"overflow"),l.zIndex=o();else if(e.parent===document.body){t.originalPosition.value=y(document.body,"position"),await c();for(const n of["top","left"]){const t="top"===n?"scrollTop":"scrollLeft";l[n]=e.target.getBoundingClientRect()[n]+document.body[t]+document.documentElement[t]-Number.parseInt(y(document.body,`margin-${n}`),10)+"px"}for(const n of["height","width"])l[n]=`${e.target.getBoundingClientRect()[n]}px`}else t.originalPosition.value=y(n,"position");for(const[s,a]of Object.entries(l))t.$el.style[s]=a},$=(e,n,t)=>{const o=t.vm.ns||t.vm._.exposed.ns;["absolute","fixed","sticky"].includes(t.originalPosition.value)?p(n,o.bm("parent","relative")):k(n,o.bm("parent","relative")),e.fullscreen&&e.lock?k(n,o.bm("parent","hidden")):p(n,o.bm("parent","hidden"))},B=Symbol("ElLoading"),I=(n,t)=>{var o,l,s,a;const i=t.instance,r=e=>m(t.value)?t.value[e]:void 0,u=t=>(n=>{const t=v(n)&&(null==i?void 0:i[n])||n;return t?e(t):t})(r(t)||n.getAttribute(`element-loading-${g(t)}`)),d=null!=(o=r("fullscreen"))?o:t.modifiers.fullscreen,c={text:u("text"),svg:u("svg"),svgViewBox:u("svgViewBox"),spinner:u("spinner"),background:u("background"),customClass:u("customClass"),fullscreen:d,target:null!=(l=r("target"))?l:d?void 0:n,body:null!=(s=r("body"))?s:t.modifiers.body,lock:null!=(a=r("lock"))?a:t.modifiers.lock};n[B]={options:c,instance:L(c)}},V={mounted(e,n){n.value&&I(e,n)},updated(e,n){const t=e[B];n.oldValue!==n.value&&(n.value&&!n.oldValue?I(e,n):n.value&&n.oldValue?m(n.value)&&((e,n)=>{for(const t of Object.keys(n))b(n[t])&&(n[t].value=e[t])})(n.value,t.options):null==t||t.instance.close())},unmounted(e){var n;null==(n=e[B])||n.instance.close(),e[B]=null}};export{L,V as v};
//# sourceMappingURL=chunk.c83b4915.js.map
