import{h as e,k as a,m as t,o as l,x as s,aj as o,q as n,ak as r,al as i,E as u,am as d,$ as c,an as v,a8 as p,H as b,a1 as m,a0 as f,B as h,ao as y,y as g}from"./chunk.25a51fc3.js";import{aE as $,aD as k,d as x,z as C,o as P,c as S,n as w,u as N,O as T,h as B,f as E,X as R,Q as A,r as F,w as _,k as j,g as z,S as K,i as L,L as V,K as q,U as I,M,a3 as U,I as D,a6 as H}from"./index.7c7944d0.js";import{t as O}from"./chunk.c5fb43ac.js";import{c as X}from"./chunk.5b40f4f0.js";import{f as Q,U as W}from"./chunk.a37e6231.js";const Y=(e,a)=>{const t={},l=$([]);return{children:l,addChild:s=>{t[s.uid]=s,l.value=((e,a,t)=>Q(e.subTree).filter((e=>{var t;return k(e)&&(null==(t=e.type)?void 0:t.name)===a&&!!e.component})).map((e=>e.component.uid)).map((e=>t[e])).filter((e=>!!e)))(e,a,t)},removeChild:e=>{delete t[e],l.value=l.value.filter((a=>a.uid!==e))}}},G=e({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:a(String),default:"solid"}}),J=x({name:"ElDivider"});const Z=s(l(x({...J,props:G,setup(e){const a=e,l=t("divider"),s=C((()=>l.cssVar({"border-style":a.borderStyle})));return(e,a)=>(P(),S("div",{class:w([N(l).b(),N(l).m(e.direction)]),style:E(N(s)),role:"separator"},[e.$slots.default&&"vertical"!==e.direction?(P(),S("div",{key:0,class:w([N(l).e("text"),N(l).is(e.contentPosition)])},[T(e.$slots,"default")],2)):B("v-if",!0)],6))}}),[["__file","divider.vue"]])),ee=Symbol("tabsRootContextKey"),ae=e({tabs:{type:a(Array),default:()=>o([])}}),te="ElTabBar",le=x({name:te});var se=l(x({...le,props:ae,setup(e,{expose:a}){const l=e,s=R(),o=A(ee);o||O(te,"<el-tabs><el-tab-bar /></el-tabs>");const r=t("tabs"),i=F(),u=F(),d=()=>u.value=(()=>{let e=0,a=0;const t=["top","bottom"].includes(o.props.tabPosition)?"width":"height",n="width"===t?"x":"y",r="x"===n?"left":"top";return l.tabs.every((o=>{var n,i;const u=null==(i=null==(n=s.parent)?void 0:n.refs)?void 0:i[`tab-${o.uid}`];if(!u)return!1;if(!o.active)return!0;e=u[`offset${X(r)}`],a=u[`client${X(t)}`];const d=window.getComputedStyle(u);return"width"===t&&(l.tabs.length>1&&(a-=Number.parseFloat(d.paddingLeft)+Number.parseFloat(d.paddingRight)),e+=Number.parseFloat(d.paddingLeft)),!1})),{[t]:`${a}px`,transform:`translate${X(n)}(${e}px)`}})();return _((()=>l.tabs),(async()=>{await j(),d()}),{immediate:!0}),n(i,(()=>d())),a({ref:i,update:d}),(e,a)=>(P(),S("div",{ref_key:"barRef",ref:i,class:w([N(r).e("active-bar"),N(r).is(N(o).props.tabPosition)]),style:E(u.value)},null,6))}}),[["__file","tab-bar.vue"]]);const oe=e({panes:{type:a(Array),default:()=>o([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),ne="ElTabNav",re=x({name:ne,props:oe,emits:{tabClick:(e,a,t)=>t instanceof Event,tabRemove:(e,a)=>a instanceof Event},setup(e,{expose:a,emit:l}){const s=R(),o=A(ee);o||O(ne,"<el-tabs><tab-nav /></el-tabs>");const b=t("tabs"),m=r(),f=i(),h=F(),y=F(),g=F(),$=F(),k=F(!1),x=F(0),P=F(!1),S=F(!0),w=C((()=>["top","bottom"].includes(o.props.tabPosition)?"width":"height")),N=C((()=>({transform:`translate${"width"===w.value?"X":"Y"}(-${x.value}px)`}))),T=()=>{if(!h.value)return;const e=h.value[`offset${X(w.value)}`],a=x.value;if(!a)return;const t=a>e?a-e:0;x.value=t},B=()=>{if(!h.value||!y.value)return;const e=y.value[`offset${X(w.value)}`],a=h.value[`offset${X(w.value)}`],t=x.value;if(e-t<=a)return;const l=e-t>2*a?t+a:e-a;x.value=l},E=async()=>{const e=y.value;if(!(k.value&&g.value&&h.value&&e))return;await j();const a=g.value.querySelector(".is-active");if(!a)return;const t=h.value,l=["top","bottom"].includes(o.props.tabPosition),s=a.getBoundingClientRect(),n=t.getBoundingClientRect(),r=l?e.offsetWidth-n.width:e.offsetHeight-n.height,i=x.value;let u=i;l?(s.left<n.left&&(u=i-(n.left-s.left)),s.right>n.right&&(u=i+s.right-n.right)):(s.top<n.top&&(u=i-(n.top-s.top)),s.bottom>n.bottom&&(u=i+(s.bottom-n.bottom))),u=Math.max(u,0),x.value=Math.min(u,r)},V=()=>{var a;if(!y.value||!h.value)return;e.stretch&&(null==(a=$.value)||a.update());const t=y.value[`offset${X(w.value)}`],l=h.value[`offset${X(w.value)}`],s=x.value;l<t?(k.value=k.value||{},k.value.prev=s,k.value.next=s+l<t,t-s<l&&(x.value=t-l)):(k.value=!1,s>0&&(x.value=0))},q=e=>{const a=e.code,{up:t,down:l,left:s,right:o}=p;if(![t,l,s,o].includes(a))return;const n=Array.from(e.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),r=n.indexOf(e.target);let i;i=a===s||a===t?0===r?n.length-1:r-1:r<n.length-1?r+1:0,n[i].focus({preventScroll:!0}),n[i].click(),I()},I=()=>{S.value&&(P.value=!0)},M=()=>P.value=!1;return _(m,(e=>{"hidden"===e?S.value=!1:"visible"===e&&setTimeout((()=>S.value=!0),50)})),_(f,(e=>{e?setTimeout((()=>S.value=!0),50):S.value=!1})),n(g,V),z((()=>setTimeout((()=>E()),0))),K((()=>V())),a({scrollToActiveTab:E,removeFocus:M}),_((()=>e.panes),(()=>s.update()),{flush:"post",deep:!0}),()=>{const a=k.value?[L("span",{class:[b.e("nav-prev"),b.is("disabled",!k.value.prev)],onClick:T},[L(u,null,{default:()=>[L(d,null,null)]})]),L("span",{class:[b.e("nav-next"),b.is("disabled",!k.value.next)],onClick:B},[L(u,null,{default:()=>[L(c,null,null)]})])]:null,t=e.panes.map(((a,t)=>{var s,n,r,i;const d=a.uid,c=a.props.disabled,m=null!=(n=null!=(s=a.props.name)?s:a.index)?n:`${t}`,f=!c&&(a.isClosable||e.editable);a.index=`${t}`;const h=f?L(u,{class:"is-icon-close",onClick:e=>l("tabRemove",a,e)},{default:()=>[L(v,null,null)]}):null,y=(null==(i=(r=a.slots).label)?void 0:i.call(r))||a.props.label,g=!c&&a.active?0:-1;return L("div",{ref:`tab-${d}`,class:[b.e("item"),b.is(o.props.tabPosition),b.is("active",a.active),b.is("disabled",c),b.is("closable",f),b.is("focus",P.value)],id:`tab-${m}`,key:`tab-${d}`,"aria-controls":`pane-${m}`,role:"tab","aria-selected":a.active,tabindex:g,onFocus:()=>I(),onBlur:()=>M(),onClick:e=>{M(),l("tabClick",a,m,e)},onKeydown:e=>{!f||e.code!==p.delete&&e.code!==p.backspace||l("tabRemove",a,e)}},[y,h])}));return L("div",{ref:g,class:[b.e("nav-wrap"),b.is("scrollable",!!k.value),b.is(o.props.tabPosition)]},[a,L("div",{class:b.e("nav-scroll"),ref:h},[L("div",{class:[b.e("nav"),b.is(o.props.tabPosition),b.is("stretch",e.stretch&&["top","bottom"].includes(o.props.tabPosition))],ref:y,style:N.value,role:"tablist",onKeydown:q},[e.type?null:L(se,{ref:$,tabs:[...e.panes]},null),t])])])}}}),ie=e({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:a(Function),default:()=>!0},stretch:Boolean}),ue=e=>q(e)||h(e),de=x({name:"ElTabs",props:ie,emits:{[W]:e=>ue(e),tabClick:(e,a)=>a instanceof Event,tabChange:e=>ue(e),edit:(e,a)=>["remove","add"].includes(a),tabRemove:e=>ue(e),tabAdd:()=>!0},setup(e,{emit:a,slots:l,expose:s}){var o,n;const r=t("tabs"),{children:i,addChild:d,removeChild:c}=Y(R(),"ElTabPane"),v=F(),h=F(null!=(n=null!=(o=e.modelValue)?o:e.activeName)?n:"0"),y=async(t,l=!1)=>{var s,o,n;if(h.value!==t&&!f(t))try{!1!==await(null==(s=e.beforeLeave)?void 0:s.call(e,t,h.value))&&(h.value=t,l&&(a(W,t),a("tabChange",t)),null==(n=null==(o=v.value)?void 0:o.removeFocus)||n.call(o))}catch(r){}},g=(e,t,l)=>{e.props.disabled||(y(t,!0),a("tabClick",e,l))},$=(e,t)=>{e.props.disabled||f(e.props.name)||(t.stopPropagation(),a("edit",e.props.name,"remove"),a("tabRemove",e.props.name))},k=()=>{a("edit",void 0,"add"),a("tabAdd")};return b({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},C((()=>!!e.activeName))),_((()=>e.activeName),(e=>y(e))),_((()=>e.modelValue),(e=>y(e))),_(h,(async()=>{var e;await j(),null==(e=v.value)||e.scrollToActiveTab()})),V(ee,{props:e,currentName:h,registerPane:d,unregisterPane:c}),s({currentName:h}),()=>{const a=l.addIcon,t=e.editable||e.addable?L("span",{class:r.e("new-tab"),tabindex:"0",onClick:k,onKeydown:e=>{e.code===p.enter&&k()}},[a?T(l,"addIcon"):L(u,{class:r.is("icon-plus")},{default:()=>[L(m,null,null)]})]):null,s=L("div",{class:[r.e("header"),r.is(e.tabPosition)]},[t,L(re,{ref:v,currentName:h.value,editable:e.editable,type:e.type,panes:i.value,stretch:e.stretch,onTabClick:g,onTabRemove:$},null)]),o=L("div",{class:r.e("content")},[T(l,"default")]);return L("div",{class:[r.b(),r.m(e.tabPosition),{[r.m("card")]:"card"===e.type,[r.m("border-card")]:"border-card"===e.type}]},[..."bottom"!==e.tabPosition?[s,o]:[o,s]])}}}),ce=e({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),ve=["id","aria-hidden","aria-labelledby"],pe="ElTabPane",be=x({name:pe});var me=l(x({...be,props:ce,setup(e){const a=e,l=R(),s=I(),o=A(ee);o||O(pe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const n=t("tab-pane"),r=F(),i=C((()=>a.closable||o.props.closable)),u=y((()=>{var e;return o.currentName.value===(null!=(e=a.name)?e:r.value)})),d=F(u.value),c=C((()=>{var e;return null!=(e=a.name)?e:r.value})),v=y((()=>!a.lazy||d.value||u.value));_(u,(e=>{e&&(d.value=!0)}));const p=M({uid:l.uid,slots:s,props:a,paneName:c,active:u,index:r,isClosable:i});return z((()=>{o.registerPane(p)})),U((()=>{o.unregisterPane(p.uid)})),(e,a)=>N(v)?D((P(),S("div",{key:0,id:`pane-${N(c)}`,class:w(N(n).b()),role:"tabpanel","aria-hidden":!N(u),"aria-labelledby":`tab-${N(c)}`},[T(e.$slots,"default")],10,ve)),[[H,N(u)]]):B("v-if",!0)}}),[["__file","tab-pane.vue"]]);const fe=s(de,{TabPane:me}),he=g(me);export{Z as E,he as a,fe as b};
//# sourceMappingURL=chunk.f415a879.js.map
